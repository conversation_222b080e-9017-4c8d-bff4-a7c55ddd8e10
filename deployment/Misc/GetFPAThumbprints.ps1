$separator = "------------------------------------------------------------------------"

function Get-FPAThumbprints {
        try {
                Write-Host $separator
                Write-Host "!!!!!!!!!!!!!    USE ame.gbl for LOGIN    !!!!!!!!!!!!!"
                Write-Host $separator

                # Connect to the source Azure AD tenant
                Connect-AzAccount

                # Get the current context
                $currentContext = Get-AzContext

                # Extract the Object ID of the currently authenticated user
                $currentUserId = (Get-AzADUser -UserPrincipalName $currentContext.Account.Id).Id

                # Fetch the details of from the EnvironmentsDetails.json
                $environmentsDetailsFilePath = "..\ExpressV2\DeploymentData\EnvironmentsDetails.json"

                # Read the file content
                $environmentsDetailsJsonContent = Get-Content -Path $environmentsDetailsFilePath -Raw

                # Filtered Environments Details
                $environmentsDetails = @(($environmentsDetailsJsonContent | ConvertFrom-Json) | Where-Object { $_.Environment -eq "Production" -and $_.TenantName -eq "AME" } | Sort-Object -Property SubscriptionId)   
                
                $logfile = "fpa-cert-inventory.txt"
                $separator | Out-File -FilePath $logfile -Append
                "                               FPA Cert Inventory" | Out-File -FilePath $logfile -Append
                $separator | Out-File -FilePath $logfile -Append
                "" | Out-File -FilePath $logfile -Append

                for ($i = 0; $i -lt $environmentsDetails.Count; $i++) {
                    $environmentsDetail = $environmentsDetails[$i]

                    $resourceGroup = "adbrp$($environmentsDetail.ShortRegionName)"
                    $keyVaultName = "adbrp$($environmentsDetail.ShortRegionName)$($environmentsDetail.KeyVaultNameSuffix)"
                    $keyVaultResourceId = (Get-AzKeyVault -VaultName $keyVaultName).ResourceId
                    $region = $environmentsDetail.Region
                    $subscriptionId = $environmentsDetail.SubscriptionId
                    $certName = "adbrpfpa"
                    $roleDefinitionNameCertUser = "Key Vault Certificate User"
                    $roleDefinitionNameSecretsUser = "Key Vault Secrets User"

                    # Set the context for the relevant subscription
                    Set-AzContext -Subscription $subscriptionId

                    # Add a new access policy to read secrets
                    New-AzRoleAssignment -Scope $keyVaultResourceId -ObjectId $currentUserId -RoleDefinitionName $roleDefinitionNameCertUser
                    New-AzRoleAssignment -Scope $keyVaultResourceId -ObjectId $currentUserId -RoleDefinitionName $roleDefinitionNameSecretsUser

                    # Get the Key Vault certificate
                    $certificate = Get-AzKeyVaultCertificate -VaultName $keyVaultName -Name $certName

                    # Get the certificate thumbprint
                    $thumbprint = $certificate.Thumbprint

                    $log = "Subscription: $($subscriptionId), Region: $($region), Thumbprint: $($thumbprint)"

                    Write-Host $log
                    Write-Host $separator

                    # Log the thumbprint to a text file
                    $log | Out-File -FilePath $logfile -Append

                    # Remove the access policy
                    Remove-AzRoleAssignment -Scope $keyVaultResourceId -ObjectId $currentUserId -RoleDefinitionName $roleDefinitionNameCertUser
                    Remove-AzRoleAssignment -Scope $keyVaultResourceId -ObjectId $currentUserId -RoleDefinitionName $roleDefinitionNameSecretsUser
                }

                Write-Host $separator
                Write-Host "!!!!!!!!!!!!!    END    !!!!!!!!!!!!!"
                Write-Host $separator  
        }
        catch {
                # Return the errors to the log.
                Write-Output "ERROR: $($_.Exception.Message)"
        }
}

Get-FPAThumbprints

exit $LASTEXITCODE