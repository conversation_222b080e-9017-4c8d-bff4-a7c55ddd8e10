param(
    [Parameter(Mandatory = $true)][string]$Environment,
    [Parameter(Mandatory = $true)][string]$Region,
    [Parameter(Mandatory = $true)][string]$SubscriptionId
) #end param

$global:storageAccountEndpointSuffix = $null
$global:roleDefinitionNameCertUser = "Key Vault Certificate User"
$global:roleDefinitionNameSecretsUser = "Key Vault Secrets User"

$separator = "------------------------------------------------------------------------------------------------------------------------------------------------"

function Connect-AzureCloud {
    if ($Environment -eq "Public") {
        $environmentName = "AzureCloud"
        $global:storageAccountEndpointSuffix = "core.windows.net"
    }
    elseif ($Environment -eq "Fairfax") {
        $environmentName = "AzureUSGovernment"
        $global:storageAccountEndpointSuffix = "core.usgovcloudapi.net"
    }
    elseif ($Environment -eq "Mooncake") {
        $environmentName = "AzureChinaCloud"
        $global:storageAccountEndpointSuffix = "core.chinacloudapi.cn"
    }
    else {
        Write-Error -Message "Unsupported environment '$($Environment)'" `
            -ErrorAction Stop
    }

    Write-Host $separator

    Write-Host "Storage Account Endpoint suffix '$($global:storageAccountEndpointSuffix)'."

    Write-Host $separator

    Write-Host "Attempting to login '$($environmentName)' ($($SubscriptionId))."

    Write-Host $separator

    Connect-AzAccount -Environment $environmentName -Subscription $SubscriptionId
    Set-AzContext -Subscription $SubscriptionId

    Write-Host "Login to '$($environmentName)' successfully."
}

function Get-CurrentUser {
    param (
        [Parameter(Mandatory = $true)]
        [string]$currentUserUpn
    ) #end param

    Write-Host "Attempting to Get Object Id of '$($currentUserUpn)'."

    Get-AzADUser -UserPrincipalName $currentUserUpn
}

function Remove-KeyVaultRbac {
    param (
        [Parameter(Mandatory = $true)]
        [string]$keyVaultName,
        [Parameter(Mandatory = $true)]
        [string]$currentUserId
    ) #end param
    
    $keyVaultResourceId = (Get-AzKeyVault -VaultName $keyVaultName).ResourceId

    # Removing "Key Vault Certificate User" role definition
    $keyVaultRbacPermission = (Get-AzRoleAssignment -Scope $keyVaultResourceId -ObjectId $currentUserId -RoleDefinitionName $global:roleDefinitionNameCertUser)
    if ($null -ne $keyVaultRbacPermission) {
        Remove-AzRoleAssignment -Scope $keyVaultResourceId -ObjectId $currentUserId -RoleDefinitionName $global:roleDefinitionNameCertUser
        Write-Host "** '$($currentUserId)' successfully removed from '$($keyVaultName)' for role definition '$($global:roleDefinitionNameCertUser)'."
    }

    # Removing "Key Vault Secrets User" role definition
    $keyVaultRbacPermission = (Get-AzRoleAssignment -Scope $keyVaultResourceId -ObjectId $currentUserId -RoleDefinitionName $global:roleDefinitionNameSecretsUser)
    if ($null -ne $keyVaultRbacPermission) {
        Remove-AzRoleAssignment -Scope $keyVaultResourceId -ObjectId $currentUserId -RoleDefinitionName $global:roleDefinitionNameSecretsUser
        Write-Host "** '$($currentUserId)' successfully removed from '$($keyVaultName)' for role definition '$($global:roleDefinitionNameSecretsUser)'."
    }
}

function Set-KeyVaultRbac {
    param (
        [Parameter(Mandatory = $true)]
        [string]$keyVaultName,
        [Parameter(Mandatory = $true)]
        [string]$currentUserId
    ) #end param

    $keyVaultResourceId = (Get-AzKeyVault -VaultName $keyVaultName).ResourceId

    # Setting "Key Vault Certificate User" role definition 
    $keyVaultRbacPermission = (Get-AzRoleAssignment -Scope $keyVaultResourceId -ObjectId $currentUserId -RoleDefinitionName $global:roleDefinitionNameCertUser)
    if ($null -ne $keyVaultRbacPermission) {
        Write-Host "** '$($currentUserId)' already added in '$($keyVaultName)' for role definition '$($global:roleDefinitionNameCertUser)'."
    }
    else {
        New-AzRoleAssignment -Scope $keyVaultResourceId -ObjectId $currentUserId -RoleDefinitionName $global:roleDefinitionNameCertUser
        Write-Host "** '$($currentUserId)' successfully added in '$($keyVaultName)' for role definition '$($global:roleDefinitionNameCertUser)'."
    }

    # Setting "Key Vault Secrets User" role definition
    $keyVaultRbacPermission = (Get-AzRoleAssignment -Scope $keyVaultResourceId -ObjectId $currentUserId -RoleDefinitionName $global:roleDefinitionNameSecretsUser)
    if ($null -ne $keyVaultRbacPermission) {
        Write-Host "** '$($currentUserId)' already added in '$($keyVaultName)' for role definition '$($global:roleDefinitionNameSecretsUser)'."
    }
    else {
        New-AzRoleAssignment -Scope $keyVaultResourceId -ObjectId $currentUserId -RoleDefinitionName $global:roleDefinitionNameSecretsUser
        Write-Host "** '$($currentUserId)' successfully added in '$($keyVaultName)' for role definition '$($global:roleDefinitionNameSecretsUser)'."
    }
}

function Get-StorageAccountConnectionString {
    param (
        [Parameter(Mandatory = $true)]
        [string]$storageAccountKey,
        [Parameter(Mandatory = $true)]
        [string]$storageAccountName
    ) #end param

    $storageAccountConnectionString = "DefaultEndpointsProtocol=https;AccountName=$($storageAccountName);AccountKey=$($storageAccountKey);EndpointSuffix=$($global:storageAccountEndpointSuffix)"

    $storageAccountConnectionString
}

function Add-SecondaryJobsResources {
    param (
        [Parameter(Mandatory = $true)]
        [string]$shortLocationName,
        [Parameter(Mandatory = $true)]
        [string]$currentUserId
    ) #end param

    try {
        $adbrpPrefix = "adbrp"
        $resourceGroupName = "$($adbrpPrefix)$($shortLocationName)"
        $keyVaultName = "$($adbrpPrefix)$($shortLocationName)secrets"
        $jobsDataStorageAccountName = "$($shortLocationName)jobsdata"
        $jobsDatav2StorageAccountName = "$($shortLocationName)jobsdatav2"
        $jobsDatav2ConnectionStringSecretName = "jobsdatasecretv2"

        # Set KeyVault Access Policy.
        Set-KeyVaultRbac -keyVaultName $keyVaultName `
            -currentUserId $currentUserId

        # Get Jobs Data location.
        $jobsDataStorageAccount = Get-AzStorageAccount -ResourceGroupName $resourceGroupName -Name $jobsDataStorageAccountName | Select-Object PrimaryLocation

        # Add Jobs Data v2 Storage Account.
        $jobsDatav2StorageAccount = New-AzStorageAccount -ResourceGroupName $resourceGroupName `
            -Name $jobsDatav2StorageAccountName `
            -Location $jobsDataStorageAccount.PrimaryLocation `
            -Sku "Standard_ZRS" `
            -EnableHttpsTrafficOnly $true `
            -MinimumTlsVersion TLS1_2 `
            -AllowBlobPublicAccess $false

        Write-Host $separator

        Write-Host "Jobs Data v2 Storage Account in '$($shortLocationName)' created successfully."

        Write-Host $separator

        # Get Primary Connection String of Jobs Data v2 Storage Account.
        $jobsDatav2StorageAccountPrimaryKey = (Get-AzStorageAccountKey -ResourceGroupName $resourceGroupName -Name $jobsDatav2StorageAccountName).Value[0]
        $jobsDatav2StorageAccountPrimaryConnectionString = Get-StorageAccountConnectionString -storageAccountKey $jobsDatav2StorageAccountPrimaryKey `
            -storageAccountName $jobsDatav2StorageAccountName

        # Add Jobs Data v2 Secret.
        $jobsDatav2ConnectionStringSecretValue = ConvertTo-SecureString -String $jobsDatav2StorageAccountPrimaryConnectionString -AsPlainText -Force
        $jobsDatav2ConnectionStringSecret = Set-AzKeyVaultSecret -VaultName $keyVaultName -Name $jobsDatav2ConnectionStringSecretName `
            -SecretValue $jobsDatav2ConnectionStringSecretValue

        Write-Host "Jobs Data v2 Storage Account Secret in '$($shortLocationName)' created successfully."

        Write-Host $separator

        # Remove KeyVault Access Policy.
        Remove-KeyVaultRbac -keyVaultName $keyVaultName `
            -currentUserId $currentUserId

        Write-Host $separator

        Write-Host "Secondary Jobs Resources in '$($shortLocationName)' created successfully."    
        
        Write-Host $separator
    }
    catch {
        Write-Error -Message "Something went wrong! Error '$($_.Exception)'"`
            -ErrorAction Stop
    }
}

function Init {
    $canaryShortLocationNames = "eastus2euap", "cenuseuap"
    $nonCustomerShortLocationNames = "auscentral2"
    $lowUsageShortLocatioNames = "korcentral", "eastasia", "uksouth", "cancentral", "caneast", "japanwest", "indwest", "safrnorth", "brazsouth", "francentral", "uaenorth", "switznorth", "norwayeast", "gerwcentral", "switzwest", "swedcentral", "wcentralus", "westus3", "qatcentral", "mexcentral"
    $mediumUsageShortLocatioNames = "westus", "seastasia", "eastus", "scentralus", "ausseast", "japaneast", "ukwest", "indcentral", "auseast", "indsouth", "ncentralus"
    $highUsageShortLocationNames = "westus2", "westeurope", "eastus2", "northeurope", "auscentral", "centralus"
    $fairfaxShortLocationNames = "govvirginia", "govarizona", "govtexas"
    $mooncakeShortLocationNames = "chinaeast2", "chinaeast3", "chinanorth2", "chinanorth3"

    $azAccount = Connect-AzureCloud

    if ($Region.ToLowerInvariant() -eq "canary") {
        $shortLocationNames = $canaryShortLocationNames
    }
    elseif ($Region.ToLowerInvariant() -eq "noncustomer") {
        $shortLocationNames = $nonCustomerShortLocationNames
    }
    elseif ($Region.ToLowerInvariant() -eq "lowusage") {
        $shortLocationNames = $lowUsageShortLocatioNames
    }
    elseif ($Region.ToLowerInvariant() -eq "mediumusage") {
        $shortLocationNames = $mediumUsageShortLocatioNames
    }
    elseif ($Region.ToLowerInvariant() -eq "highusage") {
        $shortLocationNames = $highUsageShortLocationNames
    }
    elseif ($Region.ToLowerInvariant() -eq "fairfax") {
        $shortLocationNames = $fairfaxShortLocationNames
    }
    elseif ($Region.ToLowerInvariant() -eq "mooncake") {
        $shortLocationNames = $mooncakeShortLocationNames
    }
    else {
        Write-Error -Message "Supported regions 'canary', 'noncustomer', 'lowuage', 'mediumusage', 'highusage', 'fairfax', 'mooncake'" `
            -ErrorAction Stop
    }

    Write-Host $separator

    Write-Host "Attempting to Add Secondary Jobs Resources in '$($shortLocationNames)'."

    Write-Host $separator

    $currentUserUpn = (Get-AzContext | Select-Object Account).Account.Id
    $currentUser = Get-CurrentUser -currentUserUpn $currentUserUpn
    $currentUserId = $currentUser.Id

    Write-Host $separator

    Write-Host "Object Id '$($currentUserId)' of '$($currentUserUpn)' retrieved successfully."

    Write-Host $separator

    ForEach ($shortLocationName in $shortLocationNames) {
        Add-SecondaryJobsResources -shortLocationName $shortLocationName `
            -currentUserId $currentUserId
    }

    Write-Host "Secondary Jobs Resources successfully Added in '$($shortLocationNames)'."

    Write-Host $separator
}

Init