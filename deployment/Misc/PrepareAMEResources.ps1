param(
        [Parameter(Mandatory = $true)][string]$Region
)

$separator = "------------------------------------------------------------------------"

$Global:GlobalResourceGroupName = "adbrp_global"
$Global:MicrosoftTenantName = "Microsoft"
$Global:AMETenantName = "AME"
$Global:ServiceFabricRPName = "Azure Service Fabric Resource Provider"
$Global:RegionRolloutSPNName = "databricks-region-rollout-msi"
$Global:ADBRPSPNName = "Databricks Resource Provider"

$Global:roleDefinitionNameCertUser = "Key Vault Certificate User"
$Global:roleDefinitionNameSecretsUser = "Key Vault Secrets User"

function Get-ObjectIdFromServicePrincipal {
        param (
                [string]$displayName
        )

        try {
                $matchingApp = (Get-AzADServicePrincipal -DisplayName $displayName)[0]
                return $matchingApp.Id
        }
        catch {
                Write-Error "could not fetch object Id for the $displayName, $_"
        }
}

function Get-RolloutObjectId {
        param (
                [string]$resourceGroupName,
                [string]$msiName
        )

        return (Get-AzResource -ResourceGroupName $resourceGroupName -ResourceName $msiName -ResourceType "Microsoft.ManagedIdentity/userAssignedIdentities").Properties.principalId
}

function Add-SFUserAssignedIdentity {
        param (
                [string]$name,
                [string]$resourceGroupName
        )

        try {
                # Create a user-assigned managed identity
                $vmIdentityRoleNameGuid = $(New-Guid).Guid
                $serviceFabricResourceProviderId = Get-ObjectIdFromServicePrincipal -displayName $Global:ServiceFabricRPName

                $parameters = @{
                        userAssignedIdentityName        = "$name"
                        vmIdentityRoleNameGuid          = "$vmIdentityRoleNameGuid"
                        serviceFabricResourceProviderId = "$serviceFabricResourceProviderId"
                }

                New-AzResourceGroupDeployment -ResourceGroupName $resourceGroupName -TemplateFile ".\arm-templates\managedidentity-add-sfrp-spn.template.json" -TemplateParameterObject $parameters -Verbose

                Start-Sleep -s 30
        }
        catch {
                Write-Host "Caught an exception: $($_.Exception.Message)"

                # Check for an inner exception
                if ($_.Exception.InnerException) {
                        Write-Host "Inner exception: $($_.Exception.InnerException.Message)"
                }

                return "error!!"
        }
}

function Get-RandomPassword {
        $uppercase = "ABCDEFGHKLMNOPRSTUVWXYZ".tochararray() 
        $lowercase = "abcdefghiklmnoprstuvwxyz".tochararray() 
        $number = "**********".tochararray() 
        $special = "$%&/()=?}{@#*+!".tochararray() 

        $password = ($uppercase | Get-Random -count 4) -join ''
        $password += ($lowercase | Get-Random -count 4) -join ''
        $password += ($number | Get-Random -count 4) -join ''
        $password += ($special | Get-Random -count 3) -join ''

        $passwordarray = $password.tochararray() 
        $scrambledpassword = ($passwordarray | Get-Random -Count 16) -join ''
        $scrambledpassword
}

function Get-RegionDetails {
        Param(
                [string]$filePath,
                [string]$tenantName
        )

        if ($false -eq (Test-Path $filePath)) {
                throw "EnvironmentsDetails.json file not found '$($jsonPath)'"
        }

        $detailsContent = Get-Content -Raw -Path $filePath
        $detailsJson = $detailsContent | ConvertFrom-Json

        return $detailsJson | Where-Object { $_.Region -eq $Region -and $_.TenantName -eq $tenantName }
}

function Get-Location {
        Param(
                [string]$region
        )

        if ($region -like "*EUAP") { 
                return $region.Replace("EUAP", "") 
        }
        else {
                return $region
        }
}

function Add-ResourceGroup {
        Param(
                [string]$resourceGroupName,
                [string]$location
        )

        Write-Host $separator

        # Check if region has already been deployed
        if ($null -eq (Get-AzResourceGroup -Name $resourceGroupName -ErrorAction Ignore)) {
                # Create resource group
                Write-Host "Attempting to create '$($resourceGroupName)'."
                New-AzResourceGroup -Name $resourceGroupName -Location $location -Force
                Write-Host "'$($resourceGroupName)' created successfully."
        }
        else {
                Write-Host "'$($resourceGroupName)' already exists. skipping resource group creation"
        }

        Write-Host $separator
}

function Add-StorageAccount {
        param (
                [string]$resourceGroupName,
                [string]$storageAccountName, 
                [string] $location, 
                [string] $storageSku
        )

        if ($storageAccountName -notin $Global:allResources.Name) {
                New-AzStorageAccount -ResourceGroupName $resourceGroupName -Name $storageAccountName -Location $location -Sku $storageSku -EnableHttpsTrafficOnly $true -MinimumTlsVersion TLS1_2 -AllowBlobPublicAccess $false
                Write-Host $storageAccountName "created successfully."
        }
        else {
                Write-Host $storageAccountName "already exists. skipping storage account creation"
        }
}

function Add-KeyVault {
        Param(
                [string]$keyVaultName,
                [string]$resourceGroupName,
                [string]$location
        )

        $allKeyVaults = Get-AzResource -ResourceGroupName $resourceGroupName -ResourceType "Microsoft.KeyVault/vaults"

        Write-Host $separator

        if ($keyVaultName -notin $Global:allKeyVaults.Name) {
                # Create key vault
                Write-Host "Attempting to create '$($keyVaultName)'."
                New-AzKeyVault -VaultName $keyVaultName -ResourceGroupName $resourceGroupName -Location $location
                Write-Host "'$($keyVaultName)'created successfully."
        }
        else {
                Write-Host "'$($keyVaultName)' already exists. skipping keyvault creation"
        }

        Write-Host $separator
}

function Get-SecretValue {
        param (
                [string]$keyVaultName,
                [string]$secretName
        )

        $secret = Get-AzKeyVaultSecret -VaultName $keyVaultName -Name $secretName
        $secretValue = $secret.SecretValue

        return $secretValue
}

function Set-CloudSpecificVariables {
        param (
                [string]$environment
        )

        if ($environment -eq "Production") {
                $Global:cloudEnvironment = "AzureCloud"
                $Global:storageAccountEndpointSuffix = "core.windows.net"
                $Global:adbRpSfAdminCertSubjectName = "CN=adbrpsfadmin.azclient.ms"
                $Global:databricksRpAadCertSubjectName = "CN=adbrpfpa.azclient.ms"
                $Global:databricksRpSslCertSubjectName = "CN=*.adb-spearfish-rp.net"
                $Global:genevaCertSubjectName = "CN=prod.geneva.keyvault.adb-spearfish-rp.net"
                $Global:dnsName = "*.adb-spearfish-rp.net"
                $Global:genevaCertDnsName = "prod.geneva.keyvault.adb-spearfish-rp.net"
                $Global:genevaActionsAADServicePrincipalName = "GenevaActionsProductionAAD"
        }
        elseif ($environment -eq "Fairfax") {
                $Global:cloudEnvironment = "AzureUSGovernment"
                $Global:storageAccountEndpointSuffix = "core.usgovcloudapi.net"
                $Global:adbRpSfAdminCertSubjectName = "CN=adbrpsfadmin.azclient.us"
                $Global:databricksRpAadCertSubjectName = "CN=databricksrp.windowsazure.us"
                $Global:databricksRpSslCertSubjectName = "CN=*.adb-spearfish-rp.azure.us"
                $Global:genevaCertSubjectName = "CN=ff.geneva.keyvault.adb-spearfish-rp.azure.us"
                $Global:dnsName = "*.adb-spearfish-rp.azure.us"
                $Global:genevaCertDnsName = "ff.geneva.keyvault.adb-spearfish-rp.azure.us"
                $Global:genevaActionsAADServicePrincipalName = "AcisFairfaxAAD"
        }
        elseif ($environment -eq "Mooncake") {
                $Global:cloudEnvironment = "AzureChinaCloud"
                $Global:storageAccountEndpointSuffix = "core.chinacloudapi.cn"
                $Global:adbRpSfAdminCertSubjectName = "CN=adbrpsfadmin.azclient.cn"
                $Global:databricksRpAadCertSubjectName = "CN=databricksrp.azure.cn"
                $Global:databricksRpSslCertSubjectName = "CN=*.adb-spearfish-rp.azure.cn"
                $Global:genevaCertSubjectName = "CN=mc.geneva.keyvault.adb-spearfish-rp.azure.cn"
                $Global:dnsName = "*.adb-spearfish-rp.azure.cn"
                $Global:genevaCertDnsName = "mc.geneva.keyvault.adb-spearfish-rp.azure.cn"
                $Global:genevaActionsAADServicePrincipalName = "AcisMooncakeAAD"
        }
        else {
                Write-Error -Message "Unsupported environment '$($environment)'" ` -ErrorAction Stop
        }
}

function Add-Certificate {
        param(
                [string]$keyVaultName, 
                [string]$certName, 
                [string]$subjectName, 
                [string]$issuerName, 
                [string[]]$dnsName
        )

        try {
                Set-AzKeyVaultCertificateIssuer -VaultName $keyVaultName -IssuerProvider $issuerName -IssuerName $issuerName
                # Create a certificate policy with SANs
                if ($dnsName.Length -gt 0) {
                        $policy = New-AzKeyVaultCertificatePolicy -SecretContentType "application/x-pkcs12" -SubjectName $subjectName -IssuerName $issuerName -ValidityInMonths 12 -RenewAtPercentageLifetime 24 -DnsName $dnsName
                }
                else {
                        $policy = New-AzKeyVaultCertificatePolicy -SecretContentType "application/x-pkcs12" -SubjectName $subjectName -IssuerName $issuerName -ValidityInMonths 12 -RenewAtPercentageLifetime 24
                }
                Add-AzKeyVaultCertificate -VaultName $keyVaultName -Name $certName -CertificatePolicy $policy
                Get-AzKeyVaultCertificateOperation -VaultName $keyVaultName -Name $certName

                Write-Host "$keyVaultName, $certName creation successful"
        }
        catch {
                Write-Error "$keyVaultName, $certName creation failed, $_"
        }
}

function Connect-AzureCloud {
        do {
                try {
                        # Connect using MSI
                        Write-Output "Attempting to connect to $environment environment using MSI"
                        Connect-AzAccount -Identity -Environment $cloudEnvironment
                        Write-Output "Connection to $environment environment using MSI was successful"
                        $connectToSub = 0
                }
                catch {
                        Write-Output "Connection to $environment environment using MSI failed, retrying."
                        $connectToSub = 1
                        $retryCount++
                }
        } while ($connectToSub -eq 1 -and $retryCount -lt $maxRetries)

        # Fail deployment if we reached the maximum retries without connecting to MSI
        if ($connectToSub -eq 1) {
                throw "Error occurred during region rollout! Deployment has failed - could not connect to subscription using MSI."
        }
}

function Add-AMEResources {
        try {
                Write-Host $separator
                Write-Host "!!!!!!!!!!!!!    USE microsoft.com for LOGIN    !!!!!!!!!!!!! "
                Write-Host $separator

                # Fetch the details of from the EnvironmentsDetails.json
                $environmentsDetailsFilePath = "..\ExpressV2\DeploymentData\EnvironmentsDetails.json"

                # Get the details of the source
                $source = Get-RegionDetails -filePath $environmentsDetailsFilePath -tenantName $Global:MicrosoftTenantName

                # Set the cloud specific variables
                Set-CloudSpecificVariables -environment $source.Environment

                # Get the details of the destination
                $destination = Get-RegionDetails -filePath $environmentsDetailsFilePath -tenantName $Global:AMETenantName

                # Connect to the source Azure AD tenant
                Connect-AzAccount -TenantId $source.TenantId -Subscription $source.SubscriptionId
                Set-AzContext -Subscription $source.SubscriptionId

                # Set the source KeyVault details
                $sourceKeyVaultName = "adbrp$($source.ShortRegionName)$($source.KeyVaultNameSuffix)"
                $sourceKeyVaultResourceId = (Get-AzKeyVault -VaultName $sourceKeyVaultName).ResourceId

                # Add current user in AKV
                $azureContext = Get-AzContext
                $currentUserUPN = $azureContext.Account.Id
                $currentUser = Get-AzADUser -UserPrincipalName $currentUserUPN
                $currentUserId = $currentUser.Id
 
                Write-Host $separator
                Write-Host "Adding '$($currentUserUPN)' in '$($sourceKeyVaultName)' Access Policy."
                Write-Host $separator
 
                New-AzRoleAssignment -Scope $sourceKeyVaultResourceId -ObjectId $currentUserId -RoleDefinitionName $Global:roleDefinitionNameCertUser
                New-AzRoleAssignment -Scope $sourceKeyVaultResourceId -ObjectId $currentUserId -RoleDefinitionName $Global:roleDefinitionNameSecretsUser

                Write-Host $separator
                Write-Host "Fetching secrets from '$($sourceKeyVaultName)'."
                Write-Host $separator

                # 1. workspacedatasecret
                $workspaceDataSecretKey = "workspacedatasecret"
                $workspaceDataSecretValue = Get-SecretValue -keyVaultName $sourceKeyVaultName -secretName $workspaceDataSecretKey

                # 2. jobsdatasecret
                $jobsDataSecretKey = "jobsdatasecret"
                $jobsDataSecretValue = Get-SecretValue -keyVaultName $sourceKeyVaultName -secretName $jobsDataSecretKey

                # 3. jobsdatasecretv2
                $jobsDatav2SecretKey = "jobsdatasecretv2"
                $jobsDatav2SecretValue = Get-SecretValue -keyVaultName $sourceKeyVaultName -secretName $jobsDatav2SecretKey

                # 4. templatefilesastokensecret
                $templateFilSecretKey = "templatefilesastokensecret"
                $templateFilSecretValue = Get-SecretValue -keyVaultName $sourceKeyVaultName -secretName $templateFilSecretKey

                # 5. packagedatastorageconnectionstring
                $packageDataSecretKey = "packagedatastorageconnectionstring"
                $packageDataSecretValue = Get-SecretValue -keyVaultName $sourceKeyVaultName -secretName $packageDataSecretKey

                # 6. AllowedAcisExtensions
                $allowedAcisExtensionSecretKey = "AllowedAcisExtensions"
                $allowedAcisExtensionSecretValue = Get-SecretValue -keyVaultName $sourceKeyVaultName -secretName $allowedAcisExtensionSecretKey

                Write-Host $separator
                Write-Host "!!!!!!!!!!!!!    USE ame.gbl for LOGIN    !!!!!!!!!!!!!"
                Write-Host $separator

                # Connect to the target Azure AD tenant
                Connect-AzAccount -TenantId $destination.TenantId -Subscription $destination.SubscriptionId
                Set-AzContext -Subscription $destination.SubscriptionId

                $destinationResourceGroup = "adbrp$($destination.ShortRegionName)"
                $destinationLocation = (Get-Location -region $destination.Region)
                $targetKeyVaultName = "adbrp$($destination.ShortRegionName)$($destination.KeyVaultNameSuffix)"
                $dscPackageStorageName = "$($destination.ShortRegionName)$($destination.DSCPackageStorageAccountNameSuffix)"
                $sfUserAssignedIdentityName = "$($destination.ShortRegionName)-servicefabric-msi"
                $adminPasswordSecretKey = "vmss-adminpassword"

                if ($destination.ZonalResiliency) {
                        $storageSku = "Standard_ZRS"
                }
                else {
                        $storageSku = "Standard_GRS"
                }

                $certIssuerName = "OneCertV2-PrivateCA"

                # Create Resource Group
                Add-ResourceGroup -resourceGroupName $destinationResourceGroup -location $destinationLocation

                # Create SF User Assigned Identity
                Add-SFUserAssignedIdentity -resourceGroupName $destinationResourceGroup -name $sfUserAssignedIdentityName

                # Create DSC package data storage
                Add-StorageAccount -resourceGroupName $destinationResourceGroup -storageAccountName $dscPackageStorageName -location $destinationLocation -storageSku $storageSku

                # Create Key Vault
                Add-KeyVault -resourceGroupName $destinationResourceGroup -keyVaultName $targetKeyVaultName -location $destinationLocation
                $targetKeyVaultResourceId = (Get-AzKeyVault -VaultName $targetKeyVaultName).ResourceId
                # Add Access Policy

                # 1. Region rollout MSI
                $regionRolloutSPNId = Get-RolloutObjectId -resourceGroupName $Global:GlobalResourceGroupName -msiName $Global:RegionRolloutSPNName
                New-AzRoleAssignment -Scope $targetKeyVaultResourceId -ObjectId $regionRolloutSPNId -RoleDefinitionName $Global:roleDefinitionNameCertUser
                New-AzRoleAssignment -Scope $targetKeyVaultResourceId -ObjectId $regionRolloutSPNId -RoleDefinitionName $Global:roleDefinitionNameSecretsUser

                # 2. SF MI
                $serviceFabricResourceProviderId = Get-ObjectIdFromServicePrincipal -displayName $sfUserAssignedIdentityName
                New-AzRoleAssignment -Scope $targetKeyVaultResourceId -ObjectId $serviceFabricResourceProviderId -RoleDefinitionName $Global:roleDefinitionNameCertUser
                New-AzRoleAssignment -Scope $targetKeyVaultResourceId -ObjectId $serviceFabricResourceProviderId -RoleDefinitionName $Global:roleDefinitionNameSecretsUser

                # 3. ADB RP
                $adbRPSPNId = Get-ObjectIdFromServicePrincipal -displayName $Global:ADBRPSPNName
                New-AzRoleAssignment -Scope $targetKeyVaultResourceId -ObjectId $adbRPSPNId -RoleDefinitionName $Global:roleDefinitionNameCertUser
                New-AzRoleAssignment -Scope $targetKeyVaultResourceId -ObjectId $adbRPSPNId -RoleDefinitionName $Global:roleDefinitionNameSecretsUser

                # 4. ACIS Production AAD
                $acisProductionAADSPNId = Get-ObjectIdFromServicePrincipal -displayName $Global:genevaActionsAADServicePrincipalName
                New-AzRoleAssignment -Scope $targetKeyVaultResourceId -ObjectId $acisProductionAADSPNId -RoleDefinitionName $Global:roleDefinitionNameCertUser
                New-AzRoleAssignment -Scope $targetKeyVaultResourceId -ObjectId $acisProductionAADSPNId -RoleDefinitionName $Global:roleDefinitionNameSecretsUser

                # Add Secret to Key Vault

                # 1. DSC SAS Token
                $dscPackageConnectionString = "DefaultEndpointsProtocol=https;AccountName=" + $dscPackageStorageName + ";AccountKey=" + (Get-AzStorageAccountKey -ResourceGroupName $destinationResourceGroup -Name $dscPackageStorageName).Value[0] + ";EndpointSuffix=" + $Global:storageAccountEndpointSuffix
                $dscPackageStorageCtx = New-AzStorageContext -ConnectionString $dscPackageConnectionString
                $dscPackageSASToken = New-AzStorageAccountSASToken -Service File -ResourceType Service, Container, Object -Permission "r" -Context $dscPackageStorageCtx -ExpiryTime (Get-Date).AddYears(10)
                $dscPackageSASSecret = ConvertTo-SecureString -String $dscPackageSASToken -AsPlainText -Force

                # 2. VMSS Admin Password
                $adminPassword = Get-RandomPassword
                $adminPasswordSecretValue = ConvertTo-SecureString -String $adminPassword -AsPlainText -Force
                Set-AzKeyVaultSecret -VaultName $targetKeyVaultName -Name $adminPasswordSecretKey -SecretValue $adminPasswordSecretValue

                # Iterate through the secret files and import them into the target KeyVault

                # 1. workspacedatasecret
                Set-AzKeyVaultSecret -VaultName $targetKeyVaultName -Name $workspaceDataSecretKey -SecretValue $workspaceDataSecretValue

                # 2. jobsdatasecret
                Set-AzKeyVaultSecret -VaultName $targetKeyVaultName -Name $jobsDataSecretKey -SecretValue $jobsDataSecretValue

                # 3. jobsdatasecretv2
                Set-AzKeyVaultSecret -VaultName $targetKeyVaultName -Name $jobsDatav2SecretKey -SecretValue $jobsDatav2SecretValue

                # 4. templatefilesastokensecret
                Set-AzKeyVaultSecret -VaultName $targetKeyVaultName -Name $templateFilSecretKey -SecretValue $templateFilSecretValue

                # 5. packagedatastorageconnectionstring
                Set-AzKeyVaultSecret -VaultName $targetKeyVaultName -Name $packageDataSecretKey -SecretValue $packageDataSecretValue

                # 6. AllowedAcisExtensions
                Set-AzKeyVaultSecret -VaultName $targetKeyVaultName -Name $allowedAcisExtensionSecretKey -SecretValue $allowedAcisExtensionSecretValue

                # Add Certs

                # 1. SF Admin cert
                $certName = "adbrpsfadmincertificate"
                Add-Certificate -keyVaultName $targetKeyVaultName -certName $certName -subjectName $Global:adbRpSfAdminCertSubjectName -issuerName $certIssuerName  -dnsName @()

                # 2. RP 1P cert
                $certName = "adbrpfpa"
                Add-Certificate -keyVaultName $targetKeyVaultName -certName $certName -subjectName $Global:databricksRpAadCertSubjectName -issuerName $certIssuerName -dnsName @()

                # 3. RP SSL cert
                $certName = "databricksrpssl"
                Add-Certificate -keyVaultName $targetKeyVaultName -certName $certName -subjectName $Global:databricksRpSslCertSubjectName -issuerName $certIssuerName -dnsName @($Global:dnsName)

                # 4. Geneva cert
                $certName = "GenevaCert"
                Add-Certificate -keyVaultName $targetKeyVaultName -certName $certName -subjectName $Global:genevaCertSubjectName -issuerName $certIssuerName -dnsName @($Global:genevaCertDnsName)

                Write-Host "Regional Certs created in '$($targetKeyVaultName)'."

                # Allow Azure Resource Manager for template deployment
                Set-AzKeyVaultAccessPolicy -VaultName $targetKeyVaultName -EnabledForTemplateDeployment

                Write-Host $separator
                Write-Host "!!!!!!!!!!!!!    END    !!!!!!!!!!!!!"
                Write-Host $separator  
        }
        catch {
                # Return the errors to the log.
                Write-Output "ERROR: $($_.Exception.Message)"
        }
}

Add-AMEResources