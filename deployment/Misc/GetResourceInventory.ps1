param(
    [Parameter(Mandatory = $true)]
    [ValidateSet("AME", "Microsoft")]
    [string]$Tenant
)

$separator = "--------------------------------------------------------------------------------------------------------------"

function Get-Inventory {
    try {
        Write-Host $separator
        if ($Tenant -eq "AME") {
            Write-Host "!!!!!!!!!!!!!    USE @ame.gbl for login    !!!!!!!!!!!!!"
        }
        else {
            Write-Host "!!!!!!!!!!!!!    USE @microsoft for login    !!!!!!!!!!!!!"
        }

        Write-Host $separator

        # Connect to the source Azure AD tenant
        Connect-AzAccount

        # Fetch the details of from the EnvironmentsDetails.json
        $environmentsDetailsFilePath = "..\ExpressV2\DeploymentData\EnvironmentsDetails.json"

        # Read the file content
        $environmentsDetailsJsonContent = Get-Content -Path $environmentsDetailsFilePath -Raw

        # Filtered Environments Details
        $environmentsDetails = @(($environmentsDetailsJsonContent | ConvertFrom-Json) | Where-Object { $_.Environment -eq "Production" -and $_.TenantName -eq $Tenant } | Sort-Object -Property ShortRegionName)

        $logfile = "$($Tenant)_$(Get-Date -Format "MM-dd_HH-mm-ss").txt"

        $separator | Out-File -FilePath $logfile -Append
        "                               ($($Tenant)) Resources" | Out-File -FilePath $logfile -Append
        $separator | Out-File -FilePath $logfile -Append
        "" | Out-File -FilePath $logfile -Append

        for ($i = 0; $i -lt $environmentsDetails.Count; $i++) {
            $environmentsDetail = $environmentsDetails[$i]
            $resourceGroup = "adbrp$($environmentsDetail.ShortRegionName)"
            $subscriptionId = $environmentsDetail.SubscriptionId

            # Set the context for the relevant subscription
            Set-AzContext -Subscription $subscriptionId

            $resources = Get-AzResource -ResourceGroupName $resourceGroup

            "" | Out-File -FilePath $logfile -Append
            "- $($resourceGroup)" | Out-File -FilePath $logfile -Append
            "" | Out-File -FilePath $logfile -Append

            if ($null -eq $resources) {
                "** No resources were found **" | Out-File -FilePath $logfile -Append
            }
            else {
                $resources | Format-Table | Out-File -FilePath $logfile -Append
            }

            $separator | Out-File -FilePath $logfile -Append
        }

        Write-Host $separator
        Write-Host "!!!!!!!!!!!!!    END    !!!!!!!!!!!!!"
        Write-Host $separator
    }
    catch {
        # Return the errors to the log.
        Write-Output "ERROR: $($_.Exception.Message)"
    }
}

Get-Inventory