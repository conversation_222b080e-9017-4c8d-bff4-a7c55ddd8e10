﻿# Rollback steps for removing Providers.ServiceFabric.JobRole from service fabric managed cluster

#1. Take JIT access to appropriate subscriptions
#2. Update the Set-EnvironmentDetails method if you want to run the script for selected regions in a environment.
# This script will remove Providers.ServiceFabric.JobRole from all the regions in a cloud (AME/Microsoft/CME/USGov).

# Public cloud subscription Id: 9ae8e263-8df7-4b7e-b27b-d59f0bfb685b
# Mooncake subscription Id: a1fc4d92-3628-4757-943a-73e4716bf01e
# Fairfax subscription Id: 37a352eb-6f92-45cb-91a2-27cafb74d09b

#AME Subscription Names (JIT from ame.gbl): 
#AzureDatabricks_ResourceProvider_Canary: 8e1611c2-b76d-436d-a464-fd25091684a5"
#AzureDatabricks_ResourceProvider_NonCustomer: 0cc22e9e-c6f4-43b4-bdf3-b75ba321c6b0
#AzureDatabricks_ResourceProvider_Pilot: df2bf6ba-b305-4cf0-a415-b9394a92f875
#AzureDatabricks_ResourceProvider_Medium: def654bb-7f16-4354-89e5-ff2f16d60cf0
#AzureDatabricks_ResourceProvider_Heavy: 4d61f921-568e-4745-8f7b-1876248ed6d2
#AzureDatabricks_ResourceProvider_RowA: 8a59d0dc-e1ba-489b-b66f-c2356f6d049a
#AzureDatabricks_ResourceProvider_RowB: f61b0ebf-5f8c-4319-aa8a-b3d4c35d8c95

param(
    [Parameter(Mandatory = $true)][ValidateSet('Production', 'FairFax', 'Mooncake')][string]$environment,
    [Parameter(Mandatory = $true)][ValidateSet('Microsoft', 'AME', "CME", "USGOV")][string]$cloudName
)

$maxAttempts = 5
$separator = "------------------------------------------------------------------------------------------------------------------------------------------------"
function Connect-AzureCloud {
    $tap = 1
    $loggedIn = $false

    if ($environment -eq "Production") {
        $azcloud = "AzureCloud"
    }
    elseif ($environment -eq "Fairfax") {
        $azcloud = "AzureUSGovernment"     
    }
    elseif ($environment -eq "Mooncake") {
        $azcloud = "AzureChinaCloud"
    }
    else {
        Write-Error -Message "Unsupported environment '$($environment)'" `
            -ErrorAction Stop
    }

    Write-Host $separator
    Write-Host "Attempting to login '$($azcloud)' ($($subscriptionId))."

    do {
        try {
            Connect-AzAccount -Environment $azcloud -ErrorAction Stop
            $loggedIn = $true
            Write-Host "Login to '$($azcloud)' successfully."
        }
        catch {
            Write-Warning "Login to '$($azcloud)' failed with error '$($_.Exception)', retrying..."
        }
    } while (!$loggedIn `
            -and $tap++ -lt $maxAttempts)

    if (!$loggedIn) {
        Write-Error -Message "Maximum attempts to login exceeded. Login failed." `
            -ErrorAction Stop
    }
}

function Set-EnvironmentDetails {
    if ($cloudName -eq "Microsoft" -and $environment -eq "Production") {
        
        $global:subscriptionWithRegions = @{
            "9ae8e263-8df7-4b7e-b27b-d59f0bfb685b" = @("eastus2", "eastus2euap", "westus", "westeurope", "northeurope", "eastus", "seastasia",
                "eastasia", "scentralus", "ncentralus", "westus2", "centralus", "ukwest", "uksouth", "auseast", "ausseast", "auscentral",
                "auscentral2", "japaneast", "japanwest", "cancentral", "caneast", "indcentral", "indsouth", "indwest", "korcentral", "safrnorth",
                "brazsouth", "francentral", "uaenorth", "switznorth", "norwayeast", "gerwcentral", "switzwest", "swedcentral", "wcentralus", "westus3") 
        }
        $global:clusterSuffix = ""
    }
    elseif ($cloudName -eq "AME" -and $environment -eq "Production") {
        
        $global:subscriptionWithRegions = @{

            #Subscription Name: AzureDatabricks_ResourceProvider_Canary
            "8e1611c2-b76d-436d-a464-fd25091684a5" = @("eastus2euap")

            #Subscription Name: AzureDatabricks_ResourceProvider_NonCustomer
            "0cc22e9e-c6f4-43b4-bdf3-b75ba321c6b0" = @("auscentral2")

            #Subscription Name: AzureDatabricks_ResourceProvider_Pilot
            "df2bf6ba-b305-4cf0-a415-b9394a92f875" = @("wcentralus", "eastasia")

            #Subscription Name: AzureDatabricks_ResourceProvider_Medium
            "def654bb-7f16-4354-89e5-ff2f16d60cf0" = @("uksouth")

            #Subscription Name: AzureDatabricks_ResourceProvider_Heavy
            "4d61f921-568e-4745-8f7b-1876248ed6d2" = @("eastus")

            #Subscription Name: AzureDatabricks_ResourceProvider_RowA
            "8a59d0dc-e1ba-489b-b66f-c2356f6d049a" = @("auseast", "auscentral", "brazsouth", "cancentral", "northeurope", "gerwcentral", "indcentral", "indwest", "japaneast", "korcentral", "westus", "eastus2", "ncentralus", "safrnorth", "swedcentral", "switzwest", "qatcentral")

            #Subscription Name: AzureDatabricks_ResourceProvider_RowB
            "f61b0ebf-5f8c-4319-aa8a-b3d4c35d8c95" = @("seastasia", "ausseast", "scentralus", "caneast", "westeurope", "francentral", "indsouth", "japanwest", "norwayeast", "centralus", "westus2", "westus3", "switznorth", "ukwest", "uaenorth")
        }
        $global:clusterSuffix = "v2"
    }
    elseif ($environment -eq "Fairfax" -and $cloudName -eq "USGOV") {
        
        # Fairfax RP subscription Id: 37a352eb-6f92-45cb-91a2-27cafb74d09b
        $global:subscriptionWithRegions = @{
            "37a352eb-6f92-45cb-91a2-27cafb74d09b" = @("govtexas", "govarizona", "govvirginia")
        } 
        $global:clusterSuffix = ""   
    }
    elseif ($environment -eq "Mooncake" -and $cloudName -eq "CME") {
        
        # Mooncake RP subscription Id: a1fc4d92-3628-4757-943a-73e4716bf01e
        $global:subscriptionWithRegions = @{
            "a1fc4d92-3628-4757-943a-73e4716bf01e" = @("chinanorth3", "chinanorth2", "chinaeast3", "chinaeast2")
        }
        $global:clusterSuffix = ""
    }
}

function Remove-SFService () {

    Write-Host $separator

    foreach ($subscription in $global:subscriptionWithRegions.GetEnumerator()) {
        $subscriptionId = $subscription.Key
        $shortRegionNames = $subscription.Value    

        Set-AzContext -Subscription $subscriptionId
        Write-Host "Using subscription: $subscriptionId" -ForegroundColor Green

        try {
            foreach ($shortRegionName in $shortRegionNames) {
        
                Write-Host "Trying to remove Providers.ServiceFabric.JobRole from Subscription ID: $subscriptionId, Region: $shortRegionName"

                $clusterName = "adbrp" + $shortRegionName + $global:clusterSuffix
                $resourceGroupName = "adbrp" + $shortRegionName
        
                try {
        
                    Remove-AzServiceFabricManagedClusterService -ResourceGroupName $resourceGroupName -ClusterName $clusterName -ApplicationName 'Providers.ServiceFabric' -Name 'Providers.ServiceFabric.JobRole' -Force -ErrorAction Stop -verbose
                    write-host "Providers.ServiceFabric.JobRole" successfully removed from $clusterName - forgroundcolor Green
        
                    if ($shortRegionName -eq "eastus2euap") {
                        Remove-AzServiceFabricManagedClusterService -ResourceGroupName $resourceGroupName -ClusterName $clusterName -ApplicationName 'Providers.ServiceFabric.S2' -Name 'Providers.ServiceFabric.JobRole' -Force -ErrorAction Stop -Verbose
                        Remove-AzServiceFabricManagedClusterService -ResourceGroupName $resourceGroupName -ClusterName $clusterName -ApplicationName 'Providers.ServiceFabric.S3' -Name 'Providers.ServiceFabric.JobRole' -Force -ErrorAction Stop -verbose
                    }
                }
                catch {
                    Write-Host "Could not remove Providers.ServiceFabric.JobRole from '$($clusterName)'" -ForegroundColor Red
                    Write-Error -Message $($_.Exception)
                }
            }
        }
        catch {
            Write-Host "Error: $($SubscriptionId) $($region) $($_.Exception.Message)"
    
        }    
    }
    Write-Host $separator
}

Connect-AzureCloud
Set-EnvironmentDetails
Remove-SFService
