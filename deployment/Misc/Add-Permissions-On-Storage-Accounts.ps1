param(
        [Parameter(Mandatory = $true)][string]$SubscriptionId
)

$separator = "------------------------------------------------------------------------"
function Log($logStatement) {
        $filePath = ".\AddRoleAssignmentLogs.csv";
        $date = get-date
        $logStatement = "$date,$logStatement"
    
        if (!(Test-Path $filePath -ErrorAction SilentlyContinue)) {
                New-Item -path $filePath -type "file"
                Add-Content -path $filePath -value $logStatement
        }
        else {
                Add-Content -path $filePath -value $logStatement
        }
}
    
function Add-RoleAssignments {
        param(
                [string]$storageAccountName,
                [string]$resourceGroupName,
                [string]$managedIdentityName,
                [string]$managedIdentityResourceGroupName
        )
        try {
                # Get managed identity details
                $managedIdentity = Get-AzUserAssignedIdentity -ResourceGroupName $managedIdentityResourceGroupName -Name $managedIdentityName
               
                # Get storage account resource ID
                $storageAccount = Get-AzStorageAccount -ResourceGroupName $resourceGroupName -Name $storageAccountName
                $storageAccountResourceId = $storageAccount.Id

                # Assign role to managed identity for the storage account
                New-AzRoleAssignment -ObjectId $managedIdentity.PrincipalId -RoleDefinitionName "Storage Queue Data Contributor" -Scope $storageAccountResourceId -ErrorAction Stop
                Log "Role assignment for $storageAccountName, $managedIdentityName, Storage Queue Data Contributor, $storageAccountResourceId, Success"
                New-AzRoleAssignment -ObjectId $managedIdentity.PrincipalId -RoleDefinitionName "Storage Table Data Contributor" -Scope $storageAccountResourceId -ErrorAction Stop
                Log "Role assignment for $storageAccountName, $managedIdentityName, Storage Table Data Contributor, $storageAccountResourceId, Success"
        }
        catch {
                Write-Error "Add Roleassignment to $storageAccountName failed, $_"
                Log "Role assignment for $storageAccountName, $managedIdentityName, Failed, $_"
        }
}

function Add-PermissionsOnStorageAccount {
        try {
                # Fetch the details of from the EnvironmentsDetails.json
                $environmentsDetailsFilePath = "..\ExpressV2\DeploymentData\EnvironmentsDetails.json"

                # Read the file content
                $environmentsDetailsJsonContent = Get-Content -Path $environmentsDetailsFilePath -Raw

                # Filtered Environments Details
                $environmentsDetails = @(($environmentsDetailsJsonContent | ConvertFrom-Json) | Where-Object { $_.TenantName -ne "Microsoft" -and $_.SubscriptionId -eq $SubscriptionId })

                $environment = $environmentsDetails[0].Environment

                if ($environment -eq "Production" -or $environment -eq "Staging") {
                        $cloudEnvironment = "AzureCloud"
                }
                elseif ($environment -eq "Fairfax") {
                        $cloudEnvironment = "AzureUSGovernment"
                }
                elseif ($environment -eq "Mooncake") {
                        $cloudEnvironment = "AzureChinaCloud"
                }

                Write-Host $separator
                Write-Host "!!!!!!!!!!!!!    USE ame.gbl for LOGIN    !!!!!!!!!!!!!"
                Write-Host $separator

                # Connect to the source Azure AD tenant
                Connect-AzAccount -Subscription $SubscriptionId -Environment $cloudEnvironment
                Set-AzContext -Subscription $SubscriptionId

                for ($i = 0; $i -lt $environmentsDetails.Count; $i++) {
                        $environmentsDetail = $environmentsDetails[$i]
                        $environment = $environmentsDetail.Environment

                        if ($environment -eq "Production" -or $environment -eq "Staging") {
                                $managedIdentityName = "$($environmentsDetail.ShortRegionName)-servicefabric-msi"
                                $sfIdentityResourceGroup = "adbrp$($environmentsDetail.ShortRegionName)"
                        }
                        elseif ($environment -eq "Fairfax" -or $environment -eq "Mooncake") {
                                $managedIdentityName = "databricks-servicefabric-msi"
                                $sfIdentityResourceGroup = "adbrp_global"
                        }

                        $resourceGroup = "adbrp$($environmentsDetail.ShortRegionName)"
                        $workspaceDataStorageAccountName = $environmentsDetail.WorkspaceDataStorageAccountName
                        $primaryJobsDataStorageAccountName = $environmentsDetail.PrimaryJobsDataStorageAccountName
                        $secondaryJobsdataStorageAccountName = $environmentsDetail.SecondaryJobsDataStorageAccountName

                        Add-RoleAssignments -storageAccountName $workspaceDataStorageAccountName -resourceGroupName $resourceGroup -managedIdentityName $managedIdentityName -managedIdentityResourceGroupName $sfIdentityResourceGroup
                        Add-RoleAssignments -storageAccountName $primaryJobsDataStorageAccountName -resourceGroupName $resourceGroup -managedIdentityName $managedIdentityName -managedIdentityResourceGroupName $sfIdentityResourceGroup
                        Add-RoleAssignments -storageAccountName $secondaryJobsdataStorageAccountName -resourceGroupName $resourceGroup -managedIdentityName $managedIdentityName -managedIdentityResourceGroupName $sfIdentityResourceGroup

                        Write-Host $separator
                }

                Write-Host $separator
                Write-Host "!!!!!!!!!!!!!    END    !!!!!!!!!!!!!"
                Write-Host $separator  
        }
        catch {
                # Return the errors to the log.
                Write-Output "ERROR: $($_.Exception.Message)"
        }
}

Add-PermissionsOnStorageAccount