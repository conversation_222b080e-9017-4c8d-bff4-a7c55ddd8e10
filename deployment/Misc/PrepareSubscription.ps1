param(
        [Parameter(Mandatory = $true)][string]$Environment,
        [Parameter(Mandatory = $true)][string]$TenantId,
        [Parameter(Mandatory = $true)][string]$SubscriptionId
)

$separator = "------------------------------------------------------------------------"

$Global:GlobalResourceGroupName = "adbrp_global"
$Global:GlobalResourceGroupLocation = "WestUS2"
$Global:RegionRolloutSPNName = "databricks-region-rollout-msi"
$Global:ContributorRoleDefinition = "Contributor"
$Global:AzureServiceDeployContributorRoleDefinition = "Azure Service Deploy Release Management Contributor"
$Global:AzureServiceDeployKeyVaultUserRoleDefinition = "Azure Service Deploy Release Management Key Vault Secrets User"

function Get-RolloutObjectId {
        param (
                [string]$resourceGroupName,
                [string]$msiName
        )

        return (Get-AzResource -ResourceGroupName $resourceGroupName -ResourceName $msiName -ResourceType "Microsoft.ManagedIdentity/userAssignedIdentities").Properties.principalId
}

function Add-RoleInSubscriptionScope {
        param (
                [string]$roleDefinitionName,
                [string]$objectId
        )

        # Check if the role assignment already exists
        $existingRoleAssignment = Get-AzRoleAssignment -RoleDefinitionName $roleDefinitionName -ObjectId $objectId -Scope "/subscriptions/$($SubscriptionId)"

        if (!$existingRoleAssignment) {
                # Role assignment does not exist, so add it
                New-AzRoleAssignment -RoleDefinitionName $roleDefinitionName -ObjectId $objectId -Scope "/subscriptions/$($SubscriptionId)"
                Write-Output "Role assignment '$($roleDefinitionName)' added successfully."
        }
        else {
                Write-Output "Role assignment '$($roleDefinitionName)' already exists."
        }
}

function Add-UserAssignedIdentity {
        param (
                [string]$name,
                [string]$resourceGroupName
        )

        try {
                $parameters = @{
                        userAssignedIdentityName = "$name"
                }

                New-AzResourceGroupDeployment -ResourceGroupName $resourceGroupName -TemplateFile ".\arm-templates\managedidentity-add-databricks-rollout.json" -TemplateParameterObject $parameters -Verbose

                Start-Sleep -s 30
        }
        catch {
                Write-Host "Caught an exception: $($_.Exception.Message)"

                # Check for an inner exception
                if ($_.Exception.InnerException) {
                        Write-Host "Inner exception: $($_.Exception.InnerException.Message)"
                }

                return "error!!"
        }
}

function Add-ResourceGroup {
        Param(
                [string]$resourceGroupName,
                [string]$location
        )

        Write-Host $separator

        # Check if region has already been deployed
        if ($null -eq (Get-AzResourceGroup -Name $resourceGroupName -ErrorAction Ignore)) {
                # Create resource group
                Write-Host "Attempting to create '$($resourceGroupName)'."
                New-AzResourceGroup -Name $resourceGroupName -Location $location -Force
                Write-Host "'$($resourceGroupName)' created successfully."
        }
        else {
                Write-Host "'$($resourceGroupName)' already exists. skipping resource group creation"
        }

        Write-Host $separator
}

function Set-CloudSpecificVariables {
        param (
                [string]$environment
        )

        if ($environment -eq "Production") {
                $Global:cloudEnvironment = "AzureCloud"

                if ($TenantId -eq "72f988bf-86f1-41af-91ab-2d7cd011db47") {
                        # MSFT -> "AzureDatabricksDeploymentAdmins"
                        $Global:AzureDatabricksGroupObjectId = "087090ea-74b9-42e3-90e9-87445141c991"
                }
                else {
                        # AME Tenant -> "AD-AzureDatabricks"
                        $Global:AzureDatabricksGroupObjectId = "25d4060d-0873-4e58-b529-c386347a4de4"
                }
        }
        elseif ($environment -eq "Fairfax") {
                # USME -> "Role-Escort"
                $Global:AzureDatabricksGroupObjectId = "872ba138-3d33-4165-8ff9-ee8f32680bc0"
                $Global:cloudEnvironment = "AzureUSGovernment"
        }
        elseif ($environment -eq "Mooncake") {
                # CME -> "Role-Deployment"
                $Global:AzureDatabricksGroupObjectId = "f1bed89b-0f9a-42f7-b4dc-6234a9ffd123"
                $Global:cloudEnvironment = "AzureChinaCloud"
        }
        else {
                Write-Error -Message "Unsupported environment '$($environment)'" ` -ErrorAction Stop
        }
}

function Connect-AzureCloud {
        do {
                try {
                        # Connect using MSI
                        Write-Output "Attempting to connect to $environment environment using MSI"
                        Connect-AzAccount -Identity -Environment $cloudEnvironment
                        Write-Output "Connection to $environment environment using MSI was successful"
                        $connectToSub = 0
                }
                catch {
                        Write-Output "Connection to $environment environment using MSI failed, retrying."
                        $connectToSub = 1
                        $retryCount++
                }
        } while ($connectToSub -eq 1 -and $retryCount -lt $maxRetries)

        # Fail deployment if we reached the maximum retries without connecting to MSI
        if ($connectToSub -eq 1) {
                throw "Error occurred during region rollout! Deployment has failed - could not connect to subscription using MSI."
        }
}

function Update-Subscription {
        try {
                Write-Host $separator
                Write-Host "!!!!!!!!!!!!!    USE ame.gbl for LOGIN    !!!!!!!!!!!!!"
                Write-Host $separator

                # Set the cloud specific variables
                Set-CloudSpecificVariables -environment $Environment

                # Connect to the target Azure AD tenant
                Connect-AzAccount -TenantId $TenantId -Subscription $SubscriptionId
                Set-AzContext -Subscription $SubscriptionId

                # Register Dependent RP's
                Register-AzResourceProvider -ProviderNamespace Microsoft.Storage
                Register-AzResourceProvider -ProviderNamespace Microsoft.Network
                Register-AzResourceProvider -ProviderNamespace Microsoft.Compute

                # Create Global Resource Group
                Add-ResourceGroup -resourceGroupName $Global:GlobalResourceGroupName -location $Global:GlobalResourceGroupLocation

                # Create Databricks Rollout Identity
                Add-UserAssignedIdentity -resourceGroupName $Global:GlobalResourceGroupName -name $Global:RegionRolloutSPNName

                # Add Databricks Region Rollout Identity as "Contributor" in Subscription.
                $regionRolloutSPNId = Get-RolloutObjectId -resourceGroupName $Global:GlobalResourceGroupName -msiName $Global:RegionRolloutSPNName
                Add-RoleInSubscriptionScope -roleDefinitionName $Global:ContributorRoleDefinition -objectId $regionRolloutSPNId

                # Add "AzureDatabricksGroupObjectId" as "Azure Service Deploy Release Management Contributor" and "Azure Service Deploy Release Management Key Vault Secrets User" in Subscription
                Add-RoleInSubscriptionScope -roleDefinitionName $Global:AzureServiceDeployContributorRoleDefinition -objectId $Global:AzureDatabricksGroupObjectId
                 Add-RoleInSubscriptionScope -roleDefinitionName $Global:AzureServiceDeployKeyVaultUserRoleDefinition -objectId $Global:AzureDatabricksGroupObjectId

                Write-Host $separator
                Write-Host "!!!!!!!!!!!!!    END    !!!!!!!!!!!!! "
                Write-Host $separator  
        }
        catch {
                # Return the errors to the log.
                Write-Output "ERROR: $($_.Exception.Message)"
        }
}

Update-Subscription