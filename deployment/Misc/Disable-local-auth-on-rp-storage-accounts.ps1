param(
        [Parameter(Mandatory = $true)][string]$SubscriptionId
)

$separator = "------------------------------------------------------------------------"
function Log($logStatement) {
        $filePath = ".\DisableLocalAuthLogs.csv";
        $date = get-date
        $logStatement = "$date,$logStatement"
    
        if (!(Test-Path $filePath -ErrorAction SilentlyContinue)) {
                New-Item -path $filePath -type "file"
                Add-Content -path $filePath -value $logStatement
        }
        else {
                Add-Content -path $filePath -value $logStatement
        }
}
    
function Disable-Local-Auth {
        param(
                [string]$storageAccountName,
                [string]$resourceGroupName
        )
        try {
              
              Set-AzStorageAccount -ResourceGroupName $resourceGroupName -AccountName $storageAccountName -AllowSharedKeyAccess $false -ErrorAction Stop

                Log "Disable local auth, $storageAccountName, Success"
        }
        catch {
                Write-Error "disable local auth for $storageAccountName failed, $_"
                Log "Disable local auth, $storageAccountName, Failed, $_"
        }
}

function Disable-Local-Auth-On-StorageAccounts {
        try {
                # Fetch the details of from the EnvironmentsDetails.json
                $environmentsDetailsFilePath = "..\ExpressV2\DeploymentData\EnvironmentsDetails.json"

                # Read the file content
                $environmentsDetailsJsonContent = Get-Content -Path $environmentsDetailsFilePath -Raw

                # Filtered Environments Details
                $environmentsDetails = @(($environmentsDetailsJsonContent | ConvertFrom-Json) | Where-Object { $_.SubscriptionId -eq $SubscriptionId })

                $environment = $environmentsDetails[0].Environment

                if ($environment -eq "Production" -or $environment -eq "Staging") {
                        $cloudEnvironment = "AzureCloud"
                }
                elseif ($environment -eq "Fairfax") {
                        $cloudEnvironment = "AzureUSGovernment"
                }
                elseif ($environment -eq "Mooncake") {
                        $cloudEnvironment = "AzureChinaCloud"
                }

                Write-Host $separator
                Write-Host "!!!!!!!!!!!!!  LOGIN using ame credentials    !!!!!!!!!!!!!"
                Write-Host $separator

                # Connect to the source Azure AD tenant
                Connect-AzAccount -Subscription $SubscriptionId -Environment $cloudEnvironment
                Set-AzContext -Subscription $SubscriptionId

                for ($i = 0; $i -lt $environmentsDetails.Count; $i++) {
                        $environmentsDetail = $environmentsDetails[$i]
                        $environment = $environmentsDetail.Environment

                        $resourceGroup = "adbrp$($environmentsDetail.ShortRegionName)"
                        $workspaceDataStorageAccountName = $environmentsDetail.WorkspaceDataStorageAccountName
                        $primaryJobsDataStorageAccountName = $environmentsDetail.PrimaryJobsDataStorageAccountName
                        $secondaryJobsdataStorageAccountName = $environmentsDetail.SecondaryJobsDataStorageAccountName
                        $backupDataStorageAccountName = "$($environmentsDetail.ShortRegionName)worksb"
                        $replicationDataStorageAccountName = "$($environmentsDetail.ShortRegionName)worksr"
                        $dscPackageStorageAccountName = "$($environmentsDetail.ShortRegionName)$($environmentsDetail.DSCPackageStorageAccountNameSuffix)"


                        Disable-Local-Auth -storageAccountName $workspaceDataStorageAccountName -resourceGroupName $resourceGroup 
                        Disable-Local-Auth -storageAccountName $primaryJobsDataStorageAccountName -resourceGroupName $resourceGroup 
                        Disable-Local-Auth -storageAccountName $secondaryJobsdataStorageAccountName -resourceGroupName $resourceGroup
                        Disable-Local-Auth -storageAccountName $backupDataStorageAccountName -resourceGroupName $resourceGroup
                        Disable-Local-Auth -storageAccountName $replicationDataStorageAccountName -resourceGroupName $resourceGroup
                        Disable-Local-Auth -storageAccountName $dscPackageStorageAccountName -resourceGroupName $resourceGroup

                        Write-Host $separator
                }

                Write-Host $separator
                Write-Host "!!!!!!!!!!!!!    END    !!!!!!!!!!!!!"
                Write-Host $separator  
        }
        catch {
                # Return the errors to the log.
                Write-Output "ERROR: $($_.Exception.Message)"
        }
}

Disable-Local-Auth-On-StorageAccounts