param(
    [Parameter(Mandatory = $true)][string]$Environment,
    [Parameter(Mandatory = $true)][string]$Region,
    [Parameter(Mandatory = $true)][string]$SubscriptionId
) #end param

$global:storageAccountEndpointSuffix = $null

$global:roleDefinitionNameCertUser = "Key Vault Certificate User"
$global:roleDefinitionNameSecretsUser = "Key Vault Secrets User"

$separator = "------------------------------------------------------------------------------------------------------------------------------------------------"

function Connect-AzureCloud {
    if ($Environment -eq "Public") {
        $environmentName = "AzureCloud"
    }
    elseif ($Environment -eq "Fairfax") {
        $environmentName = "AzureUSGovernment"
    }
    elseif ($Environment -eq "Mooncake") {
        $environmentName = "AzureChinaCloud"
    }
    else {
        Write-Error -Message "Unsupported environment '$($Environment)'" `
            -ErrorAction Stop
    }

    Write-Host "Attempting to login '$($environmentName)' ($($SubscriptionId))."

    Write-Host $separator

    Connect-AzAccount -Environment $environmentName -Subscription $SubscriptionId
    Set-AzContext -Subscription $SubscriptionId

    Write-Host "Login to '$($environmentName)' successfully."
}

function Get-CurrentUser {
    param (
        [Parameter(Mandatory = $true)]
        [string]$currentUserUpn
    ) #end param

    Write-Host $separator

    Write-Host "Attempting to Get Object Id of '$($currentUserUpn)'."

    Get-AzADUser -UserPrincipalName $currentUserUpn
}

function Remove-KeyVaultAccessPolicy {
    param (
        [Parameter(Mandatory = $true)]
        [string]$keyVaultName,
        [Parameter(Mandatory = $true)]
        [string]$currentUserId
    ) #end param
    
    $keyVaultResourceId = (Get-AzKeyVault -VaultName $keyVaultName).ResourceId

    # Removing "Key Vault Certificate User" role definition 
    $keyVaultRbacPermission = (Get-AzRoleAssignment -Scope $keyVaultResourceId -ObjectId $currentUserId -RoleDefinitionName $global:roleDefinitionNameCertUser)
    if ($null -ne $keyVaultRbacPermission) {
        Remove-AzRoleAssignment -Scope $keyVaultResourceId -ObjectId $currentUserId -RoleDefinitionName $global:roleDefinitionNameCertUser
        Write-Host "** '$($currentUserId)' successfully removed from '$($keyVaultName)' for role definition '$($global:roleDefinitionNameCertUser)'."
    }

    # Removing "Key Vault Secrets User" role definition
    $keyVaultRbacPermission = (Get-AzRoleAssignment -Scope $keyVaultResourceId -ObjectId $currentUserId -RoleDefinitionName $global:roleDefinitionNameSecretsUser)
    if ($null -ne $keyVaultRbacPermission) {
        Remove-AzRoleAssignment -Scope $keyVaultResourceId -ObjectId $currentUserId -RoleDefinitionName $global:roleDefinitionNameSecretsUser
        Write-Host "** '$($currentUserId)' successfully removed from '$($keyVaultName)' for role definition '$($global:roleDefinitionNameSecretsUser)'."
    }
}

function Set-KeyVaultAccessPolicy {
    param (
        [Parameter(Mandatory = $true)]
        [string]$keyVaultName,
        [Parameter(Mandatory = $true)]
        [string]$currentUserId
    ) #end param

    $keyVaultResourceId = (Get-AzKeyVault -VaultName $keyVaultName).ResourceId

    # Setting "Key Vault Certificate User" role definition 
    $keyVaultRbacPermission = (Get-AzRoleAssignment -Scope $keyVaultResourceId -ObjectId $currentUserId -RoleDefinitionName $global:roleDefinitionNameCertUser)
    if ($null -ne $keyVaultRbacPermission) {
        Write-Host "** '$($currentUserId)' already added in '$($keyVaultName)' for role definition '$($global:roleDefinitionNameCertUser)'."
    }
    else {
        New-AzRoleAssignment -Scope $keyVaultResourceId -ObjectId $currentUserId -RoleDefinitionName $global:roleDefinitionNameCertUser
        Write-Host "** '$($currentUserId)' successfully added in '$($keyVaultName)' for role definition '$($global:roleDefinitionNameCertUser)'."
    }

    # Setting "Key Vault Secrets User" role definition
    $keyVaultRbacPermission = (Get-AzRoleAssignment -Scope $keyVaultResourceId -ObjectId $currentUserId -RoleDefinitionName $global:roleDefinitionNameSecretsUser)
    if ($null -ne $keyVaultRbacPermission) {
        Write-Host "** '$($currentUserId)' already added in '$($keyVaultName)' for role definition '$($global:roleDefinitionNameSecretsUser)'."
    }
    else {
        New-AzRoleAssignment -Scope $keyVaultResourceId -ObjectId $currentUserId -RoleDefinitionName $global:roleDefinitionNameSecretsUser
        Write-Host "** '$($currentUserId)' successfully added in '$($keyVaultName)' for role definition '$($global:roleDefinitionNameSecretsUser)'."
    }
}

function Add-VMSSAdminPassword {
    param (
        [Parameter(Mandatory = $true)]
        [string]$shortLocationName,
        [Parameter(Mandatory = $true)]
        [string]$currentUserId
    ) #end param

    try {
        $adbrpPrefix = "adbrp"
        $keyVaultName = "$($adbrpPrefix)$($shortLocationName)secrets"
        $adminPasswordSecretName = "vmss-adminpassword"

        # Set KeyVault Access Policy.
        Set-KeyVaultAccessPolicy -keyVaultName $keyVaultName `
            -currentUserId $currentUserId
        
        # Allow Azure Resource Manager for template deployment
        Set-AzKeyVaultAccessPolicy -VaultName $keyVaultName -EnabledForTemplateDeployment

        # Create new VMSS Admin Password
        $adminPassword = [System.Web.Security.Membership]::GeneratePassword(15, 5)

        # Adds VMSS Admin Secret.
        $adminPasswordSecretValue = ConvertTo-SecureString -String $adminPassword -AsPlainText -Force
        $adminPasswordSecret = Set-AzKeyVaultSecret -VaultName $keyVaultName -Name $adminPasswordSecretName `
            -SecretValue $adminPasswordSecretValue

        Write-Host "VMSS Admin Password Secret in '$($shortLocationName)' created successfully."

        Write-Host $separator

        # Remove KeyVault Access Policy.
        Remove-KeyVaultAccessPolicy -keyVaultName $keyVaultName `
            -currentUserId $currentUserId
    }
    catch {
        Write-Error -Message "Something went wrong! Error '$($_.Exception)'"`
            -ErrorAction Stop
    }
}

function Init {
    $globalKeyVault = "adbrp-global-keyvault"
    $canaryShortLocationNames = "eastus2euap", "cenuseuap"
    $nonCustomerShortLocationNames = "auscentral2"
    $lowUsageShortLocatioNames = "korcentral", "eastasia", "uksouth", "cancentral", "caneast", "japanwest", "indwest", "safrnorth", "brazsouth", "francentral", "uaenorth", "switznorth", "norwayeast", "gerwcentral", "switzwest", "swedcentral", "wcentralus", "westus3", "qatcentral", "mexcentral"
    $mediumUsageShortLocatioNames = "westus", "seastasia", "eastus", "scentralus", "ausseast", "japaneast", "ukwest", "indcentral", "auseast", "indsouth", "ncentralus"
    $highUsageShortLocationNames = "westus2", "westeurope", "eastus2", "northeurope", "auscentral", "centralus"
    $fairfaxShortLocationNames = "govvirginia", "govarizona", "govtexas"
    $mooncakeShortLocationNames = "chinaeast2", "chinaeast3", "chinanorth2", "chinanorth3"

    Write-Host $separator

    $azAccount = Connect-AzureCloud

    if ($Region.ToLowerInvariant() -eq "canary") {
        $shortLocationNames = $canaryShortLocationNames
    }
    elseif ($Region.ToLowerInvariant() -eq "noncustomer") {
        $shortLocationNames = $nonCustomerShortLocationNames
    }
    elseif ($Region.ToLowerInvariant() -eq "lowusage") {
        $shortLocationNames = $lowUsageShortLocatioNames
    }
    elseif ($Region.ToLowerInvariant() -eq "mediumusage") {
        $shortLocationNames = $mediumUsageShortLocatioNames
    }
    elseif ($Region.ToLowerInvariant() -eq "highusage") {
        $shortLocationNames = $highUsageShortLocationNames
    }
    elseif ($Region.ToLowerInvariant() -eq "fairfax") {
        $shortLocationNames = $fairfaxShortLocationNames
    }
    elseif ($Region.ToLowerInvariant() -eq "mooncake") {
        $shortLocationNames = $mooncakeShortLocationNames
    }
    else {
        Write-Error -Message "Supported regions 'canary', 'noncustomer', 'lowuage', 'mediumusage', 'highusage', 'fairfax', 'mooncake'" `
            -ErrorAction Stop
    }

    $currentUserUpn = (Get-AzContext | Select-Object Account).Account.Id
    $currentUser = Get-CurrentUser -currentUserUpn $currentUserUpn
    $currentUserId = $currentUser.Id

    Write-Host $separator

    Write-Host "Object Id '$($currentUserId)' of '$($currentUserUpn)' retrieved successfully."

    Write-Host $separator

    # Adds System.Web assembly
    Add-Type -AssemblyName 'System.Web' 

    # Allow Azure Virtual Machines for deployment in Global Key Vault
    Set-AzKeyVaultAccessPolicy -VaultName $globalKeyVault -EnabledForDeployment

    # Allow Azure Resource Manager for template deployment in Global Key Vault
    Set-AzKeyVaultAccessPolicy -VaultName $globalKeyVault -EnabledForTemplateDeployment    

    ForEach ($shortLocationName in $shortLocationNames) {
        Add-VMSSAdminPassword -shortLocationName $shortLocationName `
            -currentUserId $currentUserId
    }

    Write-Host "Success in '$($shortLocationNames)'."

    Write-Host $separator
}

Init