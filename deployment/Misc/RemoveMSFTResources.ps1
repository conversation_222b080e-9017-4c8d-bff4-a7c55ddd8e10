param(
        [Parameter(Mandatory = $true)]
        [ValidateSet("Canary", "NonCustomer", "Pilot", "Medium", "Heavy", "ROWA", "ROWB")]
        [string]$Stage
)

$Global:StageMap = @{
        Canary      = @("EastUS2EUAP")
        NonCustomer = @("AustraliaCentral2")
        Pilot       = @("WestCentralUS", "EastAsia")
        Medium      = @("UKSouth")
        Heavy       = @("EastUS")
        ROWA        = @("AustraliaEast", "AustraliaCentral", "BrazilSouth", "CanadaCentral", "NorthEurope", "GermanyWestCentral", "CentralIndia", "WestIndia", "JapanEast", "KoreaCentral", "WestUS", "EastUS2", "NorthCentralUS", "SouthAfricaNorth", "SwedenCentral", "SwitzerlandWest", "QatarCentral")
        ROWB        = @("SouthEastAsia", "AustraliaSouthEast", "SouthCentralUS", "CanadaEast", "WestEurope", "FranceCentral", "SouthIndia", "JapanWest", "NorwayEast", "CentralUS", "WestUS2", "WestUS3", "SwitzerlandNorth", "UKWest", "UAENorth")
}

$separator = "--------------------------------------------------------------------------------------------------------------"

function Remove-MSFTResources {
        try {
                Write-Host $separator
                Write-Host "!!!!!!!!!!!!!    @microsoft for login    !!!!!!!!!!!!!"
                Write-Host $separator

                # Connect to the source Azure AD tenant
                Connect-AzAccount

                # Set the context for the MSFT Prod subscription
                Set-AzContext -Subscription "9ae8e263-8df7-4b7e-b27b-d59f0bfb685b"

                # Fetch the details of from the EnvironmentsDetails.json
                $environmentsDetailsFilePath = "..\ExpressV2\DeploymentData\EnvironmentsDetails.json"

                # Read the file content
                $environmentsDetailsJsonContent = Get-Content -Path $environmentsDetailsFilePath -Raw

                # Filtered Environments Details
                $environmentsDetails = @(($environmentsDetailsJsonContent | ConvertFrom-Json) | Where-Object { $_.Environment -eq "Production" -and $_.TenantName -eq "Microsoft" })

                $logfile = "MSFT_Sunset_$(Get-Date -Format "MM-dd_HH-mm-ss").txt"
                $stop = $false

                $regions = $Global:StageMap[$Stage];

                # Validate
                for ($i = 0; $i -lt $regions.Count; $i++) {
                        $region = $regions[$i]
                        $environmentsDetail = $environmentsDetails | Where-Object { $_.Region -eq $region }
                        $resourceGroup = "adbrp$($environmentsDetail.ShortRegionName)"

                        Write-Host "Start: $($region)"

                        $separator | Out-File -FilePath $logfile -Append
                        "                               ($($Region))" | Out-File -FilePath $logfile -Append
                        $separator | Out-File -FilePath $logfile -Append
                        "" | Out-File -FilePath $logfile -Append

                        $storageAccounts = Get-AzStorageAccount -ResourceGroupName $resourceGroup

                        # Check the count of storage accounts
                        if ($storageAccounts.Count -gt 0) {
                                $stop = $true
                                $log = "Validation Error: '$($storageAccounts.Count)' storage accounts found in $($resourceGroup)"
                                Write-Error $log
                                $log | Out-File -FilePath $logfile -Append
                        }
                }

                if ($stop -eq $true) {
                        $log = "!!!! Before decommissioning, ensure that the storage migration of '$($Stage)' is completed !!!!"
                        Write-Error $log
                        $log | Out-File -FilePath $logfile -Append

                        "" | Out-File -FilePath $logfile -Append
                        "- $($resourceGroup)" | Out-File -FilePath $logfile -Append
                        "" | Out-File -FilePath $logfile -Append

                        $storageAccounts | Format-Table | Out-File -FilePath $logfile -Append

                        $separator | Out-File -FilePath $logfile -Append
                        "" | Out-File -FilePath $logfile -Append
                }
                else {
                        $log = "No storage accounts were discovered in '$($Stage)', it is now safe to initiate the decommissioning process."
                        Write-Host $log
                        $log | Out-File -FilePath $logfile -Append

                        for ($i = 0; $i -lt $regions.Count; $i++) {
                                $region = $regions[$i]
                                $environmentsDetail = $environmentsDetails | Where-Object { $_.Region -eq $region }
                                $resourceGroup = "adbrp$($environmentsDetail.ShortRegionName)"

                                $managedCluster = "adbrp$($environmentsDetail.ShortRegionName)"
                                $keyVault = "adbrp$($environmentsDetail.ShortRegionName)secrets"

                                Write-Host "Start decomissioning in '$($region)'"

                                # Delete Service Fabric cluster
                                Remove-AzResource -ResourceName $managedCluster -ResourceType "Microsoft.ServiceFabric/managedclusters" -ResourceGroupName $resourceGroup -Force

                                $log = "- Service Fabric cluster '$managedCluster' has been deleted"
                                Write-Host $log
                                $log | Out-File -FilePath $logfile -Append

                                # Delete Azure Keyvault
                                Remove-AzKeyVault -VaultName $keyVault -ResourceGroupName $resourceGroup -Force

                                $log = "- Key Vault '$keyVault' has been deleted"
                                Write-Host $log
                                $log | Out-File -FilePath $logfile -Append

                                # Get the resources within the resource group
                                $resources = Get-AzResource -ResourceGroupName $resourceGroup

                                # Check if the resource group is empty
                                if ($resources.Count -eq 0) {
                                        # Delete the resource group
                                        Remove-AzResourceGroup -Name $resourceGroup -Force
                                        Write-Host "- Resource group '$resourceGroup' has been deleted"
                                }
                                else {
                                        $log = "!!!! Before deleting Resource group '$resourceGroup', ensure it is empty !!!!"
                                        Write-Error $log
                                        $log | Out-File -FilePath $logfile -Append
                                }
                        }
                }

                Write-Host $separator
                Write-Host "!!!!!!!!!!!!!    END    !!!!!!!!!!!!!"
                Write-Host $separator  
        }
        catch {
                # Return the errors to the log.
                Write-Output "ERROR: $($_.Exception.Message)"
        }
}

Remove-MSFTResources