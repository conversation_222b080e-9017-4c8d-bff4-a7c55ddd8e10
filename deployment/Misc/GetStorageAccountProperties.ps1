Get-AzStorageAccount `
| Where-Object { $_.ResourceGroupName -like "adbrp*" } `
| Select-Object StorageAccountName, `
    ResourceGroupName, `
    PrimaryLocation, `
    @{Name = "SkuName"; Expression = { $_.Sku.Name } }, `
    @{Name = "SkuTier"; Expression = { $_.Sku.Tier } }, `
    Kind, `
    EnableHttpsTrafficOnly, `
    MinimumTlsVersion, `
    AllowBlobPublicAccess `
| Export-Csv -Path "adbrp-storageaccounts.metadata.csv"