param(
    [Parameter(Mandatory = $true)][string]$MSIObjectId,
    [Parameter(Mandatory = $true)][string]$Region,
    [Parameter(Mandatory = $true)][string]$SubscriptionId
)

$canaryShortLocationNames = "eastus2euap", "cenuseuap"
$nonCustomerShortLocationNames = "auscentral2"
$lowUsageShortLocatioNames = "korcentral", "eastasia", "uksouth", "cancentral", "caneast", "japanwest", "indwest", "safrnorth", "brazsouth", "francentral", "uaenorth", "switznorth", "norwayeast", "gerwcentral", "switzwest", "swedcentral", "wcentralus", "westus3", "qatcentral", "mexcentral"
$mediumUsageShortLocatioNames = "westus", "seastasia", "eastus", "scentralus", "ausseast", "japaneast", "ukwest", "indcentral", "auseast", "indsouth", "ncentralus"
$highUsageShortLocationNames = "westus2", "westeurope", "eastus2", "northeurope", "auscentral", "centralus"
$separator = "--------------------------------------------------------------------------------------------------------------------------------------------------------------------------"

$roleDefinitionNameCertUser = "Key Vault Certificate User"
$roleDefinitionNameSecretsUser = "Key Vault Secrets User"

function Add-RolloutMSI {
    Connect-AzAccount
    # MSFT Tenant contains more than one active subscription.
    Set-AzContext -Subscription $subscriptionId

    if ($Region.ToLowerInvariant() -eq "canary") {
        $ShortLocationNames = $canaryShortLocationNames
    }
    elseif ($Region.ToLowerInvariant() -eq "noncustomer") {
        $ShortLocationNames = $nonCustomerShortLocationNames
    }
    elseif ($Region.ToLowerInvariant() -eq "lowusage") {
        $ShortLocationNames = $lowUsageShortLocatioNames
    }
    elseif ($Region.ToLowerInvariant() -eq "mediumusage") {
        $ShortLocationNames = $mediumUsageShortLocatioNames
    }
    elseif ($Region.ToLowerInvariant() -eq "highusage") {
        $ShortLocationNames = $highUsageShortLocationNames
    }
    else {
        Write-Error -Message "Supported regions 'canary', 'noncustomer', 'lowuage', 'mediumusage', 'highusage'" `
            -ErrorAction Stop
    }

    Write-Host $separator

    Write-Host "Attempting to update '$($ShortLocationNames)'."

    Write-Host $separator

    ForEach ($shortLocationName in $ShortLocationNames) {
        $keyVaultName = "adbrp$($shortLocationName)secrets"
        $keyVaultResourceId = (Get-AzKeyVault -VaultName $keyVaultName).ResourceId

         # Setting "Key Vault Certificate User" role definition 
        $keyVaultRbacPermission = (Get-AzRoleAssignment -Scope $keyVaultResourceId -ObjectId $MSIObjectId -RoleDefinitionName $roleDefinitionNameCertUser)
        if ($null -ne $keyVaultRbacPermission) {            
            Write-Host "** 'databricks-region-rollout-msi' already added in '$($keyVaultName)' for role definition '$($roleDefinitionNameCertUser)'."
        }
        else {            
            New-AzRoleAssignment -Scope $keyVaultResourceId -ObjectId $MSIObjectId -RoleDefinitionName $roleDefinitionNameCertUser
            Write-Host "** 'databricks-region-rollout-msi' successfully added in '$($keyVaultName)' for role definition '$($roleDefinitionNameCertUser)'."
        }

        # Setting "Key Vault Secrets User" role definition
        $keyVaultRbacPermission = (Get-AzRoleAssignment -Scope $keyVaultResourceId -ObjectId $MSIObjectId -RoleDefinitionName $roleDefinitionNameSecretsUser)
        if ($null -ne $keyVaultRbacPermission) {            
            Write-Host "** 'databricks-region-rollout-msi' already added in '$($keyVaultName)' for role definition '$($roleDefinitionNameSecretsUser)'."
        }
        else {            
            New-AzRoleAssignment -Scope $keyVaultResourceId -ObjectId $MSIObjectId -RoleDefinitionName $roleDefinitionNameSecretsUser
            Write-Host "** 'databricks-region-rollout-msi' successfully added in '$($keyVaultName)' for role definition '$($roleDefinitionNameSecretsUser)'."
        }

    }

    Write-Host $separator

    Write-Host "'$($ShortLocationNames)' updated successfully."

    Write-Host $separator
}


Add-RolloutMSI