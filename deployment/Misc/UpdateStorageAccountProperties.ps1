param(
    [Parameter(Mandatory = $true)][string]$Region,
    [Parameter(Mandatory = $true)][string]$SubscriptionId
)

$canaryShortLocationNames = "eastus2euap", "centraleuap", "cenuseuap"
$nonCustomerShortLocationNames = "auscentral2"
$lowUsageShortLocatioNames = "korcentral", "eastasia", "uksouth", "cancentral", "caneast", "japanwest", "indwest", "safrnorth", "brazsouth", "francentral", "uaenorth", "switznorth", "norwayeast", "gerwcentral", "westus3", "qatcentral", "mexcentral"
$mediumUsageShortLocatioNames = "westus", "seastasia", "eastus", "scentralus", "ausseast", "japaneast", "ukwest", "indcentral", "auseast", "indsouth", "ncentralus"
$highUsageShortLocationNames = "westus2", "westeurope", "eastus2", "northeurope", "auscentral", "centralus"
$separator = "-------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------"

function Update-StorageAccountProperties {
    param (
        [Parameter(Mandatory = $true)][string]$ResourceGroupName,
        [Parameter(Mandatory = $true)][string]$StorageAccountName
    )
    
    try {
        # Upgrade a Storage account with Kind "Storage" to "StorageV2" kind Storage account
        $storage = Set-AzStorageAccount -ResourceGroupName $ResourceGroupName -AccountName $StorageAccountName -UpgradeToStorageV2

        # Set EnableHttpsTrafficOnly, MinimumTlsVersion and AllowBlobPublicAccess
        $storage = Set-AzStorageAccount -ResourceGroupName $ResourceGroupName -AccountName $StorageAccountName -EnableHttpsTrafficOnly $true -MinimumTlsVersion TLS1_2 -AllowBlobPublicAccess $false
    }
    catch {
        # Return the errors to the log.
        Write-Error "Something went wrong, while updating - ResourceGroupName: '$($ResourceGroupName)', StorageAccountName: '$($StorageAccountName)'."
        Write-Error "Error occurred while running command: '$($_.InvocationInfo.MyCommand.Name)'"
        Write-Error "ERROR: $($_.Exception.Message)"
    }
}

function Update-RegionalStorageAccounts {
    Connect-AzAccount
    # MSFT Tenant contains more than one active subscription.
    Set-AzContext -Subscription $subscriptionId

    if ($Region.ToLowerInvariant() -eq "canary") {
        $ShortLocationNames = $canaryShortLocationNames
    }
    elseif ($Region.ToLowerInvariant() -eq "noncustomer") {
        $ShortLocationNames = $nonCustomerShortLocationNames
    }
    elseif ($Region.ToLowerInvariant() -eq "lowusage") {
        $ShortLocationNames = $lowUsageShortLocatioNames
    }
    elseif ($Region.ToLowerInvariant() -eq "mediumusage") {
        $ShortLocationNames = $mediumUsageShortLocatioNames
    }
    elseif ($Region.ToLowerInvariant() -eq "highusage") {
        $ShortLocationNames = $highUsageShortLocationNames
    }
    else {
        Write-Error -Message "Supported regions 'canary', 'noncustomer', 'lowuage', 'mediumusage', 'highusage'" `
            -ErrorAction Stop
    }

    Write-Host $separator

    Write-Host "Attempting to update '$($ShortLocationNames)'."

    Write-Host $separator

    ForEach ($shortLocationName in $ShortLocationNames) {
        $resourceGroupName = "adbrp$($shortLocationName)"
        $storageAccounts = Get-AzStorageAccount -ResourceGroupName $resourceGroupName

        ForEach ($storageAccount in $storageAccounts) {
            $storageAccountName = $storageAccount.StorageAccountName

            Write-Host "Attempting to update - ResourceGroupName: '$($resourceGroupName)', StorageAccountName: '$($storageAccountName)'."

            Update-StorageAccountProperties -ResourceGroupName $resourceGroupName -StorageAccountName $storageAccountName

            Write-Host "ResourceGroupName: '$($resourceGroupName)', StorageAccountName: '$($storageAccountName)' updated Successfully." -ForegroundColor Green
            
            Write-Host $separator
        }
    }

    Write-Host "'$($ShortLocationNames)' updated successfully."

    Write-Host $separator
}

Update-RegionalStorageAccounts