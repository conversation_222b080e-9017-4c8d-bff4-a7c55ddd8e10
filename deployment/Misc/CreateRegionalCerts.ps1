param(
    [Parameter(Mandatory = $true)][string]$EnvironmentDetailsFilePath
)
function AddToLogFile($logStatement) {
    $filePath = ".\CreateRegionalCertsLogFile.csv";
    $date = get-date
    $logStatement = "$date,$logStatement"

    if (!(Test-Path $filePath -ErrorAction SilentlyContinue)) {
        New-Item -path $filePath -type "file"
        Add-Content -path $filePath -value $logStatement
    }
    else {
        Add-Content -path $filePath -value $logStatement
    }
}

function GetObjectIdFromGroup([string] $groupName)
{
  $matchingGroup = Get-AzADGroup -DisplayName $groupName
  return $matchingGroup.Id
}

function GetObjectIdFromServicePrincipal([string] $principalName)
{
  $matchingApp = Get-AzADServicePrincipal -DisplayName $principalName
  return $matchingApp.Id
}

function CreateCertificate([string]$keyVaultName, [string]$certName, [string]$subjectName, [string]$issuerName, [string[]]$dnsName) {
    try {
        Set-AzKeyVaultCertificateIssuer -VaultName $keyVaultName -IssuerProvider $issuerName -IssuerName $issuerName
        # Create a certificate policy with SANs
        $policy = New-AzKeyVaultCertificatePolicy -SecretContentType "application/x-pkcs12" -SubjectName $subjectName -IssuerName $issuerName -ValidityInMonths 12 -RenewAtPercentageLifetime 24 -DnsName $dnsName
        Add-AzKeyVaultCertificate -VaultName $keyVaultName -Name $certName -CertificatePolicy $policy
        Get-AzKeyVaultCertificateOperation -VaultName $keyVaultName -Name $certName

        $logStatement = "$keyVaultName,$certName,successful"
    }
    catch {
        $logStatement = "$keyVaultName,$certName,failed"
    }
    AddToLogFile $logStatement
}

function SetCloudSpeficVaiables([object[]] $environmentDetail) {
    if ($environmentDetail.Environment -eq "Production") {
        $global:cloudEnvironment = "AzureCloud"
        $global:adbRpSfAdminCertSubjectName = "CN=adbrpsfadmin.azclient.ms"
        $global:databricksRpAadCertSubjectName = "CN=adbrpfpa.azclient.ms"
        $global:databricksRpSslCertSubjectName = "CN=*.adb-spearfish-rp.net"
        $global:dnsName = "*.adb-spearfish-rp.net"
    }
    elseif ($environmentDetail.Environment -eq "Fairfax") {
        $global:cloudEnvironment = "AzureUSGovernment"
        $global:adbRpSfAdminCertSubjectName = "CN=adbrpsfadmin.azclient.us"
        $global:databricksRpAadCertSubjectName = "CN=databricksrp.windowsazure.us"
        $global:databricksRpSslCertSubjectName = "CN=*.adb-spearfish-rp.azure.us"
        $global:dnsName = "*.adb-spearfish-rp.azure.us"
    }
    elseif ($environmentDetail.Environment -eq "Mooncake") {
        $global:cloudEnvironment = "AzureChinaCloud"
        $global:adbRpSfAdminCertSubjectName = "CN=adbrpsfadmin.azclient.cn"
        $global:databricksRpAadCertSubjectName = "CN=databricksrp.azure.cn"
        $global:databricksRpSslCertSubjectName = "CN=*.adb-spearfish-rp.azure.cn"
        $global:dnsName = "*.adb-spearfish-rp.azure.cn"
    }
    else {
        Write-Error -Message "Unsupported environment '$($environment)'" ` -ErrorAction Stop
    }  
}

function ConnectToSubscription([object[]] $environmentDetail, [string] $cloudEnvironment) {
    $connectToSub = 1
    $retryCount = 0
    $maxRetries = 30
    
    do {
        try {
            # Connect to MSI
            Write-Output "Attempting to connect to Azure"
            #Connect-AzAccount -Identity -Environment $cloudEnvironment
            Connect-AzAccount -Tenant $environmentDetail.TenantId -SubscriptionId $environmentDetail.SubscriptionId -Environment $cloudEnvironment
    
            Write-Output "Connection to Azure environment was successful!"
            $connectToSub = 0
        }
        catch {
            Write-Output "Connection to Azure failed, retrying."
            $connectToSub = 1
            $retryCount++
        }
    } while ($connectToSub -eq 1 -and $retryCount -lt $maxRetries)
}

try {
    $environmentDetails = Get-Content $EnvironmentDetailsFilePath | ConvertFrom-Json
    $prevEnvironemnt = "";
    Foreach ($environmentDetail in $environmentDetails) {
        if ($prevEnvironemnt -ne $environmentDetail.Environment) {
            SetCloudSpeficVaiables -environmentDetail $environmentDetail
            ConnectToSubscription -environmentDetail $environmentDetail -cloudEnvironment $cloudEnvironment
            $prevEnvironemnt = $environmentDetail.Environment;
        }
        
        $roleDefinitionName = "Key Vault Certificate User"

        $keyVaultName = "adbrp" + $environmentDetail.ShortRegionName + "secrets"
        $keyVaultResourceId = (Get-AzKeyVault -VaultName $keyVaultName).ResourceId

        $adbrpCertExpiryListenerServicePrincipalId = GetObjectIdFromServicePrincipal "adbrp-cert-expiry-listener"
        New-AzRoleAssignment -Scope $keyVaultResourceId -ObjectId $adbrpCertExpiryListenerServicePrincipalId -RoleDefinitionName $roleDefinitionName

        $genevaActionsProductionAADServicePrincipalId = GetObjectIdFromServicePrincipal "GenevaActionsProductionAAD"
        New-AzRoleAssignment -Scope $keyVaultResourceId -ObjectId $genevaActionsProductionAADServicePrincipalId -RoleDefinitionName $roleDefinitionName

        CreateCertificate -keyVaultName $keyVaultName -certName "adbrpsfadmincertificate" -subjectName $adbRpSfAdminCertSubjectName -issuerName "OneCertV2-PrivateCA" -dnsName @()
        CreateCertificate -keyVaultName $keyVaultName -certName "adbrpfpa" -subjectName $databricksRpAadCertSubjectName -issuerName "OneCertV2-PrivateCA" -dnsName @()
        CreateCertificate -keyVaultName $keyVaultName -certName "databricksrpssl" -subjectName $databricksRpSslCertSubjectName -issuerName "OneCertV2-PrivateCA" -dnsName @($global:dnsName)
        Write-Host "Regional Certs created for" $environmentDetail.Region
    }
}
catch {
    Write-Host $_ | Out-String
    Write-Error -Message "Error executing the powershell script" ` -ErrorAction Stop
}

