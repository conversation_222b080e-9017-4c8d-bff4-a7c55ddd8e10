param(
        [Parameter(Mandatory = $true)][string]$SubscriptionId
)

$roleDefinitionNameCertUser = "Key Vault Certificate User"
$roleDefinitionNameSecretsUser = "Key Vault Secrets User"

$separator = "------------------------------------------------------------------------"

function Add-Certificate {
        param(
                [string]$keyVaultName, 
                [string]$certName, 
                [string]$subjectName, 
                [string]$issuerName, 
                [string[]]$dnsName
        )

        try {
                Set-AzKeyVaultCertificateIssuer -VaultName $keyVaultName -IssuerProvider $issuerName -IssuerName $issuerName
                # Create a certificate policy with SANs
                if ($dnsName.Length -gt 0) {
                        $policy = New-AzKeyVaultCertificatePolicy -SecretContentType "application/x-pkcs12" -SubjectName $subjectName -IssuerName $issuerName -ValidityInMonths 12 -RenewAtPercentageLifetime 24 -DnsName $dnsName
                }
                else {
                        $policy = New-AzKeyVaultCertificatePolicy -SecretContentType "application/x-pkcs12" -SubjectName $subjectName -IssuerName $issuerName -ValidityInMonths 12 -RenewAtPercentageLifetime 24
                }
                Add-AzKeyVaultCertificate -VaultName $keyVaultName -Name $certName -CertificatePolicy $policy
                Get-AzKeyVaultCertificateOperation -VaultName $keyVaultName -Name $certName

                Write-Host "$keyVaultName, $certName creation successful"
        }
        catch {
                Write-Error "$keyVaultName, $certName creation failed, $_"
        }
}

function Add-FPACert {
        try {
                Write-Host $separator
                Write-Host "!!!!!!!!!!!!!    USE ame.gbl for LOGIN    !!!!!!!!!!!!!"
                Write-Host $separator

                # Connect to the source Azure AD tenant
                Connect-AzAccount -Subscription $SubscriptionId
                Set-AzContext -Subscription $SubscriptionId

                # Get the current context
                $currentContext = Get-AzContext

                # Extract the Object ID of the currently authenticated user
                $currentUserId = (Get-AzADUser -UserPrincipalName $currentContext.Account.Id).Id

                # Fetch the details of from the EnvironmentsDetails.json
                $environmentsDetailsFilePath = "..\ExpressV2\DeploymentData\EnvironmentsDetails.json"

                # Read the file content
                $environmentsDetailsJsonContent = Get-Content -Path $environmentsDetailsFilePath -Raw

                # Filtered Environments Details
                $environmentsDetails = @(($environmentsDetailsJsonContent | ConvertFrom-Json) | Where-Object { $_.Environment -eq "Production" -and $_.SubscriptionId -eq $SubscriptionId })

                for ($i = 0; $i -lt $environmentsDetails.Count; $i++) {
                    $environmentsDetail = $environmentsDetails[$i]

                    $keyVaultName = "adbrp$($environmentsDetail.ShortRegionName)$($environmentsDetail.KeyVaultNameSuffix)"
                    $keyVaultResourceId = (Get-AzKeyVault -VaultName $keyVaultName).ResourceId
                    
                    $resourceGroup = "adbrp$($environmentsDetail.ShortRegionName)"

                    # Add a new access policy to read secrets
                    New-AzRoleAssignment -Scope $keyVaultResourceId -ObjectId $currentUserId -RoleDefinitionName $roleDefinitionNameCertUser
                    New-AzRoleAssignment -Scope $keyVaultResourceId -ObjectId $currentUserId -RoleDefinitionName $roleDefinitionNameSecretsUser

                    # Add RP 1P cert
                    $certName = "adbrpfpa"
                    $subjectName = "CN=adbrpfpa.azclient.ms"
                    $dnsName = "adbrpfpa.azclient.ms"
                    $certIssuerName = "OneCertV2-PrivateCA"

                    Add-Certificate -keyVaultName $keyVaultName -certName $certName -subjectName $subjectName -issuerName $certIssuerName -dnsName @($dnsName)

                    Write-Host $separator

                    # Remove the access policy
                    Remove-AzRoleAssignment -Scope $keyVaultResourceId -ObjectId $currentUserId -RoleDefinitionName $roleDefinitionNameCertUser
                    Remove-AzRoleAssignment -Scope $keyVaultResourceId -ObjectId $currentUserId -RoleDefinitionName $roleDefinitionNameSecretsUser
                }

                Write-Host $separator
                Write-Host "!!!!!!!!!!!!!    END    !!!!!!!!!!!!!"
                Write-Host $separator  
        }
        catch {
                # Return the errors to the log.
                Write-Output "ERROR: $($_.Exception.Message)"
        }
}

Add-FPACert