{"$schema": "https://schema.management.azure.com/schemas/2019-04-01/deploymentTemplate.json#", "contentVersion": "*******", "parameters": {"clusterName": {"type": "string", "metadata": {"description": "Name of your cluster - Between 3 and 23 characters. Letters and numbers only"}}, "applicationTypeName": {"type": "string", "metadata": {"description": "The application type name."}}, "applicationTypeVersion": {"type": "string", "metadata": {"description": "The application type version."}}, "appPackageUrl": {"type": "securestring", "metadata": {"description": "The URL to the application package sfpkg file."}}, "applicationName": {"type": "string", "metadata": {"description": "The name of the application resource."}}, "aadCertThumbprint": {"type": "string"}, "aadCertSubjectName": {"type": "string"}, "sslCertSubjectName": {"type": "string"}, "featureServiceName": {"type": "string", "metadata": {"description": "The name of the service resource in the format of {applicationName}~{serviceName}."}}, "featureServiceTypeName": {"type": "string", "metadata": {"description": "The name of the service type."}}, "featureServiceInstanceCount": {"type": "int", "metadata": {"description": "The number of instances."}}, "workerServiceName": {"type": "string", "defaultValue": "Providers.ServiceFabric.WorkerRole", "metadata": {"description": "The name of the service resource in the format of {applicationName}~{serviceName}."}}, "workerServiceTypeName": {"type": "string", "metadata": {"description": "The name of the service type."}}, "workerServiceInstanceCount": {"type": "int", "metadata": {"description": "The number of instances."}}, "jobServiceName": {"type": "string", "defaultValue": "Providers.ServiceFabric.JobRole", "metadata": {"description": "The name of the service resource in the format of {applicationName}~{serviceName}."}}, "jobServiceTypeName": {"type": "string", "metadata": {"description": "The name of the service type."}}, "jobServiceInstanceCount": {"type": "int", "metadata": {"description": "The number of instances."}}, "roleLocation": {"type": "string"}, "tenantId": {"type": "string"}, "mainTemplateFilePath_Dev": {"type": "string"}, "mainTemplateFilePath_Staging": {"type": "string"}, "mainTemplateFilePath_Prod": {"type": "string"}, "keyVaultEndpoint": {"type": "string"}, "databricksNetworkIntentPoliciesFilePath_Dev": {"type": "string"}, "databricksNetworkIntentPoliciesFilePath_Staging": {"type": "string"}, "databricksNetworkIntentPoliciesFilePath_Prod": {"type": "string"}, "databricksOutboundNetworkEndpointsFilePath": {"type": "string"}, "databricksOutboundNetworkEndpointsFilePath_NPIP": {"type": "string"}, "applicationPort": {"type": "string"}, "jobDefinitionsTableName": {"type": "string"}, "jobTriggersQueuePrefix": {"type": "string"}, "region": {"type": "string"}, "kustoClusterUri": {"type": "string"}, "primaryJobsDataStorageAccountName": {"type": "string"}, "secondaryJobsDataStorageAccountName": {"type": "string"}, "workspaceDataStorageAccountName": {"type": "string"}, "dscPackageStorageAccountNameSuffix": {"type": "string"}, "argEndpoint": {"type": "string"}, "shortRegionName": {"type": "string"}}, "variables": {"sfApiVersion": "2022-01-01", "appVersion": "[concat(resourcegroup().id,'/providers/Microsoft.ServiceFabric/managedClusters/',parameters('clusterName'),'/applicationTypes/', parameters('applicationTypeName'), '/versions/', parameters('applicationTypeVersion'))]", "clusterLocation": "[resourcegroup().location]"}, "resources": [{"apiVersion": "[variables('sfApiVersion')]", "type": "Microsoft.ServiceFabric/managedclusters/applicationTypes", "name": "[concat(parameters('clusterName'), '/', parameters('applicationTypeName'))]", "location": "[variables('clusterLocation')]", "dependsOn": [], "properties": {}}, {"apiVersion": "[variables('sfApiVersion')]", "type": "Microsoft.ServiceFabric/managedclusters/applicationTypes/versions", "name": "[concat(parameters('clusterName'), '/', parameters('applicationTypeName'), '/', parameters('applicationTypeVersion'))]", "location": "[variables('clusterLocation')]", "dependsOn": ["[concat('Microsoft.ServiceFabric/managedclusters/', parameters('clusterName'), '/applicationTypes/', parameters('applicationTypeName'))]"], "properties": {"appPackageUrl": "[parameters('appPackageUrl')]"}}, {"apiVersion": "[variables('sfApiVersion')]", "type": "Microsoft.ServiceFabric/managedclusters/applications", "name": "[concat(parameters('clusterName'), '/', parameters('applicationName'))]", "location": "[variables('clusterLocation')]", "dependsOn": ["[concat('Microsoft.ServiceFabric/managedclusters/', parameters('clusterName'), '/applicationTypes/', parameters('applicationTypeName'), '/versions/', parameters('applicationTypeVersion'))]"], "properties": {"version": "[variables('appVersion')]", "parameters": {"RoleLocation": "[parameters('roleLocation')]", "TenantId": "[parameters('tenantId')]", "MainTemplateFilePath_Dev": "[parameters('mainTemplateFilePath_Dev')]", "MainTemplateFilePath_Staging": "[parameters('mainTemplateFilePath_Staging')]", "MainTemplateFilePath_Prod": "[parameters('mainTemplateFilePath_Prod')]", "KeyVaultEndpoint": "[parameters('keyVaultEndpoint')]", "DatabricksNetworkIntentPoliciesFilePath_Dev": "[parameters('databricksNetworkIntentPoliciesFilePath_Dev')]", "DatabricksNetworkIntentPoliciesFilePath_Staging": "[parameters('databricksNetworkIntentPoliciesFilePath_Staging')]", "DatabricksNetworkIntentPoliciesFilePath_Prod": "[parameters('databricksNetworkIntentPoliciesFilePath_Prod')]", "DatabricksOutboundNetworkEndpointsFilePath": "[parameters('databricksOutboundNetworkEndpointsFilePath')]", "DatabricksOutboundNetworkEndpointsFilePath_NPIP": "[parameters('databricksOutboundNetworkEndpointsFilePath_NPIP')]", "AADCertThumbprint": "[parameters('aadCertThumbprint')]", "AADCertSubjectName": "[parameters('aadCertSubjectName')]", "SSLCertSubjectName": "[parameters('sslCertSubjectName')]", "ApplicationPort": "[parameters('applicationPort')]", "JobDefinitionsTableName": "[parameters('jobDefinitionsTableName')]", "JobTriggersQueuePrefix": "[parameters('jobTriggersQueuePrefix')]", "Region": "[parameters('region')]", "KustoClusterUri": "[parameters('kustoClusterUri')]", "PrimaryJobsDataStorageAccountName": "[parameters('primaryJobsDataStorageAccountName')]", "SecondaryJobsDataStorageAccountName": "[parameters('secondaryJobsDataStorageAccountName')]", "WorkspaceDataStorageAccountName": "[parameters('workspaceDataStorageAccountName')]", "DSCPackageStorageAccountNameSuffix": "[parameters('dscPackageStorageAccountNameSuffix')]", "ARGEndpoint": "[parameters('argEndpoint')]", "ShortRegionName": "[parameters('shortRegionName')]"}, "upgradePolicy": {"upgradeReplicaSetCheckTimeout": "3600", "forceRestart": "false", "rollingUpgradeMonitoringPolicy": {"failureAction": "Rollback", "healthCheckWaitDuration": "00:00:30.0", "healthCheckStableDuration": "00:00:30.0", "healthCheckRetryTimeout": "00:15:00.0", "upgradeTimeout": "01:00:00.0", "upgradeDomainTimeout": "00:20:00.0"}, "applicationHealthPolicy": {"considerWarningAsError": "false", "maxPercentUnhealthyDeployedApplications": "0", "defaultServiceTypeHealthPolicy": {"maxPercentUnhealthyServices": "0", "maxPercentUnhealthyPartitionsPerService": "0", "maxPercentUnhealthyReplicasPerPartition": "0"}}}}}, {"apiVersion": "[variables('sfApiVersion')]", "type": "Microsoft.ServiceFabric/managedclusters/applications/services", "name": "[concat(parameters('clusterName'), '/', parameters('applicationName'), '/', parameters('featureServiceName'))]", "location": "[variables('clusterLocation')]", "dependsOn": ["[concat('Microsoft.ServiceFabric/managedclusters/', parameters('clusterName'), '/applications/', parameters('applicationName'))]"], "properties": {"serviceKind": "Stateless", "serviceTypeName": "[parameters('featureServiceTypeName')]", "partitionDescription": {"partitionScheme": "<PERSON><PERSON>"}, "instanceCount": "[parameters('featureServiceInstanceCount')]"}}, {"apiVersion": "[variables('sfApiVersion')]", "type": "Microsoft.ServiceFabric/managedclusters/applications/services", "name": "[concat(parameters('clusterName'), '/', parameters('applicationName'), '/', parameters('workerServiceName'))]", "location": "[variables('clusterLocation')]", "dependsOn": ["[concat('Microsoft.ServiceFabric/managedclusters/', parameters('clusterName'), '/applications/', parameters('applicationName'))]"], "properties": {"serviceKind": "Stateless", "serviceTypeName": "[parameters('workerServiceTypeName')]", "partitionDescription": {"partitionScheme": "<PERSON><PERSON>"}, "instanceCount": "[parameters('workerServiceInstanceCount')]"}}, {"apiVersion": "[variables('sfApiVersion')]", "type": "Microsoft.ServiceFabric/managedclusters/applications/services", "name": "[concat(parameters('clusterName'), '/', parameters('applicationName'), '/', parameters('jobServiceName'))]", "location": "[variables('clusterLocation')]", "dependsOn": ["[concat('Microsoft.ServiceFabric/managedclusters/', parameters('clusterName'), '/applications/', parameters('applicationName'))]"], "properties": {"serviceKind": "Stateless", "serviceTypeName": "[parameters('jobServiceTypeName')]", "partitionDescription": {"partitionScheme": "<PERSON><PERSON>"}, "instanceCount": "[parameters('jobServiceInstanceCount')]"}}]}