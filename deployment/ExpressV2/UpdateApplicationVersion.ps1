param(
[Parameter(Mandatory=$true)]
$PackageLocation,
[Parameter(Mandatory=$true)]
$BuildVersion
)

#Update Service manifest/CodePackage/ConfigPackage versions in service manifest file
function UpdateServiceManifests()
{
    param(
        [Parameter(Mandatory = $true)][string]$Environment
    )
    
    $applicationManifest = "$($PackageLocation)\\$($Environment)\\ApplicationManifest.xml"
    $applicationManifestXml = (Select-Xml -Path $applicationManifest -XPath /).Node
    $serviceManifestNames = $applicationManifestXml.ApplicationManifest.ServiceManifestImport.ServiceManifestRef.ServiceManifestName

    foreach($item in $serviceManifestNames)
    {
        $serviceManifest = "$($PackageLocation)\\$($Environment)\\$($item)\\ServiceManifest.xml"
        $serviceManifestXml = (Select-Xml -Path $serviceManifest -XPath /).Node
        $ServiceManifestXml.ServiceManifest.Version = $BuildVersion
        $serviceManifestXml.ServiceManifest.CodePackage.Version = $BuildVersion
        $serviceManifestXml.ServiceManifest.ConfigPackage.Version = $BuildVersion
        $serviceManifestXml.Save($serviceManifest)
    }
}

#Update ApplicationTypeVersion/ServiceManifestVersion versions in Application manifest file
function UpdateApplicationManifest()
{    
    param(
        [Parameter(Mandatory = $true)][string]$Environment
    )

    $applicationManifest = "$($PackageLocation)\\$($Environment)\\ApplicationManifest.xml"
    $applicationManifestXml = (Select-Xml -Path $applicationManifest -XPath /).Node
    $applicationManifestXml.ApplicationManifest.ApplicationTypeVersion = $BuildVersion
    $applicationManifestXml.ApplicationManifest.ServiceManifestImport.ServiceManifestRef | ForEach-Object{$_.ServiceManifestVersion=$BuildVersion}
    $applicationManifestXml.Save($applicationManifest)
}

#Production
Write-Output "Updating version in (Production) Application Manifest...with $BuildVersion"
UpdateApplicationManifest -Environment "Production"
Write-Output "Updating version in (Production) Service Manifest... with $BuildVersion"
UpdateServiceManifests -Environment "Production"

#Fairfax
Write-Output "Updating version in (Fairfax) Application Manifest...with $BuildVersion"
UpdateApplicationManifest -Environment "Fairfax"
Write-Output "Updating version in (Fairfax) Service Manifest... with $BuildVersion"
UpdateServiceManifests -Environment "Fairfax"

#Mooncake
Write-Output "Updating version in (Mooncake) Application Manifest...with $BuildVersion"
UpdateApplicationManifest -Environment "Mooncake"
Write-Output "Updating version in (Mooncake) Service Manifest... with $BuildVersion"
UpdateServiceManifests -Environment "Mooncake"

#Staging
Write-Output "Updating version in (Staging) Application Manifest...with $BuildVersion"
UpdateApplicationManifest -Environment "Staging"
Write-Output "Updating version in (Staging) Service Manifest... with $BuildVersion"
UpdateServiceManifests -Environment "Staging"