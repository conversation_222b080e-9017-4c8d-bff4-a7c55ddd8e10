Configuration ClusterProvisioningStartup
{
    param(
        [string] $BuildVersion,
        [string] $CertificateSubjectNameDnsZonesToACLToNetworkService,
        [string] $MonitoringConfigVersion,
        [string] $MonitoringGCSAccount,
        [string] $MonitoringGCSEnvironment,
        [string] $MonitoringGCSNamespace,
        [string] $MonitoringGCSRegion,
        [string] $MonitoringGCSAuthId,
        [string] $TenantName
    )

    Import-DscResource -ModuleName 'PSDesiredStateConfiguration' -ModuleVersion 2.0.5

    # PSScriptRoot doesn't automatically propagate to Script blocks so capture it here
    $DscWorkingFolder = $PSScriptRoot
 
    Node localhost
    {
        LocalConfigurationManager {
            ConfigurationModeFrequencyMins = 15
            ConfigurationMode              = 'ApplyAndAutoCorrect'
            RebootNodeIfNeeded             = $true
            ActionAfterReboot              = 'ContinueConfiguration'
        }

        Script Startup {
            TestScript = {
                $lastBootupTime = (Get-CimInstance -ClassName Win32_OperatingSystem | Select-Object LastBootUpTime).LastBootUpTime
                $lastBootupTimeString = $lastBootupTime.ToString("yyyy''MM''dd-HH''mm''ss'Z'")
                $clusterInitScriptStateFilePath = "$using:DscWorkingFolder\cluster-init-succeed-$lastBootupTimeString.txt"

                Write-Output "'clusterInitScriptStateFilePath': $clusterInitScriptStateFilePath" | Out-File -FilePath $using:DscWorkingFolder\output.txt -Append -Force

                Test-Path $clusterInitScriptStateFilePath
            }
            GetScript  = {
            }
            SetScript  = {
                Import-Module "$using:DscWorkingFolder\RebootRequiredRegedit.psm1" -Force

                Write-Output "'BuildVersion': $using:BuildVersion" | Out-File -FilePath $using:DscWorkingFolder\output.txt -Append -Force
                Write-Output "'CertSubjectNameDNSZones': $using:CertificateSubjectNameDnsZonesToACLToNetworkService" | Out-File -FilePath $using:DscWorkingFolder\output.txt -Append -Force
                Write-Output "'MonitoringConfigVersion': $using:MonitoringConfigVersion" | Out-File -FilePath $using:DscWorkingFolder\output.txt -Append -Force
                Write-Output "'MonitoringGCSAccount': $using:MonitoringGCSAccount" | Out-File -FilePath $using:DscWorkingFolder\output.txt -Append -Force
                Write-Output "'MonitoringGCSEnvironment': $using:MonitoringGCSEnvironment" | Out-File -FilePath $using:DscWorkingFolder\output.txt -Append -Force
                Write-Output "'MonitoringGCSNamespace': $using:MonitoringGCSNamespace" | Out-File -FilePath $using:DscWorkingFolder\output.txt -Append -Force
                Write-Output "'MonitoringGCSRegion': $using:MonitoringGCSRegion" | Out-File -FilePath $using:DscWorkingFolder\output.txt -Append -Force
                Write-Output "'MonitoringGCSAuthId': $using:MonitoringGCSAuthId" | Out-File -FilePath $using:DscWorkingFolder\output.txt -Append -Force
                Write-Output "'TenantName': $using:TenantName" | Out-File -FilePath $using:DscWorkingFolder\output.txt -Append -Force

                $lastBootupTime = (Get-CimInstance -ClassName Win32_OperatingSystem | Select-Object LastBootUpTime).LastBootUpTime

                $rebootRequestTime = Get-RebootRequestTime

                Write-Output "Last bootup time: $lastBootupTime" | Out-File -FilePath $using:DscWorkingFolder\output.txt -Append -Force
                Write-Output "Reboot request time: $rebootRequestTime" | Out-File -FilePath $using:DscWorkingFolder\output.txt -Append -Force

                if ($lastBootupTime -gt $rebootRequestTime) {
                    Set-RebootRequired -Value $false
                }

                Write-Output "Starting Startup.ps1: $([System.DateTime]::UtcNow)" | Out-File -FilePath $using:DscWorkingFolder\output.txt -Append -Force

                $ManifestFileFullPath = "$DscWorkingFolder\Microsoft-WindowsAzure-Provider.man"
                $ManifestAssemblyFullPath = "$ManifestFileFullPath.dll"

                $scriptsList =
                @(
                    "UpdateRegistry.ps1 -TenantName '$using:TenantName'"
                    "ACLCertificates.ps1 -CertificateSubjectNameDnsZonesToACLToNetworkService '$using:CertificateSubjectNameDnsZonesToACLToNetworkService'"
                    "InstallEventSource.ps1 -ManifestFileFullPath '$ManifestFileFullPath' -ManifestAssemblyFullPath '$ManifestAssemblyFullPath'"
                    "StartMonitoringAgent.ps1 -BuildVersion '$using:BuildVersion' -DscWorkingFolder '$using:DscWorkingFolder' -MonitoringConfigVersion '$using:MonitoringConfigVersion' -MonitoringGCSAccount '$using:MonitoringGCSAccount' -MonitoringGCSEnvironment '$using:MonitoringGCSEnvironment' -MonitoringGCSNamespace '$using:MonitoringGCSNamespace' -MonitoringGCSRegion '$using:MonitoringGCSRegion' -MonitoringGCSAuthId '$using:MonitoringGCSAuthId' -TenantName '$using:TenantName'"
                )

                $failures = 0

                $UserPrincipal = New-Object System.Security.Principal.WindowsPrincipal([System.Security.Principal.WindowsIdentity]::GetCurrent())
                $AdminTEST = $UserPrincipal.IsInRole([System.Security.Principal.WindowsBuiltInRole]::Administrator)
                Write-Output "Running as: $($UserPrincipal.Identities.Name)" | Out-File -FilePath $using:DscWorkingFolder\output.txt -Append -Force
                Write-Output "IsElevated = $AdminTEST" | Out-File -FilePath $using:DscWorkingFolder\output.txt -Append -Force

                foreach ($script in $scriptsList) {
                    Invoke-Expression "$using:DscWorkingFolder\$script" | Out-File -FilePath $using:DscWorkingFolder\output.txt -Append -Force
                    if (-not $?) {
                        Write-Output "Script $script failed." | Out-File -FilePath $using:DscWorkingFolder\output.txt -Append -Force
                        $failures = $failures + 1
                    }
                }

                If ($failures -ne 0) {
                    $withFailures = "with failures "
                }
                Else {
                    $withFailures = ""

                    # We setup our cluster by creating sf cluster first and creating vmss after that.
                    # When we upgrade cluster, we will upgrade sf cluster following the UD definition and only move forward if sf node is ready.
                    # However when we change vmss change that requires reboot, it doesn't ensure sf node coming back and move to next.
                    # So this puts the cluster at risk whenever there is the cluster setup reboot.
                    # We want to disable reboot during vmss setup in cluster upgrade and only reboot if that is first time setup or vmss patching which is handled by SF in safe manner.
                    # This Lkg cluster setup time will be used to differentiate whether this is brand new machine or cluster upgrade scenario.
                    Set-LkgClusterSetupTime
                    Write-Output "Set lkg cluster setup time." | Out-File -FilePath $using:DscWorkingFolder\output.txt -Append -Force

                    if (Get-RebootRequired -eq $true) {
                        $Global:DSCMachineStatus = 1
                    }
                    else {
                        # Create new state file
                        $lastBootupTime = (Get-CimInstance -ClassName Win32_OperatingSystem | Select-Object LastBootUpTime).LastBootUpTime
                        $lastBootupTimeString = $lastBootupTime.ToString("yyyy''MM''dd-HH''mm''ss'Z'")
                        $clusterInitScriptStateFilePath = "$DscWorkingFolder\cluster-init-succeed-$lastBootupTimeString.txt"
                        "Cluster init succeeded!" > $using:clusterInitScriptStateFilePath
                    }
                }

                Write-Output "Finished $($withFailures)Startup.ps1: $([System.DateTime]::UtcNow)" | Out-File -FilePath $using:DscWorkingFolder\output.txt -Append -Force

                exit $failures
            }
        }
    }
}