$RebootRequiredRegistryKey = @{
  Path = "HKLM:\SOFTWARE\Microsoft\ClusterInitDsc"
  Property = "RebootRequired"
  RebootRequestTime = "RebootRequestTime"
  LkgClusterSetupTimePropertyName = "LkgClusterSetupTime"
}

<#
.SY<PERSON><PERSON><PERSON><PERSON>

Creates a registry key indicating if a reboot is required.

.<PERSON>SC<PERSON><PERSON><PERSON><PERSON>

Creates a registry key indicating if a reboot is required.

.PARAMETER Value

Value $true or $false

.EXAMPLE
Set-RebootRequired -Value $true
#>
function Set-RebootRequired {
  [CmdletBinding()]
  param
  (
    [parameter(Mandatory = $true)]
    [boolean] $Value
  )

  if (($Value -eq $true) -and !(Test-FirstTimeClusterSetup))
  {
    Write-Output "Skip setting reboot required as this is not first time cluster setup"
    return;
  }

  If(!(Test-Path $RebootRequiredRegistryKey.Path))
  {
    New-Item -Path $RebootRequiredRegistryKey.Path -Force
  }

  #Write-Output "Setting reboot required registry key: Value:$Value" | Out-File -FilePath $PSScriptRoot\output.txt -Append -Force
  New-ItemProperty -Path $RebootRequiredRegistryKey.Path -Name $RebootRequiredRegistryKey.Property -Value $Value -PropertyType DWORD -Force

  if ($Value -eq $true) {
    $rebootRequestTime = Get-Date
    New-ItemProperty -Path $RebootRequiredRegistryKey.Path -Name $RebootRequiredRegistryKey.RebootRequestTime -Value $rebootRequestTime -Force
  }
}

function Get-RebootRequired {

  $result = $false

  If(Test-Path $RebootRequiredRegistryKey.Path)
  {
    $rebootRegistryKeyProperty = Get-ItemProperty -Path $RebootRequiredRegistryKey.Path -Name $RebootRequiredRegistryKey.Property -ErrorAction Silent

    if ($rebootRegistryKeyProperty -ne $null -and $rebootRegistryKeyProperty.RebootRequired -eq 1)
    {
      $result = $true
    }
  }

  #Write-Output "Get reboot required registry key. Value:$result" | Out-File -FilePath $PSScriptRoot\output.txt -Append -Force
  return $result
}

function Get-RebootRequestTime {

  $result = $null

  If(Test-Path $RebootRequiredRegistryKey.Path)
  {
    $rebootRequestTimeProperty = Get-ItemProperty -Path $RebootRequiredRegistryKey.Path -Name $RebootRequiredRegistryKey.RebootRequestTime -ErrorAction Silent

    $result = $rebootRequestTimeProperty.RebootRequestTime
  }

  return $result
}

# Check whether current execution is first time cluster setup based on LkgClusterSetupTime registry entry set through Set-LkgClusterSetupTime
function Test-FirstTimeClusterSetup {
  $lkgClusterSetupTimeProperty = Get-ItemProperty -Path $RebootRequiredRegistryKey.Path -Name $RebootRequiredRegistryKey.LkgClusterSetupTimePropertyName -ErrorAction Silent
  return $lkgClusterSetupTimeProperty -eq $null
}

# Set current time as Last Known Good cluster setup time
function Set-LkgClusterSetupTime {
  If(!(Test-Path $RebootRequiredRegistryKey.Path))
  {
    New-Item -Path $RebootRequiredRegistryKey.Path -Force
  }

  $currentTime = Get-Date
  New-ItemProperty -Path $RebootRequiredRegistryKey.Path -Name $RebootRequiredRegistryKey.LkgClusterSetupTimePropertyName -Value $currentTime -Force
}