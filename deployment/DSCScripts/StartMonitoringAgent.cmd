set MONITORING_TENANT=##BUILD_VERSION##
set MONITORING_ROLE=Providers.SFMC.##TENANT_NAME##
set MONITORING_ROLE_INSTANCE=##ROLE_INSTANCE##
set MONITORING_DATA_DIRECTORY=##DATA_DIRECTORY##
set MONITORING_GCS_ACCOUNT=##GCS_ACCOUNT##
set MONITORING_GCS_NAMESPACE=##GCS_NAMESPACE##
set MONITORING_GCS_ENVIRONMENT=##GCS_ENVIRONMENT##
set MONITORING_GCS_REGION=##GCS_REGION##
set MONITORING_CONFIG_VERSION=##CONFIG_VERSION##
set MONITORING_GCS_CERTSTORE=LOCAL_MACHINE\MY
set MONITORING_GCS_AUTH_ID_TYPE=AuthKeyVault
set MONITORING_GCS_AUTH_ID=##GCS_AUTH_ID##
set AZSECPACK_CODEINTEGRITY_ENFORCE=enforce

cd ##AGENT_CLIENT_LOCATION##

MonAgentClient.exe -useenv