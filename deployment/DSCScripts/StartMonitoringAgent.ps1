﻿<#
.SYNOPSIS
    Starts Monitoring Agent.
    Geneva Docs recommend monitoring agent client is accesible from MonAgentClientLocation environment variable (https://eng.ms/docs/products/geneva/collect/references/windowsagentrelease)
    However, this variable is not available everytime, so we are shipping MonAgent client with DSC.    
#>

param(
    [parameter(Mandatory = $true)]
    [string] $BuildVersion,
    [parameter(Mandatory = $true)]
    [string] $DscWorkingFolder,
    [parameter(Mandatory = $true)]
    [string] $MonitoringConfigVersion,
    [parameter(Mandatory = $true)]
    [string] $MonitoringGCSAccount,
    [parameter(Mandatory = $true)]
    [string] $MonitoringGCSEnvironment,
    [parameter(Mandatory = $true)]
    [string] $MonitoringGCSNamespace,
    [parameter(Mandatory = $true)]
    [string] $MonitoringGCSRegion,
    [parameter(Mandatory = $true)]
    [string] $MonitoringGCSAuthId,
    [parameter(Mandatory = $true)]
    [string] $TenantName
)

$ErrorActionPreference = "Stop"

function Start-MonitoringAgent () {
    $computerName = $env:COMPUTERNAME
    $monAgentClientLocation = $env:MonAgentClientLocation
    $gcsRegion = $MonitoringGCSRegion
    $genevaDataDirectoryPath = "C:\GenevaAgent"
    $genevaAgentClientDirectoryPath = "C:\GenevaAgentClient\GenevaMonitoringAgent.45.6.1"
    $localGenevaAgentClientArchive = "$DscWorkingFolder\GenevaMonitoringAgent.45.6.1.zip"
    $monAgentCmdFullPath = "$DscWorkingFolder\StartMonitoringAgent.cmd"

    Write-Output "Starting StartMonitoringAgent.ps1: $([System.DateTime]::UtcNow)"

    # Updates the Geneva Monitoring Agent Configuration.
    ((Get-Content $monAgentCmdFullPath -Raw) -replace "##BUILD_VERSION##", $BuildVersion) | Set-Content -Path $monAgentCmdFullPath
    ((Get-Content $monAgentCmdFullPath -Raw) -replace "##TENANT_NAME##", $TenantName) | Set-Content -Path $monAgentCmdFullPath
    ((Get-Content $monAgentCmdFullPath -Raw) -replace "##ROLE_INSTANCE##", $computerName) | Set-Content -Path $monAgentCmdFullPath
    ((Get-Content $monAgentCmdFullPath -Raw) -replace "##GCS_ACCOUNT##", $MonitoringGCSAccount) | Set-Content -Path $monAgentCmdFullPath
    ((Get-Content $monAgentCmdFullPath -Raw) -replace "##GCS_NAMESPACE##", $MonitoringGCSNamespace) | Set-Content -Path $monAgentCmdFullPath
    ((Get-Content $monAgentCmdFullPath -Raw) -replace "##GCS_ENVIRONMENT##", $MonitoringGCSEnvironment) | Set-Content -Path $monAgentCmdFullPath
    ((Get-Content $monAgentCmdFullPath -Raw) -replace "##CONFIG_VERSION##", $MonitoringConfigVersion) | Set-Content -Path $monAgentCmdFullPath
    ((Get-Content $monAgentCmdFullPath -Raw) -replace "##GCS_AUTH_ID##", $MonitoringGCSAuthId) | Set-Content -Path $monAgentCmdFullPath

    if ($gcsRegion -eq "Brazil US") {
        # Change BrazilUS to a GSM supported region.
        $gcsRegion = "South Central US"

        Write-Output "Changed original MONITORING_GCS_REGION from '$($MonitoringGCSRegion)' to '$($gcsRegion)'."
    }
    elseif ($gcsRegion -like "*EUAP") {
        # GSM Region of EUAP regions ex: East US2 EUAP to EAST US2.
        $gcsRegion = $gcsRegion.Replace("EUAP", "")
        $gcsRegion = $gcsRegion.TrimEnd()

        Write-Output "Changed original MONITORING_GCS_REGION from '$($MonitoringGCSRegion)' to '$($gcsRegion)'."
    }

    ((Get-Content $monAgentCmdFullPath -Raw) -replace "##GCS_REGION##", $gcsRegion) | Set-Content -Path $monAgentCmdFullPath

    # Updates Geneva Monitoring Agent Data Directory.
    if (!(Test-Path $genevaDataDirectoryPath)) {
        Write-Output "Creating directory: '$genevaDataDirectoryPath'."
        New-Item -ItemType Directory -Force -Path $genevaDataDirectoryPath
    }
    else {
        Write-Output "Directory: '$genevaDataDirectoryPath' already exist."
    }

    ((Get-Content $monAgentCmdFullPath -Raw) -replace "##DATA_DIRECTORY##", $genevaDataDirectoryPath) | Set-Content -Path $monAgentCmdFullPath

    # Updates Geneva Monitoring Agent Client Location.
    Write-Output "Monitoring Agent Client Location: '$($monAgentClientLocation)'."

    # Attempts to get monAgentClientLocation from environment variable and falls backs to shipped Agent along with DSC.
    if (($null -eq $monAgentClientLocation) -or !(Test-Path $monAgentClientLocation)) {
        if (!(Test-Path $genevaAgentClientDirectoryPath)) {
            Write-Output "Creating directory: '$genevaAgentClientDirectoryPath'."
            New-Item -ItemType Directory -Force -Path $genevaAgentClientDirectoryPath

            Expand-Archive -Path $localGenevaAgentClientArchive -DestinationPath $genevaAgentClientDirectoryPath
        }
        else {
            Write-Output "Directory: '$genevaAgentClientDirectoryPath' already exist."
        }

        $monAgentClientLocation = $genevaAgentClientDirectoryPath
    }
    
    $monAgentClientExePath = "$monAgentClientLocation\MonAgentClient.exe."
    Write-Output "Using MonAgentClientLocation '$($monAgentClientLocation)', MonAgentClient.exe Exist: '$(Test-Path $monAgentClientExePath)'."

    ((Get-Content $monAgentCmdFullPath -Raw) -replace "##AGENT_CLIENT_LOCATION##", $monAgentClientLocation) | Set-Content -Path $monAgentCmdFullPath

    Invoke-Item $monAgentCmdFullPath

    Write-Output "Monitoring Agent started successfully."
}

try {
    Start-MonitoringAgent >> $PSScriptRoot\StartMonitoringAgent.output.txt
}
catch {
    Write-Output $($_ | Out-String) >> $PSScriptRoot\StartMonitoringAgent.output.txt
    throw $_
}