<#
.SYNOPSIS
    Update Registry.

    1. Increases the max header value size.
    2. Increases the the upper limit for the total size of the Request line and the headers.
    3. Sets maximum number of characters in a URL path segment.
    4. Updates the max user port configuration to 65534 (0xfffe = 65534).
    5. Updates the TCP timed wait delay to 60 seconds (0x3c = 60).
    6. Updates the FIPS Enablement guidance (only for AME).

.PARAMETER  TenantName
    Tenant Name (Microsoft | AME | USME | CME).
#>

param(
    [parameter(Mandatory = $true)]
    [string] $TenantName
)

$ErrorActionPreference = "Stop"
$global:restartComputer = $false

$RegistryKeysToSet = @(
    @{
        "Path"  = "HKLM:\System\CurrentControlSet\Services\HTTP\Parameters";
        "Name"  = "MaxFieldLength";
        "Value" = (64 * 1024);
        "Type"  = "DWORD";
    },
    @{
        "Path"  = "HKLM:\System\CurrentControlSet\Services\HTTP\Parameters";
        "Name"  = "MaxRequestBytes";
        "Value" = (4 * 1024 * 1024);
        "Type"  = "DWORD";
    },
    @{
        "Path"  = "HKLM:\System\CurrentControlSet\Services\HTTP\Parameters";
        "Name"  = "UrlSegmentMaxLength";
        "Value" = (16 * 1024);
        "Type"  = "DWORD";
    },
    @{
        "Path"  = "HKLM:\System\CurrentControlSet\Services\Tcpip\Parameters";
        "Name"  = "MaxUserPort";
        "Value" = 65534;
        "Type"  = "DWORD";
    },
    @{
        "Path"  = "HKLM:\System\CurrentControlSet\Services\Tcpip\Parameters";
        "Name"  = "TcpTimedWaitDelay";
        "Value" = 60;
        "Type"  = "DWORD";
    }
)

$RegistryKeysToSetV2 = @(
    @{
        "Path"  = "HKLM:\System\CurrentControlSet\Control\Lsa\FIPSAlgorithmPolicy";
        "Name"  = "STE";
        "Value" = 1;
        "Type"  = "DWORD";
    }
)

function Set-RegistryKeys {
    $RegistryKeysToSet | ForEach-Object { Set-RegistryKeyValue -path $_.Path -name $_.Name -type $_.Type -value $_.Value }

    $RegistryKeysToSetV2 | ForEach-Object { Set-RegistryKeyValue -path $_.Path -name $_.Name -type $_.Type -value $_.Value }

    if ($global:restartComputer) {
        Set-RebootRequired -Value $true
    }
}

function Set-RegistryKeyValue ([string] $path, [string] $name, [string] $type, $value) {
    if (-not (Test-Path $path)) {
        Write-Output "Registry key '$($path)' does not exist.. Creating"
        New-Item -Path $path -Force | Out-Null
    }
    else {
        Write-Output 'Registry key '$($path)' already exist.. Skipping'
    }

    $key = Get-Item -path $path
    $currentValue = $key.GetValue($name)
    if (($null -eq $currentValue) -or ($currentValue -ne $value)) {
        if ($null -eq $currentValue) {
            Write-Output "Creating '$($path)\$($name)' registry property to '$($value)'"
            New-ItemProperty -Path $path -Name $name -Value $value  -PropertyType $type
        }
        else {
            Write-Output "Setting '$($path)\$($name)' registry property to '$($value)'"
            Set-ItemProperty -Path $path -Name $name -Value $value
        }

        # We are making changes to registry so a reboot is needed.
        $global:restartComputer = $true
    }
}

try {
    Set-RegistryKeys >> $PSScriptRoot\UpdateRegistry.output.txt
}
catch {
    Write-Output $($_ | Out-String) >> $PSScriptRoot\UpdateRegistry.output.txt
    throw $_
}