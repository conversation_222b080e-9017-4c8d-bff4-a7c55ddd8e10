Param(
    [Parameter(Mandatory = $true, HelpMessage = "The Build version to be used in the Configs")] 
    [string]$BuildVersion,
    [Parameter(Mandatory = $true, HelpMessage = "Certificate Subject Name Dns Zones To ACL To Network Service")] 
    [string]$CertSubjectNameDNSZonesToACL,
    [Parameter(Mandatory = $true, HelpMessage = "Monitoring GCS Account")]
    [string]$MonitoringGCSAccount,
    [Parameter(Mandatory = $true, HelpMessage = "Monitoring GCS Namespace")]
    [string]$MonitoringGCSNamespace,
    [Parameter(Mandatory = $true, HelpMessage = "Monitoring GCS Environment")] 
    [string]$MonitoringGCSEnvironment,
    [Parameter(Mandatory = $true, HelpMessage = "Monitoring GCS Region")] 
    [string]$MonitoringGCSRegion,
    [Parameter(Mandatory = $true, HelpMessage = "Monitoring Config Version")] 
    [string]$MonitoringConfigVersion,
    [Parameter(Mandatory = $true, HelpMessage = "Geneva Certificate Subject Name")] 
    [string]$GenevaCertSubjectName,
    [Parameter(Mandatory = $true, HelpMessage = "Tenant Name")] 
    [string]$TenantName
)

function ExecutePS7 {
    Param([string]$BuildVersion, 
        [string]$CertSubjectNameDNSZonesToACL, 
        [string]$MonitoringGCSAccount, 
        [string]$MonitoringGCSNamespace,
        [string]$MonitoringGCSEnvironment,
        [string]$MonitoringGCSRegion,
        [string]$MonitoringConfigVersion,
        [string]$GenevaCertSubjectName,
        [string]$TenantName)

    Write-Host "Setting PSGallery installations as Trusted"
    Set-PSRepository PSGallery -InstallationPolicy Trusted 
    
    Write-Host "Installing PSDesiredStateConfiguration"
    Install-Module -Name PSDesiredStateConfiguration -RequiredVersion 2.0.5 -Confirm:$False -Force


    if(Test-Path ..\\..\\Startup){
        Write-Host "Startup folder already exists, delete for new one"
        Remove-Item -Path ..\\..\\Startup -Force -Recurse
    }

    Write-Host "Creating Startup folder"
    mkdir ..\\..\\Startup 
    

    $StartupPackage = "Startup-$BuildVersion.zip"
    Write-Host "Copying Startup package $StartupPackage to Startup folder"
    Copy-Item -Path $StartUpPackage -Destination ..\\..\\Startup
    
    Write-Host "Moving to Startup folder"
    cd ..\\..\\Startup 

    Write-Host "Extracting Startup package"
    Expand-Archive -Path $StartUpPackage -DestinationPath . -Force

    Write-Host "Dot-sourcing Startup script"
    . .\\Startup.ps1

    Write-Host "Compiling DSC Configuration"
    ClusterProvisioningStartup -BuildVersion $BuildVersion `
        -CertificateSubjectNameDnsZonesToACLToNetworkService $CertSubjectNameDNSZonesToACL `
        -MonitoringGCSAccount $MonitoringGCSAccount `
        -MonitoringGCSNamespace $MonitoringGCSNamespace `
        -MonitoringGCSEnvironment $MonitoringGCSEnvironment `
        -MonitoringGCSRegion $MonitoringGCSRegion `
        -MonitoringConfigVersion $MonitoringConfigVersion `
        -MonitoringGCSAuthId $GenevaCertSubjectName `
        -TenantName $TenantName
    
    Write-Host "Starting DSC Configuration"    
    Start-DscConfiguration -Path .\\ClusterProvisioningStartup -Verbose -Wait -Force

    Write-Host "Updating DSC Configuration to match resource files"
    Set-DscLocalConfigurationManager -Path .\\ClusterProvisioningStartup -Verbose -Force
    
    Write-Host "PS7 Complete!" -ForegroundColor Green
}

try{
if((Get-WmiObject -Class Win32_Product -filter "Name LIKE 'PowerShell%'" | Measure-Object).Count -eq 0){
    Write-Host "Installing standalone Powershell version"
    iex "& { $(irm https://aka.ms/install-powershell.ps1) } -UseMSI -Quiet"
} else {
    Write-Host "Standalone powershell version already installed"
}

Write-Host "Installing NuGet"
Install-PackageProvider -Name NuGet -MinimumVersion ********* -Force 

# $argObj = '1.0.02771.2371', '\"databricksrpstaging.cloudapp.net,*.adb-spearfish-rp-staging.net,prod.geneva.keyvault.adb-spearfish-rp-staging.net\"', 'AzureDatabricksRPStgWarm', 'AzureDatabricksRPStgWarm', 'Test', '\"West US\"', '\"21.4\"', 'prod.geneva.keyvault.adb-spearfish-rp-staging.net', 'Microsoft'
$argObj = $BuildVersion, $CertSubjectNameDNSZonesToACL, $MonitoringGCSAccount, $MonitoringGCSNamespace, $MonitoringGCSEnvironment, $MonitoringGCSRegion, $MonitoringConfigVersion, $GenevaCertSubjectName, $TenantName
Start-Process pwsh -args '-ExecutionPolicy', 'Unrestricted', '-noexit' , '-EncodedCommand',
  ([Convert]::ToBase64String(
     [Text.Encoding]::Unicode.GetBytes(
       (Get-Command -Type Function ExecutePS7).Definition 
     )
  )), 
  '-EncodedArguments',
  ([Convert]::ToBase64String([Text.Encoding]::Unicode.GetBytes(
      [System.Management.Automation.PSSerializer]::Serialize($argObj)
    ))) 

Write-Host "CSE Script Complete!"
} catch {
Write-Host "Something went wrong!"
}
