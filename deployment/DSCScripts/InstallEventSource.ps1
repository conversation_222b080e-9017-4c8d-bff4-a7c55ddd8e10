﻿<#
.SYNOPSIS
    Installs event source to windows event store.

.PARAMETER  ManifestFileFullPath
    Manifest file (.man) full path.
.PARAMETER  ManifestAssemblyFullPath
    Manifest assembly (.man.dll) full path.
#>

param(
    [parameter(Mandatory = $true)]
    [string] $ManifestFileFullPath,
    [parameter(Mandatory = $true)]
    [string] $ManifestAssemblyFullPath
)

$ErrorActionPreference = "Stop"

function Install-EventSource ($ManifestFileFullPath, $ManifestAssemblyFullPath) {
    Write-Output "Starting InstallEventSource.ps1: $([System.DateTime]::UtcNow)"

    if (($ManifestFileFullPath -eq $null) -or ($ManifestAssemblyFullPath -eq $null)) {
        Write-Output "ManifestFileFullPath or ManifestAssemblyFullPath is empty."
        exit 1
    }

    Write-Output "Reading manifest file $ManifestFileFullPath"
    [xml]$man = Get-Content $ManifestFileFullPath

    Write-Output "Updating manifest file path to $ManifestAssemblyFullPath"
    $man.instrumentationManifest.instrumentation.events.provider.resourceFileName = $ManifestAssemblyFullPath
    $man.instrumentationManifest.instrumentation.events.provider.messageFileName = $ManifestAssemblyFullPath

    Write-Output "Saving manifest file."
    $man.Save($ManifestFileFullPath)

    Write-Output "Uninstalling any existing manifest"
    & wevtutil uninstall-manifest $ManifestFileFullPath

    Write-Output "Installing manifest"
    & wevtutil install-manifest $ManifestFileFullPath

    Write-Output "Install manifest completed."
}

try {
    Install-EventSource $ManifestFileFullPath $ManifestAssemblyFullPath >> $PSScriptRoot\InstallEventSource.output.txt
}
catch {
    Write-Output $($_ | Out-String) >> $PSScriptRoot\InstallEventSource.output.txt
    throw $_
}