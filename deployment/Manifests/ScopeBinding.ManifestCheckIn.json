{"$schema": "https://ev2schema.azure.net/schemas/2020-01-01/scopeBindings.json", "contentVersion": "*******", "scopeBindings": [{"scopeTagName": "ManifestCheckIn", "bindings": [{"find": "__MANIFEST.LOCATION__", "replaceWith": "$location()"}, {"find": "__REGISTRATIONSUBSCRIPTION__", "replaceWith": "$azureSubscriptionId()"}, {"find": "__RESOURCEPROVIDER__", "replaceWith": "Microsoft.Databricks"}, {"find": "__CHECKINENVIRONMENT__", "replaceWith": "$config(checkInEnvironment)"}, {"find": "__CLOUDNAME__", "replaceWith": "$config(cloudName)"}, {"find": "__REGISTRATIONUSERASSIGNEDIDENTITY__", "replaceWith": "/subscriptions/$azureSubscriptionId()/resourceGroups/adbrp_global/providers/Microsoft.ManagedIdentity/userAssignedIdentities/databricks-manifest-rollout-msi"}]}]}