{"$schema": "http://schema.express.azure.com/schema/2015-01-01-alpha/RolloutParameters.json", "contentVersion": "*******", "ShellExtensions": [{"Name": "ManifestCheckIn", "Type": "ManifestCheckIn", "Properties": {"MaxExecutionTime": "PT30M"}, "Package": {"Reference": {"Path": "Scripts\\Deployment.zip"}}, "Launch": {"Command": ["/bin/bash", "-c", "pwsh ./ManifestCheckIn.ps1"], "EnvironmentVariables": [{"name": "RegistrationSubscription", "value": "__REGISTRATIONSUBSCRIPTION__"}, {"name": "CheckInEnvironment", "value": "__CHECKINENVIRONMENT__"}, {"name": "CloudName", "value": "__CLOUDNAME__"}, {"name": "ResourceProvider", "value": "__RESOURCEPROVIDER__"}, {"name": "PRODManifestJSON", "reference": {"path": "Prod\\Manifest.json"}}, {"name": "CANARYManifestJSON", "reference": {"path": "Canary\\Manifest.json"}}, {"name": "FAIRFAXManifestJSON", "reference": {"path": "Fairfax\\Manifest.json"}}, {"name": "MOONCAKEManifestJSON", "reference": {"path": "Mooncake\\Manifest.json"}}], "identity": {"type": "UserAssigned", "userAssignedIdentities": ["__REGISTRATIONUSERASSIGNEDIDENTITY__"]}}}]}