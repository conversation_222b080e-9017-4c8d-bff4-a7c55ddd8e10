#################################################################################
#                               OneBranch Pipelines                             #
# Documentation:  https://aka.ms/obpipelines                                    #
# Yaml Schema:    https://aka.ms/obpipelines/yaml/schema                        #
# Support:        https://aka.ms/onebranchsup                                   #
#################################################################################

trigger: none

parameters:
- name: 'rolloutType'
  displayName: 'SDP rollout type'
  type: string
  default: 'normal'
  values:
    - normal
    - emergency
    - globaloutage

- name: CheckinPublic
  displayName: 'Checkin to Public'
  type: boolean
  default: true
- name: CheckinMC
  displayName: 'Checkin to Moon<PERSON>'
  type: boolean
  default: false
- name: CheckinFF
  displayName: 'Checkin to Fairfax'
  type: boolean
  default: false

- name: 'overrideManagedValidationDuration'
  displayName: 'Override standard SDP duration?'
  type: boolean
  default: false 

- name: 'managedValidationDurationInHours'
  displayName: 'Override standard SDP duration (in hours)'
  type: number
  default: 0

- name: 'icmIncidentId'
  displayName: 'IcM Incident Id'
  type: number
  default: 0

resources:
  repositories: 
    - repository: templates
      type: git
      name: OneBranch.Pipelines/GovernedTemplates
      ref: refs/heads/main

  pipelines:
    - pipeline: build-artifacts
      source: DatabricksRP-Official

extends:
  template: v2/OneBranch.Official.CrossPlat.yml@templates # https://aka.ms/obpipelines/templates
  parameters:

    ev2ManagedSdpRolloutConfig:
      rolloutType: ${{parameters.rolloutType}}
      overrideManagedValidationDuration: ${{parameters.overrideManagedValidationDuration}} 
      managedValidationOverrideDurationInHours: ${{parameters.managedValidationDurationInHours}} 
      icmIncidentId: ${{parameters.icmIncidentId}}    

    stages:

    # PROD (Public)
    - ${{ if parameters.CheckinPublic }}:
      - stage: 'PROD_Managed_SDP'      
        displayName: 'PROD: Manifest CheckIn'
        variables:
          ob_release_environment: Production
        jobs:
        - job: PROD_Managed_SDP
          displayName: 'PROD_Managed_SDP'
          pool:          
            type: release  # read more about custom job types at https://aka.ms/obpipelines/yaml/jobs        

          steps:        
            - download: build-artifacts
            - task: vsrm-ev2.ev2-rollout.ev2-rollout-task.Ev2RARollout@2
              displayName: 'Ev2 Managed SDP Manifest CheckIn Rollout'
              inputs:
                EndpointProviderType: ApprovalService
                ApprovalServiceEnvironment: Production
                TaskAction: RegisterAndRollout
                SkipRegistrationIfExists: true
                ServiceRootPath: $(Pipeline.Workspace)/build-artifacts/drop_build_main/Manifests
                RolloutSpecPath: $(Pipeline.Workspace)/build-artifacts/drop_build_main/Manifests/RolloutSpec.ManifestCheckIn.json
                StageMapName: 'Microsoft.Azure.SDP.Standard'
                Select: regions(eastus)
                ArtifactsVersionOverride: PROD_$(Build.BuildNumber)
                ConfigurationOverrides: "{ 'ConfigurationSpecification': { 'settings': { 'manifestDeploymentRegion': 'eastus', 'checkInEnvironment': 'PROD', 'cloudName': 'AzureCloud', 'tenantId': '33e01921-4d64-4f8c-a055-5bdaffd5e33d', 'environment': 'Prod' } } }"
    
    # MOONCAKE (Mooncake)
    - ${{ if parameters.CheckinMC }}:
      - stage: 'MC_Managed_SDP'      
        displayName: 'Mooncake: Manifest CheckIn'
        variables:
          ob_release_environment: Mooncake
        jobs:
        - job: MC_Managed_SDP
          displayName: 'Mooncake_Managed_SDP'
          pool:          
            type: release  # read more about custom job types at https://aka.ms/obpipelines/yaml/jobs        

          steps:        
            - download: build-artifacts
            - task: vsrm-ev2.ev2-rollout.ev2-rollout-task.Ev2RARollout@2
              displayName: 'Ev2 Managed SDP Manifest CheckIn Rollout'
              inputs:
                EndpointProviderType: ApprovalService
                ApprovalServiceEnvironment: Mooncake
                TaskAction: RegisterAndRollout
                SkipRegistrationIfExists: true
                ServiceRootPath: $(Pipeline.Workspace)/build-artifacts/drop_build_main/Manifests
                RolloutSpecPath: $(Pipeline.Workspace)/build-artifacts/drop_build_main/Manifests/RolloutSpec.ManifestCheckIn.json
                StageMapName: 'Microsoft.Azure.SDP.Standard'
                Select: regions(chinaeast2)
                ArtifactsVersionOverride: MOONCAKE_$(Build.BuildNumber)
                ConfigurationOverrides: "{ 'ConfigurationSpecification': { 'settings': { 'manifestDeploymentRegion': 'chinaeast2', 'checkInEnvironment': 'MOONCAKE', 'cloudName': 'AzureChinaCloud', 'tenantId': 'a55a4d5b-9241-49b1-b4ff-befa8db00269', 'environment': 'Mooncake' } } }"

    # FAIRFAX (Fairfax)
    - ${{ if parameters.CheckinFF }}:
      - stage: 'FF_Managed_SDP'      
        displayName: 'Fairfax: Manifest CheckIn'
        variables:
          ob_release_environment: Fairfax
        jobs:
        - job: FF_Managed_SDP
          displayName: 'Fairfax_Managed_SDP'
          pool:          
            type: release  # read more about custom job types at https://aka.ms/obpipelines/yaml/jobs        

          steps:        
            - download: build-artifacts
            - task: vsrm-ev2.ev2-rollout.ev2-rollout-task.Ev2RARollout@2
              displayName: 'Ev2 Managed SDP Manifest CheckIn Rollout'
              inputs:
                EndpointProviderType: ApprovalService
                ApprovalServiceEnvironment: Fairfax
                TaskAction: RegisterAndRollout
                SkipRegistrationIfExists: true
                ServiceRootPath: $(Pipeline.Workspace)/build-artifacts/drop_build_main/Manifests
                RolloutSpecPath: $(Pipeline.Workspace)/build-artifacts/drop_build_main/Manifests/RolloutSpec.ManifestCheckIn.json
                StageMapName: 'Microsoft.Azure.SDP.Standard'
                Select: regions(usgovarizona)
                ArtifactsVersionOverride: FAIRFAX_$(Build.BuildNumber)
                ConfigurationOverrides: "{ 'ConfigurationSpecification': { 'settings': { 'manifestDeploymentRegion': 'usgovarizona', 'checkInEnvironment': 'FAIRFAX', 'cloudName': 'AzureUSGovernment', 'tenantId': 'cab8a31a-1906-4287-a0d8-4eef66b95f6e', 'environment': 'Fairfax' } } }"

  
    
      