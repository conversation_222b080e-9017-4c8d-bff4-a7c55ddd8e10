#################################################################################
#                               OneBranch Pipelines                             #
# Documentation:  https://aka.ms/obpipelines                                    #
# Yaml Schema:    https://aka.ms/obpipelines/yaml/schema                        #
# Support:        https://aka.ms/onebranchsup                                   #
#################################################################################

trigger: none

parameters:
- name: 'rolloutType'
  displayName: 'SDP rollout type'
  type: string
  default: 'normal'
  values:
    - normal
    - emergency
    - globaloutage

- name: 'overrideManagedValidationDuration'
  displayName: 'Override standard SDP duration?'
  type: boolean
  default: false 

- name: 'managedValidationDurationInHours'
  displayName: 'Override standard SDP duration (in hours)'
  type: number
  default: 0

- name: 'icmIncidentId'
  displayName: 'IcM Incident Id'
  type: number
  default: 0

resources:
  repositories: 
    - repository: templates
      type: git
      name: OneBranch.Pipelines/GovernedTemplates
      ref: refs/heads/main

  pipelines:
    - pipeline: build-artifacts
      source: DatabricksRP-Official

extends:
  template: v2/OneBranch.Official.CrossPlat.yml@templates # https://aka.ms/obpipelines/templates
  parameters:

    ev2ManagedSdpRolloutConfig:
      rolloutType: ${{parameters.rolloutType}}
      overrideManagedValidationDuration: ${{parameters.overrideManagedValidationDuration}} 
      managedValidationOverrideDurationInHours: ${{parameters.managedValidationDurationInHours}} 
      icmIncidentId: ${{parameters.icmIncidentId}}    

    stages:

    # PROD (Canary)
    - stage: 'PROD_Managed_SDP'      
      displayName: 'PROD: CANARY Manifest CheckIn'
      variables:
        ob_release_environment: Production
      jobs:
      - job: PROD_Managed_SDP
        displayName: 'PROD_CANARY_Managed_SDP'
        pool:          
          type: release  # read more about custom job types at https://aka.ms/obpipelines/yaml/jobs        

        steps:        
          - download: build-artifacts
          - task: vsrm-ev2.ev2-rollout.ev2-rollout-task.Ev2RARollout@2
            displayName: 'Ev2 Managed SDP Manifest CheckIn Rollout'
            inputs:
              EndpointProviderType: ApprovalService
              ApprovalServiceEnvironment: Production
              TaskAction: RegisterAndRollout
              SkipRegistrationIfExists: true
              ServiceRootPath: $(Pipeline.Workspace)/build-artifacts/drop_build_main/Manifests
              RolloutSpecPath: $(Pipeline.Workspace)/build-artifacts/drop_build_main/Manifests/RolloutSpec.ManifestCheckIn.json
              StageMapName: 'Microsoft.Azure.SDP.Standard'
              Select: regions(eastus2euap)
              ArtifactsVersionOverride: CANARY_$(Build.BuildNumber)
              ConfigurationOverrides: "{ 'ConfigurationSpecification': { 'settings': { 'manifestDeploymentRegion': 'eastus2euap', 'checkInEnvironment': 'CANARY', 'cloudName': 'AzureCloud', 'tenantId': '33e01921-4d64-4f8c-a055-5bdaffd5e33d', 'environment': 'Prod', 'subscriptionId': '8e1611c2-b76d-436d-a464-fd25091684a5' } } }"