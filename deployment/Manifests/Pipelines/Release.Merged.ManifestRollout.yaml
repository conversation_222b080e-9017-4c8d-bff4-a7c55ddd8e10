#################################################################################
#                               OneBranch Pipelines                             #
# Documentation:  https://aka.ms/obpipelines                                    #
# Yaml Schema:    https://aka.ms/obpipelines/yaml/schema                        #
# Support:        https://aka.ms/onebranchsup                                   #
#################################################################################

trigger: none

parameters:
- name: 'rolloutType'
  displayName: 'SDP rollout type'
  type: string
  default: 'normal'
  values:
    - normal
    - emergency
    - globaloutage

- name: 'overrideManagedValidationDuration'
  displayName: 'Override standard SDP duration?'
  type: boolean
  default: false 

- name: 'managedValidationDurationInHours'
  displayName: 'Override standard SDP duration (in hours)'
  type: number
  default: 0

- name: 'icmIncidentId'
  displayName: 'IcM Incident Id'
  type: number
  default: 0

- name: debug
  displayName: Enable debug output
  type: boolean
  default: false

resources:
  repositories: 
    - repository: templates
      type: git
      name: OneBranch.Pipelines/GovernedTemplates
      ref: refs/heads/main

extends:
  template: v2/OneBranch.Official.CrossPlat.yml@templates # https://aka.ms/obpipelines/templates
  parameters:

    ev2ManagedSdpRolloutConfig:
      rolloutType: ${{parameters.rolloutType}}
      overrideManagedValidationDuration: ${{parameters.overrideManagedValidationDuration}} 
      managedValidationOverrideDurationInHours: ${{parameters.managedValidationDurationInHours}} 
      icmIncidentId: ${{parameters.icmIncidentId}}    

    stages:

    # PROD (Public)
    - stage: 'PROD_Managed_SDP'
      displayName: 'PROD: Manifest Rollout'
      variables:
        ob_release_environment: Production
      jobs:
      - job: PROD_Managed_SDP
        displayName: 'PROD Manifest Rollout'
        pool:          
          type: release  # read more about custom job types at https://aka.ms/obpipelines/yaml/jobs        

        steps:        
          - task: vsrm-ev2.ev2-rollout.ev2-rollout-task.Ev2RARollout@2
            displayName: 'Ev2 Managed SDP Manifest Rollout Rollout'
            inputs:
              UseServerMonitorTask: true
              EndpointProviderType: ApprovalService
              ApprovalServiceEnvironment: Production
              TaskAction: CentralArtifactsRollout
              CentralArtifactsServiceIdentifier: d1b7d8ba-05e2-48e6-90d6-d781b99c6e69
              CentralArtifactsPolicyName: RPPlatform-ManifestRollout-Policy
              CentralArtifactsTenantIdentifier: 72f988bf-86f1-41af-91ab-2d7cd011db47
              ServiceIdentifier: 90b3f8fb-6a44-4ae3-9705-01da5c4ab7d5
              ServiceGroup: Microsoft.Azure.RPPlatform.Centralized.ManifestRollout
              Select: 'regions(*)'
              Except: 'regions(eastus2euap)'
              ConfigurationOverrides: "{ 'ConfigurationSpecification': { 'settings': { 'providerNamespace': 'Microsoft.Databricks', 'checkInEnvironment': 'PROD', 'registrationTenantId': '33e01921-4d64-4f8c-a055-5bdaffd5e33d' } } }"

    # Mooncake
    - stage: 'MC_Managed_SDP'
      displayName: 'MC: Manifest Rollout'
      dependsOn: PROD_Managed_SDP
      variables:
        ob_release_environment: Mooncake
      jobs:
      - job: MC_Managed_SDP
        displayName: 'MC Manifest Rollout'
        pool:          
          type: release  # read more about custom job types at https://aka.ms/obpipelines/yaml/jobs        

        steps:        
          - task: vsrm-ev2.ev2-rollout.ev2-rollout-task.Ev2RARollout@2
            displayName: 'Ev2 Managed SDP Manifest Rollout Rollout'
            inputs:
              UseServerMonitorTask: true
              EndpointProviderType: ApprovalService
              ApprovalServiceEnvironment: Mooncake
              TaskAction: CentralArtifactsRollout
              CentralArtifactsServiceIdentifier: d1b7d8ba-05e2-48e6-90d6-d781b99c6e69
              CentralArtifactsPolicyName: RPPlatform-ManifestRollout-Policy
              CentralArtifactsTenantIdentifier: a55a4d5b-9241-49b1-b4ff-befa8db00269
              ServiceIdentifier: 90b3f8fb-6a44-4ae3-9705-01da5c4ab7d5
              ServiceGroup: Microsoft.Azure.RPPlatform.Centralized.ManifestRollout
              Select: 'regions(*)'
              ConfigurationOverrides: "{ 'ConfigurationSpecification': { 'settings': { 'providerNamespace': 'Microsoft.Databricks', 'checkInEnvironment': 'Mooncake', 'registrationTenantId': 'a55a4d5b-9241-49b1-b4ff-befa8db00269' } } }"

    # Fairfax
    - stage: 'FF_Managed_SDP'
      displayName: 'FF: Manifest Rollout'
      dependsOn: MC_Managed_SDP
      variables:
        ob_release_environment: Fairfax
      jobs:
      - job: FF_Managed_SDP
        displayName: 'FF Manifest Rollout'
        pool:          
          type: release  # read more about custom job types at https://aka.ms/obpipelines/yaml/jobs        

        steps:        
          - task: vsrm-ev2.ev2-rollout.ev2-rollout-task.Ev2RARollout@2
            displayName: 'Ev2 Managed SDP Manifest Rollout Rollout'
            inputs:
              UseServerMonitorTask: true
              EndpointProviderType: ApprovalService
              ApprovalServiceEnvironment: Fairfax
              TaskAction: CentralArtifactsRollout
              CentralArtifactsServiceIdentifier: d1b7d8ba-05e2-48e6-90d6-d781b99c6e69
              CentralArtifactsPolicyName: RPPlatform-ManifestRollout-Policy
              CentralArtifactsTenantIdentifier: cab8a31a-1906-4287-a0d8-4eef66b95f6e
              ServiceIdentifier: 90b3f8fb-6a44-4ae3-9705-01da5c4ab7d5
              ServiceGroup: Microsoft.Azure.RPPlatform.Centralized.ManifestRollout
              Select: 'regions(*)'
              ConfigurationOverrides: "{ 'ConfigurationSpecification': { 'settings': { 'providerNamespace': 'Microsoft.Databricks', 'checkInEnvironment': 'Fairfax', 'registrationTenantId': 'cab8a31a-1906-4287-a0d8-4eef66b95f6e' } } }"