{"$schema": "https://ev2schema.azure.net/schemas/2020-04-01/RegionAgnosticRolloutSpecification.json", "ContentVersion": "1.0.0.0", "RolloutMetadata": {"ServiceModelPath": "ServiceModel.ManifestCheckIn.json", "ScopeBindingsPath": "ScopeBinding.ManifestCheckIn.json", "Name": "Resource Provider Registration Rollout", "RolloutType": "Major", "BuildSource": {"Parameters": {"VersionFile": "BuildId.ManifestCheckIn.txt"}}}, "OrchestratedSteps": [{"Name": "ManifestCheckIn", "TargetType": "ServiceResourceDefinition", "TargetName": "ManifestCheckIn", "Actions": ["Shell/ManifestCheckIn"]}]}