param (
    [string[]]$target_environments = @("Canary", "Prod", "Mooncake", "Fairfax"),  # Set the target environments as a parameter with default values
    [string]$new_api_version = "2025-06-01-preview",  # Set the new API version as a parameter with a default value
    [string]$new_feature = "EnableServerlessWorkspaceFeature",  # Set the new required feature as a parameter with a default value
    [string]$target_api_version = "2025-02-01-preview",  # Set the target API version as a parameter with a default value
    [string]$target_feature = "ManagedvNetInjectionMigration",  # Set the target feature as a parameter with a default value
    [string]$mode = "Test",  # Set the mode as a parameter with a default value
    [string]$base_path = 'c:\Repo\DatabricksRP\deployment\Manifests'  # Set the base path as a parameter with a default value
)

# Determine the output suffix based on the mode
$output_suffix = if ($mode -eq "Prod") { "" } else { "Test" }
$new_required_feature = "Microsoft.Databricks/$new_feature"  # Construct the new required feature name
$target_required_feature = "Microsoft.Databricks/$target_feature"  # Construct the target required feature name


foreach ($target_environment in $target_environments) {
    # Load the JSON file
    $input_file_path = "$base_path\\$target_environment\\Manifest.json"
    $output_file_path = "$base_path\\$target_environment\\Manifest$output_suffix.json"

    if (-not (Test-Path -Path $input_file_path)) {
        Write-Error "Input file not found: $input_file_path"
        continue
    }

    # Read the JSON data from the input file
    $json_data = Get-Content -Raw -Path $input_file_path | ConvertFrom-Json

    # Iterate through all resource types and their endpoints
    foreach ($resource_type in $json_data.resourceTypes) {
        # Create a new list to store updated endpoints
        $new_endpoints = @()
        
        # Iterate through each endpoint in the resource type
        for ($i = 0; $i -lt $resource_type.endpoints.Count; $i++) {
            $endpoint = $resource_type.endpoints[$i]
            # Add the current endpoint to the new list
            $new_endpoints += $endpoint
            
            # Check if the endpoint contains the target API version
            if ($endpoint.apiVersions -contains $target_api_version) {
                # Create a new section by copying the existing endpoint
                $new_section = $endpoint.PSObject.Copy()
                # Update the API version in the new section
                $new_section.apiVersions = @($new_api_version)
                
                # Check if the requiredFeatures property exists and update it
                if ($new_section.PSObject.Properties.Match("requiredFeatures").Count -gt 0) {
                    $new_section.requiredFeatures = $new_section.requiredFeatures -replace $target_required_feature, $new_required_feature
                } else {
                    # Add the requiredFeatures property if it doesn't exist
                    $new_section | Add-Member -MemberType NoteProperty -Name requiredFeatures -Value @($new_required_feature)
                }
                
                # Insert the new section just after the found target section
                $new_endpoints += $new_section
            }
        }
        # Replace the old endpoints list with the new one
        $resource_type.endpoints = $new_endpoints
    }

    # Ensure the output directory exists
    $output_dir = Split-Path -Path $output_file_path
    if (-not (Test-Path -Path $output_dir)) {
        New-Item -ItemType Directory -Path $output_dir | Out-Null
    }

    # Save the updated JSON data to the output file
    $json_data | ConvertTo-Json -Depth 10 | Set-Content -Path $output_file_path

    Write-Output "New sections added successfully and saved to $output_file_path for environment $target_environment."
}