$PSNativeCommandUseErrorActionPreference = $true
$ErrorActionPreference = "Stop"

Write-Host "Logging Into Azure Subscription ${env:RegistrationSubscription}!"

az cloud set -n $env:CloudName
az login --identity
az account set --subscription $env:RegistrationSubscription

Write-Host "Checking in ARM Manifest to ${env:CheckInEnvironment} for Resource Provider ${env:ResourceProvider}!"

if ($env:CheckInEnvironment -eq "PROD") {
    $Manifest = Invoke-RestMethod -URI ${env:PRODManifestJSON} | ConvertTo-Json -Depth 100
} elseif ($env:CheckInEnvironment -eq "MOONCAKE") {
    $Manifest = Invoke-RestMethod -URI ${env:MOONCAKEManifestJSON} | ConvertTo-Json -Depth 100
} elseif ($env:CheckInEnvironment -eq "FAIRFAX") {
    $Manifest = Invoke-RestMethod -URI ${env:FAIRFAXManifestJSON} | ConvertTo-Json -Depth 100
} elseif ($env:CheckInEnvironment -eq "CANARY") {
    $Manifest = Invoke-RestMethod -URI ${env:CANARYManifestJSON} | ConvertTo-Json -Depth 100
}

$CheckinBody = "{'environment': '${env:CheckInEnvironment}', 'manifest': ${Manifest}}"

Out-File -FilePath "Manifest.json" -InputObject $CheckinBody

$checkInResult = az rest --method POST `
 --url /subscriptions/$env:RegistrationSubscription/providers/Microsoft.ProviderHub/providerRegistrations/$env:ResourceProvider/checkinManifest?api-version=2023-08-01-preview `
 --body '@Manifest.json' | ConvertFrom-Json

if ($checkInResult.isCheckedIn -eq $false) {
    Write-Host "Failed to Check in Manifest: ${checkInResult}"
    throw $checkInResult
}
else {
    Write-Host "Successfully checked in ARM Manifest to ${env:CheckInEnvironment} for Resource Provider ${env:ResourceProvider}!"
}