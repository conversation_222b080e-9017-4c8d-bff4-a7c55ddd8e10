{"providerAuthorizations": [{"applicationId": "d9327919-6775-4843-9037-3fb0fb0473cb", "roleDefinitionId": "f31567d0-b61f-43c2-97a5-a98cdc3bfcb6", "managedByRoleDefinitionId": "9e3af657-a8ff-583c-a75c-2fe7c4bcb635", "managedByAuthorization": {"additionalAuthorizations": [{"applicationId": "2ff814a6-3304-4ab8-85cb-cd0e6f879c1d", "roleDefinitionId": "9e3af657-a8ff-583c-a75c-2fe7c4bcb635"}]}}, {"applicationId": "2ff814a6-3304-4ab8-85cb-cd0e6f879c1d", "roleDefinitionId": "f31567d0-b61f-43c2-97a5-a98cdc3bfcb6", "managedByRoleDefinitionId": "9e3af657-a8ff-583c-a75c-2fe7c4bcb635"}], "namespace": "Microsoft.Databricks", "providerVersion": "2.0", "providerType": "Internal", "tokenAuthConfiguration": {"authenticationScheme": "PoP", "signedRequestScope": "ResourceUri", "disableCertificateAuthenticationFallback": false}, "notificationOptions": "NotSpecified", "resourceTypes": [{"name": "workspaces", "routingType": "<PERSON><PERSON><PERSON>", "resourceDeletionPolicy": "Force", "linkedAccessChecks": [{"actionName": "Microsoft.Databricks/workspaces/write", "linkedProperty": "properties.parameters.customVirtualNetworkId.value", "linkedAction": "Microsoft.Network/virtualNetworks/subnets/join/action"}, {"actionName": "Microsoft.Databricks/workspaces/write", "linkedProperty": "properties.parameters.amlWorkspaceId.value", "linkedAction": "Microsoft.MachineLearningServices/workspaces/write"}, {"actionName": "Microsoft.Databricks/workspaces/write", "linkedProperty": "properties.accessConnector.id", "linkedAction": "Microsoft.Databricks/accessConnectors/write"}], "onBehalfOfTokens": [{"actionName": "Microsoft.Databricks/workspaces/write", "lifeTime": "PT4H"}, {"actionName": "Microsoft.Databricks/workspaces/delete", "lifeTime": "PT4H"}], "linkedOperationRules": [{"linkedOperation": "CrossResourceGroupResourceMove, CrossSubscriptionResourceMove", "linkedAction": "Blocked"}], "loggingRules": [{"action": "Microsoft.Databricks/workspaces/write", "direction": "Request, Response", "detailLevel": "Body"}], "throttlingRules": [{"action": "Microsoft.Databricks/workspaces/read", "metrics": [{"type": "NumberOfRequests", "limit": 1800, "interval": "PT5M"}]}, {"action": "Microsoft.Databricks/workspaces/write", "metrics": [{"type": "NumberOfRequests", "limit": 150, "interval": "PT5M"}]}], "endpoints": [{"enabled": true, "apiVersions": ["2018-03-01", "2018-03-15"], "endpointUri": "https://usgovvirginiasf.adb-spearfish-rp.azure.us:11707", "locations": ["USGov Virginia"], "requiredFeatures": ["Microsoft.Databricks/DatabricksTestEnv"], "timeout": "PT2M"}, {"enabled": true, "apiVersions": ["2018-04-01", "2021-04-01-preview", "2022-04-01-preview", "2023-02-01", "2023-04-01-preview", "2023-09-15-preview", "2024-02-01-preview", "2024-05-01", "2024-09-01-preview", "2025-03-01-preview"], "endpointUri": "https://usgovvirginiasf.adb-spearfish-rp.azure.us:11707", "locations": ["USGov Virginia"], "timeout": "PT2M"}, {"enabled": true, "apiVersions": ["2025-02-01-preview"], "endpointUri": "https://usgovvirginiasf.adb-spearfish-rp.azure.us:11707", "locations": ["USGov Virginia"], "requiredFeatures": ["Microsoft.Databricks/ManagedvNetInjectionMigration"], "timeout": "PT2M"}, {"enabled": true, "apiVersions": ["2025-06-01-preview"], "endpointUri": "https://usgovvirginiasf.adb-spearfish-rp.azure.us:11707", "locations": ["USGov Virginia"], "requiredFeatures": ["Microsoft.Databricks/EnableServerlessWorkspaceFeature"], "timeout": "PT2M"}, {"enabled": true, "apiVersions": ["2021-10-01-preview"], "endpointUri": "https://usgovvirginiasf.adb-spearfish-rp.azure.us:11707", "locations": ["USGov Virginia"], "requiredFeatures": ["Microsoft.Databricks/EnableDatabricksRoleAssignmentsPreview"], "timeout": "PT2M"}, {"enabled": true, "apiVersions": ["2018-03-15"], "endpointUri": "https://usgovarizonasf.adb-spearfish-rp.azure.us:11707", "locations": ["USGov Arizona"], "requiredFeatures": ["Microsoft.Databricks/DatabricksTestEnv"], "timeout": "PT2M"}, {"enabled": true, "apiVersions": ["2018-04-01", "2021-04-01-preview", "2022-04-01-preview", "2023-02-01", "2023-04-01-preview", "2023-09-15-preview", "2024-02-01-preview", "2024-05-01", "2024-09-01-preview", "2025-03-01-preview"], "endpointUri": "https://usgovarizonasf.adb-spearfish-rp.azure.us:11707", "locations": ["USGov Arizona"], "timeout": "PT2M"}, {"enabled": true, "apiVersions": ["2025-02-01-preview"], "endpointUri": "https://usgovarizonasf.adb-spearfish-rp.azure.us:11707", "locations": ["USGov Arizona"], "requiredFeatures": ["Microsoft.Databricks/ManagedvNetInjectionMigration"], "timeout": "PT2M"}, {"enabled": true, "apiVersions": ["2025-06-01-preview"], "endpointUri": "https://usgovarizonasf.adb-spearfish-rp.azure.us:11707", "locations": ["USGov Arizona"], "requiredFeatures": ["Microsoft.Databricks/EnableServerlessWorkspaceFeature"], "timeout": "PT2M"}, {"enabled": true, "apiVersions": ["2021-10-01-preview"], "endpointUri": "https://usgovarizonasf.adb-spearfish-rp.azure.us:11707", "locations": ["USGov Arizona"], "requiredFeatures": ["Microsoft.Databricks/EnableDatabricksRoleAssignmentsPreview"], "timeout": "PT2M"}, {"enabled": true, "apiVersions": ["2018-03-01", "2021-04-01-preview", "2022-04-01-preview", "2023-02-01", "2023-04-01-preview", "2023-09-15-preview", "2024-02-01-preview", "2024-05-01", "2024-09-01-preview", "2025-03-01-preview"], "endpointUri": "https://usgovtexassf.adb-spearfish-rp.azure.us:11707", "locations": ["USGov Texas"], "requiredFeatures": ["Microsoft.Databricks/DatabricksTestEnv"], "timeout": "PT2M"}, {"enabled": true, "apiVersions": ["2025-02-01-preview"], "endpointUri": "https://usgovtexassf.adb-spearfish-rp.azure.us:11707", "locations": ["USGov Texas"], "requiredFeatures": ["Microsoft.Databricks/DatabricksTestEnv", "Microsoft.Databricks/ManagedvNetInjectionMigration"], "featuresRule": {"requiredFeaturesPolicy": "All"}, "timeout": "PT2M"}, {"enabled": true, "apiVersions": ["2025-06-01-preview"], "endpointUri": "https://usgovtexassf.adb-spearfish-rp.azure.us:11707", "locations": ["USGov Texas"], "requiredFeatures": ["Microsoft.Databricks/DatabricksTestEnv", "Microsoft.Databricks/EnableServerlessWorkspaceFeature"], "featuresRule": {"requiredFeaturesPolicy": "All"}, "timeout": "PT2M"}, {"enabled": true, "apiVersions": ["2021-10-01-preview"], "endpointUri": "https://usgovtexassf.adb-spearfish-rp.azure.us:11707", "locations": ["USGov Texas"], "requiredFeatures": ["Microsoft.Databricks/DatabricksTestEnv", "Microsoft.Databricks/EnableDatabricksRoleAssignmentsPreview"], "featuresRule": {"requiredFeaturesPolicy": "All"}, "timeout": "PT2M"}], "management": {"serviceTreeInfos": [{"serviceId": "90b3f8fb-6a44-4ae3-9705-01da5c4ab7d5", "readiness": "GA"}], "resourceAccessPolicy": "NotSpecified"}, "marketplaceType": "Bypass", "metadata": {"microsoft.insights": {"monitoringResourceProvider": {"version": "1.0", "apiVersions": {"default": "2018-04-01"}, "metrics": {"mdsInfo": [{"serviceIdentity": "DatabrickAuditLogs"}], "mdmInfo": [{"sourceMdmAccount": "AzureDatabricksHotFFProd", "sourceMdmNamespace": "Canary"}]}, "logs": {"logFilterPathSelector": "sku.name", "mdsInfo": [{"serviceIdentity": "DatabrickAuditLogs", "onbehalfSupportedLogCategories": ["dbfs", "clusters", "accounts", "jobs", "notebook", "ssh", "workspace", "secrets", "sqlPermissions", "tables", "instancePools", "sqlanalytics", "genie", "globalInitScripts", "iamRole", "mlflowExperiment", "featureStore", "RemoteHistoryService", "mlflowAcledArtifact", "databrickssql", "deltaPipelines", "modelRegistry", "repos", "unityCatalog", "gitCredentials", "webTerminal", "serverlessRealTimeInference", "clusterLibraries", "partnerHub", "clamAVScan", "capsule8Dataplane"]}]}}}}}, {"name": "accessConnectors", "routingType": "<PERSON><PERSON><PERSON>", "resourceDeletionPolicy": "NotSpecified", "linkedAccessChecks": [{"actionName": "Microsoft.Databricks/accessConnectors/write", "linkedProperty": "identity.identityIds[*]", "linkedAction": "Microsoft.ManagedIdentity/userAssignedIdentities/assign/action"}, {"actionName": "Microsoft.Databricks/accessConnectors/write", "linkedProperty": "identity.userAssignedIdentities.*~", "linkedAction": "Microsoft.ManagedIdentity/userAssignedIdentities/assign/action"}], "linkedOperationRules": [{"linkedOperation": "CrossResourceGroupResourceMove, CrossSubscriptionResourceMove", "linkedAction": "Blocked"}], "defaultApiVersion": "2024-05-01", "loggingRules": [{"action": "Microsoft.Databricks/accessConnectors/write", "direction": "Request, Response", "detailLevel": "Body"}], "throttlingRules": [{"action": "Microsoft.Databricks/accessConnectors/read", "metrics": [{"type": "NumberOfRequests", "limit": 1800, "interval": "PT5M"}]}, {"action": "Microsoft.Databricks/accessConnectors/write", "metrics": [{"type": "NumberOfRequests", "limit": 150, "interval": "PT5M"}]}], "endpoints": [{"enabled": true, "apiVersions": ["2022-04-01-preview", "2022-10-01-preview", "2023-05-01", "2024-05-01", "2024-09-01-preview", "2025-03-01-preview"], "endpointUri": "https://usgovvirginiasf.adb-spearfish-rp.azure.us:11707", "locations": ["USGov Virginia"], "timeout": "PT2M"}, {"enabled": true, "apiVersions": ["2022-04-01-preview", "2022-10-01-preview", "2023-05-01", "2024-05-01", "2024-09-01-preview", "2025-03-01-preview"], "endpointUri": "https://usgovarizonasf.adb-spearfish-rp.azure.us:11707", "locations": ["USGov Arizona"], "timeout": "PT2M"}, {"enabled": true, "apiVersions": ["2022-04-01-preview", "2022-10-01-preview", "2023-05-01", "2024-05-01", "2024-09-01-preview", "2025-03-01-preview"], "endpointUri": "https://usgovtexassf.adb-spearfish-rp.azure.us:11707", "locations": ["USGov Texas"], "requiredFeatures": ["Microsoft.Databricks/DatabricksTestEnv", "Microsoft.Databricks/DevControlPlane"], "timeout": "PT2M"}], "marketplaceType": "Bypass", "identityManagement": {"type": "SystemAssigned, UserAssigned"}, "management": {"serviceTreeInfos": [{"serviceId": "90b3f8fb-6a44-4ae3-9705-01da5c4ab7d5", "readiness": "InDevelopment"}], "resourceAccessPolicy": "NotSpecified"}}, {"name": "workspaces/virtualNetworkPeerings", "routingType": "ProxyOnly", "resourceDeletionPolicy": "NotSpecified", "linkedAccessChecks": [{"actionName": "Microsoft.Databricks/workspaces/virtualNetworkPeerings/write", "linkedProperty": "properties.remoteVirtualNetwork.id", "linkedActionVerb": "peer/action"}], "linkedNotificationRules": [{"actions": ["Microsoft.Databricks/workspaces/write", "Microsoft.Databricks/workspaces/delete"]}], "loggingRules": [{"action": "Microsoft.Databricks/workspaces/virtualNetworkPeerings/write", "direction": "Request, Response", "detailLevel": "Body"}], "endpoints": [{"enabled": true, "apiVersions": ["2018-03-01"], "endpointUri": "https://azure-rp-backend.dev.databricks.azure.us", "locations": ["USGov Virginia", "USGov Texas"], "requiredFeatures": ["Microsoft.Databricks/DatabricksTestEnv"], "timeout": "PT2M"}, {"enabled": true, "apiVersions": ["2021-04-01-preview", "2022-04-01-preview", "2023-02-01", "2023-04-01-preview", "2023-09-15-preview", "2024-02-01-preview", "2024-05-01", "2024-09-01-preview", "2025-03-01-preview"], "endpointUri": "https://azure-rp-backend.dev.databricks.azure.us", "locations": ["USGov Virginia", "USGov Texas"], "requiredFeatures": ["Microsoft.Databricks/DatabricksTestEnv", "Microsoft.Databricks/DevControlPlane"], "featuresRule": {"requiredFeaturesPolicy": "All"}, "timeout": "PT2M"}, {"enabled": true, "apiVersions": ["2018-03-15"], "endpointUri": "https://azure-rp-backend.staging.databricks.azure.us", "locations": ["USGov Virginia", "USGov Arizona"], "requiredFeatures": ["Microsoft.Databricks/DatabricksTestEnv"], "timeout": "PT2M"}, {"enabled": true, "apiVersions": ["2021-04-01-preview", "2022-04-01-preview", "2023-02-01", "2023-04-01-preview", "2023-09-15-preview", "2024-02-01-preview", "2024-05-01", "2024-09-01-preview", "2025-03-01-preview"], "endpointUri": "https://azure-rp-backend.staging.databricks.azure.us", "locations": ["USGov Virginia", "USGov Arizona"], "requiredFeatures": ["Microsoft.Databricks/DatabricksTestEnv", "Microsoft.Databricks/StagingControlPlane"], "featuresRule": {"requiredFeaturesPolicy": "All"}, "timeout": "PT2M"}, {"enabled": true, "apiVersions": ["2018-04-01", "2021-04-01-preview", "2022-04-01-preview", "2023-02-01", "2023-04-01-preview", "2023-09-15-preview", "2024-02-01-preview", "2024-05-01", "2024-09-01-preview", "2025-03-01-preview"], "endpointUri": "https://azure-rp-backend.databricks.azure.us", "locations": ["USGov Virginia", "USGov Arizona", "USGov Texas"], "timeout": "PT2M"}], "marketplaceType": "Bypass", "management": {"serviceTreeInfos": [{"serviceId": "90b3f8fb-6a44-4ae3-9705-01da5c4ab7d5", "readiness": "GA"}]}}, {"name": "workspaces/dbWorkspaces", "routingType": "ProxyOnly", "resourceDeletionPolicy": "NotSpecified", "policyExecutionType": "BypassPolicies", "loggingRules": [{"action": "Microsoft.Databricks/workspaces/dbWorkspaces/write", "direction": "Request, Response", "detailLevel": "Body"}], "endpoints": [{"enabled": true, "apiVersions": ["2018-03-01"], "endpointUri": "https://azure-rp-backend.dev.databricks.azure.us", "locations": ["USGov Virginia", "USGov Texas"], "requiredFeatures": ["Microsoft.Databricks/DatabricksTestEnv"], "timeout": "PT2M"}, {"enabled": true, "apiVersions": ["2018-03-15"], "endpointUri": "https://azure-rp-backend.staging.databricks.azure.us", "locations": ["USGov Virginia", "USGov Arizona"], "requiredFeatures": ["Microsoft.Databricks/DatabricksTestEnv"], "timeout": "PT2M"}, {"enabled": true, "apiVersions": ["2018-04-01"], "endpointUri": "https://azure-rp-backend.databricks.azure.us", "locations": ["USGov Virginia", "USGov Arizona", "USGov Texas"], "timeout": "PT2M"}], "marketplaceType": "Bypass", "management": {"serviceTreeInfos": [{"serviceId": "90b3f8fb-6a44-4ae3-9705-01da5c4ab7d5", "readiness": "InternalOnly"}], "resourceAccessPolicy": "NotSpecified"}}, {"name": "operations", "routingType": "<PERSON>xy<PERSON><PERSON><PERSON>, Tenant", "resourceDeletionPolicy": "NotSpecified", "allowedUnauthorizedActions": ["Microsoft.Databricks/operations/read"], "endpoints": [{"enabled": true, "apiVersions": ["2018-03-01", "2018-03-15", "2018-04-01", "2021-04-01-preview", "2021-10-01-preview", "2022-04-01-preview", "2023-02-01", "2023-04-01-preview", "2025-02-01-preview", "2023-09-15-preview", "2024-02-01-preview", "2024-05-01", "2024-09-01-preview", "2025-03-01-preview"], "endpointUri": "https://usgovvirginiasf.adb-spearfish-rp.azure.us:11707", "locations": ["USGov Virginia", "USGov Arizona", "USGov Texas"], "timeout": "PT2M"}, {"enabled": true, "apiVersions": ["2025-06-01-preview"], "endpointUri": "https://usgovvirginiasf.adb-spearfish-rp.azure.us:11707", "locations": ["USGov Virginia", "USGov Arizona", "USGov Texas"], "timeout": "PT2M", "requiredFeatures": ["Microsoft.Databricks/EnableServerlessWorkspaceFeature"]}], "management": {"serviceTreeInfos": [{"serviceId": "90b3f8fb-6a44-4ae3-9705-01da5c4ab7d5", "readiness": "GA"}], "resourceAccessPolicy": "NotSpecified"}}, {"name": "locations", "routingType": "ProxyOnly", "resourceDeletionPolicy": "NotSpecified", "endpoints": [{"enabled": true, "apiVersions": ["2018-03-01", "2018-03-15", "2018-04-01", "2021-04-01-preview", "2022-04-01-preview", "2023-02-01", "2023-04-01-preview", "2023-09-15-preview", "2024-02-01-preview", "2024-05-01", "2024-09-01-preview", "2025-03-01-preview"], "endpointUri": "https://usgovvirginiasf.adb-spearfish-rp.azure.us:11707", "locations": ["USGov Virginia", "USGov Arizona", "USGov Texas"], "timeout": "PT2M"}, {"enabled": true, "apiVersions": ["2025-02-01-preview"], "endpointUri": "https://usgovvirginiasf.adb-spearfish-rp.azure.us:11707", "locations": ["USGov Virginia", "USGov Arizona", "USGov Texas"], "requiredFeatures": ["Microsoft.Databricks/ManagedvNetInjectionMigration"], "timeout": "PT2M"}, {"enabled": true, "apiVersions": ["2025-06-01-preview"], "endpointUri": "https://usgovvirginiasf.adb-spearfish-rp.azure.us:11707", "locations": ["USGov Virginia", "USGov Arizona", "USGov Texas"], "requiredFeatures": ["Microsoft.Databricks/EnableServerlessWorkspaceFeature"], "timeout": "PT2M"}, {"enabled": true, "apiVersions": ["2021-10-01-preview"], "endpointUri": "https://usgovvirginiasf.adb-spearfish-rp.azure.us:11707", "locations": ["USGov Virginia", "USGov Arizona", "USGov Texas"], "requiredFeatures": ["Microsoft.Databricks/EnableDatabricksRoleAssignmentsPreview"], "timeout": "PT2M"}], "management": {"serviceTreeInfos": [{"serviceId": "90b3f8fb-6a44-4ae3-9705-01da5c4ab7d5", "readiness": "GA"}], "resourceAccessPolicy": "NotSpecified"}}, {"name": "locations/operationstatuses", "routingType": "ProxyOnly, LocationBased", "resourceDeletionPolicy": "NotSpecified", "additionalOptions": "ProtectedAsyncOperationPolling", "endpoints": [{"enabled": true, "apiVersions": ["2018-03-01", "2018-03-15"], "endpointUri": "https://usgovvirginiasf.adb-spearfish-rp.azure.us:11707", "locations": ["USGov Virginia"], "requiredFeatures": ["Microsoft.Databricks/DatabricksTestEnv"], "timeout": "PT2M"}, {"enabled": true, "apiVersions": ["2018-04-01", "2021-04-01-preview", "2022-04-01-preview", "2023-02-01", "2023-04-01-preview", "2023-09-15-preview", "2024-02-01-preview", "2024-05-01", "2024-09-01-preview", "2025-03-01-preview"], "endpointUri": "https://usgovvirginiasf.adb-spearfish-rp.azure.us:11707", "locations": ["USGov Virginia"], "timeout": "PT2M"}, {"enabled": true, "apiVersions": ["2025-02-01-preview"], "endpointUri": "https://usgovvirginiasf.adb-spearfish-rp.azure.us:11707", "locations": ["USGov Virginia"], "requiredFeatures": ["Microsoft.Databricks/ManagedvNetInjectionMigration"], "timeout": "PT2M"}, {"enabled": true, "apiVersions": ["2025-06-01-preview"], "endpointUri": "https://usgovvirginiasf.adb-spearfish-rp.azure.us:11707", "locations": ["USGov Virginia"], "requiredFeatures": ["Microsoft.Databricks/EnableServerlessWorkspaceFeature"], "timeout": "PT2M"}, {"enabled": true, "apiVersions": ["2021-10-01-preview"], "endpointUri": "https://usgovvirginiasf.adb-spearfish-rp.azure.us:11707", "locations": ["USGov Virginia"], "requiredFeatures": ["Microsoft.Databricks/EnableDatabricksRoleAssignmentsPreview"], "timeout": "PT2M"}, {"enabled": true, "apiVersions": ["2018-03-15"], "endpointUri": "https://usgovarizonasf.adb-spearfish-rp.azure.us:11707", "locations": ["USGov Arizona"], "requiredFeatures": ["Microsoft.Databricks/DatabricksTestEnv"], "timeout": "PT2M"}, {"enabled": true, "apiVersions": ["2018-04-01", "2021-04-01-preview", "2022-04-01-preview", "2023-02-01", "2023-04-01-preview", "2023-09-15-preview", "2024-02-01-preview", "2024-05-01", "2024-09-01-preview", "2025-03-01-preview"], "endpointUri": "https://usgovarizonasf.adb-spearfish-rp.azure.us:11707", "locations": ["USGov Arizona"], "timeout": "PT2M"}, {"enabled": true, "apiVersions": ["2025-02-01-preview"], "endpointUri": "https://usgovarizonasf.adb-spearfish-rp.azure.us:11707", "locations": ["USGov Arizona"], "requiredFeatures": ["Microsoft.Databricks/ManagedvNetInjectionMigration"], "timeout": "PT2M"}, {"enabled": true, "apiVersions": ["2025-06-01-preview"], "endpointUri": "https://usgovarizonasf.adb-spearfish-rp.azure.us:11707", "locations": ["USGov Arizona"], "requiredFeatures": ["Microsoft.Databricks/EnableServerlessWorkspaceFeature"], "timeout": "PT2M"}, {"enabled": true, "apiVersions": ["2021-10-01-preview"], "endpointUri": "https://usgovarizonasf.adb-spearfish-rp.azure.us:11707", "locations": ["USGov Arizona"], "requiredFeatures": ["Microsoft.Databricks/EnableDatabricksRoleAssignmentsPreview"], "timeout": "PT2M"}, {"enabled": true, "apiVersions": ["2018-03-01", "2021-04-01-preview", "2022-04-01-preview", "2023-02-01", "2023-04-01-preview", "2023-09-15-preview", "2024-02-01-preview", "2024-05-01", "2024-09-01-preview", "2025-03-01-preview"], "endpointUri": "https://usgovtexassf.adb-spearfish-rp.azure.us:11707", "locations": ["USGov Texas"], "requiredFeatures": ["Microsoft.Databricks/DatabricksTestEnv"], "timeout": "PT2M"}, {"enabled": true, "apiVersions": ["2025-02-01-preview"], "endpointUri": "https://usgovtexassf.adb-spearfish-rp.azure.us:11707", "locations": ["USGov Texas"], "requiredFeatures": ["Microsoft.Databricks/DatabricksTestEnv", "Microsoft.Databricks/ManagedvNetInjectionMigration"], "featuresRule": {"requiredFeaturesPolicy": "All"}, "timeout": "PT2M"}, {"enabled": true, "apiVersions": ["2025-06-01-preview"], "endpointUri": "https://usgovtexassf.adb-spearfish-rp.azure.us:11707", "locations": ["USGov Texas"], "requiredFeatures": ["Microsoft.Databricks/DatabricksTestEnv", "Microsoft.Databricks/EnableServerlessWorkspaceFeature"], "featuresRule": {"requiredFeaturesPolicy": "All"}, "timeout": "PT2M"}, {"enabled": true, "apiVersions": ["2021-10-01-preview"], "endpointUri": "https://usgovtexassf.adb-spearfish-rp.azure.us:11707", "locations": ["USGov Texas"], "requiredFeatures": ["Microsoft.Databricks/DatabricksTestEnv", "Microsoft.Databricks/EnableDatabricksRoleAssignmentsPreview"], "featuresRule": {"requiredFeaturesPolicy": "All"}, "timeout": "PT2M"}], "management": {"serviceTreeInfos": [{"serviceId": "90b3f8fb-6a44-4ae3-9705-01da5c4ab7d5", "readiness": "GA"}], "resourceAccessPolicy": "NotSpecified"}}, {"name": "locations/getNetworkPolicies", "routingType": "ProxyOnly, LocationBased", "resourceDeletionPolicy": "NotSpecified", "endpoints": [{"enabled": true, "apiVersions": ["2018-03-01", "2018-03-15"], "endpointUri": "https://usgovvirginiasf.adb-spearfish-rp.azure.us:11707", "locations": ["USGov Virginia"], "requiredFeatures": ["Microsoft.Databricks/DatabricksTestEnv"], "timeout": "PT2M"}, {"enabled": true, "apiVersions": ["2018-04-01", "2021-04-01-preview", "2022-04-01-preview", "2023-02-01", "2023-04-01-preview", "2023-09-15-preview", "2024-02-01-preview", "2024-05-01", "2024-09-01-preview", "2025-03-01-preview"], "endpointUri": "https://usgovvirginiasf.adb-spearfish-rp.azure.us:11707", "locations": ["USGov Virginia"], "timeout": "PT2M"}, {"enabled": true, "apiVersions": ["2025-02-01-preview"], "endpointUri": "https://usgovvirginiasf.adb-spearfish-rp.azure.us:11707", "locations": ["USGov Virginia"], "requiredFeatures": ["Microsoft.Databricks/ManagedvNetInjectionMigration"], "timeout": "PT2M"}, {"enabled": true, "apiVersions": ["2025-06-01-preview"], "endpointUri": "https://usgovvirginiasf.adb-spearfish-rp.azure.us:11707", "locations": ["USGov Virginia"], "requiredFeatures": ["Microsoft.Databricks/EnableServerlessWorkspaceFeature"], "timeout": "PT2M"}, {"enabled": true, "apiVersions": ["2021-10-01-preview"], "endpointUri": "https://usgovvirginiasf.adb-spearfish-rp.azure.us:11707", "locations": ["USGov Virginia"], "requiredFeatures": ["Microsoft.Databricks/EnableDatabricksRoleAssignmentsPreview"], "timeout": "PT2M"}, {"enabled": true, "apiVersions": ["2018-03-15"], "endpointUri": "https://usgovarizonasf.adb-spearfish-rp.azure.us:11707", "locations": ["USGov Arizona"], "requiredFeatures": ["Microsoft.Databricks/DatabricksTestEnv"], "timeout": "PT2M"}, {"enabled": true, "apiVersions": ["2018-04-01", "2021-04-01-preview", "2022-04-01-preview", "2023-02-01", "2023-04-01-preview", "2023-09-15-preview", "2024-02-01-preview", "2024-05-01", "2024-09-01-preview", "2025-03-01-preview"], "endpointUri": "https://usgovarizonasf.adb-spearfish-rp.azure.us:11707", "locations": ["USGov Arizona"], "timeout": "PT2M"}, {"enabled": true, "apiVersions": ["2025-02-01-preview"], "endpointUri": "https://usgovarizonasf.adb-spearfish-rp.azure.us:11707", "locations": ["USGov Arizona"], "requiredFeatures": ["Microsoft.Databricks/ManagedvNetInjectionMigration"], "timeout": "PT2M"}, {"enabled": true, "apiVersions": ["2025-06-01-preview"], "endpointUri": "https://usgovarizonasf.adb-spearfish-rp.azure.us:11707", "locations": ["USGov Arizona"], "requiredFeatures": ["Microsoft.Databricks/EnableServerlessWorkspaceFeature"], "timeout": "PT2M"}, {"enabled": true, "apiVersions": ["2021-10-01-preview"], "endpointUri": "https://usgovarizonasf.adb-spearfish-rp.azure.us:11707", "locations": ["USGov Arizona"], "requiredFeatures": ["Microsoft.Databricks/EnableDatabricksRoleAssignmentsPreview"], "timeout": "PT2M"}, {"enabled": true, "apiVersions": ["2018-03-01", "2021-04-01-preview", "2022-04-01-preview", "2023-02-01", "2023-04-01-preview", "2023-09-15-preview", "2024-02-01-preview", "2024-05-01", "2024-09-01-preview", "2025-03-01-preview"], "endpointUri": "https://usgovtexassf.adb-spearfish-rp.azure.us:11707", "locations": ["USGov Texas"], "requiredFeatures": ["Microsoft.Databricks/DatabricksTestEnv"], "timeout": "PT2M"}, {"enabled": true, "apiVersions": ["2025-02-01-preview"], "endpointUri": "https://usgovtexassf.adb-spearfish-rp.azure.us:11707", "locations": ["USGov Texas"], "requiredFeatures": ["Microsoft.Databricks/DatabricksTestEnv", "Microsoft.Databricks/ManagedvNetInjectionMigration"], "featuresRule": {"requiredFeaturesPolicy": "All"}, "timeout": "PT2M"}, {"enabled": true, "apiVersions": ["2025-06-01-preview"], "endpointUri": "https://usgovtexassf.adb-spearfish-rp.azure.us:11707", "locations": ["USGov Texas"], "requiredFeatures": ["Microsoft.Databricks/DatabricksTestEnv", "Microsoft.Databricks/EnableServerlessWorkspaceFeature"], "featuresRule": {"requiredFeaturesPolicy": "All"}, "timeout": "PT2M"}, {"enabled": true, "apiVersions": ["2021-10-01-preview"], "endpointUri": "https://usgovtexassf.adb-spearfish-rp.azure.us:11707", "locations": ["USGov Texas"], "requiredFeatures": ["Microsoft.Databricks/DatabricksTestEnv", "Microsoft.Databricks/EnableDatabricksRoleAssignmentsPreview"], "featuresRule": {"requiredFeaturesPolicy": "All"}, "timeout": "PT2M"}], "marketplaceType": "NotSpecified", "management": {"serviceTreeInfos": [{"serviceId": "90b3f8fb-6a44-4ae3-9705-01da5c4ab7d5", "readiness": "GA"}], "resourceAccessPolicy": "NotSpecified"}}], "management": {"manifestOwners": ["Databricks-PlatformServiceAdministrator"], "incidentRoutingService": "Azure Databricks Service", "incidentRoutingTeam": "Azure Databricks RP", "incidentContactEmail": "<EMAIL>", "resourceAccessPolicy": "AcisReadAllowed, AcisActionAllowed", "serviceTreeInfos": [{"serviceId": "90b3f8fb-6a44-4ae3-9705-01da5c4ab7d5", "readiness": "GA"}]}, "capabilities": [{"quotaId": "CSP_2015-05-01", "effect": "Allow"}, {"quotaId": "CSP_MG_2017-12-01", "effect": "Allow"}], "metadata": {"owningSubscriptionId": "37a352eb-6f92-45cb-91a2-27cafb74d09b", "microsoft.insights": {"Microsoft.ManagedIdentity": {"applicationIds": ["d9327919-6775-4843-9037-3fb0fb0473cb", "2ff814a6-3304-4ab8-85cb-cd0e6f879c1d"], "restrictedApplicationIds": {"1c8e03fb-11c0-48df-bf00-1b76ade97dd1": {"AccessibleSubscriptions": ["********-46c8-402a-8bc2-c266c5cdfac2", "ebeff1c8-5db5-4ac4-9227-718d1d116763", "37664ca1-2eaa-4abb-bb3c-249d4e857a29"]}}}, "monitoringResourceProvider": {"version": "1.0", "apiVersions": {"default": "2018-04-01", "operations": "2018-03-01"}, "metrics": {"mdsInfo": [{"serviceIdentity": "DatabrickAuditLogs"}], "mdmInfo": [{"sourceMdmAccount": "AzureDatabricksHotFFProd", "sourceMdmNamespace": "Canary"}]}}}}, "enableTenantLinkedNotification": false}