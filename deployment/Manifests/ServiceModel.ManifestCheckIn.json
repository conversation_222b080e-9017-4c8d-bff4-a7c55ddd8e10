{"$schema": "https://ev2schema.azure.net/schemas/2020-04-01/RegionAgnosticServiceModel.json", "ContentVersion": "*******", "ServiceMetadata": {"ServiceIdentifier": "90b3f8fb-6a44-4ae3-9705-01da5c4ab7d5", "ServiceGroup": "Microsoft.Azure.ARMCoreRP.ManifestCheckIn", "Environment": "$config(environment)", "TenantId": "$config(tenantId)"}, "ServiceResourceGroupDefinitions": [{"Name": "ManifestCheckIn", "AzureResourceGroupName": "ResourceProviders", "SubscriptionKey": "ResourceProviderRegistration", "ExecutionConstraint": {"quantifier": "Always", "level": "Cloud", "regions": ["$config(manifestDeploymentRegion)"]}, "ServiceResourceDefinitions": [{"name": "ManifestCheckIn", "composedOf": {"Extension": {"RolloutParametersPath": "Parameters\\ManifestCheckInParameters.json", "Shell": [{"Type": "ManifestCheckIn", "Properties": {"ImageName": "adm-mariner-20-l", "ImageVersion": "v9"}}]}}, "ScopeTags": [{"name": "ManifestCheckIn"}]}]}]}