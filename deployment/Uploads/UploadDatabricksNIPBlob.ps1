param(
    [Parameter(Mandatory=$true)][string]$location
)

$containerName = "databricksnipmappings"
$keyVaultName = "adbrp" + $location + "secrets"
$applianceStorageName = $location + "packagedata"
$subscriptionName = "Azure Databricks Resource Provider"
$applianceStorageSecretname = "packagedatastorageconnectionstring"
$resourceGroupName = "adbrp_" + $location

# Login
Set-AzureRmContext -SubscriptionId "9ae8e263-8df7-4b7e-b27b-d59f0bfb685b"
Select-AzureSubscription -Default -Name $subscriptionName

# Get appliance data storage context
$applianceAccount = Get-AzureRmStorageAccount -Name $applianceStorageName -ResourceGroupName $resourceGroupName
$applianceStorageCtx = $applianceAccount.Context

# Add appliance packages to blob
$devBlob = Set-AzureStorageBlobContent -Context $applianceStorageCtx.Context -Container $containerName -File  "..\..\..\DatabricksAppliances\devDatabricksNIPMappings.json" -Blob "devDatabricksNIPMappings.json" -Properties @{"ContentType" = "application/json" } -Force
$stagingBlob = Set-AzureStorageBlobContent -Context $applianceStorageCtx.Context -Container $containerName -File  "..\..\..\DatabricksAppliances\stagingDatabricksNIPMappings.json" -Blob "stagingDatabricksNIPMappings.json" -Properties @{"ContentType" = "application/json" } -Force
$prodBlob = Set-AzureStorageBlobContent -Context $applianceStorageCtx.Context -Container $containerName -File  "..\..\..\DatabricksAppliances\prodDatabricksNIPMappings.json" -Blob "prodDatabricksNIPMappings.json" -Properties @{"ContentType" = "application/json" } -Force

# Create read storage connection string of SA
$saKey = (Get-AzureRmStorageAccountKey -ResourceGroupName $resourceGroupName -Name $applianceStorageName)[0].Value
$applianceStorageConnectionString = 'DefaultEndpointsProtocol=https;AccountName=' + $applianceStorageName + ';AccountKey=' + $saKey + ';EndpointSuffix=core.windows.net' 

# Add connection string to key vault
$keyVaultCtx = Get-AzureRMKeyVault -VaultName $keyVaultName
$secret = ConvertTo-SecureString -String $applianceStorageConnectionString -AsPlainText -Force
$SecretValue = Set-AzureKeyVaultSecret -VaultName $keyVaultName -Name $applianceStorageSecretname -SecretValue $secret