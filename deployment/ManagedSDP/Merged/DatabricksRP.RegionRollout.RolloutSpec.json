﻿{
  "$schema": "https://ev2schema.azure.net/schemas/2020-04-01/RegionAgnosticRolloutSpecification.json",
  "contentVersion": "1.0.0.0",
  "rolloutMetadata": {
    "serviceModelPath": "ManagedSDP\\Merged\\DatabricksRP.RegionRollout.ServiceModel.json",
    "ScopeBindingsPath": "ManagedSDP\\Merged\\DatabricksRP.RegionRollout.ScopeBindings.json",
    "configuration": {
      "serviceGroupScope": {
        "specPath": "ManagedSDP\\Merged\\Configurations\\DatabricksRP.Config.$rolloutInfra().json"
      }
    },
    "name": "Resource Provider deployment",
    "rolloutType": "Major",
    "buildSource": {
      "parameters": {
        "versionFile": "bin\\buildver.txt"
      }
    }
  },
  "orchestratedSteps": [
    {
      "name": "DatabricksRP_SetupRegionRollout",
      "targetType": "ServiceResourceDefinition",
      "targetName": "Microsoft.AzureDatabricks.DatabricksRP.Shell.SetupRegionRollout",
      "actions": [
        "Shell/SetupRegionRollout"
      ],
      "dependsOn": []
    },
    {
      "name": "DatabricksRP_UploadDSCPackage",
      "targetType": "ServiceResourceDefinition",
      "targetName": "Microsoft.AzureDatabricks.DatabricksRP.Shell.UploadDSCPackage",
      "actions": [
        "Shell/UploadDSCPackage"
      ],
      "dependsOn": ["DatabricksRP_SetupRegionRollout"]
    },
    {
      "name": "DatabricksRP_SFMC_VMSS",
      "targetType": "ServiceResourceDefinition",
      "targetName": "Microsoft.AzureDatabricks.DatabricksRP.SFMC.VMSS",
      "actions": [
        "Deploy"
      ],
      "dependsOn": [
        "DatabricksRP_UploadDSCPackage"
      ]
    },
    {
      "name": "DatabricksRP_SFMC_Application",
      "targetType": "ServiceResourceDefinition",
      "targetName": "Microsoft.AzureDatabricks.DatabricksRP.SFMC.Application",
      "actions": [
        "Deploy"
      ],
      "dependsOn": [
        "DatabricksRP_SFMC_VMSS"
      ]
    }
  ]
}