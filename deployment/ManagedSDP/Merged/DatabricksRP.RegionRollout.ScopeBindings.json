{"scopeBindings": [{"scopeTagName": "general-bindings", "bindings": [{"find": "__CREATE_RESOURCES__", "replaceWith": "true"}, {"find": "__UPDATE_SFMC__", "replaceWith": "$config(UpdateSfmc)"}, {"find": "__TenantId__", "replaceWith": "$config(TenantId)"}, {"find": "__BuildVersion__", "replaceWith": "$buildVersion()"}, {"find": "__Environment__", "replaceWith": "$environment()"}, {"find": "__ClusterNameSuffix__", "replaceWith": "$config(ClusterNameSuffix)"}, {"find": "__KeyVaultNameSuffix__", "replaceWith": "$config(KeyVaultNameSuffix)"}, {"find": "__ShellExtensionSubscriptionId__", "replaceWith": "$config(ShellExtensionSubscriptionId)"}, {"find": "__RPAppSPNId__", "replaceWith": "$config(RPAppSPNId)"}, {"find": "__RolloutSPNId__", "replaceWith": "$config(RolloutSPNId)"}]}, {"scopeTagName": "env-geneva-bindings", "bindings": [{"find": "__GCSAccount__", "replaceWith": "$config(geneva.ADBMonitoringGCSAccount)"}, {"find": "__GCSNamespace__", "replaceWith": "$config(geneva.ADBMonitoringGCSNamespace)"}, {"find": "__GCSEnv__", "replaceWith": "$config(geneva.GCSEnvironment)"}]}, {"scopeTagName": "regional-subscription", "bindings": [{"find": "__SubscriptionId__", "replaceWith": "$config(SubscriptionId)"}]}, {"scopeTagName": "regional-region-short-name", "bindings": [{"find": "__ShortRegionName__", "replaceWith": "$config(ShortRegionName)"}]}, {"scopeTagName": "regional-region-friendly-name", "bindings": [{"find": "__Region_Friendly_Name__", "replaceWith": "$config(regionFriendlyName)"}]}, {"scopeTagName": "regional-cluster-name", "bindings": [{"find": "__ClusterName__", "replaceWith": "$config(ClusterNamePrefix)"}]}, {"scopeTagName": "regional-dsc-fileshare", "bindings": [{"find": "__DSC_BLOBCONTAINER__", "replaceWith": "$config(DSCPackageBlobContainerName)"}, {"find": "__DSC_SA_SUFFIX__", "replaceWith": "$config(DSCPackageStorageAccountNameSuffix)"}]}, {"scopeTagName": "regional-trimmed-name", "bindings": [{"find": "__TRIMMED_REGION_NAME__", "replaceWith": "$config(TrimmedRegionName)"}]}, {"scopeTagName": "tenant-name", "bindings": [{"find": "__TENANT_NAME__", "replaceWith": "$config(TenantName)"}]}, {"scopeTagName": "sfmc-bindings", "bindings": [{"find": "__RolloutInfra__", "replaceWith": "$rolloutInfra()"}, {"find": "__AppPackageUrl__", "replaceWith": "$rolloutInfra()\\\\DatabricksRP-$rolloutInfra().sfpkg"}, {"find": "__KeyVaultDnsSuffix__", "replaceWith": "$config(keyVault.dnsSuffix)"}, {"find": "__KustoClusterUri__", "replaceWith": "$config(kustoClusterUri)"}, {"find": "__UserAssignedIdentityRG__", "replaceWith": "$config(UserAssignedIdentityRG)"}, {"find": "__UserAssignedIdentity__", "replaceWith": "$config(UserAssignedIdentity)"}, {"find": "__cloudDns__", "replaceWith": "$config(cloudDns)"}, {"find": "__VMImageSku__", "replaceWith": "$config(VMImageSku)"}, {"find": "__ZonalResiliency__", "replaceWith": "$config(ZonalResiliency)"}, {"find": "__MonitoringConfigVersion__", "replaceWith": "$config(MonitoringConfigVersion)"}, {"find": "__AA<PERSON>ert<PERSON>ame__", "replaceWith": "$config(AADCertName)"}, {"find": "__Geneva<PERSON><PERSON><PERSON><PERSON>__", "replaceWith": "$config(GenevaCertName)"}, {"find": "__SSLEncryptionCertName__", "replaceWith": "$config(SSLEncryptionCertName)"}, {"find": "__Tenant<PERSON>ame__", "replaceWith": "$config(TenantName)"}, {"find": "__SFAdminC<PERSON><PERSON>ame__", "replaceWith": "$config(SFAdminCertName)"}, {"find": "__PrimaryJobsDataStorageAccountName__", "replaceWith": "$config(PrimaryJobsDataStorageAccountName)"}, {"find": "__SecondaryJobsDataStorageAccountName__", "replaceWith": "$config(SecondaryJobsDataStorageAccountName)"}, {"find": "__WorkspaceDataStorageAccountName__", "replaceWith": "$config(WorkspaceDataStorageAccountName)"}, {"find": "$_ServiceInstanceIdTag_$", "replaceWith": "$config(CanonicalRegionName)"}, {"find": "__DSCPackageStorageAccountNameSuffix__", "replaceWith": "$config(DSCPackageStorageAccountNameSuffix)"}, {"find": "__ARGEndpoint__", "replaceWith": "$config(ARGEndpoint)"}]}]}