﻿{
  "$schema": "https://ev2schema.azure.net/schemas/2020-04-01/RegionAgnosticRolloutSpecification.json",
  "contentVersion": "1.0.0.0",
  "rolloutMetadata": {
    "serviceModelPath": "ManagedSDP\\Merged\\Slots\\S3\\DatabricksRP.ServiceModel.Production.S3.json",
    "ScopeBindingsPath": "ManagedSDP\\Merged\\Slots\\S3\\ScopeBindings.S3.json",
    "configuration": {
      "serviceGroupScope": {
        "specPath": "ManagedSDP\\Merged\\Slots\\S3\\RegionalParams.Production.S3.json"
      }
    },
    "name": "Resource Provider deployment",
    "rolloutType": "Major",
    "buildSource": {
      "parameters": {
        "versionFile": "bin\\buildver.txt"
      }
    }
  },
  "orchestratedSteps": [
    {
      "name": "DatabricksRP_UploadDSCPackage",
      "targetType": "ServiceResourceDefinition",
      "targetName": "Microsoft.AzureDatabricks.DatabricksRP.Shell.UploadDSCPackage",
      "actions": [
        "Shell/UploadDSCPackage"
      ],
      "dependsOn": []
    },
    {
      "name": "DatabricksRP_SFMC_VMSS",
      "targetType": "ServiceResourceDefinition",
      "targetName": "Microsoft.AzureDatabricks.DatabricksRP.SFMC.VMSS",
      "actions": [
        "Deploy"
      ],
      "dependsOn": [
        "DatabricksRP_UploadDSCPackage"
      ]
    },
    {
      "name": "DatabricksRP_SFMC_Application",
      "targetType": "ServiceResourceDefinition",
      "targetName": "Microsoft.AzureDatabricks.DatabricksRP.SFMC.Application",
      "actions": [
        "Deploy"
      ],
      "dependsOn": [
        "DatabricksRP_SFMC_VMSS"
      ]
    },
    {
      "name": "DatabricksRP_CrudWorkspaceTest",
      "targetType": "ServiceResourceDefinition",
      "targetName": "Microsoft.AzureDatabricks.DatabricksRP.Shell.CrudWorkspaceTest",
      "actions": [
        "Shell/ValidationScripts"
      ],
      "dependsOn": [
        "DatabricksRP_SFMC_Application"
      ]
    }
  ]
}