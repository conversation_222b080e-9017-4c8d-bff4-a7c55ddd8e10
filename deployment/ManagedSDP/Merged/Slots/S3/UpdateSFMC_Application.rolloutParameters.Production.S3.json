﻿{
  "$schema": "https://schema.management.azure.com/schemas/2019-04-01/deploymentParameters.json#",
  "contentVersion": "1.0.0.0",
  "paths": [
    "appPackageUrl"
  ],
  "parameters": {
    "clusterName": {
      "value": "__ClusterName____ClusterNameSuffix__"
    },
    "applicationTypeName": {
      "value": "Providers.ServiceFabricType.S3"
    },
    "applicationTypeVersion": {
      "value": "__BuildVersion__"
    },
    "appPackageUrl": {
      "value": "bin\\Production\\DatabricksRP-AMEEastUS2EUAP-S3.sfpkg"
    },
    "applicationName": {
      "value": "Providers.ServiceFabric.S3"
    },
    "aadCertThumbprint": {
      "value": "AADCert.CertificateThumbprint"
    },
    "aadCertSubjectName": {
      "value": "AADCert.SubjectSimpleName"
    },
    "sslCertSubjectName": {
      "value": "SSLCert.SubjectSimpleName"
    },
    "featureServiceName": {
      "value": "Providers.ServiceFabric.FeatureRole"
    },
    "featureServiceTypeName": {
      "value": "Providers.ServiceFabric.FeatureRoleType"
    },
    "featureServiceInstanceCount": {
      "value": 6
    },
    "workerServiceName": {
      "value": "Providers.ServiceFabric.WorkerRole"
    },
    "workerServiceTypeName": {
      "value": "Providers.ServiceFabric.WorkerRoleType"
    },
    "workerServiceInstanceCount": {
      "value": 6
    },
    "jobServiceName": {
      "value": "Providers.ServiceFabric.JobRole"
  },
  "jobServiceTypeName": {
      "value": "Providers.ServiceFabric.JobRoleType"
  },
  "jobServiceInstanceCount": {
      "value": 2
  },
    "roleLocation": {
      "value": "__Region_Friendly_Name__"
    },
    "tenantId": {
      "value": "__TenantId__"
    },
    "mainTemplateFilePath_Dev": {
      "value": ".\\DatabricksTemplates\\Production\\devMainTemplate.json"
    },
    "mainTemplateFilePath_Staging": {
      "value": ".\\DatabricksTemplates\\Production\\stagingMainTemplate.json"
    },
    "mainTemplateFilePath_Prod": {
      "value": ".\\DatabricksTemplates\\Production\\prodMainTemplate.json"
    },
    "keyVaultEndpoint": {
      "value": "https://__ClusterName____KeyVaultNameSuffix__.vault.azure.net/"
    },
    "databricksNetworkIntentPoliciesFilePath_Dev": {
      "value": ".\\DatabricksTemplates\\Production\\devDatabricksNIPMappings.json"
    },
    "databricksNetworkIntentPoliciesFilePath_Staging": {
      "value": ".\\DatabricksTemplates\\Production\\stagingDatabricksNIPMappings.json"
    },
    "databricksNetworkIntentPoliciesFilePath_Prod": {
      "value": ".\\DatabricksTemplates\\Production\\prodDatabricksNIPMappings.json"
    },
    "databricksOutboundNetworkEndpointsFilePath": {
      "value": ".\\DatabricksTemplates\\Production\\__ShortRegionName__\\outboundnetworkendpoints.json"
    },
    "databricksOutboundNetworkEndpointsFilePath_NPIP": {
      "value": ".\\DatabricksTemplates\\Production\\__ShortRegionName__\\outboundnetworkendpointsnpip.json"
    },
    "applicationPort": {
      "value": "11709"
    },
    "primaryJobsDataStorageAccountName": {
      "value": "__PrimaryJobsDataStorageAccountName__"
    },
    "secondaryJobsDataStorageAccountName": {
      "value": "__SecondaryJobsDataStorageAccountName__"
    },
    "workspaceDataStorageAccountName": {
      "value": "__WorkspaceDataStorageAccountName__"
    },
    "jobDefinitionsTableName": {
      "value": "prdsjobdefinitions2"
    },
    "jobTriggersQueuePrefix": {
      "value": "prdsjobtriggers2"
    },
    "region": {
      "value": "__TRIMMED_REGION_NAME__"
    },
    "kustoClusterUri": {
      "value": "https://azuredatabrickscpv2.westus2.kusto.windows.net"
  }
  },
  "secrets": [
    {
      "targetReference": "None",
      "certificates": [
        {
          "name": "AADCert",
          "contentReference": "https://__ClusterName____KeyVaultNameSuffix__.vault.azure.net/secrets/__AADCertName__"
        },
        {
          "name": "SSLCert",
          "contentReference": "https://__ClusterName____KeyVaultNameSuffix__.vault.azure.net/secrets/__SSLEncryptionCertName__"
        }
      ]
    }
  ]
}