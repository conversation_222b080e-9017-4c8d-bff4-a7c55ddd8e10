{"$schema": "https://schema.management.azure.com/schemas/2019-04-01/deploymentParameters.json#", "contentVersion": "*******", "parameters": {"clusterName": {"value": "__ClusterName____ClusterNameSuffix__"}, "adminPassword": {"reference": {"keyVault": {"id": "/subscriptions/__SubscriptionId__/resourceGroups/__ClusterName__/providers/Microsoft.KeyVault/vaults/__ClusterName____KeyVaultNameSuffix__"}, "secretName": "vmss-adminpassword"}}, "vmImageSku": {"value": "__VMImageSku__"}, "userAssignedIdentityResourceId": {"value": "/subscriptions/__SubscriptionId__/resourceGroups/__ClusterName__/providers/Microsoft.ManagedIdentity/userAssignedIdentities/__ShortRegionName__-servicefabric-msi"}, "appPorts": {"value": [11707]}, "zonalResiliency": {"value": "__ZonalResiliency__"}, "buildVersion": {"value": "__BuildVersion__"}, "monitoringGCSAccount": {"value": "__GCSAccount__"}, "monitoringGCSNamespace": {"value": "__GCSNamespace__"}, "monitoringGCSEnvironment": {"value": "__GCSEnv__"}, "monitoringGCSRegion": {"value": "__Region_Friendly_Name__"}, "monitoringConfigVersion": {"value": "__MonitoringConfigVersion__"}, "aadCertEndpoint": {"value": "https://__ClusterName____KeyVaultNameSuffix__.vault.azure.net/secrets/__AADCertName__"}, "genevaCertEndpoint": {"value": "https://__ClusterName____KeyVaultNameSuffix__.vault.azure.net/secrets/__GenevaCertName__"}, "sslCertEndpoint": {"value": "https://__ClusterName____KeyVaultNameSuffix__.vault.azure.net/secrets/__SSLEncryptionCertName__"}, "sfAdminCertSubjectName": {"value": "SFAdminCert.SubjectSimpleName"}, "aadCertSubjectName": {"value": "AADCert.SubjectSimpleName"}, "genevaCertSubjectName": {"value": "GenevaCert.SubjectSimpleName"}, "sslEncryptionCertSubjectName": {"value": "SSLEncryptionCert.SubjectSimpleName"}, "updateSFMC": {"value": "__UPDATE_SFMC__"}, "tenantName": {"value": "__Tenant<PERSON>ame__"}, "serviceInstanceIdTag": {"value": "/providers/Microsoft.Management/ServiceInstances/90b3f8fb-6a44-4ae3-9705-01da5c4ab7d5_adbrp_$_ServiceInstanceIdTag_$"}, "vmStartupScriptPath": {"value": "https://__ShortRegionName____DSC_SA_SUFFIX__.blob.core.windows.net/__DSC_BLOBCONTAINER__/Startup-__BuildVersion__.zip"}, "vmCSEScriptPath": {"value": "https://__ShortRegionName____DSC_SA_SUFFIX__.blob.core.windows.net/__DSC_BLOBCONTAINER__/CSE-__BuildVersion__.ps1"}}, "secrets": [{"targetReference": "None", "certificates": [{"name": "SFAdminCert", "contentReference": "https://__ClusterName____KeyVaultNameSuffix__.vault.azure.net/secrets/__SFAdminCertName__"}, {"name": "AADCert", "contentReference": "https://__ClusterName____KeyVaultNameSuffix__.vault.azure.net/secrets/__AADCertName__"}, {"name": "SSLEncryptionCert", "contentReference": "https://__ClusterName____KeyVaultNameSuffix__.vault.azure.net/secrets/__SSLEncryptionCertName__"}, {"name": "GenevaCert", "contentReference": "https://__ClusterName____KeyVaultNameSuffix__.vault.azure.net/secrets/__GenevaCertName__"}]}]}