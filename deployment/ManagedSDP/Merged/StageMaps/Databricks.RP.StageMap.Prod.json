{"Name": "Databricks.RP.StageMap.Prod", "Version": "1.0.0", "Configuration": {"EnableStampIsolation": false, "RegionConcurrency": {"DisablePairedRegions": true, "MaxParallelCount": 20, "InheritPromotion": false}, "Promotion": {"Manual": false, "Timeout": "P3D"}}, "Stages": [{"Sequence": 1, "Name": "Canary", "Regions": ["EastUS2EUAP"]}, {"Sequence": 2, "Name": "NonCustomer", "Regions": ["AustraliaCentral2"]}, {"Sequence": 3, "Name": "Pilot", "Regions": ["WestCentralUS", "EastAsia"]}, {"Sequence": 4, "Name": "Medium", "Regions": ["UKSouth"]}, {"Sequence": 5, "Name": "High", "Regions": ["EastUS"]}, {"Sequence": 6, "Name": "RowA", "Regions": ["MexicoCentral", "AustraliaEast", "AustraliaCentral", "BrazilSouth", "CanadaCentral", "NorthEurope", "GermanyWestCentral", "CentralIndia", "WestIndia", "JapanEast", "KoreaCentral", "WestUS", "EastUS2", "NorthCentralUS", "SouthAfricaNorth", "SwedenCentral", "SwitzerlandWest", "QatarCentral"]}, {"Sequence": 7, "Name": "RowB", "Regions": ["SouthEastAsia", "AustraliaSouthEast", "SouthCentralUS", "CanadaEast", "WestEurope", "FranceCentral", "SouthIndia", "JapanWest", "NorwayEast", "CentralUS", "WestUS2", "WestUS3", "SwitzerlandNorth", "UKWest", "UAENorth"]}]}