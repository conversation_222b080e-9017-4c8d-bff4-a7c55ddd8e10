{"Name": "Databricks.RP.StageMap.Mooncake", "Version": "1.0.0", "Configuration": {"EnableStampIsolation": false, "RegionConcurrency": {"DisablePairedRegions": true, "MaxParallelCount": 3, "InheritPromotion": false}, "Promotion": {"Manual": false, "Timeout": "P3D"}}, "Stages": [{"Sequence": 1, "Name": "FirstRegion", "Regions": ["chinanorth2"]}, {"Sequence": 2, "Name": "SecondRegion", "Regions": ["chinaeast2"]}, {"Sequence": 3, "Name": "ThirdRegion", "Regions": ["chinanorth3"]}, {"Sequence": 4, "Name": "FourthRegion", "Regions": ["chinaeast3"]}, {"Sequence": 5, "Name": "Rest", "Regions": ["REMAINING_REGIONS"]}]}