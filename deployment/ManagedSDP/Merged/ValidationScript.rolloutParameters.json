{"$schema": "https://ev2schema.azure.net/schemas/2020-01-01/rolloutParameters.json", "contentVersion": "*******", "shellExtensions": [{"name": "ValidationScripts", "type": "ValidationScripts", "properties": {"maxExecutionTime": "PT30M"}, "package": {"reference": {"path": "Ev2DeploymentPackage.tar"}}, "launch": {"command": ["/bin/bash", "-c", "pwsh -file ValidationScripts.ps1 -runCrudWorkspaceTest \"true\" -location \"__TRIMMED_REGION_NAME__\" -cloudEnvironment __Environment__"], "identity": {"type": "UserAssigned", "userAssignedIdentities": ["/subscriptions/__ShellExtensionSubscriptionId__/resourceGroups/adbrp_global/providers/Microsoft.ManagedIdentity/userAssignedIdentities/databricks-region-rollout-msi"]}}}]}