{"$schema": "https://ev2schema.azure.net/schemas/2020-01-01/rolloutParameters.json", "contentVersion": "*******", "shellExtensions": [{"name": "SetupRegionRollout", "type": "SetupRegionRollout", "properties": {"maxExecutionTime": "PT50M"}, "package": {"reference": {"path": "Ev2DeploymentPackage.tar"}}, "launch": {"command": ["/bin/bash", "-c", "pwsh -file DeployRegion.ps1 -createResources \"__CREATE_RESOURCES__\" -location \"__TRIMMED_REGION_NAME__\" -tenantName \"__TENANT_NAME__\""], "identity": {"type": "UserAssigned", "userAssignedIdentities": ["/subscriptions/__SubscriptionId__/resourceGroups/adbrp_global/providers/Microsoft.ManagedIdentity/userAssignedIdentities/databricks-region-rollout-msi"]}}}]}