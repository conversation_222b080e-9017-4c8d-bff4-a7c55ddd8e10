{"$schema": "https://ev2schema.azure.net/schemas/2020-01-01/rolloutParameters.json", "contentVersion": "1.0.0.0", "MdmHealthCheckParameters": {"MonitoringAccountName": "__MonitoringAccountName__", "WaitBeforeMonitorTimeInMinutes": "__BakeTimeInMinutes__", "MonitorTimeInMinutes": "__MonitorTimeInMinutes__", "MdmHealthCheckEndPoint": "__MdmHealthCheckEndPoint__", "HealthResources": [{"Name": "HealthCheck", "ResourceType": "Region", "Dimensions": {"Region": "__TRIMMED_REGION_NAME__"}}]}}