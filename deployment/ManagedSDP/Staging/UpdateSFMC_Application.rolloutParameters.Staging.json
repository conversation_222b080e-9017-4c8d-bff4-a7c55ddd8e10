﻿{
  "$schema": "https://schema.management.azure.com/schemas/2019-04-01/deploymentParameters.json#",
  "contentVersion": "1.0.0.0",
  "paths": [
    "appPackageUrl"
  ],
  "parameters": {
    "clusterName": {
      "value": "__ClusterName____ClusterNameSuffix__"
    },
    "applicationTypeName": {
      "value": "Providers.ServiceFabricType"
    },
    "applicationTypeVersion": {
      "value": "__BuildVersion__"
    },
    "appPackageUrl": {
      "value": "bin\\__AppPackageUrl__"
    },
    "applicationName": {
      "value": "Providers.ServiceFabric"
    },
    "aadCertThumbprint": {
      "value": "AADCert.CertificateThumbprint"
    },
    "aadCertSubjectName": {
      "value": "AADCert.SubjectSimpleName"
    },
    "sslCertSubjectName": {
      "value": "SSLCert.SubjectSimpleName"
    },
    "featureServiceName": {
      "value": "Providers.ServiceFabric.FeatureRole"
    },
    "featureServiceTypeName": {
      "value": "Providers.ServiceFabric.FeatureRoleType"
    },
    "featureServiceInstanceCount": {
      "value": 6
    },
    "workerServiceName": {
      "value": "Providers.ServiceFabric.WorkerRole"
    },
    "workerServiceTypeName": {
      "value": "Providers.ServiceFabric.WorkerRoleType"
    },
    "workerServiceInstanceCount": {
      "value": 6
    },
    "jobServiceName": {
      "value": "Providers.ServiceFabric.JobRole"
    },
    "jobServiceTypeName": {
      "value": "Providers.ServiceFabric.JobRoleType"
    },
    "jobServiceInstanceCount": {
      "value": 2
    },
    "roleLocation": {
      "value": "__Region_Friendly_Name__"
    },
    "tenantId": {
      "value": "__TenantId__"
    },
    "mainTemplateFilePath_Dev": {
      "value": ".\\DatabricksTemplates\\Prod\\devMainTemplate.json"
    },
    "mainTemplateFilePath_Staging": {
      "value": ".\\DatabricksTemplates\\Prod\\stagingMainTemplate.json"
    },
    "mainTemplateFilePath_Prod": {
      "value": ".\\DatabricksTemplates\\Prod\\prodMainTemplate.json"
    },
    "keyVaultEndpoint": {
      "value": "https://__ClusterName____KeyVaultNameSuffix____KeyVaultDnsSuffix__/"
    },
    "databricksNetworkIntentPoliciesFilePath_Dev": {
      "value": ".\\DatabricksTemplates\\Prod\\devDatabricksNIPMappings.json"
    },
    "databricksNetworkIntentPoliciesFilePath_Staging": {
      "value": ".\\DatabricksTemplates\\Prod\\stagingDatabricksNIPMappings.json"
    },
    "databricksNetworkIntentPoliciesFilePath_Prod": {
      "value": ".\\DatabricksTemplates\\Prod\\prodDatabricksNIPMappings.json"
    },
    "databricksOutboundNetworkEndpointsFilePath": {
      "value": ".\\DatabricksTemplates\\Prod\\__ShortRegionName__\\outboundnetworkendpoints.json"
    },
    "databricksOutboundNetworkEndpointsFilePath_NPIP": {
      "value": ".\\DatabricksTemplates\\Prod\\__ShortRegionName__\\outboundnetworkendpointsnpip.json"
    },
    "applicationPort": {
      "value": "11707"
    },
    "primaryJobsDataStorageAccountName": {
      "value": "__PrimaryJobsDataStorageAccountName__"
    },
    "secondaryJobsDataStorageAccountName": {
      "value": "__SecondaryJobsDataStorageAccountName__"
    },
    "workspaceDataStorageAccountName": {
      "value": "__WorkspaceDataStorageAccountName__"
    },
    "jobDefinitionsTableName": {
      "value": "prdsjobdefinitions"
    },
    "jobTriggersQueuePrefix": {
      "value": "prdsjobtriggers"
    },
    "region": {
      "value": "__TRIMMED_REGION_NAME__"
    },
    "kustoClusterUri": {
      "value": "__KustoClusterUri__"
    },
    "dscPackageStorageAccountNameSuffix": {
      "value": "__DSCPackageStorageAccountNameSuffix__"
    },
    "argEndpoint": {
      "value": "__ARGEndpoint__"
    },
    "shortRegionName": {
      "value": "__ShortRegionName__"
    }
  },
  "secrets": [
    {
      "targetReference": "None",
      "certificates": [
        {
          "name": "AADCert",
          "contentReference": "https://__ClusterName____KeyVaultNameSuffix____KeyVaultDnsSuffix__/secrets/__AADCertName__"
        },
        {
          "name": "SSLCert",
          "contentReference": "https://__ClusterName____KeyVaultNameSuffix____KeyVaultDnsSuffix__/secrets/__SSLEncryptionCertName__"
        }
      ]
    }
  ]
}