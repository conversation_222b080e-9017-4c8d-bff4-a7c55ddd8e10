﻿{
  "$schema": "https://ev2schema.azure.net/schemas/2020-04-01/RegionAgnosticServiceModel.json",
  "contentVersion": "*******",
  "serviceMetadata": {
    "serviceIdentifier": "90b3f8fb-6a44-4ae3-9705-01da5c4ab7d5",
    "serviceGroup": "Microsoft.Azure.Databricks.RP",
    "displayName": "ResourceProvider",
    "environment": "Production",
    "tenantId": "72f988bf-86f1-41af-91ab-2d7cd011db47"
  },
  "serviceResourceGroupDefinitions": [
    {
      "name": "SetupRegionRollout",
      "subscriptionKey": "$config(SubscriptionKey)",
      "azureResourceGroupName": "adbrpshell-SetupRegionRolloutwestus2",
      "serviceResourceDefinitions": [
        {
          "name": "Microsoft.AzureDatabricks.DatabricksRP.Shell.SetupRegionRollout",
          "composedOf": {
            "Extension": {
              "Shell": [
                {
                  "type": "SetupRegionRollout",
                  "properties": {
                    "imageName": "adm-mariner-20-l",
                    "imageVersion": "v6",
                    "cpu": "2",
                    "memoryInGB": "4.0"
                  }
                }
              ],
              "RolloutParametersPath": "ManagedSDP/Staging/DatabricksRP.RegionRollout.RolloutParameters.json"
            }
          },
          "scopeTags": [
            {
              "name": "general-bindings"
            },
            {
              "name": "regional-subscription"
            },
            {
              "name": "regional-trimmed-name"
            },
            {
              "name": "regional-region-short-name"
            },
            {
              "name": "regional-dsc-fileshare"
            },
            {
              "name": "tenant-name"
            }
          ]
        }
      ]
    },
    {
      "name": "UploadDSCPackage",
      "subscriptionKey": "$config(SubscriptionKey)",
      "azureResourceGroupName": "$config(uploadDSCPackageAzureResourceGroupName)",
      "serviceResourceDefinitions": [
        {
          "name": "Microsoft.AzureDatabricks.DatabricksRP.Shell.UploadDSCPackage",
          "composedOf": {
            "Extension": {
              "Shell": [
                {
                  "type": "UploadDSCPackage",
                  "properties": {
                    "imageName": "adm-mariner-20-l",
                    "imageVersion": "v6",
                    "cpu": "2",
                    "memoryInGB": "4.0"
                  }
                }
              ],
              "RolloutParametersPath": "ManagedSDP\\Staging\\UploadDSCPackage.rolloutParameters.Staging.json"
            }
          },
          "scopeTags": [
            {
              "name": "general-bindings"
            },
            {
              "name": "regional-subscription"
            },
            {
              "name": "regional-region-short-name"
            },
            {
              "name": "regional-dsc-fileshare"
            },
            {
              "name": "regional-cluster-name"
            }
          ]
        }
      ]
    },
    {
      "name": "UpdateSFMC",
      "subscriptionKey": "$config(SubscriptionKey)",
      "azureResourceGroupName": "adbrp$config(ShortRegionName)",
      "serviceResourceDefinitions": [
        {
          "name": "Microsoft.AzureDatabricks.DatabricksRP.SFMC.VMSS",
          "composedOf": {
            "arm": {
              "templatePath": "Templates\\DatabricksRP.SFMC.VMSS.Template.json",
              "parametersPath": "ManagedSDP\\Staging\\UpdateSFMC_VMSS.rolloutParameters.Staging.json"
            }
          },
          "scopeTags": [
            {
              "name": "general-bindings"
            },
            {
              "name": "env-geneva-bindings"
            },
            {
              "name": "regional-subscription"
            },
            {
              "name": "regional-cluster-name"
            },
            {
              "name": "regional-region-short-name"
            },
            {
              "name": "regional-region-friendly-name"
            },
            {
              "name": "sfmc-bindings"
            },
            {
              "name": "regional-dsc-fileshare"
            }
          ]
        },
        {
          "name": "Microsoft.AzureDatabricks.DatabricksRP.SFMC.Application",
          "composedOf": {
            "arm": {
              "templatePath": "Templates\\DatabricksRP.SFMC.Application.Template.json",
              "parametersPath": "ManagedSDP\\Staging\\UpdateSFMC_Application.rolloutParameters.Staging.json"
            }
          },
          "scopeTags": [
            {
              "name": "regional-cluster-name"
            },
            {
              "name": "regional-region-short-name"
            },
            {
              "name": "regional-region-friendly-name"
            },
            {
              "name": "regional-trimmed-name"
            },
            {
              "name": "general-bindings"
            },
            {
              "name": "sfmc-bindings"
            }
          ]
        }
      ]
    }
  ]
}