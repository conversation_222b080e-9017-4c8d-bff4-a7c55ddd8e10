﻿{
  "$schema": "https://ev2schema.azure.net/schemas/2020-04-01/RegionAgnosticServiceModel.json",
  "contentVersion": "*******",
  "serviceMetadata": {
    "serviceIdentifier": "90b3f8fb-6a44-4ae3-9705-01da5c4ab7d5",
    "serviceGroup": "Microsoft.Azure.Databricks.RP.Staging",
    "displayName": "ResourceProvider",
    "environment": "$config(environment)",
    "tenantId": "$config(tenantId)"
  },
  "serviceResourceGroupDefinitions": [
    {
      "name": "UploadDSCPackage",
      "subscriptionKey": "AzureDatabricks-RP-Staging",
      "azureResourceGroupName": "$config(uploadDSCPackageAzureResourceGroupName)",
      "serviceResourceDefinitions": [
        {
          "name": "Microsoft.AzureDatabricks.DatabricksRP.Shell.UploadDSCPackage",
          "composedOf": {
            "Extension": {
              "Shell": [
                {
                  "type": "UploadDSCPackage",
                  "properties": {
                    "imageName": "adm-mariner-20-l",
                    "imageVersion": "v6",
                    "cpu": "2",
                    "memoryInGB": "4.0"
                  }
                }
              ],
              "RolloutParametersPath": "ManagedSDP\\Staging\\UploadDSCPackage.rolloutParameters.Staging.json"
            }
          },
          "scopeTags": [
            {
              "name": "general-bindings"
            },
            {
              "name": "regional-subscription"
            },
            {
              "name": "regional-region-short-name"
            },
            {
              "name": "regional-dsc-fileshare"
            }
          ]
        }
      ]
    },
    {
      "name": "UpdateSFMC",
      "subscriptionKey": "AzureDatabricks-RP-Staging",
      "azureResourceGroupName": "adbrp$config(ShortRegionName)",
      "serviceResourceDefinitions": [
        {
          "name": "Microsoft.AzureDatabricks.DatabricksRP.SFMC.VMSS",
          "composedOf": {
            "arm": {
              "templatePath": "Templates\\DatabricksRP.SFMC.VMSS.Template.json",
              "parametersPath": "ManagedSDP\\Staging\\UpdateSFMC_VMSS.rolloutParameters.Staging.json"
            }
          },
          "scopeTags": [
            {
              "name": "general-bindings"
            },
            {
              "name": "env-geneva-bindings"
            },
            {
              "name": "regional-subscription"
            },
            {
              "name": "regional-cluster-name"
            },
            {
              "name": "regional-region-short-name"
            },
            {
              "name": "regional-region-friendly-name"
            },
            {
              "name": "sfmc-bindings"
            },
            {
              "name": "regional-dsc-fileshare"
            }
          ]
        },
        {
          "name": "Microsoft.AzureDatabricks.DatabricksRP.SFMC.Application",
          "composedOf": {
            "arm": {
              "templatePath": "Templates\\DatabricksRP.SFMC.Application.Template.json",
              "parametersPath": "ManagedSDP\\Staging\\UpdateSFMC_Application.rolloutParameters.Staging.json"
            }
          },
          "scopeTags": [
            {
              "name": "regional-cluster-name"
            },
            {
              "name": "regional-region-friendly-name"
            },
            {
              "name": "regional-trimmed-name"
            },
            {
              "name": "general-bindings"
            },
            {
              "name": "sfmc-bindings"
            }
          ]
        }
      ]
    }
  ]
}