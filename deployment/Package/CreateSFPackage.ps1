
param(
    [Parameter(Mandatory = $true)]
    $PackageLocation,
    [Parameter(Mandatory = $true)]
    $BuildLocalPath
)

function CreateSFPackage {
    param(
        [Parameter(Mandatory = $true)][string]$Environment,
        [Parameter(Mandatory = $true)][string]$BinDir
    )

    # In YAML pipeline, to make the rollout files work for all cloud, using $rolloutInfra() to reuse configurations.
    # Other dynamic configuration is not allowed in this case, $rolloutInfra is the only usable variable.
    # However $rolloutInfra only maps to Prod, not Production.  So we need to rename the package.
    if ($Environment -eq "Production") {
        $dirName = "Prod"
        $destinationFileName = "DatabricksRP-Prod.zip"
        $newFileName = "DatabricksRP-Prod.sfpkg"
    } else {
        $dirName = $Environment
        $destinationFileName = "DatabricksRP-$Environment.zip"
        $newFileName = "DatabricksRP-$Environment.sfpkg"
    }

    $destinationPath = "$BinDir\$dirName\$destinationFileName"

    Compress-Archive -Path "$PackageLocation\$Environment\*" -DestinationPath $destinationPath
    Rename-Item -Path $destinationPath -NewName $newFileName
} 

$binDir = "$BuildLocalPath\ServiceGroupRoot\bin"

CreateSFPackage -Environment "Production" -BinDir $binDir
CreateSFPackage -Environment "Fairfax" -BinDir $binDir
CreateSFPackage -Environment "Mooncake" -BinDir $binDir
CreateSFPackage -Environment "Staging" -BinDir $binDir