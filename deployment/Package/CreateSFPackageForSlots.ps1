param(
    [Parameter(Mandatory = $true)]
    $PackageLocation,
    [Parameter(Mandatory=$true)]
    $BuildVersion,
    [Parameter(Mandatory = $true, HelpMessage = "Local path of the Build on the Build Agent")] 
    [string]$BuildLocalPath
)

function CreateSFPackage {
    param(
        [Parameter(Mandatory = $true)][string]$Environment,
        [Parameter(Mandatory = $true)][string]$EnvironmentSlotName,
        [Parameter(Mandatory = $true)][string]$RegionSlotName
    )

    if ((Test-Path  "$serviceGroupRootDir\bin\$Environment") -eq $false) {
        New-Item -ItemType directory -Path "$serviceGroupRootDir\bin" -Name $Environment 
        Write-Host "Created $serviceGroupRootDir\bin\$Environment"
    }

    Compress-Archive -path "$PackageLocation\$EnvironmentSlotName\*" -DestinationPath "$serviceGroupRootDir\bin\$Environment\DatabricksRP-$EnvironmentSlotName.zip"
    Rename-Item -Path "$serviceGroupRootDir\bin\$Environment\DatabricksRP-$EnvironmentSlotName.zip" -NewName "DatabricksRP-$RegionSlotName.sfpkg"
} 

#Update Service manifest/CodePackage/ConfigPackage versions in service manifest file
function UpdateServiceManifests() {
    param(
        [Parameter(Mandatory = $true)][string]$Environment
    )
    
    $applicationManifest = "$($PackageLocation)\\$($Environment)\\ApplicationManifest.xml"
    $applicationManifestXml = (Select-Xml -Path $applicationManifest -XPath /).Node
    $serviceManifestNames = $applicationManifestXml.ApplicationManifest.ServiceManifestImport.ServiceManifestRef.ServiceManifestName

    foreach ($item in $serviceManifestNames) {
        $serviceManifest = "$($PackageLocation)\\$($Environment)\\$($item)\\ServiceManifest.xml"
        $serviceManifestXml = (Select-Xml -Path $serviceManifest -XPath /).Node
        $ServiceManifestXml.ServiceManifest.Version = $BuildVersion
        $serviceManifestXml.ServiceManifest.CodePackage.Version = $BuildVersion
        $serviceManifestXml.ServiceManifest.ConfigPackage.Version = $BuildVersion
        $serviceManifestXml.Save($serviceManifest)
    }
}

#Update ApplicationTypeVersion/ServiceManifestVersion versions in Application manifest file
function UpdateApplicationManifest() {
    param(
        [Parameter(Mandatory = $true)][string]$Environment,
        [Parameter(Mandatory = $true)][string]$ApplicationTypeName
    )

    $applicationManifest = "$($PackageLocation)\\$($Environment)\\ApplicationManifest.xml"
    $applicationManifestXml = (Select-Xml -Path $applicationManifest -XPath /).Node
    $applicationManifestXml.ApplicationManifest.ApplicationTypeName = $ApplicationTypeName
    $applicationManifestXml.ApplicationManifest.ApplicationTypeVersion = $BuildVersion
    $applicationManifestXml.ApplicationManifest.ServiceManifestImport.ServiceManifestRef | ForEach-Object { $_.ServiceManifestVersion = $BuildVersion }
    $applicationManifestXml.Save($applicationManifest)
}

# Function to get slot name for canary. 
function GetEnvSlotName {
    Param(
        [string]$jsonPath
    )

    if ((Test-Path $jsonPath) -eq $false) {
        throw "No environment details json found in path $jsonPath"
    }

    $detailsContent = Get-Content -Raw -Path $jsonPath

    $json = Get-Content -Raw -Path $jsonPath | ConvertFrom-Json

    $slotDetails = @()
    $json | ForEach-Object {
        $environment = New-Object System.Object
        $environment | Add-Member -MemberType NoteProperty -Name "Region" -Value "$($_.TenantName)$($_.Region)"

        $_.Slots | ForEach-Object {
            if ( $_.DefaultSlot -ne "yes") {
                $slotDetails += "$($environment.Region)-$($_.SlotId)"
            }
        }
    }
    return $slotDetails
}

# Function to get App type name for canary. 
function GetAppTypeSlotName {
    Param(
        [string]$jsonPath
    )

    if ((Test-Path $jsonPath) -eq $false) {
        throw "No environment details json found in path $jsonPath"
    }

    $detailsContent = Get-Content -Raw -Path $jsonPath

    $json = Get-Content -Raw -Path $jsonPath | ConvertFrom-Json

    $slotDetails = @()
    $json | ForEach-Object {
        $environment = New-Object System.Object
        $environment | Add-Member -MemberType NoteProperty -Name "Region" -Value $_.Region

        $_.Slots | ForEach-Object {
            if ( $_.DefaultSlot -ne "yes") {
                $slotDetails += "Providers.ServiceFabricType.$($_.SlotId)"
            }
        }
    }
    return $slotDetails
}

function CopyTemplateFiles {
    Param(
        [string]$FileName
    )

    $templatePath = "$BuildLocalPath\Templates\$FileName"
    Copy-Item $templatePath -Destination "$serviceGroupRootDir\Templates"
    Write-Host "Copied $FileName to $serviceGroupRootDir\Templates `n"
}

function CreateBuildVer {
    Param(
        [string]$BuildLocalPath,
        [string]$BuildVersion
    )

    $BuildVerPath = "$serviceGroupRootDir\bin\buildver.txt"
    if ((Test-Path $BuildVerPath) -eq $false) {
        New-Item -ItemType "file" -Path $BuildVerPath
        Write-Host "The buildver.txt doesn't exist and hence created"
    }

    Set-Content -Path $BuildVerPath -Value $BuildVersion

    Write-Host "Buildver.txt created with the build number $BuildVersion"  -ForegroundColor Green
}

$serviceGroupRootDir = ".\deployment\ExpressV2\ServiceGroupRoot"

$environmentsDetailsJsonPath = "$BuildLocalPath\DeploymentData\EnvironmentsDetails.json"

$EnvironmentSlotName = (GetEnvSlotName -jsonPath "$environmentsDetailsJsonPath")
$ApplicationTypeName = (GetAppTypeSlotName -jsonPath "$environmentsDetailsJsonPath")

if ((Test-Path  "$serviceGroupRootDir\bin") -eq $false) {
    New-Item -ItemType directory -Path $serviceGroupRootDir -Name "bin"   
    Write-Host "Created $serviceGroupRootDir\bin"
}

if ((Test-Path  "$serviceGroupRootDir\Templates") -eq $false) {
    New-Item -ItemType directory -Path $serviceGroupRootDir -Name "Templates"   
    Write-Host "Created $serviceGroupRootDir\Templates"
}

CreateBuildVer -BuildLocalPath $BuildLocalPath -BuildVersion $BuildVersion

CopyTemplateFiles -FileName "DatabricksRP.SFMC.VMSS.Template.json"
CopyTemplateFiles -FileName "DatabricksRP.SFMC.Application.Template.json"

for ($i = 0; $i -lt $EnvironmentSlotName.Count; $i++) {
    Write-Output "Updating version in ($EnvironmentSlotName[$i]) Application Manifest...with $BuildVersion"
    UpdateApplicationManifest -Environment $EnvironmentSlotName[$i] -ApplicationTypeName $ApplicationTypeName[$i]
    Write-Output "Updating version in ($EnvironmentSlotName[$i]) Service Manifest... with $BuildVersion"
    UpdateServiceManifests -Environment $EnvironmentSlotName[$i]
    CreateSFPackage -Environment "Production" -EnvironmentSlotName $EnvironmentSlotName[$i] -RegionSlotName $EnvironmentSlotName[$i]
}