
param(
    [Parameter(Mandatory = $true, HelpMessage = "Local path of the Build on the Build Agent")] 
    [string]$ServiceGroupRootDir,
    [Parameter(Mandatory = $true)]
    $PackageLocation,
    [Parameter(Mandatory = $true)]
    $BuildVersion
)

if ((Test-Path  "$ServiceGroupRootDir\DatabricksEV2") -eq $false) {
    New-Item -ItemType directory -Path $ServiceGroupRootDir -Name "DatabricksEV2"
    Write-Host "Created DatabricksEV2 folder"
}

Compress-Archive -Path "$PackageLocation\GenevaMonitoringAgent.45.6.1\*" -DestinationPath "$PackageLocation\GenevaMonitoringAgent.45.6.1.zip"
Remove-Item -Path "$PackageLocation\GenevaMonitoringAgent.45.6.1\*" -Recurse
Remove-Item -Path "$PackageLocation\GenevaMonitoringAgent.45.6.1"

Move-Item -Path "$PackageLocation\CSE.ps1" -Destination "$ServiceGroupRootDir\DatabricksEV2\CSE-$BuildVersion.ps1"
Move-Item -Path "$PackageLocation\EnvironmentsDetails.json" -Destination "$ServiceGroupRootDir\DatabricksEV2\"
Compress-Archive -Path "$PackageLocation\*" -DestinationPath "$ServiceGroupRootDir\DatabricksEV2\Startup-$BuildVersion.zip"