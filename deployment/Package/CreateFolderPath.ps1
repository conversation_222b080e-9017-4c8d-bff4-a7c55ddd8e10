
param(
    [Parameter(Mandatory = $true)]
    $BuildVersion,
    [Parameter(Mandatory = $true)]
    $BuildLocalPath
)

function CreateBuildVer {
    Param(
        [string]$BuildLocalPath,
        [string]$BuildVersion
    )

    $BuildVerPath = "$serviceGroupRootDir\bin\buildver.txt"
    if ((Test-Path $BuildVerPath) -eq $false) {
        New-Item -ItemType "file" -Path $BuildVerPath
        Write-Host "The buildver.txt doesn't exist and hence created"
    }

    Set-Content -Path $BuildVerPath -Value $BuildVersion

    Write-Host "Buildver.txt created with the build number $BuildVersion"  -ForegroundColor Green
}

function CreateBinSubDir {
    param(
        [Parameter(Mandatory = $true)][string]$Environment
    )

    # In YAML pipeline, to make the rollout files work for all cloud, using $rolloutInfra() to reuse configurations.
    # Other dynamic configuration is not allowed in this case, $rolloutInfra is the only usable variable.
    # However $rolloutInfra only maps to Prod, not Production.  So we need to rename the package.
    if ($Environment -eq "Production") {
        $dirName = "Prod"
    } else {
        $dirName = $Environment
    }

    $destinationDir = "$binDir\$dirName"

    if ((Test-Path  $destinationDir) -eq $false) {
        New-Item -ItemType directory -Path $binDir -Name $dirName
        Write-Host "Created $destinationDir"
    }
} 

function CopyTemplateFile {
    param(
        [Parameter(Mandatory = $true)][string]$FileName
    )

    $templatePath = "$BuildLocalPath\Templates\$FileName"  
    if ((Test-Path $templatePath) -eq $false) {
        throw "File $templatePath is not found"
    }

    Copy-Item $templatePath -Destination "$serviceGroupRootDir\Templates"
    Write-Host "Copied $FileName to $serviceGroupRootDir\Templates `n"
}

$serviceGroupRootDir = "$BuildLocalPath\ServiceGroupRoot" 
$binDir = "$serviceGroupRootDir\bin"
$templatesDir = "$serviceGroupRootDir\Templates"

if ((Test-Path  $binDir) -eq $false) {
    New-Item -ItemType directory -Path $serviceGroupRootDir -Name "bin"   
    Write-Host "Created $binDir"
}

if ((Test-Path  $templatesDir) -eq $false) {
    New-Item -ItemType directory -Path $serviceGroupRootDir -Name "Templates"
    Write-Host "Created $templatesDir"
}

CreateBuildVer -BuildLocalPath $BuildLocalPath -BuildVersion $BuildVersion

CreateBinSubDir -Environment "Production"
CreateBinSubDir -Environment "Fairfax"
CreateBinSubDir -Environment "Mooncake"
CreateBinSubDir -Environment "Staging"

CopyTemplateFile -FileName "DatabricksRP.SFMC.VMSS.Template.json"
CopyTemplateFile -FileName "DatabricksRP.SFMC.Application.Template.json"
