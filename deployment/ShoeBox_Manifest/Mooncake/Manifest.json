{"incidentManagement": {"incidentRoutingService": "Azure Databricks", "incidentRoutingTeam": "Azure Databricks RP"}, "manifestOwners": {"claims": ["Databricks-PlatformServiceAdministrator"], "reviewerAlias": ["sanwar", "n<PERSON><PERSON>", "subraman", "scott<PERSON><PERSON>a", "sabose", "blairch", "<PERSON><PERSON><PERSON><PERSON>", "xuchen", "v-v<PERSON><PERSON><PERSON>"]}, "serviceTreeId": "90b3f8fb-6a44-4ae3-9705-01da5c4ab7d5", "displayName": "Microsoft.Databricks/workspaces", "name": "Microsoft.Databricks/workspaces", "getResourceApiVersion": "2023-02-01", "metrics": {"globalInfo": {"serviceIdentity": "Microsoft.Databricks", "version": "1.0", "enableRegionalMdmAccount": false, "sourceMdmAccount": "AzureDatabricksRPHot_MC", "sourceMdmNamespace": "SLIMetrics", "metricsEnabled": true}, "metricSpecifications": [{"name": "WorkspaceProvisioning", "displayName": "Workspace Provisioning", "displayDescription": "The count of successful and failed workspace create and delete operations", "internalMetricName": "LongRunningResponseLatency", "category": "Availability", "unit": "Count", "aggregationType": "Count", "afecFlags": ["Microsoft.Databricks/EnableAvailabilityMetric"], "supportedAggregationTypes": ["Count"], "resourceIdDimensionNameOverride": "CustomerResourceId", "dimensions": [{"name": "Operation", "displayName": "Operation", "internalName": "Operation"}, {"name": "Region", "displayName": "Region", "internalName": "LocationId"}, {"name": "Status", "displayName": "Status", "internalName": "Status"}]}]}, "logs": {"globalInfo": {"serviceIdentity": "DatabrickAuditLogs", "version": "1.0", "logFilterPathSelector": "sku.name", "logsEnabled": true}, "logCategories": [{"name": "dbfs", "displayName": "Databricks File System", "logFilterPattern": "(P|p)remium"}, {"name": "clusters", "displayName": "Databricks Clusters", "logFilterPattern": "(P|p)remium"}, {"name": "accounts", "displayName": "Databricks Accounts", "logFilterPattern": "(P|p)remium"}, {"name": "jobs", "displayName": "Databricks Jobs", "logFilterPattern": "(P|p)remium"}, {"name": "notebook", "displayName": "Databricks Notebook", "logFilterPattern": "(P|p)remium"}, {"name": "ssh", "displayName": "Databricks SSH", "logFilterPattern": "(P|p)remium"}, {"name": "workspace", "displayName": "Databricks Workspace", "logFilterPattern": "(P|p)remium"}, {"name": "secrets", "displayName": "Databricks Secrets", "logFilterPattern": "(P|p)remium"}, {"name": "sqlPermissions", "displayName": "Databricks SQLPermissions", "logFilterPattern": "(P|p)remium"}, {"name": "instancePools", "displayName": "Instance Pools", "logFilterPattern": "(P|p)remium"}, {"name": "sqlanalytics", "displayName": "Databricks SQL Analytics", "logFilterPattern": "(P|p)remium"}, {"name": "genie", "displayName": "Databricks Genie", "logFilterPattern": "(P|p)remium"}, {"name": "globalInitScripts", "displayName": "Databricks Global Init Scripts", "logFilterPattern": "(P|p)remium"}, {"name": "iamRole", "displayName": "Databricks IAM Role", "logFilterPattern": "(P|p)remium"}, {"name": "mlflowExperiment", "displayName": "Databricks MLFlow Experiment", "logFilterPattern": "(P|p)remium"}, {"name": "featureStore", "displayName": "Databricks Feature Store", "logFilterPattern": "(P|p)remium"}, {"name": "RemoteHistoryService", "displayName": "Databricks Remote History Service", "logFilterPattern": "(P|p)remium"}, {"name": "mlflowAcledArtifact", "displayName": "Databricks MLFlow Acled Artifact", "logFilterPattern": "(P|p)remium"}, {"name": "databrickssql", "displayName": "Databricks DatabricksSQL", "logFilterPattern": "(P|p)remium"}, {"name": "deltaPipelines", "displayName": "Databricks Delta Pipelines", "logFilterPattern": "(P|p)remium"}, {"name": "modelRegistry", "displayName": "Databricks Model Registry", "logFilterPattern": "(P|p)remium"}, {"name": "repos", "displayName": "Databricks Repos", "logFilterPattern": "(P|p)remium"}, {"name": "unityCatalog", "displayName": "Databricks Unity Catalog", "logFilterPattern": "(P|p)remium"}, {"name": "gitCredentials", "displayName": "Databricks Git Credentials", "logFilterPattern": "(P|p)remium"}, {"name": "webTerminal", "displayName": "Databricks Web Terminal", "logFilterPattern": "(P|p)remium"}, {"name": "serverlessRealTimeInference", "displayName": "Databricks Serverless Real-Time Inference", "logFilterPattern": "(P|p)remium"}, {"name": "clusterLibraries", "displayName": "Databricks Cluster Libraries", "logFilterPattern": "(P|p)remium"}, {"name": "partnerHub", "displayName": "Databricks Partner Hub", "logFilterPattern": "(P|p)remium"}, {"name": "clamAVScan", "displayName": "Databricks Clam AV Scan", "logFilterPattern": "(P|p)remium"}, {"name": "capsule8Dataplane", "displayName": "Databricks Capsule8 Container Security Scanning Reports", "logFilterPattern": "(P|p)remium"}, {"name": "BrickStoreHttpGateway", "displayName": "Databricks Brick Store HttpGateway", "logFilterPattern": "(P|p)remium"}, {"name": "Dashboards", "displayName": "Databricks Dashboards", "logFilterPattern": "(P|p)remium"}, {"name": "CloudStorageMetadata", "displayName": "Databricks Cloud Storage Metadata", "logFilterPattern": "(P|p)remium"}, {"name": "PredictiveOptimization", "displayName": "Databricks Predictive Optimization", "logFilterPattern": "(P|p)remium"}, {"name": "DataMonitoring", "displayName": "Databricks Data Monitoring", "logFilterPattern": "(P|p)remium"}, {"name": "Ingestion", "displayName": "Databricks Ingestion", "logFilterPattern": "(P|p)remium"}, {"name": "MarketplaceConsumer", "displayName": "Databricks Marketplace Consumer", "logFilterPattern": "(P|p)remium"}, {"name": "LineageTracking", "displayName": "Databricks Lineage Tracking", "logFilterPattern": "(P|p)remium"}, {"name": "Apps", "displayName": "Databricks Lakehouse Apps", "logFilterPattern": "(P|p)remium"}, {"name": "ClusterPolicies", "displayName": "Databricks Cluster Policies", "logFilterPattern": "(P|p)remium"}, {"name": "DataRooms", "displayName": "Databricks Data Rooms", "logFilterPattern": "(P|p)remium"}, {"name": "Groups", "displayName": "Databricks Groups", "logFilterPattern": "(P|p)remium"}, {"name": "MarketplaceProvider", "displayName": "Databricks Marketplace Provider", "logFilterPattern": "(P|p)remium"}, {"name": "OnlineTables", "displayName": "Databricks Online Tables", "logFilterPattern": "(P|p)remium"}, {"name": "RBAC", "displayName": "Databricks Role Based Access Control", "logFilterPattern": "(P|p)remium"}, {"name": "RFA", "displayName": "Databricks Request For Access Events", "logFilterPattern": "(P|p)remium"}, {"name": "VectorSearch", "displayName": "Databricks Vector Search", "logFilterPattern": "(P|p)remium"}, {"name": "WebhookNotifications", "displayName": "Databricks Webhook Notifications", "logFilterPattern": "(P|p)remium"}, {"name": "WorkspaceFiles", "displayName": "Databricks Workspace Files", "logFilterPattern": "(P|p)remium"}, {"name": "Files", "displayName": "Databricks Files", "logFilterPattern": "(P|p)remium"}, {"name": "LakeviewConfig", "displayName": "Databricks Lakeview configuration", "logFilterPattern": "(P|p)remium"}, {"name": "BudgetPolicyCentral", "displayName": "Databricks Budget Policy Central", "logFilterPattern": "(P|p)remium"}, {"name": "Filesystem", "displayName": "Databricks Filesystem Logs", "logFilterPattern": "(P|p)remium"}]}}