param(
    [Parameter(Mandatory = $true)][string]$runCrudWorkspaceTest,
    [Parameter(Mandatory = $true)][string]$location,
    [Parameter(Mandatory = $true)][string]$cloudEnvironment
)
# script 
# 1. auth to the DataBricks RP using MSI
# 2. validate if a set of common features are working - get workspace, for example - minimalistic check since there is an extensive health check after this.
# 3. create workspace test

#Added wait time to avoid race condition in ACI creation 
#https://ev2docs.azure.net/features/extensibility/shell/troubleshooting.html#running-into-connect-azaccount--connection-refused-error-or-fail-to-connect-to-azure-error
Start-Sleep -s 5


$resourceGroupName = "ev2healthcheck-rg-${location}"
$workspaceName = "ev2healthcheck-ws"
$accessConnectorName = "ev2healthcheck-ac"
$maxAttempts = 5
$sleepInSeconds = 10

$separator = "------------------------------------------------------------------------------------------------------------------------------------------------"

function Connect-AzureCloud {
    $tap = 1
    $loggedIn = $false

    if ($cloudEnvironment -eq "Production" -or $cloudEnvironment -eq "Staging") {
        $azcloud = "AzureCloud"
    }
    elseif ($cloudEnvironment -eq "Fairfax") {
        $azcloud = "AzureUSGovernment"
    }
    elseif ($cloudEnvironment -eq "Mooncake") {
        $azcloud = "AzureChinaCloud"
    }
    else {
        Write-Error -Message "Unsupported environment '$($cloudEnvironment)'" `
            -ErrorAction Stop
    }

    Write-Host $separator

    Write-Host "Attempting to login '$($azcloud)' ($($subscriptionId)) using MSI."

    do {
        try {
            az cloud set --name $azcloud
            az login --identity

            if ($LASTEXITCODE -ne 0) {
                Start-Sleep -Seconds ($sleepInSeconds * $tap)
                -ErrorAction "Stop"
            }
            else {
                $loggedIn = $true
                Write-Host "Login to '$($azcloud)' successfully."
            }        
        }
        catch {
            Write-Warning "Login to '$($azcloud)' failed with error '$($_.Exception)', retrying..."
        }
    } while (!$loggedIn `
            -and $tap++ -lt $maxAttempts)

    if (!$loggedIn) {
        Write-Error -Message "Maximum attempts to login exceeded. Login failed." `
            -ErrorAction Stop
    }
}

function Install-ADBExtension {
    $maxTries = 10
    $count = 1
    $extInstalled = $false;

    Write-Output "Attempting installation of Azure databricks extension."

    do {
        Invoke-Expression -Command "az extension add --name databricks" -ErrorVariable errVariable -ErrorAction SilentlyContinue
        sleep -Milliseconds 10
        #No error means extension installed
        if ($errVariable.Count -eq 0) {
            $extInstalled = $true
            Write-Output "Successfully added extension: databricks."
        }
        else {
            Write-Host $errVariable
        }
    } while (($extInstalled = $false) -and ($count++ -lt $maxTries))
  
}

#Initiate health check
function Initiate-HealthCheck {

    az config set extension.use_dynamic_install=yes_without_prompt

    #create Resource Group
    az group create --location $location --name $resourceGroupName

    if ($cloudEnvironment -eq "Fairfax") {
        $acCreateCheck = $true
        Write-Output "Skipping Access Connector creation for $cloudEnvironment"
    }
    else {
        #create Access connector
        $newAc = az databricks access-connector create --resource-group $resourceGroupName --name $accessConnectorName --location $location --identity-type SystemAssigned | ConvertFrom-Json

        #Checking provisioningstate of AC
        if ($newAc.properties.provisioningState -eq "Succeeded") {
            $acCreateCheck = $true
            Write-Output "Creation of Access Connector successful: $accessConnectorName"
            Write-Host "Successfully created the Access Connector : $newAc"
        }
        else {
            $acCreateCheck = $false
            Write-Output "Creation of Access Connector failed: $accessConnectorName"
        }
    }

    #create workspace
    $newWs = az databricks workspace create --resource-group $resourceGroupName --name $workspaceName --location $location --sku standard
    $getWs = az databricks workspace show --name $workspaceName --resource-group $resourceGroupName | ConvertFrom-Json
    Start-Sleep -s 10

    #Checking provisioningstate of workspace
    if ($getWs.provisioningState -eq "Succeeded") {
        $wsCreateCheck = $true
        Write-Output "Creation of workspace successful: $workspaceName"
        Write-Host "Successfully retrieved the workspace : $getWs"
    }
    else {
        $wsCreateCheck = $false
        Write-Output "Creation of workspace failed: $workspaceName"
    }

    #delete workspace
    $delws = az databricks workspace delete --resource-group $resourceGroupName --name $workspaceName -y

    $getDelWs = az databricks workspace list --resource-group $resourceGroupName

    #checking if workspace is deleted
    if ($getDelWs.name -eq $workspaceName) {
        $WsDelCheck = $false
        Write-Output "Deletion of workspace failed: $workspaceName"
    }
    else {
        $WsDelCheck = $true
        Write-Output "Deletion of workspace successful: $workspaceName"
    }

    if ($cloudEnvironment -eq "Fairfax") {
        $AcDelCheck = $true
        Write-Output "Skipping Access Connector deletion for $cloudEnvironment"
    }
    else {
        #delete Access Connector
        $delAc = az databricks access-connector delete --resource-group $resourceGroupName --name $accessConnectorName

        $getAc = az databricks access-connector list --resource-group $resourceGroupName

        #Checking if Access Connector is deleted
        if ($getAc.name -eq $accessConnectorName) {
            $AcDelCheck = $false
            Write-Output "Deletion of Access Connector failed: $accessConnectorName"
        }
        else {
            $AcDelCheck = $true
            Write-Output "Deletion of Access Connector successful: $accessConnectorName"
        }
    }

    #delete Resource Group
    az group delete --name $resourceGroupName --yes

    #check if RG exists
    $delRG = az group exists --name $resourceGroupName

    if ($delRG -eq $false) {
        Write-Output "Successfully deleted the Resource Group: $resourceGroupName"
    }
    else {
        Write-Output "Deletion of Resource Group failed: $resourceGroupName"
    }

    #Checking if all operations are successfully executed to confirm health check
    if ($acCreateCheck -eq $true -and $wsCreateCheck -eq $true -and $AcDelCheck -eq $true -and $WsDelCheck -eq $true) {
        Write-Output "Health check test Passed."
    }
    else {
        Write-Output "Health check test Failed."
        exit 1
    }
}

function DeleteRGIfExists {

    Install-ADBExtension
    Connect-AzureCloud

    #Check existing ResourceGroup
    $checkExRG = az group exists --name $resourceGroupName

    # check whether RG is existing. If yes, then delete the existing RG.

    if ($checkExRG -eq $false) {
        Write-Output "$resourceGroupName does not exist"
    }
    else {
        Write-Output "$resourceGroupName still exists, thus deleting"
        #delete existing RG
        az group delete --name $resourceGroupName --yes
        #Checking if existing RG got deleted
        $delExRG = az group exists --name $resourceGroupName
        if ($delExRG -eq $false) {
            Write-Output "$resourceGroupName deleted successfully"
        }
        else {
            Write-Output "Failed to delete existing RG: $resourceGroupName"
            exit 1
        }
    }
}

if ($runCrudWorkspaceTest -eq $true) {
    DeleteRGIfExists

    if ($location -ne "CentralUSEUAP" -and $location -ne "USGovTexas") {
        #Initiate Health Check
        Initiate-HealthCheck
    }
    else {
        Write-Host "Skipping Health Check for location: $($location)"
    }
}
else {
    Write-Host "Skipping Get Workspace test for location: $($location)"
}

exit $LASTEXITCODE