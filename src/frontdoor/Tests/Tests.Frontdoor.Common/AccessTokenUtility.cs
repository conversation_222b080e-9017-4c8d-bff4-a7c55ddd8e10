﻿//-----------------------------------------------------------
// Copyright (c) Microsoft Corporation.  All rights reserved.
//-----------------------------------------------------------

namespace Internal.WindowsAzure.ResourceStack.Tests.Frontdoor.Common
{
    using System;
    using System.Collections.Generic;
    using System.Linq;
    using System.Security.Cryptography.X509Certificates;
    using Microsoft.Identity.Client;
    using Microsoft.Identity.Web;
    using Microsoft.WindowsAzure.ResourceStack.Common.Extensions;
    using Microsoft.WindowsAzure.ResourceStack.Common.Services;
    using Microsoft.WindowsAzure.ResourceStack.Frontdoor.Data.Entities.Authentication;
    using SystemClaims = System.Security.Claims;
    using SystemIdentity = System.IdentityModel.Tokens;
    
    /// <summary>
    /// This class gets the access token from user using prompt.
    /// </summary>
    public class AccessTokenUtility
    {
        #region Constants.

        /// <summary>
        /// The configuration setting to get the access token signing thumbprint.
        /// </summary>
        private static readonly string AccessTokenSigningThumbPrint = CloudConfigurationManager.GetConfiguration(
                settingName: "Microsoft.WindowsAzure.ResourceStack.Frontdoor.AuthenticationCacheProvider.DevelopmentTokenSigningThumbprint");

        /// <summary>
        /// The configuration setting to get the service client thumbprint.
        /// </summary>
        private static readonly string ServiceClientThumbPrint = CloudConfigurationManager.GetConfiguration(
                settingName: "Microsoft.WindowsAzure.ResourceStack.Frontdoor.ServiceClient.PrimaryCertificateThumbprint");

        /// <summary>
        /// The AAD PPE authority for getting the token.
        /// </summary>
        private static readonly string DogFoodAuthority = "https://login.windows-ppe.net/common";

        /// <summary>
        /// The AAD PPE issuer template.
        /// </summary>
        private static readonly string AadIssuerTemplate = "https://sts.windows-ppe.net/{0}/";

        #endregion

        #region Public properties.

        /// <summary>
        /// Gets the authentication result with RDFE audience.
        /// </summary>
        public static AuthenticationResult RdfeAudienceAuthenticationResult
        {
            get
            {
                return AccessTokenUtility.GetAccessToken("https://management.core.windows.net/");
            }
        }

        /// <summary>
        /// Gets the authentication result with CSM audience.
        /// </summary>
        public static AuthenticationResult CsmAudienceAuthenticationResult
        {
            get
            {
                return AccessTokenUtility.GetAccessToken("https://management.azure.com/");
            }
        }

        /// <summary>
        /// Get the signed access token from the claims principal.
        /// </summary>
        /// <param name="claimsPrincipal">The claims principal.</param>
        public static string SignClaimsPrincpal(SystemClaims.ClaimsPrincipal claimsPrincipal)
        {
            var signingCredentials = new SystemIdentity.X509SigningCredentials(certificate: StoreLocation.LocalMachine.FindCertificateByThumbprint(thumbprint: AccessTokenUtility.AccessTokenSigningThumbPrint));
            var signatureProvider = new SystemIdentity.SignatureProviderFactory().CreateForSigning(key: signingCredentials.SigningKey, algorithm: signingCredentials.SignatureAlgorithm);
            var requestIdentity = claimsPrincipal.ToRequestCorrelationContextIdentity();
            return new SystemIdentity.JwtSecurityTokenHandler()
                .CreateToken(
                    issuer: requestIdentity.Issuer,
                    audience: requestIdentity.Audience,
                    subject: new SystemClaims.ClaimsIdentity(claimsPrincipal.Identity),
                    signingCredentials: signingCredentials, 
                    signatureProvider: signatureProvider,
                    expires: requestIdentity.ExpirationTime)
                .RawData;
        }

        /// <summary>
        /// Validate and get the claims from signed token.
        /// </summary>
        /// <param name="token">The token.</param>
        /// <param name="tenantId">The tenant ID.</param>
        public static Dictionary<string, string> GetClaimsFromSignedToken(string token, string tenantId)
        {
            var allowedAudiences = new string[] { "https://management.core.windows.net/", "https://management.azure.com/" };
            var signingToken = new SystemIdentity.X509SecurityToken(certificate: StoreLocation.LocalMachine.FindCertificateByThumbprint(thumbprint: AccessTokenUtility.ServiceClientThumbPrint));
            var validationParameters = new SystemIdentity.TokenValidationParameters
            {
                ValidAudiences = allowedAudiences,
                IssuerSigningTokens = signingToken.AsList(),
                ValidateIssuer = true,
                ValidIssuers = string.Format(format: AccessTokenUtility.AadIssuerTemplate, arg0: tenantId).AsList(),
            };

            var tokenHandler = new SystemIdentity.JwtSecurityTokenHandler();
            SystemIdentity.SecurityToken validatedSecurityToken = null;
            var claimsPrincipal = tokenHandler.ValidateToken(
                securityToken: token,
                validationParameters: validationParameters,
                validatedToken: out validatedSecurityToken);

            return claimsPrincipal.Claims.CoalesceEnumerable()
                .GroupBy(claim => claim.Type)
                .ToDictionary(claim => claim.Key, group => group.Select(claim => claim.Value).ConcatStrings(","));
        }

        #endregion

        #region Private helper methods.

        /// <summary>
        /// Gets the authentication result for given audience.
        /// </summary>
        /// <param name="audience">The audience.</param>
        private static async Task<AuthenticationResult> GetAccessTokenAsync(string audience)
        {
            var app = ConfidentialClientApplicationBuilder
                .Create("1950a258-227b-4e31-a9cf-717495945fc2")
                .WithRedirectUri("urn:ietf:wg:oauth:2.0:oob")
                .WithAuthority(new Uri(AccessTokenUtility.DogFoodAuthority))
                .Build();

            var result = await app.AcquireTokenForClient(new[] { audience }).ExecuteAsync();
            return result;
        }

        #endregion
    }
}