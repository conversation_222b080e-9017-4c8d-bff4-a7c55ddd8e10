﻿<?xml version="1.0" encoding="utf-8"?>
<packages>
  <package id="Antlr4" version="4.6.4" targetFramework="net451" developmentDependency="true" />
  <package id="Antlr4.CodeGenerator" version="4.6.4" targetFramework="net451" developmentDependency="true" />
  <package id="Antlr4.Runtime" version="4.6.4" targetFramework="net451" />
  <package id="DatabricksFrontdoorDependencies" version="1.21.928.327" targetFramework="net451" />
  <package id="DsmsCredentialsManagement" version="4.3.183377" targetFramework="net451" />
  <package id="DstsAuthentication.master" version="********" targetFramework="net451" />
  <package id="Microsoft.AspNet.WebApi.Client" version="5.2.3" targetFramework="net451" />
  <package id="Microsoft.AspNet.WebApi.Core" version="5.2.3" targetFramework="net451" />
  <package id="Microsoft.AspNet.WebApi.WebHost" version="5.2.3" targetFramework="net451" />
  <package id="Microsoft.Azure.KeyVault.Core" version="1.0.0" targetFramework="net451" />
  <package id="Microsoft.Bcl" version="1.1.10" targetFramework="net451" />
  <package id="Microsoft.Bcl.Build" version="1.0.14" targetFramework="net451" />
  <package id="Microsoft.Data.Edm" version="5.8.4" targetFramework="net451" />
  <package id="Microsoft.Data.OData" version="5.8.4" targetFramework="net451" />
  <package id="Microsoft.Data.Services.Client" version="5.8.4" targetFramework="net451" />
  <package id="Microsoft.IdentityModel" version="6.1.7600.16394" targetFramework="net451" />
  <package id="Microsoft.IdentityModel.Authorization.DataModel" version="5.0.5667" targetFramework="net451" />
  <package id=" Microsoft.Identity.Web" version="" targetFramework="net451" />
  <package id=" Microsoft.Identity.Client" version="" targetFramework="net451" />
  <package id="Microsoft.Net.Http" version="2.2.29" targetFramework="net451" />
  <package id="Microsoft.TeamFoundation.DistributedTask.Common" version="15.112.1" targetFramework="net451" />
  <package id="Microsoft.TeamFoundationServer.Client" version="15.112.1" targetFramework="net451" />
  <package id="Microsoft.Tpl.Dataflow" version="4.5.24" targetFramework="net451" />
  <package id="Microsoft.VisualStudio.Services.Client" version="15.112.1" targetFramework="net451" />
  <package id="Microsoft.WindowsAzure.ConfigurationManager" version="3.1.0" targetFramework="net451" />
  <package id="MSTest.TestFramework" version="2.2.5" targetFramework="net451" />
  <package id="Newtonsoft.Json" version="13.0.2" targetFramework="net472" />
  <package id="ResourceStack.Storage.Client" version="6.0.0.900" targetFramework="net451" />
  <package id="System.IdentityModel.Tokens.Jwt" version="4.0.2.206221351" targetFramework="net451" />
  <package id="System.Spatial" version="5.8.4" targetFramework="net451" />
  <package id="WindowsAzure.Storage" version="8.3.0" targetFramework="net451" />
</packages>