﻿//-----------------------------------------------------------
// Copyright (c) Microsoft Corporation.  All rights reserved.
//-----------------------------------------------------------

namespace Internal.WindowsAzure.ResourceStack.Tests.Frontdoor.Common
{
    using System;
    using System.Collections.Generic;
    using System.Linq;
    using Microsoft.WindowsAzure.ResourceStack.Common.Extensions;
    using Microsoft.WindowsAzure.ResourceStack.Frontdoor.Admin.Definitions;
    using Microsoft.WindowsAzure.ResourceStack.Frontdoor.Data.Entities.Registration;

    /// <summary>
    /// Helper utilities for resource provider unit tests. Notably, these classes provide a clean way to generate test resource provider manifests
    /// and alias manifests using builder pattern.
    /// </summary>
    public static class ResourceProviderUtility
    {
        // Usage notes on the resource provider builder classes
        // ----------------------------------------------------
        // 1. Build the resource provider or alias manifest structure you want using builder class instances
        // 2. Call the Build() method to generate the manifest represented by your builder structure
        //
        // Sample:
        // var manifest = new ResourceProviderUtilities.ResourceProviderManifestBuilder(testResourceProviderNamespace);
        // var resourceType = testResourceProviderRegistration.AddResourceType(testResourceType);
        // var endpoint = resourceType.AddEndpoint(endpointUri: TestEnvironment.ExtensionProviderServiceUri.ToString())
        //
        // Note: you can also build your builder classes declaratively using property initialization syntax or constructors
        // ----------------------------------------------------

        /// <summary>
        /// Test resource provider manifest builder
        /// </summary>
        public class ResourceProviderManifestBuilder
        {
            /// <summary>
            /// Constant valid incident management test data
            /// </summary>
            private static readonly ResourceProviderManagement IncidentManagement = new ResourceProviderManagement
            {
                ManifestOwners = new[] { ResourceProviderTestHostRegistration.TestManifestOwner },
                IncidentRoutingService = "incidentRoutingService",
                IncidentRoutingTeam = "incidentRoutingTeam",
                IncidentContactEmail = "<EMAIL>"
            };

            /// <summary>
            /// Gets the resource provider namespace
            /// </summary>
            public string Namespace { get; }

            /// <summary>
            /// Gets the collection of resource types of the manifest
            /// </summary>
            public IList<ResourceTypeBuilder> ResourceTypes { get; private set; } = new List<ResourceTypeBuilder>();

            /// <summary>
            /// Gets or sets the resource provider management of the manifest
            /// </summary>
            private ResourceProviderManagement Management { get; set; }

            /// <summary>
            /// Gets the collection of resource hydration account of the manifest
            /// </summary>
            private IList<ResourceHydrationAccount> HydrationAccounts { get; } = new List<ResourceHydrationAccount>();

            /// <summary>
            /// Gets the resource manifest from the current state of the builder
            /// </summary>
            public ResourceProviderManifest Build()
            {
                return new ResourceProviderManifest
                {
                    Namespace = this.Namespace,
                    ProviderVersion = "2.0",
                    ProviderType = ResourceProviderType.Internal,
                    ResourceTypes = this.ResourceTypes.Select(resourceType => resourceType.Build()).ToArray(),
                    Management = this.Management,
                    ResourceHydrationAccounts = this.HydrationAccounts.ToArray()
                };
            }

            /// <summary>
            /// Initializes a new instance of the ResourceProviderManifestBuilder class
            /// </summary>
            /// <param name="providerNamespace">Optional provider namespace value for the resource provider manifest</param>
            /// <param name="resourceTypes">Optional list of resource type builders</param>
            public ResourceProviderManifestBuilder(string providerNamespace = null, IEnumerable<ResourceTypeBuilder> resourceTypes = null)
            {
                this.Namespace = providerNamespace ?? TestEnvironment.GetNewName("NS.");
                if (resourceTypes != null)
                {
                    foreach (var resourceType in resourceTypes)
                    {
                        this.AddResourceType(resourceType);
                    }
                }
            }

            /// <summary>
            /// Adds a resource type to the current state of the builder
            /// </summary>
            /// <param name="resourceType">Resource provider to add</param>
            public ResourceProviderManifestBuilder AddResourceType(ResourceTypeBuilder resourceType)
            {
                if (resourceType.Parent != null)
                {
                    throw new InvalidOperationException($"Resource type already added to {resourceType.Parent.Namespace}, must be removed before adding to provider {this.Namespace}");
                }

                resourceType.Parent = this;
                this.ResourceTypes.Add(resourceType);
                return this;
            }

            /// <summary>
            /// Removes the given resource type from the current state of the builder
            /// </summary>
            /// <param name="resourceType">The resource type to remove</param>
            public void RemoveResourceType(ResourceTypeBuilder resourceType)
            {
                this.ResourceTypes.Remove(resourceType);
                resourceType.Parent = null;
            }

            /// <summary>
            /// Sets incident management information of the current state of the builder
            /// </summary>
            /// <param name="resourceProviderManagement">Optional incident management information</param>
            public ResourceProviderManifestBuilder SetIncidentManagement(ResourceProviderManagement resourceProviderManagement = null)
            {
                this.Management = resourceProviderManagement ?? ResourceProviderManifestBuilder.IncidentManagement;
                return this;
            }

            /// <summary>
            /// Sets hydration account information of the current state of the builder
            /// </summary>
            /// <param name="maxChildResourceConsistencyJobLimit">Concurrency job limit to use in hydration account information</param>
            /// <param name="accountName">Optional account name to use in hydration account information</param>
            /// <param name="subscriptionId">Optional subscription id to use in hydration account information</param>
            public ResourceProviderManifestBuilder AddHydrationAccount(int maxChildResourceConsistencyJobLimit, string accountName = null, string subscriptionId = null)
            {
                this.HydrationAccounts.Add(new ResourceHydrationAccount
                {
                    AccountName = accountName ?? "testAccount",
                    SubscriptionId = subscriptionId ?? TestEnvironment.ReserveSubscriptionId(),
                    MaxChildResourceConsistencyJobLimit = maxChildResourceConsistencyJobLimit
                });

                return this;
            }

            /// <summary>
            /// Makes a resource provider manifest registration using the given alias manifest
            /// </summary>
            /// <param name="aliasManifest">The alias manifest</param>
            /// <returns>The resource provider manifest registration</returns>
            public ResourceProviderManifestRegistration GetRegistration(AliasManifestBuilder aliasManifest = null)
            {
                var now = DateTime.Now;
                return new ResourceProviderManifestRegistration
                {
                    IsEnabled = true,
                    ChangedTime = now,
                    CreatedTime = now,
                    Manifest = this.Build(),
                    ResourceTypeAliases = aliasManifest?.Build().ResourceTypeAliases
                };
            }
        }

        /// <summary>
        /// ResourceType builder utility class
        /// </summary>
        public class ResourceTypeBuilder
        {
            /// <summary>
            /// Gets the resource type name
            /// </summary>
            public string Name { get; }

            /// <summary>
            /// Gets the resource type name
            /// </summary>
            public string FullName => $"{this.ProviderNamespace}/{this.Name}";

            /// <summary>
            /// Gets the provider namespace of the resource type
            /// </summary>
            public string ProviderNamespace => this.Parent?.Namespace ?? string.Empty;

            /// <summary>
            /// Gets the collection of endpoints of the resource type
            /// </summary>
            public IList<ResourceProviderEndpoint> Endpoints { get; private set; } = new List<ResourceProviderEndpoint>();

            /// <summary>
            /// Gets the resource provider manifest builder this resource type alias builder is associated with
            /// </summary>
            public ResourceProviderManifestBuilder Parent { get; internal set; }

            /// <summary>
            /// Gets the resource type from the current state of the builder
            /// </summary>
            public ResourceType Build()
            {
                return new ResourceType
                {
                    Name = this.Name,
                    Endpoints = this.Endpoints.ToArray()
                };
            }

            /// <summary>
            /// Initializes a new instance of the ResourceTypeBuilder class
            /// </summary>
            /// <param name="name">Resource type name</param>
            /// <param name="endpoints">Optional collection of endpoints</param>
            public ResourceTypeBuilder(string name = null, IEnumerable<ResourceProviderEndpoint> endpoints = null)
            {
                this.Name = name ?? TestEnvironment.GetNewName("RT-");
                if (endpoints != null)
                {
                    this.Endpoints = endpoints.ToList();
                }
            }

            /// <summary>
            /// Initializes a new instance of the ResourceTypeBuilder class by cloning the given ResourceTypeBuilder
            /// </summary>
            /// <param name="resourceTypeBuilder">Resource type builder to clone</param>
            private ResourceTypeBuilder(ResourceTypeBuilder resourceTypeBuilder)
            {
                this.Name = resourceTypeBuilder.Name;
                this.Endpoints = resourceTypeBuilder.Endpoints;
            }

            /// <summary>
            /// Make a clone
            /// </summary>
            public ResourceTypeBuilder Clone()
            {
                return new ResourceTypeBuilder(this);
            }

            /// <summary>
            /// Adds an endpoint to the current state of the resource type builder
            /// </summary>
            /// <param name="endpointUri">Endpoint address string</param>
            /// <param name="apiVersions">Optional collection of API versions</param>
            /// <param name="locations">Optional collection of locations</param>
            /// <param name="requiredFeatures">Optional set of required features</param>
            /// <returns>The newly added endpoint</returns>
            public ResourceProviderEndpoint AddEndpoint(string endpointUri = null, IEnumerable<string> apiVersions = null, IEnumerable<string> locations = null, IEnumerable<string> requiredFeatures = null)
            {
                var endpoint = new ResourceProviderEndpoint
                {
                    ApiVersions = apiVersions == null ? new[] { TestEnvironment.DefaultApiVersion } : apiVersions.ToArray(),
                    Locations = locations == null ? new[] { ResourceProviderTestHostRegistration.TestLocation } : locations.ToArray(),
                    RequiredFeatures = requiredFeatures.CoalesceEnumerable().ToArray(),
                    Timeout = TimeSpan.FromMinutes(1),
                    Enabled = true,
                    EndpointUri = endpointUri ?? "https://localhost"
                };

                this.Endpoints.Add(endpoint);
                return endpoint;
            }
        }

        /// <summary>
        /// Alias manifest builder utility class
        /// </summary>
        public class AliasManifestBuilder
        {
            /// <summary>
            /// Gets the namespace
            /// </summary>
            public string Namespace { get; }

            /// <summary>
            /// Gets the collection of resource type aliases
            /// </summary>
            public IList<ResourceTypeAliasBuilder> ResourceTypeAliases { get; private set; } = new List<ResourceTypeAliasBuilder>();

            /// <summary>
            /// Initializes a new instance of the <see cref="AliasManifestBuilder"/> class
            /// </summary>
            /// <param name="namespace">Namespace of the new alias manifest</param>
            /// <param name="resourceTypeAliases">The resource Type Aliases.</param>
            public AliasManifestBuilder(string @namespace = null, IEnumerable<ResourceTypeAliasBuilder> resourceTypeAliases = null)
            {
                this.Namespace = @namespace ?? TestEnvironment.GetNewName("NS.");
                if (resourceTypeAliases != null)
                {
                    foreach (var resourceTypeAlias in resourceTypeAliases)
                    {
                        this.AddResourceTypeAlias(resourceTypeAlias);
                    }
                }
            }

            /// <summary>
            /// Gets the alias manifest from the current state of the builder
            /// </summary>
            public ResourceProviderAliasManifest Build()
            {
                return new ResourceProviderAliasManifest
                {
                    Namespace = this.Namespace,
                    ResourceTypeAliases = this.ResourceTypeAliases.Select(resourceTypeAlias => resourceTypeAlias.Build()).ToArray()
                };
            }

            /// <summary>
            /// Adds a resource type alias to the current state of the builder
            /// </summary>
            /// <param name="resourceTypeAlias">The resource type alias to add</param>
            public AliasManifestBuilder AddResourceTypeAlias(ResourceTypeAliasBuilder resourceTypeAlias)
            {
                if (resourceTypeAlias.Parent != null)
                {
                    throw new InvalidOperationException($"Resource type alias already added to {resourceTypeAlias.Parent.Namespace}, must be removed before adding to provider {this.Namespace}");
                }

                resourceTypeAlias.Parent = this;
                this.ResourceTypeAliases.Add(resourceTypeAlias);
                return this;
            }

            /// <summary>
            /// Removes a resource type alias from the current state of the builder
            /// </summary>
            /// <param name="resourceTypeAlias">The resource type alias to remove</param>
            public void RemoveResourceTypeAlias(ResourceTypeAliasBuilder resourceTypeAlias)
            {
                resourceTypeAlias.Parent = null;
                this.ResourceTypeAliases.Remove(resourceTypeAlias);
            }
        }

        /// <summary>
        /// Resource type alias builder utility class
        /// </summary>
        public class ResourceTypeAliasBuilder
        {
            /// <summary>
            /// Gets or sets the resource type (name)
            /// </summary>
            public string ResourceType { get; set; }

            /// <summary>
            /// Gets the collection of aliases
            /// </summary>
            public IList<AliasBuilder> Aliases { get; private set; } = new List<AliasBuilder>();

            /// <summary>
            /// Gets the resource type builder this resource type alias builder is associated with
            /// </summary>
            public AliasManifestBuilder Parent { get; internal set; }

            /// <summary>
            /// Gets the resource type aliases from the current state of the builder
            /// </summary>
            public ResourceTypeAliases Build()
            {
                return new ResourceTypeAliases
                {
                    ResourceType = this.ResourceType,
                    Aliases = this.Aliases.Select(alias => alias.Build()).ToArray()
                };
            }

            /// <summary>
            /// Gets the provider namespace
            /// </summary>
            public string ProviderNamespace => this.Parent?.Namespace ?? string.Empty;

            /// <summary>
            /// Initializes a new instance of the <see cref="ResourceTypeAliasBuilder"/> class
            /// </summary>
            /// <param name="resourceType">resource type name</param>
            /// <param name="aliases">optional collection of aliases</param>
            public ResourceTypeAliasBuilder(string resourceType, IEnumerable<AliasBuilder> aliases = null)
            {
                this.ResourceType = resourceType ?? TestEnvironment.GetNewName("RTA-");
                if (aliases != null)
                {
                    foreach (var alias in aliases)
                    {
                        this.AddAlias(alias);
                    }
                }
            }

            /// <summary>
            /// Adds an alias to the current state of the builder class
            /// </summary>
            /// <param name="alias">Alias to add</param>
            public void AddAlias(AliasBuilder alias)
            {
                if (alias.Parent != null)
                {
                    throw new InvalidOperationException($"Resource type alias already added to {alias.Parent.ResourceType}, must be removed before adding to {this.ResourceType}");
                }

                alias.Parent = this;
                this.Aliases.Add(alias);
            }

            /// <summary>
            /// Replace existing collection of aliases with the given one
            /// </summary>
            /// <param name="alias">new collection of aliases</param>
            public void RemoveAlias(AliasBuilder alias)
            {
                alias.Parent = null;
                this.Aliases.Remove(alias);
            }
        }

        /// <summary>
        /// Alias builder utility class
        /// </summary>
        public class AliasBuilder
        {
            /// <summary>
            /// Gets the leaf-level name value
            /// </summary>
            public string Name { get; }

            /// <summary>
            /// Gets or sets a value that overrides the default way that naming works
            /// </summary>
            public string NameOverride { get; set; }

            /// <summary>
            /// Gets the provider namespace
            /// </summary>
            public string ProviderNamespace => this.Parent?.ProviderNamespace ?? string.Empty;

            /// <summary>
            /// Gets the provider namespace
            /// </summary>
            public string ResourceType => this.NameOverride != null ? string.Empty : this.Parent?.ResourceType ?? string.Empty;

            /// <summary>
            /// Gets the fully qualified name value
            /// </summary>
            public string FullName => this.NameOverride ?? $"{this.ProviderNamespace}/{this.ResourceType}/{this.Name}";

            /// <summary>
            /// Gets or sets the default path value
            /// </summary>
            public string DefaultPath { get; set; }

            /// <summary>
            /// Gets or sets the default pattern value
            /// </summary>
            public AliasPattern DefaultPattern { get; set; }

            /// <summary>
            /// Gets the Paths value
            /// </summary>
            public IList<AliasPath> Paths { get; private set; } = new List<AliasPath>();

            /// <summary>
            /// Gets the resource type builder this alias builder is associated with
            /// </summary>
            public ResourceTypeAliasBuilder Parent { get; internal set; }

            /// <summary>
            /// Gets the resource type aliases from the current state of the builder
            /// </summary>
            public Alias Build()
            {
                return new Alias
                {
                    Name = this.FullName,
                    DefaultPath = this.DefaultPath,
                    Paths = this.Paths.ToArray(),
                    DefaultPattern = this.DefaultPattern
                };
            }

            /// <summary>
            /// Initializes a new instance of the <see cref="AliasBuilder"/> class
            /// </summary>
            /// <param name="aliasName">Name of the alias</param>
            /// <param name="defaultPath">Optional default path of the alias</param>
            /// <param name="defaultPattern">Optional default pattern of the alias</param>
            /// <param name="aliasPaths">Optional collection of alias paths</param>
            public AliasBuilder(string aliasName = null, string defaultPath = null, AliasPattern defaultPattern = null, IEnumerable<AliasPath> aliasPaths = null)
            {
                this.Name = aliasName ?? TestEnvironment.GetNewName("Alias-");
                this.DefaultPath = defaultPath;
                this.DefaultPattern = defaultPattern;
                if (aliasPaths != null)
                {
                    this.Paths = aliasPaths.ToList();
                }
            }

            /// <summary>
            /// Initializes a new instance of the <see cref="AliasBuilder"/> class by cloning an existing alias builder
            /// </summary>
            /// <param name="alias">Alias to initialize a </param>
            private AliasBuilder(AliasBuilder alias)
            {
                this.Name = alias.Name;
                this.NameOverride = alias.NameOverride;
                this.DefaultPath = alias.DefaultPath;
                this.Paths = alias.Paths;
            }

            /// <summary>
            /// Make a clone
            /// </summary>
            /// <returns>Newly cloned copy of the input object</returns>
            public AliasBuilder Clone()
            {
                return new AliasBuilder(this);
            }

            /// <summary>
            /// Adds an alias to the current state of the builder
            /// </summary>
            /// <param name="path">Path of the new alias</param>
            /// <param name="apiVersions">Optional API versions collection of the new path</param>
            /// <param name="aliasPattern">Optional pattern of the new path</param>
            public AliasPath AddAliasPath(string path, IEnumerable<string> apiVersions = null, AliasPattern aliasPattern = null)
            {
                var rv = new AliasPath { Path = path, ApiVersions = apiVersions.CoalesceEnumerable().ToArray(), Pattern = aliasPattern };
                this.Paths.Add(rv);
                return rv;
            }

            /// <summary>
            /// Remove an alias path from the current state of the builder
            /// </summary>
            /// <param name="aliasPath">The alias path to remove</param>
            public void RemoveAliasPath(AliasPath aliasPath)
            {
                this.Paths.Remove(aliasPath);
            }
        }
    }
}
