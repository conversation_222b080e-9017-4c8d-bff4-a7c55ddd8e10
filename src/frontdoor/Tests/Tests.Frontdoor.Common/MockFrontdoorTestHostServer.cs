﻿//-----------------------------------------------------------
// Copyright (c) Microsoft Corporation.  All rights reserved.
//-----------------------------------------------------------

namespace Internal.WindowsAzure.ResourceStack.Tests.Frontdoor.Common
{
    using System;
    using System.Net.Http;
    using System.Web.Http;
    using System.Web.Http.Routing;

    /// <summary>
    /// The mock front door test host server.
    /// </summary>
    public class MockFrontdoorTestHostServer : FrontdoorTestHost.FrontdoorTestHostServer
    {
        /// <summary>
        /// Initializes a new instance of the <see cref="MockFrontdoorTestHostServer"/> class.
        /// </summary>
        /// <param name="serviceUri">The service URI.</param>
        public MockFrontdoorTestHostServer(Uri serviceUri)
            : base(serviceUri, null)
        {
        }

        /// <summary>
        /// Called to configure http configuration.
        /// </summary>
        /// <param name="configuration">The http configuration.</param>
        /// <param name="state">The instance state.</param>
        protected override void OnConfigure(HttpConfiguration configuration, object state)
        {
            configuration.Routes.MapHttpRoute(
                name: "ConflictRoute",
                routeTemplate: "subscriptions/{subscriptionId}/conflictRoute/{resourceProviderNamespace}",
                defaults: new { controller = "ResourceProvider" },
                constraints: new { httpMethod = new HttpMethodConstraint(HttpMethod.Get) });

            base.OnConfigure(configuration, state);
        }
    }
}
