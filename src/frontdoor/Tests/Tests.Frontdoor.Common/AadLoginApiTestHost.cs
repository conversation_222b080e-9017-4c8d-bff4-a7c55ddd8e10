﻿//-----------------------------------------------------------
// Copyright (c) Microsoft Corporation.  All rights reserved.
//-----------------------------------------------------------

namespace Internal.WindowsAzure.ResourceStack.Tests.Frontdoor.Common
{
    using System;
    using System.Collections.Generic;
    using System.Linq;
    using System.Net;
    using System.Net.Http;
    using System.Web.Http;
    using System.Web.Http.Controllers;
    using System.Web.Http.Dependencies;
    using System.Web.Http.Dispatcher;
    using Internal.WindowsAzure.ResourceStack.Tests.Common;
    using Microsoft.WindowsAzure.ResourceStack.Common.Extensions;

    /// <summary>
    /// Delegate used for getting user details.
    /// </summary>
    /// <param name="request">The incoming request.</param>
    /// <param name="tenantId">The tenant id.</param>
    /// <param name="userEmail">The user email.</param>
    public delegate HttpResponseMessage GetUserDetailsDelegate(HttpRequestMessage request, string tenantId, string userEmail);

    /// <summary>
    /// The web service host for the AAD login API.
    /// </summary>
    public class AadLoginApiTestHost : ServiceTestHost<AadLoginApiTestHost.AadLoginApiTestHostServer>, IDisposable
    {
         /// <summary>
        /// Initializes a new instance of the <see cref="AadLoginApiTestHost"/> class.
        /// </summary>
        /// <param name="serviceUri">The service URI.</param>
        public AadLoginApiTestHost(Uri serviceUri)
            : base(serviceUri)
        {
            this.Start();
        }

        /// <summary>
        /// Performs application-defined tasks associated with freeing, releasing, or resetting unmanaged resources.
        /// </summary>
        public void Dispose()
        {
            this.Stop();
        }

        /// <summary>
        /// Restarts this instance.
        /// </summary>
        public void Restart()
        {
            this.Stop();
            this.Start();
        }

        /// <summary>
        /// Gets the state of the test host.
        /// </summary>
        protected override object GetState
        {
            get { return this; }
        }

        /// <summary>
        /// Gets or sets the delegate called when user details are requested.
        /// </summary>
        public GetUserDetailsDelegate OnGetUserDetails { get; set; }

        /// <summary>
        /// Called to configure http configuration.
        /// </summary>
        /// <param name="configuration">The http configuration.</param>
        private void OnConfigure(HttpConfiguration configuration)
        {
            configuration.Routes.MapHttpRoute(
                name: "GetUserDetails",
                routeTemplate: "{TenantId}/userRealm/{UserEmail}",
                defaults: new
                {
                    controller = "AadLoginApi",
                });

            configuration.Services.Replace(typeof(IHttpControllerSelector), new AadLoginApiTestHostControllerSelector(this, configuration));
            configuration.DependencyResolver = new AadLoginApiTestHostDependencyResolver(this, configuration);
        }

        /// <summary>
        /// The AAD login API test host server.
        /// </summary>
        public class AadLoginApiTestHostServer : ServiceTestHostServer
        {
            /// <summary>
            /// Initializes a new instance of the <see cref="AadLoginApiTestHostServer"/> class.
            /// </summary>
            /// <param name="serviceUri">The service URI.</param>
            /// <param name="state">The instance state.</param>
            public AadLoginApiTestHostServer(Uri serviceUri, object state)
                : base(serviceUri, state)
            {
            }

            /// <summary>
            /// Called to configure http configuration.
            /// </summary>
            /// <param name="configuration">The http configuration.</param>
            /// <param name="state">The instance state.</param>
            protected override void OnConfigure(HttpConfiguration configuration, object state)
            {
                (state as AadLoginApiTestHost).OnConfigure(configuration);
            }
        }

        #region AAD login API controller

        /// <summary>
        /// The AAD login API request controller.
        /// </summary>
        public class AadLoginApiController : ApiController
        {
            /// <summary>
            /// The AAD login API test host.
            /// </summary>
            private readonly AadLoginApiTestHost host;

            /// <summary>
            /// Initializes a new instance of the <see cref="AadLoginApiController"/> class.
            /// </summary>
            /// <param name="host">The AAD login API test host.</param>
            public AadLoginApiController(AadLoginApiTestHost host)
            {
                this.host = host;
            }

            /// <summary>
            /// Responds to get user details request.
            /// </summary>
            /// <param name="tenantId">The tenant id.</param>
            /// <param name="userEmail">The user email.</param>
            [HttpGet]
            public HttpResponseMessage GetUserDetails(string tenantId, string userEmail)
            {
                if (this.host.OnGetUserDetails != null)
                {
                    return this.host.OnGetUserDetails(this.Request, tenantId, userEmail);
                }

                return this.Request.CreateResponse(HttpStatusCode.OK);
            }
        }

        #endregion

        #region AadLoginApiTestHostControllerSelector

        /// <summary>
        /// The AAD login API test host controller selector.
        /// </summary>
        private class AadLoginApiTestHostControllerSelector : DefaultHttpControllerSelector
        {
            /// <summary>
            /// The AAD login API test host.
            /// </summary>
            private readonly AadLoginApiTestHost host;

            /// <summary>
            /// The HTTP configuration.
            /// </summary>
            private readonly HttpConfiguration configuration;

            /// <summary>
            /// Initializes a new instance of the <see cref="AadLoginApiTestHostControllerSelector" /> class.
            /// </summary>
            /// <param name="host">The host.</param>
            /// <param name="configuration">The configuration.</param>
            public AadLoginApiTestHostControllerSelector(AadLoginApiTestHost host, HttpConfiguration configuration)
                : base(configuration)
            {
                this.host = host;
                this.configuration = configuration;
            }

            /// <summary>
            /// Selects the controller.
            /// </summary>
            /// <param name="request">The request.</param>
            public override HttpControllerDescriptor SelectController(HttpRequestMessage request)
            {
                var matchedTypes = typeof(AadLoginApiTestHost)
                    .GetNestedTypes()
                    .Where(type => typeof(IHttpController).IsAssignableFrom(type))
                    .ToList();

                var controllerName = this.GetControllerName(request);
                var matchedController = matchedTypes.FirstOrDefault(type => type.Name.EqualsInsensitively(controllerName + "controller"));

                return new HttpControllerDescriptor(this.configuration, controllerName, matchedController);
            }
        }

        #endregion

        #region AAD login api dependency respolver

        /// <summary>
        /// The AAD login API test host dependency resolver.
        /// </summary>
        private class AadLoginApiTestHostDependencyResolver : IDependencyResolver
        {
            /// <summary>
            /// The AAD login API test host.
            /// </summary>
            private readonly AadLoginApiTestHost host;

            /// <summary>
            /// The HTTP configuration.
            /// </summary>
            private readonly HttpConfiguration configuration;

            /// <summary>
            /// Initializes a new instance of the <see cref="AadLoginApiTestHostDependencyResolver" /> class.
            /// </summary>
            /// <param name="host">The host.</param>
            /// <param name="configuration">The configuration.</param>
            public AadLoginApiTestHostDependencyResolver(AadLoginApiTestHost host, HttpConfiguration configuration)
            {
                this.host = host;
                this.configuration = configuration;
            }

            /// <summary>
            /// Begins the scope.
            /// </summary>
            public IDependencyScope BeginScope()
            {
                return this;
            }

            /// <summary>
            /// Gets the service.
            /// </summary>
            /// <param name="serviceType">Type of the service.</param>
            public object GetService(Type serviceType)
            {
                if (typeof(IHttpController).IsAssignableFrom(serviceType))
                {
                    if (typeof(AadLoginApiTestHost).GetNestedTypes().Any(type => type == serviceType))
                    {
                        return Activator.CreateInstance(serviceType, args: new object[] { this.host });
                    }
                }

                return null;
            }

            /// <summary>
            /// Gets the services.
            /// </summary>
            /// <param name="serviceType">Type of the service.</param>
            public IEnumerable<object> GetServices(Type serviceType)
            {
                return Enumerable.Empty<object>();
            }

            /// <summary>
            /// Performs application-defined tasks associated with freeing, releasing, or resetting unmanaged resources.
            /// </summary>
            public void Dispose()
            {
            }
        }

        #endregion
    }
}