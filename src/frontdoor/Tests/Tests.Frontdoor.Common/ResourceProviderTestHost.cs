﻿//-----------------------------------------------------------
// Copyright (c) Microsoft Corporation.  All rights reserved.
//-----------------------------------------------------------

namespace Internal.WindowsAzure.ResourceStack.Tests.Frontdoor.Common
{
    using System;

    /// <summary>
    /// The web service host for the test resource provider.
    /// </summary>
    public class ResourceProviderTestHost : BaseResourceProviderTestHost
    {
        #region Constructor

        /// <summary>
        /// Initializes a new instance of the <see cref="ResourceProviderTestHost"/> class.
        /// </summary>
        /// <param name="serviceUri">The service URI.</param>
        public ResourceProviderTestHost(Uri serviceUri)
            : base(serviceUri)
        {
            this.Start();
        }

        /// <summary>
        /// Gets the state of the get.
        /// </summary>
        protected override object GetState
        {
            get { return this; }
        }

        #endregion
    }
}