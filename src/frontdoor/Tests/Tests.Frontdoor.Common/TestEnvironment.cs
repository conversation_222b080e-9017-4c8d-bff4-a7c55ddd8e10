﻿//------------------------------------------------------------
// Copyright (c) Microsoft Corporation.  All rights reserved.
//------------------------------------------------------------

namespace Internal.WindowsAzure.ResourceStack.Tests.Frontdoor.Common
{
    using System;
    using System.Collections.Generic;
    using System.Linq;
    using System.Net;
    using System.Net.Http;
    using System.Security.Claims;
    using System.Threading.Tasks;
    using System.Web.Http;
    using Internal.WindowsAzure.ResourceStack.Tests.Common;
    using Microsoft.WindowsAzure.ResourceStack.Common.Algorithms;
    using Microsoft.WindowsAzure.ResourceStack.Common.Extensions;
    using Microsoft.WindowsAzure.ResourceStack.Common.Instrumentation;
    using Microsoft.WindowsAzure.ResourceStack.Common.Json;
    using Microsoft.WindowsAzure.ResourceStack.Common.Services;
    using Microsoft.WindowsAzure.ResourceStack.Common.Storage;
    using Microsoft.WindowsAzure.ResourceStack.Common.Utilities;
    using Microsoft.WindowsAzure.ResourceStack.Frontdoor.Common.Constants;
    using Microsoft.WindowsAzure.ResourceStack.Frontdoor.Common.Extensions;
    using Microsoft.WindowsAzure.ResourceStack.Frontdoor.Common.Resources;
    using Microsoft.WindowsAzure.ResourceStack.Frontdoor.Data.Configuration;
    using Microsoft.WindowsAzure.ResourceStack.Frontdoor.Data.DataProviders;
    using Microsoft.WindowsAzure.ResourceStack.Frontdoor.Data.Definitions;
    using Microsoft.WindowsAzure.ResourceStack.Frontdoor.Data.Entities.Registration;
    using Microsoft.WindowsAzure.ResourceStack.Frontdoor.Data.Entities.Schema;
    using Microsoft.WindowsAzure.ResourceStack.Frontdoor.Data.Entities.Sku;
    using Microsoft.WindowsAzure.ResourceStack.Frontdoor.Data.Entities.Subscriptions;
    using Microsoft.WindowsAzure.ResourceStack.Frontdoor.Web;
    using Microsoft.WindowsAzure.ResourceStack.Frontdoor.Web.Common;
    using Microsoft.WindowsAzure.ResourceStack.Frontdoor.Web.Entities;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Common.DataProviders;
    using Microsoft.WindowsAzure.Security.Authentication;
    using Microsoft.WindowsAzure.Storage;
    using Newtonsoft.Json.Linq;
    using Assert = Microsoft.VisualStudio.TestTools.UnitTesting.Assert;

    /// <summary>
    /// This class initializes the test environments for all of the Web CITs.
    /// </summary>
    public class TestEnvironment
    {
        /// <summary>
        /// The test resource provider namespace to be registered and used.
        /// </summary>
        public static readonly string ResourceNamespace = ResourceProviderTestHostRegistration.TestNamespace;

        /// <summary>
        /// The test resource type to be registered and used.
        /// </summary>
        public static readonly string ResourceType = ResourceProviderTestHostRegistration.TestResourceType;

        /// <summary>
        /// The second test resource type to be registered and used.
        /// </summary>
        public static readonly string ResourceTypeTwo = ResourceProviderTestHostRegistration.TestResourceTypeTwo;

        /// <summary>
        /// The test location for the test resource provider namespace / types.
        /// </summary>
        public static readonly string Location = ResourceProviderTestHostRegistration.TestLocation;

        /// <summary>
        /// The front door service URI.
        /// </summary>
        public static readonly Uri FrontdoorServiceUri = new UriBuilder(Uri.UriSchemeHttp, Environment.MachineName, 100).Uri;

        /// <summary>
        /// The resource provider service URI.
        /// </summary>
        public static readonly Uri ResourceProviderServiceUri = new UriBuilder(Uri.UriSchemeHttp, Environment.MachineName, 101).Uri;

        /// <summary>
        /// The resource provider service URI.
        /// </summary>
        public static readonly Uri ResourceProviderServiceUriWithSegments = new UriBuilder(Uri.UriSchemeHttp, Environment.MachineName, 101, "segment1/segment2/").Uri;

        /// <summary>
        /// The secondary resource provider service URI.
        /// </summary>
        public static readonly Uri SecondaryResourceProviderServiceUri = new UriBuilder(Uri.UriSchemeHttp, Environment.MachineName, 102).Uri;

        /// <summary>
        /// The tertiary resource provider service URI.
        /// </summary>
        public static readonly Uri TertiaryResourceProviderServiceUri = new UriBuilder(Uri.UriSchemeHttp, Environment.MachineName, 103).Uri;

        /// <summary>
        /// The fourth resource provider service URI.
        /// </summary>
        public static readonly Uri FourthResourceProviderServiceUri = new UriBuilder(Uri.UriSchemeHttp, Environment.MachineName, 104).Uri;

        /// <summary>
        /// The extension resource provider service URI.
        /// </summary>
        public static readonly Uri ExtensionProviderServiceUri = new UriBuilder(Uri.UriSchemeHttp, Environment.MachineName, 101, "api/extensions/").Uri;

        /// <summary>
        /// The secondary extension resource provider service URI.
        /// </summary>
        public static readonly Uri SecondaryExtensionProviderServiceUri = new UriBuilder(Uri.UriSchemeHttp, Environment.MachineName, 102, "api/extensions/").Uri;

        /// <summary>
        /// The front door service URI.
        /// </summary>
        public static readonly Uri AdminServiceUri = new UriBuilder(Uri.UriSchemeHttp, Environment.MachineName, 1001).Uri;

        /// <summary>
        /// The storage shim service URI.
        /// </summary>
        public static readonly Uri ShimProviderUri = new UriBuilder(Uri.UriSchemeHttp, Environment.MachineName, 1002).Uri;

        /// <summary>
        /// The feature service URI.
        /// </summary>
        public static readonly Uri FeatureProviderUri = new UriBuilder(Uri.UriSchemeHttp, Environment.MachineName, 1003).Uri;

        /// <summary>
        /// The Test provider service URI.
        /// </summary>
        public static readonly Uri TestProviderUri = new UriBuilder(Uri.UriSchemeHttp, Environment.MachineName, 1004).Uri;

        /// <summary>
        /// The managed services service URI.
        /// </summary>
        public static readonly Uri ManagedServicesProviderUri = new UriBuilder(Uri.UriSchemeHttp, Environment.MachineName, 1005).Uri;

        /// <summary>
        /// Uri of the external resource provider.
        /// </summary>
        public static readonly Uri ExternalResourceProviderServiceUri = new UriBuilder(Uri.UriSchemeHttp, Environment.MachineName, 104).Uri;

        /// <summary>
        /// Uri of the graph endpoint.
        /// </summary>
        public static readonly Uri GraphApiEndpointUri = new UriBuilder(Uri.UriSchemeHttp, Environment.MachineName, 222).Uri;

        /// <summary>
        /// Uri of the marketplace service.
        /// </summary>
        /// <remarks>
        /// This port number needs to match the one in the app.config.
        /// </remarks>
        public static readonly Uri MarketplaceServiceUri = new UriBuilder(Uri.UriSchemeHttp, Environment.MachineName, 333).Uri;

        /// <summary>
        /// Uri of the KeyVault service.
        /// </summary>
        /// <remarks>
        /// This URI/port number needs to match the one in the app.config.
        /// </remarks>
        public static readonly Uri KeyVaultServiceUri = new UriBuilder(Uri.UriSchemeHttp, Environment.MachineName, 555).Uri;

        /// <summary>
        /// Uri of the token service.
        /// </summary>
        public static readonly Uri TokenServiceUri = new UriBuilder(Uri.UriSchemeHttp, Environment.MachineName, 666).Uri;

        /// <summary>
        /// Uri of the AAD login API.
        /// </summary>
        public static readonly Uri AadLoginApiServiceUri = new UriBuilder(Uri.UriSchemeHttp, Environment.MachineName, 777).Uri;

        /// <summary>
        /// Uri of the appliance publishing service.
        /// </summary>
        public static readonly Uri AppliancePublishingServiceUri = new UriBuilder(Uri.UriSchemeHttp, Environment.MachineName, 888).Uri;

        /// <summary>
        /// Uri of the visual studio team services service.
        /// </summary>
        public static readonly Uri VstsApiEndpointUri = new UriBuilder(Uri.UriSchemeHttp, Environment.MachineName, 889).Uri;

        /// <summary>
        /// Uri of the API validation service.
        /// </summary>
        public static readonly Uri APIValidationServiceUri = new UriBuilder(Uri.UriSchemeHttp, Environment.MachineName, 999).Uri;

        /// <summary>
        /// Uri of the API validation service.
        /// </summary>
        public static readonly Uri JITServiceUri = new UriBuilder(Uri.UriSchemeHttp, Environment.MachineName, 444).Uri;

        /// <summary>
        /// Uri of the management group API service.
        /// </summary>
        /// <remarks>
        /// This port number needs to match the one in the app.config.
        /// </remarks>
        public static readonly Uri ManagementGroupApiServiceUri = new UriBuilder(Uri.UriSchemeHttp, Environment.MachineName, 977).Uri;

        /// <summary>
        /// Uri of the event grid service.
        /// </summary>
        /// <remarks>
        /// This port number needs to match the one in the app.config.
        /// </remarks>
        public static readonly Uri EventGridServiceUri = new UriBuilder(Uri.UriSchemeHttp, Environment.MachineName, 987).Uri;

        /// <summary>
        /// The resource hydration queue name.
        /// </summary>
        public static readonly string ResourceHydrationQueueName = "resourcehydration";

        /// <summary>
        /// The event grid queue name.
        /// </summary>
        public static readonly string EventGridDataQueueName = "eventgrideventdatainputqueue";

        /// <summary>
        /// The event grid table name.
        /// </summary>
        public static readonly string EventGridDataTableName = "eventgrideventdatainputtable";

        /// <summary>
        /// The linked notification event queue name.
        /// </summary>
        public static readonly string LinkedNotificationQueueName = "linkednotificationqueue";

        /// <summary>
        /// The supported <c>api</c> version.
        /// </summary>
        public static readonly string DefaultApiVersion = "2018-07-01";

        /// <summary>
        /// The supported <c>api</c> version for Register Managed by tenant.
        /// </summary>
        public static readonly string DefaultRegisterManagedByTenantApiVersion = FrontdoorConstants.ApiVersion20180701;

        /// <summary>
        /// The default policy CRUD API version.
        /// </summary>
        public static readonly string DefaultPolicyApiVersion = "2018-05-01";

        /// <summary>
        /// The default managed services CRUD API version.
        /// </summary>
        public static readonly string DefaultManagedServicesApiVersion = "2018-06-01-preview";

        /// <summary>
        /// The data aliases API version.
        /// </summary>
        public static readonly string DataAliasesApiVersion = "2018-06-01-preview";

        /// <summary>
        /// The default User Agent.
        /// </summary>
        public static readonly string DefaultUserAgent = "Windows-RSS-Platform/2.0 (IE 11.0; Windows NT 6.1)";

        /// <summary>
        /// The front door batch API URI.
        /// </summary>
        public static readonly string FrontdoorBatchUri = TestEnvironment.FrontdoorServiceUri + "batch?api-version=" + TestEnvironment.DefaultApiVersion;

        /// <summary>
        /// The DSTS authentication header name.
        /// </summary>
        public static readonly string DstsAuthenticationHeaderName = "WWW-Authenticate-DSTS";

        /// <summary>
        /// The RDFE client role.
        /// </summary>
        public static readonly string RdfeClientRole = "SuperUser";

        /// <summary>
        /// Gets the test storage account connection string.
        /// </summary>
        public static string TestStorageAccountConnectionString
        {
            get
            {
                return CloudConfigurationManager.GetConfiguration(
                    settingName: "encrypted.CloudStorageAccount.ResourceStack.Frontdoor.GlobalDataStorage.devfabric.ConnectionString",
                    defaultValue: "UseDevelopmentStorage=true;");
            }
        }

        /// <summary>
        /// Construct a new random provider name that starts with the optional given root
        /// </summary>
        /// <param name="root">Start of new name</param>
        public static string GetNewName(string root = null)
        {
            return $"{root ?? "SomeName"}{RandomNumber.Next():X8}";
        }

        /// <summary>
        /// Gets the test storage account.
        /// </summary>
        public static CloudStorageAccount GetTestStorageAccount()
        {
            return CloudStorageAccount.Parse(TestEnvironment.TestStorageAccountConnectionString);
        }

        /// <summary>
        /// Gets the test resource hydration account.
        /// </summary>
        /// <remarks>
        /// The <seealso cref="CloudStorageAccount"/> class does not have any methods to retrieve
        /// account name and key, so we have to do some manual parsing.
        /// </remarks>
        public static ResourceHydrationAccount GetTestResourceHydrationAccount()
        {
            var connectionString = TestEnvironment.TestStorageAccountConnectionString;

            // Connection strings are pairs of "key=value" separated by ';'
            var pieces = connectionString.SplitRemoveEmpty(';');
            if (pieces.ContainsInsensitively("UseDevelopmentStorage=true"))
            {
                return new ResourceHydrationAccount
                {
                    AccountName = "devstoreaccount1"
                };
            }

            // If it isn't dev storage, than we need to parse out the account name and key
            var accountName = string.Empty;
            var accountKey = string.Empty;
            
            foreach (var piece in pieces)
            {
                // Because '=' can show up as padding at the end of an account key,
                // I avoid using string.Split here
                var indexOfEquals = piece.IndexOf('=');
                if (indexOfEquals >= 0)
                {
                    if (piece.Substring(0, indexOfEquals).EqualsInsensitively("AccountName"))
                    {
                        accountName = piece.Substring(indexOfEquals + 1);
                    }
                    else if (piece.Substring(0, indexOfEquals).EqualsInsensitively("AccountKey"))
                    {
                        accountKey = piece.Substring(indexOfEquals + 1);
                    }
                }
            }

            // Resource hydration accounts require the account key to be encrypted
            var certificateThumbprint = CloudConfigurationManager.GetConfiguration("Microsoft.WindowsAzure.ResourceStack.Frontdoor.RoleEncryptionCertificate");

            return new ResourceHydrationAccount
            {
                AccountName = accountName,
                EncryptedKey = new EncryptionUtility(certificateThumbprint).Encrypt(accountKey)
            };
        }

        /// <summary>
        /// Gets the test subscription data provider.
        /// </summary>
        public static ISubscriptionDataProvider GetTestSubscriptionDataProvider()
        {
            return FrontdoorConfigurationLoader.PrimaryFrontdoorConfiguration.DataProviders
                .GetSubscriptionDataProvider(FrontdoorConfigurationLoader.PrimaryFrontdoorConfiguration.FrontdoorLocation);
        }

        /// <summary>
        /// Gets the test resource data provider.
        /// </summary>
        public static ResourceDataProvider GetTestResourceDataProvider()
        {
            return FrontdoorConfigurationLoader.PrimaryFrontdoorConfiguration.DataProviders
                .GetResourceDataProvider(FrontdoorConfigurationLoader.PrimaryFrontdoorConfiguration.FrontdoorLocation);
        }

        /// <summary>
        /// The resource provider client role.
        /// </summary>
        public static readonly string ResourceProviderClientRole = "ARMRPAccess";
        
        /// <summary>
        /// Gets the http configuration for primary instance front door configuration.
        /// </summary>
        public static HttpConfiguration GetPrimaryInstanceHttpConfiguration()
        {
            var httpConfiguration = new HttpConfiguration();
            HttpConfigurationInitializer.Instance.Initialize(httpConfiguration, FrontdoorConfigurationLoader.PrimaryFrontdoorConfiguration);
            return httpConfiguration;
        }

        /// <summary>
        /// Gets the test provider DSTS authentication header.
        /// </summary>
        public static string GetTestProviderDstsAuthenticateHeader()
        {
            var dstsAuthenticationDataProvider = new DstsAuthenticationDataProvider();

            return dstsAuthenticationDataProvider.GetDstsWWWAuthenticateHeader();
        }

        /// <summary>
        /// Gets the RDFE DSTS configuration.
        /// </summary>
        /// <param name="externalEndpoint">Get for external endpoint.</param>
        private static DatacenterServiceConfiguration GetRdfeDstsConfiguration(bool externalEndpoint = true)
        {
            var dstsRealm = CloudConfigurationManager.GetConfiguration("Microsoft.WindowsAzure.ResourceStack.Test.Rdfe.DstsRealm");
            var dstsDnsName = CloudConfigurationManager.GetConfiguration("Microsoft.WindowsAzure.ResourceStack.Test.Rdfe.DstsDnsName");

            var serviceName = externalEndpoint
                ? CloudConfigurationManager.GetConfiguration("Microsoft.WindowsAzure.ResourceStack.Test.Rdfe.ServiceName")
                : CloudConfigurationManager.GetConfiguration("Microsoft.WindowsAzure.ResourceStack.Test.Rdfe.AdminServiceName");

            var serviceDnsName = externalEndpoint
                ? CloudConfigurationManager.GetConfiguration("Microsoft.WindowsAzure.ResourceStack.Test.Rdfe.ServiceDnsName")
                : CloudConfigurationManager.GetConfiguration("Microsoft.WindowsAzure.ResourceStack.Test.Rdfe.AdminServiceDnsName");

            var serverHomeDsts = new ServerHomeDsts(dstsRealm: new Uri(dstsRealm), dstsDnsHostName: dstsDnsName);

            var serviceIdentity = new ServiceIdentity(
                serviceDnsHostName: serviceDnsName,
                serviceNames: new string[] { serviceName },
                additonalDnsHostNames: new string[] { });

            return new DatacenterServiceConfiguration(
                serverHomeDsts: serverHomeDsts,
                serviceIdentity: serviceIdentity);
        }

        /// <summary>
        /// Gets the test provider DSTS configuration.
        /// </summary>
        private static DatacenterServiceConfiguration GetTestProviderDstsConfiguration()
        {
            var dstsRealm = CloudConfigurationManager.GetConfiguration("Microsoft.WindowsAzure.ResourceStack.Providers.DstsRealm");
            var dstsDnsName = CloudConfigurationManager.GetConfiguration("Microsoft.WindowsAzure.ResourceStack.Providers.DstsDnsName");

            var serviceName = CloudConfigurationManager.GetMultivaluedConfiguration("Microsoft.WindowsAzure.ResourceStack.Providers.ServiceNames");
            var serviceDnsNames = CloudConfigurationManager.GetConfiguration("Microsoft.WindowsAzure.ResourceStack.Providers.ServiceDnsNames").SplitRemoveEmpty(';');

            var serverHomeDsts = new ServerHomeDsts(dstsRealm: new Uri(dstsRealm), dstsDnsHostName: dstsDnsName);

            var serviceIdentity = new ServiceIdentity(
                serviceDnsHostName: serviceDnsNames.First(),
                serviceNames: serviceName,
                additonalDnsHostNames: serviceDnsNames.Skip(1));

            return new DatacenterServiceConfiguration(
                serverHomeDsts: serverHomeDsts,
                serviceIdentity: serviceIdentity);
        }

        /// <summary>
        /// Gets the RDFE DSTS authentication header.
        /// </summary>
        /// <param name="externalEndpoint">Get for external endpoint.</param>
        public static string GetRdfeDstsAuthenticateHeader(bool externalEndpoint = true)
        {
            var serviceConfiguration = TestEnvironment.GetRdfeDstsConfiguration(externalEndpoint);
            var authenticationProvider = new ServerAuthenticationProvider(serviceConfiguration);

            return WwwAuthenticateHeaderFactory.CreateFromMetadata(authenticationProvider.CreateAuthenticationMetadata());
        }

        /// <summary>
        /// Authenticate RDFE requests.
        /// </summary>
        /// <param name="authorizationHeader">The authorization header.</param>
        /// <param name="externalEndpoint">Get for external endpoint.</param>
        public static bool RdfeAuthenticateRequest(string authorizationHeader, bool externalEndpoint = true)
        {
            try
            {
                var serviceConfiguration = TestEnvironment.GetRdfeDstsConfiguration(externalEndpoint);

                var dstsTokenAuthenticator = new WebSecurityTokenAuthenticator(serviceConfiguration);
                var dstsIdentities = dstsTokenAuthenticator.Authenticate(authorizationHeader: authorizationHeader);

                var claimsPrincipal = new ClaimsPrincipal(identities: dstsIdentities.CoalesceEnumerable().Select(
                    dstsIdentity => new ClaimsIdentity(
                        claims: dstsIdentity.Claims.Select(claim => new Claim(type: claim.ClaimType, value: claim.Value)),
                        authenticationType: dstsIdentity.AuthenticationType)));

                return claimsPrincipal.IsInRole(TestEnvironment.RdfeClientRole);
            }
            catch (WebServerAuthenticationException)
            {
                return false;
            }
        }

        /// <summary>
        /// Authenticate resource provider requests.
        /// </summary>
        /// <param name="authorizationHeader">The authorization header.</param>
        public static bool ResourceProviderAuthenticateRequest(string authorizationHeader)
        {
            try
            {
                var serviceConfiguration = TestEnvironment.GetTestProviderDstsConfiguration();

                var dstsTokenAuthenticator = new WebSecurityTokenAuthenticator(serviceConfiguration);
                var dstsIdentities = dstsTokenAuthenticator.Authenticate(authorizationHeader: authorizationHeader);

                var claimsPrincipal = new ClaimsPrincipal(identities: dstsIdentities.CoalesceEnumerable().Select(
                    dstsIdentity => new ClaimsIdentity(
                        claims: dstsIdentity.Claims.Select(claim => new Claim(type: claim.ClaimType, value: claim.Value)),
                        authenticationType: dstsIdentity.AuthenticationType)));

                return claimsPrincipal.IsInRole(TestEnvironment.ResourceProviderClientRole);
            }
            catch (WebServerAuthenticationException)
            {
                return false;
            }
        }

        /// <summary>
        /// Registers the resource type.
        /// </summary>
        /// <param name="resourceProvider">The resource provider.</param>
        /// <param name="resourceType">Type of the resource.</param>
        /// <param name="location">The location.</param>
        /// <param name="resourceUri">The resource URI.</param>
        public static ResourceTypeRegistration RegisterResourceType(string resourceProvider, string resourceType, string location = null, string resourceUri = null)
        {
            var registration = new ResourceTypeRegistration
            {
                ResourceProviderNamespace = resourceProvider,
                ResourceType = resourceType,
                Location = location ?? TestEnvironment.Location,
                ApiVersion = TestEnvironment.DefaultApiVersion,
                IsEnabled = true,
                EndpointUri = resourceUri ?? TestEnvironment.ResourceProviderServiceUri.ToString()
            };

            TestEnvironment.SaveProviderRegistrationAndRestartCaches(registration);

            return registration;
        }

        /// <summary>
        /// Add new locations to registered resource type.
        /// </summary>
        /// <param name="resourceProvider">The resource provider.</param>
        /// <param name="resourceType">Type of the resource.</param>
        /// <param name="locations">The locations.</param>
        public static void AddLocationToResourceTypeRegistration(string resourceProvider, string resourceType, string[] locations)
        {
            var registrations = FrontdoorConfigurationLoader.PrimaryFrontdoorConfiguration.DataProviders.RegistrationDataProvider.FindRegistrationsByNamespace(resourceProvider).Result;
            var updatedregistrations = new List<ResourceProviderManifestRegistration>();

            foreach (var registration in registrations)
            {
                var resourceProviderType = registration.Manifest.ResourceTypes.FirstOrDefault(type => type.Name.EqualsInsensitively(resourceType));
                if (resourceProviderType != null)
                {
                    foreach (var endpoint in resourceProviderType.Endpoints)
                    {
                        endpoint.Locations = endpoint.Locations.UnionInsensitively(locations).ToArray();
                    }

                    registration.LastModifiedBy = "Test";
                    updatedregistrations.Add(registration);
                }
            }

            if (updatedregistrations.Any())
            {
                FrontdoorConfigurationLoader.PrimaryFrontdoorConfiguration.DataProviders.RegistrationDataProvider.Save(updatedregistrations).Wait();
                TestEnvironment.RestartCaches();
            }
        }

        /// <summary>
        /// Add new endpoint to registered resource type
        /// </summary>
        /// <param name="resourceProvider">The resource provider.</param>
        /// <param name="resourceType">Type of the resource.</param>
        /// <param name="endpoint">The endpoint.</param>
        public static void AddEndpointToResourceTypeRegistration(string resourceProvider, string resourceType, ResourceProviderEndpoint endpoint)
        {
            var registrations = FrontdoorConfigurationLoader.PrimaryFrontdoorConfiguration.DataProviders.RegistrationDataProvider.FindRegistrationsByNamespace(resourceProvider).Result;
            var updatedregistrations = new List<ResourceProviderManifestRegistration>();

            foreach (var registration in registrations)
            {
                var resourceProviderType = registration.Manifest.ResourceTypes.FirstOrDefault(type => type.Name.EqualsInsensitively(resourceType));

                if (resourceProviderType != null)
                {
                    var endpoints = resourceProviderType.Endpoints.ToList();
                    endpoints.Add(endpoint);

                    resourceProviderType.Endpoints = endpoints.ToArray();
                    registration.LastModifiedBy = "Test";

                    updatedregistrations.Add(registration);
                }
            }

            if (updatedregistrations.Any())
            {
                FrontdoorConfigurationLoader.PrimaryFrontdoorConfiguration.DataProviders.RegistrationDataProvider.Save(updatedregistrations).Wait();
                TestEnvironment.RestartCaches();
            }
        }

        /// <summary>
        /// Resets test resource provider registrations.
        /// </summary>
        public static void ResetRegistrations()
        {
            FrontdoorConfigurationLoader.PrimaryFrontdoorConfiguration.DataProviders.RegistrationDataProvider.FindAll().Result
                .ForEach(registration =>
                {
                    registration.IsEnabled = false;
                    registration.LastModifiedBy = "Test";
                    FrontdoorConfigurationLoader.PrimaryFrontdoorConfiguration.DataProviders.RegistrationDataProvider.Save(registration).Wait();
                });

            ResourceProviderTestHostRegistration.RegisterTestResourceTypes(
                configuration: FrontdoorConfigurationLoader.PrimaryFrontdoorConfiguration,
                resourceProviderServiceUri: TestEnvironment.ResourceProviderServiceUri);

            ResourceProviderTestHostRegistration.RegisterMicrosoftResourcesTypes(
                configuration: FrontdoorConfigurationLoader.PrimaryFrontdoorConfiguration);

            TestEnvironment.RestartCaches();
        }

        /// <summary>
        /// Resets test resource provider registrations.
        /// </summary>
        public static void ResetSchemas()
        {
            var schemaDataProvider = FrontdoorConfigurationLoader.PrimaryFrontdoorConfiguration.DataProviders.GetSchemaDataProvider(
                location: FrontdoorConfigurationLoader.PrimaryFrontdoorConfiguration.FrontdoorLocation);

            foreach (var schemas in schemaDataProvider.FindAll().Result.BatchEnumerable(50))
            {
                schemaDataProvider.DeleteNormalizedSchemas(schemas).Wait();
            }

            TestEnvironment.RestartCaches();
        }

        /// <summary>
        /// Register the given schema.
        /// </summary>
        /// <param name="resourceProviderNamespace">The resource provider namespace.</param>
        /// <param name="schemaVersion">The schema version.</param>
        /// <param name="schemaContent">The schema content.</param>
        public static void RegisterSchema(string resourceProviderNamespace, string schemaVersion, string schemaContent)
        {
            var schemaDataProvider = FrontdoorConfigurationLoader.PrimaryFrontdoorConfiguration.DataProviders.GetSchemaDataProvider(
                location: FrontdoorConfigurationLoader.PrimaryFrontdoorConfiguration.FrontdoorLocation);

            var schema = new ResourceProviderSchema
            {
                ResourceProviderNamespace = resourceProviderNamespace,
                Location = FrontdoorConfigurationLoader.PrimaryFrontdoorConfiguration.FrontdoorLocation,
                SchemaVersion = schemaVersion,
                SchemaContent = JToken.Parse(schemaContent)
            };

            schemaDataProvider.SaveNormalizedSchema(schema).Wait();
            TestEnvironment.RestartCaches();
        }

        /// <summary>
        /// Create org id claims principal with given PUID.
        /// </summary>
        /// <param name="puid">Optional. PUID for the claim.</param>
        /// <param name="appid">Optional. Application Id for the claim.</param>
        /// <param name="tenantId">Optional. Tenant Id for the claim.</param>
        /// <param name="objectId">Optional. Object Id for the user.</param>
        /// <param name="name">Optional. Name for the user principal.</param>
        /// <param name="audience">The audience.</param>
        /// <param name="groupIds">Optional. Group Ids for the claim.</param>
        /// <param name="slice">Optional. Slice for the claim.</param>
        /// <param name="wids">Optional. WIDs for the claim.</param>
        /// <param name="expirationTime">Optional. The expiration time.</param>
        /// <param name="hasGroups">Optional. HasGroups for the claim.</param>
        /// <param name="amr">Optional. Authentication methods for the claim.</param>
        /// <param name="appidacr">The application id ACR claim.</param>
        /// <param name="familyName">The family name.</param>
        /// <param name="givenName">The given name.</param>
        public static ClaimsPrincipal CreateClaimsPrincipal(string puid = null, string appid = null, string tenantId = null, string objectId = null, string name = null, string audience = null, string[] groupIds = null, string slice = null, string[] wids = null, long expirationTime = 0, bool hasGroups = false, string[] amr = null, string appidacr = null, string familyName = null, string givenName = null)
        {
            var tenantIdCoalesced = tenantId ?? Guid.NewGuid().ToString();
            var claims = new List<Claim>()
            {
                new Claim(type: "puid", value: puid ?? TestEnvironment.GenerateRandomPuid()),

                new Claim(type: "appid", value: appid ?? "1950a258-227b-4e31-a9cf-717495945fc2"),

                new Claim(type: "aud", value: audience ?? "https://management.core.windows.net/"),

                new Claim(type: "iss", value: string.Format("https://sts.windows-ppe.net/{0}/", tenantIdCoalesced)),

                new Claim(type: "http://schemas.microsoft.com/identity/claims/tenantid", value: tenantIdCoalesced),

                new Claim(type: "http://schemas.microsoft.com/identity/claims/objectidentifier", value: objectId ?? Guid.NewGuid().ToString()),

                new Claim(type: "http://schemas.xmlsoap.org/ws/2005/05/identity/claims/name", value: name ?? (TestEnvironment.GenerateRandomPuid() + "@live.com")),

                new Claim(type: "exp", value: expirationTime != 0 ? expirationTime.ToString() : DateTimeExtensions.ToEpoch(time: DateTime.UtcNow.AddHours(1)).ToString()),

                new Claim(type: "http://schemas.microsoft.com/claims/authnclassreference", value: "1"),

                new Claim(type: "http://schemas.microsoft.com/identity/claims/scope", value: "user_impersonation"),

                new Claim(type: "family_name", value: familyName ?? TestEnvironment.GenerateRandomPuid()),

                new Claim(type: "given_name", value: givenName ?? TestEnvironment.GenerateRandomPuid())
            };

            if (!string.IsNullOrEmpty(slice))
            {
                claims.Add(new Claim(type: "slc", value: slice));
            }

            if (!string.IsNullOrEmpty(appidacr))
            {
                claims.Add(new Claim(type: "appidacr", value: appidacr));
            }

            if (hasGroups)
            {
                claims.Add(new Claim(type: "hasgroups", value: "true"));
            }

            claims.AddRange(groupIds
                .CoalesceEnumerable()
                .Select(group => new Claim(type: "groups", value: group)));

            claims.AddRange(wids
                .CoalesceEnumerable()
                .Select(group => new Claim(type: "wids", value: group)));

            claims.AddRange(amr
                .CoalesceEnumerable()
                .Select(group => new Claim(type: "http://schemas.microsoft.com/claims/authnmethodsreferences", value: group)));

            var claimsIdentity = new ClaimsIdentity(
                claims: claims,
                authenticationType: "Federation");

            return new ClaimsPrincipal(claimsIdentity);
        }

        /// <summary>
        /// Create live id claims principal.
        /// </summary>
        /// <param name="puid">Optional. PUID for the claim.</param>
        /// <param name="appid">Optional. Application Id for the claim.</param>
        /// <param name="tenantId">Optional. Tenant Id for the claim.</param>
        /// <param name="name">Optional. Name for the user principal.</param>
        /// <param name="objectId">Optional. Object Id for the claim.</param>
        /// <param name="expirationTime">Optional. The expiration time.</param>
        /// <param name="slice">Optional. Slice for the claim.</param>
        /// <param name="wids">Optional. WIDs for the claim.</param>
        public static ClaimsPrincipal CreateLiveIdClaimsPrincipal(string puid = null, string appid = null, string tenantId = null, string name = null, string objectId = null, long expirationTime = 0, string slice = null, string[] wids = null)
        {
            var tenantIdCoalesced = tenantId ?? Guid.NewGuid().ToString();
            var claims = new List<Claim>()
            {
                new Claim(type: "altsecid", value: string.Format("1:live.com:{0}", puid ?? TestEnvironment.GenerateRandomPuid())),

                new Claim(type: "appid", value: appid ?? "1950a258-227b-4e31-a9cf-717495945fc2"),

                new Claim(type: "aud", value: "https://management.core.windows.net/"),

                new Claim(type: "iss", value: string.Format("https://sts.windows-ppe.net/{0}/", tenantIdCoalesced)),

                new Claim(type: "http://schemas.microsoft.com/identity/claims/identityprovider", value: "live.com"),

                new Claim(type: "http://schemas.microsoft.com/identity/claims/tenantid", value: tenantIdCoalesced),

                new Claim(type: "http://schemas.microsoft.com/identity/claims/objectidentifier", value: objectId ?? Guid.NewGuid().ToString()),

                new Claim(type: "exp", value: expirationTime != 0 ? expirationTime.ToString() : DateTimeExtensions.ToEpoch(time: DateTime.UtcNow.AddHours(1)).ToString()),

                new Claim(type: "http://schemas.microsoft.com/claims/authnclassreference", value: "1"),

                new Claim(type: "http://schemas.xmlsoap.org/ws/2005/05/identity/claims/name", value: name ?? (TestEnvironment.GenerateRandomPuid() + "@live.com")),

                new Claim(type: "http://schemas.microsoft.com/claims/authnclassreference", value: "1"),

                new Claim(type: "http://schemas.microsoft.com/identity/claims/scope", value: "user_impersonation")
            };

            if (!string.IsNullOrEmpty(slice))
            {
                claims.Add(new Claim(type: "slc", value: slice));
            }

            claims.AddRange(wids
                .CoalesceEnumerable()
                .Select(group => new Claim(type: "wids", value: group)));

            var claimsIdentity = new ClaimsIdentity(
                claims: claims,
                authenticationType: "Federation");

            return new ClaimsPrincipal(claimsIdentity);
        }

        /// <summary>
        /// Create pass through live id claims principal.
        /// </summary>
        /// <param name="puid">Optional. PUID for the claim.</param>
        /// <param name="tenantId">Optional. Tenant Id for the claim.</param>
        /// <param name="name">Optional. Name for the user principal.</param>
        /// <param name="slice">Optional. Slice for the claim.</param>
        public static ClaimsPrincipal CreatePassThroughLiveIdClaimsPrincipal(string puid = null, string tenantId = null, string name = null, string slice = null)
        {
            var tenantIdCoalesced = tenantId ?? Guid.NewGuid().ToString();
            var claims = new List<Claim>()
            {
                new Claim(type: "altsecid", value: string.Format("1:live.com:{0}", puid ?? TestEnvironment.GenerateRandomPuid())),

                new Claim(type: "appid", value: "1950a258-227b-4e31-a9cf-717495945fc2"),

                new Claim(type: "aud", value: "https://management.core.windows.net/"),

                new Claim(type: "iss", value: string.Format("https://sts.windows-ppe.net/{0}/", tenantIdCoalesced)),

                new Claim(type: "http://schemas.microsoft.com/identity/claims/identityprovider", value: "live.com"),

                new Claim(type: "http://schemas.microsoft.com/identity/claims/tenantid", value: tenantIdCoalesced),

                new Claim(type: "http://schemas.xmlsoap.org/ws/2005/05/identity/claims/name", value: name ?? (TestEnvironment.GenerateRandomPuid() + "@live.com")),
            };

            if (!string.IsNullOrEmpty(slice))
            {
                claims.Add(new Claim(type: "slc", value: slice));
            }

            var claimsIdentity = new ClaimsIdentity(
                claims: claims,
                authenticationType: "Federation");

            return new ClaimsPrincipal(claimsIdentity);
        }

        /// <summary>
        /// Create application claims principal with given OID.
        /// </summary>
        /// <param name="appid">Optional. Application Id for the claim.</param>
        /// <param name="tenantId">Optional. Tenant Id for the claim.</param>
        /// <param name="objectId">Optional. Object Id for the user.</param>
        /// <param name="groupIds">Optional. Group Ids for the claim.</param>
        /// <param name="appIdAcr">Optional. Application authentication context claim.</param>
        /// <param name="wids">Optional. WIDs for the claim.</param>
        /// <param name="amr">Optional. Authentication methods for the claim.</param>
        public static ClaimsPrincipal CreateApplicationClaimsPrincipal(string appid = null, string tenantId = null, string objectId = null, string[] groupIds = null, string appIdAcr = null, string[] wids = null, string[] amr = null)
        {
            var tenantIdCoalesced = tenantId ?? Guid.NewGuid().ToString();
            var claims = new List<Claim>()
            {
                new Claim(type: "appidacr", value: appIdAcr ?? "1"),

                new Claim(type: "appid", value: appid ?? "1950a258-227b-4e31-a9cf-717495945fc2"),

                new Claim(type: "aud", value: "https://management.core.windows.net/"),

                new Claim(type: "iss", value: string.Format("https://sts.windows-ppe.net/{0}/", tenantIdCoalesced)),

                new Claim(type: "http://schemas.microsoft.com/identity/claims/identityprovider", value: string.Format("https://sts.windows-ppe.net/{0}/", tenantIdCoalesced)),

                new Claim(type: "http://schemas.microsoft.com/identity/claims/tenantid", value: tenantIdCoalesced),

                new Claim(type: "http://schemas.microsoft.com/identity/claims/objectidentifier", value: objectId ?? Guid.NewGuid().ToString()),

                new Claim(type: "exp", value: DateTimeExtensions.ToEpoch(time: DateTime.UtcNow.AddHours(1)).ToString())
            };

            claims.AddRange(wids
                .CoalesceEnumerable()
                .Select(group => new Claim(type: "wids", value: group)));

            claims.AddRange(groupIds
                .CoalesceEnumerable()
                .Select(group => new Claim(type: "groups", value: group)));

            claims.AddRange(amr
                .CoalesceEnumerable()
                .Select(group => new Claim(type: "http://schemas.microsoft.com/claims/authnmethodsreferences", value: group)));

            var claimsIdentity = new ClaimsIdentity(
                claims: claims,
                authenticationType: "Federation");

            return new ClaimsPrincipal(claimsIdentity);
        }

        /// <summary>
        /// Create foreign principal object (FPO) claims principal.
        /// </summary>
        /// <param name="puid">Optional. PUID for the claim.</param>
        /// <param name="issTenantId">Optional. Issuer tenant Id for the claim.</param>
        /// <param name="homeTenantId">Optional. Home tenant Id for the claim.</param>
        /// <param name="name">Optional. Name for the user principal.</param>
        /// <param name="objectId">Optional. Object Id for the claim.</param>
        /// <param name="wids">Optional. WIDs for the claim.</param>
        /// <param name="amr">Optional. Authentication methods for the claim.</param>
        /// <param name="expirationTime">Optional. The expiration time.</param>
        public static ClaimsPrincipal CreateCrossTenantOrgIdClaimsPrincipal(string puid = null, string issTenantId = null, string homeTenantId = null, string name = null, string objectId = null, string[] wids = null, string[] amr = null, long expirationTime = 0)
        {
            var issTenantIdCoalesced = issTenantId ?? Guid.NewGuid().ToString();
            var homeTenantIdCoalesced = homeTenantId ?? Guid.NewGuid().ToString();
            var claims = new List<Claim>()
            {
                new Claim(type: "altsecid", value: string.Format("5::{0}", puid ?? TestEnvironment.GenerateRandomPuid())),

                new Claim(type: "puid", value: TestEnvironment.GenerateRandomPuid()),

                new Claim(type: "appid", value: "1950a258-227b-4e31-a9cf-717495945fc2"),

                new Claim(type: "aud", value: "https://management.core.windows.net/"),

                new Claim(type: "iss", value: string.Format("https://sts.windows-ppe.net/{0}/", issTenantIdCoalesced)),

                new Claim(type: "http://schemas.microsoft.com/identity/claims/tenantid", value: issTenantIdCoalesced),

                new Claim(type: "http://schemas.microsoft.com/identity/claims/identityprovider", value: string.Format("https://sts.windows-ppe.net/{0}/", homeTenantIdCoalesced)),

                new Claim(type: "http://schemas.microsoft.com/identity/claims/objectidentifier", value: objectId ?? Guid.NewGuid().ToString()),

                new Claim(type: "http://schemas.xmlsoap.org/ws/2005/05/identity/claims/name", value: name ?? (TestEnvironment.GenerateRandomPuid() + "@foreigntenant.com")),

                new Claim(type: "exp", value: expirationTime != 0 ? expirationTime.ToString() : DateTimeExtensions.ToEpoch(time: DateTime.UtcNow.AddHours(1)).ToString()),

                new Claim(type: "http://schemas.microsoft.com/claims/authnclassreference", value: "1"),
            };

            claims.AddRange(wids
                .CoalesceEnumerable()
                .Select(group => new Claim(type: "wids", value: group)));
            
            claims.AddRange(amr
                .CoalesceEnumerable()
                .Select(group => new Claim(type: "http://schemas.microsoft.com/claims/authnmethodsreferences", value: group)));

            var claimsIdentity = new ClaimsIdentity(
                claims: claims,
                authenticationType: "Federation");

            return new ClaimsPrincipal(claimsIdentity);
        }

        /// <summary>
        /// Create foreign principal object (FPO) claims principal.
        /// </summary>
        /// <param name="issTenantId">Optional. Issuer tenant Id for the claim.</param>
        /// <param name="homeTenantId">Optional. Home tenant Id for the claim.</param>
        /// <param name="name">Optional. Name for the user principal.</param>
        /// <param name="groupIds">The group ids.</param>
        public static ClaimsPrincipal CreateCrossTenantArtemisClaimsPrincipal(string issTenantId = null, string homeTenantId = null, string name = null, string[] groupIds = null)
        {
            var issTenantIdCoalesced = issTenantId ?? Guid.NewGuid().ToString();
            var homeTenantIdCoalesced = homeTenantId ?? Guid.NewGuid().ToString();
            var claims = new List<Claim>()
            {
                new Claim(type: "altsecid", value: string.Format("5::{0}", TestEnvironment.GenerateRandomPuid())),

                new Claim(type: "appid", value: "1950a258-227b-4e31-a9cf-717495945fc2"),

                new Claim(type: "aud", value: "https://management.core.windows.net/"),

                new Claim(type: "iss", value: string.Format("https://sts.windows-ppe.net/{0}/", issTenantIdCoalesced)),

                new Claim(type: "http://schemas.microsoft.com/identity/claims/tenantid", value: issTenantIdCoalesced),

                new Claim(type: "http://schemas.microsoft.com/identity/claims/identityprovider", value: string.Format("https://sts.windows-ppe.net/{0}/", homeTenantIdCoalesced)),

                new Claim(type: "http://schemas.xmlsoap.org/ws/2005/05/identity/claims/name", value: name ?? (TestEnvironment.GenerateRandomPuid() + "@foreigntenant.com")),
            };

            var groupClaims = groupIds
                .CoalesceEnumerable()
                .Select(group => new Claim(type: "groups", value: group));

            claims.AddRange(groupClaims);

            var claimsIdentity = new ClaimsIdentity(
                claims: claims,
                authenticationType: "Federation");

            return new ClaimsPrincipal(claimsIdentity);
        }

        /// <summary>
        /// Create pass through foreign principal object (FPO) claims principal for org id.
        /// </summary>
        /// <param name="puid">Optional. PUID for the claim.</param>
        /// <param name="issTenantId">Optional. Issuer tenant Id for the claim.</param>
        /// <param name="homeTenantId">Optional. Home tenant Id for the claim.</param>
        /// <param name="name">Optional. Name for the user principal.</param>
        public static ClaimsPrincipal CreatePassThroughOrgIdClaimsPrincipal(string puid = null, string issTenantId = null, string homeTenantId = null, string name = null)
        {
            var issTenantIdCoalesced = issTenantId ?? Guid.NewGuid().ToString();
            var homeTenantIdCoalesced = homeTenantId ?? Guid.NewGuid().ToString();
            var claims = new List<Claim>()
            {
                new Claim(type: "altsecid", value: string.Format("5::{0}", puid ?? TestEnvironment.GenerateRandomPuid())),

                new Claim(type: "puid", value: TestEnvironment.GenerateRandomPuid()),

                new Claim(type: "appid", value: "1950a258-227b-4e31-a9cf-717495945fc2"),

                new Claim(type: "aud", value: "https://management.core.windows.net/"),

                new Claim(type: "iss", value: string.Format("https://sts.windows-ppe.net/{0}/", issTenantIdCoalesced)),

                new Claim(type: "http://schemas.microsoft.com/identity/claims/tenantid", value: issTenantIdCoalesced),

                new Claim(type: "http://schemas.microsoft.com/identity/claims/identityprovider", value: string.Format("https://sts.windows-ppe.net/{0}/", homeTenantIdCoalesced)),

                new Claim(type: "http://schemas.xmlsoap.org/ws/2005/05/identity/claims/name", value: name ?? (TestEnvironment.GenerateRandomPuid() + "@foreigntenant.com")),
            };

            var claimsIdentity = new ClaimsIdentity(
                claims: claims,
                authenticationType: "Federation");

            return new ClaimsPrincipal(claimsIdentity);
        }

        /// <summary>
        /// Gets a subscription Id that can be used
        /// </summary>
        /// <param name="subscriptionId">The subscription Id to be used.</param>
        /// <param name="tenantId">Optional. Tenant Id of the subscription.</param>
        /// <param name="registerToTestResourceProvider">Register with test resource provider if set to true.</param>
        /// <param name="ownerPuid">The owner of the subscription.</param>
        /// <param name="locationPlacementId">The location placement identifier.</param>
        /// <param name="quotaId">The quota identifier.</param>
        /// <param name="offerCategories">The offer category.</param>
        /// <param name="addSubscriptionPolicies">Adds subscription policies if set to true.</param>
        /// <param name="setSpendingLimit">Adds spending limit if set to true.</param>
        public static string ReserveSubscriptionId(string subscriptionId = null, string tenantId = null, bool registerToTestResourceProvider = true, string ownerPuid = null, string locationPlacementId = "Internal_2014-09-01", string quotaId = "Default_2014-09-01", string offerCategories = null, bool addSubscriptionPolicies = true, bool setSpendingLimit = false)
        {
            var subscription = new Subscription
            {
                SubscriptionId = subscriptionId ?? Guid.NewGuid().ToString(),
                TenantId = tenantId ?? Guid.NewGuid().ToString(),
                State = SubscriptionState.Enabled,
                DisplayName = "DisplayName-" + Guid.NewGuid().ToString(),
                AccountOwner = new SubscriptionAccountOwner { Email = "<EMAIL>", Puid = ownerPuid ?? TestEnvironment.GenerateRandomPuid() },
                SubscriptionPolicies = addSubscriptionPolicies
                    ? setSpendingLimit
                        ? new SubscriptionPolicies { LocationPlacementId = locationPlacementId, QuotaId = quotaId, SpendingLimit = SubscriptionSpendingLimit.On }
                        : new SubscriptionPolicies { LocationPlacementId = locationPlacementId, QuotaId = quotaId }
                    : null,
                OfferCategory = offerCategories,
                EntitlementStartDate = DateTime.UtcNow
            };

            TestEnvironment.SaveSubscription(subscription);

            if (registerToTestResourceProvider)
            {
                // Register new test subscriptions to test RP 
                TestEnvironment.RegisterSubscription(subscription.SubscriptionId, TestEnvironment.ResourceNamespace, ResourceProviderTestHostRegistration.GetTestResourceTypes());
            }

            return subscription.SubscriptionId;
        }

        /// <summary>
        /// Registers the subscription.
        /// </summary>
        /// <param name="subscriptionId">The subscription identifier.</param>
        /// <param name="resourceProviderNameSpace">The resource provider name space.</param>
        /// <param name="resourceTypes">The provider resource types.</param>
        public static void RegisterSubscription(string subscriptionId, string resourceProviderNameSpace, string[] resourceTypes)
        {
            ProviderResourceType[] providerResourceTypes = resourceTypes
                .Select(
                    resourceType => new ProviderResourceType
                    {
                        ResourceType = resourceType,
                        Locations = new string[] { ResourceProviderTestHostRegistration.TestLocation }
                    })
                .ToArray();

            TestEnvironment.RegisterSubscription(subscriptionId, resourceProviderNameSpace, providerResourceTypes);
        }

        /// <summary>
        /// Registers the subscription.
        /// </summary>
        /// <param name="subscriptionId">The subscription identifier.</param>
        /// <param name="resourceProviderNameSpace">The resource provider name space.</param>
        /// <param name="providerResourceTypes">The provider resource types.</param>
        /// <param name="registrationState">The registration state to use.</param>
        public static void RegisterSubscription(string subscriptionId, string resourceProviderNameSpace, ProviderResourceType[] providerResourceTypes, SubscriptionRegistrationState registrationState = SubscriptionRegistrationState.Registering)
        {
            // Register subscriptions to RP 
            var registration = new SubscriptionRegistration
            {
                SubscriptionId = subscriptionId,
                ResourceProviderNamespace = resourceProviderNameSpace,
                ResourceTypes = providerResourceTypes,
                RegistrationDate = DateTime.UtcNow,
                RegistrationState = registrationState
            };

            TestEnvironment.SaveSubscriptionRegistration(registration);
        }

        /// <summary>
        /// Gets the subscription registration.
        /// </summary>
        /// <param name="subscriptionId">The subscription identifier.</param>
        /// <param name="resourceProviderNameSpace">The resource provider name space.</param>
        public static SubscriptionRegistration FindSubscriptionRegistration(string subscriptionId, string resourceProviderNameSpace)
        {
            var subscriptionDataProvider = TestEnvironment.GetTestSubscriptionDataProvider();

            return subscriptionDataProvider.FindSubscriptionRegistration(subscriptionId, resourceProviderNameSpace).Result;
        }

        /// <summary>
        /// Saves the subscription registration.
        /// </summary>
        /// <param name="registration">The subscription registration.</param>
        public static void SaveSubscriptionRegistration(SubscriptionRegistration registration)
        {
            var subscriptionDataProvider = TestEnvironment.GetTestSubscriptionDataProvider();

            subscriptionDataProvider.SaveSubscriptionRegistration(registration).Wait();
        }

        /// <summary>
        /// Save subscription to development storage.
        /// </summary>
        /// <param name="subscription">The subscription to be added.</param>
        public static void SaveSubscription(Subscription subscription)
        {
            var subscriptionDataProvider = TestEnvironment.GetTestSubscriptionDataProvider();

            var oldSubscription = subscriptionDataProvider.FindSubscription(subscription.SubscriptionId).Result;
            if (oldSubscription == null)
            {
                subscriptionDataProvider.InsertSubscription(subscription).Wait();
            }
            else
            {
                oldSubscription.OfferCategory = subscription.OfferCategory;
                oldSubscription.OfferType = subscription.OfferType;
                oldSubscription.State = subscription.State;
                oldSubscription.TenantId = subscription.TenantId;
                oldSubscription.DisplayName = subscription.DisplayName;
                oldSubscription.SubscriptionPolicies = subscription.SubscriptionPolicies;

                if (subscription.EntitlementStartDate.HasValue)
                {
                    oldSubscription.EntitlementStartDate = oldSubscription.EntitlementStartDate ?? subscription.EntitlementStartDate;
                }

                subscriptionDataProvider.ReplaceSubscription(oldSubscription).Wait();
            }
        }

        /// <summary>
        /// Save subscription admin.
        /// </summary>
        /// <param name="adminPuid">The admin PUID to be added.</param>
        /// <param name="subscriptionId">The subscription Id.</param>
        /// <param name="adminState">Optional.The subscription admin state.</param>
        /// <param name="adminType">Optional. The subscription admin type.</param>
        public static void ReserveSubscriptionAdmin(
            string adminPuid,
            string subscriptionId,
            SubscriptionAdminState adminState = SubscriptionAdminState.Enabled,
            SubscriptionAdminType adminType = SubscriptionAdminType.CoAdmin)
        {
            var admin = SubscriptionAdmin.CreateAdmin(
                subscriptionId: subscriptionId ?? Guid.NewGuid().ToString(),
                adminPuid: adminPuid,
                adminState: adminState,
                adminType: adminType);

            var subscriptionDataProvider = TestEnvironment.GetTestSubscriptionDataProvider();

            subscriptionDataProvider.InsertSubscriptionAdmin(admin).Wait();
            subscriptionDataProvider.SaveSubscriptionAdminIndex(admin).Wait();
        }

        /// <summary>
        /// Save subscription tenant index.
        /// </summary>
        /// <param name="tenantId">The tenant Id.</param>
        /// <param name="subscriptionId">The subscription Id.</param>
        public static void ReserveSubscriptionTenantIndex(string tenantId, string subscriptionId)
        {
            var subscriptionDataProvider = FrontdoorConfigurationLoader.PrimaryFrontdoorConfiguration.DataProviders
                .GetSubscriptionDataProvider(FrontdoorConfigurationLoader.PrimaryFrontdoorConfiguration.FrontdoorLocation);

            subscriptionDataProvider
                .UpdateSubscriptionTenantIndex(
                    subscriptionId: subscriptionId,
                    state: SubscriptionState.Enabled,
                    tenantId: tenantId)
                .Wait();
        }

        /// <summary>
        /// Generate random PUID string.
        /// </summary>
        public static string GenerateRandomPuid()
        {
            return Guid.NewGuid().ToString("N").ToUpperInvariant().Substring(0, 16);
        }

        /// <summary>
        /// Generate random MSA PUID string.
        /// </summary>
        public static string GenerateRandomMSAPuid()
        {
            return "000" + Guid.NewGuid().ToString("N").ToUpperInvariant().Substring(0, 13);
        }

        /// <summary>
        /// Set account owner for the subscription.
        /// </summary>
        /// <param name="subscriptionId">The subscription Id.</param>
        /// <param name="puid">The PUID of the account owner.</param>
        /// <param name="email">The email of the account owner.</param>
        public static async Task SetSubscriptionAccountOwner(string subscriptionId, string puid, string email = null)
        {
            var subscriptionDataProvider = TestEnvironment.GetTestSubscriptionDataProvider();

            var subscription = await subscriptionDataProvider.FindSubscription(subscriptionId);
            subscription.AccountOwner = new SubscriptionAccountOwner
            {
                Puid = puid,
                Email = email,
            };

            await subscriptionDataProvider.ReplaceSubscription(subscription);
        }

        /// <summary>
        /// Add subscription admin to subscription.
        /// </summary>
        /// <param name="puid">The PUID of the user.</param>
        /// <param name="subscriptionId">The subscription Id.</param>
        /// <param name="type">Optional. Type of the admin.</param>
        public static async Task<SubscriptionAdmin> AddSubscriptionAdmin(string puid, string subscriptionId, SubscriptionAdminType type = SubscriptionAdminType.CoAdmin)
        {
            var subscriptionDataProvider = TestEnvironment.GetTestSubscriptionDataProvider();

            SubscriptionAdmin admin = SubscriptionAdmin.CreateAdmin(
                subscriptionId: subscriptionId,
                adminPuid: puid,
                adminType: type,
                adminState: SubscriptionAdminState.Enabled);

            await subscriptionDataProvider.SaveSubscriptionAdmin(admin);
            await subscriptionDataProvider.SaveSubscriptionAdminIndex(admin);

            return admin;
        }

        /// <summary>
        /// Restart/refresh configuration caches.
        /// </summary>
        public static void RestartCaches()
        {
            FrontdoorConfigurationLoader.PrimaryFrontdoorConfiguration.Finalize().Wait();
            FrontdoorConfigurationLoader.PrimaryFrontdoorConfiguration.Initialize().Wait();

            TestContextExtensions.ResetCloudConfigurationManagerCache();
            FrontdoorConfigurationLoader.ResetFrontdoorConfigurationsCache();
            FrontdoorConfigurationLoader.PrimaryFrontdoorConfiguration.Initialize().Wait();

            GC.Collect();
        }

        /// <summary>
        /// Gets merged provider registrations.
        /// </summary>
        /// <param name="manifestRegistrations">the original manifest registrations.</param>
        /// <param name="alternateResourceTypeRegistration">The alternate resource type registration.</param>
        private static ResourceProviderManifestRegistration[] GetMergedProviderRegistrations(ResourceProviderManifestRegistration[] manifestRegistrations, ResourceTypeRegistration alternateResourceTypeRegistration)
        {
            if (string.IsNullOrEmpty(alternateResourceTypeRegistration.ResourceType))
            {
                throw new ArgumentException("Resource type name cannot be empty.");
            }

            var newType = new ResourceType
            {
                Name = alternateResourceTypeRegistration.ResourceType,
                LegacyName = alternateResourceTypeRegistration.LegacyResourceTypes.CoalesceEnumerable().FirstOrDefault(),
                LegacyNames = alternateResourceTypeRegistration.LegacyResourceTypes,
                RoutingType = alternateResourceTypeRegistration.RoutingType,
                AuthorizationActionMappings = alternateResourceTypeRegistration.AuthorizationActionMappings,
                AllowedUnauthorizedActions = alternateResourceTypeRegistration.AllowedUnauthorizedActions,
                LegacyPolicy = alternateResourceTypeRegistration.LegacyPolicy,
                LinkedAccessChecks = alternateResourceTypeRegistration.LinkedAccessChecks,
                LinkedNotificationRules = alternateResourceTypeRegistration.LinkedNotificationRules,
                AsyncTimeoutRules = alternateResourceTypeRegistration.AsyncTimeoutRules,
                LoggingRules = alternateResourceTypeRegistration.LoggingRules,
                ThrottlingRules = alternateResourceTypeRegistration.ThrottlingRules,
                ResourceDeletionPolicy = alternateResourceTypeRegistration.ResourceDeletionPolicy,
                QuotaRule = alternateResourceTypeRegistration.QuotaRule,
                CapacityRule = alternateResourceTypeRegistration.CapacityRule,
                AvailabilityZoneRule = alternateResourceTypeRegistration.AvailabilityZoneRule,
                ResourceValidation = alternateResourceTypeRegistration.ResourceValidation,
                MarketplaceType = alternateResourceTypeRegistration.MarketplaceType,
                RequiredFeatures = alternateResourceTypeRegistration.RequiredFeatures,
                DefaultApiVersion = alternateResourceTypeRegistration.DefaultApiVersion,
                RoutingRule = alternateResourceTypeRegistration.RoutingRule,
                DisallowedActionVerbs = alternateResourceTypeRegistration.DisallowedActionVerbs,
                TemplateDeploymentPolicy = alternateResourceTypeRegistration.TemplateDeploymentPolicy,
                IdentityManagement = alternateResourceTypeRegistration.IdentityManagement,
                ApiProfiles = alternateResourceTypeRegistration.ApiProfiles
            };

            var newEndpoint = new ResourceProviderEndpoint
            {
                EndpointUri = alternateResourceTypeRegistration.EndpointUri,
                Locations = new[] { alternateResourceTypeRegistration.Location },
                Zones = alternateResourceTypeRegistration.Zones,
                ApiVersion = alternateResourceTypeRegistration.ApiVersion,
                Enabled = alternateResourceTypeRegistration.IsEnabled,
                Timeout = alternateResourceTypeRegistration.EndpointTimeout
            };

            if (manifestRegistrations.Length > 0)
            {
                manifestRegistrations = manifestRegistrations.SelectArray(registration =>
                {
                    var existingResourceTypes = registration.Manifest.ResourceTypes
                        .CoalesceEnumerable()
                        .Where(type => !type.Name.EqualsInsensitively(alternateResourceTypeRegistration.ResourceType))
                        .ToList();

                    var currentEndpoints = registration.Manifest.ResourceTypes
                        .CoalesceEnumerable()
                        .Where(type => type.Name.EqualsInsensitively(alternateResourceTypeRegistration.ResourceType))
                        .SelectMany(type => type.Endpoints.CoalesceEnumerable());

                    var currentLocations = currentEndpoints
                        .Where(endpoint => endpoint.ApiVersion.EqualsInsensitively(alternateResourceTypeRegistration.ApiVersion))
                        .SelectManyArray(endpoint => endpoint.Locations.CoalesceEnumerable());

                    newEndpoint.Locations = currentLocations.Union(newEndpoint.Locations, StringComparer.InvariantCulture).ToArray();

                    var newEndpoints = currentEndpoints
                        .Where(endpoint =>
                            !newEndpoint.GetApiVersions().SetEqualsInsensitively(endpoint.GetApiVersions()) ||
                            !newEndpoint.RequiredFeatures.SetEqualsInsensitively(endpoint.RequiredFeatures) ||
                            !newEndpoint.Locations.SetEqualsInsensitively(endpoint.Locations))
                        .ToList();

                    newEndpoints.Add(newEndpoint);
                    newType.Endpoints = newEndpoints.ToArray();

                    var currentRequiredResourceTypeFeatures = registration.Manifest.ResourceTypes
                        .CoalesceEnumerable()
                        .Where(type => type.Name.EqualsInsensitively(alternateResourceTypeRegistration.ResourceType))
                        .SelectMany(type => type.RequiredFeatures.CoalesceEnumerable());

                    newType.RequiredFeatures = newType.RequiredFeatures
                        .CoalesceEnumerable()
                        .Concat(currentRequiredResourceTypeFeatures)
                        .DistinctArrayInsensitively();

                    existingResourceTypes.Add(newType);
                    registration.Manifest.ResourceTypes = existingResourceTypes.ToArray();

                    var alternateProviderRequiredFeatures = new string[] { };
                    if (alternateResourceTypeRegistration.Manifest != null && alternateResourceTypeRegistration.Manifest.RequiredFeatures != null)
                    {
                        alternateProviderRequiredFeatures = alternateResourceTypeRegistration.Manifest.RequiredFeatures;
                    }

                    registration.Manifest.RequiredFeatures = registration.Manifest.RequiredFeatures
                        .CoalesceEnumerable()
                        .Concat(alternateProviderRequiredFeatures)
                        .DistinctArrayInsensitively();

                    registration.IsEnabled = true;
                    registration.LastModifiedBy = "Test";

                    registration.Manifest.ProviderType = alternateResourceTypeRegistration.Manifest != null
                        ? alternateResourceTypeRegistration.Manifest.ProviderType
                        : ResourceProviderType.NotSpecified;

                    registration.Manifest.ProviderAuthorization = alternateResourceTypeRegistration.Manifest != null
                        ? alternateResourceTypeRegistration.Manifest.ProviderAuthorization
                        : null;

                    registration.Manifest.ProviderAuthorizations = alternateResourceTypeRegistration.Manifest != null
                        ? alternateResourceTypeRegistration.Manifest.ProviderAuthorizations
                        : null;

                    registration.Manifest.ResourceGroupLockOptionDuringMove = alternateResourceTypeRegistration.Manifest != null
                        ? alternateResourceTypeRegistration.Manifest.ResourceGroupLockOptionDuringMove
                        : null;

                    return registration;
                });
            }
            else
            {
                newType.Endpoints = new[] { newEndpoint };
                var manifest = new ResourceProviderManifest
                {
                    Namespace = alternateResourceTypeRegistration.ResourceProviderNamespace,
                    LegacyNamespace = alternateResourceTypeRegistration.LegacyResourceProviderNamespace,
                    LegacyRegistrations = alternateResourceTypeRegistration.LegacyRegistrations,
                    ProviderType = alternateResourceTypeRegistration.Manifest != null
                        ? alternateResourceTypeRegistration.Manifest.ProviderType
                        : ResourceProviderType.NotSpecified,
                    RequiredFeatures = alternateResourceTypeRegistration.Manifest != null
                        ? alternateResourceTypeRegistration.Manifest.RequiredFeatures
                        : new string[] { },
                    ResourceTypes = new[]
                    {
                        newType
                    },
                };

                manifestRegistrations = FrontdoorConfigurationLoader.AvailableLocations
                    .SelectArray(location => new ResourceProviderManifestRegistration
                    {
                        Manifest = manifest,
                        IsEnabled = true,
                        Location = location,
                        LastModifiedBy = "Test",
                        ResourceTypeAliases = new ResourceTypeAliases[0]
                    });
            }

            return manifestRegistrations;
        }

        /// <summary>
        /// Gets a resource provider registration scope.
        /// </summary>
        /// <param name="alternateProviderRegistrations">The alternate provider registrations.</param>
        public static ResourceProviderRegistrationScope GetProviderRegistrationScope(params ResourceTypeRegistration[] alternateProviderRegistrations)
        {
            var newManifests = alternateProviderRegistrations
                .GroupByInsensitively(registration => registration.ResourceProviderNamespace)
                .SelectManyArray(providerGroup =>
                {
                    var newRegistrations = FrontdoorConfigurationLoader.PrimaryFrontdoorConfiguration.DataProviders.RegistrationDataProvider
                        .FindRegistrationsByNamespace(providerGroup.Key).Result
                        .Where(registration => registration.IsEnabled).ToArray();

                    foreach (var registration in providerGroup)
                    {
                        newRegistrations = TestEnvironment.GetMergedProviderRegistrations(newRegistrations, registration);
                    }

                    return newRegistrations;
                });

            var providerRegistrationScope = new ResourceProviderRegistrationScope(newManifests);
            TestEnvironment.RestartCaches();

            return providerRegistrationScope;
        }

        /// <summary>
        /// Saves the provider registration and restart caches.
        /// </summary>
        /// <param name="alternateProviderRegistration">The alternate provider registration.</param>
        public static void SaveProviderRegistrationAndRestartCaches(ResourceTypeRegistration alternateProviderRegistration)
        {
            var registrations = FrontdoorConfigurationLoader.PrimaryFrontdoorConfiguration.DataProviders.RegistrationDataProvider
                .FindRegistrationsByNamespace(alternateProviderRegistration.ResourceProviderNamespace).Result
                .Where(registration => registration.IsEnabled).ToArray();

            var newRegistrations = TestEnvironment
                .GetMergedProviderRegistrations(registrations, alternateProviderRegistration)
                .ForEach(registration => registration.LastModifiedBy = "Test");

            FrontdoorConfigurationLoader.PrimaryFrontdoorConfiguration.DataProviders.RegistrationDataProvider.Save(newRegistrations).Wait();
            TestEnvironment.RestartCaches();
        }

        #region Request URLs

        /// <summary>
        /// Gets the activity status request URI.
        /// </summary>
        /// <param name="providerNamespace">The test provider namespace.</param>
        /// <param name="activityId">The activity ID.</param>
        /// <param name="apiVersion">The API version.</param>
        public static string GetActivityStatusRequestUri(string providerNamespace, string activityId, string apiVersion)
        {
            var baseUri = string.Format(
                "{0}providers/{1}/activityStatuses/{2}",
                TestEnvironment.ShimProviderUri,
                providerNamespace,
                activityId);

            return baseUri.TrimEnd('/') + "?api-version=" + apiVersion;
        }

        /// <summary>
        /// Gets the subscription request URI.
        /// </summary>
        /// <param name="testSubscriptionId">The test subscription identifier.</param>
        /// <param name="apiVersion">The API version of the request.</param>
        public static string GetSubscriptionRequestUri(string testSubscriptionId, string apiVersion = null)
        {
            return string.Format(
                "{0}subscriptions/{1}?api-version={2}",
                TestEnvironment.FrontdoorServiceUri,
                testSubscriptionId,
                apiVersion ?? TestEnvironment.DefaultApiVersion);
        }

        /// <summary>
        /// Returns a formatted Uri for subscription requests.
        /// </summary>
        /// <param name="serviceUri">The service Uri.</param>
        /// <param name="subscriptionId">The subscription Id.</param>
        public static string GetSubscriptionRequestUri(Uri serviceUri, string subscriptionId)
        {
            return string.Format(
                "{0}subscriptions/{1}",
                serviceUri,
                subscriptionId);
        }

        /// <summary>
        /// Gets the resource provider request URI at subscription level.
        /// </summary>
        /// <param name="testSubscriptionId">The test subscription identifier.</param>
        /// <param name="providerNamespace">The test provider namespace.</param>
        /// <param name="apiVersion">The API version of the request.</param>
        /// <param name="expandAliases">Adds the alias expansion parameter to the request URI if true</param>
        public static string GetResourceProviderRequestUri(string testSubscriptionId, string providerNamespace = null, string apiVersion = null, bool expandAliases = false)
        {
            var baseUri = string.Format(
                "{0}subscriptions/{1}/providers/{2}",
                TestEnvironment.FrontdoorServiceUri,
                testSubscriptionId,
                providerNamespace);

            return baseUri + $"?{(expandAliases ? "$expand=resourceTypes/aliases&" : string.Empty)}api-version={(apiVersion ?? TestEnvironment.DefaultApiVersion)}";
        }

        /// <summary>
        /// Gets the resource groups request URI.
        /// </summary>
        /// <param name="testSubscriptionId">The test subscription identifier.</param>
        /// <param name="apiVersion">The API version of the request.</param>
        public static string GetResourceGroupsRequestUri(string testSubscriptionId, string apiVersion = null)
        {
            return string.Format(
                "{0}subscriptions/{1}/resourcegroups?api-version={2}",
                TestEnvironment.FrontdoorServiceUri,
                testSubscriptionId,
                apiVersion ?? TestEnvironment.DefaultApiVersion);
        }

        /// <summary>
        /// Gets the resource request URI at subscription level.
        /// </summary>
        /// <param name="testSubscriptionId">The test subscription identifier.</param>
        /// <param name="providerNamespace">The test provider namespace.</param>
        /// <param name="wildcard">The rest of the URL.</param>
        public static string GetSubscriptionLevelRequestUri(string testSubscriptionId, string providerNamespace, string wildcard)
        {
            var baseUri = string.Format(
                "{0}subscriptions/{1}/providers/{2}/{3}",
                TestEnvironment.FrontdoorServiceUri,
                testSubscriptionId,
                providerNamespace,
                wildcard);

            return baseUri.TrimEnd('/') + "?api-version=" + TestEnvironment.DefaultApiVersion;
        }

        /// <summary>
        /// Gets the health check request URI
        /// </summary>
        /// <param name="endpoint">The endpoint.</param>
        /// <param name="healthCheckType">Type of the health check.</param>
        public static string GetHealthCheckRequestUri(string endpoint, string healthCheckType = null)
        {
            var baseUri = string.Format(
                "{0}healthcheck/{1}",
                endpoint,
                healthCheckType ?? string.Empty);

            return baseUri.TrimEnd('/') + "?api-version=" + TestEnvironment.DefaultApiVersion;
        }

        /// <summary>
        /// Gets the endpoints discovery request URI
        /// </summary>
        /// <param name="apiVersion">The API version.</param>
        public static string GetEndpointsDiscoveryRequestUri(string apiVersion = null)
        {
            return string.Format(
                "{0}metadata/endpoints?api-version={1}",
                TestEnvironment.FrontdoorServiceUri,
                apiVersion ?? TestEnvironment.DefaultApiVersion);
        }

        /// <summary>
        /// Gets the register tenant level provider URI.
        /// </summary>
        /// <param name="providerNamespace">The test provider namespace.</param>
        public static string GetRegisterTenantProviderUri(string providerNamespace)
        {
            var baseUri = string.Format(
                "{0}providers/{1}/register",
                TestEnvironment.FrontdoorServiceUri,
                providerNamespace);

            return baseUri.TrimEnd('/') + "?api-version=" + TestEnvironment.DefaultApiVersion;
        }

        /// <summary>
        /// Gets the cross tenant register tenant level provider URI.
        /// </summary>
        /// <param name="tenantId">The tenant id.</param>
        /// <param name="providerNamespace">The test provider namespace.</param>
        public static string GetCrossTenantRegisterUri(string tenantId, string providerNamespace)
        {
            var baseUri = string.Format(
                "{0}providers/{1}/tenants/{2}/register",
                TestEnvironment.FrontdoorServiceUri,
                tenantId,
                providerNamespace);

            return baseUri.TrimEnd('/') + "?api-version=" + TestEnvironment.DefaultApiVersion;
        }

        /// <summary>
        /// Gets the register managed by tenant URI.
        /// </summary>
        /// <param name="subscriptionId">The subscription identifier.</param>
        /// <param name="tenantId">The tenant identifier.</param>
        /// <param name="apiVersion">The API version.</param>
        public static string GetRegisterManagedByTenantUri(string subscriptionId, string tenantId, string apiVersion = null)
        {
            var baseUri = string.Format(
                "{0}subscriptions/{1}/providers/Microsoft.Resources/tenants/{2}/register",
                TestEnvironment.FrontdoorServiceUri,
                subscriptionId,
                tenantId);

            var requestApiVersion = string.IsNullOrEmpty(apiVersion) ? TestEnvironment.DefaultRegisterManagedByTenantApiVersion : apiVersion;
            return baseUri.TrimEnd('/') + "?api-version=" + requestApiVersion;
        }

        /// <summary>
        /// Gets the managed by tenant URI.
        /// </summary>
        /// <param name="subscriptionId">The subscription identifier.</param>
        /// <param name="tenantId">The tenant identifier.</param>
        /// <param name="apiVersion">The API version.</param>
        public static string GetUnRegisterManagedByTenantUri(string subscriptionId, string tenantId, string apiVersion = null)
        {
            var baseUri = string.Format(
                "{0}subscriptions/{1}/providers/Microsoft.Resources/tenants/{2}/unregister",
                TestEnvironment.FrontdoorServiceUri,
                subscriptionId,
                tenantId);

            var requestApiVersion = string.IsNullOrEmpty(apiVersion) ? TestEnvironment.DefaultRegisterManagedByTenantApiVersion : apiVersion;
            return baseUri.TrimEnd('/') + "?api-version=" + requestApiVersion;
        }

        /// <summary>
        /// Gets the resource request URI at tenant level.
        /// </summary>
        /// <param name="providerNamespace">The test provider namespace.</param>
        /// <param name="resourceType">The resource type.</param>
        /// <param name="wildcard">The rest of the URL.</param>
        public static string GetTenantLevelRequestUri(string providerNamespace, string resourceType, string wildcard)
        {
            var baseUri = string.Format(
                "{0}providers/{1}/{2}/{3}",
                TestEnvironment.FrontdoorServiceUri,
                providerNamespace,
                resourceType,
                wildcard);

            return baseUri.TrimEnd('/') + "?api-version=" + TestEnvironment.DefaultApiVersion;
        }

        /// <summary>
        /// Gets the provider extension resource request URI at tenant level.
        /// </summary>
        /// <param name="providerNamespace">The test provider namespace.</param>
        /// <param name="extensionProvideNamespace">The extension provider namespace.</param>
        /// <param name="extensionResourceType">The extension resource type.</param>
        /// <param name="wildcard">The wild card.</param>
        public static string GetTenantProviderExtensionResourceRequestUri(string providerNamespace, string extensionProvideNamespace, string extensionResourceType, string wildcard)
        {
            var baseUri = string.Format(
                "{0}providers/{1}/providers/{2}/{3}/{4}",
                TestEnvironment.FrontdoorServiceUri,
                providerNamespace,
                extensionProvideNamespace,
                extensionResourceType,
                wildcard);

            return baseUri.TrimEnd('/') + "?api-version=" + TestEnvironment.DefaultApiVersion;
        }

        /// <summary>
        /// Gets the extension resource request URI at tenant level.
        /// </summary>
        /// <param name="providerNamespace">The test provider namespace.</param>
        /// <param name="tenantResourceType">The tenant resource type.</param>
        /// <param name="tenantResourceName">The tenant resource name.</param>
        /// <param name="extensionProvideNamespace">The extension provider namespace.</param>
        /// <param name="extensionResourceType">The extension resource type.</param>
        /// <param name="wildcard">The wild card.</param>
        public static string GetTenantExtensionResourceRequestUri(string providerNamespace, string tenantResourceType, string tenantResourceName, string extensionProvideNamespace, string extensionResourceType, string wildcard)
        {
            var baseUri = string.Format(
                "{0}providers/{1}/{2}/{3}/providers/{4}/{5}/{6}",
                TestEnvironment.FrontdoorServiceUri,
                providerNamespace,
                tenantResourceType,
                tenantResourceName,
                extensionProvideNamespace,
                extensionResourceType,
                wildcard);

            return baseUri.TrimEnd('/') + "?api-version=" + TestEnvironment.DefaultApiVersion;
        }

        /// <summary>
        /// Gets the resource request URI batched across multiple subscriptions within a single tenant.
        /// </summary>
        public static string GetBatchedResourcesRequestUri()
        {
            var baseUri = string.Format("{0}resources", TestEnvironment.FrontdoorServiceUri);

            return baseUri.TrimEnd('/') + "?api-version=" + TestEnvironment.DefaultApiVersion;
        }

        /// <summary>
        /// Gets the subscriptions request URI
        /// </summary>
        /// <param name="apiVersion">The API version.</param>
        public static string GetSubscriptionsRequestUri(string apiVersion = null)
        {
            var baseUri = string.Format("{0}subscriptions", TestEnvironment.FrontdoorServiceUri);

            var requestApiVersion = string.IsNullOrEmpty(apiVersion) ? TestEnvironment.DefaultApiVersion : apiVersion;
            return baseUri.TrimEnd('/') + "?api-version=" + requestApiVersion;
        }

        /// <summary>
        /// Gets the tenants request URI
        /// </summary>
        public static string GetTenantsRequestUri()
        {
            var baseUri = string.Format("{0}tenants", TestEnvironment.FrontdoorServiceUri);

            return baseUri.TrimEnd('/') + "?api-version=" + TestEnvironment.DefaultApiVersion;
        }

        /// <summary>
        /// Gets the resource name validation request URI at tenant level.
        /// </summary>
        /// <param name="apiVersion">The API version.</param>
        public static string GetTenantLevelNameValidityUri(string apiVersion = null)
        {
            return string.Format(
                "{0}providers/Microsoft.Resources/CheckResourceName?api-version={1}",
                TestEnvironment.FrontdoorServiceUri,
                apiVersion ?? TestEnvironment.DefaultApiVersion);
        }

        /// <summary>
        /// Gets the resource name validation request URI at tenant level.
        /// </summary>
        /// <param name="testSubscriptionId">The subscription id.</param>
        /// <param name="apiVersion">The API version.</param>
        public static string GetCheckPolicyComplianceUri(string testSubscriptionId, string apiVersion = null)
        {
            return string.Format(
                "{0}subscriptions/{1}/providers/Microsoft.Resources/checkPolicyCompliance?api-version={2}",
                TestEnvironment.FrontdoorServiceUri,
                testSubscriptionId,
                apiVersion ?? TestEnvironment.DefaultApiVersion);
        }

        /// <summary>
        /// Gets the resource request URI at tenant level.
        /// </summary>
        /// <param name="resourceProviderNamespace">The resource provider namespace.</param>
        /// <param name="apiVersion">The API version.</param>
        public static string GetTenantLevelProvidersRequestUri(string resourceProviderNamespace = null, string apiVersion = null)
        {
            return string.Format(
                "{0}providers/{1}?api-version={2}",
                TestEnvironment.FrontdoorServiceUri,
                resourceProviderNamespace ?? string.Empty,
                apiVersion ?? TestEnvironment.DefaultApiVersion);
        }

        /// <summary>Gets the management group request URI.</summary>
        /// <param name="managementGroupId">The test management group id</param>
        /// <param name="apiVersion">The API version.</param>
        /// <param name="baseUri">The base Uri.</param>
        public static string GetManagementGroupRequestUri(string managementGroupId, string apiVersion = null, Uri baseUri = null)
        {
            return string.Format(
                "{0}providers/Microsoft.Management/managementGroups/{1}?api-version={2}",
                baseUri ?? TestEnvironment.FrontdoorServiceUri,
                managementGroupId,
                apiVersion ?? TestEnvironment.DefaultApiVersion);
        }

        /// <summary>
        /// Gets the resource group request URI.
        /// </summary>
        /// <param name="testSubscriptionId">The test subscription identifier.</param>
        /// <param name="testResourceGroupName">Name of the test resource group.</param>
        /// <param name="apiVersion">The API version.</param>
        /// <param name="baseUri">The base Uri.</param>
        public static string GetResourceGroupRequestUri(string testSubscriptionId, string testResourceGroupName, string apiVersion = null, Uri baseUri = null)
        {
            return string.Format(
                "{0}subscriptions/{1}/resourceGroups/{2}?api-version={3}",
                baseUri ?? TestEnvironment.FrontdoorServiceUri,
                testSubscriptionId,
                testResourceGroupName,
                apiVersion ?? TestEnvironment.DefaultApiVersion);
        }

        /// <summary>
        /// Gets the resource group resources request URI.
        /// </summary>
        /// <param name="testSubscriptionId">The test subscription identifier.</param>
        /// <param name="testResourceGroupName">Name of the test resource group.</param>
        /// <param name="apiVersion">The API version.</param>
        public static string GetResourceGroupResourcesRequestUri(string testSubscriptionId, string testResourceGroupName, string apiVersion = null)
        {
            return string.Format(
                "{0}subscriptions/{1}/resourcegroups/{2}/resources?api-version={3}",
                TestEnvironment.FrontdoorServiceUri,
                testSubscriptionId,
                testResourceGroupName,
                apiVersion ?? TestEnvironment.DefaultApiVersion);
        }

        /// <summary>
        /// Gets the resource request URI using the test environment RPNS / resource types.
        /// </summary>
        /// <param name="testSubscriptionId">The test subscription identifier.</param>
        /// <param name="testResourceGroupName">Name of the test resource group.</param>
        /// <param name="testResourceName">Name of the test resource.</param>
        /// <param name="apiVersion">The API version.</param>
        public static string GetDefaultResourceRequestUri(string testSubscriptionId, string testResourceGroupName, string testResourceName = null, string apiVersion = null)
        {
            return string.Format(
                "{0}subscriptions/{1}/resourcegroups/{2}/providers/{3}/{4}/{5}?api-version={6}",
                TestEnvironment.FrontdoorServiceUri,
                testSubscriptionId,
                testResourceGroupName,
                TestEnvironment.ResourceNamespace,
                TestEnvironment.ResourceType,
                testResourceName ?? string.Empty,
                apiVersion ?? TestEnvironment.DefaultApiVersion);
        }

        /// <summary>
        /// Gets the resource request URI.
        /// </summary>
        /// <param name="uri">The RP URI.</param>
        /// <param name="testSubscriptionId">The test subscription identifier.</param>
        /// <param name="testResourceGroupName">Name of the test resource group.</param>
        /// <param name="testResourceProviderNamespace">The test resource provider namespace.</param>
        /// <param name="testResourceType">Type of the test resource.</param>
        /// <param name="testResourceName">Name of the test resource.</param>
        /// <param name="apiVersion">The API version.</param>
        public static string GetResourceRequestUri(Uri uri, string testSubscriptionId, string testResourceGroupName, string testResourceProviderNamespace, string testResourceType, string testResourceName = null, string apiVersion = null)
        {
            var baseUri = string.Format(
                "{0}subscriptions/{1}/resourceGroups/{2}/providers/{3}/{4}/{5}",
                uri,
                testSubscriptionId,
                testResourceGroupName,
                testResourceProviderNamespace,
                testResourceType,
                testResourceName ?? string.Empty);

            return baseUri + "?api-version=" + (apiVersion ?? TestEnvironment.DefaultApiVersion);
        }

        /// <summary>
        /// Gets the resource request URI.
        /// </summary>
        /// <param name="testSubscriptionId">The test subscription identifier.</param>
        /// <param name="testResourceGroupName">Name of the test resource group.</param>
        /// <param name="testResourceProviderNamespace">The test resource provider namespace.</param>
        /// <param name="testResourceType">Type of the test resource.</param>
        /// <param name="testResourceName">Name of the test resource.</param>
        /// <param name="apiVersion">The API version.</param>
        public static string GetResourceRequestUri(string testSubscriptionId, string testResourceGroupName, string testResourceProviderNamespace, string testResourceType, string testResourceName = null, string apiVersion = null)
        {
            return TestEnvironment.GetResourceRequestUri(
                uri: TestEnvironment.FrontdoorServiceUri,
                testSubscriptionId: testSubscriptionId,
                testResourceGroupName: testResourceGroupName,
                testResourceProviderNamespace: testResourceProviderNamespace,
                testResourceType: testResourceType,
                testResourceName: testResourceName,
                apiVersion: apiVersion);
        }

        /// <summary>
        /// Gets the extension resource request URI.
        /// </summary>
        /// <param name="uri">The RP URI.</param>
        /// <param name="testSubscriptionId">The test subscription identifier.</param>
        /// <param name="testResourceGroupName">Name of the test resource group.</param>
        /// <param name="testResourceProviderNamespace">The test resource provider namespace.</param>
        /// <param name="testResourceType">Type of the test resource.</param>
        /// <param name="testResourceName">Name of the test resource.</param>
        /// <param name="testExtensionProviderNamespace">The test extension resource provider namespace.</param>
        /// <param name="testExtensionResourceType">Type of the test extension resource.</param>
        /// <param name="testExtensionResourceName">Name of the test extension resource.</param>
        /// <param name="apiVersion">The API version.</param>
        public static string GetExtensionResourceRequestUri(Uri uri, string testSubscriptionId, string testResourceGroupName, string testResourceProviderNamespace, string testResourceType, string testResourceName, string testExtensionProviderNamespace, string testExtensionResourceType, string testExtensionResourceName = null, string apiVersion = null)
        {
            var baseUri = string.Format(
                "{0}subscriptions/{1}/resourceGroups/{2}/providers/{3}/{4}/{5}/providers/{6}/{7}/{8}",
                uri,
                testSubscriptionId,
                testResourceGroupName,
                testResourceProviderNamespace,
                testResourceType,
                testResourceName,
                testExtensionProviderNamespace,
                testExtensionResourceType,
                testExtensionResourceName);

            return baseUri + "?api-version=" + (apiVersion ?? TestEnvironment.DefaultApiVersion);
        }

        /// <summary>
        /// Gets the extension resource request URI.
        /// </summary>
        /// <param name="uri">The RP URI.</param>
        /// <param name="testSubscriptionId">The test subscription identifier.</param>
        /// <param name="testResourceGroupName">Name of the test resource group.</param>
        /// <param name="testResourceProviderNamespace">The test resource provider namespace.</param>
        /// <param name="testResourceType">Type of the test resource.</param>
        /// <param name="testResourceName">Name of the test resource.</param>
        /// <param name="testExtensionProviderNamespace">The test extension resource provider namespace.</param>
        /// <param name="testExtensionResourceType">Type of the test extension resource.</param>
        /// <param name="testExtensionResourceName">Name of the test extension resource.</param>
        /// <param name="testExtensionChildResourceType">Type of the test extension child resource.</param>
        /// <param name="testExtensionChildResourceName">Name of the test extension child resource.</param>
        /// <param name="apiVersion">The API version.</param>
        public static string GetExtensionNestedResourceRequestUri(Uri uri, string testSubscriptionId, string testResourceGroupName, string testResourceProviderNamespace, string testResourceType, string testResourceName, string testExtensionProviderNamespace, string testExtensionResourceType, string testExtensionResourceName, string testExtensionChildResourceType, string testExtensionChildResourceName, string apiVersion = null)
        {
            var baseUri = string.Format(
                "{0}subscriptions/{1}/resourceGroups/{2}/providers/{3}/{4}/{5}/providers/{6}/{7}/{8}/{9}/{10}",
                uri,
                testSubscriptionId,
                testResourceGroupName,
                testResourceProviderNamespace,
                testResourceType,
                testResourceName,
                testExtensionProviderNamespace,
                testExtensionResourceType,
                testExtensionResourceName,
                testExtensionChildResourceType,
                testExtensionChildResourceName);

            return baseUri + "?api-version=" + (apiVersion ?? TestEnvironment.DefaultApiVersion);
        }

        /// <summary>
        /// Gets the extension resource request URI.
        /// </summary>
        /// <param name="testSubscriptionId">The test subscription identifier.</param>
        /// <param name="testResourceGroupName">Name of the test resource group.</param>
        /// <param name="testResourceProviderNamespace">The test resource provider namespace.</param>
        /// <param name="testResourceType">Type of the test resource.</param>
        /// <param name="testResourceName">Name of the test resource.</param>
        /// <param name="testExtensionProviderNamespace">The test extension resource provider namespace.</param>
        /// <param name="testExtensionResourceType">Type of the test extension resource.</param>
        /// <param name="testExtensionResourceName">Name of the test extension resource.</param>
        /// <param name="apiVersion">The API version.</param>
        public static string GetExtensionResourceRequestUri(string testSubscriptionId, string testResourceGroupName, string testResourceProviderNamespace, string testResourceType, string testResourceName, string testExtensionProviderNamespace, string testExtensionResourceType, string testExtensionResourceName = null, string apiVersion = null)
        {
            return TestEnvironment.GetExtensionResourceRequestUri(
                uri: TestEnvironment.FrontdoorServiceUri,
                testSubscriptionId: testSubscriptionId,
                testResourceGroupName: testResourceGroupName,
                testResourceProviderNamespace: testResourceProviderNamespace,
                testResourceType: testResourceType,
                testResourceName: testResourceName,
                testExtensionProviderNamespace: testExtensionProviderNamespace,
                testExtensionResourceType: testExtensionResourceType,
                testExtensionResourceName: testExtensionResourceName,
                apiVersion: apiVersion);
        }

        /// <summary>
        /// Gets the extension resource request URI.
        /// </summary>
        /// <param name="testSubscriptionId">The test subscription identifier.</param>
        /// <param name="testResourceGroupName">Name of the test resource group.</param>
        /// <param name="testResourceProviderNamespace">The test resource provider namespace.</param>
        /// <param name="testResourceType">Type of the test resource.</param>
        /// <param name="testResourceName">Name of the test resource.</param>
        /// <param name="testExtensionProviderNamespace">The test extension resource provider namespace.</param>
        /// <param name="testExtensionResourceType">Type of the test extension resource.</param>
        /// <param name="testExtensionResourceName">Name of the test extension resource.</param>
        /// <param name="testExtensionChildResourceType">Type of the test extension child resource.</param>
        /// <param name="testExtensionChildResourceName">Name of the test extension child resource.</param>
        /// <param name="apiVersion">The API version.</param>
        public static string GetExtensionNestedResourceRequestUri(string testSubscriptionId, string testResourceGroupName, string testResourceProviderNamespace, string testResourceType, string testResourceName, string testExtensionProviderNamespace, string testExtensionResourceType, string testExtensionResourceName, string testExtensionChildResourceType, string testExtensionChildResourceName, string apiVersion = null)
        {
            return TestEnvironment.GetExtensionNestedResourceRequestUri(
                uri: TestEnvironment.FrontdoorServiceUri,
                testSubscriptionId: testSubscriptionId,
                testResourceGroupName: testResourceGroupName,
                testResourceProviderNamespace: testResourceProviderNamespace,
                testResourceType: testResourceType,
                testResourceName: testResourceName,
                testExtensionProviderNamespace: testExtensionProviderNamespace,
                testExtensionResourceType: testExtensionResourceType,
                testExtensionResourceName: testExtensionResourceName,
                testExtensionChildResourceType: testExtensionChildResourceType,
                testExtensionChildResourceName: testExtensionChildResourceName,
                apiVersion: apiVersion);
        }

        /// <summary>
        /// Gets the resource async operation request URI.
        /// </summary>
        /// <param name="testSubscriptionId">The test subscription identifier.</param>
        /// <param name="testResourceGroupName">Name of the test resource group.</param>
        /// <param name="testResourceProviderNamespace">The test resource provider namespace.</param>
        /// <param name="testResourceType">Type of the test resource.</param>
        /// <param name="testResourceName">Name of the test resource.</param>
        /// <param name="request">The request.</param>
        /// <param name="apiVersion">The API version.</param>
        public static string GetResoureAsyncOperationUri(string testSubscriptionId, string testResourceGroupName, string testResourceProviderNamespace, string testResourceType, string testResourceName, HttpRequestMessage request = null, string apiVersion = null)
        {
            var baseUri = string.Format(
                "{0}subscriptions/{1}/resourceGroups/{2}/providers/{3}/{4}/{5}/asyncOperations/{6}",
                request == null ? TestEnvironment.FrontdoorServiceUri : new UriBuilder(Uri.UriSchemeHttp, request.Headers.Referrer.Host).Uri,
                testSubscriptionId,
                testResourceGroupName,
                testResourceProviderNamespace,
                testResourceType,
                testResourceName,
                Guid.Empty.ToString());

            return baseUri + "?api-version=" + (apiVersion ?? TestEnvironment.DefaultApiVersion);
        }

        /// <summary>
        /// Gets the extension resource request URI.
        /// </summary>
        /// <param name="testSubscriptionId">The test subscription identifier.</param>
        /// <param name="testResourceGroupName">Name of the test resource group.</param>
        /// <param name="testResourceProviderNamespace">The test resource provider namespace.</param>
        /// <param name="extensionNamespace">The extension namespace.</param>
        /// <param name="extensionType">The extension type.</param>
        /// <param name="extensionResourceName">the extension resource name.</param>
        /// <param name="extensionApiVersion">The extension <c>api</c> version.</param>
        public static string GetSubscriptionProviderExtensionRequestUri(string testSubscriptionId, string testResourceGroupName, string testResourceProviderNamespace, string extensionNamespace, string extensionType, string extensionResourceName = null, string extensionApiVersion = null)
        {
            var baseUri = string.Format(
                "{0}subscriptions/{1}/resourcegroups/{2}/providers/{3}/providers/{4}/{5}/{6}",
                TestEnvironment.FrontdoorServiceUri,
                testSubscriptionId,
                testResourceGroupName,
                testResourceProviderNamespace,
                extensionNamespace,
                extensionType,
                extensionResourceName ?? string.Empty);

            return baseUri + "?api-version=" + (extensionApiVersion ?? TestEnvironment.DefaultApiVersion);
        }

        /// <summary>
        /// Gets the extension resource request URI.
        /// </summary>
        /// <param name="testSubscriptionId">The test subscription identifier.</param>
        /// <param name="testResourceGroupName">Name of the test resource group.</param>
        /// <param name="testResourceProviderNamespace">The test resource provider namespace.</param>
        /// <param name="testResourceType">Type of the test resource.</param>
        /// <param name="testResourceName">Name of the test resource.</param>          
        /// <param name="extensionNamespace">The extension namespace.</param>
        /// <param name="extensionType">The extension type.</param>
        /// <param name="extensionResourceName">the extension resource name.</param>
        /// <param name="extensionApiVersion">The extension <c>api</c> version.</param>
        public static string GetResourceExtensionRequestUri(string testSubscriptionId, string testResourceGroupName, string testResourceProviderNamespace, string testResourceType, string testResourceName, string extensionNamespace, string extensionType, string extensionResourceName = null, string extensionApiVersion = null)
        {
            var baseUri = string.Format(
                "{0}subscriptions/{1}/resourcegroups/{2}/providers/{3}/{4}/{5}/providers/{6}/{7}/{8}",
                TestEnvironment.FrontdoorServiceUri,
                testSubscriptionId,
                testResourceGroupName,
                testResourceProviderNamespace,
                testResourceType,
                testResourceName,
                extensionNamespace,
                extensionType,
                extensionResourceName ?? string.Empty);

            return baseUri + "?api-version=" + (extensionApiVersion ?? TestEnvironment.DefaultApiVersion);
        }

        /// <summary>
        /// Gets the resource request uri from the resource locator.
        /// </summary>
        /// <param name="resourceLocator">The resource locator to convert to URI.</param>
        public static string GetResourceRequestUri(ResourceLocator resourceLocator)
        {
            return TestEnvironment.GetResourceRequestUri(
                testSubscriptionId: resourceLocator.SubscriptionId,
                testResourceGroupName: resourceLocator.ResourceGroupName,
                testResourceProviderNamespace: resourceLocator.ResourceProviderNamespace,
                testResourceType: resourceLocator.ResourceType,
                testResourceName: resourceLocator.ResourceName);
        }

        /// <summary>
        /// Gets the resource action URI.
        /// </summary>
        /// <param name="testSubscriptionId">The test subscription identifier.</param>
        /// <param name="testResourceGroupName">Name of the test resource group.</param>
        /// <param name="testResourceProviderNamespace">The test resource provider namespace.</param>
        /// <param name="testResourceType">Type of the test resource.</param>
        /// <param name="testResourceName">Name of the test resource.</param>
        /// <param name="actionName">The action name.</param>
        /// <param name="apiVersion">The API version.</param>
        /// <param name="endpointUri">The RP URI.</param>
        public static string GetResourceActionUri(string testSubscriptionId, string testResourceGroupName, string testResourceProviderNamespace, string testResourceType, string testResourceName, string actionName, string apiVersion = null, Uri endpointUri = null)
        {
            var baseUri = string.Format(
                "{0}subscriptions/{1}/resourceGroups/{2}/providers/{3}/{4}/{5}/{6}",
                endpointUri ?? TestEnvironment.FrontdoorServiceUri,
                testSubscriptionId,
                testResourceGroupName,
                testResourceProviderNamespace,
                testResourceType,
                testResourceName,
                actionName);

            return baseUri + "?api-version=" + (apiVersion ?? TestEnvironment.DefaultApiVersion);
        }

        /// <summary>
        /// Gets the resource action uri from the resource locator.
        /// </summary>
        /// <param name="resourceLocator">The resource locator to convert to URI.</param>
        /// <param name="actionName">The action name.</param>
        public static string GetResourceActionUri(ResourceLocator resourceLocator, string actionName)
        {
            return TestEnvironment.GetResourceActionUri(
                testSubscriptionId: resourceLocator.SubscriptionId,
                testResourceGroupName: resourceLocator.ResourceGroupName,
                testResourceProviderNamespace: resourceLocator.ResourceProviderNamespace,
                testResourceType: resourceLocator.ResourceType,
                testResourceName: resourceLocator.ResourceName,
                actionName: actionName);
        }

        /// <summary>
        /// Appends a URI segment at the end of the path and before the parameters.
        /// </summary>
        /// <param name="originalUri">The original URI.</param>
        /// <param name="relativePath">The relative path to append.</param>
        public static string AppendUriSegment(string originalUri, string relativePath)
        {
            var original = new Uri(originalUri);

            return new UriBuilder(
                scheme: original.Scheme,
                host: original.Host,
                port: original.Port,
                path: original.AbsolutePath + relativePath,
                extraValue: original.Query).ToString();
        }

        /// <summary>
        /// Gets the resource request URI with a trailing slash.
        /// </summary>
        /// <param name="testSubscriptionId">The test subscription identifier.</param>
        /// <param name="testResourceGroupName">Name of the test resource group.</param>
        /// <param name="testResourceProviderNamespace">The test resource provider namespace.</param>
        /// <param name="testResourceType">Type of the test resource.</param>
        /// <param name="testResourceName">Name of the test resource.</param>
        /// <param name="apiVersion">The API version.</param>
        public static string GetResourceRequestUriWithTrailingSlash(string testSubscriptionId, string testResourceGroupName, string testResourceProviderNamespace, string testResourceType, string testResourceName = null, string apiVersion = null)
        {
            var baseUri = string.Format(
                "{0}subscriptions/{1}/resourcegroups/{2}/providers/{3}/{4}/{5}",
                TestEnvironment.FrontdoorServiceUri,
                testSubscriptionId,
                testResourceGroupName,
                testResourceProviderNamespace,
                testResourceType,
                testResourceName ?? string.Empty);

            return baseUri.TrimEnd('/') + '/' + "?api-version=" + (apiVersion ?? TestEnvironment.DefaultApiVersion);
        }

        /// <summary>
        /// Gets the nested resource request URI.
        /// </summary>
        /// <param name="testSubscriptionId">The test subscription identifier.</param>
        /// <param name="testResourceGroupName">Name of the test resource group.</param>
        /// <param name="testResourceProviderNamespace">The test resource provider namespace.</param>
        /// <param name="rootResourceType">Type of the root resource.</param>
        /// <param name="rootResourceName">Name of the root resource.</param>
        /// <param name="nestedResourceType">Type of the nested resource.</param>
        /// <param name="nestedResourceName">Name of the nested resource.</param>
        /// <param name="apiVersion">The API version.</param>
        public static string GetNestResourceRequestUri(string testSubscriptionId, string testResourceGroupName, string testResourceProviderNamespace, string rootResourceType, string rootResourceName, string nestedResourceType, string nestedResourceName = null, string apiVersion = null)
        {
            var baseUri = string.Format(
                "{0}subscriptions/{1}/resourcegroups/{2}/providers/{3}/{4}/{5}/{6}/{7}",
                TestEnvironment.FrontdoorServiceUri,
                testSubscriptionId,
                testResourceGroupName,
                testResourceProviderNamespace,
                rootResourceType,
                rootResourceName,
                nestedResourceType,
                nestedResourceName ?? string.Empty);

            return baseUri + "?api-version=" + (apiVersion ?? TestEnvironment.DefaultApiVersion);
        }

        /// <summary>
        /// Gets the nested resource request URI.
        /// </summary>
        /// <param name="testSubscriptionId">The test subscription identifier.</param>
        /// <param name="testResourceGroupName">Name of the test resource group.</param>
        /// <param name="testResourceProviderNamespace">The test resource provider namespace.</param>
        /// <param name="rootResourceType">Type of the root resource.</param>
        /// <param name="rootResourceName">Name of the root resource.</param>
        /// <param name="nestedResourceType">Type of the nested resource.</param>
        /// <param name="nestedResourceName">Name of the nested resource.</param>
        /// <param name="secondNestedResourceType">Type of the second nested resource.</param>
        /// <param name="secondNestedResourceName">Name of the second nested resource.</param>
        /// <param name="apiVersion">The API version.</param>
        public static string GetSecondNestResourceRequestUri(string testSubscriptionId, string testResourceGroupName, string testResourceProviderNamespace, string rootResourceType, string rootResourceName, string nestedResourceType, string nestedResourceName, string secondNestedResourceType, string secondNestedResourceName = null, string apiVersion = null)
        {
            var baseUri = string.Format(
                "{0}subscriptions/{1}/resourcegroups/{2}/providers/{3}/{4}/{5}/{6}/{7}/{8}/{9}",
                TestEnvironment.FrontdoorServiceUri,
                testSubscriptionId,
                testResourceGroupName,
                testResourceProviderNamespace,
                rootResourceType,
                rootResourceName,
                nestedResourceType,
                nestedResourceName,
                secondNestedResourceType,
                secondNestedResourceName ?? string.Empty);

            return baseUri + "?api-version=" + (apiVersion ?? TestEnvironment.DefaultApiVersion);
        }

        /// <summary>
        /// Gets the nested resource request URI.
        /// </summary>
        /// <param name="testSubscriptionId">The test subscription identifier.</param>
        /// <param name="testResourceGroupName">Name of the test resource group.</param>
        /// <param name="testResourceProviderNamespace">The test resource provider namespace.</param>
        /// <param name="rootResourceType">Type of the root resource.</param>
        /// <param name="rootResourceName">Name of the root resource.</param>
        /// <param name="nestedResourceType">Type of the nested resource.</param>
        /// <param name="nestedResourceName">Name of the nested resource.</param>
        /// <param name="secondNestedResourceType">Type of the second nested resource.</param>
        /// <param name="secondNestedResourceName">Name of the second nested resource.</param>
        /// <param name="thirdNestedResourceType">Type of the third nested resource.</param>
        /// <param name="thirdNestedResourceName">Name of the third nested resource.</param>
        /// <param name="apiVersion">The API version.</param>
        public static string GetThirdNestResourceRequestUri(
            string testSubscriptionId,
            string testResourceGroupName,
            string testResourceProviderNamespace,
            string rootResourceType,
            string rootResourceName,
            string nestedResourceType,
            string nestedResourceName,
            string secondNestedResourceType,
            string secondNestedResourceName,
            string thirdNestedResourceType,
            string thirdNestedResourceName = null,
            string apiVersion = null)
        {
            var baseUri = string.Format(
                "{0}subscriptions/{1}/resourcegroups/{2}/providers/{3}/{4}/{5}/{6}/{7}/{8}/{9}/{10}/{11}",
                TestEnvironment.FrontdoorServiceUri,
                testSubscriptionId,
                testResourceGroupName,
                testResourceProviderNamespace,
                rootResourceType,
                rootResourceName,
                nestedResourceType,
                nestedResourceName,
                secondNestedResourceType,
                secondNestedResourceName,
                thirdNestedResourceType,
                thirdNestedResourceName ?? string.Empty);

            return baseUri + "?api-version=" + (apiVersion ?? TestEnvironment.DefaultApiVersion);
        }

        /// <summary>
        /// Gets the register subscription URI.
        /// </summary>
        /// <param name="testSubscriptionId">The test subscription identifier.</param>
        /// <param name="testResourceProviderNamespace">The test resource provider namespace.</param>
        /// <param name="apiVersion">The API version.</param>
        public static string GetRegisterSubscriptionUri(string testSubscriptionId, string testResourceProviderNamespace, string apiVersion = null)
        {
            return string.Format(
                "{0}subscriptions/{1}/providers/{2}/register?api-version={3}",
                TestEnvironment.FrontdoorServiceUri,
                testSubscriptionId,
                testResourceProviderNamespace,
                apiVersion ?? TestEnvironment.DefaultApiVersion);
        }

        /// <summary>
        /// Gets the register subscription URI.
        /// </summary>
        /// <param name="serviceUri">The service URI.</param>
        /// <param name="testSubscriptionId">The test subscription identifier.</param>
        /// <param name="testResourceProviderNamespace">The test resource provider namespace.</param>
        /// <param name="apiVersion">The API version.</param>
        public static string GetRegisterSubscriptionUri(Uri serviceUri, string testSubscriptionId, string testResourceProviderNamespace, string apiVersion = null)
        {
            return string.Format(
                "{0}subscriptions/{1}/providers/{2}/register?api-version={3}",
                serviceUri,
                testSubscriptionId,
                testResourceProviderNamespace,
                apiVersion ?? TestEnvironment.DefaultApiVersion);
        }

        /// <summary>
        /// Gets the unregister subscription URI.
        /// </summary>
        /// <param name="testSubscriptionId">The test subscription identifier.</param>
        /// <param name="testResourceProviderNamespace">The test resource provider namespace.</param>
        /// <param name="apiVersion">The API version.</param>
        /// <param name="baseUri">The base Uri.</param>
        public static string GetUnregisterSubscriptionUri(string testSubscriptionId, string testResourceProviderNamespace, string apiVersion = null, Uri baseUri = null)
        {
            return string.Format(
                "{0}subscriptions/{1}/providers/{2}/unregister?api-version={3}",
                baseUri ?? TestEnvironment.FrontdoorServiceUri,
                testSubscriptionId,
                testResourceProviderNamespace,
                apiVersion ?? TestEnvironment.DefaultApiVersion);
        }

        /// <summary>
        /// Gets the register management group URI.
        /// </summary>
        /// <param name="testManagementGroupId">The test management group identifier.</param>
        /// <param name="testResourceProviderNamespace">The test resource provider namespace.</param>
        /// <param name="apiVersion">The API version.</param>
        public static string GetRegisterManagementGroupUri(string testManagementGroupId, string testResourceProviderNamespace, string apiVersion = null)
        {
            return string.Format(
                "{0}providers/Microsoft.Management/managementGroups/{1}/providers/{2}/register?api-version={3}",
                TestEnvironment.FrontdoorServiceUri,
                testManagementGroupId,
                testResourceProviderNamespace,
                apiVersion ?? TestEnvironment.DefaultApiVersion);
        }

        /// <summary>
        /// Returns a formatted URI for resource group export template requests.
        /// </summary>
        /// <param name="subscriptionId">The subscription Id.</param>
        /// <param name="resourceGroupName">The resource group name.</param>
        public static string GetResourceGroupExportTemplateUri(string subscriptionId, string resourceGroupName)
        {
            return string.Format(
                "{0}subscriptions/{1}/resourceGroups/{2}/exportTemplate?api-version={3}",
                TestEnvironment.FrontdoorServiceUri,
                subscriptionId,
                resourceGroupName,
                TestEnvironment.DefaultApiVersion);
        }

        /// <summary>
        /// Gets the resource provider SKUs URI.
        /// </summary>
        /// <param name="testSubscriptionId">The test subscription identifier.</param>
        /// <param name="testResourceProviderNamespace">The test resource provider namespace.</param>
        /// <param name="apiVersion">The API version.</param>
        /// <param name="baseUri">The base Uri.</param>
        public static string GetResourceProviderSkusUri(string testSubscriptionId, string testResourceProviderNamespace, string apiVersion = null, Uri baseUri = null)
        {
            return string.Format(
                "{0}subscriptions/{1}/providers/{2}/skus?api-version={3}",
                baseUri ?? TestEnvironment.FrontdoorServiceUri,
                testSubscriptionId,
                testResourceProviderNamespace,
                apiVersion ?? TestEnvironment.DefaultApiVersion);
        }

        /// <summary>
        /// Returns a formatted URI for resource group move requests.
        /// </summary>
        /// <param name="serviceUri">The service Uri.</param>
        /// <param name="subscriptionId">The subscription Id.</param>
        /// <param name="resourceGroupName">The resource group name.</param>
        public static string GetMoveResourcesUri(Uri serviceUri, string subscriptionId, string resourceGroupName)
        {
            return string.Format(
                "{0}subscriptions/{1}/resourceGroups/{2}/moveResources",
                serviceUri,
                subscriptionId,
                resourceGroupName);
        }

        /// <summary>
        /// Gets the move resources URI.
        /// </summary>
        /// <param name="testSubscriptionId">The test subscription identifier.</param>
        /// <param name="sourceResourceGroupName">The source resource group name.</param>
        public static string GetMoveResourcesUri(string testSubscriptionId, string sourceResourceGroupName)
        {
            var uri = TestEnvironment.GetMoveResourcesUri(
                TestEnvironment.FrontdoorServiceUri,
                testSubscriptionId,
                sourceResourceGroupName);

            return string.Format(
                "{0}?api-version={1}",
                uri,
                TestEnvironment.DefaultApiVersion);
        }

        /// <summary>
        /// Gets the validate move resources URI.
        /// </summary>
        /// <param name="testSubscriptionId">The test subscription identifier.</param>
        /// <param name="sourceResourceGroupName">The source resource group name.</param>
        public static string GetValidateMoveResourcesUri(string testSubscriptionId, string sourceResourceGroupName)
        {
            return string.Format(
                "{0}subscriptions/{1}/resourceGroups/{2}/validateMoveResources?api-version={3}",
                TestEnvironment.FrontdoorServiceUri,
                testSubscriptionId,
                sourceResourceGroupName,
                TestEnvironment.DefaultApiVersion);
        }

        /// <summary>
        /// Gets the resource move request URI.
        /// </summary>
        /// <param name="testSubscriptionId">The test subscription identifier.</param>
        /// <param name="testResourceGroupName">Name of the test resource group.</param>
        /// <param name="testResourceProviderNamespace">The test resource provider namespace.</param>
        /// <param name="rootResourceType">Type of the root resource.</param>
        /// <param name="rootResourceName">Name of the root resource.</param>
        /// <param name="nestedResourceType">Type of the nested resource.</param>
        /// <param name="nestedResourceName">Name of the nested resource.</param>
        /// <param name="secondNestedResourceType">Type of the second nested resource.</param>
        /// <param name="secondNestedResourceName">Name of the second nested resource.</param>
        public static string GetResourceMoveRequestUri(string testSubscriptionId, string testResourceGroupName, string testResourceProviderNamespace, string rootResourceType, string rootResourceName, string nestedResourceType = null, string nestedResourceName = null, string secondNestedResourceType = null, string secondNestedResourceName = null)
        {
            var baseUri = string.Format(
                "{0}subscriptions/{1}/resourcegroups/{2}/providers/{3}/{4}/{5}/{6}/{7}/{8}/{9}",
                TestEnvironment.FrontdoorServiceUri,
                testSubscriptionId,
                testResourceGroupName,
                testResourceProviderNamespace,
                rootResourceType,
                rootResourceName,
                nestedResourceType,
                nestedResourceName,
                secondNestedResourceType,
                secondNestedResourceName ?? string.Empty);

            return baseUri + "/move?api-version=" + TestEnvironment.DefaultApiVersion;
        }

        /// <summary>
        /// Gets the Subscription tags URI
        /// </summary>
        /// <param name="testSubscriptionId">The test subscription identifier.</param>
        /// <param name="tagName">The tag name.</param>
        /// <param name="tagValue">The tag value.</param>
        public static string GetSubscriptionTagUri(string testSubscriptionId, string tagName, string tagValue)
        {
            return string.Format(
                "{0}subscriptions/{1}/tagnames/{2}/tagvalues/{3}/?api-version={4}",
                TestEnvironment.FrontdoorServiceUri,
                testSubscriptionId,
                tagName,
                tagValue,
                TestEnvironment.DefaultApiVersion);
        }

        /// <summary>
        /// Gets the Subscription tag name URI
        /// </summary>
        /// <param name="testSubscriptionId">The test subscription identifier.</param>
        /// <param name="tagName">The tag name.</param>
        public static string GetSubscriptionTagNameUri(string testSubscriptionId, string tagName)
        {
            return string.Format(
                "{0}subscriptions/{1}/tagnames/{2}?api-version={3}",
                TestEnvironment.FrontdoorServiceUri,
                testSubscriptionId,
                tagName,
                TestEnvironment.DefaultApiVersion);
        }

        /// <summary>
        /// Gets the URI to query unique tags in a subscription.
        /// </summary>
        /// <param name="testSubscriptionId">The test subscription Id.</param>
        public static string GetUniqueTagsUri(string testSubscriptionId)
        {
            return string.Format(
                "{0}subscriptions/{1}/tagnames?api-version={2}",
                TestEnvironment.FrontdoorServiceUri,
                testSubscriptionId,
                TestEnvironment.DefaultApiVersion);
        }

        /// <summary>
        /// Gets the URI to query unique tags in a subscription.
        /// </summary>
        /// <param name="testSubscriptionId">The test subscription Id.</param>
        /// <param name="resourceGroupName">The resource group name.</param>
        /// <param name="virtualNetworkName">The virtual network name.</param>
        public static string GetNetworkShimResourceRequestUri(string testSubscriptionId, string resourceGroupName, string virtualNetworkName)
        {
            return string.Format(
                "{0}/subscriptions/{1}/resourceGroups/{2}/providers/Microsoft.ClassicNetwork/virtualNetworks/{3}",
                TestEnvironment.ShimProviderUri,
                testSubscriptionId,
                resourceGroupName,
                virtualNetworkName);
        }

        /// <summary>
        /// Gets the URI to query virtual networks in a subscription.
        /// </summary>
        /// <param name="testSubscriptionId">The test subscription Id.</param>
        public static string GetNetworkShimResourcesRequestUri(string testSubscriptionId)
        {
            return string.Format(
                "{0}/subscriptions/{1}/providers/Microsoft.ClassicNetwork/virtualNetworks",
                TestEnvironment.ShimProviderUri,
                testSubscriptionId);
        }

        /// <summary>
        /// Gets the URI to query virtual networks in a resource group.
        /// </summary>
        /// <param name="testSubscriptionId">The test subscription Id.</param>
        /// <param name="resourceGroupName">The resource group name.</param>
        /// <param name="apiVersion">Optional. The API version.</param>
        public static string GetNetworkShimResourcesRequestUri(string testSubscriptionId, string resourceGroupName, string apiVersion = "2016-11-01")
        {
            return string.Format(
                "{0}/subscriptions/{1}/resourceGroups/{2}/providers/Microsoft.ClassicNetwork/virtualNetworks?api-version={3}",
                TestEnvironment.ShimProviderUri,
                testSubscriptionId,
                resourceGroupName,
                apiVersion);
        }

        /// <summary>
        /// Gets the URI to trigger the virtual network validate migration operation
        /// </summary>
        /// <param name="testSubscriptionId">The test subscription Id.</param>
        /// <param name="resourceGroupName">The resource group name.</param>
        /// <param name="virtualNetworkName">The virtual network name.</param>
        /// <param name="apiVersion">The API version.</param>
        public static string GetNetworkValidateMigrationShimUri(string testSubscriptionId, string resourceGroupName, string virtualNetworkName, string apiVersion)
        {
            return string.Format(
                "{0}/subscriptions/{1}/resourceGroups/{2}/providers/Microsoft.ClassicNetwork/virtualNetworks/{3}/validateMigration?api-version={4}",
                TestEnvironment.ShimProviderUri,
                testSubscriptionId,
                resourceGroupName,
                virtualNetworkName,
                apiVersion);
        }

        /// <summary>
        /// Gets the URI to trigger the virtual network prepare migration operation
        /// </summary>
        /// <param name="testSubscriptionId">The test subscription Id.</param>
        /// <param name="resourceGroupName">The resource group name.</param>
        /// <param name="virtualNetworkName">The virtual network name.</param>
        /// <param name="apiVersion">The API version.</param>
        public static string GetNetworkPrepareMigrationShimUri(string testSubscriptionId, string resourceGroupName, string virtualNetworkName, string apiVersion)
        {
            return string.Format(
                "{0}/subscriptions/{1}/resourceGroups/{2}/providers/Microsoft.ClassicNetwork/virtualNetworks/{3}/prepareMigration?api-version={4}",
                TestEnvironment.ShimProviderUri,
                testSubscriptionId,
                resourceGroupName,
                virtualNetworkName,
                apiVersion);
        }

        /// <summary>
        /// Gets the URI to trigger the virtual network commit migration operation
        /// </summary>
        /// <param name="testSubscriptionId">The test subscription Id.</param>
        /// <param name="resourceGroupName">The resource group name.</param>
        /// <param name="virtualNetworkName">The virtual network name.</param>
        /// <param name="apiVersion">The API version.</param>
        public static string GetNetworkCommitMigrationShimUri(string testSubscriptionId, string resourceGroupName, string virtualNetworkName, string apiVersion)
        {
            return string.Format(
                "{0}/subscriptions/{1}/resourceGroups/{2}/providers/Microsoft.ClassicNetwork/virtualNetworks/{3}/commitMigration?api-version={4}",
                TestEnvironment.ShimProviderUri,
                testSubscriptionId,
                resourceGroupName,
                virtualNetworkName,
                apiVersion);
        }

        /// <summary>
        /// Gets the URI to trigger the virtual network abort migration operation
        /// </summary>
        /// <param name="testSubscriptionId">The test subscription Id.</param>
        /// <param name="resourceGroupName">The resource group name.</param>
        /// <param name="virtualNetworkName">The virtual network name.</param>
        /// <param name="apiVersion">The API version.</param>
        public static string GetNetworkAbortMigrationShimUri(string testSubscriptionId, string resourceGroupName, string virtualNetworkName, string apiVersion)
        {
            return string.Format(
                "{0}/subscriptions/{1}/resourceGroups/{2}/providers/Microsoft.ClassicNetwork/virtualNetworks/{3}/abortMigration?api-version={4}",
                TestEnvironment.ShimProviderUri,
                testSubscriptionId,
                resourceGroupName,
                virtualNetworkName,
                apiVersion);
        }

        /// <summary>
        /// Lists the virtual network gateways shim URI.
        /// </summary>
        /// <param name="subscriptionId">The subscription identifier.</param>
        /// <param name="resourceGroup">The resource group.</param>
        /// <param name="virtualNetwork">The virtual network.</param>
        public static string ListVirtualNetworkGatewaysShimUri(string subscriptionId, string resourceGroup, string virtualNetwork)
        {
            return string.Format(
                "{0}/subscriptions/{1}/resourceGroups/{2}/providers/Microsoft.ClassicNetwork/virtualNetworks/{3}/gateways",
                TestEnvironment.ShimProviderUri,
                subscriptionId,
                resourceGroup,
                virtualNetwork);
        }

        /// <summary>
        /// Gets the virtual network gateway shim URI.
        /// </summary>
        /// <param name="subscriptionId">The subscription identifier.</param>
        /// <param name="resourceGroup">The resource group.</param>
        /// <param name="virtualNetwork">The virtual network.</param>
        /// <param name="gatewayName">Name of the gateway.</param>
        public static string GetVirtualNetworkGatewayShimUri(string subscriptionId, string resourceGroup, string virtualNetwork, string gatewayName = "primary")
        {
            return string.Format(
                "{0}/subscriptions/{1}/resourceGroups/{2}/providers/Microsoft.ClassicNetwork/virtualNetworks/{3}/gateways/{4}",
                TestEnvironment.ShimProviderUri,
                subscriptionId,
                resourceGroup,
                virtualNetwork,
                gatewayName);
        }

        /// <summary>
        /// Lists the virtual network gateway certificates shim URI.
        /// </summary>
        /// <param name="subscriptionId">The subscription identifier.</param>
        /// <param name="resourceGroup">The resource group.</param>
        /// <param name="virtualNetwork">The virtual network.</param>
        public static string ListVirtualNetworkGatewayCertificatesShimUri(string subscriptionId, string resourceGroup, string virtualNetwork)
        {
            return string.Format(
                "{0}/subscriptions/{1}/resourceGroups/{2}/providers/Microsoft.ClassicNetwork/virtualNetworks/{3}/gateways/{4}/clientRootCertificates",
                TestEnvironment.ShimProviderUri,
                subscriptionId,
                resourceGroup,
                virtualNetwork,
                "primary");
        }

        /// <summary>
        /// Gets the virtual network gateway certificate shim URI.
        /// </summary>
        /// <param name="subscriptionId">The subscription identifier.</param>
        /// <param name="resourceGroup">The resource group.</param>
        /// <param name="virtualNetwork">The virtual network.</param>
        /// <param name="certificateThumbprint">The certificate thumbprint.</param>
        public static string GetVirtualNetworkGatewayCertificateShimUri(string subscriptionId, string resourceGroup, string virtualNetwork, string certificateThumbprint)
        {
            return string.Format(
                "{0}/subscriptions/{1}/resourceGroups/{2}/providers/Microsoft.ClassicNetwork/virtualNetworks/{3}/gateways/{4}/clientRootCertificates/{5}",
                TestEnvironment.ShimProviderUri,
                subscriptionId,
                resourceGroup,
                virtualNetwork,
                "primary",
                certificateThumbprint);
        }

        /// <summary>
        /// Lists the virtual network gateway package shim URI.
        /// </summary>
        /// <param name="subscriptionId">The subscription identifier.</param>
        /// <param name="resourceGroup">The resource group.</param>
        /// <param name="virtualNetwork">The virtual network.</param>
        /// <param name="certificateThumbprint">The certificate thumbprint.</param>
        public static string ListVirtualNetworkGatewayPackageShimUri(string subscriptionId, string resourceGroup, string virtualNetwork, string certificateThumbprint = null)
        {
            if (string.IsNullOrEmpty(certificateThumbprint))
            {
                return string.Format(
                    "{0}/subscriptions/{1}/resourceGroups/{2}/providers/Microsoft.ClassicNetwork/virtualNetworks/{3}/gateways/{4}/listPackage",
                    TestEnvironment.ShimProviderUri,
                    subscriptionId,
                    resourceGroup,
                    virtualNetwork,
                    "primary");
            }
            else
            {
                return string.Format(
                    "{0}/subscriptions/{1}/resourceGroups/{2}/providers/Microsoft.ClassicNetwork/virtualNetworks/{3}/gateways/{4}/clientRootCertificates/{5}/listPackage",
                    TestEnvironment.ShimProviderUri,
                    subscriptionId,
                    resourceGroup,
                    virtualNetwork,
                    "primary",
                    certificateThumbprint);
            }
        }

        /// <summary>
        /// Gets the URI to query classic administrators in the subscription.
        /// </summary>
        /// <param name="testSubscriptionId">The test subscription Id.</param>
        public static string GetClassicAdministratorsRequestUri(string testSubscriptionId)
        {
            return string.Format(
                "{0}/subscriptions/{1}/providers/microsoft.authorization/classicAdministrators?api-version=2015-05-01-preview",
                TestEnvironment.ShimProviderUri,
                testSubscriptionId);
        }

        /// <summary>
        /// Gets the URI to query classic administrators in the subscription.
        /// </summary>
        /// <param name="testSubscriptionId">The test subscription Id.</param>
        /// <param name="principalId">The principal Id for the administrator.</param>
        public static string GetClassicAdministratorRequestUri(string testSubscriptionId, string principalId)
        {
            return string.Format(
                "{0}/subscriptions/{1}/providers/microsoft.authorization/classicAdministrators/{2}?api-version=2015-05-01-preview",
                TestEnvironment.ShimProviderUri,
                testSubscriptionId,
                principalId);
        }

        /// <summary>
        /// Gets the URI to query role definition id at subscription scope.
        /// </summary>
        /// <param name="testSubscriptionId">The test subscription Id.</param>
        /// <param name="roleDefinitionId">The principal Id for the administrator.</param>
        public static string GetRoleDefinitionRequestUri(string testSubscriptionId, string roleDefinitionId)
        {
            return string.Format(
                "{0}/subscriptions/{1}/providers/microsoft.authorization/roleDefinitions/{2}?api-version=2015-06-01",
                TestEnvironment.FrontdoorServiceUri,
                testSubscriptionId,
                roleDefinitionId);
        }

        /// <summary>
        /// Gets the URI to query policy assignment id.
        /// </summary>
        /// <param name="testSubscriptionId">The test subscription Id.</param>
        /// <param name="resourceGroup">The resource group.</param>
        /// <param name="policyAssignmentId">The policy assignment identifier.</param>
        /// <param name="apiVersion">The API version.</param>
        public static string GetPolicyAssignmentRequestUri(string testSubscriptionId, string resourceGroup, string policyAssignmentId, string apiVersion = null)
        {
            if (string.IsNullOrEmpty(resourceGroup))
            {
                return string.Format(
                    "{0}/subscriptions/{1}/providers/microsoft.authorization/policyAssignments/{2}?api-version={3}",
                    TestEnvironment.ShimProviderUri,
                    testSubscriptionId,
                    policyAssignmentId,
                    string.IsNullOrEmpty(apiVersion) ? TestEnvironment.DefaultPolicyApiVersion : apiVersion);
            }

            return string.Format(
                "{0}/subscriptions/{1}/resourcegroups/{2}/providers/microsoft.authorization/policyAssignments/{3}?api-version={4}",
                TestEnvironment.ShimProviderUri,
                testSubscriptionId,
                resourceGroup,
                policyAssignmentId,
                string.IsNullOrEmpty(apiVersion) ? TestEnvironment.DefaultPolicyApiVersion : apiVersion);
        }

        /// <summary>Gets the URI to query policy assignment id.</summary>
        /// <param name="scope">The scope.</param>
        /// <param name="policyAssignmentId">The policy assignment identifier.</param>
        /// <param name="apiVersion">The API version.</param>
        public static string GetPolicyAssignmentRequestUriForScope(string scope, string policyAssignmentId, string apiVersion = null)
        {
            return string.Format(
                "{0}/{1}/providers/microsoft.authorization/policyAssignments/{2}?api-version={3}",
                TestEnvironment.ShimProviderUri,
                scope,
                policyAssignmentId,
                string.IsNullOrEmpty(apiVersion) ? TestEnvironment.DefaultPolicyApiVersion : apiVersion);
        }

        /// <summary>Gets the URI to query policy assignment id.</summary>
        /// <param name="managementGroupId">the test management group id</param>
        /// <param name="policyAssignmentId">The policy assignment identifier.</param>
        /// <param name="apiVersion">The API version.</param>
        public static string GetPolicyAssignmentManagementGroupRequestUri(string managementGroupId, string policyAssignmentId = null, string apiVersion = null)
        {
            return string.Format(
                "{0}providers/Microsoft.Management/managementGroups/{1}/providers/microsoft.authorization/policyAssignments/{2}?api-version={3}",
                TestEnvironment.ShimProviderUri,
                managementGroupId,
                policyAssignmentId,
                string.IsNullOrEmpty(apiVersion) ? TestEnvironment.DefaultPolicyApiVersion : apiVersion);
        }

        /// <summary>Gets the URI for a collection request at management group scope</summary>
        /// <param name="managementGroupId">the test management group id</param>
        /// <param name="apiVersion">The API version.</param>
        public static string GetPolicyAssignmentManagementGroupCollectionAtScopeRequestUri(string managementGroupId, string apiVersion = null)
        {
            return string.Format(
                "{0}providers/Microsoft.Management/managementGroups/{1}/providers/microsoft.authorization/policyAssignments?$filter=atScope()&api-version={2}",
                TestEnvironment.ShimProviderUri,
                managementGroupId,
                string.IsNullOrEmpty(apiVersion) ? TestEnvironment.DefaultPolicyApiVersion : apiVersion);
        }

        /// <summary>
        /// Gets the URI to query policy definition id at subscription scope.
        /// </summary>
        /// <param name="testSubscriptionId">The test subscription Id.</param>
        /// <param name="policyDefinitionId">The principal Id for the administrator.</param>
        /// <param name="apiVersion">The API version.</param>
        public static string GetPolicyDefinitionRequestUri(string testSubscriptionId, string policyDefinitionId, string apiVersion = null)
        {
            if (string.IsNullOrEmpty(testSubscriptionId))
            {
                return string.Format(
                    "{0}providers/microsoft.authorization/policyDefinitions/{1}?api-version={2}",
                    TestEnvironment.ShimProviderUri,
                    policyDefinitionId,
                    string.IsNullOrEmpty(apiVersion) ? TestEnvironment.DefaultPolicyApiVersion : apiVersion);
            }

            return string.Format(
                "{0}subscriptions/{1}/providers/microsoft.authorization/policyDefinitions/{2}?api-version={3}",
                TestEnvironment.ShimProviderUri,
                testSubscriptionId,
                policyDefinitionId,
                string.IsNullOrEmpty(apiVersion) ? TestEnvironment.DefaultPolicyApiVersion : apiVersion);
        }

        /// <summary>
        /// Gets the URI to query policy definition id at management group scope.
        /// </summary>
        /// <param name="managementGroupId">The test management group Id.</param>
        /// <param name="policyDefinitionId">The principal Id for the administrator.</param>
        /// <param name="apiVersion">The API version.</param>
        public static string GetManagementGroupPolicyDefinitionRequestUri(string managementGroupId, string policyDefinitionId, string apiVersion = null)
        {
            return string.Format(
                "{0}/providers/Microsoft.Management/managementGroups/{1}/providers/microsoft.authorization/policyDefinitions/{2}?api-version={3}",
                TestEnvironment.ShimProviderUri,
                managementGroupId,
                policyDefinitionId,
                string.IsNullOrEmpty(apiVersion) ? TestEnvironment.DefaultPolicyApiVersion : apiVersion);
        }

        /// <summary>
        /// Gets the URI to query policy set definition id at subscription scope.
        /// </summary>
        /// <param name="testSubscriptionId">The test subscription Id.</param>
        /// <param name="policySetDefinitionId">The policy set definition Id.</param>
        /// <param name="apiVersion">The API version.</param>
        public static string GetPolicySetDefinitionRequestUri(string testSubscriptionId, string policySetDefinitionId, string apiVersion = null)
        {
            if (string.IsNullOrEmpty(testSubscriptionId))
            {
                return string.Format(
                    "{0}providers/microsoft.authorization/policySetDefinitions/{1}?api-version={2}",
                    TestEnvironment.ShimProviderUri,
                    policySetDefinitionId,
                    string.IsNullOrEmpty(apiVersion) ? TestEnvironment.DefaultPolicyApiVersion : apiVersion);
            }

            return string.Format(
                "{0}subscriptions/{1}/providers/microsoft.authorization/policySetDefinitions/{2}?api-version={3}",
                TestEnvironment.ShimProviderUri,
                testSubscriptionId,
                policySetDefinitionId,
                string.IsNullOrEmpty(apiVersion) ? TestEnvironment.DefaultPolicyApiVersion : apiVersion);
        }

        /// <summary>
        /// Gets the URI to query policy set definition id at management group scope.
        /// </summary>
        /// <param name="managementGroupId">The test subscription Id.</param>
        /// <param name="policySetDefinitionId">The policy set definition Id.</param>
        /// <param name="apiVersion">The API version.</param>
        public static string GetManagementGroupPolicySetDefinitionRequestUri(string managementGroupId, string policySetDefinitionId, string apiVersion = null)
        {
            return string.Format(
                "{0}/providers/Microsoft.Management/managementGroups/{1}/providers/microsoft.authorization/policySetDefinitions/{2}?api-version={3}",
                TestEnvironment.ShimProviderUri,
                managementGroupId,
                policySetDefinitionId,
                string.IsNullOrEmpty(apiVersion) ? TestEnvironment.DefaultPolicyApiVersion : apiVersion);
        }

        /// <summary>
        /// Gets the URI to query built in policy definitions.
        /// </summary>
        /// <param name="policyDefinitionName">The name of the policy definition.</param>
        public static string GetBuiltInPolicyDefinitionRequestUri(string policyDefinitionName = null)
        {
            if (string.IsNullOrEmpty(policyDefinitionName))
            {
                return string.Format("{0}providers/microsoft.authorization/builtInPolicyDefinitions", TestEnvironment.AdminServiceUri);
            }

            return string.Format(
                "{0}providers/microsoft.authorization/builtInPolicyDefinitions/{1}",
                TestEnvironment.AdminServiceUri,
                policyDefinitionName);
        }

        /// <summary>
        /// Gets the URI to validate built in policy definitions.
        /// </summary>
        /// <param name="policyDefinitionName">The name of the policy definition.</param>
        public static string GetBuiltInPolicyDefinitionValidationUri(string policyDefinitionName = null)
        {
            if (string.IsNullOrWhiteSpace(policyDefinitionName))
            {
                return string.Format("{0}providers/microsoft.authorization/validateBuiltInPolicyDefinitions", TestEnvironment.AdminServiceUri);
            }

            return string.Format(
                "{0}providers/microsoft.authorization/builtInPolicyDefinitions/{1}/validate",
                TestEnvironment.AdminServiceUri,
                policyDefinitionName);
        }

        /// <summary>
        /// Gets the URI to query built in policy set definitions.
        /// </summary>
        /// <param name="policySetDefinitionName">The name of the policy set definition.</param>
        public static string GetBuiltInPolicySetDefinitionRequestUri(string policySetDefinitionName = null)
        {
            if (string.IsNullOrEmpty(policySetDefinitionName))
            {
                return string.Format("{0}providers/microsoft.authorization/builtInPolicySetDefinitions", TestEnvironment.AdminServiceUri);
            }

            return string.Format(
                "{0}providers/microsoft.authorization/builtInPolicySetDefinitions/{1}",
                TestEnvironment.AdminServiceUri,
                policySetDefinitionName);
        }

        /// <summary>
        /// Gets the URI to validate built in policy set definition.
        /// </summary>
        /// <param name="policySetDefinitionName">The name of the policy set definition.</param>
        public static string GetBuiltInPolicySetDefinitionValidationUri(string policySetDefinitionName = null)
        {
            if (string.IsNullOrWhiteSpace(policySetDefinitionName))
            {
                return string.Format("{0}providers/microsoft.authorization/validateBuiltInPolicySetDefinitions", TestEnvironment.AdminServiceUri);
            }

            return string.Format(
                "{0}providers/microsoft.authorization/builtInPolicySetDefinitions/{1}/validate",
                TestEnvironment.AdminServiceUri,
                policySetDefinitionName);
        }

        /// <summary>
        /// Gets the URI to query data manifests.
        /// </summary>
        /// <param name="dataManifestNamespace">The namespace of the data manifest.</param>
        public static string GetDataManifestRequestUri(string dataManifestNamespace)
        {
            return string.Format(
                "{0}providers/microsoft.authorization/dataManifests/{1}",
                TestEnvironment.AdminServiceUri,
                dataManifestNamespace);
        }

        /// <summary>
        /// Gets the URI to reset the resource group.
        /// </summary>
        /// <param name="subscriptionId">The subscription Id.</param>
        /// <param name="resourceGroupName">The resource group name.</param>
        public static string GetResourceGroupResetUri(string subscriptionId, string resourceGroupName)
        {
            return string.Format(
                "{0}/subscriptions/{1}/resourcegroups/{2}/reset",
                TestEnvironment.AdminServiceUri,
                subscriptionId,
                resourceGroupName);
        }

        /// <summary>
        /// Gets the URI to start a synchronization job at a resource group level.
        /// </summary>
        /// <param name="subscriptionId">The subscription Id.</param>
        /// <param name="resourceGroupName">The resource group name.</param>
        public static string GetResourceGroupSynchronizationUri(string subscriptionId, string resourceGroupName)
        {
            return string.Format(
                "{0}/subscriptions/{1}/resourcegroups/{2}/synchronizeResources",
                TestEnvironment.AdminServiceUri,
                subscriptionId,
                resourceGroupName);
        }

        /// <summary>
        /// Gets the URI to start a synchronization job at a resource group level.
        /// </summary>
        /// <param name="subscriptionId">The subscription Id.</param>
        /// <param name="resourceGroupName">The resource group name.</param>
        /// <param name="resourceProviderNamespace">The resource provider namespace.</param>
        public static string GetResourceGroupSynchronizationUri(string subscriptionId, string resourceGroupName, string resourceProviderNamespace)
        {
            return string.Format(
                "{0}/subscriptions/{1}/resourcegroups/{2}/providers/{3}/synchronizeResources",
                TestEnvironment.AdminServiceUri,
                subscriptionId,
                resourceGroupName,
                resourceProviderNamespace);
        }

        /// <summary>
        /// Gets the batch request URI.
        /// </summary>
        /// <param name="apiVersion">The API version.</param>
        public static string GetBatchRequestUri(string apiVersion = null)
        {
            return string.Format(
                "{0}batch?api-version={1}",
                TestEnvironment.FrontdoorServiceUri,
                apiVersion ?? TestEnvironment.DefaultApiVersion);
        }

        /// <summary>
        /// Gets the URI to start a synchronization job at a subscription level.
        /// </summary>
        /// <param name="subscriptionId">The subscription Id.</param>
        public static string GetSubscriptionSynchronizationUri(string subscriptionId)
        {
            return string.Format(
                "{0}/subscriptions/{1}/synchronizeResources",
                TestEnvironment.AdminServiceUri,
                subscriptionId);
        }

        /// <summary>
        /// Gets the URI to start a synchronization job at a subscription level.
        /// </summary>
        /// <param name="subscriptionId">The subscription Id.</param>
        /// <param name="resourceProviderNamespace">The resource provider namespace.</param>
        public static string GetSubscriptionSynchronizationUri(string subscriptionId, string resourceProviderNamespace)
        {
            return string.Format(
                "{0}/subscriptions/{1}/providers/{2}/synchronizeResources",
                TestEnvironment.AdminServiceUri,
                subscriptionId,
                resourceProviderNamespace);
        }

        /// <summary>
        /// Get the role instances request Uri.
        /// </summary>
        /// <param name="subscriptionId">The subscription Id.</param>
        /// <param name="serviceName">The service name.</param>
        /// <param name="slotName">The slot name.</param>
        public static string GetRoleInstancesRequestUri(string subscriptionId, string serviceName, string slotName)
        {
            return string.Format(
                "{0}subscriptions/{1}/services/{2}/slots/{3}/roleInstances",
                TestEnvironment.AdminServiceUri,
                subscriptionId,
                serviceName,
                slotName);
        }

        /// <summary>
        /// Validate whether the role instance exists.
        /// </summary>
        /// <param name="subscriptionId">The subscription Id.</param>
        /// <param name="serviceName">The service name.</param>
        /// <param name="slotName">The deployment slot.</param>
        /// <param name="roleName">The role name.</param>
        /// <param name="roleInstanceName">The role instance name.</param>
        public static string GetRoleInstanceRequestUri(string subscriptionId, string serviceName, string slotName, string roleName, string roleInstanceName)
        {
            return string.Format(
                "{0}subscriptions/{1}/services/{2}/slots/{3}/roles/{4}/roleInstances/{5}",
                TestEnvironment.AdminServiceUri,
                subscriptionId,
                serviceName,
                slotName,
                roleName,
                roleInstanceName);
        }

        /// <summary>
        /// Get the service configuration URI.
        /// </summary>
        /// <param name="subscriptionId">The subscription Id.</param>
        /// <param name="serviceName">The service name.</param>
        /// <param name="slotName">The deployment slot.</param>
        public static string GetServiceConfigurationRequestUri(string subscriptionId, string serviceName, string slotName)
        {
            return string.Format(
                "{0}subscriptions/{1}/services/{2}/slots/{3}/configurations",
                TestEnvironment.AdminServiceUri,
                subscriptionId,
                serviceName,
                slotName);
        }

        /// <summary>
        /// Returns a formatted URI for gallery requests.
        /// </summary>
        /// <param name="serviceUri">The service Uri.</param>
        /// <param name="subscriptionId">The subscription Id.</param>
        /// <param name="resourceGroupName">The resource group name.</param>
        /// <param name="resourceName">The resource name.</param>
        public static string GetDashboardRequestUri(Uri serviceUri, string subscriptionId, string resourceGroupName, string resourceName)
        {
            return string.Format(
                "{0}subscriptions/{1}/resourceGroups/{2}/providers/Microsoft.Portal/dashboards/{3}",
                serviceUri,
                subscriptionId,
                resourceGroupName,
                resourceName);
        }

        /// <summary>
        /// Returns a formatted URI for gallery resource group requests.
        /// </summary>
        /// <param name="serviceUri">The service Uri.</param>
        /// <param name="subscriptionId">The subscription Id.</param>
        /// <param name="resourceGroupName">The resource group name.</param>
        public static string GetDashboardResourceGroupRequestUri(Uri serviceUri, string subscriptionId, string resourceGroupName)
        {
            return string.Format(
                "{0}subscriptions/{1}/resourceGroups/{2}/providers/Microsoft.Portal/dashboards",
                serviceUri,
                subscriptionId,
                resourceGroupName);
        }

        /// <summary>
        /// Returns a formatted URI for gallery subscription requests.
        /// </summary>
        /// <param name="serviceUri">The service Uri.</param>
        /// <param name="subscriptionId">The subscription Id.</param>
        public static string GetDashboardSubscriptionRequestUri(Uri serviceUri, string subscriptionId)
        {
            return string.Format(
                "{0}subscriptions/{1}/providers/Microsoft.Portal/dashboards",
                serviceUri,
                subscriptionId);
        }

        /// <summary>
        /// Returns a formatted URI for dashboard operations requests.
        /// </summary>
        /// <param name="serviceUri">The service Uri.</param>
        public static string GetDashboardOperationsRequestUri(Uri serviceUri)
        {
            return string.Format(
                "{0}providers/Microsoft.Portal/operations",
                serviceUri);
        }

        /// <summary>
        /// Returns a formatted URI for appliance requests.
        /// </summary>
        /// <param name="serviceUri">The service Uri.</param>
        /// <param name="subscriptionId">The subscription Id.</param>
        /// <param name="resourceGroupName">The resource group name.</param>
        /// <param name="applianceName">The appliance name.</param>
        public static string GetApplianceRequestUri(Uri serviceUri, string subscriptionId, string resourceGroupName, string applianceName)
        {
            return string.Format(
                "{0}subscriptions/{1}/resourceGroups/{2}/providers/Microsoft.Solutions/appliances/{3}",
                serviceUri,
                subscriptionId,
                resourceGroupName,
                applianceName);
        }

        /// <summary>
        /// Returns a formatted URI for appliance resource group requests.
        /// </summary>
        /// <param name="serviceUri">The service Uri.</param>
        /// <param name="subscriptionId">The subscription Id.</param>
        /// <param name="resourceGroupName">The resource group name.</param>
        public static string GetAppliancesResourceGroupRequestUri(Uri serviceUri, string subscriptionId, string resourceGroupName)
        {
            return string.Format(
                "{0}subscriptions/{1}/resourceGroups/{2}/providers/Microsoft.Solutions/appliances",
                serviceUri,
                subscriptionId,
                resourceGroupName);
        }

        /// <summary>
        /// Returns a formatted URI for appliances subscription requests.
        /// </summary>
        /// <param name="serviceUri">The service Uri.</param>
        /// <param name="subscriptionId">The subscription Id.</param>
        public static string GetAppliancesSubscriptionRequestUri(Uri serviceUri, string subscriptionId)
        {
            return string.Format(
                "{0}subscriptions/{1}/providers/Microsoft.Solutions/appliances",
                serviceUri,
                subscriptionId);
        }

        /// <summary>
        /// Returns notify resource jobs URI
        /// </summary>
        /// <param name="asyncNotificationTokenData">The asyncNotificationTokenData.</param>
        public static string GetNotifyResourceJobsRequestUri(string asyncNotificationTokenData)
        {
            return string.Format(
                "{0}providers/Microsoft.Resources/notifyResourceJobs?api-version={1}&asyncNotificationToken={2}",
                TestEnvironment.FrontdoorServiceUri,
                FrontdoorConstants.ApiVersion20180201,
                asyncNotificationTokenData);
        }

        #endregion

        /// <summary>
        /// Puts a resource group with the given location in the description.
        /// </summary>
        /// <param name="subscriptionId">The subscription Id.</param>
        /// <param name="resourceGroupName">The resource group name.</param>
        /// <param name="location">The location.</param>
        /// <param name="tags">tags for the resource group</param>
        public static void PutResourceGroup(string subscriptionId, string resourceGroupName, string location, Dictionary<string, string> tags = null)
        {
            using (var httpClient = new HttpClient())
            {
                var putResourceGroupContent = new JObject
                {
                    { "location", location }
                };

                if (tags.CoalesceDictionary().Any())
                {
                    putResourceGroupContent.Add("tags", tags.ToJToken());
                }

                var requestUri = TestEnvironment.GetResourceGroupRequestUri(subscriptionId, resourceGroupName);
                var putResponse = httpClient.PutSync(requestUri, putResourceGroupContent.ToJson());
                Assert.AreEqual(HttpStatusCode.Created, putResponse.StatusCode);
            }
        }

        /// <summary>
        /// Assert that the resource locator matches all the input strings.
        /// </summary>
        /// <param name="expectedResourceLocator">The locator with expectations.</param>
        /// <param name="subscriptionId">The subscription Id.</param>
        /// <param name="resourceGroupName">The resource group name.</param>
        /// <param name="resourceProviderNamespace">The resource provider namespace.</param>
        /// <param name="resourceType">The resource type.</param>
        /// <param name="resourceName">The resource name.</param>
        public static void AssertExpectedLocator(ResourceLocator expectedResourceLocator, string subscriptionId, string resourceGroupName, string resourceProviderNamespace, string resourceType, string resourceName = null)
        {
            Assert.AreEqual(expectedResourceLocator.SubscriptionId, subscriptionId);
            Assert.AreEqual(expectedResourceLocator.ResourceGroupName, resourceGroupName);
            Assert.AreEqual(expectedResourceLocator.ResourceProviderNamespace, resourceProviderNamespace);
            Assert.AreEqual(expectedResourceLocator.ResourceType, resourceType);
            if (resourceName != null)
            {
                Assert.AreEqual(expectedResourceLocator.ResourceName, resourceName);
            }
        }

        /// <summary>
        /// Assert that the resource locator matches all the input strings for an extension resource.
        /// </summary>
        /// <param name="expectedResourceLocator">The locator with expectations.</param>
        /// <param name="subscriptionId">The subscription Id.</param>
        /// <param name="resourceGroupName">The resource group name.</param>
        /// <param name="resourceProviderNamespace">The resource provider namespace.</param>
        /// <param name="resourceType">The resource type.</param>
        /// <param name="resourceName">The resource name.</param>
        /// <param name="extensionProviderNamespace">The extension resource provider namespace.</param>
        /// <param name="extensionResourceType">The extension resource type.</param>
        /// <param name="extensionResourceName">The extension resource name.</param>
        public static void AssertExpectedLocator(ResourceLocator expectedResourceLocator, string subscriptionId, string resourceGroupName, string resourceProviderNamespace, string resourceType, string resourceName, string extensionProviderNamespace, string extensionResourceType, string extensionResourceName = null)
        {
            TestEnvironment.AssertExpectedLocator(expectedResourceLocator, subscriptionId, resourceGroupName, resourceProviderNamespace, resourceType, resourceName);

            Assert.AreEqual(expectedResourceLocator.ExtensionResourceMatches[0].ResourceProviderNamespace, extensionProviderNamespace);
            Assert.AreEqual(expectedResourceLocator.ExtensionResourceMatches[0].ResourceType, extensionResourceType);
            if (extensionResourceName != null)
            {
                Assert.AreEqual(expectedResourceLocator.ExtensionResourceMatches[0].FullyQualifiedExtensionId.SplitRemoveEmpty("/").Last(), extensionResourceName);
            }
        }

        /// <summary>
        /// Put vanilla resource to resource provider test host with standard validation.
        /// </summary>
        /// <param name="resourceProvider">The provider test host receiving the resource.</param>
        /// <param name="resourceLocator">The resource locator describing the resource scope.</param>
        /// <param name="location">The intended location of the resource.</param>
        /// <param name="providerCallback">Optional. Provides listener inside provider request processing.</param>
        public static void PutResource(
            ResourceProviderTestHost resourceProvider,
            ResourceLocator resourceLocator,
            string location,
            Action<HttpRequestMessage> providerCallback = null)
        {
            using (var httpClient = new HttpClient())
            {
                var putResourceContent = new JObject { { "location", location } };

                var requestUri = TestEnvironment.GetResourceRequestUri(
                    testSubscriptionId: resourceLocator.SubscriptionId,
                    testResourceGroupName: resourceLocator.ResourceGroupName,
                    testResourceProviderNamespace: resourceLocator.ResourceProviderNamespace,
                    testResourceType: resourceLocator.ResourceType,
                    testResourceName: resourceLocator.ResourceName);

                var isRequestHandled = false;
                resourceProvider.OnPutResource = (HttpRequestMessage request, string subscriptionId, string resourceGroupName, string resourceProviderNamespace, string resourceType, string resourceName) =>
                {
                    TestEnvironment.AssertExpectedLocator(resourceLocator, subscriptionId, resourceGroupName, resourceProviderNamespace, resourceType, resourceName);

                    var putResourceInput = request.Content.ReadAsAsync<ResourceDefinition>(JsonExtensions.JsonMediaTypeFormatters).Result;
                    Assert.AreEqual(putResourceInput.Location, location);

                    if (providerCallback != null)
                    {
                        providerCallback(request);
                    }

                    isRequestHandled = true;

                    resourceLocator.ResourceId = IResourceIdentifiableExtensions.GetResourceId(
                        fullyQualifiedResourceType: string.Join("/", resourceLocator.ResourceProviderNamespace, resourceLocator.ResourceType),
                        resourceName: string.Join("/", resourceLocator.RootResourceName, resourceLocator.ResourceName));

                    var resource = new ResourceProxyDefinition
                    {
                        Id = resourceLocator.GetFullyQualifiedResourceId(),
                        Name = resourceLocator.GetResourceName(),
                        ResourceGroup = resourceLocator.ResourceGroupName,
                        Type = resourceLocator.GetFullyQualifiedResourceType()
                    };

                    return request.CreateResponse(
                        statusCode: HttpStatusCode.Created,
                        value: resource);
                };

                var putResponse = httpClient.PutSync(requestUri, putResourceContent.ToJson());
                Assert.AreEqual(HttpStatusCode.Created, putResponse.StatusCode);
                Assert.IsTrue(isRequestHandled);
            }
        }

        /// <summary>
        /// Put vanilla resource to resource provider test host with standard validation.
        /// </summary>
        /// <param name="resourceProvider">The provider test host receiving the resource.</param>
        /// <param name="resourceLocator">The resource locator describing the resource scope.</param>
        /// <param name="location">The intended location of the resource.</param>
        /// <param name="providerCallback">Optional. Provides listener inside provider request processing.</param>
        public static void PutNestedResource(
            ResourceProviderTestHost resourceProvider,
            ResourceLocator resourceLocator,
            string location,
            Action<HttpRequestMessage> providerCallback = null)
        {
            using (var httpClient = new HttpClient())
            {
                var putResourceContent = new JObject { { "location", location } };

                resourceLocator.ResourceId = IResourceIdentifiableExtensions.GetResourceId(
                    fullyQualifiedResourceType: string.Join("/", resourceLocator.ResourceProviderNamespace, resourceLocator.ResourceType),
                    resourceName: string.Join("/", resourceLocator.RootResourceName, resourceLocator.ResourceName));

                var requestUri = TestEnvironment.GetNestResourceRequestUri(
                    testSubscriptionId: resourceLocator.SubscriptionId,
                    testResourceGroupName: resourceLocator.ResourceGroupName,
                    testResourceProviderNamespace: resourceLocator.ResourceProviderNamespace,
                    rootResourceType: resourceLocator.GetRootResourceType(),
                    rootResourceName: resourceLocator.RootResourceName,
                    nestedResourceType: resourceLocator.GetResourceTypes().Last(),
                    nestedResourceName: resourceLocator.ResourceName);

                var isRequestHandled = false;

                resourceProvider.OnPutNestedResource = (HttpRequestMessage request, string subscriptionId, string resourceGroupName, string resourceProviderNamespace, string resourceType, string resourceName, string nestedType, string nestedName, string wildcard) =>
                {
                    TestEnvironment.AssertExpectedLocator(resourceLocator, subscriptionId, resourceGroupName, resourceProviderNamespace, string.Join("/", resourceType, nestedType), nestedName);

                    var putResourceInput = request.Content.ReadAsAsync<ResourceDefinition>(JsonExtensions.JsonMediaTypeFormatters).Result;
                    Assert.AreEqual(putResourceInput.Location, location);

                    if (providerCallback != null)
                    {
                        providerCallback(request);
                    }

                    isRequestHandled = true;

                    var resource = new ResourceProxyDefinition
                    {
                        Id = resourceLocator.GetFullyQualifiedResourceId(),
                        Name = resourceLocator.GetResourceName(),
                        ResourceGroup = resourceLocator.ResourceGroupName,
                        Type = resourceLocator.GetFullyQualifiedResourceType()
                    };

                    return request.CreateResponse(
                        statusCode: HttpStatusCode.Created,
                        value: resource);
                };

                var putResponse = httpClient.PutSync(requestUri, putResourceContent.ToJson());
                Assert.AreEqual(HttpStatusCode.Created, putResponse.StatusCode);
                Assert.IsTrue(isRequestHandled);
            }
        }

        /// <summary>
        /// Put vanilla resource to resource provider test host with standard validation.
        /// </summary>
        /// <param name="resourceProvider">The provider test host receiving the resource.</param>
        /// <param name="resourceLocator">The resource locator describing the resource scope.</param>
        /// <param name="location">The intended location of the resource.</param>
        /// <param name="providerCallback">Optional. Provides listener inside provider request processing.</param>
        public static void PutExtensionResource(
            ResourceProviderTestHost resourceProvider,
            ResourceLocator resourceLocator,
            string location,
            Action<HttpRequestMessage> providerCallback = null)
        {
            using (var httpClient = new HttpClient())
            {
                var putResourceContent = new JObject { { "location", location } };

                var requestUri = TestEnvironment.GetExtensionResourceRequestUri(
                    testSubscriptionId: resourceLocator.SubscriptionId,
                    testResourceGroupName: resourceLocator.ResourceGroupName,
                    testResourceProviderNamespace: resourceLocator.ResourceProviderNamespace,
                    testResourceType: resourceLocator.ResourceType,
                    testResourceName: resourceLocator.ResourceName,
                    testExtensionProviderNamespace: resourceLocator.ExtensionResourceMatches[0].ResourceProviderNamespace,
                    testExtensionResourceType: resourceLocator.ExtensionResourceMatches[0].ResourceType,
                    testExtensionResourceName: resourceLocator.ExtensionResourceMatches[0].FullyQualifiedExtensionId.SplitRemoveEmpty("/").Last());

                var isRequestHandled = false;

                resourceProvider.OnPutExtensionResource = (HttpRequestMessage request, string subscriptionId, string resourceGroupName, string resourceProviderNamespace, string resourceType, string resourceName, string extensionProviderNamespace, string extensionResourceType, string extensionResourceName) =>
                {
                    TestEnvironment.AssertExpectedLocator(resourceLocator, subscriptionId, resourceGroupName, resourceProviderNamespace, resourceType, resourceName, extensionProviderNamespace, extensionResourceType, extensionResourceName);

                    var putResourceInput = request.Content.ReadAsAsync<ResourceDefinition>(JsonExtensions.JsonMediaTypeFormatters).Result;
                    Assert.AreEqual(putResourceInput.Location, location);

                    if (providerCallback != null)
                    {
                        providerCallback(request);
                    }

                    isRequestHandled = true;

                    var resource = new ResourceProxyDefinition
                    {
                        Id = resourceLocator.GetFullyQualifiedResourceId(),
                        Name = resourceLocator.GetResourceName(),
                        ResourceGroup = resourceLocator.ResourceGroupName,
                        Type = resourceLocator.GetFullyQualifiedResourceType()
                    };

                    return request.CreateResponse(
                        statusCode: HttpStatusCode.Created,
                        value: resource);
                };

                var putResponse = httpClient.PutSync(requestUri, putResourceContent.ToJson());
                Assert.AreEqual(HttpStatusCode.Created, putResponse.StatusCode);
                Assert.IsTrue(isRequestHandled);
            }
        }

        /// <summary>
        /// Creates the resource provider manifest.
        /// </summary>
        /// <param name="providerNamespace">The provider namespace.</param>
        /// <param name="resourceType">Type of the resource.</param>
        /// <param name="locations">The locations.</param>
        /// <param name="legacyNamespace">The legacy namespace.</param>
        /// <param name="legacyResourceTypeNames">Name of the legacy resource type.</param>
        /// <param name="routingType">The routing type.</param>
        /// <param name="providerType">Type of the resource provider.</param>
        /// <param name="apiVersion">The <c>api</c> version of the request.</param>
        /// <param name="apiVersions">The <c>api</c> versions of the request.</param>
        /// <param name="apiProfiles">The API profiles in the resource type.</param>
        /// <param name="applicationId">The application ID of the provider</param>
        /// <param name="allowUnauthorizedActions">The allowed unauthorized actions.</param>
        /// <param name="allowedTemplateDeploymentReferenceActions">The allowed template deployment reference actions.</param>
        /// <param name="allowedAudiences">The allowed audiences.</param>
        /// <param name="legacyPolicy">The legacy rules.</param>
        /// <param name="authorizationActionMapping">The authorization action mapping.</param>
        /// <param name="subscriptionStateAllowedActions">The disabled subscription allowed actions.</param>
        /// <param name="endpointRequiredFeatures">Endpoint required features.</param>
        /// <param name="resourceTypeEndpoint">The resource type endpoint.</param>
        /// <param name="identityManagementType">Type of the identity.</param>
        /// <param name="rules">The linked operation rules.</param>
        /// <param name="zoneRule">The zone rule.</param>
        /// <param name="zones">The zones.</param>
        /// <param name="quotaRule">The quota rule.</param>
        /// <param name="notificationOptions">The notification options.</param>
        /// <param name="asyncTimeoutRules">The async timeout rules.</param>
        public static ResourceProviderManifestRegistration CreateResourceProviderManifest(
            string providerNamespace,
            string resourceType,
            string[] locations,
            string legacyNamespace = null,
            string[] legacyResourceTypeNames = null,
            RoutingType routingType = RoutingType.Default,
            ResourceProviderType providerType = ResourceProviderType.NotSpecified,
            string apiVersion = null,
            string[] apiVersions = null,
            ApiProfile[] apiProfiles = null,
            string applicationId = null,
            string[] allowUnauthorizedActions = null,
            string[] allowedTemplateDeploymentReferenceActions = null,
            string[] allowedAudiences = null,
            LegacyPolicy legacyPolicy = null,
            Dictionary<string, string> authorizationActionMapping = null,
            Dictionary<SubscriptionState, string[]> subscriptionStateAllowedActions = null,
            string[] endpointRequiredFeatures = null,
            string resourceTypeEndpoint = null,
            IdentityManagementType identityManagementType = IdentityManagementType.NotSpecified,
            LinkedOperationRule[] rules = null,
            AvailabilityZoneRule zoneRule = null,
            string[] zones = null,
            QuotaRule quotaRule = null,
            ResourceProviderNotificationOptions notificationOptions = ResourceProviderNotificationOptions.None,
            AsyncTimeoutRule[] asyncTimeoutRules = null)
        {
            var registration = new ResourceProviderManifestRegistration
            {
                Manifest = new ResourceProviderManifest
                {
                    Namespace = providerNamespace,
                    LegacyNamespace = legacyNamespace,
                    ProviderType = providerType,
                    NotificationOptions = notificationOptions,
                    ProviderAuthorization = new ResourceProviderAuthorization
                    {
                        ApplicationId = applicationId
                    },
                    ResourceTypes = new[]
                    {
                        new ResourceType
                        {
                            Name = resourceType,
                            LegacyNames = legacyResourceTypeNames,
                            RoutingType = routingType,
                            AllowedUnauthorizedActions = allowUnauthorizedActions,
                            AllowedTemplateDeploymentReferenceActions = allowedTemplateDeploymentReferenceActions,
                            LegacyPolicy = legacyPolicy,
                            DefaultApiVersion = apiVersion,
                            LinkedOperationRules = rules,
                            Endpoints = new[]
                            {
                                new ResourceProviderEndpoint
                                {
                                    RequiredFeatures = endpointRequiredFeatures,
                                    EndpointUri = resourceTypeEndpoint ?? TestEnvironment.ResourceProviderServiceUri.ToString(),
                                    Locations = locations,
                                    Zones = zones,
                                    Timeout = TimeSpan.FromMinutes(1),
                                    ApiVersions = apiVersions.CoalesceEnumerable().ConcatArray(apiVersion ?? string.Empty),
                                    Enabled = true
                                }
                            },
                            IdentityManagement = new IdentityManagement { Type = identityManagementType },
                            AuthorizationActionMappings = authorizationActionMapping
                                .CoalesceDictionary()
                                .SelectArray(element => new AuthorizationActionMapping { Original = element.Key, Desired = element.Value }),
                            SubscriptionStateRules = subscriptionStateAllowedActions
                                .CoalesceDictionary()
                                .SelectArray(element => new SubscriptionStateRule { State = element.Key, AllowedActions = element.Value }),
                            AvailabilityZoneRule = zoneRule,
                            QuotaRule = quotaRule,
                            ApiProfiles = apiProfiles,
                            AsyncTimeoutRules = asyncTimeoutRules,
                        }
                    }
                },
                IsEnabled = true,
            };

            return registration;
        }

        /// <summary>
        /// Gets the scope for given request.
        /// </summary>
        /// <param name="requestUri">The request URI.</param>
        public static string GetScope(string requestUri)
        {
            return TestEnvironment.GetScope(new Uri(requestUri));
        }

        /// <summary>
        /// Gets the scope for given request.
        /// </summary>
        /// <param name="requestUri">The request URI.</param>
        public static string GetScope(Uri requestUri)
        {
            var segments = HttpUtility.GetPathSegments(requestUri);
            var subscriptionId = segments.Count() >= 2 ?
                segments.Skip(1).First()
                : Guid.NewGuid().ToString();

            return RequestUrlParser.GetAuthorizationScope(requestUri, subscriptionId);
        }

        /// <summary>
        /// Gets the action for given request.
        /// </summary>
        /// <param name="requestUri">The request URI.</param>
        /// <param name="requestMethod">The request method.</param>
        public static string GetAction(Uri requestUri, HttpMethod requestMethod)
        {
            return RequestUrlParser.GetAuthorizationAction(requestUri, requestMethod.ToActionVerb());
        }

        /// <summary>
        /// Gets the subscription scope.
        /// </summary>
        /// <param name="subscriptionId">The subscription id.</param>
        public static string GetSubscriptionScope(string subscriptionId)
        {
            return RequestUrlParser.GetSubscriptionScope(subscriptionId: subscriptionId);
        }

        /// <summary>
        /// Gets the resource group scope.
        /// </summary>
        /// <param name="subscriptionId">The subscription id.</param>
        /// <param name="resourceGroupName">The resource group name.</param>
        public static string GetResourceGroupScope(string subscriptionId, string resourceGroupName)
        {
            return string.Format("/subscriptions/{0}/resourceGroups/{1}", subscriptionId, resourceGroupName);
        }

        /// <summary>
        /// Gets the resource scope.
        /// </summary>
        /// <param name="subscriptionId">The subscription id.</param>
        /// <param name="resourceGroupName">The resource group name.</param>
        /// <param name="resourceProviderNamespace">The resource provider namespace.</param>
        /// <param name="resourceType">Type of the resource.</param>
        /// <param name="resourceName">Name of the resource.</param>
        public static string GetResourceScope(
            string subscriptionId,
            string resourceGroupName,
            string resourceProviderNamespace,
            string resourceType,
            string resourceName = null)
        {
            return string.Format(
                "/subscriptions/{0}/resourceGroups/{1}/providers/{2}/{3}/{4}",
                subscriptionId,
                resourceGroupName,
                resourceProviderNamespace,
                resourceType,
                resourceName);
        }

        /// <summary>
        /// Gets the nested resource scope.
        /// </summary>
        /// <param name="subscriptionId">The subscription id.</param>
        /// <param name="resourceGroupName">The resource group name.</param>
        /// <param name="resourceProviderNamespace">The resource provider namespace.</param>
        /// <param name="resourceType">Type of the resource.</param>
        /// <param name="resourceName">Name of the resource.</param>
        /// <param name="nestedResourceType">The nested resource type.</param>
        /// <param name="nestedResourceName">The nested resource name.</param>
        public static string GetNestedResourceScope(
            string subscriptionId,
            string resourceGroupName,
            string resourceProviderNamespace,
            string resourceType,
            string resourceName,
            string nestedResourceType,
            string nestedResourceName = null)
        {
            return string.Format(
                "/subscriptions/{0}/resourceGroups/{1}/providers/{2}/{3}/{4}/{5}/{6}",
                subscriptionId,
                resourceGroupName,
                resourceProviderNamespace,
                resourceType,
                resourceName,
                nestedResourceType,
                nestedResourceName);
        }

        /// <summary>
        /// Gets the management group scope.
        /// </summary>
        /// <param name="managementGroupId">the management group id</param>
        public static string GetManagementGroupScope(string managementGroupId)
        {
            return string.Format(format: "/providers/Microsoft.Management/managementGroups/{0}", arg0: managementGroupId);
        }

        /// <summary>
        /// Gets the explicit identity resource request URI.
        /// </summary>
        /// <param name="testSubscriptionId">The test subscription identifier.</param>
        /// <param name="testResourceGroupName">Name of the test resource group.</param>
        /// <param name="identityName">The identity name.</param>
        public static string GetExplicitIdentityRequestUri(string testSubscriptionId, string testResourceGroupName, string identityName)
        {
            return string.Format(
                format: "/subscriptions/{0}/resourceGroups/{1}/providers/Microsoft.ManagedIdentity/userAssignedIdentities/{2}",
                arg0: testSubscriptionId,
                arg1: testResourceGroupName,
                arg2: identityName);
        }

        /// <summary>
        /// Gets the secret URI of a proxy resource identity.
        /// </summary>
        /// <param name="uri">The MSI endpoint URI.</param>
        /// <param name="testSubscriptionId">The test subscription identifier.</param>
        /// <param name="testResourceGroupName">Name of the test resource group.</param>
        /// <param name="identityName">Name of the identity resource.</param>
        public static string GetProxyResourceIdentitySecretUri(Uri uri, string testSubscriptionId, string testResourceGroupName, string identityName)
        {
            return string.Format(
                "{0}subscriptions/{1}/resourceGroups/{2}/providers/Microsoft.ManagedIdentity/Identities/{3}",
                uri,
                testSubscriptionId,
                testResourceGroupName,
                identityName);
        }

        /// <summary>
        /// Gets the URI to validate a DeployIfNotExists policy assignment deployment.
        /// </summary>
        /// <param name="subscriptionId">The test subscription Id.</param>
        /// <param name="resourceGroup">The resource group.</param>
        /// <param name="apiVersion">The API version.</param>
        public static string GetPolicyDeploymentValidateUri(string subscriptionId, string resourceGroup, string apiVersion = "2018-05-01")
        {
            return string.Format(
                "{0}/subscriptions/{1}/resourcegroups/{2}/providers/microsoft.authorization/validatePolicyDeployment?api-version={3}",
                TestEnvironment.ShimProviderUri,
                subscriptionId,
                resourceGroup,
                string.IsNullOrEmpty(apiVersion) ? TestEnvironment.DefaultPolicyApiVersion : apiVersion);
        }

        /// <summary>
        /// Gets the URI to trigger a DeployIfNotExists policy assignment deployment.
        /// </summary>
        /// <param name="subscriptionId">The test subscription Id.</param>
        /// <param name="resourceGroup">The resource group.</param>
        /// <param name="apiVersion">The API version.</param>
        public static string GetPolicyDeploymentTriggerUri(string subscriptionId, string resourceGroup, string apiVersion = "2018-05-01")
        {
            return string.Format(
                "{0}/subscriptions/{1}/resourcegroups/{2}/providers/microsoft.authorization/triggerPolicyDeployment?api-version={3}",
                TestEnvironment.ShimProviderUri,
                subscriptionId,
                resourceGroup,
                string.IsNullOrEmpty(apiVersion) ? TestEnvironment.DefaultPolicyApiVersion : apiVersion);
        }

        /// <summary>
        /// Gets the URI to trigger policy data cleanup.
        /// </summary>
        /// <param name="scope">The scope to clean up.</param>
        /// <param name="runInBackground">A value indicating whether the cleanup should execute asynchronously.</param>
        /// <param name="apiVersion">The API version</param>
        public static string GetPolicyDataCleanupActionUri(string scope, bool runInBackground = false, string apiVersion = "2018-05-01")
        {
            var result = string.Format("{0}{1}/providers/Microsoft.Authorization/PolicyDataCleanup?api-version={2}", TestEnvironment.ShimProviderUri, scope, apiVersion);
            return result + (runInBackground ? "&RunInBackground=true" : string.Empty);
        }

        /// <summary>
        /// Generates the subscription notification for the front door.
        /// </summary>
        /// <param name="subscriptionId">The subscription id.</param>
        /// <param name="subscriptionState">The subscription state to be notified.</param>
        public static void GenerateSubscriptionNotification(string subscriptionId, SubscriptionState subscriptionState)
        {
            using (RequestCorrelationContext.Current.Initialize(
                subscriptionId: subscriptionId,
                clientIpAddress: "127.0.0.1",
                clientRequestId: Guid.NewGuid().ToString(),
                apiVersion: TestEnvironment.DefaultApiVersion,
                userAgent: "csm-cit"))
            {
                JobsDataProvider jobsDataProvider = FrontdoorConfigurationLoader.PrimaryFrontdoorConfiguration.DataProviders.GetJobsDataProvider(
                    FrontdoorConfigurationLoader.PrimaryFrontdoorConfiguration.FrontdoorLocation);

                ISubscriptionDataProvider subscriptionDataProvider = FrontdoorConfigurationLoader.PrimaryFrontdoorConfiguration.DataProviders.GetSubscriptionDataProvider(
                    FrontdoorConfigurationLoader.PrimaryFrontdoorConfiguration.FrontdoorLocation);

                string connectionString = TestEnvironment.TestStorageAccountConnectionString;

                // This job is not needed for the CIT. Adding to show the pattern that will be followed in admin role.
                jobsDataProvider
                    .CreateSubscriptionNotificationJob(
                        subscriptionId: subscriptionId,
                        locationAffinity: FrontdoorConfigurationLoader.PrimaryFrontdoorConfiguration.FrontdoorLocation,
                        delayJobStart: true)
                    .Wait();

                Subscription subscription = subscriptionDataProvider.FindSubscription(subscriptionId: subscriptionId).Result
                    ?? new Subscription { SubscriptionId = subscriptionId };

                subscription.State = subscriptionState;
                TestEnvironment.SaveSubscription(subscription);

                jobsDataProvider
                    .CreateSubscriptionNotificationJob(
                        subscriptionId: subscriptionId,
                        locationAffinity: FrontdoorConfigurationLoader.PrimaryFrontdoorConfiguration.FrontdoorLocation,
                        delayJobStart: false)
                    .Wait();
            }
        }

        #region Deployment request URLs

        /// <summary>
        /// Gets the deployment request URI.
        /// </summary>
        /// <param name="testSubscriptionId">The test subscription identifier.</param>
        /// <param name="testDeploymentName">Name of the test deployment.</param>
        /// <param name="apiVersion"><c>Api</c> version of the query.</param>
        public static string GetSubscriptionDeploymentRequestUri(string testSubscriptionId, string testDeploymentName, string apiVersion)
        {
            return string.Format(
                "{0}subscriptions/{1}/providers/Microsoft.Resources/deployments/{2}?api-version={3}",
                TestEnvironment.FrontdoorServiceUri,
                testSubscriptionId,
                testDeploymentName,
                apiVersion);
        }

        /// <summary>
        /// Gets the deployment request URI.
        /// </summary>
        /// <param name="testSubscriptionId">The test subscription identifier.</param>
        /// <param name="testResourceGroupName">Name of the test resource group.</param>
        /// <param name="testDeploymentName">Name of the test deployment.</param>
        /// <param name="apiVersion"><c>Api</c> version of the query.</param>
        public static string GetDeploymentRequestUri(string testSubscriptionId, string testResourceGroupName, string testDeploymentName, string apiVersion = null)
        {
            apiVersion = apiVersion ?? TestEnvironment.DefaultApiVersion;
            var format = apiVersion.IsGreaterThan("2014-04-01-preview")
                   ? "{0}subscriptions/{1}/resourcegroups/{2}/providers/Microsoft.Resources/deployments/{3}?api-version={4}"
                   : "{0}subscriptions/{1}/resourcegroups/{2}/deployments/{3}?api-version={4}";
            return string.Format(
                format,
                TestEnvironment.FrontdoorServiceUri,
                testSubscriptionId,
                testResourceGroupName,
                testDeploymentName,
                apiVersion);
        }

        /// <summary>
        /// Gets the subscription deployments request URI.
        /// </summary>
        /// <param name="testSubscriptionId">The test subscription identifier.</param>
        /// <param name="apiVersion">The API version.</param>
        public static string GetSubscriptionDeploymentsRequestUri(string testSubscriptionId, string apiVersion)
        {
            return string.Format(
                "{0}subscriptions/{1}/providers/Microsoft.Resources/deployments?api-version={2}",
                TestEnvironment.FrontdoorServiceUri,
                testSubscriptionId,
                apiVersion);
        }

        /// <summary>
        /// Gets the subscription deployment operations request URI.
        /// </summary>
        /// <param name="testSubscriptionId">The test subscription identifier.</param>
        /// <param name="testDeploymentName">Name of the test deployment.</param>
        /// <param name="provisioningState">State of the provisioning.</param>
        /// <param name="apiVersion">The API version.</param>
        public static string GetSubscriptionDeploymentOperationsRequestUri(string testSubscriptionId, string testDeploymentName, string provisioningState = null, string apiVersion = null)
        {
            return string.Format(
                "{0}subscriptions/{1}/providers/Microsoft.Resources/deployments/{2}/operations?{3}api-version={4}",
                TestEnvironment.FrontdoorServiceUri,
                testSubscriptionId,
                testDeploymentName,
                provisioningState != null ? string.Format("$filter=provisioningState eq '{0}'&", provisioningState) : string.Empty,
                apiVersion ?? TestEnvironment.DefaultApiVersion);
        }

        /// <summary>
        /// Gets the deployment request URI.
        /// </summary>
        /// <param name="testSubscriptionId">The test subscription identifier.</param>
        /// <param name="testResourceGroupName">Name of the test resource group.</param>
        /// <param name="testDeploymentName">Name of the test deployment.</param>
        /// <param name="apiVersion"><c>Api</c> version of the query.</param>
        public static string GetDeploymentRedeployUri(string testSubscriptionId, string testResourceGroupName, string testDeploymentName, string apiVersion = null)
        {
            apiVersion = apiVersion ?? TestEnvironment.DefaultApiVersion;
            var format = "{0}subscriptions/{1}/resourcegroups/{2}/providers/Microsoft.Resources/deployments/{3}/redeploy?api-version={4}";
            return string.Format(
                format,
                TestEnvironment.FrontdoorServiceUri,
                testSubscriptionId,
                testResourceGroupName,
                testDeploymentName,
                apiVersion);
        }

        /// <summary>
        /// Gets the deployment operations request URI.
        /// </summary>
        /// <param name="testSubscriptionId">The test subscription identifier.</param>
        /// <param name="testResourceGroupName">Name of the test resource group.</param>
        /// <param name="testDeploymentName">Name of the test deployment.</param>
        /// <param name="provisioningState">State of the provisioning.</param>
        /// <param name="apiVersion">The API version.</param>
        public static string GetDeploymentOperationsRequestUri(string testSubscriptionId, string testResourceGroupName, string testDeploymentName, string provisioningState = null, string apiVersion = null)
        {
            return string.Format(
                "{0}subscriptions/{1}/resourcegroups/{2}/deployments/{3}/operations?{4}api-version={5}",
                TestEnvironment.FrontdoorServiceUri,
                testSubscriptionId,
                testResourceGroupName,
                testDeploymentName,
                provisioningState != null ? string.Format("$filter=provisioningState eq '{0}'&", provisioningState) : string.Empty,
                apiVersion ?? TestEnvironment.DefaultApiVersion);
        }

        /// <summary>
        /// Gets the deployment operation request URI.
        /// </summary>
        /// <param name="testSubscriptionId">The test subscription identifier.</param>
        /// <param name="testDeploymentName">Name of the test deployment.</param>
        /// <param name="operationId">The operation identifier.</param>
        /// <param name="apiVersion"><c>Api</c> version of the query.</param>
        public static string GetSubscriptionDeploymentOperationRequestUri(string testSubscriptionId, string testDeploymentName, string operationId, string apiVersion)
        {
            return string.Format(
                "{0}subscriptions/{1}/providers/Microsoft.Resources/deployments/{2}/operations/{3}?api-version={4}",
                TestEnvironment.FrontdoorServiceUri,
                testSubscriptionId,
                testDeploymentName,
                operationId,
                apiVersion);
        }

        /// <summary>
        /// Gets the deployment operation request URI.
        /// </summary>
        /// <param name="testSubscriptionId">The test subscription identifier.</param>
        /// <param name="testResourceGroupName">Name of the test resource group.</param>
        /// <param name="testDeploymentName">Name of the test deployment.</param>
        /// <param name="operationId">The operation identifier.</param>
        /// <param name="apiVersion"><c>Api</c> version of the query.</param>
        public static string GetDeploymentOperationRequestUri(string testSubscriptionId, string testResourceGroupName, string testDeploymentName, string operationId, string apiVersion = null)
        {
            apiVersion = apiVersion ?? TestEnvironment.DefaultApiVersion;
            var format = apiVersion.IsGreaterThan("2014-04-01-preview")
                ? "{0}subscriptions/{1}/resourceGroups/{2}/providers/Microsoft.Resources/deployments/{3}/operations/{4}?api-version={5}"
                : "{0}subscriptions/{1}/resourceGroups/{2}/deployments/{3}/operations/{4}?api-version={5}";
            return string.Format(
                format,
                TestEnvironment.FrontdoorServiceUri,
                testSubscriptionId,
                testResourceGroupName,
                testDeploymentName,
                operationId,
                apiVersion);
        }

        /// <summary>
        /// Gets the deployment request URI.
        /// </summary>
        /// <param name="testSubscriptionId">The test subscription identifier.</param>
        /// <param name="testResourceGroupName">Name of the test resource group.</param>
        /// <param name="filter">The provided filter for the request.</param>
        public static string GetResourceGroupDeploymentsFilterUri(string testSubscriptionId, string testResourceGroupName, string filter = "")
        {
            return string.Format(
                "{0}subscriptions/{1}/resourcegroups/{2}/deployments?{3}api-version={4}",
                TestEnvironment.FrontdoorServiceUri,
                testSubscriptionId,
                testResourceGroupName,
                filter,
                TestEnvironment.DefaultApiVersion);
        }

        /// <summary>
        /// Gets the deployment request URI.
        /// </summary>
        /// <param name="testSubscriptionId">The test subscription identifier.</param>
        /// <param name="testResourceGroupName">Name of the test resource group.</param>
        /// <param name="provisioningState">Provisioning state deployment.</param>
        public static string GetResourceGroupDeploymentsUri(string testSubscriptionId, string testResourceGroupName, string provisioningState)
        {
            return TestEnvironment.GetResourceGroupDeploymentsFilterUri(
                testSubscriptionId,
                testResourceGroupName,
                string.Format("$filter=provisioningState eq '{0}'&", provisioningState));
        }

        /// <summary>
        /// Gets the deployment cancellation request URI.
        /// </summary>
        /// <param name="testSubscriptionId">The test subscription identifier.</param>
        /// <param name="testDeploymentName">Name of the test deployment.</param>
        /// <param name="apiVersion">The API version.</param>
        public static string GetSubscriptionDeploymentCancellationRequestUri(string testSubscriptionId, string testDeploymentName, string apiVersion)
        {
            return string.Format(
                "{0}subscriptions/{1}/providers/Microsoft.Resources/deployments/{2}/cancel?api-version={3}",
                TestEnvironment.FrontdoorServiceUri,
                testSubscriptionId,
                testDeploymentName,
                apiVersion);
        }

        /// <summary>
        /// Gets the deployment cancellation request URI.
        /// </summary>
        /// <param name="testSubscriptionId">The test subscription identifier.</param>
        /// <param name="testResourceGroupName">Name of the test resource group.</param>
        /// <param name="testDeploymentName">Name of the test deployment.</param>
        public static string GetDeploymentCancellationRequestUri(string testSubscriptionId, string testResourceGroupName, string testDeploymentName)
        {
            return string.Format(
                "{0}subscriptions/{1}/resourcegroups/{2}/deployments/{3}/cancel?api-version={4}",
                TestEnvironment.FrontdoorServiceUri,
                testSubscriptionId,
                testResourceGroupName,
                testDeploymentName,
                TestEnvironment.DefaultApiVersion);
        }

        /// <summary>
        /// Gets the subscription level deployment validation request URI.
        /// </summary>
        /// <param name="testSubscriptionId">The test subscription identifier.</param>
        /// <param name="testDeploymentName">Name of the test deployment.</param>
        /// <param name="apiVersion">The API version.</param>
        public static string GetSubscriptionDeploymentValidationRequestUri(string testSubscriptionId, string testDeploymentName, string apiVersion)
        {
            return string.Format(
                "{0}subscriptions/{1}/providers/Microsoft.Resources/deployments/{2}/validate?api-version={3}",
                TestEnvironment.FrontdoorServiceUri,
                testSubscriptionId,
                testDeploymentName,
                apiVersion);
        }

        /// <summary>
        /// Gets the deployment validation request URI.
        /// </summary>
        /// <param name="testSubscriptionId">The test subscription identifier.</param>
        /// <param name="testResourceGroupName">Name of the test resource group.</param>
        /// <param name="testDeploymentName">Name of the test deployment.</param>
        /// <param name="apiVersion">The API version.</param>
        public static string GetDeploymentValidationRequestUri(string testSubscriptionId, string testResourceGroupName, string testDeploymentName, string apiVersion = null)
        {
            return string.Format(
                "{0}subscriptions/{1}/resourcegroups/{2}/deployments/{3}/validate?api-version={4}",
                TestEnvironment.FrontdoorServiceUri,
                testSubscriptionId,
                testResourceGroupName,
                testDeploymentName,
                apiVersion ?? TestEnvironment.DefaultApiVersion);
        }

        /// <summary>
        /// Gets the deployment export template request URI.
        /// </summary>
        /// <param name="testSubscriptionId">The test subscription identifier.</param>
        /// <param name="testDeploymentName">Name of the test deployment.</param>
        /// <param name="apiVersion">The API version.</param>
        public static string GetSubscriptionDeploymentExportTemplateRequestUri(string testSubscriptionId, string testDeploymentName, string apiVersion)
        {
            return string.Format(
                "{0}subscriptions/{1}/providers/Microsoft.Resources/deployments/{2}/exportTemplate?api-version={3}",
                TestEnvironment.FrontdoorServiceUri,
                testSubscriptionId,
                testDeploymentName,
                apiVersion);
        }

        /// <summary>
        /// Gets the deployment export template request URI.
        /// </summary>
        /// <param name="testSubscriptionId">The test subscription identifier.</param>
        /// <param name="testResourceGroupName">Name of the test resource group.</param>
        /// <param name="testDeploymentName">Name of the test deployment.</param>
        /// <param name="apiVersion">The API version.</param>
        /// <param name="includeNamespace">Includes "Microsoft.Resource" namespace.</param>
        public static string GetDeploymentExportTemplateRequestUri(string testSubscriptionId, string testResourceGroupName, string testDeploymentName, string apiVersion = null, bool includeNamespace = false)
        {
            return string.Format(
                "{0}subscriptions/{1}/resourcegroups/{2}/{3}deployments/{4}/exportTemplate?api-version={5}",
                TestEnvironment.FrontdoorServiceUri,
                testSubscriptionId,
                testResourceGroupName,
                includeNamespace ? "providers/Microsoft.Resources/" : string.Empty,
                testDeploymentName,
                apiVersion ?? TestEnvironment.DefaultApiVersion);
        }

        /// <summary>
        /// Gets the template request URI.
        /// </summary>
        /// <param name="templateId">Name of the test deployment.</param>
        /// <param name="endpoint">The endpoint.</param>
        public static string GetTemplateRequestUri(string templateId, string endpoint = null)
        {
            return string.Format(
                "{0}templates/{1}?api-version={2}",
                endpoint ?? TestEnvironment.ResourceProviderServiceUri.ToString(),
                templateId,
                TestEnvironment.DefaultApiVersion);
        }

        /// <summary>
        /// Gets the parameter request URI.
        /// </summary>
        /// <param name="templateId">Name of the test deployment.</param>
        /// <param name="endpoint">The endpoint.</param>
        public static string GetParameterRequestUri(string templateId, string endpoint = null)
        {
            return string.Format(
                "{0}parameters/{1}?api-version={2}",
                endpoint ?? TestEnvironment.ResourceProviderServiceUri.ToString(),
                templateId,
                TestEnvironment.DefaultApiVersion);
        }

        /// <summary>
        /// Gets the manifest request URI.
        /// </summary>
        /// <param name="resourceType">Type of the resource.</param>
        /// <param name="endpoint">The endpoint.</param>
        public static string GetManifestRequestUri(string resourceType, string endpoint = null)
        {
            return string.Format(
                "{0}manifest/{1}?api-version={2}",
                endpoint ?? TestEnvironment.ResourceProviderServiceUri.ToString(),
                resourceType,
                TestEnvironment.DefaultApiVersion);
        }

        /// <summary>
        /// Gets the data aliases request URI
        /// </summary>
        /// <param name="dataNamespace">The data manifest namespace.</param>
        public static string GetDataAliasesRequestUri(string dataNamespace = null)
        {
            if (string.IsNullOrWhiteSpace(dataNamespace))
            {
                return string.Format(
                    "{0}/providers/microsoft.authorization/dataAliases?api-version={1}",
                    TestEnvironment.ShimProviderUri,
                    TestEnvironment.DataAliasesApiVersion);
            }

            return string.Format(
                "{0}/providers/microsoft.authorization/dataAliases?api-version={1}&$filter=dataNamespace eq '{2}'",
                TestEnvironment.ShimProviderUri,
                TestEnvironment.DataAliasesApiVersion,
                dataNamespace);
        }

        #endregion

        #region BJS helper

        /// <summary>
        /// Reset background jobs trigger queue.
        /// </summary>
        /// <param name="queuePrefix">The queue prefix.</param>
        public static void ResetBackgroundJobTriggers(string queuePrefix = null)
        {
            var backgroundJobQueuePrefix = queuePrefix ?? CloudConfigurationManager.GetConfiguration(
                settingName: "Microsoft.WindowsAzure.ResourceStack.BackgroundJobs.JobTriggersQueuePrefix",
                defaultValue: "jobtriggers");

            var queueClient = TestEnvironment.GetTestStorageAccount().CreateCloudQueueClient();
            Task.WaitAll(queueClient.ListQueues(prefix: backgroundJobQueuePrefix).SelectArray(q => q.ClearAsync()));
        }

        /// <summary>
        /// Reset all the data that may exist in table storage.
        /// </summary>
        public static void ResetTestData()
        {
            var tableClient = TestEnvironment.GetTestStorageAccount().CreateCloudTableClient();
            tableClient.ListTables().ForEach(t => StorageArtifactsHelper.DeleteTableIfExistAsync(tableClient, t.Name, FrontdoorConfigurationLoader.PrimaryFrontdoorConfiguration.EventSource).Wait());
            var queueClient = TestEnvironment.GetTestStorageAccount().CreateCloudQueueClient();
            queueClient.ListQueues().ForEach(q => StorageArtifactsHelper.DeleteQueueIfExistAsync(queueClient, q.Name, FrontdoorConfigurationLoader.PrimaryFrontdoorConfiguration.EventSource).Wait());
        }

        /// <summary>
        /// Clears resource hydration queue.
        /// </summary>
        public static void ResetResourceHydrationQueue()
        {
            var queue = TestEnvironment
                .GetTestStorageAccount()
                .CreateCloudQueueClient()
                .GetQueueReference(queueName: TestEnvironment.ResourceHydrationQueueName);

            if (queue.Exists())
            {
                queue.Clear();
            }
        }

        /// <summary>
        /// Clears event grid queue.
        /// </summary>
        public static void ResetEventGridQueue()
        {
            var queue = TestEnvironment
                .GetTestStorageAccount()
                .CreateCloudQueueClient()
                .GetQueueReference(queueName: TestEnvironment.EventGridDataQueueName);

            if (queue.Exists())
            {
                queue.Clear();
            }
        }

        /// <summary>
        /// Resets test linked notification events queue.
        /// </summary>
        public static void ResetLinkedNotificationEventsQueue()
        {
            var queue = TestEnvironment
                .GetTestStorageAccount()
                .CreateCloudQueueClient()
                .GetQueueReference(queueName: TestEnvironment.LinkedNotificationQueueName);

            if (queue.Exists())
            {
                queue.Clear();
            }
        }

        #endregion

        #region SKU

        /// <summary>
        /// Creates Test SKU.
        /// </summary>
        /// <param name="quota">The required quota.</param>
        /// <param name="feature">The feature.</param>
        /// <param name="location">The locations.</param>
        public static SkuSetting CreateTestSku(string quota = null, string feature = null, string location = ResourceProviderTestHostRegistration.TestLocation)
        {
            return TestEnvironment.CreateTestSkuWithMultipleLocations(
                quota: quota,
                feature: feature,
                locations: location.AsArray());
        }

        /// <summary>
        /// Creates Test SKU.
        /// </summary>
        /// <param name="locations">The locations.</param>
        /// <param name="quota">The required quota.</param>
        /// <param name="feature">The feature.</param>
        public static SkuSetting CreateTestSkuWithMultipleLocations(string[] locations, string quota = null, string feature = null)
        {
            return JObject.FromObject(new
            {
                Name = Guid.NewGuid().ToString(),
                Tier = Guid.NewGuid().ToString(),
                Size = Guid.NewGuid().ToString(),
                Family = Guid.NewGuid().ToString(),
                Kind = Guid.NewGuid().ToString(),
                Capacity = new SkuCapacity
                {
                    ScaleType = SkuScaleType.Manual,
                    Minimum = 0,
                    Maximum = 2,
                    Default = 1,
                },

                Locations = locations,
                Costs = new[]
                {
                    new SkuCost
                    {
                        MeterId = Guid.NewGuid().ToString(),
                        Quantity = 100,
                        ExtendedUnit = Guid.NewGuid().ToString(),
                    }
                },
                Capabilities = new[]
                {
                    new SkuCapability
                    {
                        Name = Guid.NewGuid().ToString(),
                        Value = Guid.NewGuid().ToString(),
                    },
                    new SkuCapability
                    {
                        Name = Guid.NewGuid().ToString(),
                        Value = Guid.NewGuid().ToString(),
                    }
                },
                RequiredQuotaIds = !string.IsNullOrEmpty(quota) ? quota.AsArray() : null,
                RequiredFeatures = !string.IsNullOrEmpty(feature) ? feature.AsArray() : null
            }).ToObject<SkuSetting>();
        }

        #endregion
    }
}