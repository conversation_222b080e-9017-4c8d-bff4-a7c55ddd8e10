﻿//-----------------------------------------------------------
// Copyright (c) Microsoft Corporation.  All rights reserved.
//-----------------------------------------------------------

namespace Internal.WindowsAzure.ResourceStack.Tests.Frontdoor.Common
{
    using System;
    using Microsoft.WindowsAzure.ResourceStack.Common.EventSources;
    using Microsoft.WindowsAzure.ResourceStack.Common.Extensions;
    using Microsoft.WindowsAzure.ResourceStack.Frontdoor.Common.Logging;    

    /// <summary>
    /// Class to add limited-scope usage of test events.
    /// </summary>
    /// <remarks>
    /// This should be used before any creation of front door instances.
    /// </remarks>
    public class TestEventsRegistrationScope : IDisposable
    {
        /// <summary>
        /// Initializes a new instance of the <see cref="TestEventsRegistrationScope" /> class.
        /// </summary>
        /// <param name="eventHost">The event host.</param>
        /// <param name="frontDoorEvents">The front door events.</param>
        public TestEventsRegistrationScope(TestEventsListenerHost eventHost, params FrontdoorEvents[] frontDoorEvents)
            : this(eventHost, frontDoorEvents: frontDoorEvents, commonEvents: null)
        {
        }

        /// <summary>
        /// Initializes a new instance of the <see cref="TestEventsRegistrationScope" /> class.
        /// </summary>
        /// <param name="frontDoorEvents">The front door events.</param>
        public TestEventsRegistrationScope(params FrontdoorEvents[] frontDoorEvents)
            : this(TestEventsListenerHost.Instance, frontDoorEvents: frontDoorEvents, commonEvents: null)
        {
        }

        /// <summary>
        /// Initializes a new instance of the <see cref="TestEventsRegistrationScope" /> class.
        /// </summary>
        /// <param name="commonEvents">The common event.</param>
        public TestEventsRegistrationScope(params CommonEventSourceEvents[] commonEvents)
            : this(TestEventsListenerHost.Instance, frontDoorEvents: null, commonEvents: commonEvents)
        {
        }

        /// <summary>
        /// Initializes a new instance of the <see cref="TestEventsRegistrationScope" /> class.
        /// </summary>
        /// <param name="eventHost">The event host.</param>
        /// <param name="frontDoorEvents">The front door events.</param>
        /// <param name="commonEvents">The common event.</param>
        public TestEventsRegistrationScope(TestEventsListenerHost eventHost, FrontdoorEvents[] frontDoorEvents, CommonEventSourceEvents[] commonEvents = null)
        {
            this.FrontDoorEvents = frontDoorEvents ?? new FrontdoorEvents[0];
            this.CommonEvents = commonEvents ?? new CommonEventSourceEvents[0];
            this.EventHost = eventHost;
            this.EventHost.EnsureInitialized();

            FrontdoorLog.Initialize(EventSourceFactory.GetEventSource<IFrontdoorEventSource>(typeof(FrontdoorLog).Assembly));

            this.FrontDoorEvents.ForEach(eventId => this.EventHost.RegisterEventId((int)eventId));
            this.CommonEvents.ForEach(eventId => this.EventHost.RegisterEventId((int)eventId));
        }

        /// <summary>
        /// Gets the event host to be used for the scope.
        /// </summary>
        public TestEventsListenerHost EventHost { get; private set; }

        /// <summary>
        /// Gets or sets the event ids in this scope.
        /// </summary>
        private CommonEventSourceEvents[] CommonEvents { get; set; }

        /// <summary>
        /// Gets or sets the event ids in this scope.
        /// </summary>
        private FrontdoorEvents[] FrontDoorEvents { get; set; }

        /// <summary>
        /// Disposes the scope and unregisters the event Ids
        /// </summary>
        public void Dispose()
        {
            this.FrontDoorEvents.ForEach(eventId => this.EventHost.UnregisterEventId((int)eventId));
            this.CommonEvents.ForEach(eventId => this.EventHost.UnregisterEventId((int)eventId));
        }
    }
}