﻿//-----------------------------------------------------------
// Copyright (c) Microsoft Corporation.  All rights reserved.
//-----------------------------------------------------------

namespace Internal.WindowsAzure.ResourceStack.Tests.Frontdoor.Common
{
    using System;
    using System.Collections.Generic;
    using System.Linq;
    using System.Net;
    using System.Net.Http;
    using System.Web.Http;
    using System.Web.Http.Controllers;
    using System.Web.Http.Dependencies;
    using System.Web.Http.Dispatcher;
    using System.Web.Http.Routing;
    using Internal.WindowsAzure.ResourceStack.Tests.Common;
    using Microsoft.WindowsAzure.ResourceStack.Common.Collections;
    using Microsoft.WindowsAzure.ResourceStack.Common.Extensions;
    using Microsoft.WindowsAzure.ResourceStack.Common.Json;
    using Microsoft.WindowsAzure.ResourceStack.Frontdoor.Data.Entities.APIValidation;
    using Newtonsoft.Json.Linq;

    /// <summary>
    /// Delegate used for listening to response validation requests.
    /// </summary>
    /// <param name="request">The incoming validation request.</param>
    /// <param name="requestBody">The incoming request body.</param>
    public delegate HttpResponseMessage APIValidateDelegate(HttpRequestMessage request, JObject requestBody);

    /// <summary>
    /// Delegate used for get safe properties for API validation.
    /// </summary>
    /// <param name="request">The incoming validation request.</param>
    public delegate HttpResponseMessage GetSafePropertiesDelegate(HttpRequestMessage request);

    /// <summary>
    /// The web service host for the mock API validation service.
    /// </summary>
    public class APIValidationTestHost : ServiceTestHost<APIValidationTestHost.APIValidationTestHostServer>, IDisposable
    {
        /// <summary>
        /// Initializes a new instance of the <see cref="APIValidationTestHost"/> class.
        /// </summary>
        /// <param name="serviceUri">The service URI.</param>
        public APIValidationTestHost(Uri serviceUri)
            : base(serviceUri)
        {
            this.Start();
        }

        /// <summary>
        /// Performs application-defined tasks associated with freeing, releasing, or resetting unmanaged resources.
        /// </summary>
        public void Dispose()
        {
            this.Stop();
        }

        /// <summary>
        /// Gets the state of the test host.
        /// </summary>
        protected override object GetState
        {
            get { return this; }
        }

        /// <summary>
        /// Gets or sets the delegate called when validation request is received.
        /// </summary>
        public APIValidateDelegate OnValidateRequest { get; set; }

        /// <summary>
        /// Gets or sets the delegate called when get safe properties request is received.
        /// </summary>
        public GetSafePropertiesDelegate OnGetSafePropertiesRequest { get; set; }

        /// <summary>
        /// Called to configure http configuration.
        /// </summary>
        /// <param name="configuration">The http configuration.</param>
        private void OnConfigure(HttpConfiguration configuration)
        {
            configuration.Routes.MapHttpRoute(
               name: "ValidateRequest",
               routeTemplate: "validate",
               defaults: new
               {
                   controller = "APIValidation",
                   action = "Validate",
               });

            configuration.Routes.MapHttpRoute(
                name: "GetSafePropertiesRequest",
                routeTemplate: "allowlist",
                defaults: new { controller = "APIValidation", action = "Allowlist" },
                constraints: new { httpMethod = new HttpMethodConstraint(HttpMethod.Get) });

            configuration.Services.Replace(typeof(IHttpControllerSelector), new APIValidationTestHostControllerSelector(this, configuration));
            configuration.DependencyResolver = new APIValidationTestHostDependencyResolver(this, configuration);
        }

        /// <summary>
        /// The API validation request controller.
        /// </summary>
        public class APIValidationController : ApiController
        {
            /// <summary>
            /// The API validation test host.
            /// </summary>
            private readonly APIValidationTestHost host;

            /// <summary>
            /// Initializes a new instance of the <see cref="APIValidationController"/> class.
            /// </summary>
            /// <param name="host">The API validation host.</param>
            public APIValidationController(APIValidationTestHost host)
            {
                this.host = host;
            }

            /// <summary>
            /// Responds to a validation request.
            /// </summary>
            /// <param name="requestBody">Body of the request.</param>
            [HttpPost]
            public HttpResponseMessage Validate([FromBody] JObject requestBody)
            {
                if (this.host.OnValidateRequest != null)
                {
                    return this.host.OnValidateRequest(this.Request, requestBody);
                }

                return this.Request.CreateResponse(HttpStatusCode.OK);
            }

            /// <summary>
            /// Responds to a get safe properties request.
            /// </summary>
            [HttpGet]
            [ActionName("Allowlist")]
            public HttpResponseMessage GetSafeProperties()
            {
                if (this.host.OnGetSafePropertiesRequest != null)
                {
                    return this.host.OnGetSafePropertiesRequest(this.Request);
                }

                var responseBody = new APIValidationSafeProperties
                {
                    SafeProperties = new InsensitiveDictionary<string[]>()
                    {
                        { "ResourceProviderTestHost", new string[] { "name" } }
                    }
                };

                return this.Request.CreateResponse(
                    statusCode: HttpStatusCode.OK,
                    value: responseBody,
                    formatter: JsonExtensions.JsonMediaTypeFormatter);
            }
        }

        /// <summary>
        /// The API validation test host server.
        /// </summary>
        public class APIValidationTestHostServer : ServiceTestHostServer
        {
            /// <summary>
            /// Initializes a new instance of the <see cref="APIValidationTestHostServer"/> class.
            /// </summary>
            /// <param name="serviceUri">The service URI.</param>
            /// <param name="state">The instance state.</param>
            public APIValidationTestHostServer(Uri serviceUri, object state)
                : base(serviceUri, state)
            {
            }

            /// <summary>
            /// Called to configure http configuration.
            /// </summary>
            /// <param name="configuration">The http configuration.</param>
            /// <param name="state">The instance state.</param>
            protected override void OnConfigure(HttpConfiguration configuration, object state)
            {
                (state as APIValidationTestHost).OnConfigure(configuration);
            }
        }

        /// <summary>
        /// The API validation test host controller selector.
        /// </summary>
        private class APIValidationTestHostControllerSelector : DefaultHttpControllerSelector
        {
            /// <summary>
            /// The API validation test host.
            /// </summary>
            private readonly APIValidationTestHost host;

            /// <summary>
            /// The HTTP configuration.
            /// </summary>
            private readonly HttpConfiguration configuration;

            /// <summary>
            /// Initializes a new instance of the <see cref="APIValidationTestHostControllerSelector" /> class.
            /// </summary>
            /// <param name="host">The host.</param>
            /// <param name="configuration">The configuration.</param>
            public APIValidationTestHostControllerSelector(APIValidationTestHost host, HttpConfiguration configuration)
                : base(configuration)
            {
                this.host = host;
                this.configuration = configuration;
            }

            /// <summary>
            /// Selects the controller.
            /// </summary>
            /// <param name="request">The request.</param>
            public override HttpControllerDescriptor SelectController(HttpRequestMessage request)
            {
                var matchedTypes = typeof(APIValidationTestHost)
                    .GetNestedTypes()
                    .Where(type => typeof(IHttpController).IsAssignableFrom(type))
                    .ToList();

                var controllerName = this.GetControllerName(request);
                var matchedController = matchedTypes.FirstOrDefault(type => type.Name.EqualsInsensitively(controllerName + "controller"));

                return new HttpControllerDescriptor(this.configuration, controllerName, matchedController);
            }
        }

        /// <summary>
        /// The API validation test host dependency resolver.
        /// </summary>
        private class APIValidationTestHostDependencyResolver : IDependencyResolver
        {
            /// <summary>
            /// The API validation test host.
            /// </summary>
            private readonly APIValidationTestHost host;

            /// <summary>
            /// The HTTP configuration.
            /// </summary>
            private readonly HttpConfiguration configuration;

            /// <summary>
            /// Initializes a new instance of the <see cref="APIValidationTestHostDependencyResolver" /> class.
            /// </summary>
            /// <param name="host">The host.</param>
            /// <param name="configuration">The configuration.</param>
            public APIValidationTestHostDependencyResolver(APIValidationTestHost host, HttpConfiguration configuration)
            {
                this.host = host;
                this.configuration = configuration;
            }

            /// <summary>
            /// Begins the scope.
            /// </summary>
            public IDependencyScope BeginScope()
            {
                return this;
            }

            /// <summary>
            /// Gets the service.
            /// </summary>
            /// <param name="serviceType">Type of the service.</param>
            public object GetService(Type serviceType)
            {
                if (typeof(IHttpController).IsAssignableFrom(serviceType))
                {
                    if (typeof(APIValidationTestHost).GetNestedTypes().Any(type => type == serviceType))
                    {
                        return Activator.CreateInstance(serviceType, args: new object[] { this.host });
                    }
                }

                return null;
            }

            /// <summary>
            /// Gets the services.
            /// </summary>
            /// <param name="serviceType">Type of the service.</param>
            public IEnumerable<object> GetServices(Type serviceType)
            {
                return Enumerable.Empty<object>();
            }

            /// <summary>
            /// Performs application-defined tasks associated with freeing, releasing, or resetting unmanaged resources.
            /// </summary>
            public void Dispose()
            {
            }
        }
    }
}
