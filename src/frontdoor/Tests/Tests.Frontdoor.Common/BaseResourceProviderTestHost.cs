﻿//-----------------------------------------------------------
// Copyright (c) Microsoft Corporation.  All rights reserved.
//-----------------------------------------------------------

namespace Internal.WindowsAzure.ResourceStack.Tests.Frontdoor.Common
{
    using System;
    using System.Collections.Generic;
    using System.Diagnostics;
    using System.Linq;
    using System.Net;
    using System.Net.Http;
    using System.Net.Http.Formatting;
    using System.Net.Http.Headers;
    using System.Threading;
    using System.Threading.Tasks;
    using System.Web.Http;
    using System.Web.Http.Controllers;
    using System.Web.Http.Dependencies;
    using System.Web.Http.Dispatcher;
    using System.Web.Http.Filters;
    using System.Web.Http.Routing;
    using Internal.WindowsAzure.ResourceStack.Tests.Common;
    using Microsoft.WindowsAzure.ResourceStack.Common.Extensions;
    using Microsoft.WindowsAzure.ResourceStack.Common.Instrumentation;
    using Microsoft.WindowsAzure.ResourceStack.Frontdoor.Data.Definitions;
    using Microsoft.WindowsAzure.ResourceStack.Frontdoor.Data.Entities.Authorization;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Common.Data;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Common.Responses;
    using Newtonsoft.Json.Linq;
    using ResourceStackComponents = Microsoft.WindowsAzure.ResourceStack.Frontdoor.Data.Components;

    /// <summary>
    /// Delegate used for request authentication.
    /// </summary>
    /// <param name="request">The incoming http request.</param>
    /// <returns>Http response message.</returns>
    public delegate HttpResponseMessage AuthenticationHandlerDelegate(HttpRequestMessage request);

    /// <summary>
    /// Delegate used for getting authentication metadata.
    /// </summary>
    /// <param name="request">The incoming http request.</param>
    /// <returns>Http response message.</returns>
    public delegate HttpResponseMessage GetAuthenticationMetadataDelegate(HttpRequestMessage request);

    /// <summary>
    /// Delegate used for listening to top-level resource GETs.
    /// </summary>
    /// <param name="request">The incoming http request.</param>
    /// <param name="resourceProviderNamespace">The resource provider namespace.</param>
    /// <param name="resourceType">The type of the top-level resource.</param>
    /// <param name="resourceName">Name of top-level resource.</param>
    /// <param name="wildcard">Rest of provider-specific url.</param>
    /// <returns>Http response message.</returns>
    public delegate HttpResponseMessage GetTenantResourceDelegate(HttpRequestMessage request, string resourceProviderNamespace, string resourceType, string resourceName, string wildcard);

    /// <summary>
    /// Delegate used for listening to top-level resource GETs.
    /// </summary>
    /// <param name="request">The incoming http request.</param>
    /// <param name="resourceProviderNamespace">The resource provider namespace.</param>
    /// <param name="resourceType">The type of the top-level resource.</param>
    /// <param name="resourceName">Name of top-level resource.</param>
    /// <param name="wildcard">Rest of provider-specific url.</param>
    /// <returns>Http response message.</returns>
    public delegate HttpResponseMessage PutTenantResourceDelegate(HttpRequestMessage request, string resourceProviderNamespace, string resourceType, string resourceName, string wildcard);

    /// <summary>
    /// Delegate used for listening to top-level resource GETs.
    /// </summary>
    /// <param name="request">The incoming http request.</param>
    /// <param name="resourceProviderNamespace">The resource provider namespace.</param>
    /// <param name="resourceType">The type of the top-level resource.</param>
    /// <param name="resourceName">Name of top-level resource.</param>
    /// <param name="wildcard">Rest of provider-specific url.</param>
    /// <returns>Http response message.</returns>
    public delegate HttpResponseMessage DeleteTenantResourceDelegate(HttpRequestMessage request, string resourceProviderNamespace, string resourceType, string resourceName, string wildcard);

    /// <summary>
    /// Delegate used for listening to top-level resource GETs.
    /// </summary>
    /// <param name="request">The incoming http request.</param>
    /// <param name="subscriptionId">Subscription Id.</param>
    /// <param name="resourceGroupName">Resource group name.</param>
    /// <param name="resourceProviderNamespace">Resource provider namespace.</param>
    /// <param name="resourceType">The type of the top-level resource.</param>
    /// <param name="resourceName">Name of top-level resource.</param>
    /// <param name="wildcard">Rest of provider-specific url.</param>
    /// <returns>Http response message.</returns>
    public delegate HttpResponseMessage GetResourceDelegate(HttpRequestMessage request, string subscriptionId, string resourceGroupName, string resourceProviderNamespace, string resourceType, string resourceName, string wildcard);

    /// <summary>
    /// Delegate used for listening to top-level resource POSTs.
    /// </summary>
    /// <param name="request">The request.</param>
    /// <param name="subscriptionId">The subscription identifier.</param>
    /// <param name="resourceGroupName">Name of the resource group.</param>
    /// <param name="resourceProviderNamespace">The resource provider namespace.</param>
    /// <param name="resourceType">Type of the resource.</param>
    /// <param name="resourceName">Name of the resource.</param>
    /// <param name="actionName">Name of the action.</param>
    public delegate HttpResponseMessage ResourceActionDelegate(HttpRequestMessage request, string subscriptionId, string resourceGroupName, string resourceProviderNamespace, string resourceType, string resourceName, string actionName);

    /// <summary>
    /// Delegate used for listening to subscription level resource POSTs.
    /// </summary>
    /// <param name="request">The request.</param>
    /// <param name="subscriptionId">The subscription identifier.</param>
    /// <param name="resourceProviderNamespace">The resource provider namespace.</param>
    /// <param name="resourceType">Type of the resource.</param>
    /// <param name="resourceName">Name of the resource.</param>
    /// <param name="actionName">Name of the action.</param>
    public delegate HttpResponseMessage SubscriptionResourceActionDelegate(HttpRequestMessage request, string subscriptionId, string resourceProviderNamespace, string resourceType, string resourceName, string actionName);

    /// <summary>
    /// Delegate used for listening to request to get activity status of resource provider.
    /// </summary>
    /// <param name="request">The incoming http request.</param>
    /// <param name="resourceProviderNamespace">The resource provider namespace.</param>
    /// <param name="activityId">The activity Id.</param>
    public delegate HttpResponseMessage GetResourceProviderActivityStatusDelegate(HttpRequestMessage request, string resourceProviderNamespace, string activityId);

    /// <summary>
    /// Delegate used for notify resource provider.
    /// </summary>
    /// <param name="request">The incoming http request.</param>
    public delegate HttpResponseMessage NotifyProviderDelegate(HttpRequestMessage request);

    /// <summary>
    /// Delegate used for listening to resource level notifications.
    /// </summary>
    /// <param name="request">The incoming http request.</param>
    /// <param name="subscriptionId">Subscription Id.</param>
    /// <param name="resourceGroupName">Resource group name.</param>
    /// <param name="resourceProviderNamespace">Resource provider namespace.</param>
    /// <param name="resourceType">Resource type.</param>
    /// <param name="resourceName">Resource name.</param>
    /// <param name="notificationProviderNamespace">The notification provider namespace.</param>
    public delegate HttpResponseMessage NotifyProviderResourceDelegate(HttpRequestMessage request, string subscriptionId, string resourceGroupName, string resourceProviderNamespace, string resourceType, string resourceName, string notificationProviderNamespace);

    /// <summary>
    /// Delegate used for listening to subscription level resource notifications.
    /// </summary>
    /// <param name="request">The incoming http request.</param>
    /// <param name="subscriptionId">Subscription Id.</param>
    /// <param name="resourceProviderNamespace">Resource provider namespace.</param>
    /// <param name="resourceType">Resource type.</param>
    /// <param name="resourceName">Resource name.</param>
    /// <param name="notificationProviderNamespace">The notification provider namespace.</param>
    public delegate HttpResponseMessage NotifyProviderSubscriptionLevelResourceDelegate(HttpRequestMessage request, string subscriptionId, string resourceProviderNamespace, string resourceType, string resourceName, string notificationProviderNamespace);

    /// <summary>
    /// Delegate used for listening to tenant level resource notifications.
    /// </summary>
    /// <param name="request">The incoming http request.</param>
    /// <param name="resourceProviderNamespace">Resource provider namespace.</param>
    /// <param name="resourceType">Resource type.</param>
    /// <param name="resourceName">Resource name.</param>
    /// <param name="notificationProviderNamespace">The notification provider namespace.</param>
    public delegate HttpResponseMessage NotifyProviderTenantLevelResourceDelegate(HttpRequestMessage request, string resourceProviderNamespace, string resourceType, string resourceName, string notificationProviderNamespace);

    /// <summary>
    /// Delegate used for listening to resource level notifications.
    /// </summary>
    /// <param name="request">The incoming http request.</param>
    /// <param name="subscriptionId">Subscription Id.</param>
    /// <param name="resourceGroupName">Resource group name.</param>
    /// <param name="resourceProviderNamespace">Resource provider namespace.</param>
    /// <param name="resourceType">Resource type.</param>
    /// <param name="resourceName">Resource name.</param>
    /// <param name="extensionProviderNamespace">Extension provider namespace.</param>
    /// <param name="extensionResourceType">Extension resource type.</param>
    /// <param name="extensionResourceName">Extension resource name.</param>
    /// <param name="notificationProviderNamespace">The notification provider namespace.</param>
    public delegate HttpResponseMessage NotifyProviderExtensionResourceDelegate(HttpRequestMessage request, string subscriptionId, string resourceGroupName, string resourceProviderNamespace, string resourceType, string resourceName, string extensionProviderNamespace, string extensionResourceType, string extensionResourceName, string notificationProviderNamespace);

    /// <summary>
    /// Delegate used for listening to nested-level resource POSTs.
    /// </summary>
    /// <param name="request">The request.</param>
    /// <param name="subscriptionId">The subscription identifier.</param>
    /// <param name="resourceGroupName">Name of the resource group.</param>
    /// <param name="resourceProviderNamespace">The resource provider namespace.</param>
    /// <param name="resourceType">Type of the resource.</param>
    /// <param name="resourceName">Name of the resource.</param>
    /// <param name="nestedResourceType">Type of the nested resource.</param>
    /// <param name="nestedResourceName">Name of the nested resource.</param>
    /// <param name="actionName">Name of the action.</param>
    public delegate HttpResponseMessage NestedResourceActionDelegate(HttpRequestMessage request, string subscriptionId, string resourceGroupName, string resourceProviderNamespace, string resourceType, string resourceName, string nestedResourceType, string nestedResourceName, string actionName);

    /// <summary>
    /// Delegate used for listening to nested resource level notifications.
    /// </summary>
    /// <param name="request">The incoming http request.</param>
    /// <param name="subscriptionId">Subscription Id.</param>
    /// <param name="resourceGroupName">Resource group name.</param>
    /// <param name="resourceProviderNamespace">Resource provider namespace.</param>
    /// <param name="resourceType">Resource type.</param>
    /// <param name="resourceName">Resource name.</param>
    /// <param name="resourceTypeNestedOne">The type of the second-level resource.</param>
    /// <param name="resourceNameNestedOne">Name of second-tier resource.</param>
    /// <param name="notificationProviderNamespace">The notification provider namespace.</param>
    public delegate HttpResponseMessage NotifyProviderNestedResourceDelegate(HttpRequestMessage request, string subscriptionId, string resourceGroupName, string resourceProviderNamespace, string resourceType, string resourceName, string resourceTypeNestedOne, string resourceNameNestedOne, string notificationProviderNamespace);

    /// <summary>
    /// Delegate used for listening to nested-level resource POSTs.
    /// </summary>
    /// <param name="request">The request.</param>
    /// <param name="subscriptionId">The subscription identifier.</param>
    /// <param name="resourceGroupName">Name of the resource group.</param>
    /// <param name="resourceProviderNamespace">The resource provider namespace.</param>
    /// <param name="resourceType">Type of the resource.</param>
    /// <param name="resourceName">Name of the resource.</param>
    /// <param name="resourceTypeNestedOne">The type of the second-level resource.</param>
    /// <param name="resourceNameNestedOne">Name of second-tier resource.</param>
    /// <param name="resourceTypeNestedTwo">The type of the third-level resource.</param>
    /// <param name="resourceNameNestedTwo">Name of third-tier resource.</param>
    /// <param name="actionName">Name of the action.</param>
    public delegate HttpResponseMessage SecondNestedResourceActionDelegate(HttpRequestMessage request, string subscriptionId, string resourceGroupName, string resourceProviderNamespace, string resourceType, string resourceName, string resourceTypeNestedOne, string resourceNameNestedOne, string resourceTypeNestedTwo, string resourceNameNestedTwo, string actionName);

    /// <summary>
    /// Delegate used for listening to nested-level resource PUTs.
    /// </summary>
    /// <param name="request">The request.</param>
    /// <param name="subscriptionId">The subscription identifier.</param>
    /// <param name="resourceGroupName">Name of the resource group.</param>
    /// <param name="resourceProviderNamespace">The resource provider namespace.</param>
    /// <param name="resourceType">Type of the resource.</param>
    /// <param name="resourceName">Name of the resource.</param>
    /// <param name="resourceTypeNestedOne">The type of the second-level resource.</param>
    /// <param name="resourceNameNestedOne">Name of second-tier resource.</param>
    /// <param name="resourceTypeNestedTwo">The type of the third-level resource.</param>
    /// <param name="resourceNameNestedTwo">Name of third-tier resource.</param>
    /// <param name="wildcard">Rest of the path.</param>
    public delegate HttpResponseMessage PutSecondNestedResourceDelegate(HttpRequestMessage request, string subscriptionId, string resourceGroupName, string resourceProviderNamespace, string resourceType, string resourceName, string resourceTypeNestedOne, string resourceNameNestedOne, string resourceTypeNestedTwo, string resourceNameNestedTwo, string wildcard);

    /// <summary>
    /// Delegate used for listening to nested-level resource Deletes.
    /// </summary>
    /// <param name="request">The request.</param>
    /// <param name="subscriptionId">The subscription identifier.</param>
    /// <param name="resourceGroupName">Name of the resource group.</param>
    /// <param name="resourceProviderNamespace">The resource provider namespace.</param>
    /// <param name="resourceType">Type of the resource.</param>
    /// <param name="resourceName">Name of the resource.</param>
    /// <param name="resourceTypeNestedOne">The type of the second-level resource.</param>
    /// <param name="resourceNameNestedOne">Name of second-tier resource.</param>
    /// <param name="resourceTypeNestedTwo">The type of the third-level resource.</param>
    /// <param name="resourceNameNestedTwo">Name of third-tier resource.</param>
    /// <param name="wildcard">Rest of the path.</param>
    public delegate HttpResponseMessage DeleteSecondNestedResourceDelegate(HttpRequestMessage request, string subscriptionId, string resourceGroupName, string resourceProviderNamespace, string resourceType, string resourceName, string resourceTypeNestedOne, string resourceNameNestedOne, string resourceTypeNestedTwo, string resourceNameNestedTwo, string wildcard);

    /// <summary>
    /// Delegate used for listening to nested-level resource Deletes.
    /// </summary>
    /// <param name="request">The request.</param>
    /// <param name="subscriptionId">The subscription identifier.</param>
    /// <param name="resourceGroupName">Name of the resource group.</param>
    /// <param name="resourceProviderNamespace">The resource provider namespace.</param>
    /// <param name="resourceType">Type of the resource.</param>
    /// <param name="resourceName">Name of the resource.</param>
    /// <param name="resourceTypeNestedOne">The type of the second-level resource.</param>
    /// <param name="resourceNameNestedOne">Name of second-tier resource.</param>
    /// <param name="resourceTypeNestedTwo">The type of the third-level resource.</param>
    public delegate HttpResponseMessage GetSecondNestedResourcesDelegate(HttpRequestMessage request, string subscriptionId, string resourceGroupName, string resourceProviderNamespace, string resourceType, string resourceName, string resourceTypeNestedOne, string resourceNameNestedOne, string resourceTypeNestedTwo);

    /// <summary>
    /// Delegate used for listening to resource group level POSTs.
    /// </summary>
    /// <param name="request">The request.</param>
    /// <param name="subscriptionId">The subscription identifier.</param>
    /// <param name="resourceGroupName">Name of the resource group.</param>
    /// <param name="actionName">Name of the action.</param>
    public delegate HttpResponseMessage ResourceGroupActionDelegate(HttpRequestMessage request, string subscriptionId, string resourceGroupName, string actionName);

    /// <summary>
    /// Delegate used for listening to resource group level notifications.
    /// </summary>
    /// <param name="request">The incoming http request.</param>
    /// <param name="subscriptionId">Subscription Id.</param>
    /// <param name="resourceGroupName">Resource group name.</param>
    /// <param name="notificationProviderNamespace">The notification provider namespace.</param>
    public delegate HttpResponseMessage NotifyProviderResourceGroupDelegate(HttpRequestMessage request, string subscriptionId, string resourceGroupName, string notificationProviderNamespace);

    /// <summary>
    /// Delegate used for listening to second-tier resource GETs.
    /// </summary>
    /// <param name="request">The incoming http request.</param>
    /// <param name="subscriptionId">Subscription Id.</param>
    /// <param name="resourceGroupName">Resource group name.</param>
    /// <param name="resourceProviderNamespace">Resource provider namespace.</param>
    /// <param name="resourceType">The type of the top-level resource.</param>
    /// <param name="resourceName">Name of top-level resource.</param>
    /// <param name="resourceTypeNestedOne">The type of the second-level resource.</param>
    /// <param name="resourceNameNestedOne">Name of second-tier resource.</param>
    /// <param name="wildcard">Rest of provider-specific url.</param>
    /// <returns>Http response message.</returns>
    public delegate HttpResponseMessage GetNestedResourceDelegate(HttpRequestMessage request, string subscriptionId, string resourceGroupName, string resourceProviderNamespace, string resourceType, string resourceName, string resourceTypeNestedOne, string resourceNameNestedOne, string wildcard);

    /// <summary>
    /// Delegate used for listening to third-tier resource GETs.
    /// </summary>
    /// <param name="request">The incoming http request.</param>
    /// <param name="subscriptionId">Subscription Id.</param>
    /// <param name="resourceGroupName">Resource group name.</param>
    /// <param name="resourceProviderNamespace">Resource provider namespace.</param>
    /// <param name="resourceType">The type of the top-level resource.</param>
    /// <param name="resourceName">Name of top-level resource.</param>
    /// <param name="resourceTypeNestedOne">The type of the second-level resource.</param>
    /// <param name="resourceNameNestedOne">Name of second-tier resource.</param>
    /// <param name="resourceTypeNestedTwo">The type of the third-level resource.</param>
    /// <param name="resourceNameNestedTwo">Name of third-tier resource.</param>
    /// <param name="wildcard">Rest of provider-specific url.</param>
    /// <returns>Http response message.</returns>
    public delegate HttpResponseMessage GetDeeperNestedResourceDelegate(HttpRequestMessage request, string subscriptionId, string resourceGroupName, string resourceProviderNamespace, string resourceType, string resourceName, string resourceTypeNestedOne, string resourceNameNestedOne, string resourceTypeNestedTwo, string resourceNameNestedTwo, string wildcard);

    /// <summary>
    /// Delegate used for listening to top-level resource HEADs.
    /// </summary>
    /// <param name="request">The incoming http request.</param>
    /// <param name="subscriptionId">Subscription Id.</param>
    /// <param name="resourceGroupName">Resource group name.</param>
    /// <param name="resourceProviderNamespace">Resource provider namespace.</param>
    /// <param name="resourceType">The type of the top-level resource.</param>
    /// <param name="resourceName">Name of top-level resource.</param>
    /// <param name="wildcard">Rest of provider-specific url.</param>
    /// <returns>Http response message.</returns>
    public delegate HttpResponseMessage HeadResourceDelegate(HttpRequestMessage request, string subscriptionId, string resourceGroupName, string resourceProviderNamespace, string resourceType, string resourceName, string wildcard);

    /// <summary>
    /// Delegate used for listening to second-tier resource HEADs.
    /// </summary>
    /// <param name="request">The incoming http request.</param>
    /// <param name="subscriptionId">Subscription Id.</param>
    /// <param name="resourceGroupName">Resource group name.</param>
    /// <param name="resourceProviderNamespace">Resource provider namespace.</param>
    /// <param name="resourceType">The type of the top-level resource.</param>
    /// <param name="resourceName">Name of top-level resource.</param>
    /// <param name="resourceTypeNestedOne">The type of the second-level resource.</param>
    /// <param name="resourceNameNestedOne">Name of second-tier resource.</param>
    /// <param name="wildcard">Rest of provider-specific url.</param>
    /// <returns>Http response message.</returns>
    public delegate HttpResponseMessage HeadNestedResourceDelegate(HttpRequestMessage request, string subscriptionId, string resourceGroupName, string resourceProviderNamespace, string resourceType, string resourceName, string resourceTypeNestedOne, string resourceNameNestedOne, string wildcard);

    /// <summary>
    /// Delegate used for listening to third-tier resource HEADs.
    /// </summary>
    /// <param name="request">The incoming http request.</param>
    /// <param name="subscriptionId">Subscription Id.</param>
    /// <param name="resourceGroupName">Resource group name.</param>
    /// <param name="resourceProviderNamespace">Resource provider namespace.</param>
    /// <param name="resourceType">The type of the top-level resource.</param>
    /// <param name="resourceName">Name of top-level resource.</param>
    /// <param name="resourceTypeNestedOne">The type of the second-level resource.</param>
    /// <param name="resourceNameNestedOne">Name of second-tier resource.</param>
    /// <param name="resourceTypeNestedTwo">The type of the third-level resource.</param>
    /// <param name="resourceNameNestedTwo">Name of third-tier resource.</param>
    /// <param name="wildcard">Rest of provider-specific url.</param>
    /// <returns>Http response message.</returns>
    public delegate HttpResponseMessage HeadDeeperNestedResourceDelegate(HttpRequestMessage request, string subscriptionId, string resourceGroupName, string resourceProviderNamespace, string resourceType, string resourceName, string resourceTypeNestedOne, string resourceNameNestedOne, string resourceTypeNestedTwo, string resourceNameNestedTwo, string wildcard);

    /// <summary>
    /// Delegate used for listening to resource PUTs.
    /// </summary>
    /// <param name="request">The incoming http request.</param>
    /// <param name="subscriptionId">The subscription identifier.</param>
    /// <param name="resourceGroupName">Name of the resource group.</param>
    /// <param name="resourceProviderNamespace">The resource provider namespace.</param>
    /// <param name="resourceType">Type of the resource.</param>
    /// <param name="resourceName">Name of the resource.</param>
    /// <returns>Http response message.</returns>
    public delegate HttpResponseMessage PutResourceDelegate(HttpRequestMessage request, string subscriptionId, string resourceGroupName, string resourceProviderNamespace, string resourceType, string resourceName);

    /// <summary>
    /// Delegate used for listening to second-tier resource PUTs.
    /// </summary>
    /// <param name="request">The incoming http request.</param>
    /// <param name="subscriptionId">Subscription Id.</param>
    /// <param name="resourceGroupName">Resource group name.</param>
    /// <param name="resourceProviderNamespace">Resource provider namespace.</param>
    /// <param name="resourceType">The type of the top-level resource.</param>
    /// <param name="resourceName">Name of top-level resource.</param>
    /// <param name="resourceTypeNestedOne">The type of the second-level resource.</param>
    /// <param name="resourceNameNestedOne">Name of second-tier resource.</param>
    /// <param name="wildcard">Rest of provider-specific url.</param>
    /// <returns>Http response message.</returns>
    public delegate HttpResponseMessage PutNestedResourceDelegate(HttpRequestMessage request, string subscriptionId, string resourceGroupName, string resourceProviderNamespace, string resourceType, string resourceName, string resourceTypeNestedOne, string resourceNameNestedOne, string wildcard);

    /// <summary>
    /// Delegate used for listening to PATCH resource.
    /// </summary>
    /// <param name="request">The incoming http request.</param>
    /// <param name="subscriptionId">The subscription identifier.</param>
    /// <param name="resourceGroupName">Name of the resource group.</param>
    /// <param name="resourceProviderNamespace">The resource provider namespace.</param>
    /// <param name="resourceType">Type of the resource.</param>
    /// <param name="resourceName">Name of the resource.</param>
    /// <returns>Http response message.</returns>
    public delegate HttpResponseMessage PatchResourceDelegate(HttpRequestMessage request, string subscriptionId, string resourceGroupName, string resourceProviderNamespace, string resourceType, string resourceName);

    /// <summary>
    /// Delegate used for listening to second-tier resource PATCHs.
    /// </summary>
    /// <param name="request">The incoming http request.</param>
    /// <param name="subscriptionId">Subscription Id.</param>
    /// <param name="resourceGroupName">Resource group name.</param>
    /// <param name="resourceProviderNamespace">Resource provider namespace.</param>
    /// <param name="resourceType">The type of the top-level resource.</param>
    /// <param name="resourceName">Name of top-level resource.</param>
    /// <param name="resourceTypeNestedOne">The type of the second-level resource.</param>
    /// <param name="resourceNameNestedOne">Name of second-tier resource.</param>
    /// <param name="wildcard">Rest of provider-specific url.</param>
    /// <returns>Http response message.</returns>
    public delegate HttpResponseMessage PatchNestedResourceDelegate(HttpRequestMessage request, string subscriptionId, string resourceGroupName, string resourceProviderNamespace, string resourceType, string resourceName, string resourceTypeNestedOne, string resourceNameNestedOne, string wildcard);

    /// <summary>
    /// Delegate used for listening to resource DELETEs.
    /// </summary>
    /// <param name="request">The incoming http request.</param>
    /// <param name="subscriptionId">The subscription identifier.</param>
    /// <param name="resourceGroupName">Name of the resource group.</param>
    /// <param name="resourceProviderNamespace">The resource provider namespace.</param>
    /// <param name="resourceType">Type of the resource.</param>
    /// <param name="resourceName">Name of the resource.</param>
    public delegate HttpResponseMessage DeleteResourceDelegate(HttpRequestMessage request, string subscriptionId, string resourceGroupName, string resourceProviderNamespace, string resourceType, string resourceName);

    /// <summary>
    /// Delegate used for listening to resource group DELETEs.
    /// </summary>
    /// <param name="request">The incoming http request.</param>
    /// <param name="subscriptionId">The subscription identifier.</param>
    /// <param name="resourceGroupName">Name of the resource group.</param>
    public delegate HttpResponseMessage DeleteResourceGroupDelegate(HttpRequestMessage request, string subscriptionId, string resourceGroupName);

    /// <summary>
    /// Delegate used for listening to second-tier resource DELETEs.
    /// </summary>
    /// <param name="request">The incoming http request.</param>
    /// <param name="subscriptionId">Subscription Id.</param>
    /// <param name="resourceGroupName">Resource group name.</param>
    /// <param name="resourceProviderNamespace">Resource provider namespace.</param>
    /// <param name="resourceType">The type of the top-level resource.</param>
    /// <param name="resourceName">Name of top-level resource.</param>
    /// <param name="resourceTypeNestedOne">The type of the second-level resource.</param>
    /// <param name="resourceNameNestedOne">Name of second-tier resource.</param>
    /// <param name="wildcard">Rest of provider-specific url.</param>
    /// <returns>Http response message.</returns>
    public delegate HttpResponseMessage DeleteNestedResourceDelegate(HttpRequestMessage request, string subscriptionId, string resourceGroupName, string resourceProviderNamespace, string resourceType, string resourceName, string resourceTypeNestedOne, string resourceNameNestedOne, string wildcard);

    /// <summary>
    /// Delegate used for listening to resource collection GETs.
    /// </summary>
    /// <param name="request">The incoming http request.</param>
    /// <param name="subscriptionId">The subscription identifier.</param>
    /// <param name="resourceGroupName">Name of the resource group.</param>
    /// <param name="resourceProviderNamespace">The resource provider namespace.</param>
    /// <param name="resourceType">Type of the resource.</param>
    public delegate HttpResponseMessage GetResourcesDelegate(HttpRequestMessage request, string subscriptionId, string resourceGroupName, string resourceProviderNamespace, string resourceType);

    /// <summary>
    /// Delegate used for listening to subscription-level resource collection GETs.
    /// </summary>
    /// <param name="request">The incoming http request.</param>
    /// <param name="subscriptionId">The subscription identifier.</param>
    /// <param name="resourceProviderNamespace">The resource provider namespace.</param>
    /// <param name="resourceType">Type of the resource.</param>
    public delegate HttpResponseMessage GetSubscriptionResourcesDelegate(HttpRequestMessage request, string subscriptionId, string resourceProviderNamespace, string resourceType);

    /// <summary>
    /// Delegate used for listening to subscription-level resource GET.
    /// </summary>
    /// <param name="request">The incoming http request.</param>
    /// <param name="subscriptionId">The subscription identifier.</param>
    /// <param name="resourceProviderNamespace">The resource provider namespace.</param>
    /// <param name="resourceType">Type of the resource.</param>
    /// <param name="resourceName">Name of the resource.</param>
    public delegate HttpResponseMessage GetSubscriptionResourceDelegate(HttpRequestMessage request, string subscriptionId, string resourceProviderNamespace, string resourceType, string resourceName);

    /// <summary>
    /// Delegate used for listening to subscription-level resource collection PUTs.
    /// </summary>
    /// <param name="request">The incoming http request.</param>
    /// <param name="subscriptionId">The subscription identifier.</param>
    /// <param name="resourceProviderNamespace">The resource provider namespace.</param>
    /// <param name="resourceType">Type of the resource.</param>
    /// <param name="resourceName">Name of the resource.</param>
    public delegate HttpResponseMessage PutSubscriptionResourcesDelegate(HttpRequestMessage request, string subscriptionId, string resourceProviderNamespace, string resourceType, string resourceName);

    /// <summary>
    /// Delegate used for listening to subscription-level resource collection DELETEs.
    /// </summary>
    /// <param name="request">The incoming http request.</param>
    /// <param name="subscriptionId">The subscription identifier.</param>
    /// <param name="resourceProviderNamespace">The resource provider namespace.</param>
    /// <param name="resourceType">Type of the resource.</param>
    /// <param name="resourceName">Name of the resource.</param>
    public delegate HttpResponseMessage DeleteSubscriptionResourcesDelegate(HttpRequestMessage request, string subscriptionId, string resourceProviderNamespace, string resourceType, string resourceName);

    /// <summary>
    /// Delegate used for listening to nested resource collection GETs.
    /// </summary>
    /// <param name="request">The incoming http request.</param>
    /// <param name="subscriptionId">The subscription identifier.</param>
    /// <param name="resourceGroupName">Name of the resource group.</param>
    /// <param name="resourceProviderNamespace">The resource provider namespace.</param>
    /// <param name="resourceType">Type of the resource.</param>
    /// <param name="resourceName">Name of top-level resource.</param>
    /// <param name="resourceTypeNestedOne">The type of the second-level resource.</param>
    public delegate HttpResponseMessage GetNestedResourcesDelegate(HttpRequestMessage request, string subscriptionId, string resourceGroupName, string resourceProviderNamespace, string resourceType, string resourceName, string resourceTypeNestedOne);

    /// <summary>
    /// Delegate used for listening to resource collection HEADs.
    /// </summary>
    /// <param name="request">The incoming http request.</param>
    /// <param name="subscriptionId">The subscription identifier.</param>
    /// <param name="resourceGroupName">Name of the resource group.</param>
    /// <param name="resourceProviderNamespace">The resource provider namespace.</param>
    /// <param name="resourceType">Type of the resource.</param>
    public delegate HttpResponseMessage HeadResourcesDelegate(HttpRequestMessage request, string subscriptionId, string resourceGroupName, string resourceProviderNamespace, string resourceType);

    /// <summary>
    /// Delegate used for listening to nested resource collection HEADs.
    /// </summary>
    /// <param name="request">The incoming http request.</param>
    /// <param name="subscriptionId">The subscription identifier.</param>
    /// <param name="resourceGroupName">Name of the resource group.</param>
    /// <param name="resourceProviderNamespace">The resource provider namespace.</param>
    /// <param name="resourceType">Type of the resource.</param>
    /// <param name="resourceName">Name of top-level resource.</param>
    /// <param name="resourceTypeNestedOne">The type of the second-level resource.</param>
    public delegate HttpResponseMessage HeadNestedResourcesDelegate(HttpRequestMessage request, string subscriptionId, string resourceGroupName, string resourceProviderNamespace, string resourceType, string resourceName, string resourceTypeNestedOne);

    /// <summary>
    /// Delegate used for listening to un-scoped subscription requests.
    /// </summary>
    /// <param name="request">The incoming http request.</param>
    /// <param name="subscriptionId">The subscription identifier.</param>
    /// <param name="resourceProviderNamespace">The resource provider namespace.</param>
    /// <param name="wildcard">Rest of the url.</param>
    public delegate HttpResponseMessage UnscopedSubscriptionRequestDelegate(HttpRequestMessage request, string subscriptionId, string resourceProviderNamespace, string wildcard);

    /// <summary>
    /// Delegate used for listening to template GETs.
    /// </summary>
    /// <param name="request">The request.</param>
    /// <param name="templateId">The template identifier.</param>
    public delegate HttpResponseMessage TemplateRequestDelegate(HttpRequestMessage request, string templateId);

    /// <summary>
    /// Delegate used for listening to parameter GETs.
    /// </summary>
    /// <param name="request">The request.</param>
    /// <param name="templateId">The template identifier.</param>
    public delegate HttpResponseMessage ParameterRequestDelegate(HttpRequestMessage request, string templateId);

    /// <summary>
    /// Delegate used for listening to manifest GETs.
    /// </summary>
    /// <param name="request">The request.</param>
    /// <param name="resourceType">Type of the resource.</param>
    public delegate HttpResponseMessage ManifestRequestDelegate(HttpRequestMessage request, string resourceType);

    /// <summary>
    /// Delegate used for listening to un-scoped management group requests.
    /// </summary>
    /// <param name="request">The incoming http request.</param>
    /// <param name="managementGroupId">The management group identifier.</param>
    public delegate HttpResponseMessage PutManagementGroupDelegate(HttpRequestMessage request, string managementGroupId);

    /// <summary>
    /// Delegate used for listening to un-scoped subscription requests.
    /// </summary>
    /// <param name="request">The incoming http request.</param>
    /// <param name="subscriptionId">The subscription identifier.</param>
    public delegate HttpResponseMessage PutSubscriptionDelegate(HttpRequestMessage request, string subscriptionId);

    /// <summary>
    /// Delegate used for listening to un-scoped subscription requests.
    /// </summary>
    /// <param name="request">The incoming http request.</param>
    /// <param name="subscriptionId">The subscription identifier.</param>
    public delegate HttpResponseMessage PutExtensionSubscriptionDelegate(HttpRequestMessage request, string subscriptionId);

    /// <summary>
    /// Delegate used for listening to top-level resource GETs.
    /// </summary>
    /// <param name="request">The incoming http request.</param>
    /// <param name="subscriptionId">Subscription Id.</param>
    /// <param name="resourceGroupName">Resource group name.</param>
    /// <param name="resourceProviderNamespace">Resource provider namespace.</param>
    /// <param name="resourceType">The type of the top-level resource.</param>
    /// <param name="resourceName">Name of top-level resource.</param>
    /// <param name="extensionProviderNamespace">The extension provider namespace.</param>
    /// <param name="extensionResourceType">The extension resource type.</param>
    public delegate HttpResponseMessage GetExtensionResourcesDelegate(HttpRequestMessage request, string subscriptionId, string resourceGroupName, string resourceProviderNamespace, string resourceType, string resourceName, string extensionProviderNamespace, string extensionResourceType);

    /// <summary>
    /// Delegate used for listening to extension of extension resource GETs.
    /// </summary>
    /// <param name="request">The incoming http request.</param>
    /// <param name="subscriptionId">Subscription Id.</param>
    /// <param name="resourceGroupName">Resource group name.</param>
    /// <param name="resourceProviderNamespace">Resource provider namespace.</param>
    /// <param name="resourceType">The type of the top-level resource.</param>
    /// <param name="resourceName">Name of top-level resource.</param>
    /// <param name="extensionProviderNamespace">The first extension provider namespace.</param>
    /// <param name="extensionResourceType">The first extension resource type.</param>
    /// <param name="cascadeExtensionProviderNamespace">The cascade extension provider namespace.</param>
    /// <param name="cascadeExtensionResourceType">The cascade extension resource type.</param>
    public delegate HttpResponseMessage GetCascadeExtensionResourcesDelegate(HttpRequestMessage request, string subscriptionId, string resourceGroupName, string resourceProviderNamespace, string resourceType, string resourceName, string extensionProviderNamespace, string extensionResourceType, string cascadeExtensionProviderNamespace, string cascadeExtensionResourceType);

    /// <summary>
    /// Delegate used for listening to top-level resource GETs.
    /// </summary>
    /// <param name="request">The incoming http request.</param>
    /// <param name="subscriptionId">Subscription Id.</param>
    /// <param name="resourceGroupName">Resource group name.</param>
    /// <param name="resourceProviderNamespace">Resource provider namespace.</param>
    /// <param name="resourceType">The type of the top-level resource.</param>
    /// <param name="resourceName">Name of top-level resource.</param>
    /// <param name="extensionProviderNamespace">The extension provider namespace.</param>
    /// <param name="extensionResourceType">The extension resource type.</param>
    /// <param name="extensionResourceName">The extension resource name.</param>
    public delegate HttpResponseMessage GetExtensionResourceDelegate(HttpRequestMessage request, string subscriptionId, string resourceGroupName, string resourceProviderNamespace, string resourceType, string resourceName, string extensionProviderNamespace, string extensionResourceType, string extensionResourceName);

    /// <summary>
    /// Delegate used for listening to second-tier resource GETs.
    /// </summary>
    /// <param name="request">The incoming http request.</param>
    /// <param name="subscriptionId">Subscription Id.</param>
    /// <param name="resourceGroupName">Resource group name.</param>
    /// <param name="resourceProviderNamespace">Resource provider namespace.</param>
    /// <param name="resourceType">The type of the top-level resource.</param>
    /// <param name="resourceName">Name of top-level resource.</param>
    /// <param name="resourceTypeNestedOne">The type of the second-level resource.</param>
    /// <param name="resourceNameNestedOne">Name of second-tier resource.</param>
    /// <param name="extensionProviderNamespace">The extension provider namespace.</param>
    /// <param name="extensionResourceType">The extension resource type.</param>
    /// <param name="extensionResourceName">The extension resource name.</param>
    /// <returns>Http response message.</returns>
    public delegate HttpResponseMessage GetExtensionNestedResourceDelegate(HttpRequestMessage request, string subscriptionId, string resourceGroupName, string resourceProviderNamespace, string resourceType, string resourceName, string resourceTypeNestedOne, string resourceNameNestedOne, string extensionProviderNamespace, string extensionResourceType, string extensionResourceName);

    /// <summary>
    /// Delegate used for listening to third-tier resource GETs.
    /// </summary>
    /// <param name="request">The incoming http request.</param>
    /// <param name="subscriptionId">Subscription Id.</param>
    /// <param name="resourceGroupName">Resource group name.</param>
    /// <param name="resourceProviderNamespace">Resource provider namespace.</param>
    /// <param name="resourceType">The type of the top-level resource.</param>
    /// <param name="resourceName">Name of top-level resource.</param>
    /// <param name="resourceTypeNestedOne">The type of the second-level resource.</param>
    /// <param name="resourceNameNestedOne">Name of second-tier resource.</param>
    /// <param name="resourceTypeNestedTwo">The type of the third-level resource.</param>
    /// <param name="resourceNameNestedTwo">Name of third-tier resource.</param>
    /// <param name="extensionProviderNamespace">The extension provider namespace.</param>
    /// <param name="extensionResourceType">The extension resource type.</param>
    /// <param name="extensionResourceName">The extension resource name.</param>
    /// <returns>Http response message.</returns>
    public delegate HttpResponseMessage GetExtensionDeeperNestedResourceDelegate(HttpRequestMessage request, string subscriptionId, string resourceGroupName, string resourceProviderNamespace, string resourceType, string resourceName, string resourceTypeNestedOne, string resourceNameNestedOne, string resourceTypeNestedTwo, string resourceNameNestedTwo, string extensionProviderNamespace, string extensionResourceType, string extensionResourceName);

    /// <summary>
    /// Delegate used for listening to extension resource PUTs.
    /// </summary>
    /// <param name="request">The incoming http request.</param>
    /// <param name="subscriptionId">Subscription Id.</param>
    /// <param name="resourceGroupName">Resource group name.</param>
    /// <param name="resourceProviderNamespace">Resource provider namespace.</param>
    /// <param name="resourceType">The type of the top-level resource.</param>
    /// <param name="resourceName">Name of top-level resource.</param>
    /// <param name="extensionProviderNamespace">The extension provider namespace.</param>
    /// <param name="extensionResourceType">The extension resource type.</param>
    /// <param name="extensionResourceName">The extension resource name.</param>
    public delegate HttpResponseMessage PutExtensionResourceDelegate(HttpRequestMessage request, string subscriptionId, string resourceGroupName, string resourceProviderNamespace, string resourceType, string resourceName, string extensionProviderNamespace, string extensionResourceType, string extensionResourceName);

    /// <summary>
    /// Delegate used for listening to extension resource DELETEs.
    /// </summary>
    /// <param name="request">The incoming http request.</param>
    /// <param name="subscriptionId">Subscription Id.</param>
    /// <param name="resourceGroupName">Resource group name.</param>
    /// <param name="resourceProviderNamespace">Resource provider namespace.</param>
    /// <param name="resourceType">The type of the top-level resource.</param>
    /// <param name="resourceName">Name of top-level resource.</param>
    /// <param name="extensionProviderNamespace">The extension provider namespace.</param>
    /// <param name="extensionResourceType">The extension resource type.</param>
    /// <param name="extensionResourceName">The extension resource name.</param>
    public delegate HttpResponseMessage DeleteExtensionResourceDelegate(HttpRequestMessage request, string subscriptionId, string resourceGroupName, string resourceProviderNamespace, string resourceType, string resourceName, string extensionProviderNamespace, string extensionResourceType, string extensionResourceName);

    /// <summary>
    /// Delegate used for listening to extension resource PUTs under nested resources.
    /// </summary>
    /// <param name="request">The incoming http request.</param>
    /// <param name="subscriptionId">Subscription Id.</param>
    /// <param name="resourceGroupName">Resource group name.</param>
    /// <param name="resourceProviderNamespace">Resource provider namespace.</param>
    /// <param name="resourceType">The type of the top-level resource.</param>
    /// <param name="resourceName">Name of top-level resource.</param>
    /// <param name="resourceTypeNestedOne">The type of the nested resource.</param>
    /// <param name="resourceNameNestedOne">Name of nested resource.</param>
    /// <param name="extensionProviderNamespace">The extension provider namespace.</param>
    /// <param name="extensionResourceType">The extension resource type.</param>
    /// <param name="extensionResourceName">The extension resource name.</param>
    public delegate HttpResponseMessage PutExtensionNestedResourceDelegate(HttpRequestMessage request, string subscriptionId, string resourceGroupName, string resourceProviderNamespace, string resourceType, string resourceName, string resourceTypeNestedOne, string resourceNameNestedOne, string extensionProviderNamespace, string extensionResourceType, string extensionResourceName);

    /// <summary>
    /// Delegate used for listening to extension level POSTs.
    /// </summary>
    /// <param name="request">The incoming http request.</param>
    /// <param name="subscriptionId">Subscription Id.</param>
    /// <param name="resourceGroupName">Resource group name.</param>
    /// <param name="extensionProviderNamespace">The extension provider namespace.</param>
    public delegate HttpResponseMessage NotifyExtensionProviderResourceGroupDelegate(HttpRequestMessage request, string subscriptionId, string resourceGroupName, string extensionProviderNamespace);

    /// <summary>
    /// Delegate used for listening to extension level POSTs.
    /// </summary>
    /// <param name="request">The incoming http request.</param>
    /// <param name="subscriptionId">Subscription Id.</param>
    /// <param name="resourceGroupName">Resource group name.</param>
    /// <param name="resourceProviderNamespace">Resource provider namespace.</param>
    /// <param name="resourceType">The type of the top-level resource.</param>
    /// <param name="resourceName">Name of top-level resource.</param>
    /// <param name="extensionProviderNamespace">The extension provider namespace.</param>
    public delegate HttpResponseMessage NotifyExtensionProviderResourceDelegate(HttpRequestMessage request, string subscriptionId, string resourceGroupName, string resourceProviderNamespace, string resourceType, string resourceName, string extensionProviderNamespace);

    /// <summary>
    /// Delegate used for listening to request to get activity status of extension level provider.
    /// </summary>
    /// <param name="request">The incoming http request.</param>
    /// <param name="extensionProviderNamespace">The extension provider namespace.</param>
    /// <param name="activityId">The activity Id.</param>
    public delegate HttpResponseMessage GetExtensionProviderActivityStatusDelegate(HttpRequestMessage request, string extensionProviderNamespace, string activityId);

    /// <summary>
    /// Delegate used for listening to top-level extension resource collection through tenant level GETs.
    /// </summary>
    /// <param name="request">The incoming http request.</param>
    /// <param name="resourceProviderNamespace">Resource provider namespace.</param>
    /// <param name="resourceType">The type of the top-level resource.</param>
    /// <param name="resourceName">Name of top-level resource.</param>
    /// <param name="extensionProviderNamespace">The extension provider namespace.</param>
    /// <param name="extensionResourceType">The extension resource type.</param>
    public delegate HttpResponseMessage GetTenantExtensionResourcesDelegate(HttpRequestMessage request, string resourceProviderNamespace, string resourceType, string resourceName, string extensionProviderNamespace, string extensionResourceType);

    /// <summary>
    /// Delegate used for listening to top-level extension resource through tenant level GETs.
    /// </summary>
    /// <param name="request">The incoming http request.</param>
    /// <param name="resourceProviderNamespace">Resource provider namespace.</param>
    /// <param name="resourceType">The type of the top-level resource.</param>
    /// <param name="resourceName">Name of top-level resource.</param>
    /// <param name="extensionProviderNamespace">The extension provider namespace.</param>
    /// <param name="extensionResourceType">The extension resource type.</param>
    /// <param name="extensionResourceName">The extension resource name.</param>
    public delegate HttpResponseMessage GetTenantExtensionResourceDelegate(HttpRequestMessage request, string resourceProviderNamespace, string resourceType, string resourceName, string extensionProviderNamespace, string extensionResourceType, string extensionResourceName);

    /// <summary>
    /// Delegate used for listening to provider level extension resources through tenant level GETs.
    /// </summary>
    /// <param name="request">The incoming http request.</param>
    /// <param name="resourceProviderNamespace">Resource provider namespace.</param>
    /// <param name="extensionProviderNamespace">The extension provider namespace.</param>
    /// <param name="extensionResourceType">The extension resource type.</param>
    public delegate HttpResponseMessage GetTenantProviderExtensionResourcesDelegate(HttpRequestMessage request, string resourceProviderNamespace, string extensionProviderNamespace, string extensionResourceType);

    /// <summary>
    /// Delegate used for listening to provider level extension resource through tenant level GETs.
    /// </summary>
    /// <param name="request">The incoming http request.</param>
    /// <param name="resourceProviderNamespace">Resource provider namespace.</param>
    /// <param name="extensionProviderNamespace">The extension provider namespace.</param>
    /// <param name="extensionResourceType">The extension resource type.</param>
    /// <param name="extensionResourceName">The extension resource name.</param>
    public delegate HttpResponseMessage GetTenantProviderExtensionResourceDelegate(HttpRequestMessage request, string resourceProviderNamespace, string extensionProviderNamespace, string extensionResourceType, string extensionResourceName);

    /// <summary>
    /// Delegate used for putting provider level extension resource through tenant level PUTs.
    /// </summary>
    /// <param name="request">The incoming http request.</param>
    /// <param name="resourceProviderNamespace">Resource provider namespace.</param>
    /// <param name="extensionProviderNamespace">The extension provider namespace.</param>
    /// <param name="extensionResourceType">The extension resource type.</param>
    /// <param name="extensionResourceName">The extension resource name.</param>
    public delegate HttpResponseMessage PutTenantProviderExtensionResourceDelegate(HttpRequestMessage request, string resourceProviderNamespace, string extensionProviderNamespace, string extensionResourceType, string extensionResourceName);

    /// <summary>
    /// Delegate used for deleting provider level extension resource through tenant level DELETEs.
    /// </summary>
    /// <param name="request">The incoming http request.</param>
    /// <param name="resourceProviderNamespace">Resource provider namespace.</param>
    /// <param name="extensionProviderNamespace">The extension provider namespace.</param>
    /// <param name="extensionResourceType">The extension resource type.</param>
    /// <param name="extensionResourceName">The extension resource name.</param>
    public delegate HttpResponseMessage DeleteTenantProviderExtensionResourceDelegate(HttpRequestMessage request, string resourceProviderNamespace, string extensionProviderNamespace, string extensionResourceType, string extensionResourceName);

    /// <summary>
    /// Delegate used for listening to top-level extension resource through tenant level PUTs.
    /// </summary>
    /// <param name="request">The incoming http request.</param>
    /// <param name="resourceProviderNamespace">Resource provider namespace.</param>
    /// <param name="resourceType">The type of the top-level resource.</param>
    /// <param name="resourceName">Name of top-level resource.</param>
    /// <param name="extensionProviderNamespace">The extension provider namespace.</param>
    /// <param name="extensionResourceType">The extension resource type.</param>
    /// <param name="extensionResourceName">The extension resource name.</param>
    public delegate HttpResponseMessage PutTenantExtensionResourceDelegate(HttpRequestMessage request, string resourceProviderNamespace, string resourceType, string resourceName, string extensionProviderNamespace, string extensionResourceType, string extensionResourceName);

    /// <summary>
    /// Delegate used for listening to top-level extension resource through tenant level DELETEs.
    /// </summary>
    /// <param name="request">The incoming http request.</param>
    /// <param name="resourceProviderNamespace">Resource provider namespace.</param>
    /// <param name="resourceType">The type of the top-level resource.</param>
    /// <param name="resourceName">Name of top-level resource.</param>
    /// <param name="extensionProviderNamespace">The extension provider namespace.</param>
    /// <param name="extensionResourceType">The extension resource type.</param>
    /// <param name="extensionResourceName">The extension resource name.</param>
    public delegate HttpResponseMessage DeleteTenantExtensionResourceDelegate(HttpRequestMessage request, string resourceProviderNamespace, string resourceType, string resourceName, string extensionProviderNamespace, string extensionResourceType, string extensionResourceName);

    /// <summary>
    /// Delegate used for listening to top-level resource PUTs.
    /// </summary>
    /// <param name="request">The incoming http request.</param>
    /// <param name="subscriptionId">Subscription Id.</param>
    public delegate HttpResponseMessage GetFeaturesDelegate(HttpRequestMessage request, string subscriptionId);

    /// <summary>
    /// Delegate used for listening to top-level resource PUTs.
    /// </summary>
    /// <param name="request">The incoming http request.</param>
    /// <param name="subscriptionId">Subscription Id.</param>
    /// <param name="providerNamespace">Provider namespace.</param>
    public delegate HttpResponseMessage GetFeaturesForProviderDelegate(HttpRequestMessage request, string subscriptionId, string providerNamespace);

    /// <summary>
    /// Delegate used for listening to top-level resource PUTs.
    /// </summary>
    /// <param name="request">The incoming http request.</param>
    /// <param name="subscriptionId">Subscription Id.</param>
    /// <param name="providerNamespace">Provider namespace.</param>
    /// <param name="featureName">Feature name.</param>
    public delegate HttpResponseMessage GetFeatureByNamespaceandFeatureNameDelegate(HttpRequestMessage request, string subscriptionId, string providerNamespace, string featureName);

    /// <summary>
    /// Delegate used to get the resource async operation.
    /// </summary>
    /// <param name="request">The incoming http request.</param>
    /// <param name="subscriptionId">Subscription Id.</param>
    /// <param name="resourceGroupName">Resource group name.</param>
    /// <param name="resourceProviderNamespace">Resource provider namespace.</param>
    /// <param name="resourceType">The type of the top-level resource.</param>
    /// <param name="resourceName">Name of top-level resource.</param>
    /// <param name="asyncOperationId">The async operation identifier.</param>
    public delegate HttpResponseMessage GetResourceAsyncOperationDelegate(HttpRequestMessage request, string subscriptionId, string resourceGroupName, string resourceProviderNamespace, string resourceType, string resourceName, string asyncOperationId);

    /// <summary>
    /// Delegate used to put/delete the authorization resource.
    /// </summary>
    /// <param name="request">The http request.</param>
    /// <param name="subscriptionId">The subscription id.</param>
    /// <param name="resourceType">The authorization resource type.</param>
    /// <param name="resourceName">The authorization resource name.</param>
    /// <param name="requestBody">The request body.</param>
    public delegate HttpResponseMessage WriteAuthorizationResourceDelegate(HttpRequestMessage request, string subscriptionId, string resourceType, string resourceName, JObject requestBody);

    /// <summary>
    /// Delegate used to put/delete the nested authorization resource.
    /// </summary>
    /// <param name="request">The http request.</param>
    /// <param name="subscriptionId">The subscription identifier.</param>
    /// <param name="resourceGroupName">Name of the resource group.</param>
    /// <param name="resourceProviderNamespace">The resource provider namespace.</param>
    /// <param name="resourceType">The resource type.</param>
    /// <param name="resourceName">The resource name.</param>
    /// <param name="authorizationResourceType">The authorization resource type.</param>
    /// <param name="authorizationResourceName">The authorization resource name.</param>
    /// <param name="requestBody">The request body.</param>
    public delegate HttpResponseMessage WriteAuthorizationResourceNestedDelegate(HttpRequestMessage request, string subscriptionId, string resourceGroupName, string resourceProviderNamespace, string resourceType, string resourceName, string authorizationResourceType, string authorizationResourceName, [FromBody] JObject requestBody);

    /// <summary>
    /// Delegate used to put/delete the authorization resource for management group.
    /// </summary>
    /// <param name="request">The http request.</param>
    /// <param name="entityId">The management group Id.</param>
    /// <param name="resourceType">The authorization resource type.</param>
    /// <param name="resourceName">The authorization resource name.</param>
    /// <param name="requestBody">The request body.</param>
    public delegate HttpResponseMessage WriteAuthorizationResourceForManagementGroupDelegate(HttpRequestMessage request, string entityId, string resourceType, string resourceName, JObject requestBody);

    /// <summary>
    /// Delegate used to put/delete the authorization resource.
    /// </summary>
    /// <param name="request">The http request.</param>
    /// <param name="subscriptionId">The subscription id.</param>
    /// <param name="resourceGroupName">Name of the resource group.</param>
    /// <param name="resourceType">The authorization resource type.</param>
    /// <param name="resourceName">The authorization resource name.</param>
    /// <param name="requestBody">The request body.</param>
    public delegate HttpResponseMessage WriteAuthorizationResourceForResourceGroupDelegate(HttpRequestMessage request, string subscriptionId, string resourceGroupName, string resourceType, string resourceName, JObject requestBody);

    /// <summary>
    /// Delegate used to get the authorization resource.
    /// </summary>
    /// <param name="request">The http request.</param>
    /// <param name="subscriptionId">The subscription id.</param>
    /// <param name="resourceType">The authorization resource type.</param>
    /// <param name="resourceName">The authorization resource name.</param>
    public delegate HttpResponseMessage GetAuthorizationResourceDelegate(HttpRequestMessage request, string subscriptionId, string resourceType, string resourceName);

    /// <summary>
    /// Delegate used to get the authorization resources for management group.
    /// </summary>
    /// <param name="request">The http request.</param>
    /// <param name="entityId">The management group id.</param>
    /// <param name="resourceType">The authorization resource type.</param>
    public delegate HttpResponseMessage GetAuthorizationResourcesForManagementGroupDelegate(HttpRequestMessage request, string entityId, string resourceType);

    /// <summary>
    /// Delegate used to get the links.
    /// </summary>
    /// <param name="request">The http request.</param>
    /// <param name="subscriptionId">The subscription id.</param>
    /// <param name="resourceGroupName">Name of the resource group.</param>
    public delegate HttpResponseMessage LinkDelegate(HttpRequestMessage request, string subscriptionId, string resourceGroupName);

    /// <summary>
    /// Delegate used to get the authorization resources.
    /// </summary>
    /// <param name="request">The http request.</param>
    /// <param name="subscriptionId">The subscription id.</param>
    /// <param name="resourceType">The resource type.</param>
    public delegate HttpResponseMessage GetAuthorizationResourcesDelegate(HttpRequestMessage request, string subscriptionId, string resourceType);

    /// <summary>
    /// Delegate used to get the authorization resources.
    /// </summary>
    /// <param name="request">The http request.</param>
    /// <param name="subscriptionId">The subscription id.</param>
    /// <param name="resourceGroupName">Name of the resource group.</param>
    /// <param name="resourceType">The resource type.</param>
    public delegate HttpResponseMessage GetAuthorizationResourcesForResourceGroupDelegate(HttpRequestMessage request, string subscriptionId, string resourceGroupName, string resourceType);

    /// <summary>
    /// Delegate used to put/delete the nested authorization resource.
    /// </summary>
    /// <param name="request">The http request.</param>
    /// <param name="subscriptionId">The subscription identifier.</param>
    /// <param name="resourceGroupName">Name of the resource group.</param>
    /// <param name="resourceProviderNamespace">The resource provider namespace.</param>
    /// <param name="resourceType">The resource type.</param>
    /// <param name="resourceName">The resource name.</param>
    /// <param name="authorizationResourceType">The authorization resource type.</param>
    public delegate HttpResponseMessage GetAuthorizationResourcesForResourceDelegate(HttpRequestMessage request, string subscriptionId, string resourceGroupName, string resourceProviderNamespace, string resourceType, string resourceName, string authorizationResourceType);

    /// <summary>
    /// Delegate used to get the authorization resource at resource group.
    /// </summary>
    /// <param name="request">The http request.</param>
    /// <param name="subscriptionId">The subscription id.</param>
    /// <param name="resourceGroupName">Name of the resource group.</param>
    /// <param name="resourceType">The resource type.</param>
    /// <param name="resourceName">The resource name.</param>
    public delegate HttpResponseMessage GetAuthorizationResourceForResourceGroupDelegate(HttpRequestMessage request, string subscriptionId, string resourceGroupName, string resourceType, string resourceName);

    /// <summary>
    /// Delegate used for prepare or un prepare network policies POST.
    /// </summary>
    /// <param name="request">The http request.</param>
    /// <param name="subscriptionId">The subscription identifier.</param>
    /// <param name="resourceGroupName">Name of the resource group.</param>
    /// <param name="virtualNetworkName">The virtual network name.</param>
    /// <param name="subnetName">The subnet name.</param>
    /// <param name="actionName">The action name.</param>
    public delegate HttpResponseMessage PrepareOrUnprepareNetworkPoliciesRequestDelegate(HttpRequestMessage request, string subscriptionId, string resourceGroupName, string virtualNetworkName, string subnetName, string actionName);

    /// <summary>
    /// Delegate used for GET prepare or un prepare network policies operation status.
    /// </summary>
    /// <param name="request">The http request.</param>
    /// <param name="subscriptionId">The subscription identifier.</param>
    /// <param name="resourceProviderNamespace">The resource provider namespace.</param>
    /// <param name="location">The location.</param>
    /// <param name="operationId">The async operation id.</param>
    public delegate HttpResponseMessage GetAsyncOperationStatusDelegate(HttpRequestMessage request, string subscriptionId, string resourceProviderNamespace, string location, string operationId);

    /// <summary>
    /// Delegate used to handle a resource group level provider action
    /// </summary>
    /// <param name="request">The http request.</param>
    /// <param name="subscriptionId">The subscription id.</param>
    /// <param name="resourceGroupName">Name of the resource group.</param>
    /// <param name="resourceProviderNamespace">The name of the resource provider.</param>
    /// <param name="action">The name of the action.</param>
    public delegate HttpResponseMessage ResourceGroupProviderActionDelegate(HttpRequestMessage request, string subscriptionId, string resourceGroupName, string resourceProviderNamespace, string action);

    /// <summary>
    /// Delegate used for Put RP Backend Service Proxy.
    /// </summary>
    /// <param name="request">The http request.</param>
    /// <param name="subscriptionId">The subscription identifier.</param>
    /// <param name="resourceGroupName">Name of the resource group.</param>
    /// <param name="workspaceName">The workspace name.</param>
    public delegate HttpResponseMessage PutRPBackendProxyRequestDelegate(HttpRequestMessage request, string subscriptionId, string resourceGroupName, string workspaceName);

    /// <summary>
    /// Delegate used for Put RP Backend Service Proxy.
    /// </summary>
    /// <param name="request">The http request.</param>
    /// <param name="subscriptionId">The subscription identifier.</param>
    /// <param name="resourceGroupName">Name of the resource group.</param>
    /// <param name="workspaceName">The workspace name.</param>
    public delegate HttpResponseMessage PatchRPBackendProxyRequestDelegate(HttpRequestMessage request, string subscriptionId, string resourceGroupName, string workspaceName);

    /// <summary>
    /// Delegate used for Get RP Backend Service Proxy.
    /// </summary>
    /// <param name="request">The http request.</param>
    /// <param name="subscriptionId">The subscription identifier.</param>
    /// <param name="resourceGroupName">Name of the resource group.</param>
    /// <param name="workspaceName">The workspace name.</param>
    public delegate HttpResponseMessage GetRPBackendPrivateLinkRequestDelegate(HttpRequestMessage request, string subscriptionId, string resourceGroupName, string workspaceName);

    /// <summary>
    /// Delegate used for Put Private Link Service Proxy.
    /// </summary>
    /// <param name="request">The http request.</param>
    /// <param name="subscriptionId">The subscription identifier.</param>
    /// <param name="resourceGroupName">Name of the resource group.</param>
    /// <param name="privateEndPointName">The private endpoint name.</param>
    /// <param name="privatelinkServiceProxyName">The private link service proxy name.</param>
    public delegate HttpResponseMessage PutPrivateLinkServiceProxyRequestDelegate(HttpRequestMessage request, string subscriptionId, string resourceGroupName, string privateEndPointName, string privatelinkServiceProxyName);

    /// <summary>
    /// The web service host for the test resource provider.
    /// </summary>
    public abstract class BaseResourceProviderTestHost : ServiceTestHost<BaseResourceProviderTestHost.BaseResourceProviderTestHostServer>, IDisposable
    {
        #region Constructor

        /// <summary>
        /// Initializes a new instance of the <see cref="BaseResourceProviderTestHost"/> class.
        /// </summary>
        /// <param name="serviceUri">The service URI.</param>
        public BaseResourceProviderTestHost(Uri serviceUri)
            : base(serviceUri)
        {
        }

        /// <summary>
        /// Performs application-defined tasks associated with freeing, releasing, or resetting unmanaged resources.
        /// </summary>
        public void Dispose()
        {
            this.Stop();
        }

        /// <summary>
        /// Gets the state of the get.
        /// </summary>
        protected override object GetState
        {
            get { return this; }
        }

        #endregion

        #region Resource-Management

        /// <summary>
        /// Gets or sets the on get resource.
        /// </summary>
        public GetResourceDelegate OnGetResource { get; set; }

        /// <summary>
        /// Gets or sets the on get tenant resource.
        /// </summary>
        public GetTenantResourceDelegate OnGetTenantResource { get; set; }

        /// <summary>
        /// Gets or sets the on get tenant resource.
        /// </summary>
        public PutTenantResourceDelegate OnPutTenantResource { get; set; }

        /// <summary>
        /// Gets or sets the on get tenant resource.
        /// </summary>
        public DeleteTenantResourceDelegate OnDeleteTenantResource { get; set; }

        /// <summary>
        /// Gets or sets the on subscription level resource action.
        /// </summary>
        public SubscriptionResourceActionDelegate OnSubscriptionResourceAction { get; set; }

        /// <summary>
        /// Gets or sets the on resource action.
        /// </summary>
        public ResourceActionDelegate OnResourceAction { get; set; }

        /// <summary>
        /// Gets or sets the delegate to get activity status from resource provider.
        /// </summary>
        public GetResourceProviderActivityStatusDelegate OnGetResourceProviderActivityStatus { get; set; }

        /// <summary>
        /// Gets or sets the delegate called when notify provider request is received.
        /// </summary>
        public NotifyProviderDelegate OnNotifyProvider { get; set; }

        /// <summary>
        /// Gets or sets the on notify at resource level.
        /// </summary>
        public NotifyProviderResourceDelegate OnNotifyProviderResource { get; set; }

        /// <summary>
        /// Gets or sets the on notify at subscription level resource.
        /// </summary>
        public NotifyProviderSubscriptionLevelResourceDelegate OnNotifyProviderSubscriptionLevelResource { get; set; }

        /// <summary>
        /// Gets or sets the on notify at tenant level resource.
        /// </summary>
        public NotifyProviderTenantLevelResourceDelegate OnNotifyProviderTenantLevelResource { get; set; }

        /// <summary>
        /// Gets or sets the on notify at extension resource level.
        /// </summary>
        public NotifyProviderExtensionResourceDelegate OnNotifyProviderExtensionResource { get; set; }

        /// <summary>
        /// Gets or sets the on nested resource action.
        /// </summary>
        public NestedResourceActionDelegate OnNestedResourceAction { get; set; }

        /// <summary>
        /// Gets or sets the on notify at nested resource level.
        /// </summary>
        public NotifyProviderNestedResourceDelegate OnNotifyProviderNestedResource { get; set; }

        /// <summary>
        /// Gets or sets the on second nested resource action.
        /// </summary>
        public SecondNestedResourceActionDelegate OnSecondNestedResourceAction { get; set; }

        /// <summary>
        /// Gets or sets the on putting second nested resource.
        /// </summary>
        public PutSecondNestedResourceDelegate OnPutSecondNestedResource { get; set; }

        /// <summary>
        /// Gets or sets the on deleting second nested resource.
        /// </summary>
        public DeleteSecondNestedResourceDelegate OnDeleteSecondNestedResource { get; set; }

        /// <summary>
        /// Gets or sets the on getting second nested resources.
        /// </summary>
        public GetSecondNestedResourcesDelegate OnGetSecondNestedResources { get; set; }

        /// <summary>
        /// Gets or sets the on resource group action.
        /// </summary>
        public ResourceGroupActionDelegate OnResourceGroupAction { get; set; }

        /// <summary>
        /// Gets or sets the delegate for nested resource calls.
        /// </summary>
        public GetNestedResourceDelegate OnGetNestedResource { get; set; }

        /// <summary>
        /// Gets or sets the delegate for nested resource calls.
        /// </summary>
        public GetDeeperNestedResourceDelegate OnGetDeeperNestedResource { get; set; }

        /// <summary>
        /// Gets or sets the on head resource.
        /// </summary>
        public HeadResourceDelegate OnHeadResource { get; set; }

        /// <summary>
        /// Gets or sets the delegate for nested resource calls.
        /// </summary>
        public HeadNestedResourceDelegate OnHeadNestedResource { get; set; }

        /// <summary>
        /// Gets or sets the delegate for nested resource calls.
        /// </summary>
        public HeadDeeperNestedResourceDelegate OnHeadDeeperNestedResource { get; set; }

        /// <summary>
        /// Gets or sets the on put resource hook.
        /// </summary>
        public PutResourceDelegate OnPutResource { get; set; }

        /// <summary>
        /// Gets or sets the delegate for nested resource calls.
        /// </summary>
        public PutNestedResourceDelegate OnPutNestedResource { get; set; }

        /// <summary>
        /// Gets or sets the on patch resource hook.
        /// </summary>
        public PatchResourceDelegate OnPatchResource { get; set; }

        /// <summary>
        /// Gets or sets the delegate for nested patch resource calls.
        /// </summary>
        public PatchNestedResourceDelegate OnPatchNestedResource { get; set; }

        /// <summary>
        /// Gets or sets the on delete resource.
        /// </summary>
        public DeleteResourceDelegate OnDeleteResource { get; set; }

        /// <summary>
        /// Gets or sets the on delete resource group.
        /// </summary>
        public DeleteResourceGroupDelegate OnDeleteResourceGroup { get; set; }

        /// <summary>
        /// Gets or sets the delegate for notifying resource providers for resource group level operations.
        /// </summary>
        public NotifyProviderResourceGroupDelegate OnNotifyProviderResourceGroup { get; set; }

        /// <summary>
        /// Gets or sets the delegate for nested resource deletes.
        /// </summary>
        public DeleteNestedResourceDelegate OnDeleteNestedResource { get; set; }

        /// <summary>
        /// Gets or sets the delegate for GET on resource collection.
        /// </summary>
        public GetResourcesDelegate OnGetResources { get; set; }

        /// <summary>
        /// Gets or sets the delegate for GET on nested resource collection.
        /// </summary>
        public GetNestedResourcesDelegate OnGetNestedResources { get; set; }

        /// <summary>
        /// Gets or sets the delegate for HEAD on resource collection.
        /// </summary>
        public HeadResourcesDelegate OnHeadResources { get; set; }

        /// <summary>
        /// Gets or sets the delegate for HEAD on nested resource collection.
        /// </summary>
        public HeadNestedResourcesDelegate OnHeadNestedResources { get; set; }

        /// <summary>
        /// Gets or sets the delegate for put management group requests.
        /// </summary>
        public PutManagementGroupDelegate OnPutManagementGroup { get; set; }

        /// <summary>
        /// Gets or sets the delegate for un-scoped subscription GET requests.
        /// </summary>
        public UnscopedSubscriptionRequestDelegate OnGetUnscopedSubscriptionRequest { get; set; }

        /// <summary>
        /// Gets or sets the delegate for un-scoped subscription PUT requests.
        /// </summary>
        public UnscopedSubscriptionRequestDelegate OnPutUnscopedSubscriptionRequest { get; set; }

        /// <summary>
        /// Gets or sets the delegate for un-scoped subscription DELETE requests.
        /// </summary>
        public UnscopedSubscriptionRequestDelegate OnDeleteUnscopedSubscriptionRequest { get; set; }

        /// <summary>
        /// Gets or sets the delegate for put subscription requests.
        /// </summary>
        public PutSubscriptionDelegate OnPutSubscription { get; set; }

        /// <summary>
        /// Gets or sets the delegate for GET on subscription-level resource collection.
        /// </summary>
        public GetSubscriptionResourcesDelegate OnGetSubscriptionResources { get; set; }

        /// <summary>
        /// Gets or sets the delegate for GET on subscription-level resource.
        /// </summary>
        public GetSubscriptionResourceDelegate OnGetSubscriptionResource { get; set; }

        /// <summary>
        /// Gets or sets the delegate for PUT on subscription-level resource collection.
        /// </summary>
        public PutSubscriptionResourcesDelegate OnPutSubscriptionResources { get; set; }

        /// <summary>
        /// Gets or sets the delegate for DELETE on subscription-level resource collection.
        /// </summary>
        public PutSubscriptionResourcesDelegate OnDeleteSubscriptionResources { get; set; }

        /// <summary>
        /// Gets or sets the delegate for PUT on subscription for extension provider.
        /// </summary>
        public PutExtensionSubscriptionDelegate OnPutExtensionSubscription { get; set; }

        /// <summary>
        /// Gets or sets the on get extension resource collection.
        /// </summary>
        public GetExtensionResourcesDelegate OnGetExtensionResources { get; set; }

        /// <summary>
        /// Gets or sets the on get extension of extension resource collection.
        /// </summary>
        public GetCascadeExtensionResourcesDelegate OnGetCascadeExtensionResources { get; set; }

        /// <summary>
        /// Gets or sets the on get extension resource.
        /// </summary>
        public GetExtensionResourceDelegate OnGetExtensionResource { get; set; }

        /// <summary>
        /// Gets or sets the delegate for nested resource calls.
        /// </summary>
        public GetExtensionNestedResourceDelegate OnGetExtensionNestedResource { get; set; }

        /// <summary>
        /// Gets or sets the delegate for nested resource calls.
        /// </summary>
        public GetExtensionDeeperNestedResourceDelegate OnGetExtensionDeeperNestedResource { get; set; }

        /// <summary>
        /// Gets or sets the delegate for put extension resource collection.
        /// </summary>
        public PutExtensionResourceDelegate OnPutExtensionResource { get; set; }

        /// <summary>
        /// Gets or sets the delegate for delete extension resource collection.
        /// </summary>
        public DeleteExtensionResourceDelegate OnDeleteExtensionResource { get; set; }

        /// <summary>
        /// Gets or sets the delegate for put extension resource under a nested resource.
        /// </summary>
        public PutExtensionNestedResourceDelegate OnPutExtensionNestedResource { get; set; }

        /// <summary>
        /// Gets or sets the delegate to get activity status from extension provider.
        /// </summary>
        public GetExtensionProviderActivityStatusDelegate OnGetExtensionProviderActivityStatus { get; set; }

        /// <summary>
        /// Gets or sets the delegate for notify extension resource providers.
        /// </summary>
        public NotifyExtensionProviderResourceGroupDelegate OnNotifyExtensionProviderResourceGroup { get; set; }

        /// <summary>
        /// Gets or sets the delegate for notify extension resource providers.
        /// </summary>
        public NotifyExtensionProviderResourceDelegate OnNotifyExtensionProviderResource { get; set; }

        /// <summary>
        /// Gets or sets the on get extension resources at tenant provider level.
        /// </summary>
        public GetTenantProviderExtensionResourcesDelegate OnGetTenantProviderExtensionResourcesDelegate { get; set; }

        /// <summary>
        /// Gets or sets the on get extension resource at tenant provider level.
        /// </summary>
        public GetTenantProviderExtensionResourceDelegate OnGetTenantProviderExtensionResourceDelegate { get; set; }

        /// <summary>
        /// Gets or sets the on put extension resource at tenant provider level.
        /// </summary>
        public PutTenantProviderExtensionResourceDelegate OnPutTenantProviderExtensionResourceDelegate { get; set; }

        /// <summary>
        /// Gets or sets the on delete extension resource at tenant provider level.
        /// </summary>
        public DeleteTenantProviderExtensionResourceDelegate OnDeleteTenantProviderExtensionResourceDelegate { get; set; }

        /// <summary>
        /// Gets or sets the on get extension resource collection.
        /// </summary>
        public GetTenantExtensionResourcesDelegate OnGetTenantExtensionResources { get; set; }

        /// <summary>
        /// Gets or sets the on get extension resource.
        /// </summary>
        public GetTenantExtensionResourceDelegate OnGetTenantExtensionResource { get; set; }

        /// <summary>
        /// Gets or sets the delegate for put extension resource.
        /// </summary>
        public PutTenantExtensionResourceDelegate OnPutTenantExtensionResource { get; set; }

        /// <summary>
        /// Gets or sets the delegate for delete extension resource.
        /// </summary>
        public DeleteTenantExtensionResourceDelegate OnDeleteTenantExtensionResource { get; set; }

        /// <summary>
        /// Gets or sets the delegate for getting features.
        /// </summary>
        public GetFeaturesForProviderDelegate OnGetFeaturesForProvider { get; set; }

        /// <summary>
        /// Gets or sets the delegate for getting features.
        /// </summary>
        public GetFeatureByNamespaceandFeatureNameDelegate OnGetFeatureByNamespaceandFeatureName { get; set; }

        /// <summary>
        /// Gets or sets the delegate for getting features.
        /// </summary>
        public GetFeaturesDelegate OnGetFeatures { get; set; }

        /// <summary>
        /// Gets or sets the delegate for getting resource async operation.
        /// </summary>
        public GetResourceAsyncOperationDelegate OnGetResourceAsyncOperation { get; set; }

        /// <summary>
        /// Gets or sets the delegate for resource group provider action.
        /// </summary>
        public ResourceGroupProviderActionDelegate OnResourceGroupProviderAction { get; set; }

        /// <summary>
        /// Gets or sets the on un get operation status delegate for async operation.
        /// </summary>
        public GetAsyncOperationStatusDelegate OnGetAsyncOperationStatus { get; set; }

        /// <summary>
        /// Gets or sets the On Put RP Backend Proxy.
        /// </summary>
        public PutRPBackendProxyRequestDelegate OnPutRPBackendProxy { get; set; }

        /// <summary>
        /// Gets or sets the On Patch RP Backend Proxy.
        /// </summary>
        public PatchRPBackendProxyRequestDelegate OnPatchRPBackendProxy { get; set; }

        /// <summary>
        /// Gets or sets the On dbPrivateLinkResources.
        /// </summary>
        public GetRPBackendPrivateLinkRequestDelegate OnGetRPBackendPrivateLinkRequestDelegate { get; set; }

        /// <summary>
        /// The resource management controller.
        /// </summary>
        public class ResourceManagementController : ApiController
        {
            /// <summary>
            /// The resource provider test host.
            /// </summary>
            private readonly BaseResourceProviderTestHost host;

            /// <summary>
            /// Initializes a new instance of the <see cref="ResourceManagementController"/> class.
            /// </summary>
            /// <param name="host">The resource provider test host.</param>
            public ResourceManagementController(BaseResourceProviderTestHost host)
            {
                this.host = host;
            }

            /// <summary>
            /// Gets the tenant resource.
            /// </summary>
            /// <param name="resourceProviderNamespace">The resource provider namespace.</param>
            /// <param name="resourceType">The type of the top-level resource.</param>
            /// <param name="resourceName">Name of the resource.</param>
            /// <param name="wildcard">Optional. Rest of the path.</param>
            [HttpGet]
            public Task<HttpResponseMessage> GetTenantResource(string resourceProviderNamespace, string resourceType, string resourceName = null, string wildcard = null)
            {
                if (this.host.OnGetTenantResource != null)
                {
                    return Task.FromResult<HttpResponseMessage>(this.host.OnGetTenantResource(this.Request, resourceProviderNamespace, resourceType, resourceName, wildcard));
                }

                return Task.FromResult<HttpResponseMessage>(new HttpResponseMessage(HttpStatusCode.OK));
            }

            /// <summary>
            /// Puts the tenant resource.
            /// </summary>
            /// <param name="resourceProviderNamespace">The resource provider namespace.</param>
            /// <param name="resourceType">The type of the top-level resource.</param>
            /// <param name="resourceName">Name of the resource.</param>
            /// <param name="wildcard">Optional. Rest of the path.</param>
            [HttpPut]
            public Task<HttpResponseMessage> PutTenantResource(string resourceProviderNamespace, string resourceType, string resourceName = null, string wildcard = null)
            {
                if (this.host.OnPutTenantResource != null)
                {
                    return Task.FromResult<HttpResponseMessage>(this.host.OnPutTenantResource(this.Request, resourceProviderNamespace, resourceType, resourceName, wildcard));
                }

                return Task.FromResult<HttpResponseMessage>(new HttpResponseMessage(HttpStatusCode.OK));
            }

            /// <summary>
            /// Deletes the tenant resource.
            /// </summary>
            /// <param name="resourceProviderNamespace">The resource provider namespace.</param>
            /// <param name="resourceType">The type of the top-level resource.</param>
            /// <param name="resourceName">Name of the resource.</param>
            /// <param name="wildcard">Optional. Rest of the path.</param>
            [HttpDelete]
            public Task<HttpResponseMessage> DeleteTenantResource(string resourceProviderNamespace, string resourceType, string resourceName = null, string wildcard = null)
            {
                if (this.host.OnDeleteTenantResource != null)
                {
                    return Task.FromResult<HttpResponseMessage>(this.host.OnDeleteTenantResource(this.Request, resourceProviderNamespace, resourceType, resourceName, wildcard));
                }

                return Task.FromResult<HttpResponseMessage>(new HttpResponseMessage(HttpStatusCode.OK));
            }

            /// <summary>
            /// Gets the resource async operation.
            /// </summary>
            /// <param name="subscriptionId">The subscription identifier.</param>
            /// <param name="resourceGroupName">Name of the resource group.</param>
            /// <param name="resourceProviderNamespace">The resource provider namespace.</param>
            /// <param name="resourceType">The type of the top-level resource.</param>
            /// <param name="resourceName">Name of the resource.</param>
            /// <param name="asyncOperationId">The async operation identifier.</param>
            [HttpGet]
            [ActionName("GetAsyncOperation")]
            public Task<HttpResponseMessage> GetResourceAsyncOperation(string subscriptionId, string resourceGroupName, string resourceProviderNamespace, string resourceType, string resourceName, string asyncOperationId)
            {
                {
                    if (this.host.OnGetResourceAsyncOperation != null)
                    {
                        return Task.FromResult<HttpResponseMessage>(this.host.OnGetResourceAsyncOperation(this.Request, subscriptionId, resourceGroupName, resourceProviderNamespace, resourceType, resourceName, asyncOperationId));
                    }

                    return Task.FromResult<HttpResponseMessage>(new HttpResponseMessage(HttpStatusCode.OK));
                }
            }

            /// <summary>
            /// Gets the resource async operation.
            /// </summary>
            /// <param name="subscriptionId">The subscription identifier.</param>
            /// <param name="resourceProviderNamespace">The resource provider namespace.</param>
            /// <param name="location">The location.</param>
            /// <param name="asyncOperationId">The async operation identifier.</param>
            [HttpGet]
            [ActionName("GetAsyncOperationStatus")]
            public Task<HttpResponseMessage> GetResourceAsyncOperation(string subscriptionId, string resourceProviderNamespace, string location, string asyncOperationId)
            {
                if (this.host.OnGetAsyncOperationStatus != null)
                {
                    return Task.FromResult(this.host.OnGetAsyncOperationStatus(this.Request, subscriptionId, resourceProviderNamespace, location, asyncOperationId));
                }

                var result = new AsyncOperationResult { Status = ProvisioningState.Succeeded.ToString() };
                var message = new HttpResponseMessage(HttpStatusCode.OK)
                {
                    Content = new ObjectContent<AsyncOperationResult>(result, new JsonMediaTypeFormatter())
                };

                return Task.FromResult(message);
            }

            /// <summary>
            /// Gets the resource.
            /// </summary>
            /// <param name="subscriptionId">The subscription identifier.</param>
            /// <param name="resourceGroupName">Name of the resource group.</param>
            /// <param name="resourceProviderNamespace">The resource provider namespace.</param>
            /// <param name="resourceType">The type of the top-level resource.</param>
            /// <param name="resourceName">Name of the resource.</param>
            /// <param name="wildcard">Optional. Rest of the path.</param>
            [HttpGet]
            public Task<HttpResponseMessage> GetResource(string subscriptionId, string resourceGroupName, string resourceProviderNamespace, string resourceType, string resourceName, string wildcard = null)
            {
                if (this.host.OnGetResource != null)
                {
                    return Task.FromResult<HttpResponseMessage>(this.host.OnGetResource(this.Request, subscriptionId, resourceGroupName, resourceProviderNamespace, resourceType, resourceName, wildcard));
                }

                return Task.FromResult<HttpResponseMessage>(new HttpResponseMessage(HttpStatusCode.OK));
            }

            /// <summary>
            /// Resources the action.
            /// </summary>
            /// <param name="subscriptionId">The subscription identifier.</param>
            /// <param name="resourceProviderNamespace">The resource provider namespace.</param>
            /// <param name="resourceType">Type of the resource.</param>
            /// <param name="resourceName">Name of the resource.</param>
            /// <param name="actionName">Name of the action.</param>
            [HttpPost]
            public Task<HttpResponseMessage> SubscriptionResourceAction(string subscriptionId, string resourceProviderNamespace, string resourceType, string resourceName, string actionName)
            {
                if (this.host.OnSubscriptionResourceAction != null)
                {
                    return Task.FromResult<HttpResponseMessage>(this.host.OnSubscriptionResourceAction(this.Request, subscriptionId, resourceProviderNamespace, resourceType, resourceName, actionName));
                }

                return Task.FromResult<HttpResponseMessage>(new HttpResponseMessage(HttpStatusCode.OK));
            }

            /// <summary>
            /// Resources the action.
            /// </summary>
            /// <param name="subscriptionId">The subscription identifier.</param>
            /// <param name="resourceGroupName">Name of the resource group.</param>
            /// <param name="resourceProviderNamespace">The resource provider namespace.</param>
            /// <param name="resourceType">Type of the resource.</param>
            /// <param name="resourceName">Name of the resource.</param>
            /// <param name="actionName">Name of the action.</param>
            [HttpPost]
            public Task<HttpResponseMessage> ResourceAction(string subscriptionId, string resourceGroupName, string resourceProviderNamespace, string resourceType, string resourceName, string actionName)
            {
                if (this.host.OnResourceAction != null)
                {
                    return Task.FromResult<HttpResponseMessage>(this.host.OnResourceAction(this.Request, subscriptionId, resourceGroupName, resourceProviderNamespace, resourceType, resourceName, actionName));
                }

                return Task.FromResult<HttpResponseMessage>(new HttpResponseMessage(HttpStatusCode.OK));
            }

            /// <summary>
            /// Get activity status from the resource resource provider.
            /// </summary>
            /// <param name="resourceProviderNamespace">The resource provider namespace.</param>
            /// <param name="activityId">The activity Id.</param>
            [HttpGet]
            public Task<HttpResponseMessage> ResourceProviderActivityStatus(string resourceProviderNamespace, string activityId)
            {
                if (this.host.OnGetResourceProviderActivityStatus != null)
                {
                    return Task.FromResult<HttpResponseMessage>(this.host.OnGetResourceProviderActivityStatus(this.Request, resourceProviderNamespace, activityId));
                }

                return Task.FromResult<HttpResponseMessage>(new HttpResponseMessage(HttpStatusCode.OK));
            }

            /// <summary>
            /// Responds to notify management group.
            /// </summary>
            [HttpPost]
            [ActionName("NotifyProvider")]
            public HttpResponseMessage NotifyProvider()
            {
                if (this.Request.RequestUri.AbsoluteUri.EndsWithInsensitively("/notifySubscriptionUserInfo"))
                {
                    if (this.host.OnNotifyProvider != null)
                    {
                        return this.host.OnNotifyProvider(this.Request);
                    }

                    return this.Request.CreateResponse(HttpStatusCode.OK);
                }

                return this.Request.CreateResponse(HttpStatusCode.MethodNotAllowed);
            }

            /// <summary>
            /// Notify the resource provider for resource level action.
            /// </summary>
            /// <param name="subscriptionId">The subscription identifier.</param>
            /// <param name="resourceGroupName">Name of the resource group.</param>
            /// <param name="resourceProviderNamespace">The resource provider namespace.</param>
            /// <param name="resourceType">Type of the resource.</param>
            /// <param name="resourceName">Name of the resource.</param>
            /// <param name="notificationProviderNamespace">The notification provider namespace.</param>
            [HttpPost]
            [ActionName("notify")]
            public Task<HttpResponseMessage> NotifyProviderResource(string subscriptionId, string resourceGroupName, string resourceProviderNamespace, string resourceType, string resourceName, string notificationProviderNamespace)
            {
                if (this.host.OnNotifyProviderResource != null)
                {
                    return Task.FromResult<HttpResponseMessage>(this.host.OnNotifyProviderResource(this.Request, subscriptionId, resourceGroupName, resourceProviderNamespace, resourceType, resourceName, notificationProviderNamespace));
                }

                return Task.FromResult<HttpResponseMessage>(new HttpResponseMessage(HttpStatusCode.OK));
            }

            /// <summary>
            /// Notify the resource provider for subscription level resource action.
            /// </summary>
            /// <param name="subscriptionId">The subscription identifier.</param>
            /// <param name="resourceProviderNamespace">The resource provider namespace.</param>
            /// <param name="resourceType">Type of the resource.</param>
            /// <param name="resourceName">Name of the resource.</param>
            /// <param name="notificationProviderNamespace">The notification provider namespace.</param>
            [HttpPost]
            [ActionName("notify")]
            public Task<HttpResponseMessage> NotifyProviderResource(string subscriptionId, string resourceProviderNamespace, string resourceType, string resourceName, string notificationProviderNamespace)
            {
                if (this.host.OnNotifyProviderSubscriptionLevelResource != null)
                {
                    return Task.FromResult<HttpResponseMessage>(this.host.OnNotifyProviderSubscriptionLevelResource(this.Request, subscriptionId, resourceProviderNamespace, resourceType, resourceName, notificationProviderNamespace));
                }

                return Task.FromResult<HttpResponseMessage>(new HttpResponseMessage(HttpStatusCode.OK));
            }

            /// <summary>
            /// Notify the resource provider for tenant level resource action.
            /// </summary>
            /// <param name="resourceProviderNamespace">The resource provider namespace.</param>
            /// <param name="resourceType">Type of the resource.</param>
            /// <param name="resourceName">Name of the resource.</param>
            /// <param name="notificationProviderNamespace">The notification provider namespace.</param>
            [HttpPost]
            [ActionName("notify")]
            public Task<HttpResponseMessage> NotifyProviderResource(string resourceProviderNamespace, string resourceType, string resourceName, string notificationProviderNamespace)
            {
                if (this.host.OnNotifyProviderTenantLevelResource != null)
                {
                    return Task.FromResult<HttpResponseMessage>(this.host.OnNotifyProviderTenantLevelResource(this.Request, resourceProviderNamespace, resourceType, resourceName, notificationProviderNamespace));
                }

                return Task.FromResult<HttpResponseMessage>(new HttpResponseMessage(HttpStatusCode.OK));
            }

            /// <summary>
            /// Notify the resource provider for extension resource level action.
            /// </summary>
            /// <param name="subscriptionId">The subscription identifier.</param>
            /// <param name="resourceGroupName">Name of the resource group.</param>
            /// <param name="resourceProviderNamespace">The resource provider namespace.</param>
            /// <param name="resourceType">Type of the resource.</param>
            /// <param name="resourceName">Name of the resource.</param>
            /// <param name="extensionProviderNamespace">The extension provider namespace.</param>
            /// <param name="extensionResourceType">Type of the extension resource.</param>
            /// <param name="extensionResourceName">Name of the extension resource.</param>
            /// <param name="notificationProviderNamespace">The notification provider namespace.</param>
            [HttpPost]
            [ActionName("notify")]
            public Task<HttpResponseMessage> NotifyProviderResource(string subscriptionId, string resourceGroupName, string resourceProviderNamespace, string resourceType, string resourceName, string extensionProviderNamespace, string extensionResourceType, string extensionResourceName, string notificationProviderNamespace)
            {
                if (this.host.OnNotifyProviderExtensionResource != null)
                {
                    return Task.FromResult<HttpResponseMessage>(this.host.OnNotifyProviderExtensionResource(this.Request, subscriptionId, resourceGroupName, resourceProviderNamespace, resourceType, resourceName, extensionProviderNamespace, extensionResourceType, extensionResourceName, notificationProviderNamespace));
                }

                return Task.FromResult<HttpResponseMessage>(new HttpResponseMessage(HttpStatusCode.OK));
            }

            /// <summary>
            /// Notify the resource provider for nested resource level action.
            /// </summary>
            /// <param name="subscriptionId">The subscription identifier.</param>
            /// <param name="resourceGroupName">Name of the resource group.</param>
            /// <param name="resourceProviderNamespace">The resource provider namespace.</param>
            /// <param name="resourceType">Type of the resource.</param>
            /// <param name="resourceName">Name of the resource.</param>
            /// <param name="resourceTypeNestedOne">The type of the second-level resource.</param>
            /// <param name="resourceNameNestedOne">Name of second-tier resource.</param>
            /// <param name="notificationProviderNamespace">The notification provider namespace.</param>
            [HttpPost]
            [ActionName("notify")]
            public Task<HttpResponseMessage> NotifyProviderNestedResource(string subscriptionId, string resourceGroupName, string resourceProviderNamespace, string resourceType, string resourceName, string resourceTypeNestedOne, string resourceNameNestedOne, string notificationProviderNamespace)
            {
                if (this.host.OnNotifyProviderNestedResource != null)
                {
                    return Task.FromResult<HttpResponseMessage>(this.host.OnNotifyProviderNestedResource(this.Request, subscriptionId, resourceGroupName, resourceProviderNamespace, resourceType, resourceName, resourceTypeNestedOne, resourceNameNestedOne, notificationProviderNamespace));
                }

                return Task.FromResult<HttpResponseMessage>(new HttpResponseMessage(HttpStatusCode.OK));
            }

            /// <summary>
            /// Resources the action.
            /// </summary>
            /// <param name="subscriptionId">The subscription identifier.</param>
            /// <param name="resourceGroupName">Name of the resource group.</param>
            /// <param name="resourceProviderNamespace">The resource provider namespace.</param>
            /// <param name="resourceType">Type of the resource.</param>
            /// <param name="resourceName">Name of the resource.</param>
            /// <param name="nestedResourceType">Type of the nested resource.</param>
            /// <param name="nestedResourceName">Name of the nested resource.</param>
            /// <param name="actionName">Name of the action.</param>
            [HttpPost]
            public Task<HttpResponseMessage> OnNestedResourceAction(string subscriptionId, string resourceGroupName, string resourceProviderNamespace, string resourceType, string resourceName, string nestedResourceType, string nestedResourceName, string actionName)
            {
                if (this.host.OnNestedResourceAction != null)
                {
                    return Task.FromResult<HttpResponseMessage>(this.host.OnNestedResourceAction(this.Request, subscriptionId, resourceGroupName, resourceProviderNamespace, resourceType, resourceName, nestedResourceType, nestedResourceName, actionName));
                }

                return Task.FromResult<HttpResponseMessage>(new HttpResponseMessage(HttpStatusCode.OK));
            }

            /// <summary>
            /// Resources the action.
            /// </summary>
            /// <param name="subscriptionId">The subscription identifier.</param>
            /// <param name="resourceGroupName">Name of the resource group.</param>
            /// <param name="resourceProviderNamespace">The resource provider namespace.</param>
            /// <param name="resourceType">Type of the resource.</param>
            /// <param name="resourceName">Name of the resource.</param>
            /// <param name="resourceTypeNestedOne">The type of the second-level resource.</param>
            /// <param name="resourceNameNestedOne">Name of second-tier resource.</param>
            /// <param name="resourceTypeNestedTwo">The type of the third-level resource.</param>
            /// <param name="resourceNameNestedTwo">Name of third-tier resource.</param>
            /// <param name="actionName">Name of the action.</param>
            [HttpPost]
            public Task<HttpResponseMessage> OnSecondNestedResourceAction(string subscriptionId, string resourceGroupName, string resourceProviderNamespace, string resourceType, string resourceName, string resourceTypeNestedOne, string resourceNameNestedOne, string resourceTypeNestedTwo, string resourceNameNestedTwo, string actionName)
            {
                if (this.host.OnSecondNestedResourceAction != null)
                {
                    return Task.FromResult<HttpResponseMessage>(this.host.OnSecondNestedResourceAction(this.Request, subscriptionId, resourceGroupName, resourceProviderNamespace, resourceType, resourceName, resourceTypeNestedOne, resourceNameNestedOne, resourceTypeNestedTwo, resourceNameNestedTwo, actionName));
                }

                return Task.FromResult<HttpResponseMessage>(new HttpResponseMessage(HttpStatusCode.OK));
            }

            /// <summary>
            /// Resource group action.
            /// </summary>
            /// <param name="subscriptionId">The subscription identifier.</param>
            /// <param name="resourceGroupName">Name of the resource group.</param>
            /// <param name="actionName">Name of the action.</param>
            [HttpPost]
            public Task<HttpResponseMessage> ResourceGroupAction(string subscriptionId, string resourceGroupName, string actionName)
            {
                if (this.host.OnResourceGroupAction != null)
                {
                    return Task.FromResult<HttpResponseMessage>(this.host.OnResourceGroupAction(this.Request, subscriptionId, resourceGroupName, actionName));
                }

                return Task.FromResult<HttpResponseMessage>(new HttpResponseMessage(HttpStatusCode.OK));
            }

            /// <summary>
            /// Notify the resource provider for resource group level action.
            /// </summary>
            /// <param name="subscriptionId">The subscription identifier.</param>
            /// <param name="resourceGroupName">Name of the resource group.</param>
            /// <param name="notificationProviderNamespace">The notification provider namespace.</param>
            [HttpPost]
            [ActionName("notify")]
            public Task<HttpResponseMessage> NotifyProviderResourceGroup(string subscriptionId, string resourceGroupName, string notificationProviderNamespace)
            {
                if (this.host.OnNotifyProviderResourceGroup != null)
                {
                    return Task.FromResult<HttpResponseMessage>(this.host.OnNotifyProviderResourceGroup(this.Request, subscriptionId, resourceGroupName, notificationProviderNamespace));
                }

                return Task.FromResult<HttpResponseMessage>(new HttpResponseMessage(HttpStatusCode.OK));
            }

            /// <summary>
            /// Gets the resource.
            /// </summary>
            /// <param name="subscriptionId">The subscription identifier.</param>
            /// <param name="resourceGroupName">Name of the resource group.</param>
            /// <param name="resourceProviderNamespace">The resource provider namespace.</param>
            /// <param name="resourceType">The type of the top-level resource.</param>
            /// <param name="resourceName">Name of the resource.</param>
            /// <param name="resourceTypeNestedOne">The type of the second-level resource.</param>
            /// <param name="resourceNameNestedOne">Name of first nested resource.</param>
            /// <param name="wildcard">Optional. Rest of the path.</param>
            [HttpGet]
            public Task<HttpResponseMessage> GetResource(string subscriptionId, string resourceGroupName, string resourceProviderNamespace, string resourceType, string resourceName, string resourceTypeNestedOne, string resourceNameNestedOne, string wildcard = null)
            {
                if (this.host.OnGetNestedResource != null)
                {
                    return Task.FromResult<HttpResponseMessage>(this.host.OnGetNestedResource(this.Request, subscriptionId, resourceGroupName, resourceProviderNamespace, resourceType, resourceName, resourceTypeNestedOne, resourceNameNestedOne, wildcard));
                }

                return Task.FromResult<HttpResponseMessage>(new HttpResponseMessage(HttpStatusCode.OK));
            }

            /// <summary>
            /// Gets the resource.
            /// </summary>
            /// <param name="subscriptionId">The subscription identifier.</param>
            /// <param name="resourceGroupName">Name of the resource group.</param>
            /// <param name="resourceProviderNamespace">The resource provider namespace.</param>
            /// <param name="resourceType">The type of the top-level resource.</param>
            /// <param name="resourceName">Name of the resource.</param>
            /// <param name="resourceTypeNestedOne">The type of the second-level resource.</param>
            /// <param name="resourceNameNestedOne">Name of first nested resource.</param>
            /// <param name="resourceTypeNestedTwo">The type of the third-level resource.</param>
            /// <param name="resourceNameNestedTwo">Name of second nested resource.</param>
            /// <param name="wildcard">Optional. Rest of the path.</param>
            [HttpGet]
            public Task<HttpResponseMessage> GetResource(string subscriptionId, string resourceGroupName, string resourceProviderNamespace, string resourceType, string resourceName, string resourceTypeNestedOne, string resourceNameNestedOne, string resourceTypeNestedTwo, string resourceNameNestedTwo, string wildcard = null)
            {
                if (this.host.OnGetDeeperNestedResource != null)
                {
                    return Task.FromResult<HttpResponseMessage>(this.host.OnGetDeeperNestedResource(this.Request, subscriptionId, resourceGroupName, resourceProviderNamespace, resourceType, resourceName, resourceTypeNestedOne, resourceNameNestedOne, resourceTypeNestedTwo, resourceNameNestedTwo, wildcard));
                }

                return Task.FromResult<HttpResponseMessage>(new HttpResponseMessage(HttpStatusCode.OK));
            }

            /// <summary>
            /// Gets the resources of the specified type.
            /// </summary>
            /// <param name="subscriptionId">The subscription identifier.</param>
            /// <param name="resourceGroupName">Name of the resource group.</param>
            /// <param name="resourceProviderNamespace">The resource provider namespace.</param>
            /// <param name="resourceType">The type of the top-level resource.</param>
            [HttpGet]
            public Task<HttpResponseMessage> GetResources(string subscriptionId, string resourceGroupName, string resourceProviderNamespace, string resourceType)
            {
                if (this.host.OnGetResources != null)
                {
                    return Task.FromResult<HttpResponseMessage>(this.host.OnGetResources(this.Request, subscriptionId, resourceGroupName, resourceProviderNamespace, resourceType));
                }

                return Task.FromResult<HttpResponseMessage>(new HttpResponseMessage(HttpStatusCode.OK));
            }

            /// <summary>
            /// Gets the subscription-level resources of the specified type.
            /// </summary>
            /// <param name="subscriptionId">The subscription identifier.</param>
            /// <param name="resourceProviderNamespace">The resource provider namespace.</param>
            /// <param name="resourceType">The type of the top-level resource.</param>
            [HttpGet]
            public Task<HttpResponseMessage> GetSubscriptionResources(string subscriptionId, string resourceProviderNamespace, string resourceType)
            {
                if (this.host.OnGetSubscriptionResources != null)
                {
                    return Task.FromResult<HttpResponseMessage>(this.host.OnGetSubscriptionResources(this.Request, subscriptionId, resourceProviderNamespace, resourceType));
                }

                return Task.FromResult<HttpResponseMessage>(new HttpResponseMessage(HttpStatusCode.OK));
            }

            /// <summary>
            /// Gets registration assignments.
            /// </summary>
            /// <param name="subscriptionId">The subscription id.</param>
            [HttpGet]
            public Task<HttpResponseMessage> GetRegistrationAssignmentsSubscriptionResources(string subscriptionId)
            {
                return Task.FromResult(this.host.OnGetSubscriptionResources != null
                    ? this.host.OnGetSubscriptionResources(this.Request, subscriptionId, "Microsoft.ManagedServices", "registrationAssignments")
                    : new HttpResponseMessage(HttpStatusCode.OK));
            }

            /// <summary>
            /// GET the subscription-level resource.
            /// </summary>
            /// <param name="subscriptionId">The subscription identifier.</param>
            /// <param name="resourceProviderNamespace">The resource provider namespace.</param>
            /// <param name="resourceType">The type of the top-level resource.</param>
            /// <param name="resourceName">The resource name.</param>
            [HttpGet]
            public Task<HttpResponseMessage> GetSubscriptionResource(string subscriptionId, string resourceProviderNamespace, string resourceType, string resourceName)
            {
                if (this.host.OnGetSubscriptionResource != null)
                {
                    return Task.FromResult<HttpResponseMessage>(this.host.OnGetSubscriptionResource(this.Request, subscriptionId, resourceProviderNamespace, resourceType, resourceName));
                }

                return Task.FromResult<HttpResponseMessage>(new HttpResponseMessage(HttpStatusCode.OK));
            }

            /// <summary>
            /// Puts the subscription-level resources of the specified type.
            /// </summary>
            /// <param name="subscriptionId">The subscription identifier.</param>
            /// <param name="resourceProviderNamespace">The resource provider namespace.</param>
            /// <param name="resourceType">The type of the top-level resource.</param>
            /// <param name="resourceName">The resource name.</param>
            [HttpPut]
            public Task<HttpResponseMessage> PutSubscriptionResources(string subscriptionId, string resourceProviderNamespace, string resourceType, string resourceName)
            {
                if (this.host.OnPutSubscriptionResources != null)
                {
                    return Task.FromResult<HttpResponseMessage>(this.host.OnPutSubscriptionResources(this.Request, subscriptionId, resourceProviderNamespace, resourceType, resourceName));
                }

                return Task.FromResult<HttpResponseMessage>(new HttpResponseMessage(HttpStatusCode.OK));
            }

            /// <summary>
            /// Deletes the subscription-level resources of the specified type.
            /// </summary>
            /// <param name="subscriptionId">The subscription identifier.</param>
            /// <param name="resourceProviderNamespace">The resource provider namespace.</param>
            /// <param name="resourceType">The type of the top-level resource.</param>
            /// <param name="resourceName">The resource name.</param>
            [HttpDelete]
            public Task<HttpResponseMessage> DeleteSubscriptionResources(string subscriptionId, string resourceProviderNamespace, string resourceType, string resourceName)
            {
                if (this.host.OnDeleteSubscriptionResources != null)
                {
                    return Task.FromResult<HttpResponseMessage>(this.host.OnDeleteSubscriptionResources(this.Request, subscriptionId, resourceProviderNamespace, resourceType, resourceName));
                }

                return Task.FromResult<HttpResponseMessage>(new HttpResponseMessage(HttpStatusCode.OK));
            }

            /// <summary>
            /// Gets the resources of the specified type.
            /// </summary>
            /// <param name="subscriptionId">The subscription identifier.</param>
            /// <param name="resourceGroupName">Name of the resource group.</param>
            /// <param name="resourceProviderNamespace">The resource provider namespace.</param>
            /// <param name="resourceType">The type of the top-level resource.</param>
            /// <param name="resourceName">Name of the top-level resource.</param>
            /// <param name="resourceTypeNestedOne">The type of the second-level resource.</param>
            [HttpGet]
            public Task<HttpResponseMessage> GetResources(string subscriptionId, string resourceGroupName, string resourceProviderNamespace, string resourceType, string resourceName, string resourceTypeNestedOne)
            {
                if (this.host.OnGetNestedResources != null)
                {
                    return Task.FromResult<HttpResponseMessage>(this.host.OnGetNestedResources(this.Request, subscriptionId, resourceGroupName, resourceProviderNamespace, resourceType, resourceName, resourceTypeNestedOne));
                }

                return Task.FromResult<HttpResponseMessage>(new HttpResponseMessage(HttpStatusCode.OK));
            }

            /// <summary>
            /// Gets the resources of the specified type.
            /// </summary>
            /// <param name="subscriptionId">The subscription identifier.</param>
            /// <param name="resourceGroupName">Name of the resource group.</param>
            /// <param name="resourceProviderNamespace">The resource provider namespace.</param>
            /// <param name="resourceType">The type of the top-level resource.</param>
            /// <param name="resourceName">Name of the top-level resource.</param>
            /// <param name="resourceTypeNestedOne">The type of the second-level resource.</param>
            /// <param name="resourceNameNestedOne">Name of first nested resource.</param>
            /// <param name="resourceTypeNestedTwo">The type of the third-level resource.</param>
            [HttpGet]
            public Task<HttpResponseMessage> GetResources(string subscriptionId, string resourceGroupName, string resourceProviderNamespace, string resourceType, string resourceName, string resourceTypeNestedOne, string resourceNameNestedOne, string resourceTypeNestedTwo)
            {
                if (this.host.OnGetSecondNestedResources != null)
                {
                    return Task.FromResult<HttpResponseMessage>(this.host.OnGetSecondNestedResources(this.Request, subscriptionId, resourceGroupName, resourceProviderNamespace, resourceType, resourceName, resourceTypeNestedOne, resourceNameNestedOne, resourceTypeNestedTwo));
                }

                return Task.FromResult<HttpResponseMessage>(new HttpResponseMessage(HttpStatusCode.OK));
            }

            /// <summary>
            /// Catch-all for unknown URIs. Useful for debugging.
            /// </summary>
            /// <param name="wildcard">Unknown URI.</param>
            [HttpGet]
            public Task<HttpResponseMessage> GetAny(string wildcard)
            {
                return Task.FromResult<HttpResponseMessage>(new HttpResponseMessage(HttpStatusCode.NotFound));
            }

            /// <summary>
            /// HttpHead request for the resource.
            /// </summary>
            /// <param name="subscriptionId">The subscription identifier.</param>
            /// <param name="resourceGroupName">Name of the resource group.</param>
            /// <param name="resourceProviderNamespace">The resource provider namespace.</param>
            /// <param name="resourceType">The type of the top-level resource.</param>
            /// <param name="resourceName">Name of the resource.</param>
            /// <param name="wildcard">Optional. Rest of the path.</param>
            [HttpHead]
            public Task<HttpResponseMessage> HeadResource(string subscriptionId, string resourceGroupName, string resourceProviderNamespace, string resourceType, string resourceName, string wildcard = null)
            {
                if (this.host.OnHeadResource != null)
                {
                    return Task.FromResult<HttpResponseMessage>(this.host.OnHeadResource(this.Request, subscriptionId, resourceGroupName, resourceProviderNamespace, resourceType, resourceName, wildcard));
                }

                return Task.FromResult<HttpResponseMessage>(new HttpResponseMessage(HttpStatusCode.NoContent));
            }

            /// <summary>
            /// HttpHead request for the resource.
            /// </summary>
            /// <param name="subscriptionId">The subscription identifier.</param>
            /// <param name="resourceGroupName">Name of the resource group.</param>
            /// <param name="resourceProviderNamespace">The resource provider namespace.</param>
            /// <param name="resourceType">The type of the top-level resource.</param>
            /// <param name="resourceName">Name of the resource.</param>
            /// <param name="resourceTypeNestedOne">The type of the second-level resource.</param>
            /// <param name="resourceNameNestedOne">Name of first nested resource.</param>
            /// <param name="wildcard">Optional. Rest of the path.</param>
            [HttpHead]
            public Task<HttpResponseMessage> HeadResource(string subscriptionId, string resourceGroupName, string resourceProviderNamespace, string resourceType, string resourceName, string resourceTypeNestedOne, string resourceNameNestedOne, string wildcard = null)
            {
                if (this.host.OnHeadNestedResource != null)
                {
                    return Task.FromResult<HttpResponseMessage>(this.host.OnHeadNestedResource(this.Request, subscriptionId, resourceGroupName, resourceProviderNamespace, resourceType, resourceName, resourceTypeNestedOne, resourceNameNestedOne, wildcard));
                }

                return Task.FromResult<HttpResponseMessage>(new HttpResponseMessage(HttpStatusCode.NoContent));
            }

            /// <summary>
            /// HttpHead request for the resource.
            /// </summary>
            /// <param name="subscriptionId">The subscription identifier.</param>
            /// <param name="resourceGroupName">Name of the resource group.</param>
            /// <param name="resourceProviderNamespace">The resource provider namespace.</param>
            /// <param name="resourceType">The type of the top-level resource.</param>
            /// <param name="resourceName">Name of the resource.</param>
            /// <param name="resourceTypeNestedOne">The type of the second-level resource.</param>
            /// <param name="resourceNameNestedOne">Name of first nested resource.</param>
            /// <param name="resourceTypeNestedTwo">The type of the third-level resource.</param>
            /// <param name="resourceNameNestedTwo">Name of second nested resource.</param>
            /// <param name="wildcard">Optional. Rest of the path.</param>
            [HttpHead]
            public Task<HttpResponseMessage> HeadResource(string subscriptionId, string resourceGroupName, string resourceProviderNamespace, string resourceType, string resourceName, string resourceTypeNestedOne, string resourceNameNestedOne, string resourceTypeNestedTwo, string resourceNameNestedTwo, string wildcard = null)
            {
                if (this.host.OnHeadDeeperNestedResource != null)
                {
                    return Task.FromResult<HttpResponseMessage>(this.host.OnHeadDeeperNestedResource(this.Request, subscriptionId, resourceGroupName, resourceProviderNamespace, resourceType, resourceName, resourceTypeNestedOne, resourceNameNestedOne, resourceTypeNestedTwo, resourceNameNestedTwo, wildcard));
                }

                return Task.FromResult<HttpResponseMessage>(new HttpResponseMessage(HttpStatusCode.OK));
            }

            /// <summary>
            /// HttpHead request for the resources of the specified type.
            /// </summary>
            /// <param name="subscriptionId">The subscription identifier.</param>
            /// <param name="resourceGroupName">Name of the resource group.</param>
            /// <param name="resourceProviderNamespace">The resource provider namespace.</param>
            /// <param name="resourceType">The type of the top-level resource.</param>
            [HttpHead]
            public Task<HttpResponseMessage> HeadResources(string subscriptionId, string resourceGroupName, string resourceProviderNamespace, string resourceType)
            {
                if (this.host.OnHeadResources != null)
                {
                    return Task.FromResult<HttpResponseMessage>(this.host.OnHeadResources(this.Request, subscriptionId, resourceGroupName, resourceProviderNamespace, resourceType));
                }

                return Task.FromResult<HttpResponseMessage>(new HttpResponseMessage(HttpStatusCode.OK));
            }

            /// <summary>
            /// HttpHead request for the resources of the specified type.
            /// </summary>
            /// <param name="subscriptionId">The subscription identifier.</param>
            /// <param name="resourceGroupName">Name of the resource group.</param>
            /// <param name="resourceProviderNamespace">The resource provider namespace.</param>
            /// <param name="resourceType">The type of the top-level resource.</param>
            /// <param name="resourceName">Name of the top-level resource.</param>
            /// <param name="resourceTypeNestedOne">The type of the second-level resource.</param>
            [HttpHead]
            public Task<HttpResponseMessage> HeadResources(string subscriptionId, string resourceGroupName, string resourceProviderNamespace, string resourceType, string resourceName, string resourceTypeNestedOne)
            {
                if (this.host.OnHeadNestedResources != null)
                {
                    return Task.FromResult<HttpResponseMessage>(this.host.OnHeadNestedResources(this.Request, subscriptionId, resourceGroupName, resourceProviderNamespace, resourceType, resourceName, resourceTypeNestedOne));
                }

                return Task.FromResult<HttpResponseMessage>(new HttpResponseMessage(HttpStatusCode.OK));
            }

            /// <summary>
            /// Catch-all for unknown URIs. Useful for debugging.
            /// </summary>
            /// <param name="wildcard">Unknown URI.</param>
            [HttpHead]
            public Task<HttpResponseMessage> HeadAny(string wildcard)
            {
                return Task.FromResult<HttpResponseMessage>(new HttpResponseMessage(HttpStatusCode.NotFound));
            }

            /// <summary>
            /// Puts the resource.
            /// </summary>
            /// <param name="subscriptionId">The subscription identifier.</param>
            /// <param name="resourceGroupName">Name of the resource group.</param>
            /// <param name="resourceProviderNamespace">The resource provider namespace.</param>
            /// <param name="resourceType">Type of the resource.</param>
            /// <param name="resourceName">Name of the resource.</param>
            [HttpPut]
            public Task<HttpResponseMessage> PutResource(string subscriptionId, string resourceGroupName, string resourceProviderNamespace, string resourceType, string resourceName)
            {
                if (this.host.OnPutResource != null)
                {
                    return Task.FromResult<HttpResponseMessage>(this.host.OnPutResource(this.Request, subscriptionId, resourceGroupName, resourceProviderNamespace, resourceType, resourceName));
                }

                return Task.FromResult<HttpResponseMessage>(new HttpResponseMessage(HttpStatusCode.Created));
            }

            /// <summary>
            /// Puts the resource.
            /// </summary>
            /// <param name="subscriptionId">The subscription identifier.</param>
            /// <param name="resourceGroupName">Name of the resource group.</param>
            /// <param name="resourceProviderNamespace">The resource provider namespace.</param>
            /// <param name="resourceType">The type of the top-level resource.</param>
            /// <param name="resourceName">Name of the resource.</param>
            /// <param name="resourceTypeNestedOne">The type of the second-level resource.</param>
            /// <param name="resourceNameNestedOne">Name of first nested resource.</param>
            /// <param name="wildcard">Optional. Rest of the path.</param>
            [HttpPut]
            public Task<HttpResponseMessage> PutResource(string subscriptionId, string resourceGroupName, string resourceProviderNamespace, string resourceType, string resourceName, string resourceTypeNestedOne, string resourceNameNestedOne, string wildcard = null)
            {
                if (this.host.OnPutNestedResource != null)
                {
                    return Task.FromResult<HttpResponseMessage>(this.host.OnPutNestedResource(this.Request, subscriptionId, resourceGroupName, resourceProviderNamespace, resourceType, resourceName, resourceTypeNestedOne, resourceNameNestedOne, wildcard));
                }

                return Task.FromResult<HttpResponseMessage>(new HttpResponseMessage(HttpStatusCode.Created));
            }

            /// <summary>
            /// Puts the resource.
            /// </summary>
            /// <param name="subscriptionId">The subscription identifier.</param>
            /// <param name="resourceGroupName">Name of the resource group.</param>
            /// <param name="resourceProviderNamespace">The resource provider namespace.</param>
            /// <param name="resourceType">The type of the top-level resource.</param>
            /// <param name="resourceName">Name of the resource.</param>
            /// <param name="resourceTypeNestedOne">The type of the second-level resource.</param>
            /// <param name="resourceNameNestedOne">Name of first nested resource.</param>
            /// <param name="resourceTypeNestedTwo">The type of the third-level resource.</param>
            /// <param name="resourceNameNestedTwo">Name of second nested resource.</param>
            /// <param name="wildcard">Optional. Rest of the path.</param>
            [HttpPut]
            public Task<HttpResponseMessage> PutResource(string subscriptionId, string resourceGroupName, string resourceProviderNamespace, string resourceType, string resourceName, string resourceTypeNestedOne, string resourceNameNestedOne, string resourceTypeNestedTwo, string resourceNameNestedTwo, string wildcard = null)
            {
                if (this.host.OnPutSecondNestedResource != null)
                {
                    return Task.FromResult<HttpResponseMessage>(this.host.OnPutSecondNestedResource(this.Request, subscriptionId, resourceGroupName, resourceProviderNamespace, resourceType, resourceName, resourceTypeNestedOne, resourceNameNestedOne, resourceTypeNestedTwo, resourceNameNestedTwo, wildcard));
                }

                return Task.FromResult<HttpResponseMessage>(new HttpResponseMessage(HttpStatusCode.Created));
            }

            /// <summary>
            /// Patches the resource.
            /// </summary>
            /// <param name="subscriptionId">The subscription identifier.</param>
            /// <param name="resourceGroupName">Name of the resource group.</param>
            /// <param name="resourceProviderNamespace">The resource provider namespace.</param>
            /// <param name="resourceType">Type of the resource.</param>
            /// <param name="resourceName">Name of the resource.</param>
            [HttpPatch]
            public Task<HttpResponseMessage> PatchResource(string subscriptionId, string resourceGroupName, string resourceProviderNamespace, string resourceType, string resourceName)
            {
                if (this.host.OnPatchResource != null)
                {
                    return Task.FromResult<HttpResponseMessage>(this.host.OnPatchResource(this.Request, subscriptionId, resourceGroupName, resourceProviderNamespace, resourceType, resourceName));
                }

                return Task.FromResult<HttpResponseMessage>(new HttpResponseMessage(HttpStatusCode.Created));
            }

            /// <summary>
            /// Patches the resource.
            /// </summary>
            /// <param name="subscriptionId">The subscription identifier.</param>
            /// <param name="resourceGroupName">Name of the resource group.</param>
            /// <param name="resourceProviderNamespace">The resource provider namespace.</param>
            /// <param name="resourceType">Type of the resource.</param>
            /// <param name="resourceName">Name of the resource.</param>
            /// <param name="resourceTypeNestedOne">The resource type nested one.</param>
            /// <param name="resourceNameNestedOne">The resource name nested one.</param>
            /// <param name="wildcard">The wildcard.</param>
            [HttpPatch]
            public Task<HttpResponseMessage> PatchResource(string subscriptionId, string resourceGroupName, string resourceProviderNamespace, string resourceType, string resourceName, string resourceTypeNestedOne, string resourceNameNestedOne, string wildcard = null)
            {
                if (this.host.OnPatchNestedResource != null)
                {
                    return Task.FromResult<HttpResponseMessage>(this.host.OnPatchNestedResource(this.Request, subscriptionId, resourceGroupName, resourceProviderNamespace, resourceType, resourceName, resourceTypeNestedOne, resourceNameNestedOne, wildcard));
                }

                return Task.FromResult<HttpResponseMessage>(new HttpResponseMessage(HttpStatusCode.Created));
            }

            /// <summary>
            /// Deletes the resource.
            /// </summary>
            /// <param name="subscriptionId">The subscription identifier.</param>
            /// <param name="resourceGroupName">Name of the resource group.</param>
            /// <param name="resourceProviderNamespace">The resource provider namespace.</param>
            /// <param name="resourceType">Type of the resource.</param>
            /// <param name="resourceName">Name of the resource.</param>
            [HttpDelete]
            public Task<HttpResponseMessage> DeleteResource(string subscriptionId, string resourceGroupName, string resourceProviderNamespace, string resourceType, string resourceName)
            {
                if (this.host.OnDeleteResource != null)
                {
                    return Task.FromResult<HttpResponseMessage>(this.host.OnDeleteResource(this.Request, subscriptionId, resourceGroupName, resourceProviderNamespace, resourceType, resourceName));
                }

                return Task.FromResult<HttpResponseMessage>(new HttpResponseMessage(HttpStatusCode.OK));
            }

            /// <summary>
            /// Deletes the resource.
            /// </summary>
            /// <param name="subscriptionId">The subscription identifier.</param>
            /// <param name="resourceGroupName">Name of the resource group.</param>
            /// <param name="resourceProviderNamespace">The resource provider namespace.</param>
            /// <param name="resourceType">The type of the top-level resource.</param>
            /// <param name="resourceName">Name of the resource.</param>
            /// <param name="resourceTypeNestedOne">The type of the second-level resource.</param>
            /// <param name="resourceNameNestedOne">Name of first nested resource.</param>
            /// <param name="wildcard">Optional. Rest of the path.</param>
            [HttpDelete]
            public Task<HttpResponseMessage> DeleteResource(string subscriptionId, string resourceGroupName, string resourceProviderNamespace, string resourceType, string resourceName, string resourceTypeNestedOne, string resourceNameNestedOne, string wildcard = null)
            {
                if (this.host.OnDeleteNestedResource != null)
                {
                    return Task.FromResult<HttpResponseMessage>(this.host.OnDeleteNestedResource(this.Request, subscriptionId, resourceGroupName, resourceProviderNamespace, resourceType, resourceName, resourceTypeNestedOne, resourceNameNestedOne, wildcard));
                }

                return Task.FromResult<HttpResponseMessage>(new HttpResponseMessage(HttpStatusCode.OK));
            }

            /// <summary>
            /// Deletes the resource.
            /// </summary>
            /// <param name="subscriptionId">The subscription identifier.</param>
            /// <param name="resourceGroupName">Name of the resource group.</param>
            /// <param name="resourceProviderNamespace">The resource provider namespace.</param>
            /// <param name="resourceType">The type of the top-level resource.</param>
            /// <param name="resourceName">Name of the resource.</param>
            /// <param name="resourceTypeNestedOne">The type of the second-level resource.</param>
            /// <param name="resourceNameNestedOne">Name of first nested resource.</param>
            /// <param name="resourceTypeNestedTwo">The type of the third-level resource.</param>
            /// <param name="resourceNameNestedTwo">Name of second nested resource.</param>
            /// <param name="wildcard">Optional. Rest of the path.</param>
            [HttpDelete]
            public Task<HttpResponseMessage> DeleteResource(string subscriptionId, string resourceGroupName, string resourceProviderNamespace, string resourceType, string resourceName, string resourceTypeNestedOne, string resourceNameNestedOne, string resourceTypeNestedTwo, string resourceNameNestedTwo, string wildcard = null)
            {
                if (this.host.OnDeleteSecondNestedResource != null)
                {
                    return Task.FromResult<HttpResponseMessage>(this.host.OnDeleteSecondNestedResource(this.Request, subscriptionId, resourceGroupName, resourceProviderNamespace, resourceType, resourceName, resourceTypeNestedOne, resourceNameNestedOne, resourceTypeNestedTwo, resourceNameNestedTwo, wildcard));
                }

                return Task.FromResult<HttpResponseMessage>(new HttpResponseMessage(HttpStatusCode.OK));
            }

            /// <summary>
            /// Deletes the resource group.
            /// </summary>
            /// <param name="subscriptionId">The subscription identifier.</param>
            /// <param name="resourceGroupName">Name of the resource group.</param>
            [HttpDelete]
            public Task<HttpResponseMessage> DeleteResource(string subscriptionId, string resourceGroupName)
            {
                if (this.host.OnDeleteResourceGroup != null)
                {
                    return Task.FromResult<HttpResponseMessage>(this.host.OnDeleteResourceGroup(this.Request, subscriptionId, resourceGroupName));
                }

                return Task.FromResult<HttpResponseMessage>(new HttpResponseMessage(HttpStatusCode.OK));
            }

            /// <summary>
            /// Resource provider action at the resource group level
            /// </summary>
            /// <param name="subscriptionId">The subscription identifier.</param>
            /// <param name="resourceGroupName">Name of the resource group.</param>
            /// <param name="resourceProviderNamespace">The name of the resource provider.</param>
            /// <param name="actionName">Name of the action</param>
            [HttpPost]
            public Task<HttpResponseMessage> ResourceProviderAction(string subscriptionId, string resourceGroupName, string resourceProviderNamespace, string actionName)
            {
                if (this.host.OnResourceGroupProviderAction != null)
                {
                    return Task.FromResult<HttpResponseMessage>(this.host.OnResourceGroupProviderAction(this.Request, subscriptionId, resourceGroupName, resourceProviderNamespace, actionName));
                }

                return Task.FromResult<HttpResponseMessage>(new HttpResponseMessage(HttpStatusCode.OK));
            }
        }

        #endregion

        #region ExtensionResourceManagementController

        /// <summary>
        /// The extension resource management controller.
        /// </summary>
        public class ExtensionResourceManagementController : ApiController
        {
            /// <summary>
            /// The resource provider test host.
            /// </summary>
            private readonly BaseResourceProviderTestHost host;

            /// <summary>
            /// Initializes a new instance of the <see cref="ExtensionResourceManagementController"/> class.
            /// </summary>
            /// <param name="host">The resource provider test host.</param>
            public ExtensionResourceManagementController(BaseResourceProviderTestHost host)
            {
                this.host = host;
            }

            /// <summary>
            /// Gets the extension resources.
            /// </summary>
            /// <param name="subscriptionId">The subscription identifier.</param>
            [HttpPut]
            public Task<HttpResponseMessage> PutExtensionSubscription(string subscriptionId)
            {
                if (this.host.OnPutExtensionSubscription != null)
                {
                    return Task.FromResult<HttpResponseMessage>(this.host.OnPutExtensionSubscription(this.Request, subscriptionId));
                }

                return Task.FromResult<HttpResponseMessage>(new HttpResponseMessage(HttpStatusCode.OK));
            }

            /// <summary>
            /// Gets the extension resources.
            /// </summary>
            /// <param name="subscriptionId">The subscription identifier.</param>
            /// <param name="resourceGroupName">Name of the resource group.</param>
            /// <param name="resourceProviderNamespace">The resource provider namespace.</param>
            /// <param name="resourceType">The type of the top-level resource.</param>
            /// <param name="resourceName">Name of the resource.</param>
            /// <param name="extensionProviderNamespace">The extension provider namespace.</param>
            /// <param name="extensionResourceType">The extension resource type.</param>
            [HttpGet]
            public Task<HttpResponseMessage> GetExtensionResources(string subscriptionId, string resourceGroupName, string resourceProviderNamespace, string resourceType, string resourceName, string extensionProviderNamespace, string extensionResourceType)
            {
                if (this.host.OnGetExtensionResources != null)
                {
                    return Task.FromResult<HttpResponseMessage>(this.host.OnGetExtensionResources(this.Request, subscriptionId, resourceGroupName, resourceProviderNamespace, resourceType, resourceName, extensionProviderNamespace, extensionResourceType));
                }

                return Task.FromResult<HttpResponseMessage>(new HttpResponseMessage(HttpStatusCode.OK));
            }

            /// <summary>
            /// Gets the cascade extension resources.
            /// </summary>
            /// <param name="subscriptionId">The subscription identifier.</param>
            /// <param name="resourceGroupName">Name of the resource group.</param>
            /// <param name="resourceProviderNamespace">The resource provider namespace.</param>
            /// <param name="resourceType">The type of the top-level resource.</param>
            /// <param name="resourceName">Name of the resource.</param>
            /// <param name="extensionProviderNamespace">The extension provider namespace.</param>
            /// <param name="extensionResourceType">The first extension resource type.</param>
            /// <param name="cascadeExtensionProviderNamespace">The cascade extension provider namespace.</param>
            /// <param name="cascadeExtensionResourceType">The cascade extension resource type.</param>
            [HttpGet]
            public Task<HttpResponseMessage> GetCascadeExtensionResource(string subscriptionId, string resourceGroupName, string resourceProviderNamespace, string resourceType, string resourceName, string extensionProviderNamespace, string extensionResourceType, string cascadeExtensionProviderNamespace, string cascadeExtensionResourceType)
            {
                if (this.host.OnGetCascadeExtensionResources != null)
                {
                    return Task.FromResult<HttpResponseMessage>(this.host.OnGetCascadeExtensionResources(this.Request, subscriptionId, resourceGroupName, resourceProviderNamespace, resourceType, resourceName, extensionProviderNamespace, extensionResourceType, cascadeExtensionProviderNamespace, cascadeExtensionResourceType));
                }

                return Task.FromResult<HttpResponseMessage>(new HttpResponseMessage(HttpStatusCode.OK));
            }

            /// <summary>
            /// Gets the extension resource.
            /// </summary>
            /// <param name="subscriptionId">The subscription identifier.</param>
            /// <param name="resourceGroupName">Name of the resource group.</param>
            /// <param name="resourceProviderNamespace">The resource provider namespace.</param>
            /// <param name="resourceType">The type of the top-level resource.</param>
            /// <param name="resourceName">Name of the resource.</param>
            /// <param name="extensionProviderNamespace">The extension provider namespace.</param>
            /// <param name="extensionResourceType">The extension resource type.</param>
            /// <param name="extensionResourceName">The extension resource name.</param>
            [HttpGet]
            public Task<HttpResponseMessage> GetExtensionResource(string subscriptionId, string resourceGroupName, string resourceProviderNamespace, string resourceType, string resourceName, string extensionProviderNamespace, string extensionResourceType, string extensionResourceName)
            {
                if (this.host.OnGetExtensionResource != null)
                {
                    return Task.FromResult<HttpResponseMessage>(this.host.OnGetExtensionResource(this.Request, subscriptionId, resourceGroupName, resourceProviderNamespace, resourceType, resourceName, extensionProviderNamespace, extensionResourceType, extensionResourceName));
                }

                return Task.FromResult<HttpResponseMessage>(new HttpResponseMessage(HttpStatusCode.OK));
            }

            /// <summary>
            /// Gets the resource.
            /// </summary>
            /// <param name="subscriptionId">The subscription identifier.</param>
            /// <param name="resourceGroupName">Name of the resource group.</param>
            /// <param name="resourceProviderNamespace">The resource provider namespace.</param>
            /// <param name="resourceType">The type of the top-level resource.</param>
            /// <param name="resourceName">Name of the resource.</param>
            /// <param name="resourceTypeNestedOne">The type of the second-level resource.</param>
            /// <param name="resourceNameNestedOne">Name of first nested resource.</param>
            /// <param name="extensionProviderNamespace">The extension provider namespace.</param>
            /// <param name="extensionResourceType">The extension resource type.</param>
            /// <param name="extensionResourceName">The extension resource name.</param>
            [HttpGet]
            public Task<HttpResponseMessage> GetExtensionResource(string subscriptionId, string resourceGroupName, string resourceProviderNamespace, string resourceType, string resourceName, string resourceTypeNestedOne, string resourceNameNestedOne, string extensionProviderNamespace, string extensionResourceType, string extensionResourceName)
            {
                if (this.host.OnGetExtensionNestedResource != null)
                {
                    return Task.FromResult<HttpResponseMessage>(this.host.OnGetExtensionNestedResource(this.Request, subscriptionId, resourceGroupName, resourceProviderNamespace, resourceType, resourceName, resourceTypeNestedOne, resourceNameNestedOne, extensionProviderNamespace, extensionResourceType, extensionResourceName));
                }

                return Task.FromResult<HttpResponseMessage>(new HttpResponseMessage(HttpStatusCode.OK));
            }

            /// <summary>
            /// Gets the resource.
            /// </summary>
            /// <param name="subscriptionId">The subscription identifier.</param>
            /// <param name="resourceGroupName">Name of the resource group.</param>
            /// <param name="resourceProviderNamespace">The resource provider namespace.</param>
            /// <param name="resourceType">The type of the top-level resource.</param>
            /// <param name="resourceName">Name of the resource.</param>
            /// <param name="resourceTypeNestedOne">The type of the second-level resource.</param>
            /// <param name="resourceNameNestedOne">Name of first nested resource.</param>
            /// <param name="resourceTypeNestedTwo">The type of the third-level resource.</param>
            /// <param name="resourceNameNestedTwo">Name of second nested resource.</param>
            /// <param name="extensionProviderNamespace">The extension provider namespace.</param>
            /// <param name="extensionResourceType">The extension resource type.</param>
            /// <param name="extensionResourceName">The extension resource name.</param>
            [HttpGet]
            public Task<HttpResponseMessage> GetExtensionResource(string subscriptionId, string resourceGroupName, string resourceProviderNamespace, string resourceType, string resourceName, string resourceTypeNestedOne, string resourceNameNestedOne, string resourceTypeNestedTwo, string resourceNameNestedTwo, string extensionProviderNamespace, string extensionResourceType, string extensionResourceName)
            {
                if (this.host.OnGetExtensionDeeperNestedResource != null)
                {
                    return Task.FromResult<HttpResponseMessage>(this.host.OnGetExtensionDeeperNestedResource(this.Request, subscriptionId, resourceGroupName, resourceProviderNamespace, resourceType, resourceName, resourceTypeNestedOne, resourceNameNestedOne, resourceTypeNestedTwo, resourceNameNestedTwo, extensionProviderNamespace, extensionResourceType, extensionResourceName));
                }

                return Task.FromResult<HttpResponseMessage>(new HttpResponseMessage(HttpStatusCode.OK));
            }

            /// <summary>
            /// Puts the extension resource.
            /// </summary>
            /// <param name="subscriptionId">The subscription identifier.</param>
            /// <param name="resourceGroupName">Name of the resource group.</param>
            /// <param name="resourceProviderNamespace">The resource provider namespace.</param>
            /// <param name="resourceType">The type of the top-level resource.</param>
            /// <param name="resourceName">Name of the resource.</param>
            /// <param name="extensionProviderNamespace">The extension provider namespace.</param>
            /// <param name="extensionResourceType">The extension resource type.</param>
            /// <param name="extensionResourceName">The extension resource name.</param>
            [HttpPut]
            public Task<HttpResponseMessage> PutExtensionResource(string subscriptionId, string resourceGroupName, string resourceProviderNamespace, string resourceType, string resourceName, string extensionProviderNamespace, string extensionResourceType, string extensionResourceName)
            {
                if (this.host.OnPutExtensionResource != null)
                {
                    return Task.FromResult<HttpResponseMessage>(this.host.OnPutExtensionResource(this.Request, subscriptionId, resourceGroupName, resourceProviderNamespace, resourceType, resourceName, extensionProviderNamespace, extensionResourceType, extensionResourceName));
                }

                return Task.FromResult<HttpResponseMessage>(new HttpResponseMessage(HttpStatusCode.OK));
            }

            /// <summary>
            /// Puts the extension resource under a nested resource.
            /// </summary>
            /// <param name="subscriptionId">The subscription identifier.</param>
            /// <param name="resourceGroupName">Name of the resource group.</param>
            /// <param name="resourceProviderNamespace">The resource provider namespace.</param>
            /// <param name="resourceType">The type of the top-level resource.</param>
            /// <param name="resourceName">Name of the resource.</param>
            /// <param name="resourceTypeNestedOne">The type of the nested resource.</param>
            /// <param name="resourceNameNestedOne">Name of the nested resource.</param>
            /// <param name="extensionProviderNamespace">The extension provider namespace.</param>
            /// <param name="extensionResourceType">The extension resource type.</param>
            /// <param name="extensionResourceName">The extension resource name.</param>
            [HttpPut]
            public Task<HttpResponseMessage> PutExtensionResource(string subscriptionId, string resourceGroupName, string resourceProviderNamespace, string resourceType, string resourceName, string resourceTypeNestedOne, string resourceNameNestedOne, string extensionProviderNamespace, string extensionResourceType, string extensionResourceName)
            {
                if (this.host.OnPutExtensionNestedResource != null)
                {
                    return Task.FromResult<HttpResponseMessage>(this.host.OnPutExtensionNestedResource(this.Request, subscriptionId, resourceGroupName, resourceProviderNamespace, resourceType, resourceName, resourceTypeNestedOne, resourceNameNestedOne, extensionProviderNamespace, extensionResourceType, extensionResourceName));
                }

                return Task.FromResult<HttpResponseMessage>(new HttpResponseMessage(HttpStatusCode.OK));
            }

            /// <summary>
            /// Deletes the extension resource.
            /// </summary>
            /// <param name="subscriptionId">The subscription identifier.</param>
            /// <param name="resourceGroupName">Name of the resource group.</param>
            /// <param name="resourceProviderNamespace">The resource provider namespace.</param>
            /// <param name="resourceType">The type of the top-level resource.</param>
            /// <param name="resourceName">Name of the resource.</param>
            /// <param name="extensionProviderNamespace">The extension provider namespace.</param>
            /// <param name="extensionResourceType">The extension resource type.</param>
            /// <param name="extensionResourceName">The extension resource name.</param>
            [HttpDelete]
            public Task<HttpResponseMessage> DeleteExtensionResource(string subscriptionId, string resourceGroupName, string resourceProviderNamespace, string resourceType, string resourceName, string extensionProviderNamespace, string extensionResourceType, string extensionResourceName)
            {
                if (this.host.OnDeleteExtensionResource != null)
                {
                    return Task.FromResult<HttpResponseMessage>(this.host.OnDeleteExtensionResource(this.Request, subscriptionId, resourceGroupName, resourceProviderNamespace, resourceType, resourceName, extensionProviderNamespace, extensionResourceType, extensionResourceName));
                }

                return Task.FromResult<HttpResponseMessage>(new HttpResponseMessage(HttpStatusCode.OK));
            }

            /// <summary>
            /// Notify the extension resource provider.
            /// </summary>
            /// <param name="subscriptionId">The subscription identifier.</param>
            /// <param name="resourceGroupName">Name of the resource group.</param>
            /// <param name="extensionProviderNamespace">The extension provider namespace.</param>
            [HttpPost]
            public Task<HttpResponseMessage> NotifyExtensionProviderResourceGroup(string subscriptionId, string resourceGroupName, string extensionProviderNamespace)
            {
                if (this.host.OnNotifyExtensionProviderResourceGroup != null)
                {
                    return Task.FromResult<HttpResponseMessage>(this.host.OnNotifyExtensionProviderResourceGroup(this.Request, subscriptionId, resourceGroupName, extensionProviderNamespace));
                }

                return Task.FromResult<HttpResponseMessage>(new HttpResponseMessage(HttpStatusCode.OK));
            }

            /// <summary>
            /// Notify the extension resource provider.
            /// </summary>
            /// <param name="subscriptionId">The subscription identifier.</param>
            /// <param name="resourceGroupName">Name of the resource group.</param>
            /// <param name="resourceProviderNamespace">The resource provider namespace.</param>
            /// <param name="resourceType">The type of the top-level resource.</param>
            /// <param name="resourceName">Name of the resource.</param>
            /// <param name="extensionProviderNamespace">The extension provider namespace.</param>
            [HttpPost]
            public Task<HttpResponseMessage> NotifyExtensionProviderResource(string subscriptionId, string resourceGroupName, string resourceProviderNamespace, string resourceType, string resourceName, string extensionProviderNamespace)
            {
                if (this.host.OnNotifyExtensionProviderResource != null)
                {
                    return Task.FromResult<HttpResponseMessage>(this.host.OnNotifyExtensionProviderResource(this.Request, subscriptionId, resourceGroupName, resourceProviderNamespace, resourceType, resourceName, extensionProviderNamespace));
                }

                return Task.FromResult<HttpResponseMessage>(new HttpResponseMessage(HttpStatusCode.OK));
            }

            /// <summary>
            /// Get activity status from the extension resource provider.
            /// </summary>
            /// <param name="extensionProviderNamespace">The extension provider namespace.</param>
            /// <param name="activityId">The activity Id.</param>
            [HttpGet]
            public Task<HttpResponseMessage> GetExtensionProviderActivityStatus(string extensionProviderNamespace, string activityId)
            {
                if (this.host.OnGetExtensionProviderActivityStatus != null)
                {
                    return Task.FromResult<HttpResponseMessage>(this.host.OnGetExtensionProviderActivityStatus(this.Request, extensionProviderNamespace, activityId));
                }

                return Task.FromResult<HttpResponseMessage>(new HttpResponseMessage(HttpStatusCode.OK));
            }
        }

        #endregion

        #region TenantExtensionResourceManagementController

        /// <summary>
        /// The tenant extension resource management controller.
        /// </summary>
        public class TenantExtensionResourceManagementController : ApiController
        {
            /// <summary>
            /// The resource provider test host.
            /// </summary>
            private readonly BaseResourceProviderTestHost host;

            /// <summary>
            /// Initializes a new instance of the <see cref="TenantExtensionResourceManagementController"/> class.
            /// </summary>
            /// <param name="host">The resource provider test host.</param>
            public TenantExtensionResourceManagementController(BaseResourceProviderTestHost host)
            {
                this.host = host;
            }

            /// <summary>
            /// Gets the extension resources for tenant provider level requests.
            /// </summary>
            /// <param name="resourceProviderNamespace">The resource provider namespace.</param>
            /// <param name="extensionProviderNamespace">The extension provider namespace.</param>
            /// <param name="extensionResourceType">The extension resource type.</param>
            [HttpGet]
            public Task<HttpResponseMessage> GetTenantProviderExtensionResources(string resourceProviderNamespace, string extensionProviderNamespace, string extensionResourceType)
            {
                if (this.host.OnGetTenantProviderExtensionResourcesDelegate != null)
                {
                    return Task.FromResult<HttpResponseMessage>(this.host.OnGetTenantProviderExtensionResourcesDelegate(this.Request, resourceProviderNamespace, extensionProviderNamespace, extensionResourceType));
                }

                return Task.FromResult<HttpResponseMessage>(new HttpResponseMessage(HttpStatusCode.OK));
            }

            /// <summary>
            /// Puts the extension resources for tenant provider level requests.
            /// </summary>
            /// <param name="resourceProviderNamespace">The resource provider namespace.</param>
            /// <param name="extensionProviderNamespace">The extension provider namespace.</param>
            /// <param name="extensionResourceType">The extension resource type.</param>
            /// <param name="extensionResourceName">The extension resource name.</param>
            [HttpPut]
            public Task<HttpResponseMessage> PutTenantProviderExtensionResource(string resourceProviderNamespace, string extensionProviderNamespace, string extensionResourceType, string extensionResourceName)
            {
                if (this.host.OnPutTenantProviderExtensionResourceDelegate != null)
                {
                    return Task.FromResult<HttpResponseMessage>(this.host.OnPutTenantProviderExtensionResourceDelegate(this.Request, resourceProviderNamespace, extensionProviderNamespace, extensionResourceType, extensionResourceName));
                }

                return Task.FromResult<HttpResponseMessage>(new HttpResponseMessage(HttpStatusCode.OK));
            }

            /// <summary>
            /// Deletes the extension resources for tenant provider level requests.
            /// </summary>
            /// <param name="resourceProviderNamespace">The resource provider namespace.</param>
            /// <param name="extensionProviderNamespace">The extension provider namespace.</param>
            /// <param name="extensionResourceType">The extension resource type.</param>
            /// <param name="extensionResourceName">The extension resource name.</param>
            [HttpDelete]
            public Task<HttpResponseMessage> DeleteTenantProviderExtensionResource(string resourceProviderNamespace, string extensionProviderNamespace, string extensionResourceType, string extensionResourceName)
            {
                if (this.host.OnDeleteTenantProviderExtensionResourceDelegate != null)
                {
                    return Task.FromResult<HttpResponseMessage>(this.host.OnDeleteTenantProviderExtensionResourceDelegate(this.Request, resourceProviderNamespace, extensionProviderNamespace, extensionResourceType, extensionResourceName));
                }

                return Task.FromResult<HttpResponseMessage>(new HttpResponseMessage(HttpStatusCode.OK));
            }

            /// <summary>
            /// Gets the extension resource for tenant provider level requests.
            /// </summary>
            /// <param name="resourceProviderNamespace">The resource provider namespace.</param>
            /// <param name="extensionProviderNamespace">The extension provider namespace.</param>
            /// <param name="extensionResourceType">The extension resource type.</param>
            /// <param name="extensionResourceName">The extension resource name.</param>
            [HttpGet]
            public Task<HttpResponseMessage> GetTenantProviderExtensionResource(string resourceProviderNamespace, string extensionProviderNamespace, string extensionResourceType, string extensionResourceName)
            {
                if (this.host.OnGetTenantProviderExtensionResourceDelegate != null)
                {
                    return Task.FromResult<HttpResponseMessage>(this.host.OnGetTenantProviderExtensionResourceDelegate(this.Request, resourceProviderNamespace, extensionProviderNamespace, extensionResourceType, extensionResourceName));
                }

                return Task.FromResult<HttpResponseMessage>(new HttpResponseMessage(HttpStatusCode.OK));
            }

            /// <summary>
            /// Gets the extension resource collection for tenant level requests.
            /// </summary>
            /// <param name="resourceProviderNamespace">The resource provider namespace.</param>
            /// <param name="resourceType">The type of the top-level resource.</param>
            /// <param name="resourceName">Name of the resource.</param>
            /// <param name="extensionProviderNamespace">The extension provider namespace.</param>
            /// <param name="extensionResourceType">The extension resource type.</param>
            [HttpGet]
            public Task<HttpResponseMessage> GetTenantExtensionResources(string resourceProviderNamespace, string resourceType, string resourceName, string extensionProviderNamespace, string extensionResourceType)
            {
                if (this.host.OnGetTenantExtensionResources != null)
                {
                    return Task.FromResult<HttpResponseMessage>(this.host.OnGetTenantExtensionResources(this.Request, resourceProviderNamespace, resourceType, resourceName, extensionProviderNamespace, extensionResourceType));
                }

                return Task.FromResult<HttpResponseMessage>(new HttpResponseMessage(HttpStatusCode.OK));
            }

            /// <summary>
            /// Gets the extension resource for tenant level requests.
            /// </summary>
            /// <param name="resourceProviderNamespace">The resource provider namespace.</param>
            /// <param name="resourceType">The type of the top-level resource.</param>
            /// <param name="resourceName">Name of the resource.</param>
            /// <param name="extensionProviderNamespace">The extension provider namespace.</param>
            /// <param name="extensionResourceType">The extension resource type.</param>
            /// <param name="extensionResourceName">The extension resource name.</param>
            [HttpGet]
            public Task<HttpResponseMessage> GetTenantExtensionResource(string resourceProviderNamespace, string resourceType, string resourceName, string extensionProviderNamespace, string extensionResourceType, string extensionResourceName)
            {
                if (this.host.OnGetTenantExtensionResource != null)
                {
                    return Task.FromResult<HttpResponseMessage>(this.host.OnGetTenantExtensionResource(this.Request, resourceProviderNamespace, resourceType, resourceName, extensionProviderNamespace, extensionResourceType, extensionResourceName));
                }

                return Task.FromResult<HttpResponseMessage>(new HttpResponseMessage(HttpStatusCode.OK));
            }

            /// <summary>
            /// Puts the extension resource for tenant level requests.
            /// </summary>
            /// <param name="resourceProviderNamespace">The resource provider namespace.</param>
            /// <param name="resourceType">The type of the top-level resource.</param>
            /// <param name="resourceName">Name of the resource.</param>
            /// <param name="extensionProviderNamespace">The extension provider namespace.</param>
            /// <param name="extensionResourceType">The extension resource type.</param>
            /// <param name="extensionResourceName">The extension resource name.</param>
            [HttpPut]
            public Task<HttpResponseMessage> PutTenantExtensionResource(string resourceProviderNamespace, string resourceType, string resourceName, string extensionProviderNamespace, string extensionResourceType, string extensionResourceName)
            {
                if (this.host.OnPutTenantExtensionResource != null)
                {
                    return Task.FromResult<HttpResponseMessage>(this.host.OnPutTenantExtensionResource(this.Request, resourceProviderNamespace, resourceType, resourceName, extensionProviderNamespace, extensionResourceType, extensionResourceName));
                }

                return Task.FromResult<HttpResponseMessage>(new HttpResponseMessage(HttpStatusCode.OK));
            }

            /// <summary>
            /// Deletes the extension resource for tenant level requests.
            /// </summary>
            /// <param name="resourceProviderNamespace">The resource provider namespace.</param>
            /// <param name="resourceType">The type of the top-level resource.</param>
            /// <param name="resourceName">Name of the resource.</param>
            /// <param name="extensionProviderNamespace">The extension provider namespace.</param>
            /// <param name="extensionResourceType">The extension resource type.</param>
            /// <param name="extensionResourceName">The extension resource name.</param>
            [HttpDelete]
            public Task<HttpResponseMessage> DeleteTenantExtensionResource(string resourceProviderNamespace, string resourceType, string resourceName, string extensionProviderNamespace, string extensionResourceType, string extensionResourceName)
            {
                if (this.host.OnDeleteTenantExtensionResource != null)
                {
                    return Task.FromResult<HttpResponseMessage>(this.host.OnDeleteTenantExtensionResource(this.Request, resourceProviderNamespace, resourceType, resourceName, extensionProviderNamespace, extensionResourceType, extensionResourceName));
                }

                return Task.FromResult<HttpResponseMessage>(new HttpResponseMessage(HttpStatusCode.OK));
            }
        }

        #endregion

        #region TemplateController

        /// <summary>
        /// Gets or sets the on get template.
        /// </summary>
        public TemplateRequestDelegate OnGetTemplate { get; set; }

        /// <summary>
        /// Gets or sets the on get template.
        /// </summary>
        public TemplateRequestDelegate OnGetPackageData { get; set; }

        /// <summary>
        /// Gets or sets the on get template.
        /// </summary>
        public TemplateRequestDelegate OnGetParameter { get; set; }

        /// <summary>
        /// Gets or sets the on get manifest.
        /// </summary>
        public TemplateRequestDelegate OnGetManifest { get; set; }

        /// <summary>
        /// Gets or sets the on get schema repo contents.
        /// </summary>
        public TemplateRequestDelegate OnGetSchemaRepoContents { get; set; }

        /// <summary>
        /// Gets or sets the on get schema file content.
        /// </summary>
        public TemplateRequestDelegate OnGetSchemaFileContent { get; set; }

        /// <summary>
        /// The template controller.
        /// </summary>
        public class TemplateController : ApiController
        {
            /// <summary>
            /// The resource provider test host.
            /// </summary>
            private readonly BaseResourceProviderTestHost host;

            /// <summary>
            /// Initializes a new instance of the <see cref="TemplateController"/> class.
            /// </summary>
            /// <param name="host">The resource provider test host.</param>
            public TemplateController(BaseResourceProviderTestHost host)
            {
                this.host = host;
            }

            /// <summary>
            /// Gets the template.
            /// </summary>
            /// <param name="templateId">The template identifier.</param>
            [HttpGet]
            [ActionName("GetTemplate")]
            public Task<HttpResponseMessage> GetTemplate(string templateId)
            {
                if (this.host.OnGetTemplate != null)
                {
                    return Task.FromResult<HttpResponseMessage>(this.host.OnGetTemplate(this.Request, templateId));
                }

                return Task.FromResult<HttpResponseMessage>(new HttpResponseMessage(HttpStatusCode.OK));
            }

            /// <summary>
            /// Gets the package data mapping.
            /// </summary>
            /// <param name="blobName">The blob name.</param>
            [HttpGet]
            [ActionName("GetPackageData")]
            public Task<HttpResponseMessage> GetPackageData(string blobName)
            {
                if (this.host.OnGetPackageData != null)
                {
                    return Task.FromResult<HttpResponseMessage>(this.host.OnGetPackageData(this.Request, blobName));
                }

                return Task.FromResult<HttpResponseMessage>(new HttpResponseMessage(HttpStatusCode.OK));
            }

            /// <summary>
            /// Gets the parameters.
            /// </summary>
            /// <param name="templateId">The template identifier.</param>
            [HttpGet]
            [ActionName("GetParameter")]
            public Task<HttpResponseMessage> GetParameter(string templateId)
            {
                if (this.host.OnGetParameter != null)
                {
                    return Task.FromResult<HttpResponseMessage>(this.host.OnGetParameter(this.Request, templateId));
                }

                return Task.FromResult<HttpResponseMessage>(new HttpResponseMessage(HttpStatusCode.OK));
            }

            /// <summary>
            /// Gets the manifest.
            /// </summary>
            /// <param name="resourceType">Type of the resource.</param>
            [HttpGet]
            [ActionName("GetManifest")]
            public Task<HttpResponseMessage> GetManifest(string resourceType)
            {
                if (this.host.OnGetManifest != null)
                {
                    return Task.FromResult<HttpResponseMessage>(this.host.OnGetManifest(this.Request, resourceType));
                }

                return Task.FromResult<HttpResponseMessage>(new HttpResponseMessage(HttpStatusCode.OK));
            }

            /// <summary>
            /// Gets the schema repo contents.
            /// </summary>
            /// <param name="dirPath">path of a directory.</param>
            [HttpGet]
            [ActionName("GetSchemaRepoContents")]
            public Task<HttpResponseMessage> GetSchemaRepoContents(string dirPath)
            {
                if (this.host.OnGetSchemaRepoContents != null)
                {
                    return Task.FromResult<HttpResponseMessage>(this.host.OnGetSchemaRepoContents(this.Request, dirPath));
                }

                return Task.FromResult<HttpResponseMessage>(new HttpResponseMessage(HttpStatusCode.OK));
            }

            /// <summary>
            /// Gets the schema file content.
            /// </summary>
            /// <param name="filePath">path of file.</param>
            [HttpGet]
            [ActionName("GetSchemaFileContent")]
            public Task<HttpResponseMessage> GetSchemaFileContent(string filePath)
            {
                if (this.host.OnGetSchemaFileContent != null)
                {
                    return Task.FromResult<HttpResponseMessage>(this.host.OnGetSchemaFileContent(this.Request, filePath));
                }

                return Task.FromResult<HttpResponseMessage>(new HttpResponseMessage(HttpStatusCode.OK));
            }
        }

        #endregion

        #region UnscopedRequestTestController

        /// <summary>
        /// The un-scoped request controller.
        /// </summary>
        public class UnscopedRequestTestController : ApiController
        {
            /// <summary>
            /// The resource provider test host.
            /// </summary>
            private readonly BaseResourceProviderTestHost host;

            /// <summary>
            /// Initializes a new instance of the <see cref="UnscopedRequestTestController"/> class.
            /// </summary>
            /// <param name="host">The resource provider test host.</param>
            public UnscopedRequestTestController(BaseResourceProviderTestHost host)
            {
                this.host = host;
            }

            /// <summary>
            /// Serves subscription request.
            /// </summary>
            /// <param name="subscriptionId">The subscription Id.</param>
            /// <param name="resourceProviderNamespace">The resource provider namespace.</param>
            /// <param name="wildcard">Optional. Rest of the path.</param>
            [HttpGet]
            public Task<HttpResponseMessage> SubscriptionRequest(string subscriptionId, string resourceProviderNamespace, string wildcard = null)
            {
                if (this.host.OnGetUnscopedSubscriptionRequest != null)
                {
                    return Task.FromResult<HttpResponseMessage>(this.host.OnGetUnscopedSubscriptionRequest(this.Request, subscriptionId, resourceProviderNamespace, wildcard));
                }

                return Task.FromResult<HttpResponseMessage>(new HttpResponseMessage(HttpStatusCode.OK));
            }

            /// <summary>
            /// Serves subscription request.
            /// </summary>
            /// <param name="subscriptionId">The subscription Id.</param>
            /// <param name="resourceProviderNamespace">The resource provider namespace.</param>
            /// <param name="wildcard">Optional. Rest of the path.</param>
            [HttpPut]
            public Task<HttpResponseMessage> PutSubscriptionRequest(string subscriptionId, string resourceProviderNamespace, string wildcard = null)
            {
                if (this.host.OnPutUnscopedSubscriptionRequest != null)
                {
                    return Task.FromResult<HttpResponseMessage>(this.host.OnPutUnscopedSubscriptionRequest(this.Request, subscriptionId, resourceProviderNamespace, wildcard));
                }

                return Task.FromResult<HttpResponseMessage>(new HttpResponseMessage(HttpStatusCode.OK));
            }

            /// <summary>
            /// Serves subscription request.
            /// </summary>
            /// <param name="subscriptionId">The subscription Id.</param>
            /// <param name="resourceProviderNamespace">The resource provider namespace.</param>
            /// <param name="wildcard">Optional. Rest of the path.</param>
            [HttpDelete]
            public Task<HttpResponseMessage> DeleteSubscriptionRequest(string subscriptionId, string resourceProviderNamespace, string wildcard = null)
            {
                if (this.host.OnDeleteUnscopedSubscriptionRequest != null)
                {
                    return Task.FromResult<HttpResponseMessage>(this.host.OnDeleteUnscopedSubscriptionRequest(this.Request, subscriptionId, resourceProviderNamespace, wildcard));
                }

                return Task.FromResult<HttpResponseMessage>(new HttpResponseMessage(HttpStatusCode.OK));
            }
        }

        #endregion

        #region ManagementGroupNotificationTestController

        /// <summary>
        /// The management group lifecycle notification controller.
        /// </summary>
        public class ManagementGroupNotificationTestController : ApiController
        {
            /// <summary>
            /// The resource provider test host.
            /// </summary>
            private readonly BaseResourceProviderTestHost host;

            /// <summary>
            /// Initializes a new instance of the <see cref="ManagementGroupNotificationTestController"/> class.
            /// </summary>
            /// <param name="host">The resource provider test host.</param>
            public ManagementGroupNotificationTestController(BaseResourceProviderTestHost host)
            {
                this.host = host;
            }

            /// <summary>
            /// Serves subscription request.
            /// </summary>
            /// <param name="managementGroupId">The management group Id.</param>
            [HttpPut]
            public Task<HttpResponseMessage> PutManagementGroup(string managementGroupId)
            {
                if (this.host.OnPutManagementGroup != null)
                {
                    return Task.FromResult<HttpResponseMessage>(this.host.OnPutManagementGroup(this.Request, managementGroupId));
                }

                return Task.FromResult<HttpResponseMessage>(new HttpResponseMessage(HttpStatusCode.OK));
            }
        }

        #endregion

        #region SubscriptionNotificationTestController

        /// <summary>
        /// The subscription lifecycle notification controller.
        /// </summary>
        public class SubscriptionNotificationTestController : ApiController
        {
            /// <summary>
            /// The resource provider test host.
            /// </summary>
            private readonly BaseResourceProviderTestHost host;

            /// <summary>
            /// Initializes a new instance of the <see cref="SubscriptionNotificationTestController"/> class.
            /// </summary>
            /// <param name="host">The resource provider test host.</param>
            public SubscriptionNotificationTestController(BaseResourceProviderTestHost host)
            {
                this.host = host;
            }

            /// <summary>
            /// Serves subscription request.
            /// </summary>
            /// <param name="subscriptionId">The subscription Id.</param>
            [HttpPut]
            public Task<HttpResponseMessage> PutSubscription(string subscriptionId)
            {
                if (this.host.OnPutSubscription != null)
                {
                    return Task.FromResult<HttpResponseMessage>(this.host.OnPutSubscription(this.Request, subscriptionId));
                }

                return Task.FromResult<HttpResponseMessage>(new HttpResponseMessage(HttpStatusCode.OK));
            }
        }

        #endregion

        #region FeatureProviderController

        /// <summary>
        /// The feature provider controller.
        /// </summary>
        public class FeatureProviderController : ApiController
        {
            /// <summary>
            /// The resource provider test host.
            /// </summary>
            private readonly BaseResourceProviderTestHost host;

            /// <summary>
            /// Initializes a new instance of the <see cref="FeatureProviderController"/> class.
            /// </summary>
            /// <param name="host">The resource provider test host.</param>
            public FeatureProviderController(BaseResourceProviderTestHost host)
            {
                this.host = host;
            }

            /// <summary>
            /// Gets the features.
            /// </summary>
            /// <param name="subscriptionId">The subscription identifier.</param>
            [HttpGet]
            public Task<HttpResponseMessage> GetFeatures(string subscriptionId)
            {
                if (this.host.OnGetFeatures != null)
                {
                    return Task.FromResult<HttpResponseMessage>(this.host.OnGetFeatures(this.Request, subscriptionId));
                }

                return Task.FromResult<HttpResponseMessage>(new HttpResponseMessage(HttpStatusCode.OK));
            }

            /// <summary>
            /// Gets the features.
            /// </summary>
            /// <param name="subscriptionId">The subscription identifier.</param>
            /// <param name="providerNamespace">The provider namespace.</param>
            [HttpGet]
            public Task<HttpResponseMessage> GetFeatures(string subscriptionId, string providerNamespace)
            {
                if (this.host.OnGetFeaturesForProvider != null)
                {
                    return Task.FromResult<HttpResponseMessage>(this.host.OnGetFeaturesForProvider(this.Request, subscriptionId, providerNamespace));
                }

                var responseContent = new ResourceStackComponents.ResponseWithContinuation<ResourceProxyDefinition[]>
                {
                    Value = new ResourceProxyDefinition[0],
                    NextLink = null
                };

                return Task.FromResult<HttpResponseMessage>(this.Request.CreateResponse(statusCode: HttpStatusCode.OK, value: responseContent));
            }

            /// <summary>
            /// Gets the features.
            /// </summary>
            /// <param name="subscriptionId">The subscription identifier.</param>
            /// <param name="providerNamespace">The provider namespace.</param>
            /// <param name="featureName">The feature name.</param>
            [HttpGet]
            public Task<HttpResponseMessage> GetFeatures(string subscriptionId, string providerNamespace, string featureName)
            {
                if (this.host.OnGetFeatureByNamespaceandFeatureName != null)
                {
                    return Task.FromResult<HttpResponseMessage>(this.host.OnGetFeatureByNamespaceandFeatureName(this.Request, subscriptionId, providerNamespace, featureName));
                }

                return Task.FromResult<HttpResponseMessage>(new HttpResponseMessage(HttpStatusCode.OK));
            }
        }

        #endregion

        #region AuthorizationProviderController

        /// <summary>
        /// Gets or sets the delegate for putting authorization resource at subscription scope.
        /// </summary>
        public WriteAuthorizationResourceDelegate OnPutAuthorizationResource { get; set; }

        /// <summary>
        /// Gets or sets the delegate for putting authorization resource at a resource scope.
        /// </summary>
        public WriteAuthorizationResourceNestedDelegate OnPutNestedAuthorizationResource { get; set; }

        /// <summary>
        /// Gets or sets the delegate for deleting authorization resource at a resource scope.
        /// </summary>
        public WriteAuthorizationResourceNestedDelegate OnDeleteNestedAuthorizationResource { get; set; }

        /// <summary>
        /// Gets or sets the delegate for putting authorization resource at management group scope.
        /// </summary>
        public WriteAuthorizationResourceForManagementGroupDelegate OnPutAuthorizationResourceForManagementGroup { get; set; }

        /// <summary>
        /// Gets or sets the delegate for putting authorization resource at resource group scope.
        /// </summary>
        public WriteAuthorizationResourceForResourceGroupDelegate OnPutAuthorizationResourceForResourceGroup { get; set; }

        /// <summary>
        /// Gets or sets the delegate for deleting authorization resource at resource group scope.
        /// </summary>
        public WriteAuthorizationResourceForResourceGroupDelegate OnDeleteAuthorizationResourceForResourceGroup { get; set; }

        /// <summary>
        /// Gets or sets the delegate for deleting authorization resource at subscription scope.
        /// </summary>
        public WriteAuthorizationResourceDelegate OnDeleteAuthorizationResource { get; set; }

        /// <summary>
        /// Gets or sets the delegate for getting authorization resource.
        /// </summary>
        public GetAuthorizationResourceDelegate OnGetAuthorizationResource { get; set; }

        /// <summary>
        /// Gets or sets the delegate for getting authorization resource for management group.
        /// </summary>
        public GetAuthorizationResourcesForManagementGroupDelegate OnGetAuthorizationResourcesForManagementGroup { get; set; }

        /// <summary>
        /// Gets or sets the delegate for getting authorization resources.
        /// </summary>
        public GetAuthorizationResourcesDelegate OnGetAuthorizationResources { get; set; }

        /// <summary>
        /// Gets or sets the delegate for getting authorization resources at resource group scope.
        /// </summary>
        public GetAuthorizationResourcesForResourceGroupDelegate OnGetAuthorizationResourcesForResourceGroup { get; set; }

        /// <summary>
        /// Gets or sets the delegate for getting authorization resources at a resource scope.
        /// </summary>
        public GetAuthorizationResourcesForResourceDelegate OnGetAuthorizationResourcesForResource { get; set; }

        /// <summary>
        /// Gets or sets the delegate for getting authorization resource at resource group scope.
        /// </summary>
        public GetAuthorizationResourceForResourceGroupDelegate OnGetAuthorizationResourceForResourceGroup { get; set; }

        /// <summary>
        /// The authorization provider controller.
        /// </summary>
        public class AuthorizationProviderController : ApiController
        {
            /// <summary>
            /// The resource provider test host.
            /// </summary>
            private readonly BaseResourceProviderTestHost host;

            /// <summary>
            /// Initializes a new instance of the <see cref="AuthorizationProviderController"/> class.
            /// </summary>
            /// <param name="host">The resource provider test host.</param>
            public AuthorizationProviderController(BaseResourceProviderTestHost host)
            {
                this.host = host;
            }

            /// <summary>
            /// Puts the authorization resource.
            /// </summary>
            /// <param name="subscriptionId">The subscription identifier.</param>
            /// <param name="resourceType">The authorization resource type.</param>
            /// <param name="resourceName">The authorization resource name.</param>
            /// <param name="requestBody">The request body.</param>
            [HttpPut]
            public Task<HttpResponseMessage> PutAuthorizationResource(string subscriptionId, string resourceType, string resourceName, [FromBody] JObject requestBody)
            {
                if (this.host.OnPutAuthorizationResource != null)
                {
                    return Task.FromResult<HttpResponseMessage>(this.host.OnPutAuthorizationResource(this.Request, subscriptionId, resourceType, resourceName, requestBody));
                }

                return Task.FromResult<HttpResponseMessage>(new HttpResponseMessage(HttpStatusCode.OK));
            }

            /// <summary>
            /// Puts the nested authorization resource.
            /// </summary>
            /// <param name="subscriptionId">The subscription identifier.</param>
            /// <param name="resourceGroupName">Name of the resource group.</param>
            /// <param name="resourceProviderNamespace">The resource provider namespace.</param>
            /// <param name="resourceType">The resource type.</param>
            /// <param name="resourceName">The resource name.</param>
            /// <param name="authorizationResourceType">The authorization resource type.</param>
            /// <param name="authorizationResourceName">The authorization resource name.</param>
            /// <param name="requestBody">The request body.</param>
            [HttpPut]
            public Task<HttpResponseMessage> PutNestedAuthorizationResource(
                string subscriptionId,
                string resourceGroupName,
                string resourceProviderNamespace,
                string resourceType,
                string resourceName,
                string authorizationResourceType,
                string authorizationResourceName,
                [FromBody] JObject requestBody)
            {
                if (this.host.OnPutNestedAuthorizationResource != null)
                {
                    return Task.FromResult<HttpResponseMessage>(this.host.OnPutNestedAuthorizationResource(this.Request, subscriptionId, resourceGroupName, resourceProviderNamespace, resourceType, resourceName, authorizationResourceType, authorizationResourceName, requestBody));
                }

                return Task.FromResult<HttpResponseMessage>(new HttpResponseMessage(HttpStatusCode.OK));
            }

            /// <summary>
            /// Puts the nested authorization resource.
            /// </summary>
            /// <param name="subscriptionId">The subscription identifier.</param>
            /// <param name="resourceGroupName">Name of the resource group.</param>
            /// <param name="resourceProviderNamespace">The resource provider namespace.</param>
            /// <param name="resourceType">The resource type.</param>
            /// <param name="resourceName">The resource name.</param>
            /// <param name="authorizationResourceType">The authorization resource type.</param>
            /// <param name="authorizationResourceName">The authorization resource name.</param>
            /// <param name="requestBody">The request body.</param>
            [HttpDelete]
            public Task<HttpResponseMessage> DeleteNestedAuthorizationResource(
                string subscriptionId,
                string resourceGroupName,
                string resourceProviderNamespace,
                string resourceType,
                string resourceName,
                string authorizationResourceType,
                string authorizationResourceName,
                [FromBody] JObject requestBody)
            {
                if (this.host.OnDeleteNestedAuthorizationResource != null)
                {
                    return Task.FromResult<HttpResponseMessage>(this.host.OnDeleteNestedAuthorizationResource(this.Request, subscriptionId, resourceGroupName, resourceProviderNamespace, resourceType, resourceName, authorizationResourceType, authorizationResourceName, requestBody));
                }

                return Task.FromResult<HttpResponseMessage>(new HttpResponseMessage(HttpStatusCode.OK));
            }

            /// <summary>
            /// Puts the authorization resource for management group
            /// </summary>
            /// <param name="entityId">The management group Id.</param>
            /// <param name="resourceType">The authorization resource type.</param>
            /// <param name="resourceName">The authorization resource name.</param>
            /// <param name="requestBody">The request body.</param>
            [HttpPut]
            public Task<HttpResponseMessage> PutAuthorizationResourceForManagementGroup(string entityId, string resourceType, string resourceName, [FromBody] JObject requestBody)
            {
                if (this.host.OnPutAuthorizationResourceForManagementGroup != null)
                {
                    return Task.FromResult<HttpResponseMessage>(this.host.OnPutAuthorizationResourceForManagementGroup(this.Request, entityId, resourceType, resourceName, requestBody));
                }

                return Task.FromResult<HttpResponseMessage>(new HttpResponseMessage(HttpStatusCode.OK));
            }

            /// <summary>
            /// Puts the authorization resource.
            /// </summary>
            /// <param name="subscriptionId">The subscription identifier.</param>
            /// <param name="resourceGroupName">Name of the resource group.</param>
            /// <param name="resourceType">The authorization resource type.</param>
            /// <param name="resourceName">The authorization resource name.</param>
            /// <param name="requestBody">The request body.</param>
            [HttpPut]
            public Task<HttpResponseMessage> PutAuthorizationResourceForResourceGroup(string subscriptionId, string resourceGroupName, string resourceType, string resourceName, [FromBody] JObject requestBody)
            {
                if (this.host.OnPutAuthorizationResourceForResourceGroup != null)
                {
                    return Task.FromResult<HttpResponseMessage>(this.host.OnPutAuthorizationResourceForResourceGroup(this.Request, subscriptionId, resourceGroupName, resourceType, resourceName, requestBody));
                }

                return Task.FromResult<HttpResponseMessage>(new HttpResponseMessage(HttpStatusCode.OK));
            }

            /// <summary>
            /// Deletes the authorization resource.
            /// </summary>
            /// <param name="subscriptionId">The subscription identifier.</param>
            /// <param name="resourceGroupName">Name of the resource group.</param>
            /// <param name="resourceType">The authorization resource type.</param>
            /// <param name="resourceName">The authorization resource name.</param>
            /// <param name="requestBody">The request body.</param>
            [HttpDelete]
            public Task<HttpResponseMessage> DeleteAuthorizationResourceForResourceGroup(string subscriptionId, string resourceGroupName, string resourceType, string resourceName, [FromBody] JObject requestBody)
            {
                if (this.host.OnDeleteAuthorizationResourceForResourceGroup != null)
                {
                    return Task.FromResult<HttpResponseMessage>(this.host.OnDeleteAuthorizationResourceForResourceGroup(this.Request, subscriptionId, resourceGroupName, resourceType, resourceName, requestBody));
                }

                return Task.FromResult<HttpResponseMessage>(new HttpResponseMessage(HttpStatusCode.OK));
            }

            /// <summary>
            /// Deletes the authorization resource.
            /// </summary>
            /// <param name="subscriptionId">The subscription identifier.</param>
            /// <param name="resourceType">The authorization resource type.</param>
            /// <param name="resourceName">The authorization resource name.</param>
            /// <param name="requestBody">The request body.</param>
            [HttpDelete]
            public Task<HttpResponseMessage> DeleteAuthorizationResource(string subscriptionId, string resourceType, string resourceName, [FromBody] JObject requestBody)
            {
                if (this.host.OnDeleteAuthorizationResource != null)
                {
                    return Task.FromResult<HttpResponseMessage>(this.host.OnDeleteAuthorizationResource(this.Request, subscriptionId, resourceType, resourceName, requestBody));
                }

                return Task.FromResult<HttpResponseMessage>(new HttpResponseMessage(HttpStatusCode.OK));
            }

            /// <summary>
            /// Gets the authorization resource.
            /// </summary>
            /// <param name="subscriptionId">The subscription identifier.</param>
            /// <param name="resourceType">The authorization resource type.</param>
            /// <param name="resourceName">The authorization resource name.</param>
            [HttpGet]
            public Task<HttpResponseMessage> GetAuthorizationResource(string subscriptionId, string resourceType, string resourceName)
            {
                if (this.host.OnGetAuthorizationResource != null)
                {
                    return Task.FromResult<HttpResponseMessage>(this.host.OnGetAuthorizationResource(this.Request, subscriptionId, resourceType, resourceName));
                }

                return Task.FromResult<HttpResponseMessage>(new HttpResponseMessage(HttpStatusCode.OK));
            }

            /// <summary>
            /// Gets the authorization resources.
            /// </summary>
            /// <param name="subscriptionId">The subscription identifier.</param>
            /// <param name="resourceType">The resource type.</param>
            [HttpGet]
            public Task<HttpResponseMessage> GetAuthorizationResources(string subscriptionId, string resourceType)
            {
                if (this.host.OnGetAuthorizationResources != null)
                {
                    return Task.FromResult<HttpResponseMessage>(this.host.OnGetAuthorizationResources(this.Request, subscriptionId, resourceType));
                }

                return Task.FromResult<HttpResponseMessage>(new HttpResponseMessage(HttpStatusCode.OK));
            }

            /// <summary>
            /// Gets the authorization resources for management group.
            /// </summary>
            /// <param name="entityId">The management group identifier.</param>
            /// <param name="resourceType">The resource type.</param>
            [HttpGet]
            public Task<HttpResponseMessage> GetAuthorizationResourcesForManagementGroup(string entityId, string resourceType)
            {
                if (this.host.OnGetAuthorizationResourcesForManagementGroup != null)
                {
                    return Task.FromResult<HttpResponseMessage>(this.host.OnGetAuthorizationResourcesForManagementGroup(this.Request, entityId, resourceType));
                }

                return Task.FromResult<HttpResponseMessage>(new HttpResponseMessage(HttpStatusCode.OK));
            }

            /// <summary>
            /// Gets the authorization resources.
            /// </summary>
            /// <param name="subscriptionId">The subscription identifier.</param>
            /// <param name="resourceGroupName">Name of the resource group.</param>
            /// <param name="resourceType">The resource type.</param>
            [HttpGet]
            public Task<HttpResponseMessage> GetAuthorizationResourcesForResourceGroup(string subscriptionId, string resourceGroupName, string resourceType)
            {
                if (this.host.OnGetAuthorizationResourcesForResourceGroup != null)
                {
                    return Task.FromResult<HttpResponseMessage>(this.host.OnGetAuthorizationResourcesForResourceGroup(this.Request, subscriptionId, resourceGroupName, resourceType));
                }

                if (resourceType.EqualsInsensitively("roleAssignments"))
                {
                    var response = new ResourceStackComponents.ResponseWithContinuation<RoleAssignmentEntity[]>
                    {
                        Value = new RoleAssignmentEntity[0]
                    };

                    return Task.FromResult<HttpResponseMessage>(this.Request.CreateResponse<ResourceStackComponents.ResponseWithContinuation<RoleAssignmentEntity[]>>(HttpStatusCode.OK, response));
                }
                else if (resourceType.EqualsInsensitively("denyAssignments"))
                {
                    var response = new ResponseWithContinuation<DenyAssignmentDefinition[]>
                    {
                        Value = new DenyAssignmentDefinition[0]
                    };

                    return Task.FromResult<HttpResponseMessage>(this.Request.CreateResponse<ResponseWithContinuation<DenyAssignmentDefinition[]>>(HttpStatusCode.OK, response));
                }

                return Task.FromResult<HttpResponseMessage>(new HttpResponseMessage(HttpStatusCode.OK));
            }

            /// <summary>
            /// Gets the authorization resource at resource group.
            /// </summary>
            /// <param name="subscriptionId">The subscription identifier.</param>
            /// <param name="resourceGroupName">Name of the resource group.</param>
            /// <param name="resourceType">The resource type.</param>
            /// <param name="resourceName">The resource name.</param>
            [HttpGet]
            public Task<HttpResponseMessage> GetAuthorizationResourceForResourceGroup(string subscriptionId, string resourceGroupName, string resourceType, string resourceName)
            {
                if (this.host.OnGetAuthorizationResourceForResourceGroup != null)
                {
                    return Task.FromResult<HttpResponseMessage>(this.host.OnGetAuthorizationResourceForResourceGroup(this.Request, subscriptionId, resourceGroupName, resourceType, resourceName));
                }

                return Task.FromResult<HttpResponseMessage>(new HttpResponseMessage(HttpStatusCode.OK));
            }

            /// <summary>
            /// Gets the authorization resources at a resource scope.
            /// </summary>
            /// <param name="request">The http request.</param>
            /// <param name="subscriptionId">The subscription identifier.</param>
            /// <param name="resourceGroupName">Name of the resource group.</param>
            /// <param name="resourceProviderNamespace">The resource provider namespace.</param>
            /// <param name="resourceType">The resource type.</param>
            /// <param name="resourceName">The resource name.</param>
            /// <param name="authorizationResourceType">The authorization resource type.</param>
            [HttpGet]
            public Task<HttpResponseMessage> GetAuthorizationResourcesForResource(HttpRequestMessage request, string subscriptionId, string resourceGroupName, string resourceProviderNamespace, string resourceType, string resourceName, string authorizationResourceType)
            {
                if (this.host.OnGetAuthorizationResourcesForResource != null)
                {
                    return Task.FromResult<HttpResponseMessage>(this.host.OnGetAuthorizationResourcesForResource(this.Request, subscriptionId, resourceGroupName, resourceProviderNamespace, resourceType, resourceName, authorizationResourceType));
                }

                if (authorizationResourceType.EqualsInsensitively("roleAssignments"))
                {
                    var response = new ResourceStackComponents.ResponseWithContinuation<RoleAssignmentEntity[]>
                    {
                        Value = new RoleAssignmentEntity[0]
                    };

                    return Task.FromResult<HttpResponseMessage>(this.Request.CreateResponse<ResourceStackComponents.ResponseWithContinuation<RoleAssignmentEntity[]>>(HttpStatusCode.OK, response));
                }

                return Task.FromResult<HttpResponseMessage>(new HttpResponseMessage(HttpStatusCode.OK));
            }
        }

        #endregion

        #region LinksProviderController

        /// <summary>
        /// Gets or sets the delegate for getting links.
        /// </summary>
        public LinkDelegate OnGetLinks { get; set; }

        /// <summary>
        /// The links controller.
        /// </summary>
        public class LinksProviderController : ApiController
        {
            /// <summary>
            /// The resource provider test host.
            /// </summary>
            private readonly BaseResourceProviderTestHost host;

            /// <summary>
            /// Initializes a new instance of the <see cref="LinksProviderController"/> class.
            /// </summary>
            /// <param name="host">The resource provider test host.</param>
            public LinksProviderController(BaseResourceProviderTestHost host)
            {
                this.host = host;
            }

            /// <summary>
            /// Gets the links.
            /// </summary>
            /// <param name="subscriptionId">The subscription identifier.</param>
            /// <param name="resourceGroupName">Name of the resource group.</param>
            [HttpGet]
            public Task<HttpResponseMessage> GetLinks(string subscriptionId, string resourceGroupName)
            {
                if (this.host.OnGetLinks != null)
                {
                    return Task.FromResult<HttpResponseMessage>(this.host.OnGetLinks(this.Request, subscriptionId, resourceGroupName));
                }

                return Task.FromResult<HttpResponseMessage>(new HttpResponseMessage(HttpStatusCode.OK));
            }
        }

        #endregion

        #region RP Backend  

        /// <summary>
        /// The RP Backend controller.
        /// </summary>
        public class RPBackendController : ApiController
        {
            /// <summary>
            /// The resource provider test host.
            /// </summary>
            private readonly BaseResourceProviderTestHost host;

            /// <summary>
            /// Initializes a new instance of the <see cref="RPBackendController"/> class.
            /// </summary>
            /// <param name="host">The resource provider test host.</param>
            public RPBackendController(BaseResourceProviderTestHost host)
            {
                this.host = host;
            }

            /// <summary>
            /// Put RP Backend.
            /// </summary>
            /// <param name="subscriptionId">The subscription identifier.</param>
            /// <param name="resourceGroupName">Name of the resource group.</param>
            /// <param name="workspaceName">The workspace name.</param>
            /// <returns>Returns response</returns>
            [HttpPut]
            [ActionName("PutRPBackendProxy")]
            public Task<HttpResponseMessage> PutRPBackendProxy(string subscriptionId, string resourceGroupName, string workspaceName)
            {
                var message = new HttpResponseMessage(HttpStatusCode.OK)
                {
                    Content = new ObjectContent<WorkspaceDetails>(new WorkspaceDetails() { WorkspaceId = "2159941391644118", WorkspaceURL = "https://adb-2159941391644118.18.azuredatabricks.net" }, new JsonMediaTypeFormatter())
                };

                return this.host.OnPutRPBackendProxy != null
                    ? Task.FromResult(this.host.OnPutRPBackendProxy(this.Request, subscriptionId, resourceGroupName, workspaceName))
                    : Task.FromResult(message);
            }

            /// <summary>
            /// Patch RP Backend.
            /// </summary>
            /// <param name="subscriptionId">The subscription identifier.</param>
            /// <param name="resourceGroupName">Name of the resource group.</param>
            /// <param name="workspaceName">The workspace name.</param>
            /// <returns>Returns response</returns>
            [HttpPut]
            [ActionName("PatchRPBackendProxy")]
            public Task<HttpResponseMessage> PatchRPBackendProxyRequest(string subscriptionId, string resourceGroupName, string workspaceName)
            {
                var message = new HttpResponseMessage(HttpStatusCode.OK);

                return this.host.OnPatchRPBackendProxy != null
                    ? Task.FromResult(this.host.OnPatchRPBackendProxy(this.Request, subscriptionId, resourceGroupName, workspaceName))
                    : Task.FromResult(message);
            }

            /// <summary>
            /// Get db PrivateLink Resource.
            /// </summary>
            /// <param name="subscriptionId">The subscription identifier.</param>
            /// <param name="resourceGroupName">Name of the resource group.</param>
            /// <param name="applicationName">The workspace name.</param>
            /// <returns>Returns response</returns>
            [HttpGet]
            [ActionName("GetRPBackendPrivateLink")]
            public Task<HttpResponseMessage> GetRpBackendPrivateLinkRequest(string subscriptionId, string resourceGroupName, string applicationName)
            {
                var message = new HttpResponseMessage(HttpStatusCode.OK);

                return this.host.OnGetRPBackendPrivateLinkRequestDelegate != null
                    ? Task.FromResult(this.host.OnGetRPBackendPrivateLinkRequestDelegate(this.Request, subscriptionId, resourceGroupName, applicationName))
                    : Task.FromResult(message);
            }
        }

        #endregion

        #region NetworkController

        /// <summary>
        /// Gets or sets the on Prepare or un prepare Network Policies delegate.
        /// </summary>
        public PrepareOrUnprepareNetworkPoliciesRequestDelegate OnPrepareOrUnprepareNetworkPolicies { get; set; }

        /// <summary>
        /// Gets or sets the On Put Private Link Service Proxy.
        /// </summary>
        public PutPrivateLinkServiceProxyRequestDelegate OnPutPrivateLinkServiceProxy { get; set; }

        /// <summary>
        /// The network controller.
        /// </summary>
        public class NetworkController : ApiController
        {
            /// <summary>
            /// The resource provider test host.
            /// </summary>
            private readonly BaseResourceProviderTestHost host;

            /// <summary>
            /// Initializes a new instance of the <see cref="NetworkController"/> class.
            /// </summary>
            /// <param name="host">The resource provider test host.</param>
            public NetworkController(BaseResourceProviderTestHost host)
            {
                this.host = host;
            }

            /// <summary>
            /// Prepare or un prepare network intent policies on subnet.
            /// </summary>
            /// <param name="subscriptionId">The subscription identifier.</param>
            /// <param name="resourceGroupName">Name of the resource group.</param>
            /// <param name="virtualNetworkName">The virtual network name.</param>
            /// <param name="subnetName">The subnet name.</param>
            /// <param name="actionName">The action name.</param>
            [HttpPost]
            [ActionName("PrepareOrUnprepareNetworkPolicies")]
            public Task<HttpResponseMessage> PrepareOrUnprepareNetworkPolicies(string subscriptionId, string resourceGroupName, string virtualNetworkName, string subnetName, string actionName)
            {
                var asyncOperationUrl =
                    $"{TestEnvironment.FrontdoorServiceUri}subscriptions/{subscriptionId}/providers/Microsoft.Network/locations/DevFabric/operations/{Guid.NewGuid()}?api-version=2019-06-01";
                var message = new HttpResponseMessage(HttpStatusCode.Accepted)
                {
                    Headers =
                    {
                        RetryAfter = RetryConditionHeaderValue.Parse("1"),
                        Location = new Uri(asyncOperationUrl)
                    }
                };

                return this.host.OnPrepareOrUnprepareNetworkPolicies != null
                    ? Task.FromResult(this.host.OnPrepareOrUnprepareNetworkPolicies(this.Request, subscriptionId, resourceGroupName, virtualNetworkName, subnetName, actionName))
                    : Task.FromResult(message);
            }

            /// <summary>
            /// Prepare or un prepare network intent policies on subnet.
            /// </summary>
            /// <param name="subscriptionId">The subscription identifier.</param>
            /// <param name="resourceGroupName">Name of the resource group.</param>
            /// <param name="privateEndPointName">The private endpoint name.</param>
            /// <param name="privatelinkServiceProxyName">The private link service proxy name.</param>
            [HttpPut]
            [ActionName("PutPrivateLinkServiceProxy")]
            public Task<HttpResponseMessage> PutPrivateLinkServiceProxy(string subscriptionId, string resourceGroupName, string privateEndPointName, string privatelinkServiceProxyName)
            {
                var asyncOperationUrl =
                    $"{TestEnvironment.FrontdoorServiceUri}subscriptions/{subscriptionId}/providers/Microsoft.Network/locations/DevFabric/operations/{Guid.NewGuid()}?api-version=2019-06-01";

                var message = new HttpResponseMessage(HttpStatusCode.Accepted)
                {
                    Headers = { RetryAfter = RetryConditionHeaderValue.Parse("1") }
                };

                message.Headers.Add(RequestCorrelationContext.HeaderAzureAsyncOperation, asyncOperationUrl);

                return this.host.OnPutPrivateLinkServiceProxy != null
                    ? Task.FromResult(this.host.OnPutPrivateLinkServiceProxy(this.Request, subscriptionId, resourceGroupName, privateEndPointName, privatelinkServiceProxyName))
                    : Task.FromResult(message);
            }
        }

        #endregion

        #region HttpConfiguration

        /// <summary>
        /// Gets or sets the delegate for authenticating request.
        /// </summary>
        public AuthenticationHandlerDelegate OnAuthenticateRequest { get; set; }

        /// <summary>
        /// Gets or sets the delegate for getting authentication metadata.
        /// </summary>
        public GetAuthenticationMetadataDelegate OnGetAuthenticationMetadata { get; set; }

        /// <summary>
        /// Gets or sets the delegate for any request.
        /// </summary>
        public Func<HttpRequestMessage, HttpResponseMessage> OnAnyRequest { get; set; }

        /// <summary>
        /// Called to configure http configuration.
        /// </summary>
        /// <param name="configuration">The http configuration.</param>
        protected virtual void OnConfigure(HttpConfiguration configuration)
        {
            configuration.Routes.MapHttpRoute(
                name: "ManagementGroupRegistration",
                routeTemplate: "providers/Microsoft.Management/managementGroups/{managementGroupId}",
                defaults: new
                {
                    controller = "ManagementGroupNotificationTest",
                },
                constraints: new { httpMethod = new HttpMethodConstraint(HttpMethod.Put) });

            configuration.Routes.MapHttpRoute(
                name: "SubscriptionRegistration",
                routeTemplate: "subscriptions/{subscriptionId}",
                defaults: new
                {
                    controller = "SubscriptionNotificationTest",
                });

            configuration.Routes.MapHttpRoute(
               name: "Feature-Provider-Get-All-Features",
               routeTemplate: "subscriptions/{subscriptionId}/providers/Microsoft.Features/features",
               defaults: new
               {
                   controller = "FeatureProvider"
               },
               constraints: new { httpMethod = new HttpMethodConstraint(HttpMethod.Get) });

            configuration.Routes.MapHttpRoute(
               name: "Feature-Provider-Get-Provider-Features",
               routeTemplate: "subscriptions/{subscriptionId}/providers/Microsoft.Features/providers/{providerNamespace}/features",
               defaults: new
               {
                   controller = "FeatureProvider"
               },
               constraints: new { httpMethod = new HttpMethodConstraint(HttpMethod.Get) });

            configuration.Routes.MapHttpRoute(
               name: "Feature-Provider-Get-Specific-Feature",
               routeTemplate: "subscriptions/{subscriptionId}/providers/Microsoft.Features/providers/{providerNamespace}/features/{featureName}",
               defaults: new
               {
                   controller = "FeatureProvider"
               },
               constraints: new { httpMethod = new HttpMethodConstraint(HttpMethod.Get) });

            configuration.Routes.MapHttpRoute(
                "PrepareOrUnprepareNetworkPoliciesRequest",
                "subscriptions/{subscriptionId}/resourcegroups/{resourceGroupName}/providers/Microsoft.Network/virtualnetworks/{virtualNetworkName}/subnets/{subnetName}/{actionName}",
                new
                {
                    controller = "Network",
                    action = "PrepareOrUnprepareNetworkPolicies"
                },
                new { httpMethod = new HttpMethodConstraint(HttpMethod.Post) });

            configuration.Routes.MapHttpRoute(
              "PutRPBackendProxyRequest",
               "subscriptions/{subscriptionId}/resourceGroups/{resourceGroupName}/providers/Microsoft.Databricks/workspaces/{workspaceName}/dbWorkspaces/dbworkspace",
                 new
                 {
                     controller = "RPBackend",
                     action = "PutRPBackendProxy"
                 },
             new { httpMethod = new HttpMethodConstraint(HttpMethod.Put) });

            configuration.Routes.MapHttpRoute(
                "PatchRPBackendProxyRequest",
                "subscriptions/{subscriptionId}/resourceGroups/{resourceGroupName}/providers/Microsoft.Databricks/workspaces/{workspaceName}/dbWorkspaces/dbworkspace",
                new
                {
                    controller = "RPBackend",
                    action = "PatchRPBackendProxy"
                },
                new { httpMethod = "PATCH" });

            configuration.Routes.MapHttpRoute(
                "GetRpBackendPrivateLinkRequest",
                "subscriptions/{subscriptionId}/resourceGroups/{resourceGroupName}/providers/Microsoft.Databricks/workspaces/{applicationName}/dbWorkspaces/dbworkspace/privateLinkResources",
                new
                {
                    controller = "RPBackend",
                    action = "GetRPBackendPrivateLink"
                },
                new { httpMethod = new HttpMethodConstraint(HttpMethod.Get) });

            configuration.Routes.MapHttpRoute(
                "PutPrivateLinkServiceProxyRequest",
                "subscriptions/{subscriptionId}/resourcegroups/{resourceGroupName}/providers/Microsoft.Network/privateEndpoints/{privateEndPointName}/privateLinkServiceProxies/{privatelinkServiceProxyName}",
                new
                {
                    controller = "Network",
                    action = "PutPrivateLinkServiceProxy"
                },
                new { httpMethod = new HttpMethodConstraint(HttpMethod.Put) });

            configuration.Routes.MapHttpRoute(
                name: "AuthorizationResource",
                routeTemplate: "subscriptions/{subscriptionId}/providers/Microsoft.Authorization/{resourceType}/{resourceName}",
                defaults: new
                {
                    controller = "AuthorizationProvider"
                });

            configuration.Routes.MapHttpRoute(
                name: "AuthorizationResourceForManagementGroup",
                routeTemplate: "providers/Microsoft.Management/managementGroups/{entityId}/providers/Microsoft.Authorization/{resourceType}/{resourceName}",
                defaults: new
                {
                    controller = "AuthorizationProvider"
                });

            configuration.Routes.MapHttpRoute(
                name: "GetRoleAssignments",
                routeTemplate: "subscriptions/{subscriptionId}/providers/Microsoft.Authorization/{resourceType}/",
                defaults: new
                {
                    controller = "AuthorizationProvider"
                });

            configuration.Routes.MapHttpRoute(
                name: "GetRoleAssignmentsForManagementGroup",
                routeTemplate: "providers/Microsoft.Management/managementGroups/{entityId}/providers/Microsoft.Authorization/{resourceType}/",
                defaults: new
                {
                    controller = "AuthorizationProvider"
                });

            configuration.Routes.MapHttpRoute(
                name: "GetRoleAssignmentsForResourceGroup",
                routeTemplate: "subscriptions/{subscriptionId}/resourcegroups/{resourceGroupName}/providers/Microsoft.Authorization/{resourceType}/",
                defaults: new
                {
                    controller = "AuthorizationProvider"
                },
                constraints: new { httpMethod = new HttpMethodConstraint(HttpMethod.Get) });

            configuration.Routes.MapHttpRoute(
                name: "AuthorizationResourceForResourceGroup",
                routeTemplate: "subscriptions/{subscriptionId}/resourcegroups/{resourceGroupName}/providers/Microsoft.Authorization/{resourceType}/{resourceName}",
                defaults: new
                {
                    controller = "AuthorizationProvider"
                });

            configuration.Routes.MapHttpRoute(
                name: "AuthorizationResourceNested",
                routeTemplate: "subscriptions/{subscriptionId}/resourcegroups/{resourceGroupName}/providers/{resourceProviderNamespace}/{resourceType}/{resourceName}/providers/Microsoft.Authorization/{authorizationResourceType}/{authorizationResourceName}",
                defaults: new
                {
                    controller = "AuthorizationProvider"
                });

            configuration.Routes.MapHttpRoute(
                name: "GetAuthorizationResourcesNested",
                routeTemplate: "subscriptions/{subscriptionId}/resourcegroups/{resourceGroupName}/providers/{resourceProviderNamespace}/{resourceType}/{resourceName}/providers/Microsoft.Authorization/{authorizationResourceType}",
                defaults: new
                {
                    controller = "AuthorizationProvider"
                },
                constraints: new { httpMethod = new HttpMethodConstraint(HttpMethod.Get) });

            configuration.Routes.MapHttpRoute(
               name: "SubscriptionLevelResource",
               routeTemplate: "subscriptions/{subscriptionId}/providers/{resourceProviderNamespace}/{resourceType}/{resourceName}",
               defaults: new
               {
                   controller = "ResourceManagement",
               });

            configuration.Routes.MapHttpRoute(
                name: "GetLinksAtResourceGroup",
                routeTemplate: "subscriptions/{subscriptionId}/resourcegroups/{resourceGroupName}/providers/Microsoft.Resources/links/",
                defaults: new
                {
                    controller = "LinksProvider"
                },
                constraints: new { httpMethod = new HttpMethodConstraint(HttpMethod.Get) });

            configuration.Routes.MapHttpRoute(
               name: "Resource-GetCollection-ExtensionTypeOnSubscription",
               routeTemplate: "subscriptions/{subscriptionId}/providers/{resourceProviderNamespace}/TestExtensionResourceType",
               defaults: new
               {
                   controller = "ResourceManagement",
                   resourceType = ResourceProviderTestHostRegistration.TestExtensionResourceType,
               });

            configuration.Routes.MapHttpRoute(
               name: "Resource-Management-Action",
               routeTemplate: "subscriptions/{subscriptionId}/resourcegroups/{resourceGroupName}/providers/{resourceProviderNamespace}/TestResourceType/{resourceName}/{actionName}",
               defaults: new
               {
                   controller = "ResourceManagement",
                   resourceType = ResourceProviderTestHostRegistration.TestResourceType
               },
               constraints: new { httpMethod = new HttpMethodConstraint(HttpMethod.Post) });

            configuration.Routes.MapHttpRoute(
               name: "Resource-Management-TestResourceTypeTwo-Action",
               routeTemplate: "subscriptions/{subscriptionId}/resourcegroups/{resourceGroupName}/providers/{resourceProviderNamespace}/TestResourceTypeTwo/{resourceName}/{actionName}",
               defaults: new
               {
                   controller = "ResourceManagement",
                   resourceType = ResourceProviderTestHostRegistration.TestResourceTypeTwo
               },
               constraints: new { httpMethod = new HttpMethodConstraint(HttpMethod.Post) });

            configuration.Routes.MapHttpRoute(
               name: "Resource-Management-subscription-deployments-Action",
               routeTemplate: "subscriptions/{subscriptionId}/providers/{resourceProviderNamespace}/deployments/{resourceName}/{actionName}",
               defaults: new
               {
                   controller = "ResourceManagement",
                   resourceType = "deployments",
               },
               constraints: new { httpMethod = new HttpMethodConstraint(HttpMethod.Post) });

            configuration.Routes.MapHttpRoute(
                "Resource-GetCollection-SubscriptionLevel-ForRegistrationAssignments",
                "subscriptions/{subscriptionId}/providers/Microsoft.ManagedServices/registrationAssignments",
                new
                {
                    controller = "ResourceManagement",
                    action = "GetRegistrationAssignmentsSubscriptionResources"
                },
                new { httpMethod = new HttpMethodConstraint(HttpMethod.Get) });

            configuration.Routes.MapHttpRoute(
               name: "Resource-Management-deployments-Action",
               routeTemplate: "subscriptions/{subscriptionId}/resourcegroups/{resourceGroupName}/providers/{resourceProviderNamespace}/deployments/{resourceName}/{actionName}",
               defaults: new
               {
                   controller = "ResourceManagement",
                   resourceType = "deployments",
               },
               constraints: new { httpMethod = new HttpMethodConstraint(HttpMethod.Post) });

            configuration.Routes.MapHttpRoute(
               name: "Resource-Management-Proxy-Action",
               routeTemplate: "subscriptions/{subscriptionId}/resourcegroups/{resourceGroupName}/providers/{resourceProviderNamespace}/TestProxyResourceType/{resourceName}/{actionName}",
               defaults: new
               {
                   controller = "ResourceManagement",
                   resourceType = ResourceProviderTestHostRegistration.TestProxyResourceType
               },
               constraints: new { httpMethod = new HttpMethodConstraint(HttpMethod.Post) });

            configuration.Routes.MapHttpRoute(
               name: "Second-Nested-Resource-Management-Action",
               routeTemplate: "subscriptions/{subscriptionId}/resourcegroups/{resourceGroupName}/providers/{resourceProviderNamespace}/TestResourceType/{resourceName}/TestResourceTypeNestedOne/{resourceNameNestedOne}/TestResourceTypeNestedTwo/{resourceNameNestedTwo}/{actionName}",
               defaults: new
               {
                   controller = "ResourceManagement",
                   resourceType = ResourceProviderTestHostRegistration.TestResourceType,
                   resourceTypeNestedOne = ResourceProviderTestHostRegistration.TestResourceTypeNestedOne.Split('/').Last(),
                   resourceTypeNestedTwo = ResourceProviderTestHostRegistration.TestResourceTypeNestedTwo.Split('/').Last()
               },
               constraints: new { httpMethod = new HttpMethodConstraint(HttpMethod.Post) });

            configuration.Routes.MapHttpRoute(
                name: "Second-Nested-Resource-Management",
                routeTemplate: "subscriptions/{subscriptionId}/resourcegroups/{resourceGroupName}/providers/{resourceProviderNamespace}/TestResourceType/{resourceName}/TestResourceTypeNestedOne/{resourceNameNestedOne}/TestResourceTypeNestedTwo/{resourceNameNestedTwo}/{*wildcard}",
                defaults: new
                {
                    controller = "ResourceManagement",
                    resourceType = ResourceProviderTestHostRegistration.TestResourceType,
                    resourceTypeNestedOne = ResourceProviderTestHostRegistration.TestResourceTypeNestedOne.Split('/').Last(),
                    resourceTypeNestedTwo = ResourceProviderTestHostRegistration.TestResourceTypeNestedTwo.Split('/').Last(),
                    wildcard = RouteParameter.Optional,
                });

            configuration.Routes.MapHttpRoute(
               name: "Second-Nested-Resource-GetCollection",
               routeTemplate: "subscriptions/{subscriptionId}/resourcegroups/{resourceGroupName}/providers/{resourceProviderNamespace}/TestResourceType/{resourceName}/TestResourceTypeNestedOne/{resourceNameNestedOne}/TestResourceTypeNestedTwo",
               defaults: new
               {
                   controller = "ResourceManagement",
                   resourceType = ResourceProviderTestHostRegistration.TestResourceType,
                   resourceTypeNestedOne = ResourceProviderTestHostRegistration.TestResourceTypeNestedOne.Split('/').Last(),
                   resourceTypeNestedTwo = ResourceProviderTestHostRegistration.TestResourceTypeNestedTwo.Split('/').Last(),
               });

            configuration.Routes.MapHttpRoute(
               name: "Resource-Management-ActivityStatus",
               routeTemplate: "providers/{resourceProviderNamespace}/activityStatuses/{activityId}",
               defaults: new
               {
                   controller = "ResourceManagement"
               });

            configuration.Routes.MapHttpRoute(
               name: "Nested-Resource-Notify",
               routeTemplate: "subscriptions/{subscriptionId}/resourcegroups/{resourceGroupName}/providers/{resourceProviderNamespace}/{resourceType}/{resourceName}/{resourceTypeNestedOne}/{resourceNameNestedOne}/providers/{notificationProviderNamespace}/notify",
               defaults: new
               {
                   controller = "ResourceManagement",
                   ActionName = "notify"
               },
               constraints: new { httpMethod = new HttpMethodConstraint(HttpMethod.Post) });

            configuration.Routes.MapHttpRoute(
               name: "Nested-Resource-Management-Action",
               routeTemplate: "subscriptions/{subscriptionId}/resourcegroups/{resourceGroupName}/providers/{resourceProviderNamespace}/TestResourceType/{resourceName}/TestResourceTypeNestedOne/{resourceNameNestedOne}/{actionName}",
               defaults: new
               {
                   controller = "ResourceManagement",
                   resourceType = ResourceProviderTestHostRegistration.TestResourceType,
                   resourceTypeNestedOne = ResourceProviderTestHostRegistration.TestResourceTypeNestedOne.Split('/').Last()
               },
               constraints: new { httpMethod = new HttpMethodConstraint(HttpMethod.Post) });

            configuration.Routes.MapHttpRoute(
               name: "Nested-Resource-Management",
               routeTemplate: "subscriptions/{subscriptionId}/resourcegroups/{resourceGroupName}/providers/{resourceProviderNamespace}/TestResourceType/{resourceName}/TestResourceTypeNestedOne/{resourceNameNestedOne}/{*wildcard}",
               defaults: new
               {
                   controller = "ResourceManagement",
                   resourceType = ResourceProviderTestHostRegistration.TestResourceType,
                   resourceTypeNestedOne = ResourceProviderTestHostRegistration.TestResourceTypeNestedOne.Split('/').Last(),
                   wildcard = RouteParameter.Optional,
               });

            configuration.Routes.MapHttpRoute(
               name: "Nested-Resource-GetCollection",
               routeTemplate: "subscriptions/{subscriptionId}/resourcegroups/{resourceGroupName}/providers/{resourceProviderNamespace}/TestResourceType/{resourceName}/TestResourceTypeNestedOne",
               defaults: new
               {
                   controller = "ResourceManagement",
                   resourceType = ResourceProviderTestHostRegistration.TestResourceType,
                   resourceTypeNestedOne = ResourceProviderTestHostRegistration.TestResourceTypeNestedOne.Split('/').Last(),
               });

            configuration.Routes.MapHttpRoute(
               name: "NestedSibling-Resource-GetCollection",
               routeTemplate: "subscriptions/{subscriptionId}/resourcegroups/{resourceGroupName}/providers/{resourceProviderNamespace}/TestResourceType/{resourceName}/TestResourceTypeNestedOneSibling",
               defaults: new
               {
                   controller = "ResourceManagement",
                   resourceType = ResourceProviderTestHostRegistration.TestResourceType,
                   resourceTypeNestedOne = ResourceProviderTestHostRegistration.TestResourceTypeNestedOneSibling.Split('/').Last(),
               });

            configuration.Routes.MapHttpRoute(
               name: "ResourceGroup-Management-Action",
               routeTemplate: "subscriptions/{subscriptionId}/resourcegroups/{resourceGroupName}/{actionName}",
               defaults: new
               {
                   controller = "ResourceManagement",
                   resourceType = ResourceProviderTestHostRegistration.TestResourceType
               },
               constraints: new { httpMethod = new HttpMethodConstraint(HttpMethod.Post) });

            configuration.Routes.MapHttpRoute(
               name: "Resource-Management-Nested-Deeper-Four",
               routeTemplate: "subscriptions/{subscriptionId}/resourcegroups/{resourceGroupName}/providers/{resourceProviderNamespace}/TestResourceType/{resourceName}/TestResourceTypeNestedOne/{resourceNameNestedOne}/TestResourceTypeNestedTwo/{resourceNameNestedTwo}/TestResourceTypeNestedThree/{resourceNameNestedThree}/{*wildcard}",
               defaults: new
               {
                   controller = "ResourceManagement",
                   resourceType = ResourceProviderTestHostRegistration.TestResourceType,
                   resourceTypeNestedOne = ResourceProviderTestHostRegistration.TestResourceTypeNestedOne.Split('/').Last(),
                   resourceTypeNestedTwo = ResourceProviderTestHostRegistration.TestResourceTypeNestedTwo.Split('/').Last(),
                   resourceTypeNestedThree = ResourceProviderTestHostRegistration.TestResourceTypeNestedThree.Split('/').Last(),
                   wildcard = RouteParameter.Optional,
               });

            configuration.Routes.MapHttpRoute(
               name: "Resource-Async-Operation",
               routeTemplate: "subscriptions/{subscriptionId}/resourcegroups/{resourceGroupName}/providers/{resourceProviderNamespace}/TestResourceType/{resourceName}/asyncOperations/{asyncOperationId}",
               defaults: new
               {
                   controller = "ResourceManagement",
                   resourceType = ResourceProviderTestHostRegistration.TestResourceType,
                   action = "GetAsyncOperation",
               });

            configuration.Routes.MapHttpRoute(
               name: "Resource-Async-Operation-TestResourceTypeTwo",
               routeTemplate: "subscriptions/{subscriptionId}/resourcegroups/{resourceGroupName}/providers/{resourceProviderNamespace}/TestResourceTypeTwo/{resourceName}/asyncOperations/{asyncOperationId}",
               defaults: new
               {
                   controller = "ResourceManagement",
                   resourceType = ResourceProviderTestHostRegistration.TestResourceTypeTwo,
                   action = "GetAsyncOperation",
               });

            configuration.Routes.MapHttpRoute(
                "Resource-Async-Operation-Subscription-LocationBased",
                "subscriptions/{subscriptionId}/providers/{resourceProviderNamespace}/locations/{location}/operations/{asyncOperationId}",
                new
                {
                    controller = "ResourceManagement",
                    action = "GetAsyncOperationStatus"
                });

            configuration.Routes.MapHttpRoute(
               name: "Resource-Generic-Notify",
               routeTemplate: "subscriptions/{subscriptionId}/resourcegroups/{resourceGroupName}/providers/{resourceProviderNamespace}/{resourceType}/{resourceName}/providers/{notificationProviderNamespace}/notify",
               defaults: new
               {
                   controller = "ResourceManagement",
               });

            configuration.Routes.MapHttpRoute(
               name: "Resource-Notify",
               routeTemplate: "subscriptions/{subscriptionId}/resourcegroups/{resourceGroupName}/providers/{resourceProviderNamespace}/TestResourceType/{resourceName}/providers/{notificationProviderNamespace}/notify",
               defaults: new
               {
                   controller = "ResourceManagement",
                   resourceType = ResourceProviderTestHostRegistration.TestResourceType
               });

            configuration.Routes.MapHttpRoute(
               name: "SubscriptionLevelResource-Notify",
               routeTemplate: "subscriptions/{subscriptionId}/providers/{resourceProviderNamespace}/{resourceType}/{resourceName}/providers/{notificationProviderNamespace}/notify",
               defaults: new
               {
                   controller = "ResourceManagement"
               });

            configuration.Routes.MapHttpRoute(
               name: "TenantLevelResource-Notify",
               routeTemplate: "providers/{resourceProviderNamespace}/{resourceType}/{resourceName}/providers/{notificationProviderNamespace}/notify",
               defaults: new
               {
                   controller = "ResourceManagement"
               });

            configuration.Routes.MapHttpRoute(
               name: "Extension-Resource-Notify",
               routeTemplate: "subscriptions/{subscriptionId}/resourcegroups/{resourceGroupName}/providers/{resourceProviderNamespace}/TestResourceType/{resourceName}/providers/{extensionProviderNamespace}/TestExtensionResourceType/{extensionResourceName}/providers/{notificationProviderNamespace}/notify",
               defaults: new
               {
                   controller = "ResourceManagement",
                   resourceType = ResourceProviderTestHostRegistration.TestResourceType,
                   extensionResourceType = ResourceProviderTestHostRegistration.TestExtensionResourceType,
               });

            configuration.Routes.MapHttpRoute(
               name: "ResourceGroup-Notify",
               routeTemplate: "subscriptions/{subscriptionId}/resourcegroups/{resourceGroupName}/providers/{notificationProviderNamespace}/notify",
               defaults: new
               {
                   controller = "ResourceManagement"
               });

            configuration.Routes.MapHttpRoute(
               name: "Resource-Management",
               routeTemplate: "subscriptions/{subscriptionId}/resourcegroups/{resourceGroupName}/providers/{resourceProviderNamespace}/TestResourceType/{resourceName}/{*wildcard}",
               defaults: new
               {
                   controller = "ResourceManagement",
                   resourceType = ResourceProviderTestHostRegistration.TestResourceType,
                   wildcard = RouteParameter.Optional,
               });

            configuration.Routes.MapHttpRoute(
               name: "Resource-Management-External",
               routeTemplate: "subscriptions/{subscriptionId}/resourcegroups/{resourceGroupName}/providers/{resourceProviderNamespace}/{resourcetype}/{resourceName}/{*wildcard}",
               defaults: new
               {
                   controller = "ResourceManagement",
                   wildcard = RouteParameter.Optional,
               });

            configuration.Routes.MapHttpRoute(
                name: "Resource-Management-External-List",
                routeTemplate: "subscriptions/{subscriptionId}/resourcegroups/{resourceGroupName}/providers/{resourceProviderNamespace}/{resourcetype}",
                defaults: new
                {
                    controller = "ResourceManagement"
                },
                constraints: new { httpMethod = new HttpMethodConstraint(HttpMethod.Get) });

            configuration.Routes.MapHttpRoute(
             name: "Resource-Management-Sibling",
             routeTemplate: "subscriptions/{subscriptionId}/resourcegroups/{resourceGroupName}/providers/{resourceProviderNamespace}/TestResourceTypeSibling/{resourceName}/{*wildcard}",
             defaults: new
             {
                 controller = "ResourceManagement",
                 resourceType = ResourceProviderTestHostRegistration.TestResourceTypeSibling,
                 wildcard = RouteParameter.Optional,
             });

            configuration.Routes.MapHttpRoute(
             name: "Resource-Management-Nested-Sibling",
             routeTemplate: "subscriptions/{subscriptionId}/resourcegroups/{resourceGroupName}/providers/{resourceProviderNamespace}/TestResourceTypeNestedOneSibling/{resourceName}/{*wildcard}",
             defaults: new
             {
                 controller = "ResourceManagement",
                 resourceType = ResourceProviderTestHostRegistration.TestResourceTypeNestedOneSibling,
                 wildcard = RouteParameter.Optional,
             });

            configuration.Routes.MapHttpRoute(
               name: "Resource-Management-TestResourceTypeTwo",
               routeTemplate: "subscriptions/{subscriptionId}/resourcegroups/{resourceGroupName}/providers/{resourceProviderNamespace}/TestResourceTypeTwo/{resourceName}/{*wildcard}",
               defaults: new
               {
                   controller = "ResourceManagement",
                   resourceType = ResourceProviderTestHostRegistration.TestResourceTypeTwo,
                   wildcard = RouteParameter.Optional,
               });

            configuration.Routes.MapHttpRoute(
               name: "ProxyResource-Management",
               routeTemplate: "subscriptions/{subscriptionId}/resourcegroups/{resourceGroupName}/providers/{resourceProviderNamespace}/TestProxyResourceType/{resourceName}/{*wildcard}",
               defaults: new
               {
                   controller = "ResourceManagement",
                   resourceType = ResourceProviderTestHostRegistration.TestProxyResourceType,
                   wildcard = RouteParameter.Optional,
               });

            configuration.Routes.MapHttpRoute(
               name: "TenantProxyResource-Management",
               routeTemplate: "providers/{resourceProviderNamespace}/{resourceType}/{resourceName}/{*wildcard}",
               defaults: new
               {
                   controller = "ResourceManagement",
                   resourceType = ResourceProviderTestHostRegistration.TestProxyResourceType,
                   resourceName = RouteParameter.Optional,
                   wildcard = RouteParameter.Optional,
               });

            configuration.Routes.MapHttpRoute(
               name: "TenantProxyDirectNestedCollectionResource-Management",
               routeTemplate: "providers/{resourceProviderNamespace}/TestNestedProxyResourceType/{resourceName}/{*wildcard}",
               defaults: new
               {
                   controller = "ResourceManagement",
                   resourceType = ResourceProviderTestHostRegistration.TestNestedProxyResourceTypeSegment,
                   resourceName = RouteParameter.Optional,
                   wildcard = RouteParameter.Optional,
               });

            configuration.Routes.MapHttpRoute(
               name: "ResourceGroup-Delete",
               routeTemplate: "subscriptions/{subscriptionId}/resourcegroups/{resourceGroupName}",
               defaults: new
               {
                   controller = "ResourceManagement",
               });

            configuration.Routes.MapHttpRoute(
               name: "Resource-GetCollection-SubscriptionLevel",
               routeTemplate: "subscriptions/{subscriptionId}/providers/{resourceProviderNamespace}/TestResourceType",
               defaults: new
               {
                   controller = "ResourceManagement",
                   resourceType = ResourceProviderTestHostRegistration.TestResourceType,
               });

            configuration.Routes.MapHttpRoute(
                name: "Resource-ResourceGroup-ProviderAction",
                routeTemplate: "subscriptions/{subscriptionId}/resourcegroups/{resourceGroupName}/providers/{resourceProviderNamespace}/{actionName}",
                defaults: new { controller = "ResourceManagement" },
                constraints: new { httpMethod = new HttpMethodConstraint(HttpMethod.Post) });

            configuration.Routes.MapHttpRoute(
               name: "SubscriptionRequest",
               routeTemplate: "subscriptions/{subscriptionId}/providers/{resourceProviderNamespace}/{*wildcard}",
               defaults: new
               {
                   controller = "UnscopedRequestTest",
                   wildcard = RouteParameter.Optional,
               });

            configuration.Routes.MapHttpRoute(
               name: "GetTemplateRequest",
               routeTemplate: "templates/{templateId}",
               defaults: new
               {
                   controller = "Template",
                   action = "GetTemplate"
               });

            configuration.Routes.MapHttpRoute(
                name: "GetPackageDataRequest",
                routeTemplate: "databricksnipmappings/{blobname}",
                defaults: new
                {
                    controller = "Template",
                    action = "GetPackageData"
                });

            configuration.Routes.MapHttpRoute(
                name: "GetOutboundNetworkEndpointsRequest",
                routeTemplate: "outboundnetworkendpoints/{blobname}",
                defaults: new
                {
                    controller = "Template",
                    action = "GetPackageData"
                });

            configuration.Routes.MapHttpRoute(
               name: "GetParameterRequest",
               routeTemplate: "parameters/{templateId}",
               defaults: new
               {
                   controller = "Template",
                   action = "GetParameter"
               });

            configuration.Routes.MapHttpRoute(
               name: "GetManifestRequest",
               routeTemplate: "manifest/{resourceType}",
               defaults: new
               {
                   controller = "Template",
                   action = "GetManifest"
               });

            configuration.Routes.MapHttpRoute(
               name: "GetSchemaRepoContentsRequest",
               routeTemplate: "repos/contents/{*dirPath}",
               defaults: new
               {
                   controller = "Template",
                   action = "GetSchemaRepoContents"
               });

            configuration.Routes.MapHttpRoute(
               name: "GetSchemaFileContentRequest",
               routeTemplate: "raw/{*filePath}",
               defaults: new
               {
                   controller = "Template",
                   action = "GetSchemaFileContent"
               });

            configuration.Routes.MapHttpRoute(
                name: "ExtensionResourceCollection-SubscriptionRegistration",
                routeTemplate: "api/extensions/subscriptions/{subscriptionId}",
                defaults: new
                {
                    controller = "ExtensionResourceManagement",
                });

            configuration.Routes.MapHttpRoute(
               name: "ExtensionResourceCollection-Management",
               routeTemplate: "api/extensions/subscriptions/{subscriptionId}/resourcegroups/{resourceGroupName}/providers/{resourceProviderNamespace}/TestResourceType/{resourceName}/providers/{extensionProviderNamespace}/TestExtensionResourceType/",
               defaults: new
               {
                   controller = "ExtensionResourceManagement",
                   resourceType = ResourceProviderTestHostRegistration.TestResourceType,
                   extensionResourceType = ResourceProviderTestHostRegistration.TestExtensionResourceType,
               });

            configuration.Routes.MapHttpRoute(
               name: "Resource-GetCollection-ExtensionType",
               routeTemplate: "api/extensions/subscriptions/{subscriptionId}/resourcegroups/{resourceGroupName}/providers/{resourceProviderNamespace}/TestExtensionResourceType",
               defaults: new
               {
                   controller = "ResourceManagement",
                   resourceType = ResourceProviderTestHostRegistration.TestExtensionResourceType,
               });

            configuration.Routes.MapHttpRoute(
               name: "Resource-GetCollection-SubscriptionLevel-ExtensionType",
               routeTemplate: "api/extensions/subscriptions/{subscriptionId}/providers/{resourceProviderNamespace}/TestExtensionResourceType",
               defaults: new
               {
                   controller = "ResourceManagement",
                   resourceType = ResourceProviderTestHostRegistration.TestExtensionResourceType,
               });

            configuration.Routes.MapHttpRoute(
               name: "Resource-PutCollection-SubscriptionLevel-ExtensionType",
               routeTemplate: "api/extensions/subscriptions/{subscriptionId}/providers/{resourceProviderNamespace}/TestExtensionResourceType/{resourceName}",
               defaults: new { controller = "ResourceManagement", resourceType = ResourceProviderTestHostRegistration.TestExtensionResourceType },
               constraints: new { HttpMethod = new HttpMethodConstraint(HttpMethod.Put) });

            configuration.Routes.MapHttpRoute(
               name: "Resource-DeleteCollection-SubscriptionLevel-ExtensionType",
               routeTemplate: "api/extensions/subscriptions/{subscriptionId}/providers/{resourceProviderNamespace}/TestExtensionResourceType/{resourceName}",
               defaults: new { controller = "ResourceManagement", resourceType = ResourceProviderTestHostRegistration.TestExtensionResourceType },
               constraints: new { HttpMethod = new HttpMethodConstraint(HttpMethod.Delete) });

            configuration.Routes.MapHttpRoute(
               name: "ExtensionResourceGroup-Management-Notify",
               routeTemplate: "api/extensions/subscriptions/{subscriptionId}/resourcegroups/{resourceGroupName}/providers/{extensionProviderNamespace}/notify",
               defaults: new
               {
                   controller = "ExtensionResourceManagement"
               });

            configuration.Routes.MapHttpRoute(
               name: "ExtensionResource-Management-Notify",
               routeTemplate: "api/extensions/subscriptions/{subscriptionId}/resourcegroups/{resourceGroupName}/providers/{resourceProviderNamespace}/TestResourceType/{resourceName}/providers/{extensionProviderNamespace}/notify",
               defaults: new
               {
                   controller = "ExtensionResourceManagement",
                   resourceType = ResourceProviderTestHostRegistration.TestResourceType
               });

            configuration.Routes.MapHttpRoute(
               name: "ExtensionResource-Management-ActivityStatus",
               routeTemplate: "api/extensions/providers/{extensionProviderNamespace}/activityStatuses/{activityId}",
               defaults: new
               {
                   controller = "ExtensionResourceManagement"
               });

            configuration.Routes.MapHttpRoute(
               name: "ExtensionResource-Management",
               routeTemplate: "api/extensions/subscriptions/{subscriptionId}/resourcegroups/{resourceGroupName}/providers/{resourceProviderNamespace}/TestResourceType/{resourceName}/providers/{extensionProviderNamespace}/TestExtensionResourceType/{extensionResourceName}",
               defaults: new
               {
                   controller = "ExtensionResourceManagement",
                   resourceType = ResourceProviderTestHostRegistration.TestResourceType,
                   extensionResourceType = ResourceProviderTestHostRegistration.TestExtensionResourceType,
               });

            configuration.Routes.MapHttpRoute(
               name: "ExtensionResource-Management-CascadeExtension",
               routeTemplate: "api/extensions/subscriptions/{subscriptionId}/resourcegroups/{resourceGroupName}/providers/{resourceProviderNamespace}/TestResourceType/{resourceName}/providers/{extensionProviderNamespace}/TestExtensionResourceType/{extensionResourceName}/providers/{cascadeExtensionProviderNamespace}/TestCascadeExtensionResourceType",
               defaults: new
               {
                   controller = "ExtensionResourceManagement",
                   resourceType = ResourceProviderTestHostRegistration.TestResourceType,
                   extensionResourceType = ResourceProviderTestHostRegistration.TestExtensionResourceType,
                   cascadeExtensionResourceType = ResourceProviderTestHostRegistration.TestCascadeExtensionResourceType,
               });

            configuration.Routes.MapHttpRoute(
               name: "ExtensionResource-Management-TestResourceTypeOne",
               routeTemplate: "api/extensions/subscriptions/{subscriptionId}/resourcegroups/{resourceGroupName}/providers/{resourceProviderNamespace}/TestResourceType/{resourceName}/TestResourceTypeNestedOne/{resourceNameNestedOne}/providers/{extensionProviderNamespace}/TestExtensionResourceType/{extensionResourceName}",
               defaults: new
               {
                   controller = "ExtensionResourceManagement",
                   resourceType = ResourceProviderTestHostRegistration.TestResourceType,
                   resourceTypeNestedOne = ResourceProviderTestHostRegistration.TestResourceTypeNestedOne.Split('/').Last(),
                   extensionResourceType = ResourceProviderTestHostRegistration.TestExtensionResourceType,
               });

            configuration.Routes.MapHttpRoute(
               name: "ExtensionResource-Management-TestResourceTypeTwo",
               routeTemplate: "api/extensions/subscriptions/{subscriptionId}/resourcegroups/{resourceGroupName}/providers/{resourceProviderNamespace}/TestResourceType/{resourceName}/TestResourceTypeNestedOne/{resourceNameNestedOne}/TestResourceTypeNestedTwo/{resourceNameNestedTwo}/providers/{extensionProviderNamespace}/TestExtensionResourceType/{extensionResourceName}",
               defaults: new
               {
                   controller = "ExtensionResourceManagement",
                   resourceType = ResourceProviderTestHostRegistration.TestResourceType,
                   resourceTypeNestedOne = ResourceProviderTestHostRegistration.TestResourceTypeNestedOne.Split('/').Last(),
                   resourceTypeNestedTwo = ResourceProviderTestHostRegistration.TestResourceTypeNestedTwo.Split('/').Last(),
                   extensionResourceType = ResourceProviderTestHostRegistration.TestExtensionResourceType,
               });

            configuration.Routes.MapHttpRoute(
               name: "ExtensionResourceCollection-ProxyResource-Management",
               routeTemplate: "api/extensions/subscriptions/{subscriptionId}/resourcegroups/{resourceGroupName}/providers/{resourceProviderNamespace}/TestProxyResourceType/{resourceName}/providers/{extensionProviderNamespace}/TestExtensionResourceType/",
               defaults: new
               {
                   controller = "ExtensionResourceManagement",
                   resourceType = ResourceProviderTestHostRegistration.TestProxyResourceType,
                   extensionResourceType = ResourceProviderTestHostRegistration.TestExtensionResourceType,
               });

            configuration.Routes.MapHttpRoute(
               name: "ExtensionResource-ProxyResource-Management",
               routeTemplate: "api/extensions/subscriptions/{subscriptionId}/resourcegroups/{resourceGroupName}/providers/{resourceProviderNamespace}/TestProxyResourceType/{resourceName}/providers/{extensionProviderNamespace}/TestExtensionResourceType/{extensionResourceName}",
               defaults: new
               {
                   controller = "ExtensionResourceManagement",
                   resourceType = ResourceProviderTestHostRegistration.TestProxyResourceType,
                   extensionResourceType = ResourceProviderTestHostRegistration.TestExtensionResourceType,
               });

            configuration.Routes.MapHttpRoute(
               name: "TenantProviderExtensionResourceCollection-Management",
               routeTemplate: "api/extensions/providers/{resourceProviderNamespace}/providers/{extensionProviderNamespace}/TestExtensionResourceType",
               defaults: new
               {
                   controller = "TenantExtensionResourceManagement",
                   extensionResourceType = ResourceProviderTestHostRegistration.TestExtensionResourceType,
               });

            configuration.Routes.MapHttpRoute(
               name: "TenantProviderExtensionResource-Management",
               routeTemplate: "api/extensions/providers/{resourceProviderNamespace}/providers/{extensionProviderNamespace}/TestExtensionResourceType/{extensionResourceName}",
               defaults: new
               {
                   controller = "TenantExtensionResourceManagement",
                   extensionResourceType = ResourceProviderTestHostRegistration.TestExtensionResourceType,
               });

            configuration.Routes.MapHttpRoute(
               name: "TenantExtensionResourceCollection-Management",
               routeTemplate: "api/extensions/providers/{resourceProviderNamespace}/TestProxyResourceType/{resourceName}/providers/{extensionProviderNamespace}/TestExtensionResourceType/",
               defaults: new
               {
                   controller = "TenantExtensionResourceManagement",
                   resourceType = ResourceProviderTestHostRegistration.TestProxyResourceType,
                   extensionResourceType = ResourceProviderTestHostRegistration.TestExtensionResourceType,
               });

            configuration.Routes.MapHttpRoute(
               name: "TenantExtensionResource-Management",
               routeTemplate: "api/extensions/providers/{resourceProviderNamespace}/TestProxyResourceType/{resourceName}/providers/{extensionProviderNamespace}/TestExtensionResourceType/{extensionResourceName}",
               defaults: new
               {
                   controller = "TenantExtensionResourceManagement",
                   resourceType = ResourceProviderTestHostRegistration.TestProxyResourceType,
                   extensionResourceType = ResourceProviderTestHostRegistration.TestExtensionResourceType,
               });

            configuration.Routes.MapHttpRoute(
               name: "Resource-Any",
               routeTemplate: "{*wildcard}",
               defaults: new
               {
                   controller = "ResourceManagement",
               });

            configuration.Services.Replace(typeof(IHttpControllerSelector), new ResourceProviderTestHostControllerSelector(this, configuration));
            configuration.Services.Replace(typeof(IHttpActionSelector), new ResourceProviderTestHostActionSelector());

            configuration.MessageHandlers.Add(new GeneralHandler(this));
            configuration.MessageHandlers.Add(new AuthenticationHandler(this));

            configuration.DependencyResolver = new ResourceProviderTestHostDependencyResolver(this, configuration);
            configuration.Filters.Add(new ExceptionTraceFilter());
        }

        #endregion

        #region ResourceProviderTestHostActionSelector

        /// <summary>
        /// Test hybrid action selector.
        /// </summary>
        public class ResourceProviderTestHostActionSelector : ApiControllerActionSelector
        {
            /// <summary>
            /// Selects an action for the <see cref="T:System.Web.Http.Controllers.ApiControllerActionSelector" />.
            /// </summary>
            /// <param name="controllerContext">The controller context.</param>
            public override HttpActionDescriptor SelectAction(HttpControllerContext controllerContext)
            {
                return base.SelectAction(controllerContext);
            }
        }

        #endregion

        #region ResourceProviderTestHostControllerSelector

        /// <summary>
        /// The resource provider test host controller selector.
        /// </summary>
        private class ResourceProviderTestHostControllerSelector : DefaultHttpControllerSelector
        {
            /// <summary>
            /// The resource provider test host.
            /// </summary>
            private readonly BaseResourceProviderTestHost host;

            /// <summary>
            /// The HTTP configuration.
            /// </summary>
            private readonly HttpConfiguration configuration;

            /// <summary>
            /// Initializes a new instance of the <see cref="ResourceProviderTestHostControllerSelector" /> class.
            /// </summary>
            /// <param name="host">The host.</param>
            /// <param name="configuration">The configuration.</param>
            public ResourceProviderTestHostControllerSelector(BaseResourceProviderTestHost host, HttpConfiguration configuration)
                : base(configuration)
            {
                this.host = host;
                this.configuration = configuration;
            }

            /// <summary>
            /// Selects the controller.
            /// </summary>
            /// <param name="request">The request.</param>
            public override HttpControllerDescriptor SelectController(HttpRequestMessage request)
            {
                var matchedTypes = typeof(BaseResourceProviderTestHost)
                    .GetNestedTypes()
                    .Where(type => typeof(IHttpController).IsAssignableFrom(type))
                    .ToList();

                var controllerName = this.GetControllerName(request);
                var matchedController = matchedTypes.FirstOrDefault(type => type.Name.EqualsInsensitively(controllerName + "controller"));

                return new HttpControllerDescriptor(this.configuration, controllerName, matchedController);
            }
        }

        #endregion

        #region ResourceProviderTestHostDependencyResolver

        /// <summary>
        /// The resource provider test host dependency resolver.
        /// </summary>
        private class ResourceProviderTestHostDependencyResolver : IDependencyResolver
        {
            /// <summary>
            /// The resource provider test host.
            /// </summary>
            private readonly BaseResourceProviderTestHost host;

            /// <summary>
            /// The HTTP configuration.
            /// </summary>
            private readonly HttpConfiguration configuration;

            /// <summary>
            /// Initializes a new instance of the <see cref="ResourceProviderTestHostDependencyResolver" /> class.
            /// </summary>
            /// <param name="host">The host.</param>
            /// <param name="configuration">The configuration.</param>
            public ResourceProviderTestHostDependencyResolver(BaseResourceProviderTestHost host, HttpConfiguration configuration)
            {
                this.host = host;
                this.configuration = configuration;
            }

            /// <summary>
            /// Begins the scope.
            /// </summary>
            public IDependencyScope BeginScope()
            {
                return this;
            }

            /// <summary>
            /// Gets the service.
            /// </summary>
            /// <param name="serviceType">Type of the service.</param>
            public object GetService(Type serviceType)
            {
                if (typeof(IHttpController).IsAssignableFrom(serviceType))
                {
                    if (typeof(BaseResourceProviderTestHost).GetNestedTypes().Any(type => type == serviceType))
                    {
                        return Activator.CreateInstance(serviceType, args: new object[] { this.host });
                    }
                }

                return null;
            }

            /// <summary>
            /// Gets the services.
            /// </summary>
            /// <param name="serviceType">Type of the service.</param>
            public IEnumerable<object> GetServices(Type serviceType)
            {
                return Enumerable.Empty<object>();
            }

            /// <summary>
            /// Performs application-defined tasks associated with freeing, releasing, or resetting unmanaged resources.
            /// </summary>
            public void Dispose()
            {
            }
        }

        #endregion

        #region ResourceProviderTestHostServer

        /// <summary>
        /// The resource provider test host server.
        /// </summary>
        public class BaseResourceProviderTestHostServer : ServiceTestHostServer
        {
            /// <summary>
            /// Initializes a new instance of the <see cref="BaseResourceProviderTestHostServer"/> class.
            /// </summary>
            /// <param name="serviceUri">The service URI.</param>
            /// <param name="state">The instance state.</param>
            public BaseResourceProviderTestHostServer(Uri serviceUri, object state)
                : base(serviceUri, state)
            {
            }

            /// <summary>
            /// Called to configure http configuration.
            /// </summary>
            /// <param name="configuration">The http configuration.</param>
            /// <param name="state">The instance state.</param>
            protected override void OnConfigure(HttpConfiguration configuration, object state)
            {
                (state as BaseResourceProviderTestHost).OnConfigure(configuration);
            }
        }

        #endregion

        #region ExceptionTraceFilter

        /// <summary>
        /// The exception trace filter.
        /// </summary>
        public class ExceptionTraceFilter : ActionFilterAttribute, IExceptionFilter
        {
            /// <summary>
            /// Executes the exception filter.
            /// </summary>
            /// <param name="actionExecutedContext">The action executed context.</param>
            /// <param name="cancellationToken">The cancellation token.</param>
            public Task ExecuteExceptionFilterAsync(HttpActionExecutedContext actionExecutedContext, CancellationToken cancellationToken)
            {
                Trace.TraceError("[ResourceProviderTestHost] Unhandled exception: {0}", actionExecutedContext.Exception);
                return Task.FromResult(false);
            }
        }

        #endregion

        #region AuthenticationHandler

        /// <summary>
        /// The Authentication handler.
        /// </summary>
        public class AuthenticationHandler : DelegatingHandler
        {
            /// <summary>
            /// The resource provider test host.
            /// </summary>
            private readonly BaseResourceProviderTestHost host;

            /// <summary>
            /// Initializes a new instance of the <see cref="AuthenticationHandler"/> class.
            /// </summary>
            /// <param name="host">The service host.</param>
            public AuthenticationHandler(BaseResourceProviderTestHost host)
            {
                this.host = host;
            }

            /// <summary>
            /// Sends an HTTP request to the inner handler to send to the server as an asynchronous operation.
            /// </summary>
            /// <param name="request">The HTTP request message.</param>
            /// <param name="cancellationToken">A cancellation token to cancel operation.</param>
            protected override async Task<HttpResponseMessage> SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
            {
                if (this.host.OnAuthenticateRequest != null)
                {
                    if (request.RequestUri.AbsolutePath.EqualsInsensitively("/authenticationMetadata"))
                    {
                        if (this.host.OnGetAuthenticationMetadata != null)
                        {
                            return this.host.OnGetAuthenticationMetadata(request);
                        }

                        var metadataResponse = request.CreateResponse(HttpStatusCode.Unauthorized);
                        metadataResponse.Headers.Add(
                            name: TestEnvironment.DstsAuthenticationHeaderName,
                            value: TestEnvironment.GetTestProviderDstsAuthenticateHeader());

                        return metadataResponse;
                    }

                    var response = this.host.OnAuthenticateRequest(request);

                    if (response.StatusCode != HttpStatusCode.OK)
                    {
                        return response;
                    }

                    var authorizationHeader = request.Headers.GetFirstOrDefault("authorization");

                    if (!TestEnvironment.ResourceProviderAuthenticateRequest(authorizationHeader))
                    {
                        return request.CreateResponse(HttpStatusCode.Unauthorized);
                    }
                }

                return await base.SendAsync(request, cancellationToken).ConfigureAwait(continueOnCapturedContext: false);
            }
        }

        #endregion

        #region GeneralHandler

        /// <summary>
        /// The general handler.
        /// </summary>
        public class GeneralHandler : DelegatingHandler
        {
            /// <summary>
            /// The resource provider test host.
            /// </summary>
            private readonly BaseResourceProviderTestHost host;

            /// <summary>
            /// Initializes a new instance of the <see cref="GeneralHandler"/> class.
            /// </summary>
            /// <param name="host">The service host.</param>
            public GeneralHandler(BaseResourceProviderTestHost host)
            {
                this.host = host;
            }

            /// <summary>
            /// Sends an HTTP request to the inner handler to send to the server as an asynchronous operation.
            /// </summary>
            /// <param name="request">The HTTP request message.</param>
            /// <param name="cancellationToken">A cancellation token to cancel operation.</param>
            protected override async Task<HttpResponseMessage> SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
            {
                if (this.host.OnAnyRequest != null)
                {
                    return this.host.OnAnyRequest(request);
                }

                return await base.SendAsync(request, cancellationToken).ConfigureAwait(continueOnCapturedContext: false);
            }
        }

        #endregion
    }
}