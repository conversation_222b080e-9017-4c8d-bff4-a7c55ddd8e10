﻿//-----------------------------------------------------------
// Copyright (c) Microsoft Corporation.  All rights reserved.
//-----------------------------------------------------------

namespace Internal.WindowsAzure.ResourceStack.Tests.Frontdoor.Common
{
    using System;
    using System.Collections.Generic;
    using Microsoft.TeamFoundation.SourceControl.WebApi;
    using Microsoft.VisualStudio.Services.Common;
    using Microsoft.VisualStudio.Services.WebApi;

    /// <summary>
    /// The visual studio team services test helper object.
    /// </summary>
    public static class VSTSTestHelper
    {
        /// <summary>
        /// Gets or sets the repository Id
        /// </summary>
        public static string RepositoryId { get; set; }

        /// <summary>
        /// Create a repository.
        /// </summary>
        /// <param name="token">The token.</param>
        /// <param name="accountName">The account name.</param>
        /// <param name="projectId">The project Id.</param>
        /// <param name="repositoryName">The repository name.</param>
        public static void CreateRepositoryWithDefaultBranch(
            string token,
            string accountName,
            string projectId,
            string repositoryName)
        {
            var credential = new VssBasicCredential(userName: string.Empty, password: token);
            var connection = new VssConnection(
                baseUrl: new Uri(string.Format("https://{0}.visualstudio.com", accountName)),
                credentials: credential);
            var gitHttpClient = connection.GetClient<GitHttpClient>();

            // Create repository
            var repository = gitHttpClient
                .CreateRepositoryAsync(
                    gitRepositoryToCreate: new GitRepository { Name = repositoryName },
                    project: new Guid(projectId))
                .Result;

            VSTSTestHelper.RepositoryId = repository.Id.ToString();

            // Create default branch and commit
            var newBranch = new GitRefUpdate()
            {
                Name = "refs/heads/master",
                OldObjectId = "0000000000000000000000000000000000000000"
            };

            var newCommit = new GitCommitRef()
            {
                Comment = "Initial Commit",
                Changes = new List<GitChange>()
                {
                    new GitChange()
                    {
                        ChangeType = VersionControlChangeType.Add,
                        Item = new GitItem() { Path = "/README.MD" },
                        NewContent = new ItemContent()
                        {
                            Content = "Create repository for manifest testing",
                            ContentType = ItemContentType.RawText
                        }
                    }
                }
            };

            gitHttpClient.CreatePushAsync(
                push: new GitPush()
                {
                    RefUpdates = new GitRefUpdate[] { newBranch },
                    Commits = new GitCommitRef[] { newCommit },
                },
                repositoryId: VSTSTestHelper.RepositoryId)
            .Wait();
        }

        /// <summary>
        /// Delete a repository.
        /// </summary>
        /// <param name="token">The token.</param>
        /// <param name="accountName">The account name.</param>
        public static void DeleteRepository(
            string token,
            string accountName)
        {
            var credential = new VssBasicCredential(userName: string.Empty, password: token);
            var connection = new VssConnection(
                baseUrl: new Uri(string.Format("https://{0}.visualstudio.com", accountName)),
                credentials: credential);
            var gitHttpClient = connection.GetClient<GitHttpClient>();

            gitHttpClient.DeleteRepositoryAsync(repositoryId: new Guid(VSTSTestHelper.RepositoryId)).Wait();
        }
    }
}
