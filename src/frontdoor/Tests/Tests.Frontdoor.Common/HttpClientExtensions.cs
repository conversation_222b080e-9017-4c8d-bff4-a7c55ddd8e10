﻿//-----------------------------------------------------------
// Copyright (c) Microsoft Corporation.  All rights reserved.
//-----------------------------------------------------------

namespace Internal.WindowsAzure.ResourceStack.Tests.Frontdoor.Common
{
    using System;
    using System.Collections.Generic;
    using System.Linq;
    using System.Net.Http;
    using System.Text;
    using Microsoft.WindowsAzure.ResourceStack.Common.Json;
    using Microsoft.WindowsAzure.ResourceStack.Frontdoor.Data.Components;

    /// <summary>
    /// This class contains helper methods for HTTP Client. These helper methods should *only*
    /// be used from test code since they are blocking and do not handle references correctly.
    /// </summary>
    public static class HttpClientExtensions
    {
        /// <summary>
        /// Sends a PUT request to the server with the provided JSON body synchronously.
        /// </summary>
        /// <param name="client">The client to use for the request.</param>
        /// <param name="requestUri">The URI for the request.</param>
        /// <param name="body">The JSON body for the put request.</param>
        /// <returns>The http response message for the request.</returns>
        public static HttpResponseMessage PutSync(this HttpClient client, string requestUri, string body)
        {
            return client.PutAsync(
                requestUri: requestUri,
                content: new StringContent(body, Encoding.UTF8, "application/json")).Result;
        }

        /// <summary>
        /// Puts the synchronize.
        /// </summary>
        /// <param name="client">The client.</param>
        /// <param name="requestUri">The request URI.</param>
        /// <param name="body">The body.</param>
        public static HttpResponseMessage PutSync(this HttpClient client, Uri requestUri, string body)
        {
            return client.PutAsync(
                requestUri: requestUri,
                content: new StringContent(body, Encoding.UTF8, "application/json")).Result;
        }

        /// <summary>
        /// Posts the synchronize.
        /// </summary>
        /// <param name="client">The client.</param>
        /// <param name="requestUri">The request URI.</param>
        /// <param name="body">The body.</param>
        public static HttpResponseMessage PostSync(this HttpClient client, string requestUri, string body = null)
        {
            if (string.IsNullOrEmpty(body))
            {
                return client.PostAsync(requestUri: requestUri, content: null).Result;
            }
            else
            {
                return client.PostAsync(
                    requestUri: requestUri,
                    content: new StringContent(body, Encoding.UTF8, "application/json")).Result;
            }
        }

        /// <summary>
        /// Post the synchronize.
        /// </summary>
        /// <param name="client">The client.</param>
        /// <param name="requestUri">The request URI.</param>
        /// <param name="body">The body.</param>
        public static HttpResponseMessage PostSync(this HttpClient client, Uri requestUri, string body = null)
        {
            if (string.IsNullOrEmpty(body))
            {
                return client.PostAsync(requestUri: requestUri, content: null).Result;
            }
            else
            {
                return client.PostAsync(
                    requestUri: requestUri,
                    content: new StringContent(body, Encoding.UTF8, "application/json")).Result;
            }
        }

        /// <summary>
        /// Sends a POST request to the server with the provided JSON body synchronously.
        /// </summary>
        /// <typeparam name="T">The type being sent.</typeparam>
        /// <param name="client">The client to use for the request.</param>
        /// <param name="requestUri">The URI for the request.</param>
        /// <param name="body">The JSON body for the put request.</param>
        /// <returns>The http response message for the request.</returns>
        public static HttpResponseMessage PostAsJsonSync<T>(this HttpClient client, string requestUri, T body)
        {
            return client.PostAsync(
                requestUri: requestUri,
                content: new StringContent(body.ToJson(), Encoding.UTF8, "application/json")).Result;
        }

        /// <summary>
        /// Sends a PUT request to the server with the provided JSON body synchronously
        /// - without content type charset set to null
        /// </summary>
        /// <param name="client">The client to use for the request.</param>
        /// <param name="requestUri">The URI for the request.</param>
        /// <param name="body">The JSON body for the put request.</param>
        /// <returns>The http response message for the request.</returns>
        public static HttpResponseMessage PutSyncWithoutCharset(this HttpClient client, string requestUri, string body)
        {
            var content = new StringContent(body, null, "application/json");
            content.Headers.ContentType.CharSet = null;

            return client.PutAsync(requestUri: requestUri, content: content).Result;
        }

        /// <summary>
        /// Sends a PUT request to the server with the provided JSON body synchronously.
        /// </summary>
        /// <typeparam name="T">The type being sent.</typeparam>
        /// <param name="client">The client to use for the request.</param>
        /// <param name="requestUri">The URI for the request.</param>
        /// <param name="body">The JSON body for the put request.</param>
        /// <returns>The http response message for the request.</returns>
        public static HttpResponseMessage PutAsJsonSync<T>(this HttpClient client, string requestUri, T body)
        {
            return client.PutAsync(
                requestUri: requestUri,
                content: new StringContent(body.ToJson(), Encoding.UTF8, "application/json")).Result;
        }

        /// <summary>
        /// Sends a PATCH request to the server with the provided JSON body synchronously.
        /// </summary>
        /// <typeparam name="T">The type being sent.</typeparam>
        /// <param name="client">The client to use for the request.</param>
        /// <param name="requestUri">The URI for the request.</param>
        /// <param name="body">The JSON body for the put request.</param>
        /// <returns>The http response message for the request.</returns>
        public static HttpResponseMessage PatchSync<T>(this HttpClient client, string requestUri, T body)
        {
            return client.PatchSync(requestUri: requestUri, body: body.ToJson());
        }

        /// <summary>
        /// Sends a PATCH request to the server with the provided JSON body synchronously.
        /// </summary>
        /// <param name="client">The client to use for the request.</param>
        /// <param name="requestUri">The URI for the request.</param>
        /// <param name="body">The JSON body for the put request.</param>
        /// <returns>The http response message for the request.</returns>
        public static HttpResponseMessage PatchSync(this HttpClient client, string requestUri, string body)
        {
            var requestMessage = new HttpRequestMessage(method: new HttpMethod("PATCH"), requestUri: requestUri)
            {
                Content = new StringContent(body, Encoding.UTF8, "application/json"),
            };

            return client.SendAsync(requestMessage).Result;
        }

        /// <summary>
        /// Sends a DELETE request to the server synchronously.
        /// </summary>
        /// <param name="client">The client to use for the request.</param>
        /// <param name="requestUri">The URI for the request.</param>
        /// <returns>The http response message for the request.</returns>
        public static HttpResponseMessage DeleteSync(this HttpClient client, string requestUri)
        {
            return client.DeleteAsync(requestUri).Result;
        }

        /// <summary>
        /// Deletes the synchronize.
        /// </summary>
        /// <param name="client">The client.</param>
        /// <param name="requestUri">The request URI.</param>
        public static HttpResponseMessage DeleteSync(this HttpClient client, Uri requestUri)
        {
            return client.DeleteAsync(requestUri).Result;
        }

        /// <summary>
        /// Sends a GET request to the server with the provided JSON body synchronously.
        /// </summary>
        /// <param name="client">The client to use for the request.</param>
        /// <param name="requestUri">The URI for the request.</param>
        /// <returns>The http response message for the request.</returns>
        public static HttpResponseMessage GetSync(this HttpClient client, string requestUri)
        {
            return client.GetAsync(requestUri).Result;
        }

        /// <summary>
        /// Gets the synchronize.
        /// </summary>
        /// <param name="client">The client.</param>
        /// <param name="requestUri">The request URI.</param>
        public static HttpResponseMessage GetSync(this HttpClient client, Uri requestUri)
        {
            return client.GetAsync(requestUri).Result;
        }

        /// <summary>
        /// Synchronously reads the JSON content from the http response message.
        /// </summary>
        /// <typeparam name="T">The type of object contained in the JSON.</typeparam>
        /// <param name="message">The response message to be read.</param>
        /// <returns>An object of type T instantiated from the response message's body.</returns>
        public static T ReadContent<T>(this HttpResponseMessage message)
        {
            return message.Content.ReadAsStringAsync().Result.FromJson<T>();
        }

        /// <summary>
        /// Synchronously reads the content from the http response message.
        /// </summary>
        /// <param name="message">The response message to be read.</param>
        /// <returns>The content as string from the response message's body.</returns>
        public static string ReadContentAsString(this HttpResponseMessage message)
        {
            return message.Content.ReadAsStringAsync().Result;
        }

        /// <summary>
        /// Synchronously reads the array content from the http response message.
        /// </summary>
        /// <typeparam name="T">The type of the array element.</typeparam>
        /// <param name="message">The response message to be read.</param>
        public static T[] ReadContentAsArray<T>(this HttpResponseMessage message)
        {
            var content = message.ReadContentAsString();
            var array = content.TryFromJson<T[]>();
            if (array == null)
            {
                array = content.TryFromJson<ResponseWithContinuation<T[]>>().Value;
            }

            return array;
        }

        /// <summary>
        /// Converts query parameters dictionary to query string.
        /// </summary>
        /// <param name="queryParameters">The query parameters.</param>
        public static string ToQueryString(this IDictionary<string, string> queryParameters)
        {
            return string.Join(
                separator: "&",
                values: queryParameters.Select(kvp => string.Format("{0}={1}", kvp.Key, kvp.Value)));
        }
    }
}
