﻿//-----------------------------------------------------------
// Copyright (c) Microsoft Corporation.  All rights reserved.
//-----------------------------------------------------------

namespace Internal.WindowsAzure.ResourceStack.Tests.Frontdoor.Common
{
    using System;
    using System.Linq;
    using Microsoft.WindowsAzure.ResourceStack.Common.Extensions;
    using Microsoft.WindowsAzure.ResourceStack.Frontdoor.Data.Configuration;
    using Microsoft.WindowsAzure.ResourceStack.Frontdoor.Data.Entities.Registration;

    /// <summary>
    /// Class to add limited-scope usage of provider registrations.
    /// </summary>
    /// <remarks>
    /// This should be used before any creation of front door instances.
    /// </remarks>
    public class ResourceProviderRegistrationScope : IDisposable
    {
        /// <summary>
        /// Gets or sets the registrations in this scope.
        /// </summary>
        private ResourceProviderManifestRegistration[] ScopedProviderRegistrations { get; set; }

        /// <summary>
        /// Gets or sets the registrations in this scope.
        /// </summary>
        private ResourceProviderManifestRegistration[] PreviousProviderRegistrations { get; set; }

        /// <summary>
        /// Initializes a new instance of the <see cref="ResourceProviderRegistrationScope" /> class.
        /// </summary>
        /// <param name="providerNamespace">The provider namespace.</param>
        /// <param name="resourceType">Type of the resource.</param>
        /// <param name="location">The location.</param>
        /// <param name="routingType">The routing type.</param>
        /// <param name="providerType">Type of the provider.</param>
        /// <param name="apiVersion">The API version.</param>
        public ResourceProviderRegistrationScope(string providerNamespace, string resourceType, string location, RoutingType routingType = RoutingType.Default, ResourceProviderType providerType = ResourceProviderType.NotSpecified, string apiVersion = null)
            : this(TestEnvironment.CreateResourceProviderManifest(providerNamespace: providerNamespace, resourceType: resourceType, locations: new[] { location }, legacyResourceTypeNames: null, routingType: routingType, providerType: providerType, apiVersion: apiVersion))
        {
        }

        /// <summary>
        /// Initializes a new instance of the <see cref="ResourceProviderRegistrationScope"/> class.
        /// </summary>
        /// <param name="providerRegistrations">The registrations to include in the scope.</param>
        public ResourceProviderRegistrationScope(params ResourceProviderManifestRegistration[] providerRegistrations)
        {
            this.PreviousProviderRegistrations = providerRegistrations
                .SelectMany(registration => FrontdoorConfigurationLoader.PrimaryFrontdoorConfiguration.DataProviders.RegistrationDataProvider.FindRegistrationsByNamespace(registration.ResourceProviderNamespace).Result)
                .Where(registration => registration != null)
                .ToArray();

            this.ScopedProviderRegistrations = providerRegistrations.SelectManyArray(
                registration => string.IsNullOrEmpty(registration.Location)
                    ? FrontdoorConfigurationLoader.AvailableLocations.Select(
                        location => new ResourceProviderManifestRegistration
                        {
                            Manifest = registration.Manifest,
                            ResourceTypeSkus = registration.ResourceTypeSkus,
                            ResourceTypeAliases = registration.ResourceTypeAliases,
                            IsEnabled = registration.IsEnabled,
                            Location = location,
                        })
                    : registration.AsArray());

            foreach (var registration in this.ScopedProviderRegistrations)
            {
                registration.LastModifiedBy = "Test";
                FrontdoorConfigurationLoader.PrimaryFrontdoorConfiguration.DataProviders.RegistrationDataProvider.Save(registration).Wait();
            }

            TestEnvironment.RestartCaches();
        }

        /// <summary>
        /// Disposes the scope and cleans up the scoped registrations.
        /// </summary>
        public void Dispose()
        {
            foreach (var registration in this.ScopedProviderRegistrations)
            {
                registration.IsEnabled = false;
                registration.LastModifiedBy = "Test";
                FrontdoorConfigurationLoader.PrimaryFrontdoorConfiguration.DataProviders.RegistrationDataProvider.Save(registration).Wait();
            }

            foreach (var registration in this.PreviousProviderRegistrations)
            {
                registration.LastModifiedBy = "Test";
                FrontdoorConfigurationLoader.PrimaryFrontdoorConfiguration.DataProviders.RegistrationDataProvider.Save(registration).Wait();
            }
        }
    }
}
