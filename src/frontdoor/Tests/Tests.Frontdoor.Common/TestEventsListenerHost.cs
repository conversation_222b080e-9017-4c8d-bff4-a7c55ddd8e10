﻿//-----------------------------------------------------------
// Copyright (c) Microsoft Corporation.  All rights reserved.
//-----------------------------------------------------------

namespace Internal.WindowsAzure.ResourceStack.Tests.Frontdoor.Common
{
    using System;
    using System.Collections.Concurrent;
    using System.Linq;
    using Microsoft.WindowsAzure.ResourceStack.Common.EventSources.Tracing;
    using Microsoft.WindowsAzure.ResourceStack.Common.Extensions;

    /// <summary>
    /// The test events listener host.
    /// </summary>
    public class TestEventsListenerHost : MarshalByRefObject
    {
        #region Constructor

        /// <summary>
        /// The event ids that are registered and will be recorded during the test.
        /// </summary>
        private static readonly ConcurrentDictionary<int, bool> RegisteredEvents = new ConcurrentDictionary<int, bool>();

        /// <summary>
        /// The events that were recorded per the registered event Ids.
        /// </summary>
        private static readonly ConcurrentDictionary<int, ConcurrentBag<string>> Events = new ConcurrentDictionary<int, ConcurrentBag<string>>();

        /// <summary>
        /// The instance of test events listener host.
        /// </summary>
        public static readonly TestEventsListenerHost Instance = new TestEventsListenerHost();

        #endregion

        #region Public methods

        /// <summary>
        /// Ensures the test events listener host is initialized.
        /// </summary>
        public void EnsureInitialized()
        {
            TestEventsListener.Instance.EnsureInitialized();
        }

        /// <summary>
        /// Registers the specified eventId so that it is captured by the test listener.
        /// </summary>
        /// <param name="eventId">The eventId to be registered.</param>
        public void RegisterEventId(int eventId)
        {
            TestEventsListenerHost.Events.TryAdd(eventId, new ConcurrentBag<string>());
            TestEventsListenerHost.RegisteredEvents[eventId] = true;
        }

        /// <summary>
        /// Un-registers the specified eventId so that it is no longer captured by the test listener.
        /// </summary>
        /// <param name="eventId">The eventId to be unregistered.</param>
        public void UnregisterEventId(int eventId)
        {
            TestEventsListenerHost.RegisteredEvents[eventId] = false;
            TestEventsListenerHost.Events[eventId] = new ConcurrentBag<string>();
        }

        /// <summary>
        /// Gets the events matching the eventId.
        /// </summary>
        /// <param name="eventId">The eventId for which events are retrieved.</param>
        public string[] GetEvents(int eventId)
        {
            bool registered = false;
            if (!TestEventsListenerHost.RegisteredEvents.TryGetValue(eventId, out registered) || !registered)
            {
                throw new ArgumentException(string.Format("EventId {0} is not registered and was not collected.", eventId));
            }

            ConcurrentBag<string> bag = null;
            if (TestEventsListenerHost.Events.TryGetValue(eventId, out bag) && bag != null)
            {
                return bag.ToArray();
            }

            return new string[0];
        }

        #endregion

        #region TestEventsListener

        /// <summary>
        /// The test events listener.
        /// </summary>
        private class TestEventsListener : EventListener 
        {
            /// <summary>
            /// The singleton instance of the test events listener.
            /// </summary>
            internal static readonly TestEventsListener Instance = new TestEventsListener();

            /// <summary>
            /// Initializes a new instance of the <see cref="TestEventsListener"/> class.
            /// </summary>
            protected TestEventsListener()
            {
            }

            /// <summary>
            /// Ensures the test events listener.
            /// </summary>
            internal void EnsureInitialized()
            {
            }

            /// <summary>
            /// Called when event source is created.
            /// </summary>
            /// <param name="eventSource">The event source.</param>
            protected override void OnEventSourceCreated(EventSource eventSource)
            {
                this.EnableEvents(eventSource, EventLevel.Verbose);
            }

            /// <summary>
            /// Called when event date is written.
            /// </summary>
            /// <param name="eventData">The event data.</param>
            protected override void OnEventWritten(EventWrittenEventArgs eventData)
            {
                bool registered = false;
                if (TestEventsListenerHost.RegisteredEvents.TryGetValue(eventData.EventId, out registered) && registered)
                {
                    TestEventsListenerHost.Events[eventData.EventId].Add(eventData.Message.CoalesceFormat(eventData.Payload.ToArray()));
                }
            }
        }

        #endregion
    }
}