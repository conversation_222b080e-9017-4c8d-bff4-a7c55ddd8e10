﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="15.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="..\..\..\packages\Antlr4.CodeGenerator.4.6.4\build\Antlr4.CodeGenerator.props" Condition="Exists('..\..\..\packages\Antlr4.CodeGenerator.4.6.4\build\Antlr4.CodeGenerator.props')" />
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">x64</Platform>
    <ProjectGuid>{395F7C2E-10B1-4D7C-805F-F9437451365D}</ProjectGuid>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>Tests.Frontdoor.Common</RootNamespace>
    <AssemblyName>Tests.Frontdoor.Common</AssemblyName>
    <TargetFrameworkVersion>v4.8</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <Deterministic>true</Deterministic>
    <NuGetPackageImportStamp>
    </NuGetPackageImportStamp>
    <TargetFrameworkProfile />
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|x64' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|x64' ">
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="Antlr4.Runtime, Version=4.6.0.0, Culture=neutral, PublicKeyToken=09abb75b9ed49849, processorArchitecture=MSIL">
      <HintPath>..\..\..\packages\Antlr4.Runtime.4.6.4\lib\net45\Antlr4.Runtime.dll</HintPath>
    </Reference>
    <Reference Include="Cfx.Assert, Version=132.300.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\..\..\packages\DstsAuthentication.master.2.0.1.16\lib\net45\Cfx.Assert.dll</HintPath>
    </Reference>
    <Reference Include="Internal.WindowsAzure.ResourceStack.Tests.Common">
      <HintPath>..\..\..\packages\DatabricksFrontdoorDependencies.1.21.928.327\content\Internal.WindowsAzure.ResourceStack.Tests.Common\Internal.WindowsAzure.ResourceStack.Tests.Common.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Azure.KeyVault.Core, Version=1.0.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\..\..\packages\Microsoft.Azure.KeyVault.Core.1.0.0\lib\net40\Microsoft.Azure.KeyVault.Core.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Data.Edm, Version=5.8.4.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\..\..\packages\Microsoft.Data.Edm.5.8.4\lib\net40\Microsoft.Data.Edm.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Data.OData, Version=5.8.4.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\..\..\packages\Microsoft.Data.OData.5.8.4\lib\net40\Microsoft.Data.OData.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Data.Services.Client, Version=5.8.4.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\..\..\packages\Microsoft.Data.Services.Client.5.8.4\lib\net40\Microsoft.Data.Services.Client.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.IdentityModel, Version=3.5.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\..\..\packages\DstsAuthentication.master.2.0.1.16\lib\net45\Microsoft.IdentityModel.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.IdentityModel.Authorization, Version=5.0.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\..\..\packages\Microsoft.IdentityModel.Authorization.DataModel.5.0.5667\lib\net45\Microsoft.IdentityModel.Authorization.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.IdentityModel.Authorization.DataModel.ARM, Version=5.0.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\..\..\packages\Microsoft.IdentityModel.Authorization.DataModel.5.0.5667\lib\net45\Microsoft.IdentityModel.Authorization.DataModel.ARM.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.TeamFoundation.Build2.WebApi, Version=15.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\..\..\packages\Microsoft.TeamFoundationServer.Client.15.112.1\lib\net45\Microsoft.TeamFoundation.Build2.WebApi.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.TeamFoundation.Chat.WebApi, Version=15.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\..\..\packages\Microsoft.TeamFoundationServer.Client.15.112.1\lib\net45\Microsoft.TeamFoundation.Chat.WebApi.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.TeamFoundation.Common, Version=15.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\..\..\packages\Microsoft.VisualStudio.Services.Client.15.112.1\lib\net45\Microsoft.TeamFoundation.Common.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.TeamFoundation.Core.WebApi, Version=15.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\..\..\packages\Microsoft.TeamFoundationServer.Client.15.112.1\lib\net45\Microsoft.TeamFoundation.Core.WebApi.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.TeamFoundation.Dashboards.WebApi, Version=15.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\..\..\packages\Microsoft.TeamFoundationServer.Client.15.112.1\lib\net45\Microsoft.TeamFoundation.Dashboards.WebApi.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.TeamFoundation.DistributedTask.Common.Contracts, Version=15.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\..\..\packages\Microsoft.TeamFoundation.DistributedTask.Common.15.112.1\lib\net45\Microsoft.TeamFoundation.DistributedTask.Common.Contracts.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.TeamFoundation.Policy.WebApi, Version=15.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\..\..\packages\Microsoft.TeamFoundationServer.Client.15.112.1\lib\net45\Microsoft.TeamFoundation.Policy.WebApi.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.TeamFoundation.SourceControl.WebApi, Version=15.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\..\..\packages\Microsoft.TeamFoundationServer.Client.15.112.1\lib\net45\Microsoft.TeamFoundation.SourceControl.WebApi.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.TeamFoundation.Test.WebApi, Version=15.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\..\..\packages\Microsoft.TeamFoundationServer.Client.15.112.1\lib\net45\Microsoft.TeamFoundation.Test.WebApi.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.TeamFoundation.TestManagement.WebApi, Version=15.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\..\..\packages\Microsoft.TeamFoundationServer.Client.15.112.1\lib\net45\Microsoft.TeamFoundation.TestManagement.WebApi.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.TeamFoundation.Work.WebApi, Version=15.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\..\..\packages\Microsoft.TeamFoundationServer.Client.15.112.1\lib\net45\Microsoft.TeamFoundation.Work.WebApi.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.TeamFoundation.WorkItemTracking.WebApi, Version=15.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\..\..\packages\Microsoft.TeamFoundationServer.Client.15.112.1\lib\net45\Microsoft.TeamFoundation.WorkItemTracking.WebApi.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.VisualStudio.Services.Common, Version=15.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\..\..\packages\Microsoft.VisualStudio.Services.Client.15.112.1\lib\net45\Microsoft.VisualStudio.Services.Common.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.VisualStudio.Services.WebApi, Version=15.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\..\..\packages\Microsoft.VisualStudio.Services.Client.15.112.1\lib\net45\Microsoft.VisualStudio.Services.WebApi.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.VisualStudio.TestPlatform.TestFramework, Version=14.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\..\..\packages\MSTest.TestFramework.2.2.5\lib\net45\Microsoft.VisualStudio.TestPlatform.TestFramework.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.VisualStudio.TestPlatform.TestFramework.Extensions, Version=14.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\..\..\packages\MSTest.TestFramework.2.2.5\lib\net45\Microsoft.VisualStudio.TestPlatform.TestFramework.Extensions.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.WindowsAzure.Configuration, Version=3.0.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\..\..\packages\Microsoft.WindowsAzure.ConfigurationManager.3.1.0\lib\net40\Microsoft.WindowsAzure.Configuration.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.WindowsAzure.ResourceStack.Common, Version=6.0.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\..\..\packages\ResourceStack.Storage.Client.6.0.0.900\lib\net45\Microsoft.WindowsAzure.ResourceStack.Common.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.WindowsAzure.ResourceStack.Common.Compression, Version=6.0.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\..\..\packages\ResourceStack.Storage.Client.6.0.0.900\lib\net45\Microsoft.WindowsAzure.ResourceStack.Common.Compression.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.WindowsAzure.ResourceStack.Common.DocumentDb">
      <HintPath>..\..\..\packages\DatabricksFrontdoorDependencies.1.21.928.327\content\Microsoft.WindowsAzure.ResourceStack.Common.DocumentDb\Microsoft.WindowsAzure.ResourceStack.Common.DocumentDb.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.WindowsAzure.ResourceStack.Common.Dsms">
      <HintPath>..\..\..\packages\DatabricksFrontdoorDependencies.1.21.928.327\content\Microsoft.WindowsAzure.ResourceStack.Common.Dsms\Microsoft.WindowsAzure.ResourceStack.Common.Dsms.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.WindowsAzure.ResourceStack.Common.EventSources, Version=6.0.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\..\..\packages\ResourceStack.Storage.Client.6.0.0.900\lib\net45\Microsoft.WindowsAzure.ResourceStack.Common.EventSources.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.WindowsAzure.ResourceStack.Common.Instrumentation, Version=6.0.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\..\..\packages\ResourceStack.Storage.Client.6.0.0.900\lib\net45\Microsoft.WindowsAzure.ResourceStack.Common.Instrumentation.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.WindowsAzure.ResourceStack.Common.Jobs, Version=6.0.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\..\..\packages\ResourceStack.Storage.Client.6.0.0.900\lib\net45\Microsoft.WindowsAzure.ResourceStack.Common.Jobs.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.WindowsAzure.ResourceStack.Common.Rdfe">
      <HintPath>..\..\..\packages\DatabricksFrontdoorDependencies.1.21.928.327\content\Microsoft.WindowsAzure.ResourceStack.Common.Rdfe\Microsoft.WindowsAzure.ResourceStack.Common.Rdfe.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.WindowsAzure.ResourceStack.Common.Services, Version=6.0.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\..\..\packages\ResourceStack.Storage.Client.6.0.0.900\lib\net45\Microsoft.WindowsAzure.ResourceStack.Common.Services.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.WindowsAzure.ResourceStack.Common.Storage, Version=6.0.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\..\..\packages\ResourceStack.Storage.Client.6.0.0.900\lib\net45\Microsoft.WindowsAzure.ResourceStack.Common.Storage.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.WindowsAzure.ResourceStack.Frontdoor.Admin">
      <HintPath>..\..\..\packages\DatabricksFrontdoorDependencies.1.21.928.327\content\Microsoft.WindowsAzure.ResourceStack.Frontdoor.Admin\Microsoft.WindowsAzure.ResourceStack.Frontdoor.Admin.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.WindowsAzure.ResourceStack.Frontdoor.Common">
      <HintPath>..\..\..\packages\DatabricksFrontdoorDependencies.1.21.928.327\content\Microsoft.WindowsAzure.ResourceStack.Frontdoor.Common\Microsoft.WindowsAzure.ResourceStack.Frontdoor.Common.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.WindowsAzure.ResourceStack.Frontdoor.Data">
      <HintPath>..\..\..\packages\DatabricksFrontdoorDependencies.1.21.928.327\content\Microsoft.WindowsAzure.ResourceStack.Frontdoor.Data\Microsoft.WindowsAzure.ResourceStack.Frontdoor.Data.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.WindowsAzure.ResourceStack.Frontdoor.Expression">
      <HintPath>..\..\..\packages\DatabricksFrontdoorDependencies.1.21.928.327\content\Microsoft.WindowsAzure.ResourceStack.Frontdoor.Expression\Microsoft.WindowsAzure.ResourceStack.Frontdoor.Expression.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.WindowsAzure.ResourceStack.Frontdoor.ResourceHydration">
      <HintPath>..\..\..\packages\DatabricksFrontdoorDependencies.1.21.928.327\content\Microsoft.WindowsAzure.ResourceStack.Frontdoor.ResourceHydration\Microsoft.WindowsAzure.ResourceStack.Frontdoor.ResourceHydration.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.WindowsAzure.ResourceStack.Frontdoor.Templates">
      <HintPath>..\..\..\packages\DatabricksFrontdoorDependencies.1.21.928.327\content\Microsoft.WindowsAzure.ResourceStack.Frontdoor.Templates\Microsoft.WindowsAzure.ResourceStack.Frontdoor.Templates.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.WindowsAzure.ResourceStack.Frontdoor.Web">
      <HintPath>..\..\..\packages\DatabricksFrontdoorDependencies.1.21.928.327\content\Microsoft.WindowsAzure.ResourceStack.Frontdoor.Web\Microsoft.WindowsAzure.ResourceStack.Frontdoor.Web.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.WindowsAzure.ResourceStack.Frontdoor.Worker">
      <HintPath>..\..\..\packages\DatabricksFrontdoorDependencies.1.21.928.327\content\Microsoft.WindowsAzure.ResourceStack.Frontdoor.Worker\Microsoft.WindowsAzure.ResourceStack.Frontdoor.Worker.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.WindowsAzure.ResourceStack.Providers.Authorization.Data">
      <HintPath>..\..\..\packages\DatabricksFrontdoorDependencies.1.21.928.327\content\Microsoft.WindowsAzure.ResourceStack.Providers.Authorization.Data\Microsoft.WindowsAzure.ResourceStack.Providers.Authorization.Data.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.WindowsAzure.ResourceStack.Providers.Feature.Data">
      <HintPath>..\..\..\packages\DatabricksFrontdoorDependencies.1.21.928.327\content\Microsoft.WindowsAzure.ResourceStack.Providers.Feature.Data\Microsoft.WindowsAzure.ResourceStack.Providers.Feature.Data.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.WindowsAzure.Security.Authentication, Version=2.0.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\..\..\packages\DstsAuthentication.master.2.0.1.16\lib\net45\Microsoft.WindowsAzure.Security.Authentication.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.WindowsAzure.Security.Authentication.Contracts, Version=2.0.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\..\..\packages\DstsAuthentication.master.2.0.1.16\lib\net45\Microsoft.WindowsAzure.Security.Authentication.Contracts.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.WindowsAzure.Security.Authentication.Helpers.PassiveAuthHelper, Version=2.0.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\..\..\packages\DstsAuthentication.master.2.0.1.16\lib\net45\Microsoft.WindowsAzure.Security.Authentication.Helpers.PassiveAuthHelper.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.WindowsAzure.Security.Authentication.Logging, Version=2.0.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\..\..\packages\DstsAuthentication.master.2.0.1.16\lib\net45\Microsoft.WindowsAzure.Security.Authentication.Logging.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.WindowsAzure.Security.CredentialsManagement.Client, Version=4.3.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\..\..\packages\DsmsCredentialsManagement.4.3.183377\lib\net462\Microsoft.WindowsAzure.Security.CredentialsManagement.Client.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.WindowsAzure.Security.CredentialsManagement.StorageHelper, Version=4.3.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\..\..\packages\DsmsCredentialsManagement.4.3.183377\lib\net462\Microsoft.WindowsAzure.Security.CredentialsManagement.StorageHelper.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.WindowsAzure.Security.TestUtilities, Version=2.0.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\..\..\packages\DstsAuthentication.master.2.0.1.16\lib\net45\Test\Microsoft.WindowsAzure.Security.TestUtilities.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.WindowsAzure.Storage, Version=8.3.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\..\..\packages\WindowsAzure.Storage.8.3.0\lib\net45\Microsoft.WindowsAzure.Storage.dll</HintPath>
    </Reference>
    <Reference Include="Newtonsoft.Json, Version=13.0.0.0, Culture=neutral, PublicKeyToken=30ad4fe6b2a6aeed, processorArchitecture=MSIL">
      <HintPath>..\..\..\packages\Newtonsoft.Json.13.0.2\lib\net45\Newtonsoft.Json.dll</HintPath>
    </Reference>
    <Reference Include="RetryEngine, Version=2.0.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\..\..\packages\DstsAuthentication.master.2.0.1.16\lib\net45\RetryEngine\RetryEngine.dll</HintPath>
    </Reference>
    <Reference Include="System" />
    <Reference Include="System.Core" />
    <Reference Include="System.Data.Services.Client" />
    <Reference Include="System.IdentityModel" />
    <Reference Include="System.IdentityModel.Tokens.Jwt, Version=4.0.20622.1351, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\..\..\packages\System.IdentityModel.Tokens.Jwt.4.0.2.206221351\lib\net45\System.IdentityModel.Tokens.Jwt.dll</HintPath>
    </Reference>
    <Reference Include="System.Net.Http.Extensions, Version=2.2.29.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\..\..\packages\Microsoft.Net.Http.2.2.29\lib\net45\System.Net.Http.Extensions.dll</HintPath>
    </Reference>
    <Reference Include="System.Net.Http.Formatting, Version=5.2.6.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\..\..\..\packages\Microsoft.AspNet.WebApi.Client.5.2.6\lib\net45\System.Net.Http.Formatting.dll</HintPath>
    </Reference>
    <Reference Include="System.Net.Http.Primitives, Version=4.2.29.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\..\..\packages\Microsoft.Net.Http.2.2.29\lib\net45\System.Net.Http.Primitives.dll</HintPath>
    </Reference>
    <Reference Include="System.Net.Http.WebRequest" />
    <Reference Include="System.Runtime.Serialization" />
    <Reference Include="System.ServiceModel" />
    <Reference Include="System.ServiceModel.Activation" />
    <Reference Include="System.ServiceModel.Web" />
    <Reference Include="System.Spatial, Version=5.8.4.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\..\..\packages\System.Spatial.5.8.4\lib\net40\System.Spatial.dll</HintPath>
    </Reference>
    <Reference Include="System.Threading.Tasks.Dataflow, Version=4.5.24.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\..\..\packages\Microsoft.Tpl.Dataflow.4.5.24\lib\portable-net45+win8+wpa81\System.Threading.Tasks.Dataflow.dll</HintPath>
    </Reference>
    <Reference Include="System.Web" />
    <Reference Include="System.Web.Http, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\..\..\packages\Microsoft.AspNet.WebApi.Core.5.2.3\lib\net45\System.Web.Http.dll</HintPath>
    </Reference>
    <Reference Include="System.Web.Http.WebHost, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\..\..\packages\Microsoft.AspNet.WebApi.WebHost.5.2.3\lib\net45\System.Web.Http.WebHost.dll</HintPath>
    </Reference>
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="System.Data" />
    <Reference Include="System.Net.Http" />
    <Reference Include="System.Xml" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="AadLoginApiTestHost.cs" />
    <Compile Include="AccessTokenParser.cs" />
    <Compile Include="AccessTokenUtility.cs" />
    <Compile Include="AdminTestHost.cs" />
    <Compile Include="APIValidationTestHost.cs" />
    <Compile Include="AuthorizationPolicyUtility.cs" />
    <Compile Include="BaseResourceProviderTestHost.cs" />
    <Compile Include="EventGridTestHost.cs" />
    <Compile Include="FrontdoorTestHost.cs" />
    <Compile Include="GraphApiTestHost.cs" />
    <Compile Include="HttpClientExtensions.cs" />
    <Compile Include="ITestSetup.cs" />
    <Compile Include="JitAPITestHost.cs" />
    <Compile Include="KeyVaultTestHost.cs" />
    <Compile Include="LegacyManagementApiTestHost.cs" />
    <Compile Include="ManagementGroupApiTestHost.cs" />
    <Compile Include="MarketplaceTestHost.cs" />
    <Compile Include="MockFrontdoorTestHostServer.cs" />
    <Compile Include="PolicyUtility.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="ProvisioningServiceTestHost.cs" />
    <Compile Include="ResourceProviderRegistrationScope.cs" />
    <Compile Include="ResourceProviderTestHost.cs" />
    <Compile Include="ResourceProviderTestHostRegistration.cs" />
    <Compile Include="ResourceProviderUtility.cs" />
    <Compile Include="TestEnvironment.cs" />
    <Compile Include="TestEventsListenerHost.cs" />
    <Compile Include="TestEventsRegistrationScope.cs" />
    <Compile Include="VSTSTestHelper.cs" />
  </ItemGroup>
  <ItemGroup>
    <None Include="app.config" />
    <None Include="packages.config" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\..\..\providers\Roles\Providers.Common\Providers.Common.csproj">
      <Project>{fb6ef224-378d-4e5c-a323-cef06b1cfab2}</Project>
      <Name>Providers.Common</Name>
    </ProjectReference>
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
  <Import Project="..\..\..\packages\Microsoft.Bcl.Build.1.0.14\tools\Microsoft.Bcl.Build.targets" Condition="Exists('..\..\..\packages\Microsoft.Bcl.Build.1.0.14\tools\Microsoft.Bcl.Build.targets')" />
  <Target Name="EnsureBclBuildImported" BeforeTargets="BeforeBuild" Condition="'$(BclBuildImported)' == ''">
    <Error Condition="!Exists('..\..\..\packages\Microsoft.Bcl.Build.1.0.14\tools\Microsoft.Bcl.Build.targets')" Text="This project references NuGet package(s) that are missing on this computer. Enable NuGet Package Restore to download them.  For more information, see http://go.microsoft.com/fwlink/?LinkID=317567." HelpKeyword="BCLBUILD2001" />
    <Error Condition="Exists('..\..\..\packages\Microsoft.Bcl.Build.1.0.14\tools\Microsoft.Bcl.Build.targets')" Text="The build restored NuGet packages. Build the project again to include these packages in the build. For more information, see http://go.microsoft.com/fwlink/?LinkID=317568." HelpKeyword="BCLBUILD2002" />
  </Target>
  <Target Name="EnsureNuGetPackageBuildImports" BeforeTargets="PrepareForBuild">
    <PropertyGroup>
      <ErrorText>This project references NuGet package(s) that are missing on this computer. Use NuGet Package Restore to download them.  For more information, see http://go.microsoft.com/fwlink/?LinkID=322105. The missing file is {0}.</ErrorText>
    </PropertyGroup>
    <Error Condition="!Exists('..\..\..\packages\Antlr4.CodeGenerator.4.6.4\build\Antlr4.CodeGenerator.props')" Text="$([System.String]::Format('$(ErrorText)', '..\..\..\packages\Antlr4.CodeGenerator.4.6.4\build\Antlr4.CodeGenerator.props'))" />
    <Error Condition="!Exists('..\..\..\packages\Antlr4.CodeGenerator.4.6.4\build\Antlr4.CodeGenerator.targets')" Text="$([System.String]::Format('$(ErrorText)', '..\..\..\packages\Antlr4.CodeGenerator.4.6.4\build\Antlr4.CodeGenerator.targets'))" />
    <Error Condition="!Exists('..\..\..\packages\DsmsCredentialsManagement.4.3.183377\build\DsmsCredentialsManagement.targets')" Text="$([System.String]::Format('$(ErrorText)', '..\..\..\packages\DsmsCredentialsManagement.4.3.183377\build\DsmsCredentialsManagement.targets'))" />
  </Target>
  <Import Project="..\..\..\packages\Antlr4.CodeGenerator.4.6.4\build\Antlr4.CodeGenerator.targets" Condition="Exists('..\..\..\packages\Antlr4.CodeGenerator.4.6.4\build\Antlr4.CodeGenerator.targets')" />
  <Import Project="..\..\..\packages\DsmsCredentialsManagement.4.3.183377\build\DsmsCredentialsManagement.targets" Condition="Exists('..\..\..\packages\DsmsCredentialsManagement.4.3.183377\build\DsmsCredentialsManagement.targets')" />
</Project>