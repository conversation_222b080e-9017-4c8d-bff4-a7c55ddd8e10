﻿//-----------------------------------------------------------
// Copyright (c) Microsoft Corporation.  All rights reserved.
//-----------------------------------------------------------

namespace Internal.WindowsAzure.ResourceStack.Tests.Frontdoor.Common
{
    using System;
    using System.Collections.Generic;
    using System.Linq;
    using System.Net;
    using System.Net.Http;
    using System.Web.Http;
    using System.Web.Http.Controllers;
    using System.Web.Http.Dependencies;
    using System.Web.Http.Dispatcher;
    using Internal.WindowsAzure.ResourceStack.Tests.Common;
    using Microsoft.WindowsAzure.ResourceStack.Common.Extensions;
    using Newtonsoft.Json.Linq;

    /// <summary>
    /// Delegate used for getting management groups.
    /// </summary>
    /// <param name="request">The incoming request.</param>
    /// <param name="entityId">The entity id.</param>
    public delegate HttpResponseMessage GetManagementGroupAncestorsDelegate(HttpRequestMessage request, string entityId);

    /// <summary>
    /// Delegate used for getting management groups.
    /// </summary>
    /// <param name="request">The incoming request.</param>
    /// <param name="entityId">The entity id.</param>
    public delegate HttpResponseMessage GetManagementGroupDelegate(HttpRequestMessage request, string entityId);

    /// <summary>
    /// Delegate used for getting inherited subscriptions.
    /// </summary>
    /// <param name="request">The incoming request.</param>
    public delegate HttpResponseMessage GetInheritedSubscriptionsDelegate(HttpRequestMessage request);

    /// <summary>
    /// The web service host for the management group API.
    /// </summary>
    public class ManagementGroupApiTestHost : ServiceTestHost<ManagementGroupApiTestHost.ManagementGroupApiTestHostServer>, IDisposable
    {
        /// <summary>
        /// Initializes a new instance of the <see cref="ManagementGroupApiTestHost"/> class.
        /// </summary>
        /// <param name="serviceUri">The service URI.</param>
        public ManagementGroupApiTestHost(Uri serviceUri)
            : base(serviceUri)
        {
            this.Start();
        }

        /// <summary>
        /// Performs application-defined tasks associated with freeing, releasing, or resetting unmanaged resources.
        /// </summary>
        public void Dispose()
        {
            this.Stop();
        }

        /// <summary>
        /// Restarts this instance.
        /// </summary>
        public void Restart()
        {
            this.Stop();
            this.Start();
        }

        /// <summary>
        /// Gets the state of the test host.
        /// </summary>
        protected override object GetState
        {
            get { return this; }
        }

        /// <summary>
        /// Gets or sets the delegate called when management group ancestors request is received.
        /// </summary>
        public GetManagementGroupAncestorsDelegate OnGetManagementGroupAncestors { get; set; }

        /// <summary>
        /// Gets or sets the delegate called when management group request is received.
        /// </summary>
        public GetManagementGroupDelegate OnGetManagementGroup { get; set; }

        /// <summary>
        /// Gets or sets the delegate called when inherited subscriptions request is received.
        /// </summary>
        public GetInheritedSubscriptionsDelegate OnGetInheritedSubscriptions { get; set; }

        /// <summary>
        /// Called to configure http configuration.
        /// </summary>
        /// <param name="configuration">The http configuration.</param>
        private void OnConfigure(HttpConfiguration configuration)
        {
            configuration.Routes.MapHttpRoute(
                name: "GetManagementGroupAncestors",
                routeTemplate: "ancestors/{EntityId}",
                defaults: new
                {
                    controller = "ManagementGroupApi",
                    Action = "GetManagementGroupAncestors"
                });

            configuration.Routes.MapHttpRoute(
                name: "GetManagementGroup",
                routeTemplate: "providers/Microsoft.Management/managementGroups/{entityId}",
                defaults: new
                {
                    controller = "ManagementGroupApi",
                    Action = "GetManagementGroup"
                });

            configuration.Routes.MapHttpRoute(
                name: "GetInheritedSubscriptions",
                routeTemplate: "inheritedSubscriptions",
                defaults: new
                {
                    controller = "ManagementGroupApi",
                    Action = "GetInheritedSubscriptions"
                });

            configuration.Services.Replace(typeof(IHttpControllerSelector), new ManagementGroupApiTestHostControllerSelector(this, configuration));
            configuration.DependencyResolver = new ManagementGroupApiTestHostDependencyResolver(this, configuration);
        }

        /// <summary>
        /// The management group API test host server.
        /// </summary>
        public class ManagementGroupApiTestHostServer : ServiceTestHostServer
        {
            /// <summary>
            /// Initializes a new instance of the <see cref="ManagementGroupApiTestHostServer"/> class.
            /// </summary>
            /// <param name="serviceUri">The service URI.</param>
            /// <param name="state">The instance state.</param>
            public ManagementGroupApiTestHostServer(Uri serviceUri, object state)
                : base(serviceUri, state)
            {
            }

            /// <summary>
            /// Called to configure http configuration.
            /// </summary>
            /// <param name="configuration">The http configuration.</param>
            /// <param name="state">The instance state.</param>
            protected override void OnConfigure(HttpConfiguration configuration, object state)
            {
                (state as ManagementGroupApiTestHost).OnConfigure(configuration);
            }
        }

        #region Management group api controller

        /// <summary>
        /// The management group API request controller.
        /// </summary>
        public class ManagementGroupApiController : ApiController
        {
            /// <summary>
            /// The management group API test host.
            /// </summary>
            private readonly ManagementGroupApiTestHost host;

            /// <summary>
            /// Initializes a new instance of the <see cref="ManagementGroupApiController"/> class.
            /// </summary>
            /// <param name="host">The management group API test host.</param>
            public ManagementGroupApiController(ManagementGroupApiTestHost host)
            {
                this.host = host;
            }

            /// <summary>
            /// Responds to get management group ancestors request.
            /// </summary>
            /// <param name="entityId">The entity id.</param>
            [HttpGet]
            [ActionName("GetManagementGroupAncestors")]
            public HttpResponseMessage GetManagementGroupAncestors(string entityId)
            {
                if (this.host.OnGetManagementGroupAncestors != null)
                {
                    return this.host.OnGetManagementGroupAncestors(this.Request, entityId);
                }

                return this.Request.CreateResponse(HttpStatusCode.OK);
            }

            /// <summary>
            /// Responds to get management group request.
            /// </summary>
            /// <param name="entityId">The entity id.</param>
            [HttpGet]
            [ActionName("GetManagementGroup")]
            public HttpResponseMessage GetManagementGroup(string entityId)
            {
                if (this.host.OnGetManagementGroup != null)
                {
                    return this.host.OnGetManagementGroup(this.Request, entityId);
                }

                return this.Request.CreateResponse(HttpStatusCode.OK);
            }

            /// <summary>
            /// Responds to get inherited subscriptions.
            /// </summary>
            [HttpGet]
            [ActionName("GetInheritedSubscriptions")]
            public HttpResponseMessage GetInheritedSubscriptions()
            {
                if (this.host.OnGetInheritedSubscriptions != null)
                {
                    return this.host.OnGetInheritedSubscriptions(this.Request);
                }

                return this.Request.CreateResponse(HttpStatusCode.OK);
            }
        }

        #endregion

        #region Management group API test host controller selector

        /// <summary>
        /// The management group API test host controller selector.
        /// </summary>
        private class ManagementGroupApiTestHostControllerSelector : DefaultHttpControllerSelector
        {
            /// <summary>
            /// The management group API test host.
            /// </summary>
            private readonly ManagementGroupApiTestHost host;

            /// <summary>
            /// The HTTP configuration.
            /// </summary>
            private readonly HttpConfiguration configuration;

            /// <summary>
            /// Initializes a new instance of the <see cref="ManagementGroupApiTestHostControllerSelector" /> class.
            /// </summary>
            /// <param name="host">The host.</param>
            /// <param name="configuration">The configuration.</param>
            public ManagementGroupApiTestHostControllerSelector(ManagementGroupApiTestHost host, HttpConfiguration configuration)
                : base(configuration)
            {
                this.host = host;
                this.configuration = configuration;
            }

            /// <summary>
            /// Selects the controller.
            /// </summary>
            /// <param name="request">The request.</param>
            public override HttpControllerDescriptor SelectController(HttpRequestMessage request)
            {
                var matchedTypes = typeof(ManagementGroupApiTestHost)
                    .GetNestedTypes()
                    .Where(type => typeof(IHttpController).IsAssignableFrom(type))
                    .ToList();

                var controllerName = this.GetControllerName(request);
                var matchedController = matchedTypes.FirstOrDefault(type => type.Name.EqualsInsensitively(controllerName + "controller"));

                return new HttpControllerDescriptor(this.configuration, controllerName, matchedController);
            }
        }

        #endregion

        #region Management group API dependency respolver

        /// <summary>
        /// The management group API test host dependency resolver.
        /// </summary>
        private class ManagementGroupApiTestHostDependencyResolver : IDependencyResolver
        {
            /// <summary>
            /// The management group API test host.
            /// </summary>
            private readonly ManagementGroupApiTestHost host;

            /// <summary>
            /// The HTTP configuration.
            /// </summary>
            private readonly HttpConfiguration configuration;

            /// <summary>
            /// Initializes a new instance of the <see cref="ManagementGroupApiTestHostDependencyResolver" /> class.
            /// </summary>
            /// <param name="host">The host.</param>
            /// <param name="configuration">The configuration.</param>
            public ManagementGroupApiTestHostDependencyResolver(ManagementGroupApiTestHost host, HttpConfiguration configuration)
            {
                this.host = host;
                this.configuration = configuration;
            }

            /// <summary>
            /// Begins the scope.
            /// </summary>
            public IDependencyScope BeginScope()
            {
                return this;
            }

            /// <summary>
            /// Gets the service.
            /// </summary>
            /// <param name="serviceType">Type of the service.</param>
            public object GetService(Type serviceType)
            {
                if (typeof(IHttpController).IsAssignableFrom(serviceType))
                {
                    if (typeof(ManagementGroupApiTestHost).GetNestedTypes().Any(type => type == serviceType))
                    {
                        return Activator.CreateInstance(serviceType, args: new object[] { this.host });
                    }
                }

                return null;
            }

            /// <summary>
            /// Gets the services.
            /// </summary>
            /// <param name="serviceType">Type of the service.</param>
            public IEnumerable<object> GetServices(Type serviceType)
            {
                return Enumerable.Empty<object>();
            }

            /// <summary>
            /// Performs application-defined tasks associated with freeing, releasing, or resetting unmanaged resources.
            /// </summary>
            public void Dispose()
            {
            }
        }

        #endregion
    }
}
