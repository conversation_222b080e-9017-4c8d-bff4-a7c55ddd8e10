﻿//-----------------------------------------------------------
// Copyright (c) Microsoft Corporation.  All rights reserved.
//-----------------------------------------------------------

namespace Internal.WindowsAzure.ResourceStack.Tests.Frontdoor.Common
{
    using System;
    using System.Collections.Generic;
    using System.Linq;
    using System.Net;
    using System.Net.Http;
    using System.Web.Http;
    using System.Web.Http.Controllers;
    using System.Web.Http.Dependencies;
    using System.Web.Http.Dispatcher;
    using Internal.WindowsAzure.ResourceStack.Tests.Common;
    using Microsoft.WindowsAzure.ResourceStack.Common.Extensions;
    using Newtonsoft.Json.Linq;

    /// <summary>
    /// Delegate used for onboarding resource.
    /// </summary>
    /// <param name="request">The incoming request.</param>
    /// <param name="requestBody">The incoming request body.</param>
    public delegate HttpResponseMessage OnboardResourceDelegate(HttpRequestMessage request, JObject requestBody);

    /// <summary>
    /// Delegate used for getting an <c>onboarded</c> resource.
    /// </summary>
    /// <param name="request">The incoming request.</param>
    public delegate HttpResponseMessage OnGetOnboardedResourceDelegate(HttpRequestMessage request);

    /// <summary>
    /// Delegate used for getting governance role definition resource.
    /// </summary>
    /// <param name="request">The incoming request.</param>
    public delegate HttpResponseMessage OnGetGovernanceRoleDefinitionsDelegate(HttpRequestMessage request);

    /// <summary>
    /// Delegate used for creating governance role definition.
    /// </summary>
    /// <param name="request">The incoming request.</param>
    /// <param name="requestBody">The incoming request body.</param>
    public delegate HttpResponseMessage OnCreateGovernanceRoleDefinitionsDelegate(HttpRequestMessage request, JObject requestBody);

    /// <summary>
    /// Delegate used for getting governance role settings.
    /// </summary>
    /// <param name="request">The incoming request.</param>
    /// <param name="resourceName">Name of the resource.</param>
    public delegate HttpResponseMessage OnGetGovernanceRoleSettingsDelegate(HttpRequestMessage request, string resourceName);

    /// <summary>
    /// Delegate used for getting governance role settings.
    /// </summary>
    /// <param name="request">The incoming request.</param>
    /// <param name="roleSettingName">Name of the role setting.</param>
    /// <param name="requestBody">The request body.</param>
    public delegate HttpResponseMessage OnUpdateGovernanceRoleSettingsDelegate(HttpRequestMessage request, string roleSettingName, JObject requestBody);

    /// <summary>
    /// Delegate used for getting governance role assignments.
    /// </summary>
    /// <param name="request">The incoming request.</param>
    public delegate HttpResponseMessage OnGetGovernanceRoleAssignmentsDelegate(HttpRequestMessage request);

    /// <summary>
    /// Delegate used for getting governance role assignments.
    /// </summary>
    /// <param name="request">The incoming request.</param>
    /// <param name="requestBody">The request body.</param>
    public delegate HttpResponseMessage OnCreateGovernanceRoleAssignmentsDelegate(HttpRequestMessage request, JObject requestBody);

    /// <summary>
    /// The web service host for the JIT <c>api</c>.
    /// </summary>
    public class JitApiTestHost : ServiceTestHost<JitApiTestHost.JitApiTestHostServer>, IDisposable
    {
        /// <summary>
        /// Initializes a new instance of the <see cref="JitApiTestHost"/> class.
        /// </summary>
        /// <param name="serviceUri">The service URI.</param>
        public JitApiTestHost(Uri serviceUri)
            : base(serviceUri)
        {
            this.Start();
        }

        /// <summary>
        /// Performs application-defined tasks associated with freeing, releasing, or resetting unmanaged resources.
        /// </summary>
        public void Dispose()
        {
            this.Stop();
        }

        /// <summary>
        /// Restarts this instance.
        /// </summary>
        public void Restart()
        {
            this.Stop();
            this.Start();
        }

        /// <summary>
        /// Gets the state of the test host.
        /// </summary>
        protected override object GetState
        {
            get { return this; }
        }

        /// <summary>
        /// Gets or sets the delegate called when onboard resource request is received.
        /// </summary>
        public OnboardResourceDelegate OnboardResource { get; set; }

        /// <summary>
        /// Gets or sets the delegate called when onboard resource request is received.
        /// </summary>
        public OnGetOnboardedResourceDelegate OnGetOnboardedResource { get; set; }

        /// <summary>
        /// Gets or sets the delegate called when get governance role definition request is received.
        /// </summary>
        public OnGetGovernanceRoleDefinitionsDelegate OnGetGovernanceRoleDefinitions { get; set; }

        /// <summary>
        /// Gets or sets the delegate called when create governance role definition request is received.
        /// </summary>
        public OnCreateGovernanceRoleDefinitionsDelegate OnCreateGovernanceRoleDefinitions { get; set; }

        /// <summary>
        /// Gets or sets the delegate called when get governance role setting request is received.
        /// </summary>
        public OnGetGovernanceRoleSettingsDelegate OnGetGovernanceRoleSettings { get; set; }

        /// <summary>
        /// Gets or sets the delegate called when update governance role setting request is received.
        /// </summary>
        public OnUpdateGovernanceRoleSettingsDelegate OnUpdateGovernanceRoleSettings { get; set; }

        /// <summary>
        /// Gets or sets the delegate called when get governance role assignment request is received.
        /// </summary>
        public OnGetGovernanceRoleAssignmentsDelegate OnGetGovernanceRoleAssignments { get; set; }

        /// <summary>
        /// Gets or sets the delegate called when create governance role assignment request is received.
        /// </summary>
        public OnCreateGovernanceRoleAssignmentsDelegate OnCreateGovernanceRoleAssignments { get; set; }

        /// <summary>
        /// Called to configure http configuration.
        /// </summary>
        /// <param name="configuration">The http configuration.</param>
        private void OnConfigure(HttpConfiguration configuration)
        {
            // Onboard resource
            configuration.Routes.MapHttpRoute(
                name: "GetOnboardedResource",
                routeTemplate: "api/v2/privilegedAccess/azureManagedApplications/resources",
                defaults: new { controller = "JitApi", action = "GetOnboardedResource" },
                constraints: new { HttpMethod = new System.Web.Http.Routing.HttpMethodConstraint(HttpMethod.Get) });

            configuration.Routes.MapHttpRoute(
                name: "OnboardedResource",
                routeTemplate: "api/v2/privilegedAccess/azureManagedApplications/resources",
                defaults: new { controller = "JitApi", action = "OnboardResource" },
                constraints: new { HttpMethod = new System.Web.Http.Routing.HttpMethodConstraint(HttpMethod.Post) });

            // Governance role definitions
            configuration.Routes.MapHttpRoute(
                name: "GetRoleDefinition",
                routeTemplate: "api/v2/privilegedAccess/azureManagedApplications/roleDefinitions",
                defaults: new { controller = "JitApi", action = "GetRoleDefinition" },
                constraints: new { HttpMethod = new System.Web.Http.Routing.HttpMethodConstraint(HttpMethod.Get) });

            configuration.Routes.MapHttpRoute(
                name: "CreateRoleDefinition",
                routeTemplate: "api/v2/privilegedAccess/azureManagedApplications/roleDefinitions",
                defaults: new { controller = "JitApi", action = "CreateRoleDefinition" },
                constraints: new { HttpMethod = new System.Web.Http.Routing.HttpMethodConstraint(HttpMethod.Post) });

            // Governance role settings
            configuration.Routes.MapHttpRoute(
                name: "GetRoleSetting",
                routeTemplate: "api/v2/privilegedAccess/azureManagedApplications/resources/{resourceName}/roleSettings",
                defaults: new { controller = "JitApi", action = "GetRoleSetting" },
                constraints: new { HttpMethod = new System.Web.Http.Routing.HttpMethodConstraint(HttpMethod.Get) });

            configuration.Routes.MapHttpRoute(
                name: "CreateRoleSetting",
                routeTemplate: "api/v2/privilegedAccess/azureManagedApplications/roleSettings/{roleSettingName}",
                defaults: new { controller = "JitApi", action = "CreateRoleSetting" },
                constraints: new { HttpMethod = new System.Web.Http.Routing.HttpMethodConstraint(new HttpMethod("PATCH")) });

            // Governance role assignments
            configuration.Routes.MapHttpRoute(
                name: "GetRoleAssignment",
                routeTemplate: "api/v2/privilegedAccess/azureManagedApplications/roleAssignmentRequests",
                defaults: new { controller = "JitApi", action = "GetRoleAssignment" },
                constraints: new { HttpMethod = new System.Web.Http.Routing.HttpMethodConstraint(HttpMethod.Get) });

            configuration.Routes.MapHttpRoute(
                name: "CreateRoleAssignment",
                routeTemplate: "api/v2/privilegedAccess/azureManagedApplications/roleAssignmentRequests",
                defaults: new { controller = "JitApi", action = "CreateRoleAssignment" },
                constraints: new { HttpMethod = new System.Web.Http.Routing.HttpMethodConstraint(HttpMethod.Post) });

            configuration.Services.Replace(typeof(IHttpControllerSelector), new JitApiTestHostControllerSelector(this, configuration));
            configuration.DependencyResolver = new JitApiTestHostDependencyResolver(this, configuration);
        }

        /// <summary>
        /// The JIT <c>api</c> test host server.
        /// </summary>
        public class JitApiTestHostServer : ServiceTestHostServer
        {
            /// <summary>
            /// Initializes a new instance of the <see cref="JitApiTestHostServer"/> class.
            /// </summary>
            /// <param name="serviceUri">The service URI.</param>
            /// <param name="state">The instance state.</param>
            public JitApiTestHostServer(Uri serviceUri, object state)
                : base(serviceUri, state)
            {
            }

            /// <summary>
            /// Called to configure http configuration.
            /// </summary>
            /// <param name="configuration">The http configuration.</param>
            /// <param name="state">The instance state.</param>
            protected override void OnConfigure(HttpConfiguration configuration, object state)
            {
                (state as JitApiTestHost).OnConfigure(configuration);
            }
        }

        #region Jit api controller

        /// <summary>
        /// The JIT <c>api</c> request controller.
        /// </summary>
        public class JitApiController : ApiController
        {
            /// <summary>
            /// The graph <c>api</c> test host.
            /// </summary>
            private readonly JitApiTestHost host;

            /// <summary>
            /// Initializes a new instance of the <see cref="JitApiController"/> class.
            /// </summary>
            /// <param name="host">The graph <c>api</c> test host.</param>
            public JitApiController(JitApiTestHost host)
            {
                this.host = host;
            }

            #region resource onboarding

            /// <summary>
            /// Responds to get on boarded request.
            /// </summary>
            [HttpGet]
            [ActionName("GetOnboardedResource")]
            public HttpResponseMessage GetOnboardedResource()
            {
                if (this.host.OnGetOnboardedResource != null)
                {
                    return this.host.OnGetOnboardedResource(this.Request);
                }

                return this.Request.CreateResponse(HttpStatusCode.OK);
            }

            /// <summary>
            /// Responds to create on boarded request.
            /// </summary>
            /// <param name="requestBody">The body of the request.</param>
            [HttpPost]
            [ActionName("OnboardResource")]
            public HttpResponseMessage OnboardResource([FromBody] JObject requestBody)
            {
                if (this.host.OnboardResource != null)
                {
                    return this.host.OnboardResource(this.Request, requestBody);
                }

                return this.Request.CreateResponse(HttpStatusCode.OK);
            }

            #endregion

            #region governance role definition

            /// <summary>
            /// Responds to get role definition request.
            /// </summary>
            [HttpGet]
            [ActionName("GetRoleDefinition")]
            public HttpResponseMessage GetGovernanceRoleDefinition()
            {
                if (this.host.OnGetGovernanceRoleDefinitions != null)
                {
                    return this.host.OnGetGovernanceRoleDefinitions(this.Request);
                }

                return this.Request.CreateResponse(HttpStatusCode.OK);
            }

            /// <summary>
            /// Responds to create role definition.
            /// </summary>
            /// <param name="requestBody">The body of the request.</param>
            [HttpPost]
            [ActionName("CreateRoleDefinition")]
            public HttpResponseMessage CreateGovernanceRoleDefinition([FromBody] JObject requestBody)
            {
                if (this.host.OnCreateGovernanceRoleDefinitions != null)
                {
                    return this.host.OnCreateGovernanceRoleDefinitions(this.Request, requestBody);
                }

                return this.Request.CreateResponse(HttpStatusCode.OK);
            }

            #endregion

            #region governance role definition

            /// <summary>
            /// Responds to get role setting.
            /// </summary>
            /// <param name="resourceName">Name of the resource.</param>
            [HttpGet]
            [ActionName("GetRoleSetting")]
            public HttpResponseMessage GetGovernanceRoleSetting(string resourceName)
            {
                if (this.host.OnGetGovernanceRoleSettings != null)
                {
                    return this.host.OnGetGovernanceRoleSettings(this.Request, resourceName);
                }

                return this.Request.CreateResponse(HttpStatusCode.OK);
            }

            /// <summary>
            /// Responds to create role setting.
            /// </summary>
            /// <param name="roleSettingName">Name of the role setting.</param>
            /// <param name="requestBody">The body of the request.</param>
            [HttpPatch]
            [ActionName("CreateRoleSetting")]
            public HttpResponseMessage CreateGovernanceRoleSetting(string roleSettingName, [FromBody] JObject requestBody)
            {
                if (this.host.OnUpdateGovernanceRoleSettings != null)
                {
                    return this.host.OnUpdateGovernanceRoleSettings(this.Request, roleSettingName, requestBody);
                }

                return this.Request.CreateResponse(HttpStatusCode.OK);
            }

            #endregion

            #region governance role assignment

            /// <summary>
            /// Responds to get role assignment.
            /// </summary>
            [HttpGet]
            [ActionName("GetRoleAssignment")]
            public HttpResponseMessage GetGovernanceRoleAssignment()
            {
                if (this.host.OnGetGovernanceRoleAssignments != null)
                {
                    return this.host.OnGetGovernanceRoleAssignments(this.Request);
                }

                return this.Request.CreateResponse(HttpStatusCode.OK);
            }

            /// <summary>
            /// Responds to create role assignment.
            /// </summary>
            /// <param name="requestBody">The body of the request.</param>
            [HttpPost]
            [ActionName("CreateRoleAssignment")]
            public HttpResponseMessage CreateGovernanceRoleAssignment([FromBody] JObject requestBody)
            {
                if (this.host.OnCreateGovernanceRoleAssignments != null)
                {
                    return this.host.OnCreateGovernanceRoleAssignments(this.Request, requestBody);
                }

                return this.Request.CreateResponse(HttpStatusCode.OK);
            }

            #endregion
        }

        #endregion

        #region JitApiTestHostControllerSelector

        /// <summary>
        /// The JIT <c>api</c> test host controller selector.
        /// </summary>
        private class JitApiTestHostControllerSelector : DefaultHttpControllerSelector
        {
            /// <summary>
            /// The JIT <c>api</c> test host.
            /// </summary>
            private readonly JitApiTestHost host;

            /// <summary>
            /// The HTTP configuration.
            /// </summary>
            private readonly HttpConfiguration configuration;

            /// <summary>
            /// Initializes a new instance of the <see cref="JitApiTestHostControllerSelector" /> class.
            /// </summary>
            /// <param name="host">The host.</param>
            /// <param name="configuration">The configuration.</param>
            public JitApiTestHostControllerSelector(JitApiTestHost host, HttpConfiguration configuration)
                : base(configuration)
            {
                this.host = host;
                this.configuration = configuration;
            }

            /// <summary>
            /// Selects the controller.
            /// </summary>
            /// <param name="request">The request.</param>
            public override HttpControllerDescriptor SelectController(HttpRequestMessage request)
            {
                var matchedTypes = typeof(JitApiTestHost)
                    .GetNestedTypes()
                    .Where(type => typeof(IHttpController).IsAssignableFrom(type))
                    .ToList();

                var controllerName = this.GetControllerName(request);
                var matchedController = matchedTypes.FirstOrDefault(type => type.Name.EqualsInsensitively(controllerName + "controller"));

                return new HttpControllerDescriptor(this.configuration, controllerName, matchedController);
            }
        }

        #endregion

        #region Jit api dependency resolver

        /// <summary>
        /// The JIT <c>api</c> test host dependency resolver.
        /// </summary>
        private class JitApiTestHostDependencyResolver : IDependencyResolver
        {
            /// <summary>
            /// The graph <c>api</c> test host.
            /// </summary>
            private readonly JitApiTestHost host;

            /// <summary>
            /// The HTTP configuration.
            /// </summary>
            private readonly HttpConfiguration configuration;

            /// <summary>
            /// Initializes a new instance of the <see cref="JitApiTestHostDependencyResolver" /> class.
            /// </summary>
            /// <param name="host">The host.</param>
            /// <param name="configuration">The configuration.</param>
            public JitApiTestHostDependencyResolver(JitApiTestHost host, HttpConfiguration configuration)
            {
                this.host = host;
                this.configuration = configuration;
            }

            /// <summary>
            /// Begins the scope.
            /// </summary>
            public IDependencyScope BeginScope()
            {
                return this;
            }

            /// <summary>
            /// Gets the service.
            /// </summary>
            /// <param name="serviceType">Type of the service.</param>
            public object GetService(Type serviceType)
            {
                if (typeof(IHttpController).IsAssignableFrom(serviceType))
                {
                    if (typeof(JitApiTestHost).GetNestedTypes().Any(type => type == serviceType))
                    {
                        return Activator.CreateInstance(serviceType, args: new object[] { this.host });
                    }
                }

                return null;
            }

            /// <summary>
            /// Gets the services.
            /// </summary>
            /// <param name="serviceType">Type of the service.</param>
            public IEnumerable<object> GetServices(Type serviceType)
            {
                return Enumerable.Empty<object>();
            }

            /// <summary>
            /// Performs application-defined tasks associated with freeing, releasing, or resetting unmanaged resources.
            /// </summary>
            public void Dispose()
            {
            }
        }

        #endregion
    }
}
