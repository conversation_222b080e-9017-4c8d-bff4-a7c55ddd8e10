﻿//-----------------------------------------------------------
// Copyright (c) Microsoft Corporation.  All rights reserved.
//-----------------------------------------------------------

namespace Internal.WindowsAzure.ResourceStack.Tests.Frontdoor.Common
{
    using System;
    using System.Collections.Generic;
    using System.Linq;
    using System.Net;
    using System.Net.Http;
    using System.Web.Http;
    using System.Web.Http.Controllers;
    using System.Web.Http.Dependencies;
    using System.Web.Http.Dispatcher;
    using Internal.WindowsAzure.ResourceStack.Tests.Common;
    using Microsoft.WindowsAzure.ResourceStack.Common.Extensions;
    using Newtonsoft.Json.Linq;

    /// <summary>
    /// Delegate used for listening to purchase requests.
    /// </summary>
    /// <param name="request">The incoming purchase request.</param>
    /// <param name="requestBody">The incoming request body.</param>
    public delegate HttpResponseMessage MarketplacePurchaseDelegate(HttpRequestMessage request, JObject requestBody);

    /// <summary>
    /// Delegate used for listening to purchase validation requests.
    /// </summary>
    /// <param name="request">The incoming purchase request.</param>
    /// <param name="requestBody">The incoming request body.</param>
    public delegate HttpResponseMessage MarketplaceValidateDelegate(HttpRequestMessage request, JObject requestBody);

    /// <summary>
    /// Delegate used for listening to cancellation requests.
    /// </summary>
    /// <param name="request">The incoming cancel request.</param>
    /// <param name="requestBody">The incoming request body.</param>
    public delegate HttpResponseMessage MarketplaceCancelDelegate(HttpRequestMessage request, JObject requestBody);

    /// <summary>
    /// Delegate used for listening to convert requests.
    /// </summary>
    /// <param name="request">The incoming convert request.</param>
    /// <param name="requestBody">The incoming request body.</param>
    public delegate HttpResponseMessage ConvertRequestDelegate(HttpRequestMessage request, JObject requestBody);

    /// <summary>
    /// Delegate used for listening to get order requests.
    /// </summary>
    /// <param name="request">The incoming get order request.</param>
    /// <param name="requestBody">The incoming request body.</param>
    public delegate HttpResponseMessage GetOrderRequestDelegate(HttpRequestMessage request, JObject requestBody);

    /// <summary>
    /// Delegate used for listening to check eligibility requests.
    /// </summary>
    /// <param name="request">The incoming get order request.</param>
    /// <param name="requestBody">The incoming request body.</param>
    public delegate HttpResponseMessage CheckEligibilityDelegate(HttpRequestMessage request, JObject requestBody);

    /// <summary>
    /// Delegate used for listening to move resource requests.
    /// </summary>
    /// <param name="request">The incoming get order request.</param>
    /// <param name="requestBody">The incoming request body.</param>
    public delegate HttpResponseMessage MoveResourceDelegate(HttpRequestMessage request, JObject requestBody);

    /// <summary>
    /// Delegate used for listening to create benefit requests.
    /// </summary>
    /// <param name="request">The incoming get order request.</param>
    /// <param name="requestBody">The incoming request body.</param>
    public delegate HttpResponseMessage CreateBenefitDelegate(HttpRequestMessage request, JObject requestBody);

    /// <summary>
    /// Delegate used for listening to get benefit requests.
    /// </summary>
    /// <param name="request">The incoming get order request.</param>
    public delegate HttpResponseMessage GetBenefitDelegate(HttpRequestMessage request);

    /// <summary>
    /// Delegate used for listening to delete benefit requests.
    /// </summary>
    /// <param name="request">The incoming get order request.</param>
    /// <param name="marketplaceBenefitId">The market place benefit identifier.</param>
    public delegate HttpResponseMessage DeleteBenefitDelegate(HttpRequestMessage request, string marketplaceBenefitId);

    /// <summary>
    /// The web service host for the mock marketplace service.
    /// </summary>
    public class MarketplaceTestHost : ServiceTestHost<MarketplaceTestHost.MarketplaceTestHostServer>, IDisposable
    {
        /// <summary>
        /// Initializes a new instance of the <see cref="MarketplaceTestHost"/> class.
        /// </summary>
        /// <param name="serviceUri">The service URI.</param>
        public MarketplaceTestHost(Uri serviceUri)
            : base(serviceUri)
        {
            this.Start();
        }

        /// <summary>
        /// Initializes a new instance of the <see cref="MarketplaceTestHost" /> class.
        /// </summary>
        /// <param name="serviceUri">The service URI.</param>
        /// <param name="assemblyConfigurationPath">The assembly configuration path.</param>
        public MarketplaceTestHost(Uri serviceUri, string assemblyConfigurationPath)
            : base(serviceUri, "MarketplaceTestHost", assemblyConfigurationPath)
        {
            this.Start();
        }

        /// <summary>
        /// Performs application-defined tasks associated with freeing, releasing, or resetting unmanaged resources.
        /// </summary>
        public void Dispose()
        {
            this.Stop();
        }

        /// <summary>
        /// Gets the state of the test host.
        /// </summary>
        protected override object GetState
        {
            get { return this; }
        }

        /// <summary>
        /// Gets or sets the delegate called when purchase request is received.
        /// </summary>
        public MarketplacePurchaseDelegate OnPurchaseRequest { get; set; }

        /// <summary>
        /// Gets or sets the delegate called when validation request is received.
        /// </summary>
        public MarketplaceValidateDelegate OnValidateRequest { get; set; }

        /// <summary>
        /// Gets or sets the delegate called when cancellation request is received.
        /// </summary>
        public MarketplaceCancelDelegate OnCancelRequest { get; set; }

        /// <summary>
        /// Gets or sets the delegate called when convert request is received.
        /// </summary>
        public ConvertRequestDelegate OnConvertRequest { get; set; }

        /// <summary>
        /// Gets or sets the delegate called when get order request is received.
        /// </summary>
        public GetOrderRequestDelegate OnGetOrderRequest { get; set; }

        /// <summary>
        /// Gets or sets the delegate called when get order request is received.
        /// </summary>
        public CheckEligibilityDelegate OnEligibilityRequest { get; set; }

        /// <summary>
        /// Gets or sets the delegate called when move resource request is received.
        /// </summary>
        public MoveResourceDelegate OnMoveResourceRequest { get; set; }

        /// <summary>
        /// Gets or sets the delegate called when create benefit request is received.
        /// </summary>
        public CreateBenefitDelegate OnCreateBenefitRequest { get; set; }

        /// <summary>
        /// Gets or sets the delegate called when get benefit request is received.
        /// </summary>
        public GetBenefitDelegate OnGetBenefitRequest { get; set; }

        /// <summary>
        /// Gets or sets the delegate called when delete benefit request is received.
        /// </summary>
        public DeleteBenefitDelegate OnDeleteBenefitRequest { get; set; }

        /// <summary>
        /// Called to configure http configuration.
        /// </summary>
        /// <param name="configuration">The http configuration.</param>
        private void OnConfigure(HttpConfiguration configuration)
        {
            configuration.Routes.MapHttpRoute(
               name: "PurchaseRequest",
               routeTemplate: "orders/purchase",
               defaults: new { controller = "Marketplace", action = "Purchase" });

            configuration.Routes.MapHttpRoute(
               name: "ValidateRequest",
               routeTemplate: "orders/validatePurchase",
               defaults: new { controller = "Marketplace", action = "Validate" });

            configuration.Routes.MapHttpRoute(
              name: "CancelRequest",
              routeTemplate: "orders/cancel",
              defaults: new { controller = "Marketplace", action = "Cancel" });

            configuration.Routes.MapHttpRoute(
              name: "ConvertRequest",
              routeTemplate: "orders/Convert",
              defaults: new { controller = "Marketplace", action = "Convert" });

            configuration.Routes.MapHttpRoute(
              name: "GetOrderRequest",
              routeTemplate: "orders/GetOrder",
              defaults: new { controller = "Marketplace", action = "GetOrder" });

            configuration.Routes.MapHttpRoute(
              name: "eligibilityRequest",
              routeTemplate: "checkpurchaseeligibility",
              defaults: new { controller = "Marketplace", action = "CheckPurchaseEligibility" });

            configuration.Routes.MapHttpRoute(
              name: "MoveResourceRequest",
              routeTemplate: "orders/moveresource",
              defaults: new { controller = "Marketplace", action = "MoveResource" });

            configuration.Routes.MapHttpRoute(
               name: "GetOrCreateBenefit",
               routeTemplate: "api/managedapplications/benefitruntimes",
               defaults: new { controller = "Marketplace" },
               constraints: new { HttpMethod = new System.Web.Http.Routing.HttpMethodConstraint(HttpMethod.Put, HttpMethod.Get) });

            configuration.Routes.MapHttpRoute(
               name: "DeleteBenefit",
               routeTemplate: "api/managedapplications/benefitruntimes/{marketplaceBenefitId}",
               defaults: new { controller = "Marketplace" },
               constraints: new { HttpMethod = new System.Web.Http.Routing.HttpMethodConstraint(HttpMethod.Delete) });

            configuration.Services.Replace(typeof(IHttpControllerSelector), new MarketplaceTestHostControllerSelector(this, configuration));
            configuration.DependencyResolver = new MarketplaceTestHostDependencyResolver(this, configuration);
        }

        /// <summary>
        /// The marketplace purchase request controller.
        /// </summary>
        public class MarketplaceController : ApiController
        {
            /// <summary>
            /// The marketplace test host.
            /// </summary>
            private readonly MarketplaceTestHost host;

            /// <summary>
            /// Initializes a new instance of the <see cref="MarketplaceController"/> class.
            /// </summary>
            /// <param name="host">The marketplace test host.</param>
            public MarketplaceController(MarketplaceTestHost host)
            {
                this.host = host;
            }

            /// <summary>
            /// Responds to a check purchase eligibility request.
            /// </summary>
            /// <param name="requestBody">Body of the request.</param>
            [HttpPost]
            public HttpResponseMessage CheckPurchaseEligibility([FromBody] JObject requestBody)
            {
                if (this.host.OnEligibilityRequest != null)
                {
                    return this.host.OnEligibilityRequest(this.Request, requestBody);
                }

                return this.Request.CreateResponse(HttpStatusCode.OK);
            }

            /// <summary>
            /// Responds to a purchase request.
            /// </summary>
            /// <param name="requestBody">Body of the request.</param>
            [HttpPost]
            public HttpResponseMessage Purchase([FromBody] JObject requestBody)
            {
                if (this.host.OnPurchaseRequest != null)
                {
                    return this.host.OnPurchaseRequest(this.Request, requestBody);
                }

                return this.Request.CreateResponse(HttpStatusCode.OK);
            }

            /// <summary>
            /// Responds to a validation request.
            /// </summary>
            /// <param name="requestBody">Body of the request.</param>
            [HttpPost]
            public HttpResponseMessage Validate([FromBody] JObject requestBody)
            {
                if (this.host.OnValidateRequest != null)
                {
                    return this.host.OnValidateRequest(this.Request, requestBody);
                }

                return this.Request.CreateResponse(HttpStatusCode.OK);
            }

            /// <summary>
            /// Responds to a cancellation request.
            /// </summary>
            /// <param name="requestBody">Body of the request.</param>
            [HttpPost]
            public HttpResponseMessage Cancel([FromBody] JObject requestBody)
            {
                if (this.host.OnCancelRequest != null)
                {
                    return this.host.OnCancelRequest(this.Request, requestBody);
                }

                return this.Request.CreateResponse(HttpStatusCode.OK);
            }

            /// <summary>
            /// Responds to a convert request.
            /// </summary>
            /// <param name="requestBody">Body of the request.</param>
            [HttpPost]
            public HttpResponseMessage Convert([FromBody] JObject requestBody)
            {
                if (this.host.OnConvertRequest != null)
                {
                    return this.host.OnConvertRequest(this.Request, requestBody);
                }

                return this.Request.CreateResponse(HttpStatusCode.OK);
            }

            /// <summary>
            /// Responds to a get order request.
            /// </summary>
            /// <param name="requestBody">Body of the request.</param>
            [HttpPost]
            public HttpResponseMessage GetOrder([FromBody] JObject requestBody)
            {
                if (this.host.OnGetOrderRequest != null)
                {
                    return this.host.OnGetOrderRequest(this.Request, requestBody);
                }

                return this.Request.CreateResponse(HttpStatusCode.OK);
            }

            /// <summary>
            /// Responds to move resource request.
            /// </summary>
            /// <param name="requestBody">Body of the request.</param>
            [HttpPost]
            public HttpResponseMessage MoveResource([FromBody] JObject requestBody)
            {
                if (this.host.OnMoveResourceRequest != null)
                {
                    return this.host.OnMoveResourceRequest(this.Request, requestBody);
                }

                return this.Request.CreateResponse(HttpStatusCode.OK);
            }

            /// <summary>
            /// Responds to create benefit request.
            /// </summary>
            /// <param name="requestBody">Body of the request.</param>
            [HttpPut]
            public HttpResponseMessage CreateBenefit([FromBody] JObject requestBody)
            {
                if (this.host.OnCreateBenefitRequest != null)
                {
                    return this.host.OnCreateBenefitRequest(this.Request, requestBody);
                }

                return this.Request.CreateResponse(HttpStatusCode.OK);
            }

            /// <summary>
            /// Responds to get benefit request.
            /// </summary>
            [HttpGet]
            public HttpResponseMessage GetBenefit()
            {
                if (this.host.OnGetBenefitRequest != null)
                {
                    return this.host.OnGetBenefitRequest(this.Request);
                }

                return this.Request.CreateResponse(HttpStatusCode.OK);
            }

            /// <summary>
            /// Responds to delete benefit request.
            /// </summary>
            /// <param name="marketplaceBenefitId">The market place benefit identifier.</param>
            [HttpDelete]
            public HttpResponseMessage DeleteBenefit(string marketplaceBenefitId)
            {
                if (this.host.OnDeleteBenefitRequest != null)
                {
                    return this.host.OnDeleteBenefitRequest(this.Request, marketplaceBenefitId);
                }

                return this.Request.CreateResponse(HttpStatusCode.OK);
            }
        }

        /// <summary>
        /// The marketplace test host server.
        /// </summary>
        public class MarketplaceTestHostServer : ServiceTestHostServer
        {
            /// <summary>
            /// Initializes a new instance of the <see cref="MarketplaceTestHostServer"/> class.
            /// </summary>
            /// <param name="serviceUri">The service URI.</param>
            /// <param name="state">The instance state.</param>
            public MarketplaceTestHostServer(Uri serviceUri, object state)
                : base(serviceUri, state)
            {
            }

            /// <summary>
            /// Called to configure http configuration.
            /// </summary>
            /// <param name="configuration">The http configuration.</param>
            /// <param name="state">The instance state.</param>
            protected override void OnConfigure(HttpConfiguration configuration, object state)
            {
                (state as MarketplaceTestHost).OnConfigure(configuration);
            }
        }

        /// <summary>
        /// The marketplace test host controller selector.
        /// </summary>
        private class MarketplaceTestHostControllerSelector : DefaultHttpControllerSelector
        {
            /// <summary>
            /// The marketplace test host.
            /// </summary>
            private readonly MarketplaceTestHost host;

            /// <summary>
            /// The HTTP configuration.
            /// </summary>
            private readonly HttpConfiguration configuration;

            /// <summary>
            /// Initializes a new instance of the <see cref="MarketplaceTestHostControllerSelector" /> class.
            /// </summary>
            /// <param name="host">The host.</param>
            /// <param name="configuration">The configuration.</param>
            public MarketplaceTestHostControllerSelector(MarketplaceTestHost host, HttpConfiguration configuration)
                : base(configuration)
            {
                this.host = host;
                this.configuration = configuration;
            }

            /// <summary>
            /// Selects the controller.
            /// </summary>
            /// <param name="request">The request.</param>
            public override HttpControllerDescriptor SelectController(HttpRequestMessage request)
            {
                var matchedTypes = typeof(MarketplaceTestHost)
                    .GetNestedTypes()
                    .Where(type => typeof(IHttpController).IsAssignableFrom(type))
                    .ToList();

                var controllerName = this.GetControllerName(request);
                var matchedController = matchedTypes.FirstOrDefault(type => type.Name.EqualsInsensitively(controllerName + "controller"));

                return new HttpControllerDescriptor(this.configuration, controllerName, matchedController);
            }
        }

        /// <summary>
        /// The marketplace test host dependency resolver.
        /// </summary>
        private class MarketplaceTestHostDependencyResolver : IDependencyResolver
        {
            /// <summary>
            /// The marketplace test host.
            /// </summary>
            private readonly MarketplaceTestHost host;

            /// <summary>
            /// The HTTP configuration.
            /// </summary>
            private readonly HttpConfiguration configuration;

            /// <summary>
            /// Initializes a new instance of the <see cref="MarketplaceTestHostDependencyResolver" /> class.
            /// </summary>
            /// <param name="host">The host.</param>
            /// <param name="configuration">The configuration.</param>
            public MarketplaceTestHostDependencyResolver(MarketplaceTestHost host, HttpConfiguration configuration)
            {
                this.host = host;
                this.configuration = configuration;
            }

            /// <summary>
            /// Begins the scope.
            /// </summary>
            public IDependencyScope BeginScope()
            {
                return this;
            }

            /// <summary>
            /// Gets the service.
            /// </summary>
            /// <param name="serviceType">Type of the service.</param>
            public object GetService(Type serviceType)
            {
                if (typeof(IHttpController).IsAssignableFrom(serviceType))
                {
                    if (typeof(MarketplaceTestHost).GetNestedTypes().Any(type => type == serviceType))
                    {
                        return Activator.CreateInstance(serviceType, args: new object[] { this.host });
                    }
                }

                return null;
            }

            /// <summary>
            /// Gets the services.
            /// </summary>
            /// <param name="serviceType">Type of the service.</param>
            public IEnumerable<object> GetServices(Type serviceType)
            {
                return Enumerable.Empty<object>();
            }

            /// <summary>
            /// Performs application-defined tasks associated with freeing, releasing, or resetting unmanaged resources.
            /// </summary>
            public void Dispose()
            {
            }
        }
    }
}
