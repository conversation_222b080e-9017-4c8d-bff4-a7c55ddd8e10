﻿//-----------------------------------------------------------
// Copyright (c) Microsoft Corporation.  All rights reserved.
//-----------------------------------------------------------

namespace Internal.WindowsAzure.ResourceStack.Tests.Frontdoor.Common
{
    using System;
    using System.Collections.Generic;
    using System.Diagnostics;
    using System.Linq;
    using System.Net;
    using System.Net.Http;
    using System.Threading;
    using System.Threading.Tasks;
    using System.Web.Http;
    using System.Web.Http.Controllers;
    using System.Web.Http.Dependencies;
    using System.Web.Http.Dispatcher;
    using System.Web.Http.Filters;
    using System.Web.Http.Routing;
    using Internal.WindowsAzure.ResourceStack.Tests.Common;
    using Microsoft.WindowsAzure.ResourceStack.Common.Extensions;

    /// <summary>
    /// Delegate used for listening to incoming requests.
    /// </summary>
    /// <param name="request">The incoming request delegate request.</param>
    /// <param name="requestBody">The incoming request body.</param>
    public delegate HttpResponseMessage IncomingRequestDelegate(HttpRequestMessage request, string requestBody);

    /// <summary>
    /// Delegate used for get disk requests.
    /// </summary>
    /// <param name="request">The incoming request delegate request.</param>
    /// <param name="diskName">The disk name.</param>
    public delegate HttpResponseMessage IncomingGetDiskRequestDelegate(HttpRequestMessage request, string diskName);

    /// <summary>
    /// Delegate used for listening to subscription operations requests.
    /// </summary>
    /// <param name="request">The incoming request delegate request.</param>
    /// <param name="subscriptionId">The subscription id.</param>
    /// <param name="startTime">The start time query string.</param>
    /// <param name="endTime">The end time query string.</param>
    /// <param name="objectIdFilter">The object id filter query string.</param>
    /// <param name="operationResultFilter">The operation result filter query string.</param>
    /// <param name="continuationToken">The continuation token query string.</param>
    public delegate HttpResponseMessage SubscriptionOperationsDelegate(
        HttpRequestMessage request,
        string subscriptionId,
        string startTime,
        string endTime,
        string objectIdFilter,
        string operationResultFilter,
        string continuationToken);

    /// <summary>
    /// Delegate used for listening to cross connection peering requests.
    /// </summary>
    /// <param name="request">The incoming request delegate request.</param>
    /// <param name="subscriptionId">The subscription id.</param>
    /// <param name="crossConnectionName">The cross connection name.</param>
    /// <param name="peeringType">The BGP peering type.</param>
    public delegate HttpResponseMessage BgpPeeringDelegate(
        HttpRequestMessage request,
        string subscriptionId,
        string crossConnectionName,
        string peeringType);

    /// <summary>
    /// Delegate used to get Data disk
    /// </summary>
    /// <param name="request">The request.</param>
    /// <param name="requestBody">The request body.</param>
    /// <param name="lun">The LUN.</param>
    public delegate HttpResponseMessage IncomingGetDataDiskRequestDelegate(HttpRequestMessage request, string requestBody, int lun);

    /// <summary>
    /// The web service host for the mock legacy cloud service test host.
    /// </summary>
    public class LegacyManagementApiTestHost : ServiceTestHost<LegacyManagementApiTestHost.LegacyCloudServiceTestHostServer>, IDisposable
    {
        /// <summary>
        /// Initializes a new instance of the <see cref="LegacyManagementApiTestHost"/> class.
        /// </summary>
        /// <param name="serviceUri">The service URI.</param>
        public LegacyManagementApiTestHost(Uri serviceUri)
            : base(serviceUri)
        {
            this.Start();
        }

        /// <summary>
        /// Initializes a new instance of the <see cref="LegacyManagementApiTestHost" /> class.
        /// </summary>
        /// <param name="serviceUri">The service URI.</param>
        /// <param name="assemblyConfigurationPath">The assembly configuration path.</param>
        public LegacyManagementApiTestHost(Uri serviceUri, string assemblyConfigurationPath)
            : base(serviceUri, "LegacyManagementApiTestHost", assemblyConfigurationPath)
        {
            this.Start();
        }

        /// <summary>
        /// Performs application-defined tasks associated with freeing, releasing, or resetting unmanaged resources.
        /// </summary>
        public void Dispose()
        {
            this.Stop();
        }

        /// <summary>
        /// Gets the state of the test host.
        /// </summary>
        protected override object GetState
        {
            get { return this; }
        }

        /// <summary>
        /// Gets or sets the delegate called when GetCloudServiceWithDetails request is received.
        /// </summary>
        public IncomingRequestDelegate OnGetCloudServiceWithDetailsRequest { get; set; }

        /// <summary>
        /// Gets or sets the delegate called when PutCloudService request is received.
        /// </summary>
        public IncomingRequestDelegate OnPutCloudServiceRequest { get; set; }

        /// <summary>
        /// Gets or sets the delegate called when PutResource request is received.
        /// </summary>
        public IncomingRequestDelegate OnPutResourceRequest { get; set; }

        /// <summary>
        /// Gets or sets the delegate called when DeleteCloudService request is received.
        /// </summary>
        public IncomingRequestDelegate OnDeleteCloudServiceRequest { get; set; }

        /// <summary>
        /// Gets or sets the delegate called when DeleteResource request is received.
        /// </summary>
        public IncomingRequestDelegate OnDeleteResourceRequest { get; set; }

        /// <summary>
        /// Gets or sets the delegate called when GetSubscription request is received.
        /// </summary>
        public IncomingRequestDelegate OnGetSubscriptionRequest { get; set; }

        /// <summary>
        /// Gets or sets the delegate called when GetSubscriptionWithDetails request is received.
        /// </summary>
        public IncomingRequestDelegate OnGetSubscriptionWithDetailsRequest { get; set; }

        /// <summary>
        /// Gets or sets the delegate called when ListSubscriptions request is received.
        /// </summary>
        public IncomingRequestDelegate OnListSubscriptionsRequest { get; set; }

        /// <summary>
        /// Gets or sets the delegate called when MoveSubscriptionResources request is received.
        /// </summary>
        public IncomingRequestDelegate OnMoveSubscriptionResourcesRequest { get; set; }

        /// <summary>
        /// Gets or sets the delegate called when GetValidateSubscriptionMoveStatus request is received.
        /// </summary>
        public IncomingRequestDelegate OnGetSubscriptionTransferStatusRequest { get; set; }

        /// <summary>
        /// Gets or sets the delegate called when GetSubscriptionPrincipals request is received.
        /// </summary>
        public IncomingRequestDelegate OnGetSubscriptionPrincipalsRequest { get; set; }

        /// <summary>
        /// Gets or sets the delegate called when PutSubscriptionPrincipal request is received.
        /// </summary>
        public IncomingRequestDelegate OnPutSubscriptionPrincipalRequest { get; set; }

        /// <summary>
        /// Gets or sets the delegate called when GetSubscriptionPrincipal request is received.
        /// </summary>
        public IncomingRequestDelegate OnGetSubscriptionPrincipalRequest { get; set; }

        /// <summary>
        /// Gets or sets the delegate called when DeleteSubscriptionPrincipal request is received.
        /// </summary>
        public IncomingRequestDelegate OnDeleteSubscriptionPrincipalRequest { get; set; }

        /// <summary>
        /// Gets or sets the delegate called when List Role Sizes request is received.
        /// </summary>
        public IncomingRequestDelegate OnListRoleSizesRequest { get; set; }

        /// <summary>
        /// Gets or sets the delegate called when ListStorageServices request is received.
        /// </summary>
        public IncomingRequestDelegate OnListStorageServicesRequest { get; set; }

        /// <summary>
        /// Gets or sets the delegate called when CreateStorageAccount request is received.
        /// </summary>
        public IncomingRequestDelegate OnCreateStorageServiceRequest { get; set; }

        /// <summary>
        /// Gets or sets the delegate called when UpdateStorageAccount request is received.
        /// </summary>
        public IncomingRequestDelegate OnUpdateStorageServiceRequest { get; set; }

        /// <summary>
        /// Gets or sets the delegate called when GetStorageService request is received.
        /// </summary>
        public IncomingRequestDelegate OnGetStorageServiceRequest { get; set; }

        /// <summary>
        /// Gets or sets the delegate called when DeleteStorageService request is received.
        /// </summary>
        public IncomingRequestDelegate OnDeleteStorageServiceRequest { get; set; }

        /// <summary>
        /// Gets or sets the delegate called when GetStorageKeys request is received.
        /// </summary>
        public IncomingRequestDelegate OnGetStorageKeysRequest { get; set; }

        /// <summary>
        /// Gets or sets the delegate called when RegenerateStorageServiceKeys request is received.
        /// </summary>
        public IncomingRequestDelegate OnRegenerateStorageServiceKeysRequest { get; set; }

        /// <summary>
        /// Gets or sets the delegate called when ListDisks request is received.
        /// </summary>
        public IncomingRequestDelegate OnListDisksRequest { get; set; }

        /// <summary>
        /// Gets or sets the delegate called when GetDisk request is received.
        /// </summary>
        public IncomingGetDiskRequestDelegate OnGetDiskRequest { get; set; }

        /// <summary>
        /// Gets or sets the delegate called when delete disk request is received.
        /// </summary>
        public IncomingRequestDelegate OnDeleteDiskRequest { get; set; }

        /// <summary>
        /// Gets or sets the delegate called when AddDisk request is received.
        /// </summary>
        public IncomingGetDiskRequestDelegate OnAddDiskRequest { get; set; }

        /// <summary>
        /// Gets or sets the delegate called when UpdateDisk request is received.
        /// </summary>
        public IncomingGetDiskRequestDelegate OnUpdateDiskRequest { get; set; }

        /// <summary>
        /// Gets or sets the delegate called when ListHostedService request is received.
        /// </summary>
        public IncomingRequestDelegate OnListHostedServicesRequest { get; set; }

        /// <summary>
        /// Gets or sets the delegate called when ListHostedServicesWithDetails request is received.
        /// </summary>
        public IncomingRequestDelegate OnListHostedServicesWithDetailsRequest { get; set; }

        /// <summary>
        /// Gets or sets the delegate called when GetHostedService request is received.
        /// </summary>
        public IncomingRequestDelegate OnGetHostedServiceRequest { get; set; }

        /// <summary>
        /// Gets or sets the delegate called when the DeleteHostedService request is received.
        /// </summary>
        public IncomingRequestDelegate OnDeleteHostedServiceRequest { get; set; }

        /// <summary>
        /// Gets or sets the delegate called when CreateHostedService request is received.
        /// </summary>
        public IncomingRequestDelegate OnCreateHostedServiceRequest { get; set; }

        /// <summary>
        /// Gets or sets the delegate called when UpdateHostedService request is received.
        /// </summary>
        public IncomingRequestDelegate OnUpdateHostedServiceRequest { get; set; }

        /// <summary>
        /// Gets or sets the delegate called when GetHostedServiceWithDetails request is received.
        /// </summary>
        public IncomingRequestDelegate OnGetHostedServiceWithDetailsRequest { get; set; }

        /// <summary>
        /// Gets or sets the delegate called when GetHostedServiceDeploymentBySlot request is received.
        /// </summary>
        public IncomingRequestDelegate OnGetHostedServiceDeploymentBySlotRequest { get; set; }

        /// <summary>
        /// Gets or sets the delegate called when CreateHostedServiceDeploymentBySlot request is received.
        /// </summary>
        public IncomingRequestDelegate OnCreateHostedServiceDeploymentBySlot { get; set; }

        /// <summary>
        /// Gets or sets the delegate called when DeleteHostedServiceDeploymentBySlot request is received.
        /// </summary>
        public IncomingRequestDelegate OnDeleteHostedServiceDeploymentBySlot { get; set; }

        /// <summary>
        /// Gets or sets the delegate called when UpdateHostedServiceDeploymentBySlot request is received.
        /// </summary>
        public IncomingRequestDelegate OnUpdateHostedServiceDeploymentBySlot { get; set; }

        /// <summary>
        /// Gets or sets the delegate called when UpgradeHostedServiceDeploymentBySlot request is received.
        /// </summary>
        public IncomingRequestDelegate OnUpgradeHostedSerivceDeploymentBySlot { get; set; }

        /// <summary>
        /// Gets or sets the delegate called when ChangeConfigurationBySlot request is received.
        /// </summary>
        public IncomingRequestDelegate OnChangeConfigurationBySlot { get; set; }

        /// <summary>
        /// Gets or sets the delegate called when SwapHostedServiceDeployment request is received.
        /// </summary>
        public IncomingRequestDelegate OnSwapHostedServiceDeployment { get; set; }

        /// <summary>
        /// Gets or sets the delegate called when AddDataDisk request is received.
        /// </summary>
        public IncomingRequestDelegate OnAddHostedServiceDataDiskRequest { get; set; }

        /// <summary>
        /// Gets or sets the delegate called when UpdateDataDisk request is received.
        /// </summary>
        public IncomingRequestDelegate OnUpdateHostedServiceDataDiskRequest { get; set; }

        /// <summary>
        /// Gets or sets the delegate called when GetDataDisk request is received.
        /// </summary>
        public IncomingGetDataDiskRequestDelegate OnGetHostedServiceDataDiskRequest { get; set; }

        /// <summary>
        /// Gets or sets the delegate called when DeleteDataDisk request is received.
        /// </summary>
        public IncomingRequestDelegate OnDeleteHostedServiceDataDiskRequest { get; set; }

        /// <summary>
        /// Gets or sets the delegate called when ListCertificates request is received.
        /// </summary>
        public IncomingRequestDelegate OnListCertificatesRequest { get; set; }

        /// <summary>
        /// Gets or sets the delegate called when GetCertificate request is received.
        /// </summary>
        public IncomingRequestDelegate OnGetCertificateRequest { get; set; }

        /// <summary>
        /// Gets or sets the delegate called when AddCertificate request is received.
        /// </summary>
        public IncomingRequestDelegate OnAddCertificateRequest { get; set; }

        /// <summary>
        /// Gets or sets the delegate called when DeleteCertificate request is received.
        /// </summary>
        public IncomingRequestDelegate OnDeleteCertificateRequest { get; set; }

        /// <summary>
        /// Gets or sets the delegate called when ListExtensions request is received.
        /// </summary>
        public IncomingRequestDelegate OnListExtensionsRequest { get; set; }

        /// <summary>
        /// Gets or sets the delegate called when GetExtension request is received.
        /// </summary>
        public IncomingRequestDelegate OnGetExtensionRequest { get; set; }

        /// <summary>
        /// Gets or sets the delegate called when AddExtension request is received.
        /// </summary>
        public IncomingRequestDelegate OnAddExtensionRequest { get; set; }

        /// <summary>
        /// Gets or sets the delegate called when DeleteExtension request is received.
        /// </summary>
        public IncomingRequestDelegate OnDeleteExtensionRequest { get; set; }

        /// <summary>
        /// Gets or sets the delegate called when GetOperationStatus request is received.
        /// </summary>
        public IncomingRequestDelegate OnGetOperationStatusRequest { get; set; }

        /// <summary>
        /// Gets or sets the delegate called when OnSetNetworkConfigurationRequest request is received.
        /// </summary>
        public IncomingRequestDelegate OnSetNetworkConfigurationRequest { get; set; }

        /// <summary>
        /// Gets or sets the delegate called when OnGetNetworkConfigurationRequest request is received.
        /// </summary>
        public IncomingRequestDelegate OnGetNetworkConfigurationRequest { get; set; }

        /// <summary>
        /// Gets or sets the delegate called when OnListAffinityGroupsRequest request is received.
        /// </summary>
        public IncomingRequestDelegate OnListAffinityGroupsRequest { get; set; }

        /// <summary>
        /// Gets or sets the delegate called when OnGetAffinityGroupRequest request is received.
        /// </summary>
        public IncomingRequestDelegate OnGetAffinityGroupRequest { get; set; }

        /// <summary>
        /// Gets or sets the delegate called when OnListLocationsRequest request is received.
        /// </summary>
        public IncomingRequestDelegate OnListLocationsRequest { get; set; }

        /// <summary>
        /// Gets or sets the delegate called when ListVirtualNetworkSites request is received.
        /// </summary>
        public IncomingRequestDelegate OnListVirtualNetworkSites { get; set; }

        /// <summary>
        /// Gets or sets the delegate called when GetGatewayOperation request is received.
        /// </summary>
        public IncomingRequestDelegate OnCheckIpAddressOperation { get; set; }

        /// <summary>
        /// Gets or sets the delegate called when ValidateDeploymentMigration request is received.
        /// </summary>
        public IncomingRequestDelegate OnValidateDeploymentMigration { get; set; }

        /// <summary>
        /// Gets or sets the delegate called when PrepareDeploymentMigration request is received.
        /// </summary>
        public IncomingRequestDelegate OnPrepareDeploymentMigration { get; set; }

        /// <summary>
        /// Gets or sets the delegate called when CommitDeploymentMigration request is received.
        /// </summary>
        public IncomingRequestDelegate OnCommitDeploymentMigration { get; set; }

        /// <summary>
        /// Gets or sets the delegate called when AbortDeploymentMigration request is received.
        /// </summary>
        public IncomingRequestDelegate OnAbortDeploymentMigration { get; set; }

        /// <summary>
        /// Gets or sets the delegate called when ValidateStorageServiceMigration request is received.
        /// </summary>
        public IncomingRequestDelegate OnValidateStorageServiceMigration { get; set; }

        /// <summary>
        /// Gets or sets the delegate called when PrepareStorageServiceMigration request is received.
        /// </summary>
        public IncomingRequestDelegate OnPrepareStorageServiceMigration { get; set; }

        /// <summary>
        /// Gets or sets the delegate called when CommitStorageServiceMigration request is received.
        /// </summary>
        public IncomingRequestDelegate OnCommitStorageServiceMigration { get; set; }

        /// <summary>
        /// Gets or sets the delegate called when AbortStorageServiceMigration request is received.
        /// </summary>
        public IncomingRequestDelegate OnAbortStorageServiceMigration { get; set; }

        /// <summary>
        /// Gets or sets the delegate called when ValidateVirtualNetworkMigration request is received.
        /// </summary>
        public IncomingRequestDelegate OnValidateVirtualNetworkMigration { get; set; }

        /// <summary>
        /// Gets or sets the delegate called when PrepareVirtualNetworkMigration request is received.
        /// </summary>
        public IncomingRequestDelegate OnPrepareVirtualNetworkMigration { get; set; }

        /// <summary>
        /// Gets or sets the delegate called when CommitVirtualNetworkMigration request is received.
        /// </summary>
        public IncomingRequestDelegate OnCommitVirtualNetworkMigration { get; set; }

        /// <summary>
        /// Gets or sets the delegate called when AbortVirtualNetworkMigration request is received.
        /// </summary>
        public IncomingRequestDelegate OnAbortVirtualNetworkMigration { get; set; }

        /// <summary>
        /// Gets or sets the delegate called when GetGateway request is received.
        /// </summary>
        public IncomingRequestDelegate OnGetGateway { get; set; }

        /// <summary>
        /// Gets or sets the delegate called when CreateGateway request is received.
        /// </summary>
        public IncomingRequestDelegate OnCreateGateway { get; set; }

        /// <summary>
        /// Gets or sets the delegate called when DeleteGateway request is received.
        /// </summary>
        public IncomingRequestDelegate OnDeleteGateway { get; set; }

        /// <summary>
        /// Gets or sets the delegate called when generate VPN client package request is received.
        /// </summary>
        public IncomingRequestDelegate OnGenerateVpnClientPackage { get; set; }

        /// <summary>
        /// Gets or sets the delegate called when GetGatewayOperation request is received.
        /// </summary>
        public IncomingRequestDelegate OnGetGatewayOperation { get; set; }

        /// <summary>
        /// Gets or sets the delegate called when UpdateGatewayDiagnostics request is received.
        /// </summary>
        public IncomingRequestDelegate OnUpdateGatewayDiagnostics { get; set; }

        /// <summary>
        /// Gets or sets the delegate called when GetGatewayDiagnostics request is received.
        /// </summary>
        public IncomingRequestDelegate OnGetGatewayDiagnostics { get; set; }

        /// <summary>
        /// Gets or sets the delegate called when UploadClientRootCertificate request is received.
        /// </summary>
        public IncomingRequestDelegate OnUploadClientRootCertificate { get; set; }

        /// <summary>
        /// Gets or sets the delegate called when DeleteClientRootCertificate request is received.
        /// </summary>
        public IncomingRequestDelegate OnDeleteClientRootCertificate { get; set; }

        /// <summary>
        /// Gets or sets the delegate called when ListClientRootCertificates request is received.
        /// </summary>
        public IncomingRequestDelegate OnListClientRootCertificates { get; set; }

        /// <summary>
        /// Gets or sets the delegate called when GetClientRootCertificate request is received.
        /// </summary>
        public IncomingRequestDelegate OnGetClientRootCertificate { get; set; }

        /// <summary>
        /// Gets or sets the delegate called when resource is registered
        /// </summary>
        public IncomingRequestDelegate OnRegisterResource { get; set; }

        /// <summary>
        /// Gets or sets the delegate called when AddRole request is received.
        /// </summary>
        public IncomingRequestDelegate OnAddRole { get; set; }

        /// <summary>
        /// Gets or sets the delegate called when PutRole request is received.
        /// </summary>
        public IncomingRequestDelegate OnPutRole { get; set; }

        /// <summary>
        /// Gets or sets the delegate called when CreateHostedServicesDeployment request is received.
        /// </summary>
        public IncomingRequestDelegate OnCreateHostedServicesDeployment { get; set; }

        /// <summary>
        /// Gets or sets the delegate called when CreateInternalLoadBalancer request is received.
        /// </summary>
        public IncomingRequestDelegate OnCreateInternalLoadBalancer { get; set; }

        /// <summary>
        /// Gets or sets the delegate called when UpdateInternalLoadBalancer request is received.
        /// </summary>
        public IncomingRequestDelegate OnUpdateInternalLoadBalancer { get; set; }

        /// <summary>
        /// Gets or sets the delegate called when DeleteInternalLoadBalancer request is received.
        /// </summary>
        public IncomingRequestDelegate OnDeleteInternalLoadBalancer { get; set; }

        /// <summary>
        /// Gets or sets the delegate called when <c>CreateReservedIp</c> request is received.
        /// </summary>
        public IncomingRequestDelegate OnCreateReservedIp { get; set; }

        /// <summary>
        /// Gets or sets the delegate called when <c>GetReservedIp</c> request is received.
        /// </summary>
        public IncomingRequestDelegate OnGetReservedIp { get; set; }

        /// <summary>
        /// Gets or sets the delegate called when <c>ListReservedIp</c> request is received.
        /// </summary>
        public IncomingRequestDelegate OnListReservedIps { get; set; }

        /// <summary>
        /// Gets or sets the delegate called when <c>DeleteReservedIp</c> request is received.
        /// </summary>
        public IncomingRequestDelegate OnDeleteReservedIp { get; set; }

        /// <summary>
        /// Gets or sets the delegate called when GetConfiguration request is received.
        /// </summary>
        public IncomingRequestDelegate OnGetConfiguration { get; set; }

        /// <summary>
        /// Gets or sets the delegate called when gateway list connection request is received.
        /// </summary>
        public IncomingRequestDelegate OnGetGatewayConnections { get; set; }

        /// <summary>
        /// Gets or sets the delegate called when get supported devices request is received.
        /// </summary>
        public IncomingRequestDelegate OnGetSupportedDevices { get; set; }

        /// <summary>
        /// Gets or sets the delegate called when revoke client certificate request is received.
        /// </summary>
        public IncomingRequestDelegate OnRevokeClientCertificate { get; set; }

        /// <summary>
        /// Gets or sets the delegate called when un-revoke client certificate request is received.
        /// </summary>
        public IncomingRequestDelegate OnUnrevokeClientCertificate { get; set; }

        /// <summary>
        /// Gets or sets the delegate called when list revoked client certificates request is received.
        /// </summary>
        public IncomingRequestDelegate OnListRevokedClientCertificates { get; set; }

        /// <summary>
        /// Gets or sets the delegate called when get virtual device configuration script request is received.
        /// </summary>
        public IncomingRequestDelegate OnGetVirtualDeviceConfigurationScript { get; set; }

        /// <summary>
        /// Gets or sets the delegate called when put virtual network connection request is received.
        /// </summary>
        public IncomingRequestDelegate OnPutVirtualNetworkConnection { get; set; }

        /// <summary>
        /// Gets or sets the delegate called when an operation to download RDP file of role instance is received.
        /// </summary>
        public IncomingRequestDelegate OnRoleInstanceDownloadRemoteDesktopConnectionFile { get; set; }

        /// <summary>
        /// Gets or sets the delegate called when an operation for role instance is received.
        /// </summary>
        public IncomingRequestDelegate OnOperationDeploymentRoleInstanceBySlot { get; set; }

        /// <summary>
        /// Gets or sets the delegate called when an operation for virtual machine is called.
        /// </summary>
        public IncomingRequestDelegate OnVirtualMachineOperation { get; set; }

        /// <summary>
        /// Gets or sets the delegate called when an operation for list images is received.
        /// </summary>
        public IncomingRequestDelegate OnListImages { get; set; }

        /// <summary>
        /// Gets or sets the delegate called when an operation for delete image is received.
        /// </summary>
        public IncomingRequestDelegate OnDeleteImage { get; set; }

        /// <summary>
        /// Gets or sets the delegate called when an operation for create virtual machine image is received.
        /// </summary>
        public IncomingRequestDelegate OnCreateVirtualMachineImage { get; set; }

        /// <summary>
        /// Gets or sets the delegate called when an operation for update virtual machine image is received.
        /// </summary>
        public IncomingRequestDelegate OnUpdateVirtualMachineImage { get; set; }

        /// <summary>
        /// Gets or sets the delegate called when an operation for list OS images is received.
        /// </summary>
        public IncomingRequestDelegate OnListOSImages { get; set; }

        /// <summary>
        /// Gets or sets the delegate called when an operation for get OS image is received.
        /// </summary>
        public IncomingRequestDelegate OnGetOSImage { get; set; }

        /// <summary>
        /// Gets or sets the delegate called when an operation for create OS image is received.
        /// </summary>
        public IncomingRequestDelegate OnCreateOSImage { get; set; }

        /// <summary>
        /// Gets or sets the delegate called when an operation for delete OS image is received.
        /// </summary>
        public IncomingRequestDelegate OnDeleteOSImage { get; set; }

        /// <summary>
        /// Gets or sets the delegate called when an operation for update OS image is received.
        /// </summary>
        public IncomingRequestDelegate OnUpdateOSImage { get; set; }

        /// <summary>
        /// Gets or sets the delegate called when OnListNetworkSecurityGroup request is received.
        /// </summary>
        public IncomingRequestDelegate OnListNetworkSecurityGroupsRequest { get; set; }

        /// <summary>
        /// Gets or sets the delegate called when CreateNetworkSecurityGroup request is received.
        /// </summary>
        public IncomingRequestDelegate OnCreateNetworkSecurityGroupRequest { get; set; }

        /// <summary>
        /// Gets or sets the delegate called when GetNetworkSecurityGroup request is received.
        /// </summary>
        public IncomingRequestDelegate OnGetNetworkSecurityGroupRequest { get; set; }

        /// <summary>
        /// Gets or sets the delegate called when GetNetworkSecurityGroupWithDetails request is received.
        /// </summary>
        public IncomingRequestDelegate OnGetNetworkSecurityGroupWithDetailsRequest { get; set; }

        /// <summary>
        /// Gets or sets the delegate called when DeleteNetworkSecurityGroup request is received.
        /// </summary>
        public IncomingRequestDelegate OnDeleteNetworkSecurityGroupRequest { get; set; }

        /// <summary>
        /// Gets or sets the delegate called when SetNetworkSecurityRule request is received.
        /// </summary>
        public IncomingRequestDelegate OnSetNetworkSecurityRuleRequest { get; set; }

        /// <summary>
        /// Gets or sets the delegate called when DeleteNetworkSecurityRule request is received.
        /// </summary>
        public IncomingRequestDelegate OnDeleteNetworkSecurityRuleRequest { get; set; }

        /// <summary>
        /// Gets or sets the delegate called when AddNetworkInterfaceNetworkSecurityGroup request is received.
        /// </summary>
        public IncomingRequestDelegate OnAddNetworkInterfaceNetworkSecurityGroupRequest { get; set; }

        /// <summary>
        /// Gets or sets the delegate called when GetNetworkInterfaceNetworkSecurityGroup request is received.
        /// </summary>
        public IncomingRequestDelegate OnGetNetworkInterfaceNetworkSecurityGroupRequest { get; set; }

        /// <summary>
        /// Gets or sets the delegate called when DeleteNetworkInterfaceNetworkSecurityGroup request is received.
        /// </summary>
        public IncomingRequestDelegate OnDeleteNetworkInterfaceNetworkSecurityGroupRequest { get; set; }

        /// <summary>
        /// Gets or sets the delegate called when AddVirtualMachineNetworkSecurityGroup request is received.
        /// </summary>
        public IncomingRequestDelegate OnAddVirtualMachineNetworkSecurityGroupRequest { get; set; }

        /// <summary>
        /// Gets or sets the delegate called when GetVirtualMachineNetworkSecurityGroup request is received.
        /// </summary>
        public IncomingRequestDelegate OnGetVirtualMachineNetworkSecurityGroupRequest { get; set; }

        /// <summary>
        /// Gets or sets the delegate called when DeleteVirtualMachineNetworkSecurityGroup request is received.
        /// </summary>
        public IncomingRequestDelegate OnDeleteVirtualMachineNetworkSecurityGroupRequest { get; set; }

        /// <summary>
        /// Gets or sets the delegate called when AddSubnetNetworkSecurityGroup request is received.
        /// </summary>
        public IncomingRequestDelegate OnAddSubnetNetworkSecurityGroupRequest { get; set; }

        /// <summary>
        /// Gets or sets the delegate called when GetSubnetNetworkSecurityGroup request is received.
        /// </summary>
        public IncomingRequestDelegate OnGetSubnetNetworkSecurityGroupRequest { get; set; }

        /// <summary>
        /// Gets or sets the delegate called when DeleteSubnetNetworkSecurityGroup request is received.
        /// </summary>
        public IncomingRequestDelegate OnDeleteSubnetNetworkSecurityGroupRequest { get; set; }

        /// <summary>
        /// Gets or sets the delegate called when ListOperatingSystems is received.
        /// </summary>
        public IncomingRequestDelegate OnListOperatingSystems { get; set; }

        /// <summary>
        /// Gets or sets the delegate called when ListOperatingSystemFamilies is received.
        /// </summary>
        public IncomingRequestDelegate OnListOperatingSystemFamilies { get; set; }

        /// <summary>
        /// Gets or sets the delegate called when ListSubscriptionOperations is received.
        /// </summary>
        public SubscriptionOperationsDelegate OnListSubscriptionOperations { get; set; }

         /// <summary>
        /// Gets or sets the delegate for authenticating request.
        /// </summary>
        public IncomingRequestDelegate OnAuthenticateRequest { get; set; }

        /// <summary>
        /// Gets or sets the delegate for getting authentication metadata.
        /// </summary>
        public IncomingRequestDelegate OnGetAuthenticationMetadata { get; set; }

        /// <summary>
        /// Gets or sets the delegate called when GetCrossConnection is received.
        /// </summary>
        public IncomingRequestDelegate OnGetCrossConnection { get; set; }

        /// <summary>
        /// Gets or sets the delegate called when ListCrossConnections is received.
        /// </summary>
        public IncomingRequestDelegate OnListCrossConnections { get; set; }

        /// <summary>
        /// Gets or sets the delegate called when <c>StartProvisionCrossConnection</c> is received.
        /// </summary>
        public IncomingRequestDelegate OnProvisionCrossConnection { get; set; }

        /// <summary>
        /// Gets or sets the delegate called when <c>UpdateCrossConnection</c> is received.
        /// </summary>
        public IncomingRequestDelegate OnUpdateCrossConnection { get; set; }

        /// <summary>
        /// Gets or sets the delegate called when <c>GetBgpPeering</c> is received.
        /// </summary>
        public BgpPeeringDelegate OnGetBgpPeering { get; set; }

        /// <summary>
        /// Gets or sets the delegate called when <c>PutBgpPeering</c> is received.
        /// </summary>
        public BgpPeeringDelegate OnPutBgpPeering { get; set; }

        /// <summary>
        /// Gets or sets the delegate called when <c>PatchBgpPeering</c> is received.
        /// </summary>
        public BgpPeeringDelegate OnPatchBgpPeering { get; set; }

        /// <summary>
        /// Gets or sets the delegate called when <c>DeleteBgpPeering</c> is received.
        /// </summary>
        public BgpPeeringDelegate OnDeleteBgpPeering { get; set; }

        /// <summary>
        /// Called to configure http configuration.
        /// </summary>
        /// <param name="configuration">The http configuration.</param>
        private void OnConfigure(HttpConfiguration configuration)
        {
            configuration.Routes.MapHttpRoute(
                name: "CloudService",
                routeTemplate: "{subscriptionId}/cloudservices/{cloudServiceName}",
                defaults: new { controller = "CloudServiceTest" });

            configuration.Routes.MapHttpRoute(
                name: "Services",
                routeTemplate: "{subscriptionId}/services",
                defaults: new { controller = "ServicesTest" });

            configuration.Routes.MapHttpRoute(
                name: "SubscriptionOperations",
                routeTemplate: "{subscriptionId}/operations",
                defaults: new { controller = "SubscriptionOperationsTest" },
                constraints: new { httpMethod = new HttpMethodConstraint(HttpMethod.Get) });

            configuration.Routes.MapHttpRoute(
                name: "ListHostedServices",
                routeTemplate: "{subscriptionId}/services/hostedservices",
                defaults: new { controller = "HostedServiceTest" },
                constraints: new { httpMethod = new HttpMethodConstraint(HttpMethod.Get) });

            configuration.Routes.MapHttpRoute(
                name: "CreateHostedService",
                routeTemplate: "{subscriptionId}/services/hostedservices",
                defaults: new { controller = "HostedServiceTest", action = "CreateHostedService" },
                constraints: new { httpMethod = new HttpMethodConstraint(HttpMethod.Post) });

            configuration.Routes.MapHttpRoute(
                name: "GetHostedService",
                routeTemplate: "{subscriptionId}/services/hostedservices/{hostedServiceName}",
                defaults: new { controller = "HostedServiceTest", action = "GetHostedService" },
                constraints: new { httpMethod = new HttpMethodConstraint(HttpMethod.Get) });

            configuration.Routes.MapHttpRoute(
                name: "DeleteHostedService",
                routeTemplate: "{subscriptionId}/services/hostedservices/{hostedServiceName}",
                defaults: new { controller = "HostedServiceTest", action = "DeleteHostedService" },
                constraints: new { httpMethod = new HttpMethodConstraint(HttpMethod.Delete) });

            configuration.Routes.MapHttpRoute(
                name: "UpdateHostedService",
                routeTemplate: "{subscriptionId}/services/hostedservices/{hostedServiceName}",
                defaults: new { controller = "HostedServiceTest", action = "UpdateHostedService" },
                constraints: new { httpMethod = new HttpMethodConstraint(HttpMethod.Put) });

            configuration.Routes.MapHttpRoute(
                name: "CreateHostedServicesDeployment",
                routeTemplate: "{subscriptionId}/services/hostedservices/{hostedServiceName}/deployments",
                defaults: new { controller = "HostedServiceTest", action = "CreateHostedServicesDeployment" },
                constraints: new { httpMethod = new HttpMethodConstraint(HttpMethod.Post) });

            configuration.Routes.MapHttpRoute(
                name: "DeploymentMigration",
                routeTemplate: "{subscriptionId}/services/hostedservices/{serviceName}/deployments/{deploymentName}/migration",
                defaults: new { controller = "HostedServiceTest", action = "DeploymentMigration" },
                constraints: new { httpMethod = new HttpMethodConstraint(HttpMethod.Post) });

            configuration.Routes.MapHttpRoute(
                name: "GetHostedServiceDeploymentBySlot",
                routeTemplate: "{subscriptionId}/services/hostedservices/{hostedServiceName}/deploymentslots/{slotName}",
                defaults: new { controller = "HostedServiceTest", action = "GetHostedServiceDeploymentBySlot" },
                constraints: new { httpMethod = new HttpMethodConstraint(HttpMethod.Get) });

            configuration.Routes.MapHttpRoute(
                name: "DeleteHostedServiceDeploymentBySlot",
                routeTemplate: "{subscriptionId}/services/hostedservices/{hostedServiceName}/deploymentslots/{slotName}",
                defaults: new { controller = "HostedServiceTest", action = "DeleteHostedServiceDeploymentBySlot" },
                constraints: new { httpMethod = new HttpMethodConstraint(HttpMethod.Delete) });

            configuration.Routes.MapHttpRoute(
                name: "CreateUpdateDeploymentSlot",
                routeTemplate: "{subscriptionId}/services/hostedservices/{hostedServiceName}/deploymentslots/{slotName}",
                defaults: new { controller = "HostedServiceTest", action = "CreateUpdateDeploymentSlot" },
                constraints: new { httpMethod = new HttpMethodConstraint(HttpMethod.Post) });

            configuration.Routes.MapHttpRoute(
                name: "DeploymentSlotCloudServiceRoleInstanceOperation",
                routeTemplate: "{subscriptionId}/services/hostedservices/{hostedServiceName}/deploymentslots/{slotName}/roleinstances/{roleInstanceName}",
                defaults: new { controller = "HostedServiceTest", action = "OperationOnRoleInstance" },
                constraints: new { httpMethod = new HttpMethodConstraint(HttpMethod.Post) });

            configuration.Routes.MapHttpRoute(
                name: "DeploymentSlotCloudServiceRoleInstanceDownloadRDP",
                routeTemplate: "{subscriptionId}/services/hostedservices/{hostedServiceName}/deployments/{deploymentName}/roleinstances/{roleInstanceName}/ModelFile",
                defaults: new { controller = "HostedServiceTest", action = "DownloadRemoteDesktopConnectionFile" },
                constraints: new { httpMethod = new HttpMethodConstraint(HttpMethod.Get) });

            configuration.Routes.MapHttpRoute(
                name: "DeploymentSlotVirtualMachineRoleInstanceOperation",
                routeTemplate: "{subscriptionId}/services/hostedservices/{hostedServiceName}/deployments/{slotName}/roleinstances/{roleInstanceName}/Operations",
                defaults: new { controller = "HostedServiceTest", action = "OperationOnVirtualMachine" },
                constraints: new { httpMethod = new HttpMethodConstraint(HttpMethod.Post) });

            configuration.Routes.MapHttpRoute(
                name: "SwapHostedServiceDeployment",
                routeTemplate: "{subscriptionId}/services/hostedservices/{hostedServiceName}",
                defaults: new { controller = "HostedServiceTest", action = "SwapHostedServiceDeployment" },
                constraints: new { httpMethod = new HttpMethodConstraint(HttpMethod.Post) });

            configuration.Routes.MapHttpRoute(
                name: "GetConfigurationHostedServiceDeployment",
                routeTemplate: "{subscriptionId}/services/hostedservices/{hostedServiceName}/deploymentSlots/{slotName}/mock/configuration",
                defaults: new { controller = "HostedServiceTest", action = "GetConfiguration" },
                constraints: new { httpMethod = new HttpMethodConstraint(HttpMethod.Get) });

            configuration.Routes.MapHttpRoute(
                name: "HostedServiceAddRole",
                routeTemplate: "{subscriptionId}/services/hostedservices/{hostedServiceName}/deployments/{deploymentName}/roles",
                defaults: new { controller = "HostedServiceTest", action = "addRole" },
                constraints: new { httpMethod = new HttpMethodConstraint(HttpMethod.Post) });

            configuration.Routes.MapHttpRoute(
                name: "HostedServicePutRole",
                routeTemplate: "{subscriptionId}/services/hostedservices/{hostedServiceName}/deployments/{deploymentName}/roles/{roleName}",
                defaults: new { controller = "HostedServiceTest", action = "putRole" },
                constraints: new { httpMethod = new HttpMethodConstraint(HttpMethod.Put) });

            configuration.Routes.MapHttpRoute(
                name: "HostedServiceAddDataDisks",
                routeTemplate: "{subscriptionId}/services/hostedservices/{hostedServiceName}/deployments/{deploymentName}/roles/{roleName}/DataDisks",
                defaults: new { controller = "HostedServiceTest" });

            configuration.Routes.MapHttpRoute(
                name: "HostedServiceDataDisk",
                routeTemplate: "{subscriptionId}/services/hostedservices/{hostedServiceName}/deployments/{deploymentName}/roles/{roleName}/DataDisks/{lun}",
                defaults: new { controller = "HostedServiceTest" },
                constraints: new { httpMethod = new HttpMethodConstraint(HttpMethod.Get, HttpMethod.Put, HttpMethod.Delete) });

            configuration.Routes.MapHttpRoute(
                name: "HostedServiceListCertificates",
                routeTemplate: "{subscriptionId}/services/hostedservices/{hostedServiceName}/certificates",
                defaults: new { controller = "HostedServiceTest", action = "listcertificates" },
                constraints: new { httpMethod = new HttpMethodConstraint(HttpMethod.Get) });

            configuration.Routes.MapHttpRoute(
                name: "HostedServiceAddCertificates",
                routeTemplate: "{subscriptionId}/services/hostedservices/{hostedServiceName}/certificates",
                defaults: new { controller = "HostedServiceTest", action = "addcertificate" },
                constraints: new { httpMethod = new HttpMethodConstraint(HttpMethod.Post) });

            configuration.Routes.MapHttpRoute(
                name: "HostedServiceGetCertificate",
                routeTemplate: "{subscriptionId}/services/hostedservices/{hostedServiceName}/certificates/{thumbprint}",
                defaults: new { controller = "HostedServiceTest", action = "getcertificate" },
                constraints: new { httpMethod = new HttpMethodConstraint(HttpMethod.Get) });

            configuration.Routes.MapHttpRoute(
                name: "HostedServiceDeleteCertificate",
                routeTemplate: "{subscriptionId}/services/hostedservices/{hostedServiceName}/certificates/{thumbprint}",
                defaults: new { controller = "HostedServiceTest", action = "deletecertificate" },
                constraints: new { httpMethod = new HttpMethodConstraint(HttpMethod.Delete) });

            configuration.Routes.MapHttpRoute(
                name: "HostedServiceListExtensions",
                routeTemplate: "{subscriptionId}/services/hostedservices/{hostedServiceName}/extensions",
                defaults: new { controller = "HostedServiceTest", action = "listextensions" },
                constraints: new { httpMethod = new HttpMethodConstraint(HttpMethod.Get) });

            configuration.Routes.MapHttpRoute(
                name: "HostedServiceAddExtension",
                routeTemplate: "{subscriptionId}/services/hostedservices/{hostedServiceName}/extensions",
                defaults: new { controller = "HostedServiceTest", action = "addextension" },
                constraints: new { httpMethod = new HttpMethodConstraint(HttpMethod.Post) });

            configuration.Routes.MapHttpRoute(
                name: "HostedServiceGetExtension",
                routeTemplate: "{subscriptionId}/services/hostedservices/{hostedServiceName}/extensions/{extensionId}",
                defaults: new { controller = "HostedServiceTest", action = "getextension" },
                constraints: new { httpMethod = new HttpMethodConstraint(HttpMethod.Get) });

            configuration.Routes.MapHttpRoute(
                name: "HostedServiceDeleteExtension",
                routeTemplate: "{subscriptionId}/services/hostedservices/{hostedServiceName}/extensions/{extensionId}",
                defaults: new { controller = "HostedServiceTest", action = "deleteextension" },
                constraints: new { httpMethod = new HttpMethodConstraint(HttpMethod.Delete) });

            configuration.Routes.MapHttpRoute(
                name: "DiskService",
                routeTemplate: "{subscriptionId}/services/disks",
                defaults: new { controller = "DiskTest" });

            configuration.Routes.MapHttpRoute(
                name: "DiskServiceGetDisk",
                routeTemplate: "{subscriptionId}/services/disks/{diskName}",
                defaults: new { controller = "DiskTest" });

            configuration.Routes.MapHttpRoute(
                name: "Operations",
                routeTemplate: "{subscriptionId}/operations/{operationId}",
                defaults: new { controller = "OperationTest" },
                constraints: new { httpMethod = new HttpMethodConstraint(HttpMethod.Get) });

            configuration.Routes.MapHttpRoute(
               name: "ListSubscriptions",
               routeTemplate: "subscriptions",
               constraints: new { httpMethod = new HttpMethodConstraint(HttpMethod.Get) },
               defaults: new
               {
                   controller = "SubscriptionTest",
                   action = "ListSubscriptions",
               });

            configuration.Routes.MapHttpRoute(
                name: "Subscription",
                routeTemplate: "{subscriptionId}",
                constraints: new { httpMethod = new HttpMethodConstraint(HttpMethod.Get) },
                defaults: new
                {
                    controller = "SubscriptionTest",
                    action = "GetSubscription",
                });

            configuration.Routes.MapHttpRoute(
                name: "MoveSubscriptionResources",
                routeTemplate: "{subscriptionId}/transferto/{targetSubscriptionId}",
                constraints: new { httpMethod = new HttpMethodConstraint(HttpMethod.Post) },
                defaults: new
                {
                    controller = "SubscriptionTest",
                    action = "MoveSubscriptionResources",
                });

            configuration.Routes.MapHttpRoute(
                name: "GetSubscriptionTransferStatus",
                routeTemplate: "{subscriptionId}/subscriptiontransferstatus",
                constraints: new { httpMethod = new HttpMethodConstraint(HttpMethod.Get) },
                defaults: new { controller = "SubscriptionTest", action = "GetSubscriptionTransferStatus" });

            configuration.Routes.MapHttpRoute(
               name: "SubscriptionWithDetails",
               routeTemplate: "detailedsubscriptions/{subscriptionId}",
               constraints: new { httpMethod = new HttpMethodConstraint(HttpMethod.Get) },
               defaults: new
               {
                   controller = "SubscriptionTest",
                   action = "GetSubscriptionWithDetails",
               });

            configuration.Routes.MapHttpRoute(
               name: "Principals",
               routeTemplate: "{subscriptionId}/principals",
               defaults: new { controller = "PrincipalsTest" });

            configuration.Routes.MapHttpRoute(
               name: "Principal",
               routeTemplate: "{subscriptionId}/principals/{principalId}",
               defaults: new { controller = "PrincipalsTest" });

            configuration.Routes.MapHttpRoute(
               name: "Resources",
               routeTemplate: "{subscriptionId}/cloudservices/{cloudServiceName}/resources/{resourceProviderNamespace}/{resourceType}/{resourceName}",
               defaults: new { controller = "ResourcesTest" });

            configuration.Routes.MapHttpRoute(
                name: "StorageServices",
                routeTemplate: "{subscriptionId}/services/storageservices",
                defaults: new { controller = "StorageAccountTest" });

            configuration.Routes.MapHttpRoute(
                name: "StorageService",
                routeTemplate: "{subscriptionId}/services/storageservices/{serviceName}",
                defaults: new { controller = "StorageAccountTest" });

            configuration.Routes.MapHttpRoute(
                name: "StorageServiceMigration",
                routeTemplate: "{subscriptionId}/services/storageservices/{serviceName}/migration",
                defaults: new { controller = "StorageAccountTest", action = "StorageServiceMigration" });

            configuration.Routes.MapHttpRoute(
                name: "GetStorageKeys",
                routeTemplate: "{subscriptionId}/services/storageservices/{serviceName}/keys",
                defaults: new { controller = "StorageKeysTest" },
                constraints: new { httpMethod = new System.Web.Http.Routing.HttpMethodConstraint(HttpMethod.Get) });

            configuration.Routes.MapHttpRoute(
                name: "RegenerateStorageKeys",
                routeTemplate: "{subscriptionId}/services/storageservices/{serviceName}/keys",
                defaults: new { controller = "StorageKeysTest", action = "regenerate" },
                constraints: new { httpMethod = new System.Web.Http.Routing.HttpMethodConstraint(HttpMethod.Post) });

            configuration.Routes.MapHttpRoute(
                name: "NetworkConfiguration",
                routeTemplate: "{subscriptionId}/services/networking/media",
                defaults: new { controller = "VirtualNetworksTest" });

            configuration.Routes.MapHttpRoute(
                name: "ListVirtualNetworkSites ",
                routeTemplate: "{subscriptionId}/services/networking/virtualnetwork",
                defaults: new { controller = "VirtualNetworkSitesTest" },
                constraints: new { httpMethod = new HttpMethodConstraint(HttpMethod.Get) });

            configuration.Routes.MapHttpRoute(
                name: "ListAffinityGroups",
                routeTemplate: "{subscriptionId}/affinitygroups",
                defaults: new { controller = "AffinityGroupsTest" },
                constraints: new { httpMethod = new HttpMethodConstraint(HttpMethod.Get) });

            configuration.Routes.MapHttpRoute(
                name: "GetAffinityGroup",
                routeTemplate: "{subscriptionId}/affinitygroups/{affinityGroupName}",
                defaults: new { controller = "AffinityGroupsTest" },
                constraints: new { httpMethod = new HttpMethodConstraint(HttpMethod.Get) });

            configuration.Routes.MapHttpRoute(
                name: "ListLocationsGroups",
                routeTemplate: "{subscriptionId}/locations",
                defaults: new { controller = "LocationsTest" },
                constraints: new { httpMethod = new HttpMethodConstraint(HttpMethod.Get) });

            configuration.Routes.MapHttpRoute(
                name: "ListRoleSizes",
                routeTemplate: "{subscriptionId}/rolesizes",
                defaults: new { controller = "RoleSizeTest" },
                constraints: new { httpMethod = new HttpMethodConstraint(HttpMethod.Get) });

            configuration.Routes.MapHttpRoute(
                name: "VirtualNetworkMigration",
                routeTemplate: "{subscriptionId}/services/networking/virtualnetwork/{virtualNetworkName}/migration",
                defaults: new { controller = "VirtualNetworksTest", action = "VirtualNetworkMigration" },
                constraints: new { httpMethod = new HttpMethodConstraint(HttpMethod.Post) });

            configuration.Routes.MapHttpRoute(
                name: "CreateVirtualNetworkGateway",
                routeTemplate: "subscriptions/{subscriptionId}/services/networking/{virtualNetworkName}/gateway",
                defaults: new { controller = "VirtualNetworkGatewayTest" },
                constraints: new { httpMethod = new HttpMethodConstraint(HttpMethod.Post, HttpMethod.Delete) });

            configuration.Routes.MapHttpRoute(
                name: "GetVirtualNetworkGateway",
                routeTemplate: "subscriptions/{subscriptionId}/services/networking/{virtualNetworkName}/gateway",
                defaults: new { controller = "VirtualNetworkGatewayTest", action = "GetGateway" },
                constraints: new { httpMethod = new HttpMethodConstraint(HttpMethod.Get) });

            configuration.Routes.MapHttpRoute(
                name: "GetVirtualNetworkDiagnostics",
                routeTemplate: "subscriptions/{subscriptionId}/Services/networking/{virtualNetworkName}/gateway/publicdiagnostics",
                defaults: new { controller = "VirtualNetworkGatewayTest", action = "GetDiagnostics" },
                constraints: new { httpMethod = new HttpMethodConstraint(HttpMethod.Get) });

            configuration.Routes.MapHttpRoute(
                name: "UpdateVirtualNetworkDiagnostics",
                routeTemplate: "subscriptions/{subscriptionId}/Services/networking/{virtualNetworkName}/gateway/publicdiagnostics",
                defaults: new { controller = "VirtualNetworkGatewayTest", action = "UpdateDiagnostics" },
                constraints: new { httpMethod = new HttpMethodConstraint(HttpMethod.Put) });

            configuration.Routes.MapHttpRoute(
                name: "GetDeviceConfigurationScript",
                routeTemplate: "subscriptions/{subscriptionId}/Services/networking/{virtualNetworkName}/gateway/vpndeviceconfigscript",
                defaults: new { controller = "VirtualNetworkGatewayTest", action = "GetVirtualDeviceConfigurationScript" },
                constraints: new { httpMethod = new HttpMethodConstraint(HttpMethod.Get) });

            configuration.Routes.MapHttpRoute(
                name: "RevokeVirtualNetworkClientCertificate",
                routeTemplate: "subscriptions/{subscriptionId}/Services/networking/{virtualNetworkName}/gateway/clientcertificates/{certificateThumbprint}",
                defaults: new { controller = "VirtualNetworkGatewayTest", action = "RevokeVirtualNetworkClientCertificate" },
                constraints: new { httpMethod = new HttpMethodConstraint(HttpMethod.Post) });

            configuration.Routes.MapHttpRoute(
                name: "UnrevokeVirtualNetworkClientCertificate",
                routeTemplate: "subscriptions/{subscriptionId}/Services/networking/{virtualNetworkName}/gateway/clientcertificates/{certificateThumbprint}",
                defaults: new { controller = "VirtualNetworkGatewayTest", action = "UnrevokeVirtualNetworkClientCertificate" },
                constraints: new { httpMethod = new HttpMethodConstraint(HttpMethod.Delete) });

            configuration.Routes.MapHttpRoute(
                name: "ListVirtualNetworkClientCertificates",
                routeTemplate: "subscriptions/{subscriptionId}/Services/networking/{virtualNetworkName}/gateway/clientcertificates",
                defaults: new { controller = "VirtualNetworkGatewayTest", action = "GetVirtualNetworkClientCertificates" },
                constraints: new { httpMethod = new HttpMethodConstraint(HttpMethod.Get) });

            configuration.Routes.MapHttpRoute(
                name: "GatewayConnectionPutRequest",
                routeTemplate: "subscriptions/{subscriptionId}/Services/networking/{virtualNetworkName}/gateway/connection/{localNetworkSiteName}",
                defaults: new { controller = "VirtualNetworkGatewayTest" },
                constraints: new { httpMethod = new HttpMethodConstraint(HttpMethod.Put) });

            configuration.Routes.MapHttpRoute(
                name: "GetGatewayConnections",
                routeTemplate: "subscriptions/{subscriptionId}/Services/networking/{virtualNetworkName}/gateway/connections",
                defaults: new { controller = "VirtualNetworkGatewayTest", action = "OnGetGatewayConnections" },
                constraints: new { httpMethod = new HttpMethodConstraint(HttpMethod.Get) });

            configuration.Routes.MapHttpRoute(
                name: "GetGatewaySupportedDevices",
                routeTemplate: "subscriptions/{subscriptionId}/Services/networking/supporteddevices",
                defaults: new { controller = "VirtualNetworkGatewayTest", action = "OnGetSupportedDevices" },
                constraints: new { httpMethod = new HttpMethodConstraint(HttpMethod.Get) });

            configuration.Routes.MapHttpRoute(
                name: "GetVirtualNetworkGatewayOperation",
                routeTemplate: "subscriptions/{subscriptionId}/Services/networking/operation/{operationId}",
                defaults: new { controller = "VirtualNetworkGatewayOperationTest" },
                constraints: new { httpMethod = new HttpMethodConstraint(HttpMethod.Get) });

            configuration.Routes.MapHttpRoute(
                name: "GetVirtualNetworkGatewayPackage",
                routeTemplate: "subscriptions/{subscriptionId}/Services/networking/{virtualNetworkName}/gateway/vpnclientpackage",
                defaults: new { controller = "VirtualNetworkGatewayPackageTest" },
                constraints: new { httpMethod = new HttpMethodConstraint(HttpMethod.Post) });

            configuration.Routes.MapHttpRoute(
                name: "VirtualNetworkGatewayCertificates",
                routeTemplate: "subscriptions/{subscriptionId}/Services/networking/{virtualNetworkName}/gateway/clientrootcertificates",
                defaults: new { controller = "VirtualNetworkGatewayCertificatesTest" });

            configuration.Routes.MapHttpRoute(
                name: "VirtualNetworkGatewayCertificate",
                routeTemplate: "subscriptions/{subscriptionId}/Services/networking/{virtualNetworkName}/gateway/clientrootcertificates/{certificateThumbprint}",
                defaults: new { controller = "VirtualNetworkGatewayCertificatesTest" });

            configuration.Routes.MapHttpRoute(
                name: "SubscriptionReservedIpsRequest",
                routeTemplate: "{subscriptionId}/services/networking/reservedips/{ipname}",
                defaults: new { controller = "ReservedIpTest" });

            configuration.Routes.MapHttpRoute(
                name: "SubscriptionResourceGroupReservedIpRequest",
                routeTemplate: "{subscriptionId}/services/networking/reservedips",
                defaults: new { controller = "ReservedIpTest" });

            configuration.Routes.MapHttpRoute(
                name: "CreateInternalLoadBalancer",
                routeTemplate: "{subscriptionId}/services/hostedservices/{serviceName}/deployments/{deploymentName}/loadbalancers",
                defaults: new { controller = "InternalLoadBalancerTest" },
                constraints: new { httpMethod = new HttpMethodConstraint(HttpMethod.Post) });

            configuration.Routes.MapHttpRoute(
                name: "UpdateInternalLoadBalancer",
                routeTemplate: "{subscriptionId}/services/hostedservices/{serviceName}/deployments/{deploymentName}/loadbalancers/{loadBalancerName}",
                defaults: new { controller = "InternalLoadBalancerTest" },
                constraints: new { httpMethod = new HttpMethodConstraint(HttpMethod.Put) });

            configuration.Routes.MapHttpRoute(
                name: "DeleteInternalLoadBalancer",
                routeTemplate: "{subscriptionId}/services/hostedservices/{serviceName}/deployments/{deploymentName}/loadbalancers/{loadBalancerName}",
                defaults: new { controller = "InternalLoadBalancerTest" },
                constraints: new { httpMethod = new HttpMethodConstraint(HttpMethod.Delete) });

            configuration.Routes.MapHttpRoute(
                name: "VMImages",
                routeTemplate: "{subscriptionId}/services/vmimages",
                defaults: new { controller = "ImageTest" },
                constraints: new { httpMethod = new HttpMethodConstraint(HttpMethod.Get, HttpMethod.Post) });

            configuration.Routes.MapHttpRoute(
                name: "VMImage",
                routeTemplate: "{subscriptionId}/services/vmimages/{imageName}",
                defaults: new { controller = "ImageTest" },
                constraints: new { httpMethod = new HttpMethodConstraint(HttpMethod.Delete, HttpMethod.Put) });

            configuration.Routes.MapHttpRoute(
                name: "OSImages",
                routeTemplate: "{subscriptionId}/services/images",
                defaults: new { controller = "OSImageTest" },
                constraints: new { httpMethod = new HttpMethodConstraint(HttpMethod.Get, HttpMethod.Post) });

            configuration.Routes.MapHttpRoute(
                name: "OSImage",
                routeTemplate: "{subscriptionId}/services/images/{imageName}",
                defaults: new { controller = "OSImageTest" },
                constraints: new { httpMethod = new HttpMethodConstraint(HttpMethod.Get, HttpMethod.Delete, HttpMethod.Put) });

            configuration.Routes.MapHttpRoute(
                name: "NetworkSecurityGroups",
                routeTemplate: "{subscriptionId}/services/networking/networksecuritygroups",
                defaults: new { controller = "NetworkSecurityGroupTest" },
                constraints: new { httpMethod = new HttpMethodConstraint(HttpMethod.Get, HttpMethod.Post) });

            configuration.Routes.MapHttpRoute(
                name: "NetworkSecurityGroup",
                routeTemplate: "{subscriptionId}/services/networking/networksecuritygroups/{networkSecurityGroupName}",
                defaults: new { controller = "NetworkSecurityGroupTest" },
                constraints: new { httpMethod = new HttpMethodConstraint(HttpMethod.Get, HttpMethod.Delete) });

            configuration.Routes.MapHttpRoute(
                name: "NetworkSecurityRule",
                routeTemplate: "{subscriptionId}/services/networking/networksecuritygroups/{networkSecurityGroupName}/rules/{ruleName}",
                defaults: new { controller = "NetworkSecurityGroupTest" },
                constraints: new { httpMethod = new HttpMethodConstraint(HttpMethod.Put, HttpMethod.Delete) });

            configuration.Routes.MapHttpRoute(
                name: "NetworkInterfaceNetworkSecurityGroups",
                routeTemplate: "{subscriptionId}/services/hostedservices/{hostedServiceName}/deployments/{deploymentName}/roles/{roleName}/networkinterfaces/{networkInterfaceName}/networksecuritygroups",
                defaults: new { controller = "NetworkInterfaceNetworkSecurityGroupTest" },
                constraints: new { httpMethod = new HttpMethodConstraint(HttpMethod.Get, HttpMethod.Post) });

            configuration.Routes.MapHttpRoute(
                name: "NetworkInterfaceNetworkSecurityGroup",
                routeTemplate: "{subscriptionId}/services/hostedservices/{hostedServiceName}/deployments/{deploymentName}/roles/{roleName}/networkinterfaces/{networkInterfaceName}/networksecuritygroups/{networkSecurityGroupName}",
                defaults: new { controller = "NetworkInterfaceNetworkSecurityGroupTest" },
                constraints: new { httpMethod = new HttpMethodConstraint(HttpMethod.Delete) });

            configuration.Routes.MapHttpRoute(
                name: "VirtualMachineNetworkSecurityGroups",
                routeTemplate: "{subscriptionId}/services/hostedservices/{hostedServiceName}/deployments/{deploymentName}/roles/{roleName}/networksecuritygroups",
                defaults: new { controller = "VirtualMachineNetworkSecurityGroupTest" },
                constraints: new { httpMethod = new HttpMethodConstraint(HttpMethod.Get, HttpMethod.Post) });

            configuration.Routes.MapHttpRoute(
                name: "VirtualMachineNetworkSecurityGroup",
                routeTemplate: "{subscriptionId}/services/hostedservices/{hostedServiceName}/deployments/{deploymentName}/roles/{roleName}/networksecuritygroups/{networkSecurityGroupName}",
                defaults: new { controller = "VirtualMachineNetworkSecurityGroupTest" },
                constraints: new { httpMethod = new HttpMethodConstraint(HttpMethod.Delete) });

            configuration.Routes.MapHttpRoute(
                name: "SubnetNetworkSecurityGroups",
                routeTemplate: "{subscriptionId}/services/networking/virtualnetwork/{virtualNetworkName}/subnets/{subnetName}/networksecuritygroups",
                defaults: new { controller = "SubnetNetworkSecurityGroupTest" },
                constraints: new { httpMethod = new HttpMethodConstraint(HttpMethod.Get, HttpMethod.Post) });

            configuration.Routes.MapHttpRoute(
                name: "SubnetNetworkSecurityGroup",
                routeTemplate: "{subscriptionId}/services/networking/virtualnetwork/{virtualNetworkName}/subnets/{subnetName}/networksecuritygroups/{networkSecurityGroupName}",
                defaults: new { controller = "SubnetNetworkSecurityGroupTest" },
                constraints: new { httpMethod = new HttpMethodConstraint(HttpMethod.Delete) });

            configuration.Routes.MapHttpRoute(
                name: "ListOperatingSystems",
                routeTemplate: "{subscriptionId}/operatingsystems",
                defaults: new { controller = "OperatingSystemTest", action = "ListOperatingSystems" },
                constraints: new { httpMethod = new HttpMethodConstraint(HttpMethod.Get) });

            configuration.Routes.MapHttpRoute(
                name: "ListOperatingSystemFamilies",
                routeTemplate: "{subscriptionId}/operatingsystemfamilies",
                defaults: new { controller = "OperatingSystemTest", action = "ListOperatingSystemFamilies" },
                constraints: new { httpMethod = new HttpMethodConstraint(HttpMethod.Get) });

            configuration.Routes.MapHttpRoute(
                name: "ListCrossConnections",
                routeTemplate: "subscriptions/{subscriptionId}/Services/networking/crossconnections",
                defaults: new { controller = "ExpressRouteTest", action = "ListCrossConnections" },
                constraints: new { httpMethod = new HttpMethodConstraint(HttpMethod.Get) });

            configuration.Routes.MapHttpRoute(
                name: "GetCrossConnection",
                routeTemplate: "subscriptions/{subscriptionId}/Services/networking/dedicatedcircuits/{crossConnectionName}/crossconnection",
                defaults: new { controller = "ExpressRouteTest", action = "GetCrossConnection" },
                constraints: new { httpMethod = new HttpMethodConstraint(HttpMethod.Get) });

            configuration.Routes.MapHttpRoute(
                name: "ProvisionCrossConnection",
                routeTemplate: "subscriptions/{subscriptionId}/Services/networking/dedicatedcircuits/{crossConnectionName}/crossconnection",
                defaults: new { controller = "ExpressRouteTest", action = "ProvisionCrossConnection" },
                constraints: new { httpMethod = new HttpMethodConstraint(HttpMethod.Post) });

            configuration.Routes.MapHttpRoute(
                name: "UpdateCrossConnection",
                routeTemplate: "subscriptions/{subscriptionId}/Services/networking/dedicatedcircuits/{crossConnectionName}/crossconnection",
                defaults: new { controller = "ExpressRouteTest", action = "UpdateCrossConnection" },
                constraints: new { httpMethod = new HttpMethodConstraint(HttpMethod.Put) });

            configuration.Routes.MapHttpRoute(
                name: "PeeringsRequest",
                routeTemplate: "subscriptions/{subscriptionId}/Services/networking/dedicatedcircuits/{crossConnectionName}/bgppeerings/{peeringType}",
                defaults: new { controller = "ExpressRouteTest" },
                constraints: new { httpMethod = new HttpMethodConstraint(HttpMethod.Get, HttpMethod.Put, HttpMethod.Post, HttpMethod.Delete) });

            // Note(wenwu): keep this at the end of the list to avoid hijacking the other /networking route.
            configuration.Routes.MapHttpRoute(
                name: "CheckVirtualNetworkIPAddressAvailability",
                routeTemplate: "{subscriptionId}/Services/networking/{virtualNetworkName}",
                defaults: new { controller = "VirtualNetworksTest", action = "CheckIPAddressAvailability" },
                constraints: new { httpMethod = new HttpMethodConstraint(HttpMethod.Get) });

            configuration.Services.Replace(typeof(IHttpControllerSelector), new LegacyCloudServiceControllerSelector(configuration));
            configuration.MessageHandlers.Add(new AuthenticationHandler(this));
            configuration.DependencyResolver = new LegacyCloudServiceDependencyResolver(this);
            configuration.Filters.Add(new ExceptionTraceFilter());
        }

        /// <summary>
        /// The services request controller.
        /// </summary>
        public class ServicesTestController : ApiController
        {
            /// <summary>
            /// The legacy cloud service test host.
            /// </summary>
            private readonly LegacyManagementApiTestHost host;

            /// <summary>
            /// Initializes a new instance of the <see cref="ServicesTestController" /> class.
            /// </summary>
            /// <param name="host">The legacy cloud service test host.</param>
            public ServicesTestController(LegacyManagementApiTestHost host)
            {
                this.host = host;
            }

            /// <summary>
            /// Registers the subscription to the resource.
            /// </summary>
            /// <param name="subscriptionId">The subscription Id.</param>
            [HttpPut]
            public Task<HttpResponseMessage> PutRegisterResource(string subscriptionId)
            {
                if (this.host.OnRegisterResource != null)
                {
                    return Task.FromResult(this.host.OnRegisterResource(this.Request, null));
                }

                return Task.FromResult(new HttpResponseMessage(HttpStatusCode.OK));
            }
        }

        /// <summary>
        /// The hosted service request controller.
        /// </summary>
        public class HostedServiceTestController : ApiController
        {
            /// <summary>
            /// The legacy cloud service test host.
            /// </summary>
            private readonly LegacyManagementApiTestHost host;

            /// <summary>
            /// Initializes a new instance of the <see cref="HostedServiceTestController"/> class.
            /// </summary>
            /// <param name="host">The legacy cloud service test host.</param>
            public HostedServiceTestController(LegacyManagementApiTestHost host)
            {
                this.host = host;
            }

            /// <summary>
            /// Lists the requested hosted service with variable details.
            /// </summary>
            /// <param name="subscriptionId">The subscription Id.</param>
            [HttpGet]
            public Task<HttpResponseMessage> ListHostedServices(string subscriptionId)
            {
                if (this.Request.Headers.Contains("x-ms-continuation-token"))
                {
                    if (this.Request.Headers.GetValues("x-ms-continuation-token").FirstOrDefault().EqualsInsensitively("All") && this.host.OnListHostedServicesWithDetailsRequest != null)
                    {
                        return Task.FromResult(this.host.OnListHostedServicesWithDetailsRequest(this.Request, null));
                    }
                }

                if (this.host.OnListHostedServicesRequest != null)
                {
                    return Task.FromResult(this.host.OnListHostedServicesRequest(this.Request, null));
                }

                return Task.FromResult(new HttpResponseMessage(HttpStatusCode.OK));
            }

            /// <summary>
            /// Creates the requested hosted service with variable details.
            /// </summary>
            /// <param name="subscriptionId">The subscription Id.</param>
            [HttpPost]
            [ActionName("CreateHostedService")]
            public Task<HttpResponseMessage> CreateHostedService(string subscriptionId)
            {
                var requestBody = this.Request.Content.ReadAsStringAsync().Result;
                if (this.host.OnCreateHostedServiceRequest != null)
                {
                    return Task.FromResult(this.host.OnCreateHostedServiceRequest(this.Request, requestBody));
                }

                return Task.FromResult(new HttpResponseMessage(HttpStatusCode.OK));
            }

            /// <summary>
            /// Gets the requested hosted service with variable details.
            /// </summary>
            /// <param name="subscriptionId">The subscription Id.</param>
            /// <param name="hostedServiceName">The target hosted service.</param>
            [HttpGet]
            [ActionName("GetHostedService")]
            public Task<HttpResponseMessage> GetHostedService(string subscriptionId, string hostedServiceName)
            {
                if (this.Request.RequestUri.ParseQueryString()["embed-detail"].EqualsInsensitively("true"))
                {
                    if (this.host.OnGetHostedServiceWithDetailsRequest != null)
                    {
                        return Task.FromResult(this.host.OnGetHostedServiceWithDetailsRequest(this.Request, null));
                    }
                }
                else if (this.host.OnGetHostedServiceRequest != null)
                {
                    return Task.FromResult(this.host.OnGetHostedServiceRequest(this.Request, null));
                }

                return Task.FromResult(new HttpResponseMessage(HttpStatusCode.OK));
            }

            /// <summary>
            /// Deletes the hosted service.
            /// </summary>
            /// <param name="subscriptionId">The subscription id.</param>
            /// <param name="hostedServiceName">The hosted service name.</param>
            [HttpDelete]
            [ActionName("DeleteHostedService")]
            public Task<HttpResponseMessage> DeleteHostedService(string subscriptionId, string hostedServiceName)
            {
                if (this.host.OnDeleteHostedServiceRequest != null)
                {
                    return Task.FromResult(this.host.OnDeleteHostedServiceRequest(this.Request, null));
                }

                return Task.FromResult(new HttpResponseMessage(HttpStatusCode.OK));
            }

            /// <summary>
            /// Updates the requested hosted service with variable details.
            /// </summary>
            /// <param name="subscriptionId">The subscription Id.</param>
            /// <param name="hostedServiceName">The target hosted service.</param>
            [HttpPut]
            [ActionName("UpdateHostedService")]
            public Task<HttpResponseMessage> UpdateHostedService(string subscriptionId, string hostedServiceName)
            {
                if (this.host.OnUpdateHostedServiceRequest != null)
                {
                    return Task.FromResult(this.host.OnUpdateHostedServiceRequest(this.Request, null));
                }

                return Task.FromResult(new HttpResponseMessage(HttpStatusCode.OK));
            }

            /// <summary>
            /// Creates the hosted services deployment.
            /// </summary>
            /// <param name="subscriptionId">The subscription identifier.</param>
            /// <param name="hostedServiceName">Name of the hosted service.</param>
            [HttpPost]
            [ActionName("CreateHostedServicesDeployment")]
            public Task<HttpResponseMessage> CreateHostedServicesDeployment(string subscriptionId, string hostedServiceName)
            {
                if (this.host.OnCreateHostedServicesDeployment != null)
                {
                    return Task.FromResult(this.host.OnCreateHostedServicesDeployment(this.Request, null));
                }

                return Task.FromResult(new HttpResponseMessage(HttpStatusCode.OK));
            }

            /// <summary>
            /// Performs a migration operation on a deployment.
            /// </summary>
            /// <param name="subscriptionId">The subscription identifier.</param>
            /// <param name="deploymentName">The deployment name.</param>
            [HttpPost]
            [ActionName("DeploymentMigration")]
            public Task<HttpResponseMessage> DeploymentMigration(string subscriptionId, string deploymentName)
            {
                var requestBody = this.Request.Content.ReadAsStringAsync().Result;

                var compValue = this.Request.RequestUri.ParseQueryString()["comp"].ToLowerInvariant();

                var asyncResponse = this.Request.CreateResponse(statusCode: HttpStatusCode.Accepted);
                asyncResponse.Headers.Add("x-ms-request-id", Guid.NewGuid().ToString("N"));

                switch (compValue)
                {
                    case "validate":
                        return this.host.OnValidateDeploymentMigration != null
                            ? Task.FromResult(this.host.OnValidateDeploymentMigration(this.Request, requestBody))
                            : Task.FromResult(new HttpResponseMessage(HttpStatusCode.OK));

                    case "prepare":
                        return this.host.OnPrepareDeploymentMigration != null
                            ? Task.FromResult(this.host.OnPrepareDeploymentMigration(this.Request, requestBody))
                            : Task.FromResult(asyncResponse);

                    case "commit":
                        return this.host.OnCommitDeploymentMigration != null
                            ? Task.FromResult(this.host.OnCommitDeploymentMigration(this.Request, requestBody))
                            : Task.FromResult(asyncResponse);

                    case "abort":
                        return this.host.OnAbortDeploymentMigration != null
                            ? Task.FromResult(this.host.OnAbortDeploymentMigration(this.Request, requestBody))
                            : Task.FromResult(asyncResponse);

                    default:
                        return Task.FromResult(new HttpResponseMessage(HttpStatusCode.BadRequest));
                }
            }

            /// <summary>
            /// Gets the requested hosted service with variable details.
            /// </summary>
            /// <param name="subscriptionId">The subscription Id.</param>
            /// <param name="hostedServiceName">The target hosted service.</param>
            /// <param name="slotName">The slot name.</param>
            [HttpGet]
            [ActionName("GetHostedServiceDeploymentBySlot")]
            public Task<HttpResponseMessage> GetHostedServiceDeploymentBySlot(string subscriptionId, string hostedServiceName, string slotName)
            {
                if (this.host.OnGetHostedServiceDeploymentBySlotRequest != null)
                {
                    return Task.FromResult(this.host.OnGetHostedServiceDeploymentBySlotRequest(this.Request, null));
                }

                return Task.FromResult(new HttpResponseMessage(HttpStatusCode.OK));
            }

            /// <summary>
            /// Creates or Update deployment/configuration in the specified deployment slot.
            /// </summary>
            /// <param name="subscriptionId">The subscription Id.</param>
            /// <param name="hostedServiceName">The target hosted service.</param>
            /// <param name="slotName">The slot name.</param>
            [HttpPost]
            [ActionName("CreateUpdateDeploymentSlot")]
            public Task<HttpResponseMessage> CreateUpdateDeploymentSlot(string subscriptionId, string hostedServiceName, string slotName)
            {
                var compValue = this.Request.RequestUri.ParseQueryString()["comp"];

                if (compValue == null)
                {
                    if (this.host.OnCreateHostedServiceDeploymentBySlot != null)
                    {
                        return Task.FromResult(this.host.OnCreateHostedServiceDeploymentBySlot(this.Request, null));
                    }
                }
                else if (compValue.EqualsInsensitively("config"))
                {
                    if (this.host.OnChangeConfigurationBySlot != null)
                    {
                        return Task.FromResult(this.host.OnChangeConfigurationBySlot(this.Request, null));
                    }
                }
                else if (compValue.EqualsInsensitively("status"))
                {
                    if (this.host.OnUpdateHostedServiceDeploymentBySlot != null)
                    {
                        return Task.FromResult(this.host.OnUpdateHostedServiceDeploymentBySlot(this.Request, null));
                    }
                }
                else if (compValue.EqualsInsensitively("upgrade"))
                {
                    if (this.host.OnUpgradeHostedSerivceDeploymentBySlot != null)
                    {
                        return Task.FromResult(this.host.OnUpgradeHostedSerivceDeploymentBySlot(this.Request, null));
                    }
                }
                else if (compValue.EqualsInsensitively("walkupgradedomain"))
                {
                    // OnWalkUpgradeDomainBySlot
                }

                return Task.FromResult(new HttpResponseMessage(HttpStatusCode.OK));
            }

            /// <summary>
            /// Deleted the requested hosted service by deployment slot.
            /// </summary>
            /// <param name="subscriptionId">The subscription Id.</param>
            /// <param name="hostedServiceName">The target hosted service.</param>
            /// <param name="slotName">The slot name.</param>
            [HttpDelete]
            [ActionName("DeleteHostedServiceDeploymentBySlot")]
            public Task<HttpResponseMessage> DeleteHostedServiceDeploymentBySlot(string subscriptionId, string hostedServiceName, string slotName)
            {
                if (this.host.OnDeleteHostedServiceDeploymentBySlot != null)
                {
                    return Task.FromResult(this.host.OnDeleteHostedServiceDeploymentBySlot(this.Request, null));
                }

                return Task.FromResult(new HttpResponseMessage(HttpStatusCode.OK));
            }

            /// <summary>
            /// Swap the requested hosted service.
            /// </summary>
            /// <param name="subscriptionId">The subscription Id.</param>
            /// <param name="hostedServiceName">The target hosted service.</param>
            [HttpPost]
            [ActionName("SwapHostedServiceDeployment")]
            public Task<HttpResponseMessage> SwapHostedServiceDeployment(string subscriptionId, string hostedServiceName)
            {
                if (this.host.OnSwapHostedServiceDeployment != null)
                {
                    return Task.FromResult(this.host.OnSwapHostedServiceDeployment(this.Request, null));
                }

                return Task.FromResult(new HttpResponseMessage(HttpStatusCode.OK));
            }

            /// <summary>
            /// Mocks where the configuration will be retrieved.
            /// </summary>
            /// <param name="subscriptionId">The subscription id.</param>
            /// <param name="hostedServiceName">The hosted service name.</param>
            /// <param name="slotName">The slot name.</param>
            [HttpGet]
            [ActionName("GetConfiguration")]
            public Task<HttpResponseMessage> GetConfiguration(string subscriptionId, string hostedServiceName, string slotName)
            {
                if (this.host.OnGetConfiguration != null)
                {
                    return Task.FromResult(this.host.OnGetConfiguration(this.Request, null));
                }

                return Task.FromResult(new HttpResponseMessage(HttpStatusCode.OK));
            }

            /// <summary>
            /// Download RDP file of the role instance.
            /// </summary>
            /// <param name="subscriptionId">The subscription Id.</param>
            /// <param name="hostedServiceName">The hosted service name.</param>
            /// <param name="deploymentName">The deployment name.</param>
            /// <param name="roleInstanceName">The role instance name.</param>
            [HttpGet]
            [ActionName("DownloadRemoteDesktopConnectionFile")]
            public Task<HttpResponseMessage> DownloadRemoteDesktopConnectionFile(string subscriptionId, string hostedServiceName, string deploymentName, string roleInstanceName)
            {
                if (this.host.OnRoleInstanceDownloadRemoteDesktopConnectionFile != null)
                {
                    return Task.FromResult(this.host.OnRoleInstanceDownloadRemoteDesktopConnectionFile(this.Request, null));
                }

                return Task.FromResult(new HttpResponseMessage(HttpStatusCode.OK));
            }

            /// <summary>
            /// Operation on the role instance.
            /// </summary>
            /// <param name="subscriptionId">The subscription Id.</param>
            /// <param name="hostedServiceName">The hosted service name.</param>
            /// <param name="slotName">The slot name.</param>
            /// <param name="roleInstanceName">The role instance name.</param>
            [HttpPost]
            [ActionName("OperationOnRoleInstance")]
            public Task<HttpResponseMessage> OperationDeploymentRoleInstanceBySlot(string subscriptionId, string hostedServiceName, string slotName, string roleInstanceName)
            {
                if (this.host.OnOperationDeploymentRoleInstanceBySlot != null)
                {
                    return Task.FromResult(this.host.OnOperationDeploymentRoleInstanceBySlot(this.Request, null));
                }

                return Task.FromResult(new HttpResponseMessage(HttpStatusCode.OK));
            }

            /// <summary>
            /// Operation on the virtual machine.
            /// </summary>
            /// <param name="subscriptionId">The subscription Id.</param>
            /// <param name="hostedServiceName">The hosted service name.</param>
            /// <param name="slotName">The slot name.</param>
            /// <param name="roleInstanceName">The role instance name.</param>
            [HttpPost]
            [ActionName("OperationOnVirtualMachine")]
            public Task<HttpResponseMessage> VirtualMachineOperation(string subscriptionId, string hostedServiceName, string slotName, string roleInstanceName)
            {
                if (this.host.OnVirtualMachineOperation != null)
                {
                    return Task.FromResult(this.host.OnVirtualMachineOperation(this.Request, null));
                }

                return Task.FromResult(new HttpResponseMessage(HttpStatusCode.OK));
            }

            /// <summary>
            /// Adds the data disk to hosted service.
            /// </summary>
            /// <param name="subscriptionId">The subscription Id.</param>
            /// <param name="hostedServiceName">The target hosted service.</param>
            /// <param name="deploymentName">The deployment name.</param>
            /// <param name="roleName">The role name.</param>
            [HttpPost]
            public Task<HttpResponseMessage> AddHostedServiceDataDisk(string subscriptionId, string hostedServiceName, string deploymentName, string roleName)
            {
                if (this.host.OnAddHostedServiceDataDiskRequest != null)
                {
                    return Task.FromResult(this.host.OnAddHostedServiceDataDiskRequest(this.Request, null));
                }

                return Task.FromResult(new HttpResponseMessage(HttpStatusCode.OK));
            }

            /// <summary>
            /// Updates the data disk to hosted service.
            /// </summary>
            /// <param name="subscriptionId">The subscription Id.</param>
            /// <param name="hostedServiceName">The target hosted service.</param>
            /// <param name="deploymentName">The deployment name.</param>
            /// <param name="roleName">The role name.</param>
            /// <param name="lun">The logical unit number.</param>
            [HttpPut]
            public Task<HttpResponseMessage> UpdateHostedServiceDataDisk(string subscriptionId, string hostedServiceName, string deploymentName, string roleName, int lun)
            {
                if (this.host.OnUpdateHostedServiceDataDiskRequest != null)
                {
                    return Task.FromResult(this.host.OnUpdateHostedServiceDataDiskRequest(this.Request, null));
                }

                return Task.FromResult(new HttpResponseMessage(HttpStatusCode.OK));
            }

            /// <summary>
            /// Gets the data disk from the hosted service.
            /// </summary>
            /// <param name="subscriptionId">The subscription Id.</param>
            /// <param name="hostedServiceName">The target hosted service.</param>
            /// <param name="deploymentName">The deployment name.</param>
            /// <param name="roleName">The role name.</param>
            /// <param name="lun">The logical unit number.</param>
            [HttpGet]
            public Task<HttpResponseMessage> GetHostedServiceDataDisk(string subscriptionId, string hostedServiceName, string deploymentName, string roleName, int lun)
            {
                if (this.host.OnGetHostedServiceDataDiskRequest != null)
                {
                    return Task.FromResult(this.host.OnGetHostedServiceDataDiskRequest(this.Request, null, lun));
                }

                return Task.FromResult(new HttpResponseMessage(HttpStatusCode.OK));
            }

            /// <summary>
            /// Deletes the data disk to hosted service.
            /// </summary>
            /// <param name="subscriptionId">The subscription Id.</param>
            /// <param name="hostedServiceName">The target hosted service.</param>
            /// <param name="deploymentName">The deployment name.</param>
            /// <param name="roleName">The role name.</param>
            /// <param name="lun">The logical unit number.</param>
            [HttpDelete]
            public Task<HttpResponseMessage> DeleteHostedServiceDataDisk(string subscriptionId, string hostedServiceName, string deploymentName, string roleName, int lun)
            {
                if (this.host.OnDeleteHostedServiceDataDiskRequest != null)
                {
                    return Task.FromResult(this.host.OnDeleteHostedServiceDataDiskRequest(this.Request, null));
                }

                return Task.FromResult(new HttpResponseMessage(HttpStatusCode.OK));
            }

            /// <summary>
            /// Gets the requested hosted service certificates.
            /// </summary>
            /// <param name="subscriptionId">The subscription Id.</param>
            /// <param name="hostedServiceName">The target hosted service.</param>
            [HttpGet]
            [ActionName("listcertificates")]
            public Task<HttpResponseMessage> GetHostedServiceCertificates(string subscriptionId, string hostedServiceName)
            {
                if (this.host.OnListCertificatesRequest != null)
                {
                    return Task.FromResult(this.host.OnListCertificatesRequest(this.Request, null));
                }

                return Task.FromResult(new HttpResponseMessage(HttpStatusCode.OK));
            }

            /// <summary>
            /// Gets the requested hosted service certificates.
            /// </summary>
            /// <param name="subscriptionId">The subscription Id.</param>
            /// <param name="hostedServiceName">The target hosted service.</param>
            [HttpPost]
            [ActionName("addcertificate")]
            public Task<HttpResponseMessage> AddHostedServiceCertificate(string subscriptionId, string hostedServiceName)
            {
                if (this.host.OnAddCertificateRequest != null)
                {
                    return Task.FromResult(this.host.OnAddCertificateRequest(this.Request, null));
                }

                return Task.FromResult(new HttpResponseMessage(HttpStatusCode.OK));
            }

            /// <summary>
            /// Gets the requested hosted service certificate.
            /// </summary>
            /// <param name="subscriptionId">The subscription Id.</param>
            /// <param name="hostedServiceName">The target hosted service.</param>
            /// <param name="thumbprint">The certificate thumbprint.</param>
            [HttpGet]
            [ActionName("getcertificate")]
            public Task<HttpResponseMessage> GetHostedServiceCertificate(string subscriptionId, string hostedServiceName, string thumbprint)
            {
                if (this.host.OnGetCertificateRequest != null)
                {
                    return Task.FromResult(this.host.OnGetCertificateRequest(this.Request, null));
                }

                return Task.FromResult(new HttpResponseMessage(HttpStatusCode.OK));
            }

            /// <summary>
            /// Deletes the requested hosted service certificate.
            /// </summary>
            /// <param name="subscriptionId">The subscription Id.</param>
            /// <param name="hostedServiceName">The target hosted service.</param>
            /// <param name="thumbprint">The certificate thumbprint.</param>
            [HttpDelete]
            [ActionName("deletecertificate")]
            public Task<HttpResponseMessage> DeleteHostedServiceCertificate(string subscriptionId, string hostedServiceName, string thumbprint)
            {
                if (this.host.OnDeleteCertificateRequest != null)
                {
                    return Task.FromResult(this.host.OnDeleteCertificateRequest(this.Request, null));
                }

                return Task.FromResult(new HttpResponseMessage(HttpStatusCode.OK));
            }

            /// <summary>
            /// Gets the hosted service extensions.
            /// </summary>
            /// <param name="subscriptionId">The subscription Id.</param>
            /// <param name="hostedServiceName">The target hosted service.</param>
            [HttpGet]
            [ActionName("listextensions")]
            public Task<HttpResponseMessage> GetHostedServiceExtensions(string subscriptionId, string hostedServiceName)
            {
                if (this.host.OnListExtensionsRequest != null)
                {
                    return Task.FromResult(this.host.OnListExtensionsRequest(this.Request, null));
                }

                return Task.FromResult(new HttpResponseMessage(HttpStatusCode.OK));
            }

            /// <summary>
            /// Add a hosted service extension.
            /// </summary>
            /// <param name="subscriptionId">The subscription Id.</param>
            /// <param name="hostedServiceName">The target hosted service.</param>
            [HttpPost]
            [ActionName("addextension")]
            public Task<HttpResponseMessage> AddHostedServiceExtension(string subscriptionId, string hostedServiceName)
            {
                if (this.host.OnAddExtensionRequest != null)
                {
                    return Task.FromResult(this.host.OnAddExtensionRequest(this.Request, null));
                }

                return Task.FromResult(new HttpResponseMessage(HttpStatusCode.OK));
            }

            /// <summary>
            /// Gets the requested hosted service extension.
            /// </summary>
            /// <param name="subscriptionId">The subscription Id.</param>
            /// <param name="hostedServiceName">The target hosted service.</param>
            /// <param name="extensionId">The extension Id.</param>
            [HttpGet]
            [ActionName("getextension")]
            public Task<HttpResponseMessage> GetHostedServiceExtension(string subscriptionId, string hostedServiceName, string extensionId)
            {
                if (this.host.OnGetExtensionRequest != null)
                {
                    return Task.FromResult(this.host.OnGetExtensionRequest(this.Request, null));
                }

                return Task.FromResult(new HttpResponseMessage(HttpStatusCode.OK));
            }

            /// <summary>
            /// Deletes the requested hosted service extension.
            /// </summary>
            /// <param name="subscriptionId">The subscription Id.</param>
            /// <param name="hostedServiceName">The target hosted service.</param>
            /// <param name="extensionId">The extension Id.</param>
            [HttpDelete]
            [ActionName("deleteextension")]
            public Task<HttpResponseMessage> DeleteHostedServiceExtension(string subscriptionId, string hostedServiceName, string extensionId)
            {
                if (this.host.OnDeleteExtensionRequest != null)
                {
                    return Task.FromResult(this.host.OnDeleteExtensionRequest(this.Request, null));
                }

                return Task.FromResult(new HttpResponseMessage(HttpStatusCode.OK));
            }

            /// <summary>
            /// Adds the deployment role.
            /// </summary>
            /// <param name="subscriptionId">The subscription id.</param>
            /// <param name="hostedServiceName">The service name.</param>
            /// <param name="deploymentName">The deployment name.</param>
            [HttpPost]
            [ActionName("addRole")]
            public Task<HttpResponseMessage> AddRole(string subscriptionId, string hostedServiceName, string deploymentName)
            {
                if (this.host.OnAddRole != null)
                {
                    return Task.FromResult(this.host.OnAddRole(this.Request, null));
                }

                return Task.FromResult(new HttpResponseMessage(HttpStatusCode.OK));
            }

            /// <summary>
            /// Puts the deployment role.
            /// </summary>
            /// <param name="subscriptionId">The subscription id.</param>
            /// <param name="hostedServiceName">The service name.</param>
            /// <param name="deploymentName">The deployment name.</param>
            /// <param name="roleName">The role name.</param>
            [HttpPut]
            [ActionName("putRole")]
            public Task<HttpResponseMessage> PutRole(string subscriptionId, string hostedServiceName, string deploymentName, string roleName)
            {
                if (this.host.OnPutRole != null)
                {
                    return Task.FromResult(this.host.OnPutRole(this.Request, null));
                }

                return Task.FromResult(new HttpResponseMessage(HttpStatusCode.OK));
            }
        }

        /// <summary>
        /// The cloud service request controller.
        /// </summary>
        public class CloudServiceTestController : ApiController
        {
            /// <summary>
            /// The legacy cloud service test host.
            /// </summary>
            private readonly LegacyManagementApiTestHost host;

            /// <summary>
            /// Initializes a new instance of the <see cref="CloudServiceTestController"/> class.
            /// </summary>
            /// <param name="host">The legacy cloud service test host.</param>
            public CloudServiceTestController(LegacyManagementApiTestHost host)
            {
                this.host = host;
            }

            /// <summary>
            /// Gets the requested cloud service with variable details.
            /// </summary>
            /// <param name="subscriptionId">The subscription Id containing the cloud service.</param>
            /// <param name="cloudServiceName">The target cloud service.</param>
            [HttpGet]
            public Task<HttpResponseMessage> GetCloudService(string subscriptionId, string cloudServiceName)
            {
                if (this.Request.RequestUri.ParseQueryString().HasKeys())
                {
                    if (this.host.OnGetCloudServiceWithDetailsRequest != null)
                    {
                        return Task.FromResult(this.host.OnGetCloudServiceWithDetailsRequest(this.Request, null));
                    }
                }
                else if (this.host.OnGetHostedServiceDeploymentBySlotRequest != null)
                {
                    return Task.FromResult(this.host.OnGetHostedServiceDeploymentBySlotRequest(this.Request, null));
                }

                return Task.FromResult(new HttpResponseMessage(HttpStatusCode.OK));
            }

            /// <summary>
            /// Deletes the requested cloud service.
            /// </summary>
            /// <param name="subscriptionId">The subscription Id containing the cloud service.</param>
            /// <param name="cloudServiceName">The target cloud service.</param>
            [HttpDelete]
            public Task<HttpResponseMessage> DeleteCloudService(string subscriptionId, string cloudServiceName)
            {
                if (this.host.OnDeleteCloudServiceRequest != null)
                {
                    return Task.FromResult(this.host.OnDeleteCloudServiceRequest(this.Request, null));
                }

                return Task.FromResult(new HttpResponseMessage(HttpStatusCode.OK));
            }

            /// <summary>
            /// Deletes the requested cloud service.
            /// </summary>
            /// <param name="subscriptionId">The subscription Id containing the cloud service.</param>
            /// <param name="cloudServiceName">The target cloud service.</param>
            /// <param name="requestBody">The request body that was provided.</param>
            [HttpPut]
            public Task<HttpResponseMessage> PutCloudService(string subscriptionId, string cloudServiceName, [FromBody] string requestBody)
            {
                if (this.host.OnPutCloudServiceRequest != null)
                {
                    return Task.FromResult(this.host.OnPutCloudServiceRequest(this.Request, requestBody));
                }

                return Task.FromResult(new HttpResponseMessage(HttpStatusCode.OK));
            }
        }

        /// <summary>
        /// The operation request controller.
        /// </summary>
        public class OperationTestController : ApiController
        {
            /// <summary>
            /// The legacy cloud service test host.
            /// </summary>
            private readonly LegacyManagementApiTestHost host;

            /// <summary>
            /// Initializes a new instance of the <see cref="OperationTestController"/> class.
            /// </summary>
            /// <param name="host">The legacy cloud service test host.</param>
            public OperationTestController(LegacyManagementApiTestHost host)
            {
                this.host = host;
            }

            /// <summary>
            /// Gets the requested operation status
            /// </summary>
            /// <param name="subscriptionId">The subscription Id.</param>
            /// <param name="operationId">The operation Id.</param>
            [HttpGet]
            public Task<HttpResponseMessage> GetOperation(string subscriptionId, string operationId)
            {
                if (this.host.OnGetOperationStatusRequest != null)
                {
                    return Task.FromResult(this.host.OnGetOperationStatusRequest(this.Request, null));
                }

                return Task.FromResult(new HttpResponseMessage(HttpStatusCode.OK));
            }
        }

        /// <summary>
        /// The disk request controller.
        /// </summary>
        public class DiskTestController : ApiController
        {
            /// <summary>
            /// The legacy disk test host.
            /// </summary>
            private readonly LegacyManagementApiTestHost host;

            /// <summary>
            /// Initializes a new instance of the <see cref="DiskTestController"/> class.
            /// </summary>
            /// <param name="host">The legacy disk test host.</param>
            public DiskTestController(LegacyManagementApiTestHost host)
            {
                this.host = host;
            }

            /// <summary>
            /// Gets the registered disks in subscription.
            /// </summary>
            /// <param name="subscriptionId">The subscription Id.</param>
            [HttpGet]
            public Task<HttpResponseMessage> GetDisks(string subscriptionId)
            {
                if (this.host.OnListDisksRequest != null)
                {
                    return Task.FromResult(this.host.OnListDisksRequest(this.Request, null));
                }

                return Task.FromResult(new HttpResponseMessage(HttpStatusCode.OK));
            }

            /// <summary>
            /// Registers the disks in subscription.
            /// </summary>
            /// <param name="subscriptionId">The subscription Id.</param>
            [HttpPost]
            public Task<HttpResponseMessage> AddDisk(string subscriptionId)
            {
                if (this.host.OnAddDiskRequest != null)
                {
                    return Task.FromResult(this.host.OnAddDiskRequest(this.Request, null));
                }

                return Task.FromResult(new HttpResponseMessage(HttpStatusCode.OK));
            }

            /// <summary>
            /// Registers the disks in subscription.
            /// </summary>
            /// <param name="subscriptionId">The subscription Id.</param>
            /// <param name="diskName">The disk name.</param>
            [HttpPut]
            public Task<HttpResponseMessage> UpdateDisk(string subscriptionId, string diskName)
            {
                if (this.host.OnUpdateDiskRequest != null)
                {
                    return Task.FromResult(this.host.OnUpdateDiskRequest(this.Request, diskName));
                }

                return Task.FromResult(new HttpResponseMessage(HttpStatusCode.OK));
            }

            /// <summary>
            /// Gets the registered disk in subscription.
            /// </summary>
            /// <param name="subscriptionId">The subscription Id.</param>
            /// <param name="diskName">The disk name.</param>
            [HttpGet]
            public Task<HttpResponseMessage> GetDisks(string subscriptionId, string diskName)
            {
                if (this.host.OnGetDiskRequest != null)
                {
                    return Task.FromResult(this.host.OnGetDiskRequest(this.Request, diskName));
                }

                return Task.FromResult(new HttpResponseMessage(HttpStatusCode.OK));
            }

            /// <summary>
            /// Deletes disk.
            /// </summary>
            /// <param name="subscriptionId">The subscription id.</param>
            /// <param name="diskName">The disk name.</param>
            [HttpDelete]
            public Task<HttpResponseMessage> DeleteDisk(string subscriptionId, string diskName)
            {
                if (this.host.OnDeleteDiskRequest != null)
                {
                    return Task.FromResult(this.host.OnDeleteDiskRequest(this.Request, diskName));
                }

                return Task.FromResult(new HttpResponseMessage(HttpStatusCode.OK));
            }
        }

        /// <summary>
        /// The legacy subscription controller.
        /// </summary>
        public class SubscriptionTestController : ApiController
        {
            /// <summary>
            /// The legacy cloud service test host.
            /// </summary>
            private readonly LegacyManagementApiTestHost host;

            /// <summary>
            /// Initializes a new instance of the <see cref="SubscriptionTestController"/> class.
            /// </summary>
            /// <param name="host">The legacy cloud service test host.</param>
            public SubscriptionTestController(LegacyManagementApiTestHost host)
            {
                this.host = host;
            }

            /// <summary>
            /// Gets the requested subscription with details.
            /// </summary>
            /// <param name="subscriptionId">The subscription Id.</param>
            [HttpGet]
            public Task<HttpResponseMessage> GetSubscriptionWithDetails(string subscriptionId)
            {
                if (this.host.OnGetSubscriptionWithDetailsRequest != null)
                {
                    return Task.FromResult(this.host.OnGetSubscriptionWithDetailsRequest(this.Request, null));
                }

                return Task.FromResult(new HttpResponseMessage(HttpStatusCode.OK));
            }

            /// <summary>
            /// Gets the requested subscription.
            /// </summary>
            /// <param name="subscriptionId">The subscription Id.</param>
            [HttpGet]
            public Task<HttpResponseMessage> GetSubscription(string subscriptionId)
            {
                if (this.host.OnGetSubscriptionRequest != null)
                {
                    return Task.FromResult(this.host.OnGetSubscriptionRequest(this.Request, null));
                }

                return Task.FromResult(new HttpResponseMessage(HttpStatusCode.OK));
            }

            /// <summary>
            /// List subscriptions by JWT token.
            /// </summary>
            [HttpGet]
            public Task<HttpResponseMessage> ListSubscriptions()
            {
                if (this.host.OnListSubscriptionsRequest != null)
                {
                    return Task.FromResult(this.host.OnListSubscriptionsRequest(this.Request, null));
                }

                return Task.FromResult(new HttpResponseMessage(HttpStatusCode.OK));
            }

            /// <summary>
            /// Move all resources from source to target subscription.
            /// </summary>
            /// <param name="subscriptionId">The subscription Id.</param>
            /// <param name="targetSubscriptionId">The target subscription Id.</param>
            [HttpPost]
            public Task<HttpResponseMessage> MoveSubscriptionResources(string subscriptionId, string targetSubscriptionId)
            {
                if (this.host.OnMoveSubscriptionResourcesRequest != null)
                {
                    return Task.FromResult(this.host.OnMoveSubscriptionResourcesRequest(this.Request, null));
                }

                return Task.FromResult(new HttpResponseMessage(HttpStatusCode.OK));
            }

            /// <summary>
            /// Get the status of a subscription for moving classic resources.
            /// </summary>
            /// <param name="subscriptionId">The subscription Id.</param>
            [HttpGet]
            public Task<HttpResponseMessage> GetSubscriptionTransferStatus(string subscriptionId)
            {
                if (this.host.OnGetSubscriptionTransferStatusRequest != null)
                {
                    return Task.FromResult(this.host.OnGetSubscriptionTransferStatusRequest(this.Request, null));
                }

                return Task.FromResult(new HttpResponseMessage(HttpStatusCode.OK));
            }
        }

        /// <summary>
        /// The legacy subscription principals controller.
        /// </summary>
        public class PrincipalsTestController : ApiController
        {
            /// <summary>
            /// The legacy cloud service test host.
            /// </summary>
            private readonly LegacyManagementApiTestHost host;

            /// <summary>
            /// Initializes a new instance of the <see cref="PrincipalsTestController"/> class.
            /// </summary>
            /// <param name="host">The legacy cloud service test host.</param>
            public PrincipalsTestController(LegacyManagementApiTestHost host)
            {
                this.host = host;
            }

            /// <summary>
            /// Gets the requested subscription principals.
            /// </summary>
            /// <param name="subscriptionId">The subscription Id.</param>
            [HttpGet]
            public Task<HttpResponseMessage> GetSubscriptionPrincipals(string subscriptionId)
            {
                if (this.host.OnGetSubscriptionPrincipalsRequest != null)
                {
                    return Task.FromResult(this.host.OnGetSubscriptionPrincipalsRequest(this.Request, null));
                }

                return Task.FromResult(new HttpResponseMessage(HttpStatusCode.OK));
            }

            /// <summary>
            /// Gets the requested subscription principals.
            /// </summary>
            /// <param name="subscriptionId">The subscription Id.</param>
            [HttpPost]
            public Task<HttpResponseMessage> PutSubscriptionPrincipal(string subscriptionId)
            {
                var requestBody = this.Request.Content.ReadAsStringAsync().Result;
                if (this.host.OnPutSubscriptionPrincipalRequest != null)
                {
                    return Task.FromResult(this.host.OnPutSubscriptionPrincipalRequest(this.Request, requestBody));
                }

                return Task.FromResult(new HttpResponseMessage(HttpStatusCode.OK));
            }

            /// <summary>
            /// Gets the requested subscription principals.
            /// </summary>
            /// <param name="subscriptionId">The subscription Id.</param>
            /// <param name="principalId">The principal Id.</param>
            [HttpGet]
            public Task<HttpResponseMessage> GetSubscriptionPrincipal(string subscriptionId, string principalId)
            {
                if (this.host.OnGetSubscriptionPrincipalRequest != null)
                {
                    return Task.FromResult(this.host.OnGetSubscriptionPrincipalRequest(this.Request, null));
                }

                return Task.FromResult(new HttpResponseMessage(HttpStatusCode.OK));
            }

            /// <summary>
            /// Gets the requested subscription principals.
            /// </summary>
            /// <param name="subscriptionId">The subscription Id.</param>
            /// <param name="principalId">The principal Id.</param>
            [HttpDelete]
            public Task<HttpResponseMessage> DeleteSubscriptionPrincipal(string subscriptionId, string principalId)
            {
                if (this.host.OnDeleteSubscriptionPrincipalRequest != null)
                {
                    return Task.FromResult(this.host.OnDeleteSubscriptionPrincipalRequest(this.Request, null));
                }

                return Task.FromResult(new HttpResponseMessage(HttpStatusCode.OK));
            }
        }

        /// <summary>
        /// The legacy resource request controller.
        /// </summary>
        public class ResourcesTestController : ApiController
        {
            /// <summary>
            /// The legacy cloud service test host.
            /// </summary>
            private readonly LegacyManagementApiTestHost host;

            /// <summary>
            /// Initializes a new instance of the <see cref="ResourcesTestController"/> class.
            /// </summary>
            /// <param name="host">The legacy cloud service test host.</param>
            public ResourcesTestController(LegacyManagementApiTestHost host)
            {
                this.host = host;
            }

            /// <summary>
            /// Deletes the requested resource.
            /// </summary>
            /// <param name="subscriptionId">The subscriptionId containing the resource.</param>
            /// <param name="cloudServiceName">The target cloud service.</param>
            /// <param name="resourceProviderNamespace">The target provider namespace.</param>
            /// <param name="resourceType">The target resource type.</param>
            /// <param name="resourceName">The target resource name.</param>
            [HttpDelete]
            public Task<HttpResponseMessage> DeleteResource(string subscriptionId, string cloudServiceName, string resourceProviderNamespace, string resourceType, string resourceName)
            {
                if (this.host.OnDeleteResourceRequest != null)
                {
                    return Task.FromResult(this.host.OnDeleteResourceRequest(this.Request, null));
                }

                return Task.FromResult(new HttpResponseMessage(HttpStatusCode.OK));
            }

            /// <summary>
            /// Creates the provided resource.
            /// </summary>
            /// <param name="subscriptionId">The subscriptionId containing the resource.</param>
            /// <param name="cloudServiceName">The target cloud service.</param>
            /// <param name="resourceProviderNamespace">The target provider namespace.</param>
            /// <param name="resourceType">The target resource type.</param>
            /// <param name="resourceName">The target resource name.</param>
            /// <param name="requestBody">The request body that was provided.</param>
            [HttpPut]
            public Task<HttpResponseMessage> PutResource(string subscriptionId, string cloudServiceName, string resourceProviderNamespace, string resourceType, string resourceName, [FromBody] string requestBody)
            {
                if (this.host.OnPutResourceRequest != null)
                {
                    return Task.FromResult(this.host.OnPutResourceRequest(this.Request, requestBody));
                }

                return Task.FromResult(new HttpResponseMessage(HttpStatusCode.OK));
            }
        }

        /// <summary>
        /// The storage accounts test controller.
        /// </summary>
        public class StorageAccountTestController : ApiController
        {
            /// <summary>
            /// The legacy cloud service test host.
            /// </summary>
            private readonly LegacyManagementApiTestHost host;

            /// <summary>
            /// Initializes a new instance of the <see cref="StorageAccountTestController"/> class.
            /// </summary>
            /// <param name="host">The legacy cloud service test host.</param>
            public StorageAccountTestController(LegacyManagementApiTestHost host)
            {
                this.host = host;
            }

            /// <summary>
            /// Gets the requested subscription storage services.
            /// </summary>
            /// <param name="subscriptionId">The subscription Id.</param>
            [HttpGet]
            public Task<HttpResponseMessage> ListStorageServices(string subscriptionId)
            {
                if (this.host.OnListStorageServicesRequest != null)
                {
                    return Task.FromResult(this.host.OnListStorageServicesRequest(this.Request, null));
                }

                return Task.FromResult(new HttpResponseMessage(HttpStatusCode.OK));
            }

            /// <summary>
            /// Creates the requested subscription storage account.
            /// </summary>
            /// <param name="subscriptionId">The subscription Id.</param>
            /// <param name="requestBody">The request body.</param>
            [HttpPost]
            public Task<HttpResponseMessage> CreateStorageService(string subscriptionId, [FromBody] string requestBody)
            {
                if (this.host.OnCreateStorageServiceRequest != null)
                {
                    return Task.FromResult(this.host.OnCreateStorageServiceRequest(this.Request, requestBody));
                }

                return Task.FromResult(new HttpResponseMessage(HttpStatusCode.OK));
            }

            /// <summary>
            /// Gets the requested subscription storage service.
            /// </summary>
            /// <param name="subscriptionId">The subscription Id.</param>
            /// <param name="serviceName">The service name.</param>
            [HttpGet]
            public Task<HttpResponseMessage> GetStorageService(string subscriptionId, string serviceName)
            {
                if (this.host.OnGetStorageServiceRequest != null)
                {
                    return Task.FromResult(this.host.OnGetStorageServiceRequest(this.Request, null));
                }

                return Task.FromResult(new HttpResponseMessage(HttpStatusCode.OK));
            }

            /// <summary>
            /// Updates the requested subscription storage account.
            /// </summary>
            /// <param name="subscriptionId">The subscription Id.</param>
            /// <param name="serviceName">The account name.</param>
            [HttpPut]
            public Task<HttpResponseMessage> UpdateStorageService(string subscriptionId, string serviceName)
            {
                if (this.host.OnUpdateStorageServiceRequest != null)
                {
                    return Task.FromResult(this.host.OnUpdateStorageServiceRequest(this.Request, null));
                }

                return Task.FromResult(new HttpResponseMessage(HttpStatusCode.OK));
            }

            /// <summary>
            /// Deletes the storage service.
            /// </summary>
            /// <param name="subscriptionId">The subscription Id.</param>
            /// <param name="serviceName">The service name.</param>
            [HttpDelete]
            public Task<HttpResponseMessage> DeleteStorageService(string subscriptionId, string serviceName)
            {
                if (this.host.OnDeleteStorageServiceRequest != null)
                {
                    return Task.FromResult(this.host.OnDeleteStorageServiceRequest(this.Request, null));
                }

                return Task.FromResult(new HttpResponseMessage(HttpStatusCode.OK));
            }

            /// <summary>
            /// Performs a migration operation on a storage service.
            /// </summary>
            /// <param name="subscriptionId">The subscription identifier.</param>
            /// <param name="serviceName">The service name.</param>
            [HttpPost]
            [ActionName("StorageServiceMigration")]
            public Task<HttpResponseMessage> StorageServiceMigration(string subscriptionId, string serviceName)
            {
                string requestBody = this.Request.Content.ReadAsStringAsync().Result;

                var compValue = this.Request.RequestUri.ParseQueryString()["comp"].ToLowerInvariant();

                var asyncResponse = this.Request.CreateResponse(statusCode: HttpStatusCode.Accepted);
                asyncResponse.Headers.Add("x-ms-request-id", Guid.NewGuid().ToString("N"));

                switch (compValue)
                {
                    case "validate":
                        return this.host.OnValidateStorageServiceMigration != null
                            ? Task.FromResult(this.host.OnValidateStorageServiceMigration(this.Request, requestBody))
                            : Task.FromResult(new HttpResponseMessage(HttpStatusCode.OK));

                    case "prepare":
                        return this.host.OnPrepareStorageServiceMigration != null
                            ? Task.FromResult(this.host.OnPrepareStorageServiceMigration(this.Request, requestBody))
                            : Task.FromResult(asyncResponse);

                    case "commit":
                        return this.host.OnCommitStorageServiceMigration != null
                            ? Task.FromResult(this.host.OnCommitStorageServiceMigration(this.Request, requestBody))
                            : Task.FromResult(asyncResponse);

                    case "abort":
                        return this.host.OnAbortStorageServiceMigration != null
                            ? Task.FromResult(this.host.OnAbortStorageServiceMigration(this.Request, requestBody))
                            : Task.FromResult(asyncResponse);

                    default:
                        return Task.FromResult(new HttpResponseMessage(HttpStatusCode.BadRequest));
                }
            }
        }

        /// <summary>
        /// The storage keys controller.
        /// </summary>
        public class StorageKeysTestController : ApiController
        {
            /// <summary>
            /// The legacy cloud service test host.
            /// </summary>
            private readonly LegacyManagementApiTestHost host;

            /// <summary>
            /// Initializes a new instance of the <see cref="StorageKeysTestController"/> class.
            /// </summary>
            /// <param name="host">The legacy cloud service test host.</param>
            public StorageKeysTestController(LegacyManagementApiTestHost host)
            {
                this.host = host;
            }

            /// <summary>
            /// Gets the storage service keys.
            /// </summary>
            /// <param name="subscriptionId">The subscription Id.</param>
            /// <param name="serviceName">The service name.</param>
            [HttpGet]
            public Task<HttpResponseMessage> GetStorageKeys(string subscriptionId, string serviceName)
            {
                if (this.host.OnGetStorageKeysRequest != null)
                {
                    return Task.FromResult(this.host.OnGetStorageKeysRequest(this.Request, null));
                }

                return Task.FromResult(new HttpResponseMessage(HttpStatusCode.OK));
            }

            /// <summary>
            /// Regenerates the storage service keys.
            /// </summary>
            /// <param name="subscriptionId">The subscription Id.</param>
            /// <param name="serviceName">The service name.</param>
            /// <param name="requestBody">The request body.</param>
            [HttpPost]
            [ActionName("regenerate")]
            public Task<HttpResponseMessage> RegenerateStorageServiceKeys(string subscriptionId, string serviceName, [FromBody] string requestBody)
            {
                if (this.host.OnRegenerateStorageServiceKeysRequest != null)
                {
                    return Task.FromResult(this.host.OnRegenerateStorageServiceKeysRequest(this.Request, requestBody));
                }

                return Task.FromResult(new HttpResponseMessage(HttpStatusCode.OK));
            }
        }

        /// <summary>
        /// The legacy cloud service test host server.
        /// </summary>
        public class LegacyCloudServiceTestHostServer : ServiceTestHostServer
        {
            /// <summary>
            /// Initializes a new instance of the <see cref="LegacyCloudServiceTestHostServer"/> class.
            /// </summary>
            /// <param name="serviceUri">The service URI.</param>
            /// <param name="state">The instance state.</param>
            public LegacyCloudServiceTestHostServer(Uri serviceUri, object state)
                : base(serviceUri, state)
            {
            }

            /// <summary>
            /// Called to configure http configuration.
            /// </summary>
            /// <param name="configuration">The http configuration.</param>
            /// <param name="state">The instance state.</param>
            protected override void OnConfigure(HttpConfiguration configuration, object state)
            {
                (state as LegacyManagementApiTestHost).OnConfigure(configuration);
            }
        }

        /// <summary>
        /// The legacy cloud service test host controller selector.
        /// </summary>
        private class LegacyCloudServiceControllerSelector : DefaultHttpControllerSelector
        {
            /// <summary>
            /// The HTTP configuration.
            /// </summary>
            private readonly HttpConfiguration configuration;

            /// <summary>
            /// Initializes a new instance of the <see cref="LegacyCloudServiceControllerSelector" /> class.
            /// </summary>
            /// <param name="configuration">The configuration.</param>
            public LegacyCloudServiceControllerSelector(HttpConfiguration configuration)
                : base(configuration)
            {
                this.configuration = configuration;
            }

            /// <summary>
            /// Selects the controller.
            /// </summary>
            /// <param name="request">The request.</param>
            public override HttpControllerDescriptor SelectController(HttpRequestMessage request)
            {
                var matchedTypes = typeof(LegacyManagementApiTestHost)
                    .GetNestedTypes()
                    .Where(type => typeof(IHttpController).IsAssignableFrom(type))
                    .ToList();

                var controllerName = this.GetControllerName(request);
                var matchedController = matchedTypes.FirstOrDefault(type => type.Name.EqualsInsensitively(controllerName + "controller"));

                return new HttpControllerDescriptor(this.configuration, controllerName, matchedController);
            }
        }

        /// <summary>
        /// The legacy cloud service test host dependency resolver.
        /// </summary>
        private class LegacyCloudServiceDependencyResolver : IDependencyResolver
        {
            /// <summary>
            /// The legacy cloud service test host.
            /// </summary>
            private readonly LegacyManagementApiTestHost host;

            /// <summary>
            /// Initializes a new instance of the LegacyCloudServiceDependencyResolver class.
            /// </summary>
            /// <param name="host">The host.</param>
            public LegacyCloudServiceDependencyResolver(LegacyManagementApiTestHost host)
            {
                this.host = host;
            }

            /// <summary>
            /// Begins the scope.
            /// </summary>
            public IDependencyScope BeginScope()
            {
                return this;
            }

            /// <summary>
            /// Gets the service.
            /// </summary>
            /// <param name="serviceType">Type of the service.</param>
            public object GetService(Type serviceType)
            {
                if (typeof(IHttpController).IsAssignableFrom(serviceType))
                {
                    if (typeof(LegacyManagementApiTestHost).GetNestedTypes().Any(type => type == serviceType))
                    {
                        return Activator.CreateInstance(serviceType, args: new object[] { this.host });
                    }
                }

                return null;
            }

            /// <summary>
            /// Gets the services.
            /// </summary>
            /// <param name="serviceType">Type of the service.</param>
            public IEnumerable<object> GetServices(Type serviceType)
            {
                return Enumerable.Empty<object>();
            }

            /// <summary>
            /// Performs application-defined tasks associated with freeing, releasing, or resetting unmanaged resources.
            /// </summary>
            public void Dispose()
            {
            }
        }

        /// <summary>
        /// The virtual networks test controller.
        /// </summary>
        public class VirtualNetworksTestController : ApiController
        {
            /// <summary>
            /// The legacy cloud service test host.
            /// </summary>
            private readonly LegacyManagementApiTestHost host;

            /// <summary>
            /// Initializes a new instance of the <see cref="VirtualNetworksTestController"/> class.
            /// </summary>
            /// <param name="host">The legacy cloud service test host.</param>
            public VirtualNetworksTestController(LegacyManagementApiTestHost host)
            {
                this.host = host;
            }

            /// <summary>
            /// Gets the requested subscription storage services.
            /// </summary>
            /// <param name="subscriptionId">The subscription Id.</param>
            [HttpGet]
            public Task<HttpResponseMessage> GetNetworkConfiguration(string subscriptionId)
            {
                if (this.host.OnGetNetworkConfigurationRequest != null)
                {
                    return Task.FromResult(this.host.OnGetNetworkConfigurationRequest(this.Request, null));
                }

                return Task.FromResult(new HttpResponseMessage(HttpStatusCode.OK));
            }

            /// <summary>
            /// Creates the requested subscription storage account.
            /// </summary>
            /// <param name="subscriptionId">The subscription Id.</param>
            [HttpPut]
            public Task<HttpResponseMessage> SetNetworkConfiguration(string subscriptionId)
            {
                string requestBody = this.Request.Content.ReadAsStringAsync().Result;

                if (this.host.OnSetNetworkConfigurationRequest != null)
                {
                    return Task.FromResult(this.host.OnSetNetworkConfigurationRequest(this.Request, requestBody));
                }

                return Task.FromResult(new HttpResponseMessage(HttpStatusCode.OK));
            }

            /// <summary>
            /// Checks if IP Address is available on the virtual network.
            /// </summary>
            /// <param name="subscriptionId">The subscription Id.</param>
            /// <param name="virtualNetworkName">The virtual network name.</param>
            [HttpGet]
            [ActionName("CheckIPAddressAvailability")]
            public Task<HttpResponseMessage> CheckIPAddressAvailability(string subscriptionId, string virtualNetworkName)
            {
                string requestBody = this.Request.Content.ReadAsStringAsync().Result;

                if (this.host.OnCheckIpAddressOperation != null)
                {
                    return Task.FromResult(this.host.OnCheckIpAddressOperation(this.Request, requestBody));
                }

                return Task.FromResult(new HttpResponseMessage(HttpStatusCode.OK));
            }

            /// <summary>
            /// Performs a Migration operation on a virtual network.
            /// </summary>
            /// <param name="subscriptionId">The subscription identifier.</param>
            /// <param name="virtualNetworkName">The virtual network.</param>
            [HttpPost]
            [ActionName("VirtualNetworkMigration")]
            public Task<HttpResponseMessage> VirtualNetworkMigration(string subscriptionId, string virtualNetworkName)
            {
                string requestBody = this.Request.Content.ReadAsStringAsync().Result;

                var compValue = this.Request.RequestUri.ParseQueryString()["comp"].ToLowerInvariant();

                var operationId = Guid.NewGuid().ToString("N");
                var asyncResponse = this.Request.CreateResponse(statusCode: HttpStatusCode.Accepted);
                asyncResponse.Headers.Add("x-ms-request-id", operationId);

                switch (compValue)
                {
                    case "validate":
                        return this.host.OnValidateVirtualNetworkMigration != null
                            ? Task.FromResult(this.host.OnValidateVirtualNetworkMigration(this.Request, requestBody))
                            : Task.FromResult(new HttpResponseMessage(HttpStatusCode.OK));

                    case "prepare":
                        return this.host.OnPrepareVirtualNetworkMigration != null
                            ? Task.FromResult(this.host.OnPrepareVirtualNetworkMigration(this.Request, requestBody))
                            : Task.FromResult(asyncResponse);

                    case "commit":
                        return this.host.OnCommitVirtualNetworkMigration != null
                            ? Task.FromResult(this.host.OnCommitVirtualNetworkMigration(this.Request, requestBody))
                            : Task.FromResult(asyncResponse);

                    case "abort":
                        return this.host.OnAbortVirtualNetworkMigration != null
                            ? Task.FromResult(this.host.OnAbortVirtualNetworkMigration(this.Request, requestBody))
                            : Task.FromResult(asyncResponse);

                    default:
                        return Task.FromResult(new HttpResponseMessage(HttpStatusCode.BadRequest));
                }
            }
        }

        /// <summary>
        /// The affinity groups test controller.
        /// </summary>
        public class AffinityGroupsTestController : ApiController
        {
            /// <summary>
            /// The legacy cloud service test host.
            /// </summary>
            private readonly LegacyManagementApiTestHost host;

            /// <summary>
            /// Initializes a new instance of the <see cref="AffinityGroupsTestController"/> class.
            /// </summary>
            /// <param name="host">The legacy cloud service test host.</param>
            public AffinityGroupsTestController(LegacyManagementApiTestHost host)
            {
                this.host = host;
            }

            /// <summary>
            /// Lists the affinity groups.
            /// </summary>
            /// <param name="subscriptionId">The subscription id.</param>
            [HttpGet]
            public Task<HttpResponseMessage> ListAffinityGroups(string subscriptionId)
            {
                if (this.host.OnListAffinityGroupsRequest != null)
                {
                    return Task.FromResult(this.host.OnListAffinityGroupsRequest(this.Request, null));
                }

                return Task.FromResult(new HttpResponseMessage(HttpStatusCode.OK));
            }

            /// <summary>
            /// Get the affinity group.
            /// </summary>
            /// <param name="subscriptionId">The subscription id.</param>
            /// <param name="affinityGroupName">The affinity group name.</param>
            [HttpGet]
            public Task<HttpResponseMessage> GetAffinityGroup(string subscriptionId, string affinityGroupName)
            {
                if (this.host.OnGetAffinityGroupRequest != null)
                {
                    return Task.FromResult(this.host.OnGetAffinityGroupRequest(this.Request, null));
                }

                return Task.FromResult(new HttpResponseMessage(HttpStatusCode.OK));
            }
        }

        /// <summary>
        /// The Virtual network sites test controller.
        /// </summary>
        public class VirtualNetworkSitesTestController : ApiController
        {
            /// <summary>
            /// The legacy cloud service test host.
            /// </summary>
            private readonly LegacyManagementApiTestHost host;

            /// <summary>
            /// Initializes a new instance of the <see cref="VirtualNetworkSitesTestController"/> class.
            /// </summary>
            /// <param name="host">The legacy cloud service test host.</param>
            public VirtualNetworkSitesTestController(LegacyManagementApiTestHost host)
            {
                this.host = host;
            }

            /// <summary>
            /// Lists the virtual network sites.
            /// </summary>
            /// <param name="subscriptionId">The subscription identifier.</param>
            [HttpGet]
            public Task<HttpResponseMessage> ListVirtualNetworkSites(string subscriptionId)
            {
                if (this.host.OnListVirtualNetworkSites != null)
                {
                    return Task.FromResult(this.host.OnListVirtualNetworkSites(this.Request, null));
                }

                return Task.FromResult(new HttpResponseMessage(HttpStatusCode.OK));
            }
        }

        /// <summary>
        /// The Virtual network gateway test controller.
        /// </summary>
        public class VirtualNetworkGatewayTestController : ApiController
        {
            /// <summary>
            /// The legacy cloud service test host.
            /// </summary>
            private readonly LegacyManagementApiTestHost host;

            /// <summary>
            /// Initializes a new instance of the <see cref="VirtualNetworkGatewayTestController"/> class.
            /// </summary>
            /// <param name="host">The legacy cloud service test host.</param>
            public VirtualNetworkGatewayTestController(LegacyManagementApiTestHost host)
            {
                this.host = host;
            }

            /// <summary>
            /// Gets the gateway.
            /// </summary>
            /// <param name="subscriptionId">The subscription identifier.</param>
            /// <param name="virtualNetworkName">Name of the virtual network.</param>
            [HttpGet]
            [ActionName("GetGateway")]
            public Task<HttpResponseMessage> GetGateway(string subscriptionId, string virtualNetworkName)
            {
                if (this.host.OnGetGateway != null)
                {
                    return Task.FromResult(this.host.OnGetGateway(this.Request, null));
                }

                return Task.FromResult(new HttpResponseMessage(HttpStatusCode.OK));
            }

            /// <summary>
            /// Deletes the gateway.
            /// </summary>
            /// <param name="subscriptionId">The subscription identifier.</param>
            /// <param name="virtualNetworkName">Name of the virtual network.</param>
            [HttpDelete]
            public Task<HttpResponseMessage> DeleteGateway(string subscriptionId, string virtualNetworkName)
            {
                if (this.host.OnDeleteGateway != null)
                {
                    return Task.FromResult(this.host.OnDeleteGateway(this.Request, null));
                }

                return Task.FromResult(new HttpResponseMessage(HttpStatusCode.OK));
            }

            /// <summary>
            /// Creates the gateway.
            /// </summary>
            /// <param name="subscriptionId">The subscription identifier.</param>
            /// <param name="virtualNetworkName">Name of the virtual network.</param>
            [HttpPost]
            public Task<HttpResponseMessage> CreateGateway(string subscriptionId, string virtualNetworkName)
            {
                if (this.host.OnCreateGateway != null)
                {
                    return Task.FromResult(this.host.OnCreateGateway(this.Request, null));
                }

                return Task.FromResult(new HttpResponseMessage(HttpStatusCode.OK));
            }

            /// <summary>
            /// Gets the gateway diagnostics.
            /// </summary>
            /// <param name="subscriptionId">The subscription id.</param>
            /// <param name="virtualNetworkName">The virtual network name.</param>
            [HttpGet]
            [ActionName("GetDiagnostics")]
            public Task<HttpResponseMessage> GetGatewayDiagnostics(string subscriptionId, string virtualNetworkName)
            {
                if (this.host.OnGetGatewayDiagnostics != null)
                {
                    return Task.FromResult(this.host.OnGetGatewayDiagnostics(this.Request, null));
                }

                return Task.FromResult(new HttpResponseMessage(HttpStatusCode.OK));
            }

            /// <summary>
            /// Updates the gateway diagnostics.
            /// </summary>
            /// <param name="subscriptionId">The subscription id.</param>
            /// <param name="virtualNetworkName">The virtual network name.</param>
            [HttpPut]
            [ActionName("UpdateDiagnostics")]
            public Task<HttpResponseMessage> UpdateGatewayDiagnostics(string subscriptionId, string virtualNetworkName)
            {
                if (this.host.OnUpdateGatewayDiagnostics != null)
                {
                    return Task.FromResult(this.host.OnUpdateGatewayDiagnostics(this.Request, null));
                }

                return Task.FromResult(new HttpResponseMessage(HttpStatusCode.OK));
            }

            /// <summary>
            /// Gets the virtual device configuration script.
            /// </summary>
            /// <param name="subscriptionId">The subscription id.</param>
            /// <param name="virtualNetworkName">The virtual network name.</param>
            [HttpGet]
            [ActionName("GetVirtualDeviceConfigurationScript")]
            public Task<HttpResponseMessage> GetVirtualDeviceConfigurationScript(string subscriptionId, string virtualNetworkName)
            {
                if (this.host.OnGetVirtualDeviceConfigurationScript != null)
                {
                    return Task.FromResult(this.host.OnGetVirtualDeviceConfigurationScript(this.Request, null));
                }

                return Task.FromResult(new HttpResponseMessage(HttpStatusCode.OK));
            }

            /// <summary>
            /// gets virtual network revoked client certificates.
            /// </summary>
            /// <param name="subscriptionId">The subscription id.</param>
            /// <param name="virtualNetworkName">The virtual network name.</param>
            [HttpGet]
            [ActionName("GetVirtualNetworkClientCertificates")]
            public Task<HttpResponseMessage> GetVirtualNetworkRevokedClientCertificates(string subscriptionId, string virtualNetworkName)
            {
                if (this.host.OnListRevokedClientCertificates != null)
                {
                    return Task.FromResult(this.host.OnListRevokedClientCertificates(this.Request, null));
                }

                return Task.FromResult(new HttpResponseMessage(HttpStatusCode.OK));
            }

            /// <summary>
            /// Revoke client certificate.
            /// </summary>
            /// <param name="subscriptionId">The subscription id.</param>
            /// <param name="virtualNetworkName">The virtual network name.</param>
            /// <param name="certificateThumbprint">The thumbprint.</param>
            [HttpPost]
            [ActionName("RevokeVirtualNetworkClientCertificate")]
            public Task<HttpResponseMessage> RevokeVirtualNetworkClientCertificate(string subscriptionId, string virtualNetworkName, string certificateThumbprint)
            {
                if (this.host.OnRevokeClientCertificate != null)
                {
                    return Task.FromResult(this.host.OnRevokeClientCertificate(this.Request, null));
                }

                return Task.FromResult(new HttpResponseMessage(HttpStatusCode.OK));
            }

            /// <summary>
            /// Un-revoke virtual network client certificate.
            /// </summary>
            /// <param name="subscriptionId">The subscription id.</param>
            /// <param name="virtualNetworkName">The virtual network name.</param>
            /// <param name="certificateThumbprint">The thumbprint.</param>
            [HttpDelete]
            [ActionName("UnrevokeVirtualNetworkClientCertificate")]
            public Task<HttpResponseMessage> UnrevokeVirtualNetworkClientCertificate(string subscriptionId, string virtualNetworkName, string certificateThumbprint)
            {
                if (this.host.OnUnrevokeClientCertificate != null)
                {
                    return Task.FromResult(this.host.OnUnrevokeClientCertificate(this.Request, null));
                }

                return Task.FromResult(new HttpResponseMessage(HttpStatusCode.OK));
            }

            /// <summary>
            /// Gets the gateway connections.
            /// </summary>
            /// <param name="subscriptionId">The subscription id.</param>
            /// <param name="virtualNetworkName">The virtual network name.</param>
            [HttpGet]
            [ActionName("OnGetGatewayConnections")]
            public Task<HttpResponseMessage> OnGetGatewayConnections(string subscriptionId, string virtualNetworkName)
            {
                if (this.host.OnGetGatewayConnections != null)
                {
                    return Task.FromResult(this.host.OnGetGatewayConnections(this.Request, null));
                }

                return Task.FromResult(new HttpResponseMessage(HttpStatusCode.OK));
            }

            /// <summary>
            /// Puts the virtual network connection.
            /// </summary>
            /// <param name="subscriptionId">The subscription id.</param>
            /// <param name="virtualNetworkName">The virtual network name.</param>
            /// <param name="localNetworkSiteName">The local site name.</param>
            [HttpPut]
            public Task<HttpResponseMessage> OnPutGatewayConnection(string subscriptionId, string virtualNetworkName, string localNetworkSiteName)
            {
                if (this.host.OnPutVirtualNetworkConnection != null)
                {
                    return Task.FromResult(this.host.OnPutVirtualNetworkConnection(this.Request, null));
                }

                return Task.FromResult(new HttpResponseMessage(HttpStatusCode.OK));
            }

            /// <summary>
            /// Gets the supported devices.
            /// </summary>
            /// <param name="subscriptionId">The subscription id.</param>
            [HttpGet]
            [ActionName("OnGetSupportedDevices")]
            public Task<HttpResponseMessage> OnGetSupportedDevices(string subscriptionId)
            {
                if (this.host.OnGetSupportedDevices != null)
                {
                    return Task.FromResult(this.host.OnGetSupportedDevices(this.Request, null));
                }

                return Task.FromResult(new HttpResponseMessage(HttpStatusCode.OK));
            }
        }

        /// <summary>
        /// The Virtual network gateway package test controller.
        /// </summary>
        public class VirtualNetworkGatewayPackageTestController : ApiController
        {
            /// <summary>
            /// The legacy cloud service test host.
            /// </summary>
            private readonly LegacyManagementApiTestHost host;

            /// <summary>
            /// Initializes a new instance of the <see cref="VirtualNetworkGatewayPackageTestController"/> class.
            /// </summary>
            /// <param name="host">The legacy cloud service test host.</param>
            public VirtualNetworkGatewayPackageTestController(LegacyManagementApiTestHost host)
            {
                this.host = host;
            }

            /// <summary>
            /// Generates the VPN client package.
            /// </summary>
            /// <param name="subscriptionId">The subscription identifier.</param>
            /// <param name="virtualNetworkName">Name of the virtual network.</param>
            [HttpPost]
            public Task<HttpResponseMessage> GenerateVpnClientPackage(string subscriptionId, string virtualNetworkName)
            {
                if (this.host.OnGenerateVpnClientPackage != null)
                {
                    return Task.FromResult(this.host.OnGenerateVpnClientPackage(this.Request, null));
                }

                return Task.FromResult(new HttpResponseMessage(HttpStatusCode.OK));
            }
        }

        /// <summary>
        /// The Virtual network gateway certificates test controller.
        /// </summary>
        public class VirtualNetworkGatewayCertificatesTestController : ApiController
        {
            /// <summary>
            /// The legacy cloud service test host.
            /// </summary>
            private readonly LegacyManagementApiTestHost host;

            /// <summary>
            /// Initializes a new instance of the <see cref="VirtualNetworkGatewayCertificatesTestController"/> class.
            /// </summary>
            /// <param name="host">The legacy cloud service test host.</param>
            public VirtualNetworkGatewayCertificatesTestController(LegacyManagementApiTestHost host)
            {
                this.host = host;
            }

            /// <summary>
            /// Lists the client root certificates.
            /// </summary>
            /// <param name="subscriptionId">The subscription identifier.</param>
            /// <param name="virtualNetworkName">Name of the virtual network.</param>
            [HttpGet]
            public Task<HttpResponseMessage> ListClientRootCertificates(string subscriptionId, string virtualNetworkName)
            {
                if (this.host.OnListClientRootCertificates != null)
                {
                    return Task.FromResult(this.host.OnListClientRootCertificates(this.Request, null));
                }

                return Task.FromResult(new HttpResponseMessage(HttpStatusCode.OK));
            }

            /// <summary>
            /// Gets the client root certificate.
            /// </summary>
            /// <param name="subscriptionId">The subscription identifier.</param>
            /// <param name="virtualNetworkName">Name of the virtual network.</param>
            /// <param name="certificateThumbprint">The certificate thumbprint.</param>
            [HttpGet]
            public Task<HttpResponseMessage> GetClientRootCertificate(string subscriptionId, string virtualNetworkName, string certificateThumbprint)
            {
                if (this.host.OnGetClientRootCertificate != null)
                {
                    return Task.FromResult(this.host.OnGetClientRootCertificate(this.Request, null));
                }

                return Task.FromResult(new HttpResponseMessage(HttpStatusCode.OK));
            }

            /// <summary>
            /// Deletes the client root certificate.
            /// </summary>
            /// <param name="subscriptionId">The subscription identifier.</param>
            /// <param name="virtualNetworkName">Name of the virtual network.</param>
            /// <param name="certificateThumbprint">The certificate thumbprint.</param>
            [HttpDelete]
            public Task<HttpResponseMessage> DeleteClientRootCertificate(string subscriptionId, string virtualNetworkName, string certificateThumbprint)
            {
                if (this.host.OnDeleteClientRootCertificate != null)
                {
                    return Task.FromResult(this.host.OnDeleteClientRootCertificate(this.Request, null));
                }

                return Task.FromResult(new HttpResponseMessage(HttpStatusCode.OK));
            }

            /// <summary>
            /// Uploads the client root certificate.
            /// </summary>
            /// <param name="subscriptionId">The subscription identifier.</param>
            /// <param name="virtualNetworkName">Name of the virtual network.</param>
            [HttpPost]
            public Task<HttpResponseMessage> UploadClientRootCertificate(string subscriptionId, string virtualNetworkName)
            {
                if (this.host.OnUploadClientRootCertificate != null)
                {
                    return Task.FromResult(this.host.OnUploadClientRootCertificate(this.Request, null));
                }

                return Task.FromResult(new HttpResponseMessage(HttpStatusCode.OK));
            }
        }

        /// <summary>
        /// The Virtual network gateway operation test controller.
        /// </summary>
        public class VirtualNetworkGatewayOperationTestController : ApiController
        {
            /// <summary>
            /// The legacy cloud service test host.
            /// </summary>
            private readonly LegacyManagementApiTestHost host;

            /// <summary>
            /// Initializes a new instance of the <see cref="VirtualNetworkGatewayOperationTestController"/> class.
            /// </summary>
            /// <param name="host">The legacy cloud service test host.</param>
            public VirtualNetworkGatewayOperationTestController(LegacyManagementApiTestHost host)
            {
                this.host = host;
            }

            /// <summary>
            /// Gets the gateway operation.
            /// </summary>
            /// <param name="subscriptionId">The subscription identifier.</param>
            /// <param name="operationId">The operation identifier.</param>
            [HttpGet]
            public Task<HttpResponseMessage> GetGatewayOperation(string subscriptionId, string operationId)
            {
                if (this.host.OnGetGatewayOperation != null)
                {
                    return Task.FromResult(this.host.OnGetGatewayOperation(this.Request, null));
                }

                return Task.FromResult(new HttpResponseMessage(HttpStatusCode.OK));
            }
        }

        /// <summary>
        /// The locations test controller.
        /// </summary>
        public class LocationsTestController : ApiController
        {
            /// <summary>
            /// The legacy cloud service test host.
            /// </summary>
            private readonly LegacyManagementApiTestHost host;

            /// <summary>
            /// Initializes a new instance of the <see cref="LocationsTestController"/> class.
            /// </summary>
            /// <param name="host">The legacy cloud service test host.</param>
            public LocationsTestController(LegacyManagementApiTestHost host)
            {
                this.host = host;
            }

            /// <summary>
            /// Lists the locations.
            /// </summary>
            /// <param name="subscriptionId">The subscription id.</param>
            [HttpGet]
            public Task<HttpResponseMessage> ListLocations(string subscriptionId)
            {
                if (this.host.OnListLocationsRequest != null)
                {
                    return Task.FromResult(this.host.OnListLocationsRequest(this.Request, null));
                }

                return Task.FromResult(new HttpResponseMessage(HttpStatusCode.OK));
            }
        }

        /// <summary>
        /// The role sizes test controller.
        /// </summary>
        public class RoleSizeTestController : ApiController
        {
            /// <summary>
            /// The role size test host.
            /// </summary>
            private readonly LegacyManagementApiTestHost host;

            /// <summary>
            /// Initializes a new instance of the <see cref="RoleSizeTestController"/> class.
            /// </summary>
            /// <param name="host">The role size test host.</param>
            public RoleSizeTestController(LegacyManagementApiTestHost host)
            {
                this.host = host;
            }

            /// <summary>
            /// Lists the role sizes.
            /// </summary>
            /// <param name="subscriptionId">The subscription id.</param>
            [HttpGet]
            public Task<HttpResponseMessage> ListRoleSizes(string subscriptionId)
            {
                if (this.host.OnListRoleSizesRequest != null)
                {
                    return Task.FromResult(this.host.OnListRoleSizesRequest(this.Request, null));
                }

                return Task.FromResult(new HttpResponseMessage(HttpStatusCode.OK));
            }
        }

        /// <summary>
        /// The reserved <c>IP</c> test controller.
        /// </summary>
        public class ReservedIpTestController : ApiController
        {
            /// <summary>
            /// The legacy cloud service test host.
            /// </summary>
            private readonly LegacyManagementApiTestHost host;

            /// <summary>
            /// Initializes a new instance of the <see cref="ReservedIpTestController"/> class.
            /// </summary>
            /// <param name="host">The legacy cloud service test host.</param>
            public ReservedIpTestController(LegacyManagementApiTestHost host)
            {
                this.host = host;
            }

            /// <summary>
            /// Gets the reserved <c>IP</c>.
            /// </summary>
            /// <param name="subscriptionId">The subscription id.</param>
            /// <param name="ipname">Name of the <c>IP</c>.</param>
            [HttpGet]
            public Task<HttpResponseMessage> GetReservedIp(string subscriptionId, string ipname)
            {
                if (this.host.OnGetReservedIp != null)
                {
                    return Task.FromResult(this.host.OnGetReservedIp(this.Request, null));
                }

                return Task.FromResult(new HttpResponseMessage(HttpStatusCode.OK));
            }

            /// <summary>
            /// Lists the reserved <c>ips</c>.
            /// </summary>
            /// <param name="subscriptionId">The subscription id.</param>
            [HttpGet]
            public Task<HttpResponseMessage> GetReservedIps(string subscriptionId)
            {
                if (this.host.OnListReservedIps != null)
                {
                    return Task.FromResult(this.host.OnListReservedIps(this.Request, null));
                }

                return Task.FromResult(new HttpResponseMessage(HttpStatusCode.OK));
            }

            /// <summary>
            /// Puts the reserved <c>IP</c>.
            /// </summary>
            /// <param name="subscriptionId">The subscription id.</param>
            [HttpPost]
            public Task<HttpResponseMessage> CreateReservedIp(string subscriptionId)
            {
                string requestBody = this.Request.Content.ReadAsStringAsync().Result;

                if (this.host.OnCreateReservedIp != null)
                {
                    return Task.FromResult(this.host.OnCreateReservedIp(this.Request, requestBody));
                }

                return Task.FromResult(new HttpResponseMessage(HttpStatusCode.OK));
            }

            /// <summary>
            /// Deletes the reserved <c>IP</c>.
            /// </summary>
            /// <param name="subscriptionId">The subscription id.</param>
            /// <param name="ipname">Name of the reserved <c>IP</c>.</param>
            [HttpDelete]
            public Task<HttpResponseMessage> DeleteReservedIp(string subscriptionId, string ipname)
            {
                if (this.host.OnDeleteReservedIp != null)
                {
                    return Task.FromResult(this.host.OnDeleteReservedIp(this.Request, null));
                }

                return Task.FromResult(new HttpResponseMessage(HttpStatusCode.OK));
            }
        }

        /// <summary>
        /// The internal load balancer controller.
        /// </summary>
        public class InternalLoadBalancerTestController : ApiController
        {
            /// <summary>
            /// The legacy cloud service test host.
            /// </summary>
            private readonly LegacyManagementApiTestHost host;

            /// <summary>
            /// Initializes a new instance of the <see cref="InternalLoadBalancerTestController"/> class.
            /// </summary>
            /// <param name="host">The legacy cloud service test host.</param>
            public InternalLoadBalancerTestController(LegacyManagementApiTestHost host)
            {
                this.host = host;
            }

            /// <summary>
            /// Creates the internal load balancer.
            /// </summary>
            /// <param name="subscriptionId">The subscription id.</param>
            /// <param name="serviceName">The service name.</param>
            /// <param name="deploymentName">The deployment name.</param>
            [HttpPost]
            public Task<HttpResponseMessage> CreateInternalLoadBalancer(string subscriptionId, string serviceName, string deploymentName)
            {
                if (this.host.OnCreateInternalLoadBalancer != null)
                {
                    return Task.FromResult(this.host.OnCreateInternalLoadBalancer(this.Request, null));
                }

                return Task.FromResult(new HttpResponseMessage(HttpStatusCode.OK));
            }

            /// <summary>
            /// Updates the internal load balancer.
            /// </summary>
            /// <param name="subscriptionId">The subscription id.</param>
            /// <param name="serviceName">The service name.</param>
            /// <param name="deploymentName">The deployment name.</param>
            /// <param name="loadBalancerName">The load balancer name.</param>
            [HttpPut]
            public Task<HttpResponseMessage> UpdateInternalLoadBalancer(string subscriptionId, string serviceName, string deploymentName, string loadBalancerName)
            {
                if (this.host.OnUpdateInternalLoadBalancer != null)
                {
                    return Task.FromResult(this.host.OnUpdateInternalLoadBalancer(this.Request, null));
                }

                return Task.FromResult(new HttpResponseMessage(HttpStatusCode.OK));
            }

            /// <summary>
            /// Deletes the reserved <c>IP</c>.
            /// </summary>
            /// <param name="subscriptionId">The subscription id.</param>
            /// <param name="serviceName">The service name.</param>
            /// <param name="deploymentName">The deployment name.</param>
            /// <param name="loadBalancerName">The load balancer name.</param>
            [HttpDelete]
            public Task<HttpResponseMessage> DeleteInternalLoadBalancer(string subscriptionId, string serviceName, string deploymentName, string loadBalancerName)
            {
                if (this.host.OnDeleteInternalLoadBalancer != null)
                {
                    return Task.FromResult(this.host.OnDeleteInternalLoadBalancer(this.Request, null));
                }

                return Task.FromResult(new HttpResponseMessage(HttpStatusCode.OK));
            }
        }

        /// <summary>
        /// The image test controller.
        /// </summary>
        public class ImageTestController : ApiController
        {
            /// <summary>
            /// The legacy cloud service test host.
            /// </summary>
            private readonly LegacyManagementApiTestHost host;

            /// <summary>
            /// Initializes a new instance of the <see cref="ImageTestController"/> class.
            /// </summary>
            /// <param name="host">The legacy cloud service test host.</param>
            public ImageTestController(LegacyManagementApiTestHost host)
            {
                this.host = host;
            }

            /// <summary>
            /// list images.
            /// </summary>
            /// <param name="subscriptionId">The subscription id.</param>
            [HttpGet]
            public Task<HttpResponseMessage> ListImages(string subscriptionId)
            {
                if (this.host.OnListImages != null)
                {
                    return Task.FromResult(this.host.OnListImages(this.Request, null));
                }

                return Task.FromResult(new HttpResponseMessage(HttpStatusCode.OK));
            }

            /// <summary>
            /// create image.
            /// </summary>
            /// <param name="subscriptionId">The subscription id.</param>
            [HttpPost]
            public Task<HttpResponseMessage> CreateImage(string subscriptionId)
            {
                if (this.host.OnCreateVirtualMachineImage != null)
                {
                    return Task.FromResult(this.host.OnCreateVirtualMachineImage(this.Request, null));
                }

                return Task.FromResult(new HttpResponseMessage(HttpStatusCode.OK));
            }

            /// <summary>
            /// Deletes the image.
            /// </summary>
            /// <param name="subscriptionId">The subscription id.</param>
            /// <param name="imageName">The virtual machine image name.</param>
            [HttpDelete]
            public Task<HttpResponseMessage> DeleteImage(string subscriptionId, string imageName)
            {
                if (this.host.OnDeleteImage != null)
                {
                    return Task.FromResult(this.host.OnDeleteImage(this.Request, null));
                }

                return Task.FromResult(new HttpResponseMessage(HttpStatusCode.OK));
            }

            /// <summary>
            /// update image.
            /// </summary>
            /// <param name="subscriptionId">The subscription id.</param>
            [HttpPut]
            public Task<HttpResponseMessage> UpdateImage(string subscriptionId)
            {
                if (this.host.OnUpdateVirtualMachineImage != null)
                {
                    return Task.FromResult(this.host.OnUpdateVirtualMachineImage(this.Request, null));
                }

                return Task.FromResult(new HttpResponseMessage(HttpStatusCode.OK));
            }
        }

        /// <summary>
        /// The OS image test controller.
        /// </summary>
        public class OSImageTestController : ApiController
        {
            /// <summary>
            /// The legacy cloud service test host.
            /// </summary>
            private readonly LegacyManagementApiTestHost host;

            /// <summary>
            /// Initializes a new instance of the <see cref="OSImageTestController"/> class.
            /// </summary>
            /// <param name="host">The legacy cloud service test host.</param>
            public OSImageTestController(LegacyManagementApiTestHost host)
            {
                this.host = host;
            }

            /// <summary>
            /// list images.
            /// </summary>
            /// <param name="subscriptionId">The subscription id.</param>
            [HttpGet]
            public Task<HttpResponseMessage> ListOSImages(string subscriptionId)
            {
                if (this.host.OnListOSImages != null)
                {
                    return Task.FromResult(this.host.OnListOSImages(this.Request, null));
                }

                return Task.FromResult(new HttpResponseMessage(HttpStatusCode.OK));
            }

            /// <summary>
            /// Creates the image.
            /// </summary>
            /// <param name="subscriptionId">The subscription id.</param>
            [HttpPost]
            public Task<HttpResponseMessage> CreateOSImage(string subscriptionId)
            {
                if (this.host.OnCreateOSImage != null)
                {
                    return Task.FromResult(this.host.OnCreateOSImage(this.Request, null));
                }

                return Task.FromResult(new HttpResponseMessage(HttpStatusCode.Created));
            }

            /// <summary>
            /// Get the image.
            /// </summary>
            /// <param name="subscriptionId">The subscription id.</param>
            /// <param name="imageName">The operating system image name.</param>
            [HttpGet]
            public Task<HttpResponseMessage> GetOSImage(string subscriptionId, string imageName)
            {
                if (this.host.OnGetOSImage != null)
                {
                    return Task.FromResult(this.host.OnGetOSImage(this.Request, null));
                }

                return Task.FromResult(new HttpResponseMessage(HttpStatusCode.OK));
            }

            /// <summary>
            /// Updates the image.
            /// </summary>
            /// <param name="subscriptionId">The subscription id.</param>
            /// <param name="imageName">The operating system image name.</param>
            [HttpPut]
            public Task<HttpResponseMessage> UpdateOSImage(string subscriptionId, string imageName)
            {
                if (this.host.OnUpdateOSImage != null)
                {
                    return Task.FromResult(this.host.OnUpdateOSImage(this.Request, null));
                }

                return Task.FromResult(new HttpResponseMessage(HttpStatusCode.OK));
            }

            /// <summary>
            /// Deletes the image.
            /// </summary>
            /// <param name="subscriptionId">The subscription id.</param>
            /// <param name="imageName">The operating system image name.</param>
            [HttpDelete]
            public Task<HttpResponseMessage> DeleteOSImage(string subscriptionId, string imageName)
            {
                if (this.host.OnDeleteOSImage != null)
                {
                    return Task.FromResult(this.host.OnDeleteOSImage(this.Request, null));
                }

                return Task.FromResult(new HttpResponseMessage(HttpStatusCode.OK));
            }
        }

        /// <summary>
        /// The network security groups test controller.
        /// </summary>
        public class NetworkSecurityGroupTestController : ApiController
        {
            /// <summary>
            /// The legacy test host.
            /// </summary>
            private readonly LegacyManagementApiTestHost host;

            /// <summary>
            /// Initializes a new instance of the <see cref="NetworkSecurityGroupTestController"/> class.
            /// </summary>
            /// <param name="host">The legacy test host.</param>
            public NetworkSecurityGroupTestController(LegacyManagementApiTestHost host)
            {
                this.host = host;
            }

            /// <summary>
            /// Lists the requested subscription network security groups.
            /// </summary>
            /// <param name="subscriptionId">The subscription Id.</param>
            [HttpGet]
            public Task<HttpResponseMessage> ListNetworkSecurityGroups(string subscriptionId)
            {
                if (this.host.OnListNetworkSecurityGroupsRequest != null)
                {
                    return Task.FromResult(this.host.OnListNetworkSecurityGroupsRequest(this.Request, null));
                }

                return Task.FromResult(new HttpResponseMessage(HttpStatusCode.OK));
            }

            /// <summary>
            /// Creates the requested subscription network security group.
            /// </summary>
            /// <param name="subscriptionId">The subscription Id.</param>
            [HttpPost]
            public Task<HttpResponseMessage> CreateNetworkSecurityGroup(string subscriptionId)
            {
                if (this.host.OnCreateNetworkSecurityGroupRequest != null)
                {
                    return Task.FromResult(this.host.OnCreateNetworkSecurityGroupRequest(this.Request, null));
                }

                return Task.FromResult(new HttpResponseMessage(HttpStatusCode.OK));
            }

            /// <summary>
            /// Gets the requested subscription network security group.
            /// </summary>
            /// <param name="subscriptionId">The subscription Id.</param>
            /// <param name="networkSecurityGroupName">The group name.</param>
            [HttpGet]
            public Task<HttpResponseMessage> GetNetworkSecurityGroup(string subscriptionId, string networkSecurityGroupName)
            {
                if (this.Request.RequestUri.ParseQueryString()["detaillevel"].EqualsInsensitively("Full"))
                {
                    if (this.host.OnGetNetworkSecurityGroupWithDetailsRequest != null)
                    {
                        return Task.FromResult(this.host.OnGetNetworkSecurityGroupWithDetailsRequest(this.Request, null));
                    }
                }
                else if (this.host.OnGetNetworkSecurityGroupRequest != null)
                {
                    return Task.FromResult(this.host.OnGetNetworkSecurityGroupRequest(this.Request, null));
                }

                return Task.FromResult(new HttpResponseMessage(HttpStatusCode.OK));
            }

            /// <summary>
            /// Deletes the network security group.
            /// </summary>
            /// <param name="subscriptionId">The subscription Id.</param>
            /// <param name="networkSecurityGroupName">The group name.</param>
            [HttpDelete]
            public Task<HttpResponseMessage> DeleteNetworkSecurityGroup(string subscriptionId, string networkSecurityGroupName)
            {
                if (this.host.OnDeleteNetworkSecurityGroupRequest != null)
                {
                    return Task.FromResult(this.host.OnDeleteNetworkSecurityGroupRequest(this.Request, null));
                }

                return Task.FromResult(new HttpResponseMessage(HttpStatusCode.OK));
            }

            /// <summary>
            /// Sets the network security rule.
            /// </summary>
            /// <param name="subscriptionId">The subscription Id.</param>
            /// <param name="networkSecurityGroupName">The group name.</param>
            /// <param name="ruleName">The rule name.</param>
            [HttpPut]
            public Task<HttpResponseMessage> SetNetworkSecurityRule(string subscriptionId, string networkSecurityGroupName, string ruleName)
            {
                if (this.host.OnSetNetworkSecurityRuleRequest != null)
                {
                    return Task.FromResult(this.host.OnSetNetworkSecurityRuleRequest(this.Request, null));
                }

                return Task.FromResult(new HttpResponseMessage(HttpStatusCode.OK));
            }

            /// <summary>
            /// Deletes the network security rule.
            /// </summary>
            /// <param name="subscriptionId">The subscription Id.</param>
            /// <param name="networkSecurityGroupName">The group name.</param>
            /// <param name="ruleName">The rule name.</param>
            [HttpDelete]
            public Task<HttpResponseMessage> DeleteNetworkSecurityRule(string subscriptionId, string networkSecurityGroupName, string ruleName)
            {
                if (this.host.OnDeleteNetworkSecurityRuleRequest != null)
                {
                    return Task.FromResult(this.host.OnDeleteNetworkSecurityRuleRequest(this.Request, null));
                }

                return Task.FromResult(new HttpResponseMessage(HttpStatusCode.OK));
            }
        }

        /// <summary>
        /// The network interface network security groups test controller.
        /// </summary>
        public class NetworkInterfaceNetworkSecurityGroupTestController : ApiController
        {
            /// <summary>
            /// The legacy test host.
            /// </summary>
            private readonly LegacyManagementApiTestHost host;

            /// <summary>
            /// Initializes a new instance of the <see cref="NetworkInterfaceNetworkSecurityGroupTestController"/> class.
            /// </summary>
            /// <param name="host">The legacy test host.</param>
            public NetworkInterfaceNetworkSecurityGroupTestController(LegacyManagementApiTestHost host)
            {
                this.host = host;
            }

            /// <summary>
            /// Adds the network interface network security group.
            /// </summary>
            /// <param name="subscriptionId">The subscription Id.</param>
            /// <param name="hostedServiceName">The hosted service name.</param>
            /// <param name="deploymentName">The deployment name.</param>
            /// <param name="roleName">The role name.</param>
            /// <param name="networkInterfaceName">The network interface name.</param>
            [HttpPost]
            public Task<HttpResponseMessage> AddNetworkInterfaceNetworkSecurityGroup(string subscriptionId, string hostedServiceName, string deploymentName, string roleName, string networkInterfaceName)
            {
                if (this.host.OnAddNetworkInterfaceNetworkSecurityGroupRequest != null)
                {
                    return Task.FromResult(this.host.OnAddNetworkInterfaceNetworkSecurityGroupRequest(this.Request, null));
                }

                return Task.FromResult(new HttpResponseMessage(HttpStatusCode.OK));
            }

            /// <summary>
            /// Gets the network interface network security group.
            /// </summary>
            /// <param name="subscriptionId">The subscription Id.</param>
            /// <param name="hostedServiceName">The hosted service name.</param>
            /// <param name="deploymentName">The deployment name.</param>
            /// <param name="roleName">The role name.</param>
            /// <param name="networkInterfaceName">The network interface name.</param>
            [HttpGet]
            public Task<HttpResponseMessage> GetNetworkInterfaceNetworkSecurityGroup(string subscriptionId, string hostedServiceName, string deploymentName, string roleName, string networkInterfaceName)
            {
                if (this.host.OnGetNetworkInterfaceNetworkSecurityGroupRequest != null)
                {
                    return Task.FromResult(this.host.OnGetNetworkInterfaceNetworkSecurityGroupRequest(this.Request, null));
                }

                return Task.FromResult(new HttpResponseMessage(HttpStatusCode.OK));
            }

            /// <summary>
            /// Deletes the network interface network security group.
            /// </summary>
            /// <param name="subscriptionId">The subscription Id.</param>
            /// <param name="hostedServiceName">The hosted service name.</param>
            /// <param name="deploymentName">The deployment name.</param>
            /// <param name="roleName">The role name.</param>
            /// <param name="networkInterfaceName">The network interface name.</param>
            /// <param name="networkSecurityGroupName">The group name.</param>
            [HttpDelete]
            public Task<HttpResponseMessage> DeleteNetworkInterfaceNetworkSecurityGroup(string subscriptionId, string hostedServiceName, string deploymentName, string roleName, string networkInterfaceName, string networkSecurityGroupName)
            {
                if (this.host.OnDeleteNetworkInterfaceNetworkSecurityGroupRequest != null)
                {
                    return Task.FromResult(this.host.OnDeleteNetworkInterfaceNetworkSecurityGroupRequest(this.Request, null));
                }

                return Task.FromResult(new HttpResponseMessage(HttpStatusCode.OK));
            }
        }

        /// <summary>
        /// The virtual machine network security groups test controller.
        /// </summary>
        public class VirtualMachineNetworkSecurityGroupTestController : ApiController
        {
            /// <summary>
            /// The legacy test host.
            /// </summary>
            private readonly LegacyManagementApiTestHost host;

            /// <summary>
            /// Initializes a new instance of the <see cref="VirtualMachineNetworkSecurityGroupTestController"/> class.
            /// </summary>
            /// <param name="host">The legacy test host.</param>
            public VirtualMachineNetworkSecurityGroupTestController(LegacyManagementApiTestHost host)
            {
                this.host = host;
            }

            /// <summary>
            /// Adds the virtual machine network security group.
            /// </summary>
            /// <param name="subscriptionId">The subscription Id.</param>
            /// <param name="hostedServiceName">The hosted service name.</param>
            /// <param name="deploymentName">The deployment name.</param>
            /// <param name="roleName">The role name.</param>
            [HttpPost]
            public Task<HttpResponseMessage> AddVirtualMachineNetworkSecurityGroup(string subscriptionId, string hostedServiceName, string deploymentName, string roleName)
            {
                if (this.host.OnAddVirtualMachineNetworkSecurityGroupRequest != null)
                {
                    return Task.FromResult(this.host.OnAddVirtualMachineNetworkSecurityGroupRequest(this.Request, null));
                }

                return Task.FromResult(new HttpResponseMessage(HttpStatusCode.OK));
            }

            /// <summary>
            /// Gets the virtual machine network security group.
            /// </summary>
            /// <param name="subscriptionId">The subscription Id.</param>
            /// <param name="hostedServiceName">The hosted service name.</param>
            /// <param name="deploymentName">The deployment name.</param>
            /// <param name="roleName">The role name.</param>
            [HttpGet]
            public Task<HttpResponseMessage> GetVirtualMachineNetworkSecurityGroup(string subscriptionId, string hostedServiceName, string deploymentName, string roleName)
            {
                if (this.host.OnGetVirtualMachineNetworkSecurityGroupRequest != null)
                {
                    return Task.FromResult(this.host.OnGetVirtualMachineNetworkSecurityGroupRequest(this.Request, null));
                }

                return Task.FromResult(new HttpResponseMessage(HttpStatusCode.OK));
            }

            /// <summary>
            /// Deletes the virtual machine network security group.
            /// </summary>
            /// <param name="subscriptionId">The subscription Id.</param>
            /// <param name="hostedServiceName">The hosted service name.</param>
            /// <param name="deploymentName">The deployment name.</param>
            /// <param name="roleName">The role name.</param>
            /// <param name="networkSecurityGroupName">The group name.</param>
            [HttpDelete]
            public Task<HttpResponseMessage> DeleteVirtualMachineNetworkSecurityGroup(string subscriptionId, string hostedServiceName, string deploymentName, string roleName, string networkSecurityGroupName)
            {
                if (this.host.OnDeleteVirtualMachineNetworkSecurityGroupRequest != null)
                {
                    return Task.FromResult(this.host.OnDeleteVirtualMachineNetworkSecurityGroupRequest(this.Request, null));
                }

                return Task.FromResult(new HttpResponseMessage(HttpStatusCode.OK));
            }
        }

        /// <summary>
        /// The subnet network security groups test controller.
        /// </summary>
        public class SubnetNetworkSecurityGroupTestController : ApiController
        {
            /// <summary>
            /// The legacy test host.
            /// </summary>
            private readonly LegacyManagementApiTestHost host;

            /// <summary>
            /// Initializes a new instance of the <see cref="SubnetNetworkSecurityGroupTestController"/> class.
            /// </summary>
            /// <param name="host">The legacy test host.</param>
            public SubnetNetworkSecurityGroupTestController(LegacyManagementApiTestHost host)
            {
                this.host = host;
            }

            /// <summary>
            /// Adds the subnet network security group.
            /// </summary>
            /// <param name="subscriptionId">The subscription Id.</param>
            /// <param name="virtualNetworkName">The virtual network name.</param>
            /// <param name="subnetName">The subnet name.</param>
            [HttpPost]
            public Task<HttpResponseMessage> AddSubnetNetworkSecurityGroup(string subscriptionId, string virtualNetworkName, string subnetName)
            {
                if (this.host.OnAddSubnetNetworkSecurityGroupRequest != null)
                {
                    return Task.FromResult(this.host.OnAddSubnetNetworkSecurityGroupRequest(this.Request, null));
                }

                return Task.FromResult(new HttpResponseMessage(HttpStatusCode.OK));
            }

            /// <summary>
            /// Gets the subnet network security group.
            /// </summary>
            /// <param name="subscriptionId">The subscription Id.</param>
            /// <param name="virtualNetworkName">The virtual network name.</param>
            /// <param name="subnetName">The subnet name.</param>
            [HttpGet]
            public Task<HttpResponseMessage> GetSubnetNetworkSecurityGroup(string subscriptionId, string virtualNetworkName, string subnetName)
            {
                if (this.host.OnGetSubnetNetworkSecurityGroupRequest != null)
                {
                    return Task.FromResult(this.host.OnGetSubnetNetworkSecurityGroupRequest(this.Request, null));
                }

                return Task.FromResult(new HttpResponseMessage(HttpStatusCode.OK));
            }

            /// <summary>
            /// Deletes the subnet network security group.
            /// </summary>
            /// <param name="subscriptionId">The subscription Id.</param>
            /// <param name="virtualNetworkName">The virtual network name.</param>
            /// <param name="subnetName">The subnet name.</param>
            /// <param name="networkSecurityGroupName">The group name.</param>
            [HttpDelete]
            public Task<HttpResponseMessage> DeleteSubnetNetworkSecurityGroup(string subscriptionId, string virtualNetworkName, string subnetName, string networkSecurityGroupName)
            {
                if (this.host.OnDeleteSubnetNetworkSecurityGroupRequest != null)
                {
                    return Task.FromResult(this.host.OnDeleteSubnetNetworkSecurityGroupRequest(this.Request, null));
                }

                return Task.FromResult(new HttpResponseMessage(HttpStatusCode.OK));
            }
        }

        /// <summary>
        /// The operating system test controller.
        /// </summary>
        public class OperatingSystemTestController : ApiController
        {
            /// <summary>
            /// The legacy test host.
            /// </summary>
            private readonly LegacyManagementApiTestHost host;

            /// <summary>
            /// Initializes a new instance of the <see cref="OperatingSystemTestController"/> class.
            /// </summary>
            /// <param name="host">The legacy test host.</param>
            public OperatingSystemTestController(LegacyManagementApiTestHost host)
            {
                this.host = host;
            }

            /// <summary>
            /// List the guest operating systems that are currently available in Microsoft Azure.
            /// </summary>
            /// <param name="subscriptionId">The subscription id.</param>
            [HttpGet]
            [ActionName("ListOperatingSystems")]
            public Task<HttpResponseMessage> ListOperatingSystems(string subscriptionId)
            {
                if (this.host.OnListOperatingSystems != null)
                {
                    return Task.FromResult(this.host.OnListOperatingSystems(this.Request, null));
                }

                return Task.FromResult(new HttpResponseMessage(HttpStatusCode.OK));
            }

            /// <summary>
            /// List the available guest operating system families and the available operating systems for each family.
            /// </summary>
            /// <param name="subscriptionId">The subscription id.</param>
            [HttpGet]
            [ActionName("ListOperatingSystemFamilies")]
            public Task<HttpResponseMessage> ListOperatingSystemFamilies(string subscriptionId)
            {
                if (this.host.OnListOperatingSystemFamilies != null)
                {
                    return Task.FromResult(this.host.OnListOperatingSystemFamilies(this.Request, null));
                }

                return Task.FromResult(new HttpResponseMessage(HttpStatusCode.OK));
            }
        }

        /// <summary>
        /// The subscription operations test controller.
        /// </summary>
        public class SubscriptionOperationsTestController : ApiController
        {
            /// <summary>
            /// The legacy test host.
            /// </summary>
            private readonly LegacyManagementApiTestHost host;

            /// <summary>
            /// Initializes a new instance of the <see cref="SubscriptionOperationsTestController"/> class.
            /// </summary>
            /// <param name="host">The legacy test host.</param>
            public SubscriptionOperationsTestController(LegacyManagementApiTestHost host)
            {
                this.host = host;
            }

            /// <summary>
            /// List subscription operations.
            /// </summary>
            /// <param name="subscriptionId">The subscription id.</param>
            /// <param name="startTime">The start time query string.</param>
            /// <param name="endTime">The end time query string.</param>
            /// <param name="objectIdFilter">The object id filter query string.</param>
            /// <param name="operationResultFilter">The operation result filter query string.</param>
            /// <param name="continuationToken">The continuation token query string.</param>
            [HttpGet]
            public Task<HttpResponseMessage> ListSubscriptionOperations(
                string subscriptionId,
                string startTime,
                string endTime,
                string objectIdFilter = null,
                string operationResultFilter = null,
                string continuationToken = null)
            {
                if (this.host.OnListSubscriptionOperations != null)
                {
                    return Task.FromResult(this.host.OnListSubscriptionOperations(this.Request, subscriptionId, startTime, endTime, objectIdFilter, operationResultFilter, continuationToken));
                }

                return Task.FromResult(new HttpResponseMessage(HttpStatusCode.OK));
            }
        }

        /// <summary>
        /// The express route test controller.
        /// </summary>
        public class ExpressRouteTestController : ApiController
        {
            /// <summary>
            /// The legacy test host.
            /// </summary>
            private readonly LegacyManagementApiTestHost host;

            /// <summary>
            /// Initializes a new instance of the <see cref="ExpressRouteTestController"/> class.
            /// </summary>
            /// <param name="host">The legacy test host.</param>
            public ExpressRouteTestController(LegacyManagementApiTestHost host)
            {
                this.host = host;
            }

            /// <summary>
            /// Lists the cross connections.
            /// </summary>
            /// <param name="subscriptionId">The subscription identifier.</param>
            [HttpGet]
            [ActionName("ListCrossConnections")]
            public Task<HttpResponseMessage> ListCrossConnections(string subscriptionId)
            {
                if (this.host.OnListCrossConnections != null)
                {
                    return Task.FromResult(this.host.OnListCrossConnections(this.Request, null));
                }

                return Task.FromResult(new HttpResponseMessage(HttpStatusCode.OK));
            }

            /// <summary>
            /// Gets the cross connection.
            /// </summary>
            /// <param name="subscriptionId">The subscription identifier.</param>
            /// <param name="crossConnectionName">The cross connection name.</param>
            [HttpGet]
            [ActionName("GetCrossConnection")]
            public Task<HttpResponseMessage> GetCrossConnection(
                string subscriptionId,
                string crossConnectionName)
            {
                if (this.host.OnGetCrossConnection != null)
                {
                    return Task.FromResult(this.host.OnGetCrossConnection(this.Request, null));
                }

                return Task.FromResult(new HttpResponseMessage(HttpStatusCode.OK));
            }

            /// <summary>
            /// Starts provision the cross connection.
            /// </summary>
            /// <param name="subscriptionId">The subscription identifier.</param>
            /// <param name="crossConnectionName">The cross connection name.</param>
            [HttpPost]
            [ActionName("ProvisionCrossConnection")]
            public Task<HttpResponseMessage> ProvisionCrossConnection(
                string subscriptionId,
                string crossConnectionName)
            {
                if (this.host.OnProvisionCrossConnection != null)
                {
                    return Task.FromResult(this.host.OnProvisionCrossConnection(this.Request, null));
                }

                return Task.FromResult(new HttpResponseMessage(HttpStatusCode.OK));
            }

            /// <summary>
            /// Finishes provision the cross connection.
            /// </summary>
            /// <param name="subscriptionId">The subscription identifier.</param>
            /// <param name="crossConnectionName">The cross connection name.</param>
            [HttpPut]
            [ActionName("UpdateCrossConnection")]
            public Task<HttpResponseMessage> UpdateCrossConnection(
                string subscriptionId,
                string crossConnectionName)
            {
                if (this.host.OnUpdateCrossConnection != null)
                {
                    return Task.FromResult(this.host.OnUpdateCrossConnection(this.Request, null));
                }

                return Task.FromResult(new HttpResponseMessage(HttpStatusCode.OK));
            }

            /// <summary>
            /// Gets the BGP peering.
            /// </summary>
            /// <param name="subscriptionId">The subscription identifier.</param>
            /// <param name="crossConnectionName">The cross connection name.</param>
            /// <param name="peeringType">The peering type.</param>
            [HttpGet]
            public Task<HttpResponseMessage> GetBgpPeeringRequest(
                string subscriptionId,
                string crossConnectionName,
                string peeringType)
            {
                if (this.host.OnGetBgpPeering != null)
                {
                    return Task.FromResult(this.host.OnGetBgpPeering(this.Request, subscriptionId, crossConnectionName, peeringType));
                }

                return Task.FromResult(new HttpResponseMessage(HttpStatusCode.OK));
            }

            /// <summary>
            /// Puts the BGP peering.
            /// </summary>
            /// <param name="subscriptionId">The subscription identifier.</param>
            /// <param name="crossConnectionName">The cross connection name.</param>
            /// <param name="peeringType">The peering type.</param>
            [HttpPost]
            public Task<HttpResponseMessage> PutBgpPeering(
                string subscriptionId,
                string crossConnectionName,
                string peeringType)
            {
                if (this.host.OnPutBgpPeering != null)
                {
                    return Task.FromResult(this.host.OnPutBgpPeering(this.Request, subscriptionId, crossConnectionName, peeringType));
                }

                return Task.FromResult(new HttpResponseMessage(HttpStatusCode.OK));
            }

            /// <summary>
            /// Deletes the BGP peering.
            /// </summary>
            /// <param name="subscriptionId">The subscription identifier.</param>
            /// <param name="crossConnectionName">The cross connection name.</param>
            /// <param name="peeringType">The peering type.</param>
            [HttpDelete]
            public Task<HttpResponseMessage> DeleteBgpPeering(
                string subscriptionId,
                string crossConnectionName,
                string peeringType)
            {
                if (this.host.OnDeleteBgpPeering != null)
                {
                    return Task.FromResult(this.host.OnDeleteBgpPeering(this.Request, subscriptionId, crossConnectionName, peeringType));
                }

                return Task.FromResult(new HttpResponseMessage(HttpStatusCode.OK));
            }

            /// <summary>
            /// Gets the BGP peering.
            /// </summary>
            /// <param name="subscriptionId">The subscription identifier.</param>
            /// <param name="crossConnectionName">The cross connection name.</param>
            /// <param name="peeringType">The peering type.</param>
            [HttpPut]
            public Task<HttpResponseMessage> PatchBgpPeering(
                string subscriptionId,
                string crossConnectionName,
                string peeringType)
            {
                if (this.host.OnPatchBgpPeering != null)
                {
                    return Task.FromResult(this.host.OnPatchBgpPeering(this.Request, subscriptionId, crossConnectionName, peeringType));
                }

                return Task.FromResult(new HttpResponseMessage(HttpStatusCode.OK));
            }
        }

        #region ExceptionTraceFilter

        /// <summary>
        /// The exception trace filter.
        /// </summary>
        public class ExceptionTraceFilter : ActionFilterAttribute, IExceptionFilter
        {
            /// <summary>
            /// Executes the exception filter.
            /// </summary>
            /// <param name="actionExecutedContext">The action executed context.</param>
            /// <param name="cancellationToken">The cancellation token.</param>
            public Task ExecuteExceptionFilterAsync(HttpActionExecutedContext actionExecutedContext, CancellationToken cancellationToken)
            {
                Trace.TraceError("[LegacyManagementApiTestHost] Unhandled exception: {0}", actionExecutedContext.Exception);
                return Task.FromResult(false);
            }
        }

        #endregion

        #region AuthenticationHandler

        /// <summary>
        /// The Authentication handler.
        /// </summary>
        public class AuthenticationHandler : DelegatingHandler
        {
            /// <summary>
            /// The legacy cloud service test host.
            /// </summary>
            private readonly LegacyManagementApiTestHost host;

            /// <summary>
            /// Initializes a new instance of the <see cref="AuthenticationHandler"/> class.
            /// </summary>
            /// <param name="host">The service host.</param>
            public AuthenticationHandler(LegacyManagementApiTestHost host)
            {
                this.host = host;
            }

            /// <summary>
            /// Sends an HTTP request to the inner handler to send to the server as an asynchronous operation.
            /// </summary>
            /// <param name="request">The HTTP request message.</param>
            /// <param name="cancellationToken">A cancellation token to cancel operation.</param>
            protected override async Task<HttpResponseMessage> SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
            {
                if (this.host.OnAuthenticateRequest != null)
                {
                    if (request.RequestUri.AbsolutePath.EqualsInsensitively("/authenticationMetadata"))
                    {
                        if (this.host.OnGetAuthenticationMetadata != null)
                        {
                            return this.host.OnGetAuthenticationMetadata(request, null);
                        }

                        var metadataResponse = request.CreateResponse(HttpStatusCode.Unauthorized);
                        metadataResponse.Headers.Add(
                            name: TestEnvironment.DstsAuthenticationHeaderName,
                            value: TestEnvironment.GetRdfeDstsAuthenticateHeader());

                        return metadataResponse;
                    }

                    var response = this.host.OnAuthenticateRequest(request, null);

                    if (response.StatusCode != HttpStatusCode.OK)
                    {
                        return response;
                    }

                    var authorizationHeader = request.Headers.GetFirstOrDefault("authorization");
                    var authorized = TestEnvironment.RdfeAuthenticateRequest(authorizationHeader);

                    if (!authorized)
                    {
                        return request.CreateResponse(HttpStatusCode.Unauthorized);
                    }
                }

                return await base.SendAsync(request, cancellationToken).ConfigureAwait(continueOnCapturedContext: false);
            }
        }

        #endregion
    }
}