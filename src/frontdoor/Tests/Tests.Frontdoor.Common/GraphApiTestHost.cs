﻿//-----------------------------------------------------------
// Copyright (c) Microsoft Corporation.  All rights reserved.
//-----------------------------------------------------------

namespace Internal.WindowsAzure.ResourceStack.Tests.Frontdoor.Common
{
    using System;
    using System.Collections.Generic;
    using System.Linq;
    using System.Net;
    using System.Net.Http;
    using System.Web.Http;
    using System.Web.Http.Controllers;
    using System.Web.Http.Dependencies;
    using System.Web.Http.Dispatcher;
    using Internal.WindowsAzure.ResourceStack.Tests.Common;
    using Microsoft.WindowsAzure.ResourceStack.Common.Extensions;
    using Newtonsoft.Json.Linq;

    /// <summary>
    /// Delegate used for getting member groups.
    /// </summary>
    /// <param name="request">The incoming request.</param>
    /// <param name="tenantId">The tenant id.</param>
    /// <param name="objectId">The object id.</param>
    /// <param name="requestBody">The incoming request body.</param>
    public delegate HttpResponseMessage GetMemberGroupsDelegate(HttpRequestMessage request, string tenantId, string objectId, JObject requestBody);

    /// <summary>
    /// Delegate used for tenant level requests.
    /// </summary>
    /// <param name="request">The incoming request.</param>
    /// <param name="tenantId">The tenant Id.</param>
    /// <param name="requestBody">The incoming request body.</param>
    public delegate HttpResponseMessage TenantRequestDelegate(HttpRequestMessage request, string tenantId, JObject requestBody);

    /// <summary>
    /// Delegate used for user requests.
    /// </summary>
    /// <param name="request">The incoming request.</param>
    /// <param name="tenantId">The tenant Id.</param>
    /// <param name="userId">The user Id.</param>
    public delegate HttpResponseMessage GetUserRequestDelegate(HttpRequestMessage request, string tenantId, string userId);

    /// <summary>
    /// The web service host for the graph <c>api</c>.
    /// </summary>
    public class GraphApiTestHost : ServiceTestHost<GraphApiTestHost.GraphApiTestHostServer>, IDisposable
    {
         /// <summary>
        /// Initializes a new instance of the <see cref="GraphApiTestHost"/> class.
        /// </summary>
        /// <param name="serviceUri">The service URI.</param>
        public GraphApiTestHost(Uri serviceUri)
            : base(serviceUri)
        {
            this.Start();
        }

        /// <summary>
        /// Performs application-defined tasks associated with freeing, releasing, or resetting unmanaged resources.
        /// </summary>
        public void Dispose()
        {
            this.Stop();
        }

        /// <summary>
        /// Restarts this instance.
        /// </summary>
        public void Restart()
        {
            this.Stop();
            this.Start();
        }

        /// <summary>
        /// Gets the state of the test host.
        /// </summary>
        protected override object GetState
        {
            get { return this; }
        }

        /// <summary>
        /// Gets or sets the delegate called when member groups request is received.
        /// </summary>
        public GetMemberGroupsDelegate OnGetMemberGroups { get; set; }

        /// <summary>
        /// Gets or sets the delegate called when get users request is received.
        /// </summary>
        public TenantRequestDelegate OnGetUsers { get; set; }

        /// <summary>
        /// Gets or sets the delegate called when get user request is received.
        /// </summary>
        public GetUserRequestDelegate OnGetUser { get; set; }

        /// <summary>
        /// Gets or sets the delegate called when invite user request is received.
        /// </summary>
        public TenantRequestDelegate OnInviteUser { get; set; }

        /// <summary>
        /// Gets or sets the delegate called when an invitation redemption request is received.
        /// </summary>
        public TenantRequestDelegate OnRedeemInvitation { get; set; }

        /// <summary>
        /// Gets or sets the delegate called when a get service principal request is received.
        /// </summary>
        public TenantRequestDelegate OnGetServicePrincipal { get; set; }

        /// <summary>
        /// Gets or sets the delegate called when a post service principal request is received.
        /// </summary>
        public TenantRequestDelegate OnPostServicePrincipal { get; set; }

        /// <summary>
        /// Gets or sets the delegate called when a get tenant detail request is received.
        /// </summary>
        public TenantRequestDelegate OnGetTenantDetails { get; set; }

        /// <summary>
        /// Gets or sets the delegate called when a post requesting for getting the tenants by alternative security id is received.
        /// </summary>
        public TenantRequestDelegate OnPostTenantsByKey { get; set; }

        /// <summary>
        /// Called to configure http configuration.
        /// </summary>
        /// <param name="configuration">The http configuration.</param>
        private void OnConfigure(HttpConfiguration configuration)
        {
            configuration.Routes.MapHttpRoute(
                name: "GetMemberGroups",
                routeTemplate: "{tenantId}/users/{objectId}/getMemberGroups",
                defaults: new { controller = "GraphApi", action = "GetMemberGroups" },
                constraints: new { HttpMethod = new System.Web.Http.Routing.HttpMethodConstraint(HttpMethod.Post) });

            configuration.Routes.MapHttpRoute(
                name: "GetUsers",
                routeTemplate: "{tenantId}/users",
                defaults: new { controller = "GraphApi", action = "GetUsers" },
                constraints: new { HttpMethod = new System.Web.Http.Routing.HttpMethodConstraint(HttpMethod.Get) });

            configuration.Routes.MapHttpRoute(
                name: "GetUser",
                routeTemplate: "{tenantId}/users/{userId}",
                defaults: new { controller = "GraphApi", action = "GetUser" },
                constraints: new { HttpMethod = new System.Web.Http.Routing.HttpMethodConstraint(HttpMethod.Get) });

            configuration.Routes.MapHttpRoute(
                name: "InviteUser",
                routeTemplate: "{tenantId}/users",
                defaults: new { controller = "GraphApi", action = "InviteUser" },
                constraints: new { HttpMethod = new System.Web.Http.Routing.HttpMethodConstraint(HttpMethod.Post) });

            configuration.Routes.MapHttpRoute(
                name: "RedeemInvitation",
                routeTemplate: "{tenantId}/redeemInvitation",
                defaults: new { controller = "GraphApi", action = "RedeemInvitation" },
                constraints: new { HttpMethod = new System.Web.Http.Routing.HttpMethodConstraint(HttpMethod.Post) });

            configuration.Routes.MapHttpRoute(
                name: "GetServicePrincipal",
                routeTemplate: "{tenantId}/servicePrincipals",
                defaults: new { controller = "GraphApi", action = "GetServicePrincipal" },
                constraints: new { HttpMethod = new System.Web.Http.Routing.HttpMethodConstraint(HttpMethod.Get) });

            configuration.Routes.MapHttpRoute(
                name: "PostServicePrincipal",
                routeTemplate: "{tenantId}/servicePrincipals",
                defaults: new { controller = "GraphApi", action = "PostServicePrincipal" },
                constraints: new { HttpMethod = new System.Web.Http.Routing.HttpMethodConstraint(HttpMethod.Post) });

            configuration.Routes.MapHttpRoute(
                name: "PostTenantsByKey",
                routeTemplate: "{tenantId}/getTenantsByKey",
                defaults: new { controller = "GraphApi", action = "PostTenantsByKey" },
                constraints: new { HttpMethod = new System.Web.Http.Routing.HttpMethodConstraint(HttpMethod.Post) });

            configuration.Routes.MapHttpRoute(
                name: "GetTenantDetails",
                routeTemplate: "{tenantId}/tenantDetails",
                defaults: new { controller = "GraphApi", action = "GetTenantDetails" },
                constraints: new { HttpMethod = new System.Web.Http.Routing.HttpMethodConstraint(HttpMethod.Get) });

            configuration.Services.Replace(typeof(IHttpControllerSelector), new GraphApiTestHostControllerSelector(this, configuration));
            configuration.DependencyResolver = new GraphApiTestHostDependencyResolver(this, configuration);
        }

        /// <summary>
        /// The graph <c>api</c> test host server.
        /// </summary>
        public class GraphApiTestHostServer : ServiceTestHostServer
        {
            /// <summary>
            /// Initializes a new instance of the <see cref="GraphApiTestHostServer"/> class.
            /// </summary>
            /// <param name="serviceUri">The service URI.</param>
            /// <param name="state">The instance state.</param>
            public GraphApiTestHostServer(Uri serviceUri, object state)
                : base(serviceUri, state)
            {
            }

            /// <summary>
            /// Called to configure http configuration.
            /// </summary>
            /// <param name="configuration">The http configuration.</param>
            /// <param name="state">The instance state.</param>
            protected override void OnConfigure(HttpConfiguration configuration, object state)
            {
                (state as GraphApiTestHost).OnConfigure(configuration);
            }
        }

        #region Graph api controller

        /// <summary>
        /// The graph <c>api</c> request controller.
        /// </summary>
        public class GraphApiController : ApiController
        {
            /// <summary>
            /// The graph <c>api</c> test host.
            /// </summary>
            private readonly GraphApiTestHost host;

            /// <summary>
            /// Initializes a new instance of the <see cref="GraphApiController"/> class.
            /// </summary>
            /// <param name="host">The graph <c>api</c> test host.</param>
            public GraphApiController(GraphApiTestHost host)
            {
                this.host = host;
            }

            /// <summary>
            /// Responds to get member groups request.
            /// </summary>
            /// <param name="tenantId">The tenant id.</param>
            /// <param name="objectId">The object id.</param>
            /// <param name="requestBody">The body of the request.</param>
            [HttpPost]
            public HttpResponseMessage GetMemberGroups(string tenantId, string objectId, [FromBody] JObject requestBody)
            {
                if (this.host.OnGetMemberGroups != null)
                {
                    return this.host.OnGetMemberGroups(this.Request, tenantId, objectId, requestBody);
                }

                return this.Request.CreateResponse(HttpStatusCode.OK);
            }

            /// <summary>
            /// Responds to get users request.
            /// </summary>
            /// <param name="tenantId">The tenant being searched.</param>
            /// <param name="requestBody">The body of the request.</param>
            [HttpGet]
            public HttpResponseMessage GetUsers(string tenantId, [FromBody] JObject requestBody)
            {
                if (this.host.OnGetUsers != null)
                {
                    return this.host.OnGetUsers(this.Request, tenantId, requestBody);
                }

                return this.Request.CreateResponse(HttpStatusCode.OK);
            }

            /// <summary>
            /// Responds to get user request.
            /// </summary>
            /// <param name="tenantId">The tenant being searched.</param>
            /// <param name="userId">The user id.</param>
            [HttpGet]
            public HttpResponseMessage GetUser(string tenantId, string userId)
            {
                if (this.host.OnGetUser != null)
                {
                    return this.host.OnGetUser(this.Request, tenantId, userId);
                }

                return this.Request.CreateResponse(HttpStatusCode.OK);
            }

            /// <summary>
            /// Responds to user invitation request.
            /// </summary>
            /// <param name="tenantId">The tenant being added to.</param>
            /// <param name="requestBody">The body of the request.</param>
            [HttpPost]
            public HttpResponseMessage InviteUser(string tenantId, [FromBody] JObject requestBody)
            {
                if (this.host.OnInviteUser != null)
                {
                    return this.host.OnInviteUser(this.Request, tenantId, requestBody);
                }

                return this.Request.CreateResponse(HttpStatusCode.Accepted);
            }

            /// <summary>
            /// Responds to invitation redemption request.
            /// </summary>
            /// <param name="tenantId">The tenant to redeem in.</param>
            /// <param name="requestBody">The body of the request.</param>
            [HttpPost]
            public HttpResponseMessage RedeemInvitation(string tenantId, [FromBody] JObject requestBody)
            {
                if (this.host.OnRedeemInvitation != null)
                {
                    return this.host.OnRedeemInvitation(this.Request, tenantId, requestBody);
                }

                return this.Request.CreateResponse(HttpStatusCode.OK);
            }

            /// <summary>
            /// Responds to get service principal request.
            /// </summary>
            /// <param name="tenantId">The tenant id.</param>
            /// <param name="requestBody">The body of the request.</param>
            [HttpGet]
            public HttpResponseMessage GetServicePrincipal(string tenantId, [FromBody] JObject requestBody)
            {
                if (this.host.OnGetServicePrincipal != null)
                {
                    return this.host.OnGetServicePrincipal(this.Request, tenantId, requestBody);
                }

                return this.Request.CreateResponse(HttpStatusCode.OK);
            }

            /// <summary>
            /// Responds to post service principal request.
            /// </summary>
            /// <param name="tenantId">The tenant id.</param>
            /// <param name="requestBody">The body of the request.</param>
            [HttpPost]
            public HttpResponseMessage PostServicePrincipal(string tenantId, [FromBody] JObject requestBody)
            {
                if (this.host.OnPostServicePrincipal != null)
                {
                    return this.host.OnPostServicePrincipal(this.Request, tenantId, requestBody);
                }

                return this.Request.CreateResponse(HttpStatusCode.Created);
            }

            /// <summary>
            /// Responds to post request for getting tenants by alternative security ids.
            /// </summary>
            /// <param name="tenantId">The tenant id.</param>
            /// <param name="requestBody">The body of the request.</param>
            [HttpPost]
            public HttpResponseMessage PostTenantsByKey(string tenantId, [FromBody] JObject requestBody)
            {
                if (this.host.OnPostTenantsByKey != null)
                {
                    return this.host.OnPostTenantsByKey(this.Request, tenantId, requestBody);
                }

                return this.Request.CreateResponse(HttpStatusCode.OK);
            }

            /// <summary>
            /// Responds to get tenant detail request.
            /// </summary>
            /// <param name="tenantId">The tenant id.</param>
            /// <param name="requestBody">The body of the request.</param>
            [HttpGet]
            public HttpResponseMessage GetTenantDetails(string tenantId, [FromBody] JObject requestBody)
            {
                if (this.host.OnGetTenantDetails != null)
                {
                    return this.host.OnGetTenantDetails(this.Request, tenantId, requestBody);
                }

                return this.Request.CreateResponse(HttpStatusCode.OK);
            }
        }

        #endregion

        #region GraphApiTestHostControllerSelector

        /// <summary>
        /// The graph <c>api</c> test host controller selector.
        /// </summary>
        private class GraphApiTestHostControllerSelector : DefaultHttpControllerSelector
        {
            /// <summary>
            /// The graph <c>api</c> test host.
            /// </summary>
            private readonly GraphApiTestHost host;

            /// <summary>
            /// The HTTP configuration.
            /// </summary>
            private readonly HttpConfiguration configuration;

            /// <summary>
            /// Initializes a new instance of the <see cref="GraphApiTestHostControllerSelector" /> class.
            /// </summary>
            /// <param name="host">The host.</param>
            /// <param name="configuration">The configuration.</param>
            public GraphApiTestHostControllerSelector(GraphApiTestHost host, HttpConfiguration configuration)
                : base(configuration)
            {
                this.host = host;
                this.configuration = configuration;
            }

            /// <summary>
            /// Selects the controller.
            /// </summary>
            /// <param name="request">The request.</param>
            public override HttpControllerDescriptor SelectController(HttpRequestMessage request)
            {
                var matchedTypes = typeof(GraphApiTestHost)
                    .GetNestedTypes()
                    .Where(type => typeof(IHttpController).IsAssignableFrom(type))
                    .ToList();

                var controllerName = this.GetControllerName(request);
                var matchedController = matchedTypes.FirstOrDefault(type => type.Name.EqualsInsensitively(controllerName + "controller"));

                return new HttpControllerDescriptor(this.configuration, controllerName, matchedController);
            }
        }

        #endregion

        #region Graph api dependency respolver

        /// <summary>
        /// The graph <c>api</c> test host dependency resolver.
        /// </summary>
        private class GraphApiTestHostDependencyResolver : IDependencyResolver
        {
            /// <summary>
            /// The graph <c>api</c> test host.
            /// </summary>
            private readonly GraphApiTestHost host;

            /// <summary>
            /// The HTTP configuration.
            /// </summary>
            private readonly HttpConfiguration configuration;

            /// <summary>
            /// Initializes a new instance of the <see cref="GraphApiTestHostDependencyResolver" /> class.
            /// </summary>
            /// <param name="host">The host.</param>
            /// <param name="configuration">The configuration.</param>
            public GraphApiTestHostDependencyResolver(GraphApiTestHost host, HttpConfiguration configuration)
            {
                this.host = host;
                this.configuration = configuration;
            }

            /// <summary>
            /// Begins the scope.
            /// </summary>
            public IDependencyScope BeginScope()
            {
                return this;
            }

            /// <summary>
            /// Gets the service.
            /// </summary>
            /// <param name="serviceType">Type of the service.</param>
            public object GetService(Type serviceType)
            {
                if (typeof(IHttpController).IsAssignableFrom(serviceType))
                {
                    if (typeof(GraphApiTestHost).GetNestedTypes().Any(type => type == serviceType))
                    {
                        return Activator.CreateInstance(serviceType, args: new object[] { this.host });
                    }
                }

                return null;
            }

            /// <summary>
            /// Gets the services.
            /// </summary>
            /// <param name="serviceType">Type of the service.</param>
            public IEnumerable<object> GetServices(Type serviceType)
            {
                return Enumerable.Empty<object>();
            }

            /// <summary>
            /// Performs application-defined tasks associated with freeing, releasing, or resetting unmanaged resources.
            /// </summary>
            public void Dispose()
            {
            }
        }

        #endregion
    }
}
