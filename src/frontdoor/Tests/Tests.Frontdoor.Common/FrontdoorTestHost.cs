﻿//-----------------------------------------------------------
// Copyright (c) Microsoft Corporation.  All rights reserved.
//-----------------------------------------------------------

namespace Internal.WindowsAzure.ResourceStack.Tests.Frontdoor.Common
{
    using System;
    using System.Collections.Generic;
    using System.Linq;
    using System.Net;
    using System.Net.Http;
    using System.Threading;
    using System.Threading.Tasks;
    using System.Web.Http;
    using System.Web.Http.Controllers;
    using System.Web.Http.Dependencies;
    using System.Web.Http.Dispatcher;
    using System.Web.Http.Routing;
    using Internal.WindowsAzure.ResourceStack.Tests.Common;
    using Microsoft.WindowsAzure.ResourceStack.Common.Extensions;
    using Microsoft.WindowsAzure.ResourceStack.Common.Instrumentation;
    using Microsoft.WindowsAzure.ResourceStack.Frontdoor.Data.Configuration;
    using Microsoft.WindowsAzure.ResourceStack.Frontdoor.Data.Entities.Authentication;
    using Microsoft.WindowsAzure.ResourceStack.Frontdoor.Web;
    using Microsoft.WindowsAzure.ResourceStack.Frontdoor.Web.Controllers;

    /// <summary>
    /// The web service host for the front door.
    /// </summary>
    public class FrontdoorTestHost : ServiceTestHost<FrontdoorTestHost.FrontdoorTestHostServer>, IDisposable
    {
        /// <summary>
        /// Gets or sets the request interceptor.
        /// </summary>
        private Action<HttpRequestMessage, object> RequestInterceptor { get; set; }

        /// <summary>
        /// Gets or sets the request handler.
        /// </summary>
        private Func<HttpRequestMessage, Func<HttpRequestMessage, Task<HttpResponseMessage>>, Task<HttpResponseMessage>> RequestHandler { get; set; }

        /// <summary>
        /// The Get Storage Properties delegate
        /// </summary>
        /// <param name="request">The request.</param>
        /// <param name="subscriptionId">The subscription identifier.</param>
        /// <param name="resourceGroupName">Name of the resource group.</param>
        /// <param name="storageAccountName">Name of the storage account.</param>
        public delegate HttpResponseMessage GetStoragePropertiesDelegate(HttpRequestMessage request, string subscriptionId, string resourceGroupName, string storageAccountName);

        /// <summary>
        /// The Patch Storage account delegate
        /// </summary>
        /// <param name="request">The request.</param>
        /// <param name="subscriptionId">The subscription identifier.</param>
        /// <param name="resourceGroupName">Name of the resource group.</param>
        /// <param name="storageAccountName">Name of the storage account.</param>
        public delegate Task<HttpResponseMessage> PatchStorageAccountDelegate(HttpRequestMessage request, string subscriptionId, string resourceGroupName, string storageAccountName);

        /// <summary>
        /// Gets or sets the state to pass into the request interceptor.
        /// </summary>
        private object InterceptorState { get; set; }

        /// <summary>
        /// Initializes a new instance of the <see cref="FrontdoorTestHost"/> class.
        /// </summary>
        /// <param name="serviceUri">The service URI.</param>
        public FrontdoorTestHost(Uri serviceUri)
            : base(serviceUri)
        {
            this.Start();
        }

        /// <summary>
        /// Initializes a new instance of the <see cref="FrontdoorTestHost" /> class.
        /// </summary>
        /// <param name="serviceUri">The service URI.</param>
        /// <param name="assemblyConfigurationPath">The assembly configuration path.</param>
        public FrontdoorTestHost(Uri serviceUri, string assemblyConfigurationPath)
            : this(serviceUri, assemblyConfigurationPath, null, null)
        {
        }

        /// <summary>
        /// Initializes a new instance of the <see cref="FrontdoorTestHost" /> class.
        /// </summary>
        /// <param name="serviceUri">The service URI.</param>
        /// <param name="assemblyConfigurationPath">The assembly configuration path.</param>
        /// <param name="requestInterceptor">Action injected in request pipeline.</param>
        public FrontdoorTestHost(Uri serviceUri, string assemblyConfigurationPath, Action<HttpRequestMessage, object> requestInterceptor)
            : this(serviceUri, assemblyConfigurationPath, requestInterceptor, null)
        {
        }

        /// <summary>
        /// Initializes a new instance of the <see cref="FrontdoorTestHost" /> class.
        /// </summary>
        /// <param name="serviceUri">The service URI.</param>
        /// <param name="assemblyConfigurationPath">The assembly configuration path.</param>
        /// <param name="requestInterceptor">Action injected in request pipeline.</param>
        /// <param name="interceptorState">Serializable object passed to interceptor.</param>
        public FrontdoorTestHost(Uri serviceUri, string assemblyConfigurationPath, Action<HttpRequestMessage, object> requestInterceptor, object interceptorState)
            : base(serviceUri, "FrontdoorTestHost", assemblyConfigurationPath)
        {
            this.RequestInterceptor = requestInterceptor;
            this.InterceptorState = interceptorState;
            this.Start();
        }

        /// <summary>
        /// Initializes a new instance of the <see cref="FrontdoorTestHost" /> class.
        /// </summary>
        /// <param name="serviceUri">The service URI.</param>
        /// <param name="assemblyConfigurationPath">The assembly configuration path.</param>
        /// <param name="requestHandler">The request handler.</param>
        public FrontdoorTestHost(Uri serviceUri, string assemblyConfigurationPath, Func<HttpRequestMessage, Func<HttpRequestMessage, Task<HttpResponseMessage>>, Task<HttpResponseMessage>> requestHandler)
            : base(serviceUri, "FrontdoorTestHost", assemblyConfigurationPath)
        {
            this.RequestHandler = requestHandler;
            this.Start();
        }

        /// <summary>
        /// Initializes a new instance of the <see cref="FrontdoorTestHost" /> class.
        /// </summary>
        /// <param name="serviceUri">The service URI.</param>
        /// <param name="assemblyConfigurationPath">The assembly configuration path.</param>
        /// <param name="puid">The PUID.</param>
        /// <param name="objectId">The object identifier.</param>
        /// <param name="tenantId">The tenant identifier.</param>
        public FrontdoorTestHost(Uri serviceUri, string assemblyConfigurationPath, string puid, string objectId, string tenantId)
            : base(serviceUri, "FrontdoorTestHost", assemblyConfigurationPath)
        {
            this.RequestInterceptor = (HttpRequestMessage request, object state) =>
            {
                var inputs = state as string[];

                // Manually set the authentication principal to avoid AAD prompt
                var claimsPrincipal = TestEnvironment.CreateClaimsPrincipal(puid: inputs[0], tenantId: inputs[1], objectId: inputs[2]);
                RequestCorrelationContext.Current.SetAuthenticationIdentity(identity: claimsPrincipal.ToRequestCorrelationContextIdentity());
            };

            this.InterceptorState = new string[] { puid, tenantId, objectId };
            this.Start();
        }

        /// <summary>
        /// Performs application-defined tasks associated with freeing, releasing, or resetting unmanaged resources.
        /// </summary>
        public void Dispose()
        {
            this.Stop();
        }

        /// <summary>
        /// Stops this instance.
        /// </summary>
        public new void Stop()
        {
            base.Stop();
        }

        /// <summary>
        /// Restarts this instance.
        /// </summary>
        public void Restart()
        {
            this.Stop();
            this.Start();
        }

        /// <summary>
        /// Gets the test events listener.
        /// </summary>
        public TestEventsListenerHost TestEventsListener
        {
            get { return this.HostServer.TestEventsListener; }
        }

        /// <summary>
        /// Gets the state of the test host.
        /// </summary>
        protected override object GetState
        {
            get { return this; }
        }

        /// <summary>
        /// Starts this instance.
        /// </summary>
        protected override void Start()
        {
            TestEnvironment.RestartCaches();
            base.Start();
        }

        /// <summary>
        /// The front door test host server.
        /// </summary>
        public class FrontdoorTestHostServer : ServiceTestHostServer
        {
            /// <summary>
            /// Initializes a new instance of the <see cref="FrontdoorTestHostServer"/> class.
            /// </summary>
            /// <param name="serviceUri">The service URI.</param>
            /// <param name="state">The instance state.</param>
            public FrontdoorTestHostServer(Uri serviceUri, object state)
                : base(serviceUri, state)
            {
            }

            /// <summary>
            /// Called to configure http configuration.
            /// </summary>
            /// <param name="configuration">The http configuration.</param>
            /// <param name="state">The instance state.</param>
            protected override void OnConfigure(HttpConfiguration configuration, object state)
            {
                TestEventsListenerHost.Instance.EnsureInitialized();

                var host = state as FrontdoorTestHost;
                if (host != null && host.RequestInterceptor != null)
                {
                    var handler = new InterceptHandler();
                    handler.RequestInterceptor = host.RequestInterceptor;
                    handler.InterceptorState = host.InterceptorState;
                    configuration.MessageHandlers.Add(handler);
                }

                if (host != null && host.RequestHandler != null)
                {
                    var handler = new InjectiontHandler();
                    handler.RequestHandler = host.RequestHandler;
                    configuration.MessageHandlers.Add(handler);
                }

                // NOTE(wayan): this has to be put before the wildcard route.
                configuration.Routes.MapHttpRoute(
                    name: "CheckPostAction",
                    routeTemplate: "subscriptions/{subscriptionId}/providers/Microsoft.Resources/checkPostAction",
                    defaults: new { controller = "FrontdoorTestHost", action = "CheckPostAction" },
                    constraints: new { httpMethod = new HttpMethodConstraint(HttpMethod.Post) });

                configuration.Routes.MapHttpRoute(
                    name: "GetStorageProperties",
                    routeTemplate: "subscriptions/{subscriptionId}/resourceGroups/{resourceGroupName}/providers/Microsoft.Storage/storageAccounts/{storageAccountName}",
                    defaults: new { controller = "FrontdoorTestHost" },
                    constraints: new { HttpMethod = new HttpMethodConstraint(HttpMethod.Get) });

                HttpConfigurationInitializer.Instance.Initialize(configuration, FrontdoorConfigurationLoader.PrimaryFrontdoorConfiguration);

                configuration.Services.Replace(typeof(IHttpControllerSelector), new FrontdoorTestHostControllerSelector(configuration));
                configuration.DependencyResolver = new FrontdoorTestHostDependencyResolver(host, FrontdoorConfigurationLoader.PrimaryFrontdoorConfiguration, configuration);
            }

            /// <summary>
            /// Called to configure http configuration.
            /// </summary>
            /// <param name="configuration">The http configuration.</param>
            /// <param name="state">The instance state.</param>
            protected override void OnFinalize(HttpConfiguration configuration, object state)
            {
                HttpConfigurationInitializer.Instance.Finalize(configuration, FrontdoorConfigurationLoader.PrimaryFrontdoorConfiguration);
            }

            /// <summary>
            /// Gets the test events listener.
            /// </summary>
            public TestEventsListenerHost TestEventsListener
            {
                get { return TestEventsListenerHost.Instance; }
            }
        }

        /// <summary>
        /// Handler that calls out to the request interceptor at the start of the front door pipeline.
        /// </summary>
        public class InterceptHandler : DelegatingHandler
        {
            /// <summary>
            /// Gets or sets the request interceptor.
            /// </summary>
            public Action<HttpRequestMessage, object> RequestInterceptor { get; set; }

            /// <summary>
            /// Gets or sets the state to pass into the request interceptor.
            /// </summary>
            public object InterceptorState { get; set; }

            /// <summary>
            /// Injection handler processing.
            /// </summary>
            /// <param name="request">The http request message.</param>
            /// <param name="cancellationToken">The request cancellation token.</param>
            protected override Task<HttpResponseMessage> SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
            {
                if (this.RequestInterceptor != null)
                {
                    this.RequestInterceptor(request, this.InterceptorState);
                }

                return base.SendAsync(request, cancellationToken);
            }
        }

        /// <summary>
        /// Handler that calls out to the request interceptor at the start of the front door pipeline.
        /// </summary>
        public class InjectiontHandler : DelegatingHandler
        {
            /// <summary>
            /// Gets or sets the request interceptor.
            /// </summary>
            public Func<HttpRequestMessage, Func<HttpRequestMessage, Task<HttpResponseMessage>>, Task<HttpResponseMessage>> RequestHandler { get; set; }

            /// <summary>
            /// Injection handler processing.
            /// </summary>
            /// <param name="request">The http request message.</param>
            /// <param name="cancellationToken">The request cancellation token.</param>
            protected override Task<HttpResponseMessage> SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
            {
                if (this.RequestHandler != null)
                {
                    return this.RequestHandler(request, async (req) => await base.SendAsync(req, cancellationToken).ConfigureAwait(continueOnCapturedContext: false));
                }

                return base.SendAsync(request, cancellationToken);
            }
        }

        #region Mock frontdoor test controller

        /// <summary>
        /// Gets or sets the callback for on check post action.
        /// </summary>
        public Func<HttpRequestMessage, string, HttpResponseMessage> OnCheckPostAction { get; set; }

        /// <summary>
        /// Gets or sets the on get resource.
        /// </summary>
        /// <value>
        /// The on get resource.
        /// </value>
        public GetStoragePropertiesDelegate OnGetResource { get; set; }

        /// <summary>
        /// Gets or sets the on patch storage resource.
        /// </summary>
        /// <value>
        /// The on patch storage resource.
        /// </value>
        public PatchStorageAccountDelegate OnPatchStorageResource { get; set; }

        /// <summary>
        /// The mock front door test controller.
        /// </summary>
        public class FrontdoorTestHostController : ApiController
        {
            /// <summary>
            /// The test host.
            /// </summary>
            private readonly FrontdoorTestHost host;

            /// <summary>
            /// Initializes a new instance of the <see cref="FrontdoorTestHostController" /> class.
            /// </summary>
            /// <param name="host">The test host.</param>
            public FrontdoorTestHostController(FrontdoorTestHost host)
            {
                this.host = host;
            }

            /// <summary>
            /// Patches the storage account.
            /// </summary>
            /// <param name="subscriptionId">The subscription identifier.</param>
            /// <param name="resourceGroupName">Name of the resource group.</param>
            /// <param name="storageAccountName">Name of the storage account.</param>
            [HttpPatch]
            public Task<HttpResponseMessage> PatchStorageAccount(
                string subscriptionId,
                string resourceGroupName,
                string storageAccountName)
            {
                if (this.host.OnPatchStorageResource != null)
                {
                    return this.host.OnPatchStorageResource(this.Request, subscriptionId, resourceGroupName, storageAccountName);
                }

                return Task.FromResult<HttpResponseMessage>(new HttpResponseMessage(HttpStatusCode.OK));
            }

            /// <summary>
            /// Gets the storage properties.
            /// </summary>
            /// <param name="subscriptionId">The subscription identifier.</param>
            /// <param name="resourceGroupName">Name of the resource group.</param>
            /// <param name="storageAccountName">Name of the storage account.</param>
            [HttpGet]
            public Task<HttpResponseMessage> GetStorageProperties(
                string subscriptionId,
                string resourceGroupName,
                string storageAccountName)
            {
                if (this.host.OnGetResource != null)
                {
                    return Task.FromResult<HttpResponseMessage>(this.host.OnGetResource(this.Request, subscriptionId, resourceGroupName, storageAccountName));
                }

                return Task.FromResult<HttpResponseMessage>(new HttpResponseMessage(HttpStatusCode.OK));
            }

            /// <summary>
            /// Checks post action.
            /// </summary>
            /// <param name="subscriptionId">The subscription ID.</param>
            [HttpPost]
            [ActionName("CheckPostAction")]
            public HttpResponseMessage CheckPostAction(string subscriptionId)
            {
                if (this.host.OnCheckPostAction != null)
                {
                    return this.host.OnCheckPostAction(this.Request, subscriptionId);
                }

                return Request.CreateResponse(HttpStatusCode.OK);
            }
        }

        /// <summary>
        /// The mock front door test controller selector.
        /// </summary>
        private class FrontdoorTestHostControllerSelector : DefaultHttpControllerSelector
        {
            /// <summary>
            /// The HTTP configuration.
            /// </summary>
            private readonly HttpConfiguration configuration;

            /// <summary>
            /// Initializes a new instance of the <see cref="FrontdoorTestHostControllerSelector" /> class.
            /// </summary>
            /// <param name="configuration">The configuration.</param>
            public FrontdoorTestHostControllerSelector(HttpConfiguration configuration)
                : base(configuration)
            {
                this.configuration = configuration;
            }

            /// <summary>
            /// Selects the controller.
            /// </summary>
            /// <param name="request">The request.</param>
            public override HttpControllerDescriptor SelectController(HttpRequestMessage request)
            {
                var controllerName = this.GetControllerName(request);

                return controllerName.EqualsInsensitively("FrontdoorTestHost")
                    ? new HttpControllerDescriptor(this.configuration, controllerName, typeof(FrontdoorTestHostController))
                    : base.SelectController(request);
            }
        }

        /// <summary>
        /// The mock front door test dependency resolver.
        /// </summary>
        private class FrontdoorTestHostDependencyResolver : IDependencyResolver
        {
            /// <summary>
            /// The test host.
            /// </summary>
            private readonly FrontdoorTestHost host;

            /// <summary>
            /// The front door configuration.
            /// </summary>
            private readonly FrontdoorConfiguration frontdoorConfiguration;

            /// <summary>
            /// The HTTP configuration.
            /// </summary>
            private readonly HttpConfiguration configuration;

            /// <summary>
            /// Initializes a new instance of the <see cref="FrontdoorTestHostDependencyResolver" /> class.
            /// </summary>
            /// <param name="host">The test host.</param>
            /// <param name="frontdoorConfiguration">The front door configuration.</param>
            /// <param name="configuration">The configuration.</param>
            public FrontdoorTestHostDependencyResolver(FrontdoorTestHost host, FrontdoorConfiguration frontdoorConfiguration, HttpConfiguration configuration)
            {
                this.host = host;
                this.frontdoorConfiguration = frontdoorConfiguration;
                this.configuration = configuration;
            }

            /// <summary>
            /// Starts a resolution scope.
            /// </summary>
            public IDependencyScope BeginScope()
            {
                return this;
            }

            /// <summary>
            /// Performs application-defined tasks associated with freeing, releasing, or resetting unmanaged resources.
            /// </summary>
            public void Dispose()
            {
            }

            /// <summary>
            /// Retrieves a service from the scope.
            /// </summary>
            /// <param name="serviceType">The service to be retrieved.</param>
            public object GetService(Type serviceType)
            {
                // Frontdoor.Web controllers
                if (typeof(FrontdoorApiController).IsAssignableFrom(serviceType))
                {
                    return Activator.CreateInstance(serviceType, args: new object[] { this.frontdoorConfiguration, this.configuration });
                }

                // The mock controller
                if (typeof(FrontdoorTestHostController).IsAssignableFrom(serviceType))
                {
                    return Activator.CreateInstance(serviceType, args: new object[] { this.host });
                }

                return null;
            }

            /// <summary>
            /// Retrieves a collection of services from the scope.
            /// </summary>
            /// <param name="serviceType">The service to be retrieved.</param>
            public IEnumerable<object> GetServices(Type serviceType)
            {
                return Enumerable.Empty<object>();
            }
        }

        #endregion
    }
}