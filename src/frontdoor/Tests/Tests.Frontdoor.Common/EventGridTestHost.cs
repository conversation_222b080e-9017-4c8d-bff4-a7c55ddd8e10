﻿//-----------------------------------------------------------
// Copyright (c) Microsoft Corporation.  All rights reserved.
//-----------------------------------------------------------

namespace Internal.WindowsAzure.ResourceStack.Tests.Frontdoor.Common
{
    using System;
    using System.Collections.Generic;
    using System.Linq;
    using System.Net;
    using System.Net.Http;
    using System.Web.Http;
    using System.Web.Http.Controllers;
    using System.Web.Http.Dependencies;
    using System.Web.Http.Dispatcher;
    using Internal.WindowsAzure.ResourceStack.Tests.Common;
    using Microsoft.WindowsAzure.ResourceStack.Common.Extensions;
    using Newtonsoft.Json.Linq;

    /// <summary>
    /// Delegate used for listening to response notify requests.
    /// </summary>
    /// <param name="request">The incoming notify request.</param>
    /// <param name="requestBody">The incoming request body.</param>
    public delegate HttpResponseMessage EventGridDelegate(HttpRequestMessage request, JArray requestBody);

    /// <summary>
    /// The web service host for the mock event grid notification service.
    /// </summary>
    public class EventGridTestHost : ServiceTestHost<EventGridTestHost.EventGridTestHostServer>, IDisposable
    {
        /// <summary>
        /// Initializes a new instance of the <see cref="EventGridTestHost"/> class.
        /// </summary>
        /// <param name="serviceUri">The service URI.</param>
        public EventGridTestHost(Uri serviceUri)
            : base(serviceUri)
        {
            this.Start();
        }

        /// <summary>
        /// Performs application-defined tasks associated with freeing, releasing, or resetting unmanaged resources.
        /// </summary>
        public void Dispose()
        {
            this.Stop();
        }

        /// <summary>
        /// Gets the state of the test host.
        /// </summary>
        protected override object GetState
        {
            get { return this; }
        }

        /// <summary>
        /// Gets or sets the delegate called when notify request is received.
        /// </summary>
        public EventGridDelegate OnNotifyRequest { get; set; }

        /// <summary>
        /// Called to configure http configuration.
        /// </summary>
        /// <param name="configuration">The http configuration.</param>
        private void OnConfigure(HttpConfiguration configuration)
        {
            configuration.Routes.MapHttpRoute(
               name: "NotifyRequest",
               routeTemplate: "eventGrid/api/events",
               defaults: new
               {
                   controller = "EventGrid",
                   action = "Notify",
               });
            configuration.Services.Replace(typeof(IHttpControllerSelector), new EventGridTestHostControllerSelector(this, configuration));
            configuration.DependencyResolver = new EventGridTestHostDependencyResolver(this, configuration);
        }

        /// <summary>
        /// The event grid request controller.
        /// </summary>
        public class EventGridController : ApiController
        {
            /// <summary>
            /// The event grid test host.
            /// </summary>
            private readonly EventGridTestHost host;

            /// <summary>
            /// Initializes a new instance of the <see cref="EventGridController"/> class.
            /// </summary>
            /// <param name="host">The event grid host.</param>
            public EventGridController(EventGridTestHost host)
            {
                this.host = host;
            }

            /// <summary>
            /// Responds to a event grid notify request.
            /// </summary>
            /// <param name="requestBody">Body of the request.</param>
            [HttpPost]
            public HttpResponseMessage Notify([FromBody] JArray requestBody)
            {
                if (this.host.OnNotifyRequest != null)
                {
                    return this.host.OnNotifyRequest(this.Request, requestBody);
                }

                return this.Request.CreateResponse(HttpStatusCode.OK);
            }
        }

        /// <summary>
        /// The event grid test host server.
        /// </summary>
        public class EventGridTestHostServer : ServiceTestHostServer
        {
            /// <summary>
            /// Initializes a new instance of the <see cref="EventGridTestHostServer"/> class.
            /// </summary>
            /// <param name="serviceUri">The service URI.</param>
            /// <param name="state">The instance state.</param>
            public EventGridTestHostServer(Uri serviceUri, object state)
                : base(serviceUri, state)
            {
            }

            /// <summary>
            /// Called to configure http configuration.
            /// </summary>
            /// <param name="configuration">The http configuration.</param>
            /// <param name="state">The instance state.</param>
            protected override void OnConfigure(HttpConfiguration configuration, object state)
            {
                (state as EventGridTestHost).OnConfigure(configuration);
            }
        }

        /// <summary>
        /// The event grid test host controller selector.
        /// </summary>
        private class EventGridTestHostControllerSelector : DefaultHttpControllerSelector
        {
            /// <summary>
            /// The event grid test host.
            /// </summary>
            private readonly EventGridTestHost host;

            /// <summary>
            /// The HTTP configuration.
            /// </summary>
            private readonly HttpConfiguration configuration;

            /// <summary>
            /// Initializes a new instance of the <see cref="EventGridTestHostControllerSelector" /> class.
            /// </summary>
            /// <param name="host">The host.</param>
            /// <param name="configuration">The configuration.</param>
            public EventGridTestHostControllerSelector(EventGridTestHost host, HttpConfiguration configuration)
                : base(configuration)
            {
                this.host = host;
                this.configuration = configuration;
            }

            /// <summary>
            /// Selects the controller.
            /// </summary>
            /// <param name="request">The request.</param>
            public override HttpControllerDescriptor SelectController(HttpRequestMessage request)
            {
                var matchedTypes = typeof(EventGridTestHost)
                    .GetNestedTypes()
                    .Where(type => typeof(IHttpController).IsAssignableFrom(type))
                    .ToList();

                var controllerName = this.GetControllerName(request);
                var matchedController = matchedTypes.FirstOrDefault(type => type.Name.EqualsInsensitively(controllerName + "controller"));

                return new HttpControllerDescriptor(this.configuration, controllerName, matchedController);
            }
        }

        /// <summary>
        /// The event grid test host dependency resolver.
        /// </summary>
        private class EventGridTestHostDependencyResolver : IDependencyResolver
        {
            /// <summary>
            /// The event grid test host.
            /// </summary>
            private readonly EventGridTestHost host;

            /// <summary>
            /// The HTTP configuration.
            /// </summary>
            private readonly HttpConfiguration configuration;

            /// <summary>
            /// Initializes a new instance of the <see cref="EventGridTestHostDependencyResolver" /> class.
            /// </summary>
            /// <param name="host">The host.</param>
            /// <param name="configuration">The configuration.</param>
            public EventGridTestHostDependencyResolver(EventGridTestHost host, HttpConfiguration configuration)
            {
                this.host = host;
                this.configuration = configuration;
            }

            /// <summary>
            /// Begins the scope.
            /// </summary>
            public IDependencyScope BeginScope()
            {
                return this;
            }

            /// <summary>
            /// Gets the service.
            /// </summary>
            /// <param name="serviceType">Type of the service.</param>
            public object GetService(Type serviceType)
            {
                if (typeof(IHttpController).IsAssignableFrom(serviceType))
                {
                    if (typeof(EventGridTestHost).GetNestedTypes().Any(type => type == serviceType))
                    {
                        return Activator.CreateInstance(serviceType, args: new object[] { this.host });
                    }
                }

                return null;
            }

            /// <summary>
            /// Gets the services.
            /// </summary>
            /// <param name="serviceType">Type of the service.</param>
            public IEnumerable<object> GetServices(Type serviceType)
            {
                return Enumerable.Empty<object>();
            }

            /// <summary>
            /// Performs application-defined tasks associated with freeing, releasing, or resetting unmanaged resources.
            /// </summary>
            public void Dispose()
            {
            }
        }
    }
}