﻿//-----------------------------------------------------------
// Copyright (c) Microsoft Corporation.  All rights reserved.
//-----------------------------------------------------------

namespace Internal.WindowsAzure.ResourceStack.Tests.Frontdoor.Common
{
    using System;
    using System.Web.Http;
    using Internal.WindowsAzure.ResourceStack.Tests.Common;
    using Microsoft.WindowsAzure.ResourceStack.Frontdoor.Admin;
    using Microsoft.WindowsAzure.ResourceStack.Frontdoor.Data.Configuration;

    /// <summary>
    /// The web service host for the front door.
    /// </summary>
    public class AdminTestHost : ServiceTestHost<AdminTestHost.AdminTestHostServer>, IDisposable
    {
        /// <summary>
        /// Initializes a new instance of the <see cref="AdminTestHost" /> class.
        /// </summary>
        /// <param name="serviceUri">The service URI.</param>
        /// <param name="assemblyConfigurationPath">The assembly configuration path.</param>
        public AdminTestHost(Uri serviceUri, string assemblyConfigurationPath)
            : base(serviceUri, "AdminTestHost", assemblyConfigurationPath)
        {
            this.Start();
        }

        /// <summary>
        /// Performs application-defined tasks associated with freeing, releasing, or resetting unmanaged resources.
        /// </summary>
        public void Dispose()
        {
            this.Stop();
        }

        /// <summary>
        /// The front door test host server.
        /// </summary>
        public class AdminTestHostServer : ServiceTestHostServer
        {
            /// <summary>
            /// Initializes a new instance of the <see cref="AdminTestHostServer"/> class.
            /// </summary>
            /// <param name="serviceUri">The service URI.</param>
            /// <param name="state">The instance state.</param>
            public AdminTestHostServer(Uri serviceUri, object state)
                : base(serviceUri, state)
            {
            }

            /// <summary>
            /// Called to configure http configuration.
            /// </summary>
            /// <param name="configuration">The http configuration.</param>
            /// <param name="state">The instance state.</param>
            protected override void OnConfigure(HttpConfiguration configuration, object state)
            {
                HttpConfigurationInitializer.Instance.Initialize(configuration, FrontdoorConfigurationLoader.PrimaryFrontdoorConfiguration);
            }
        }
    }
}