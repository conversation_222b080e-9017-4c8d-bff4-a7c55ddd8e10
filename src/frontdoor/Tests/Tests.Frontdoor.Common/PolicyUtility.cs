﻿//-----------------------------------------------------------
// Copyright (c) Microsoft Corporation.  All rights reserved.
//-----------------------------------------------------------

namespace Internal.WindowsAzure.ResourceStack.Tests.Frontdoor.Common
{
    using System;
    using Microsoft.WindowsAzure.ResourceStack.Common.Collections;
    using Microsoft.WindowsAzure.ResourceStack.Common.Services;
    using Microsoft.WindowsAzure.ResourceStack.Common.Storage;
    using Microsoft.WindowsAzure.ResourceStack.Frontdoor.Common.Logging;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Authorization.Data.DataProviders;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Authorization.Data.Entities;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Authorization.Data.Entities.ResourceIdentity;
    using Newtonsoft.Json.Linq;

    /// <summary>
    /// This class adds policies to local development storage.
    /// </summary>
    public class PolicyUtility
    {
        /// <summary>
        /// Gets the application tenant ID of CSM service principal.
        /// </summary>
        private static string ApplicationTenantId
        {
            get
            {
                return CloudConfigurationManager.GetConfiguration(
                    settingName: "Microsoft.WindowsAzure.ResourceStack.Frontdoor.AuthorizationDataProvider.ApplicationTenantId");
            }
        }

        /// <summary>
        /// Gets the policy storage connection strings.
        /// </summary>
        private static string PolicyStorageConnectionString
        {
            get
            {
                return StorageUtility.GetConnectionString("encrypted.CloudStorageAccount.ResourceStack.Frontdoor.PolicyDataStorage.{0}.ConnectionString", TestEnvironment.Location);
            }
        }

        /// <summary>
        /// Gets the policy definition data provider for developer storage. 
        /// </summary>
        public static PolicyDefinitionDataProvider GetPolicyDefinitionDataProvider()
        {
            return new PolicyDefinitionDataProvider(
                connectionString: PolicyUtility.PolicyStorageConnectionString,
                eventSource: FrontdoorLog.Current);
        }

        /// <summary>
        /// Gets the policy set definition data provider for developer storage. 
        /// </summary>
        public static PolicySetDefinitionDataProvider GetPolicySetDefinitionDataProvider()
        {
            return new PolicySetDefinitionDataProvider(
                connectionString: PolicyUtility.PolicyStorageConnectionString,
                eventSource: FrontdoorLog.Current);
        }

        /// <summary>
        /// Gets the policy assignment data provider for developer storage. 
        /// </summary>
        public static PolicyAssignmentDataProvider GetPolicyAssignmentDataProvider()
        {
            return new PolicyAssignmentDataProvider(
                connectionString: PolicyUtility.PolicyStorageConnectionString,
                eventSource: FrontdoorLog.Current);
        }

        /// <summary>
        /// Adds a policy definition.
        /// </summary>
        /// <param name="policyDefinitionName">The policy definition name.</param>
        /// <param name="scope">The scope.</param>
        /// <param name="rawPolicy">The raw policy rule.</param>
        /// <param name="tenantId">The tenant id.</param>
        /// <param name="parameters">The parameter definitions.</param>
        /// <param name="mode">The policy mode.</param>
        /// <param name="displayName">The display name.</param>
        public static void AddPolicyDefinition(
            string policyDefinitionName,
            string scope,
            JToken rawPolicy,
            string tenantId,
            InsensitiveDictionary<PolicyParameterDefinition> parameters = null,
            string mode = null,
            string displayName = null)
        {
            var policyDefinitionDataProvider = PolicyUtility.GetPolicyDefinitionDataProvider();

            var policyDefinition = new PolicyDefinitionEntity
            {
                Scope = scope,
                PolicyRule = rawPolicy,
                Name = policyDefinitionName,
                TenantId = tenantId,
                Parameters = parameters,
                Mode = mode,
                DisplayName = displayName
            };

            policyDefinitionDataProvider.Save(policyDefinition).Wait();
        }

        /// <summary>
        /// Adds a built in policy definition.
        /// </summary>
        /// <param name="policyDefinitionName">The policy definition name.</param>
        /// <param name="rawPolicy">The raw policy.</param>
        /// <param name="parameters">The parameter definitions.</param>
        public static PolicyDefinitionEntity AddBuiltInPolicyDefinition(string policyDefinitionName, JToken rawPolicy, InsensitiveDictionary<PolicyParameterDefinition> parameters = null)
        {
            var policyDefinitionDataProvider = PolicyUtility.GetPolicyDefinitionDataProvider();

            var policyDefinition = new PolicyDefinitionEntity
            {
                TenantId = PolicyUtility.ApplicationTenantId,
                Scope = "/",
                Name = policyDefinitionName,
                DisplayName = "BuiltIn Policy",
                Description = "BuiltIn Policy Description",
                PolicyRule = rawPolicy,
                Parameters = parameters,
                PolicyType = PolicyType.BuiltIn
            };

            // Put built-in policy
            policyDefinitionDataProvider.SaveBuiltIn(policyDefinition).Wait();
            return policyDefinition;
        }

        /// <summary>
        /// Delete the built-in policy
        /// </summary>
        /// <param name="policy">the built-in policy will be deleted</param>
        public static void DeleteBuiltInPolicy(PolicyDefinitionEntity policy)
        {
            var policyDefinitionDataProvider = PolicyUtility.GetPolicyDefinitionDataProvider();
            policyDefinitionDataProvider.DeleteBuiltIn(policy).Wait();
        }

        /// <summary>
        /// Adds a built in policy set definition.
        /// </summary>
        /// <param name="policySetDefinitionName">The policy set definition name.</param>
        /// <param name="policyDefinitions">The policy definitions to reference.</param>
        /// <param name="parameters">The parameter definitions.</param>
        public static PolicySetDefinitionEntity AddBuiltInPolicySetDefinition(string policySetDefinitionName, PolicyDefinitionReference[] policyDefinitions, InsensitiveDictionary<PolicyParameterDefinition> parameters = null)
        {
            var policySetDefinitionDataProvider = PolicyUtility.GetPolicySetDefinitionDataProvider();

            var policySetDefinition = new PolicySetDefinitionEntity
            {
                TenantId = PolicyUtility.ApplicationTenantId,
                Scope = "/",
                Name = policySetDefinitionName,
                DisplayName = "BuiltIn Policy Set",
                Description = "BuiltIn Policy Set Description",
                PolicyDefinitions = policyDefinitions,
                Parameters = parameters,
                PolicyType = PolicyType.BuiltIn
            };

            // Put built-in policy set
            policySetDefinitionDataProvider.SaveBuiltIn(policySetDefinition).Wait();
            return policySetDefinition;
        }

        /// <summary>
        /// Delete the built-in policy set
        /// </summary>
        /// <param name="policySet">the built-in policy set will be deleted</param>
        public static void DeleteBuiltInPolicySet(PolicySetDefinitionEntity policySet)
        {
            var policySetDefinitionDataProvider = PolicyUtility.GetPolicySetDefinitionDataProvider();
            policySetDefinitionDataProvider.DeleteBuiltIn(policySet).Wait();
        }

        /// <summary>
        /// Adds a policy set definition.
        /// </summary>
        /// <param name="policySetDefinitionName">The policy set definition name.</param>
        /// <param name="scope">The scope.</param>
        /// <param name="policyDefinitions">The policy definitions to reference.</param>
        /// <param name="tenantId">The tenant id.</param>
        /// <param name="parameters">The parameter definitions.</param>
        /// <param name="displayName">The display name.</param>
        public static void AddPolicySetDefinition(
            string policySetDefinitionName,
            string scope,
            PolicyDefinitionReference[] policyDefinitions,
            string tenantId,
            InsensitiveDictionary<PolicyParameterDefinition> parameters = null,
            string displayName = null)
        {
            var policySetDefinitionDataProvider = PolicyUtility.GetPolicySetDefinitionDataProvider();

            var policySetDefinition = new PolicySetDefinitionEntity
            {
                Scope = scope,
                PolicyDefinitions = policyDefinitions,
                Name = policySetDefinitionName,
                TenantId = tenantId,
                Parameters = parameters,
                DisplayName = displayName
            };

            policySetDefinitionDataProvider.Save(policySetDefinition).Wait();
        }

        /// <summary>
        /// Adds a policy assignment.
        /// </summary>
        /// <param name="policyAssignmentName">The policy assignment name.</param>
        /// <param name="policyDefinitionId">The policy definition id.</param>
        /// <param name="scope">The scope.</param>
        /// <param name="tenantId">The tenant id.</param>
        /// <param name="parameters">The policy parameters.</param>
        /// <param name="notScopes">The policy assignment's notScopes</param>
        /// <param name="sku">The policy assignment's SKU</param>
        /// <param name="displayName">The display name.</param>
        /// <param name="hasResourceIdentity">A value indicating whether the assignment should have a resource identity.</param>
        public static void AddPolicyAssignment(
            string policyAssignmentName,
            string policyDefinitionId,
            string scope,
            string tenantId,
            InsensitiveDictionary<PolicyParameter> parameters = null,
            string[] notScopes = null,
            PolicySku sku = null,
            string displayName = null,
            bool hasResourceIdentity = false)
        {
            var policyAssignmentDataProvider = PolicyUtility.GetPolicyAssignmentDataProvider();

            var policyAssignment = new PolicyAssignmentEntity
            {
                PolicyDefinitionId = policyDefinitionId,
                Name = policyAssignmentName,
                Sku = sku,
                Scope = scope,
                NotScopes = notScopes,
                TenantId = tenantId,
                Parameters = parameters,
                DisplayName = displayName,
                Identity = hasResourceIdentity ? new ResourceIdentity { Type = ResourceIdentityType.SystemAssigned } : null,
                IdentityUrl = hasResourceIdentity ? "https://www.example.com/secret" : null,
                IdentityClientId = hasResourceIdentity ? Guid.NewGuid().ToString() : null
            };

            policyAssignmentDataProvider.Save(policyAssignment).Wait();
        }

        /// <summary>
        /// Removes the policy assignment.
        /// </summary>
        /// <param name="policyAssignmentName">The policy assignment name.</param>
        /// <param name="scope">The scope where the policy is assigned.</param>
        /// <param name="tenantId">The tenant identifier.</param>
        public static void RemovePolicyAssignment(string policyAssignmentName, string scope, string tenantId)
        {
            var policyAssignmentDataProvider = PolicyUtility.GetPolicyAssignmentDataProvider();
            var entity = policyAssignmentDataProvider.FindPolicyAssignment(tenantId, scope, policyAssignmentName).Result;
            policyAssignmentDataProvider.Delete(entity).Wait();
        }
    }
}