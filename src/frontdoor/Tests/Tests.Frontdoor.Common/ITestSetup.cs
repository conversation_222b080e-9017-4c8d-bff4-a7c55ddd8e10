﻿//-----------------------------------------------------------
// Copyright (c) Microsoft Corporation.  All rights reserved.
//-----------------------------------------------------------

namespace Internal.WindowsAzure.ResourceStack.Tests.Frontdoor.Common
{
    using Microsoft.VisualStudio.TestTools.UnitTesting;

    /// <summary>
    /// Interface for test setup initialization.  This allows Azure Stack to consume ARM unit tests with different static initialization.
    /// </summary>
    public interface ITestSetup
    {
        /// <summary>
        /// Initializes the test project.
        /// </summary>
        /// <param name="context">The context.</param>
        void AssemblyInitialize(TestContext context);

        /// <summary>
        /// Uninitializes the test project.
        /// </summary>
        void AssemblyUninitialize();

        /// <summary>
        /// Gets the test configuration file path.
        /// </summary>
        string TestConfigFilePath { get; }
    }
}
