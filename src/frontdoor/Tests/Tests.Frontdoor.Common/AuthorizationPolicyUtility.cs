﻿//-----------------------------------------------------------
// Copyright (c) Microsoft Corporation.  All rights reserved.
//-----------------------------------------------------------

namespace Internal.WindowsAzure.ResourceStack.Tests.Frontdoor.Common
{
    using System;
    using System.Collections.Generic;
    using System.IO;
    using System.Linq;
    using System.Threading.Tasks;
    using Microsoft.IdentityModel.Authorization;
    using Microsoft.IdentityModel.Authorization.Aad;
    using Microsoft.IdentityModel.Authorization.DataModel;
    using Microsoft.IdentityModel.Authorization.DataModel.XStore;
    using Microsoft.WindowsAzure.ResourceStack.Common.Collections;
    using Microsoft.WindowsAzure.ResourceStack.Common.Dsms;
    using Microsoft.WindowsAzure.ResourceStack.Common.Extensions;
    using Microsoft.WindowsAzure.ResourceStack.Common.Services;
    using Microsoft.WindowsAzure.ResourceStack.Common.Storage;

    /// <summary>
    /// This class adds authorization policies to local development storage.
    /// </summary>
    public class AuthorizationPolicyUtility
    {
        /// <summary>
        /// Gets the application ID of CSM service principal.
        /// </summary>
        private static Guid ApplicationId
        {
            get
            {
                return CloudConfigurationManager.GetConfigurationGuid(
                    settingName: "Microsoft.WindowsAzure.ResourceStack.Frontdoor.ApplicationId");
            }
        }

        /// <summary>
        /// Gets the application tenant ID of CSM service principal.
        /// </summary>
        private static Guid ApplicationTenantId
        {
            get
            {
                return CloudConfigurationManager.GetConfigurationGuid(
                    settingName: "Microsoft.WindowsAzure.ResourceStack.Frontdoor.AuthorizationDataProvider.ApplicationTenantId");
            }
        }

        /// <summary>
        /// Gets a value indicating whether DSMS storage account is enabled.
        /// </summary>
        private static bool IsAuthorizationDataStorageDsmsEnabled
        {
            get
            {
                return CloudConfigurationManager.GetConfigurationBoolean(
                    settingName: "Microsoft.WindowsAzure.ResourceStack.Frontdoor.AuthorizationDataStorage.DsmsStorageAccountEnabled",
                    defaultValue: false);
            }
        }

        /// <summary>
        /// Gets the policy store connection strings.
        /// </summary>
        private static string[] PolicyStoreConnectionStrings
        {
            get
            {
                if (CloudConfigurationManager.IsCloudEnvironment)
                {
                    return TestEnvironment.TestStorageAccountConnectionString.AsArray();
                }

                var legacyConnectionStrings = new[]
                {
                    StorageUtility.GetConnectionString("encrypted.CloudStorageAccount.ResourceStack.Frontdoor.AuthorizationDataStorage00.{0}.ConnectionString", TestEnvironment.Location),
                    StorageUtility.GetConnectionString("encrypted.CloudStorageAccount.ResourceStack.Frontdoor.AuthorizationDataStorage01.{0}.ConnectionString", TestEnvironment.Location),
                    StorageUtility.GetConnectionString("encrypted.CloudStorageAccount.ResourceStack.Frontdoor.AuthorizationDataStorage02.{0}.ConnectionString", TestEnvironment.Location),
                    StorageUtility.GetConnectionString("encrypted.CloudStorageAccount.ResourceStack.Frontdoor.AuthorizationDataStorage03.{0}.ConnectionString", TestEnvironment.Location),
                };

                return AuthorizationPolicyUtility.IsAuthorizationDataStorageDsmsEnabled
                    ? new[]
                    {
                        DsmsDataProvider.Instance.GetConnectionString(
                            "Microsoft.WindowsAzure.ResourceStack.Frontdoor.AuthorizationDataStorage00.{0}.DsmsSourceLocation", 
                            TestEnvironment.Location,
                            legacyConnectionStrings[0]),
                        DsmsDataProvider.Instance.GetConnectionString(
                            "Microsoft.WindowsAzure.ResourceStack.Frontdoor.AuthorizationDataStorage01.{0}.DsmsSourceLocation", 
                            TestEnvironment.Location,
                            legacyConnectionStrings[1]),
                        DsmsDataProvider.Instance.GetConnectionString(
                            "Microsoft.WindowsAzure.ResourceStack.Frontdoor.AuthorizationDataStorage02.{0}.DsmsSourceLocation", 
                            TestEnvironment.Location,
                            legacyConnectionStrings[2]),
                        DsmsDataProvider.Instance.GetConnectionString(
                            "Microsoft.WindowsAzure.ResourceStack.Frontdoor.AuthorizationDataStorage03.{0}.DsmsSourceLocation", 
                            TestEnvironment.Location,
                            legacyConnectionStrings[3])
                    } 
                    : legacyConnectionStrings;
            }
        }

        /// <summary>
        /// Populate local development store with policies.
        /// </summary>
        /// <param name="roleId">The role id.</param>
        /// <param name="roleName">The role name.</param>
        /// <param name="scope">The scope.</param>
        /// <param name="tenantId">The tenant id.</param>
        /// <param name="objectIdentifier">The object identifier.</param>
        /// <param name="allowedActions">The allowed actions.</param>
        /// <param name="roleAssignmentId">The role assignment id.</param>
        /// <param name="canDelegate">This role assignment allows delegation or not</param>
        public static void PopulatePolicies(Guid roleId, string roleName, string scope, string tenantId, Guid objectIdentifier, IEnumerable<string> allowedActions, Guid? roleAssignmentId = null, bool canDelegate = false)
        {
            AuthorizationPolicyUtility.AddAllowedActionRoleDefinitionPolicy(
                roleId: roleId,
                roleName: roleName,
                allowedActions: allowedActions);

            AuthorizationPolicyUtility.AddUserRoleAssignmentPolicy(
                userTenantId: Guid.Parse(tenantId),
                roleDefinitionId: roleId,
                scope: scope,
                objectIdentifier: objectIdentifier,
                roleAssignmentId: roleAssignmentId,
                canDelegate: canDelegate);
        }

        /// <summary>
        /// Populate local development store with directory role template policies.
        /// </summary>
        /// <param name="roleId">The role id.</param>
        /// <param name="roleName">The role name.</param>
        /// <param name="scope">The scope.</param>
        /// <param name="tenantId">The tenant id.</param>
        /// <param name="objectIdentifier">The object identifier.</param>
        /// <param name="allowedActions">The allowed actions.</param>
        /// <param name="roleAssignmentId">The role assignment id.</param>
        public static void PopulateDirectoryRoleTemplatePolicies(Guid roleId, string roleName, string scope, string tenantId, Guid objectIdentifier, IEnumerable<string> allowedActions, Guid? roleAssignmentId = null)
        {
            AuthorizationPolicyUtility.AddAllowedActionRoleDefinitionPolicy(
                roleId: roleId,
                roleName: roleName,
                allowedActions: allowedActions);

            AuthorizationPolicyUtility.AddDirectoryRoleTemplateRoleAssignmentPolicy(
                userTenantId: Guid.Parse(tenantId),
                roleDefinitionId: roleId,
                scope: scope,
                objectIdentifier: objectIdentifier,
                roleAssignmentId: roleAssignmentId);
        }

        /// <summary>
        /// Add role definition policy with allowed actions.
        /// </summary>
        /// <param name="roleId">The role id.</param>
        /// <param name="roleName">The role name.</param>
        /// <param name="allowedActions">The allowed actions.</param>
        public static void AddAllowedActionRoleDefinitionPolicy(Guid roleId, string roleName, IEnumerable<string> allowedActions)
        {
            AuthorizationPolicyUtility.AddSystemRoleDefinitionPolicy(roleId: roleId, roleName: roleName, allowedActions: allowedActions, notAllowedActions: null);
        }

        /// <summary>
        /// Add role definition policy with not allowed actions.
        /// </summary>
        /// <param name="roleId">The role id.</param>
        /// <param name="roleName">The role name.</param>
        /// <param name="notAllowedActions">The not allowed actions.</param>
        public static void AddNotAllowedActionRoleDefinitionPolicy(Guid roleId, string roleName, IEnumerable<string> notAllowedActions)
        {
            AuthorizationPolicyUtility.AddSystemRoleDefinitionPolicy(roleId: roleId, roleName: roleName, allowedActions: null, notAllowedActions: notAllowedActions);
        }

        /// <summary>
        /// Add system role definition policy.
        /// </summary>
        /// <param name="roleId">The role id.</param>
        /// <param name="roleName">The role name.</param>
        /// <param name="allowedActions">The allowed actions.</param>
        /// <param name="notAllowedActions">The not allowed actions.</param>
        public static void AddSystemRoleDefinitionPolicy(Guid roleId, string roleName, IEnumerable<string> allowedActions, IEnumerable<string> notAllowedActions)
        {
            AuthorizationPolicyUtility.AddRoleDefinitionPolicy(
                tenantId: AuthorizationPolicyUtility.ApplicationTenantId,
                roleId: roleId,
                roleName: roleName,
                allowedActions: allowedActions,
                notAllowedActions: notAllowedActions,
                assignableScopes: new List<string> { "/" });
        }

        /// <summary>
        /// Add custom role definition policy.
        /// </summary>
        /// <param name="userTenantId">The tenant id.</param>
        /// <param name="roleId">The role id.</param>
        /// <param name="roleName">The role name.</param>
        /// <param name="allowedActions">The allowed actions.</param>
        /// <param name="notAllowedActions">The not allowed actions.</param>
        /// <param name="assignableScopes">The assignable scopes.</param>
        public static void AddCustomRoleDefinitionPolicy(Guid userTenantId, Guid roleId, string roleName, IEnumerable<string> allowedActions, IEnumerable<string> notAllowedActions, IEnumerable<string> assignableScopes)
        {
            AuthorizationPolicyUtility.AddRoleDefinitionPolicy(
                tenantId: userTenantId,
                roleId: roleId,
                roleName: roleName,
                allowedActions: allowedActions,
                notAllowedActions: notAllowedActions,
                assignableScopes: assignableScopes,
                ownerShipType: RoleEntityOwnership.Custom);
        }

        /// <summary>
        /// Adds the role assignment policies.
        /// </summary>
        /// <param name="userTenantId">The user tenant id.</param>
        /// <param name="roleDefinitionId">The role definition id.</param>
        /// <param name="scopes">The scopes.</param>
        /// <param name="objectIdentifier">The object identifiers.</param>
        public static void AddUserRoleAssignmentPolicies(Guid userTenantId, Guid roleDefinitionId, IEnumerable<string> scopes, Guid objectIdentifier)
        {
            Task.WhenAll(scopes.Select(scope => AuthorizationPolicyUtility.AddRoleAssignmentPolicy(userTenantId, roleDefinitionId, Guid.NewGuid(), scope, objectIdentifier, AadPrincipalType.User))).Wait();
        }

        /// <summary>
        /// Adds the user role assignment policy.
        /// </summary>
        /// <param name="userTenantId">The user tenant id.</param>
        /// <param name="roleDefinitionId">The role definition id.</param>
        /// <param name="scope">The scope.</param>
        /// <param name="objectIdentifier">The object identifiers.</param>
        /// <param name="roleAssignmentId">The role assignment id.</param>
        /// <param name="canDelegate">This role assignment allows delegation or not</param>
        public static void AddUserRoleAssignmentPolicy(Guid userTenantId, Guid roleDefinitionId, string scope, Guid objectIdentifier, Guid? roleAssignmentId = null, bool canDelegate = false)
        {
            AuthorizationPolicyUtility
                .AddRoleAssignmentPolicy(
                    userTenantId: userTenantId,
                    roleDefinitionId: roleDefinitionId,
                    roleAssignmentId: roleAssignmentId ?? Guid.NewGuid(),
                    scope: scope,
                    objectIdentifier: objectIdentifier,
                    principalType: AadPrincipalType.User,
                    canDelegate: canDelegate)
                .Wait();
        }

        /// <summary>
        /// Adds the user role assignment policy for custom roles.
        /// </summary>
        /// <param name="userTenantId">The user tenant id.</param>
        /// <param name="roleDefinitionId">The role definition id.</param>
        /// <param name="scope">The scope.</param>
        /// <param name="objectIdentifier">The object identifiers.</param>
        /// <param name="roleAssignmentId">The role assignment id.</param>
        public static void AddUserRoleAssignmentPolicyForCustomRoles(Guid userTenantId, Guid roleDefinitionId, string scope, Guid objectIdentifier, Guid? roleAssignmentId = null)
        {
            AuthorizationPolicyUtility
                .AddRoleAssignmentPolicy(
                    userTenantId: userTenantId,
                    roleDefinitionId: roleDefinitionId,
                    roleAssignmentId: roleAssignmentId ?? Guid.NewGuid(),
                    scope: scope,
                    objectIdentifier: objectIdentifier,
                    principalType: AadPrincipalType.User,
                    assignedToCustomRole: true)
                .Wait();
        }

        /// <summary>
        /// Adds the group role assignment policy.
        /// </summary>
        /// <param name="userTenantId">The user tenant id.</param>
        /// <param name="roleDefinitionId">The role definition id.</param>
        /// <param name="scope">The scope.</param>
        /// <param name="objectIdentifier">The object identifiers.</param>
        /// <param name="roleAssignmentId">The role assignment id.</param>
        public static void AddGroupRoleAssignmentPolicy(Guid userTenantId, Guid roleDefinitionId, string scope, Guid objectIdentifier, Guid? roleAssignmentId = null)
        {
            AuthorizationPolicyUtility
                .AddRoleAssignmentPolicy(
                    userTenantId: userTenantId,
                    roleDefinitionId: roleDefinitionId,
                    roleAssignmentId: roleAssignmentId ?? Guid.NewGuid(),
                    scope: scope,
                    objectIdentifier: objectIdentifier,
                    principalType: AadPrincipalType.Group)
                .Wait();
        }

        /// <summary>
        /// Adds the directory role template  role assignment policy.
        /// </summary>
        /// <param name="userTenantId">The user tenant id.</param>
        /// <param name="roleDefinitionId">The role definition id.</param>
        /// <param name="scope">The scope.</param>
        /// <param name="objectIdentifier">The object identifiers.</param>
        /// <param name="roleAssignmentId">The role assignment id.</param>
        public static void AddDirectoryRoleTemplateRoleAssignmentPolicy(Guid userTenantId, Guid roleDefinitionId, string scope, Guid objectIdentifier, Guid? roleAssignmentId = null)
        {
            AuthorizationPolicyUtility
                .AddRoleAssignmentPolicy(
                    userTenantId: userTenantId,
                    roleDefinitionId: roleDefinitionId,
                    roleAssignmentId: roleAssignmentId ?? Guid.NewGuid(),
                    scope: scope,
                    objectIdentifier: objectIdentifier,
                    principalType: AadPrincipalType.DirectoryRoleTemplate)
                .Wait();
        }

        /// <summary>
        /// Adds the built in role assignment policy.
        /// </summary>
        /// <param name="roleDefinitionId">The role definition id.</param>
        /// <param name="scope">The scope.</param>
        /// <param name="appId">The application identifier.</param>
        /// <param name="roleAssignmentId">The role assignment id.</param>
        public static void AddBuiltInRoleAssignmentPolicy(Guid roleDefinitionId, string scope, Guid appId, Guid? roleAssignmentId = null)
        {
            AuthorizationPolicyUtility
               .AddRoleAssignmentPolicy(
                   userTenantId: AuthorizationPolicyUtility.ApplicationTenantId,
                   roleDefinitionId: roleDefinitionId,
                   roleAssignmentId: roleAssignmentId ?? Guid.NewGuid(),
                   scope: scope,
                   objectIdentifier: appId,
                   principalType: AadPrincipalType.Application,
                   ownerShipType: RoleEntityOwnership.BuiltIn)
               .Wait();
        }

        /// <summary>
        /// Adds the application role assignment policy.
        /// </summary>
        /// <param name="tenantId">The tenant id.</param>
        /// <param name="roleDefinitionId">The role definition id.</param>
        /// <param name="scope">The scope.</param>
        /// <param name="appId">The application identifier.</param>
        /// <param name="roleAssignmentId">The role assignment id.</param>
        public static void AddApplicationRoleAssignmentPolicy(Guid tenantId, Guid roleDefinitionId, string scope, Guid appId, Guid? roleAssignmentId = null)
        {
            AuthorizationPolicyUtility
                .AddRoleAssignmentPolicy(
                    userTenantId: tenantId,
                    roleDefinitionId: roleDefinitionId,
                    roleAssignmentId: roleAssignmentId ?? Guid.NewGuid(),
                    scope: scope,
                    objectIdentifier: appId,
                    principalType: AadPrincipalType.Application)
                .Wait();
        }

        /// <summary>
        /// Deletes the role assignment policy.
        /// </summary>
        /// <param name="userTenantId">The user tenant id.</param>
        /// <param name="roleAssignmentId">The role assignment id.</param>
        public static void DeleteRoleAssignmentPolicy(Guid userTenantId, Guid roleAssignmentId)
        {
            var roleAssignmentDataProvider = ObjectDataProviderFactory.CreateRoleAssignmentDataProvider(connStrings: AuthorizationPolicyUtility.PolicyStoreConnectionStrings);
            roleAssignmentDataProvider.DeleteRoleAssignment(tenantId: userTenantId, appId: AuthorizationPolicyUtility.ApplicationId, roleAssignmentId: roleAssignmentId).Wait();
        }

        /// <summary>
        /// Deletes the custom role definition policy.
        /// </summary>
        /// <param name="tenantId">The tenant id.</param>
        /// <param name="roleDefinitionId">The role definition id.</param>
        public static void DeleteCustomRoleDefinitionPolicy(Guid tenantId, Guid roleDefinitionId)
        {
            var roleDefinitionDataProvider = ObjectDataProviderFactory.CreateRoleDefinitionDataProvider(connStrings: AuthorizationPolicyUtility.PolicyStoreConnectionStrings, dataType: RoleEntityOwnership.Custom);
            roleDefinitionDataProvider.DeleteRoleDefinition(tenantId: tenantId, appId: AuthorizationPolicyUtility.ApplicationId, roleId: roleDefinitionId).Wait();
        }

        /// <summary>
        /// Deletes the system role definition policy.
        /// </summary>
        /// <param name="tenantId">The tenant id.</param>
        /// <param name="roleDefinitionId">The role definition id.</param>
        public static void DeleteSystemRoleDefinitionPolicy(Guid tenantId, Guid roleDefinitionId)
        {
            var roleDefinitionDataProvider = ObjectDataProviderFactory.CreateRoleDefinitionDataProvider(connStrings: AuthorizationPolicyUtility.PolicyStoreConnectionStrings);
            roleDefinitionDataProvider.DeleteRoleDefinition(tenantId: tenantId, appId: AuthorizationPolicyUtility.ApplicationId, roleId: roleDefinitionId).Wait();
        }

        /// <summary>
        /// Deletes the system role assignment policy.
        /// </summary>
        /// <param name="roleAssignmentId">The role assignment id.</param>
        public static void DeleteSystemRoleAssignmentPolicy(Guid roleAssignmentId)
        {
            var roleAssignmentDataProvider = ObjectDataProviderFactory.CreateRoleAssignmentDataProvider(connStrings: AuthorizationPolicyUtility.PolicyStoreConnectionStrings, dataType: RoleEntityOwnership.BuiltIn);
            roleAssignmentDataProvider.DeleteRoleAssignment(tenantId: AuthorizationPolicyUtility.ApplicationTenantId, appId: AuthorizationPolicyUtility.ApplicationId, roleAssignmentId: roleAssignmentId).Wait();
        }

        /// <summary>
        /// Create the deny assignment policy.
        /// </summary>
        /// <param name="userTenantId">The user tenant id.</param>
        /// <param name="assignmentName">The deny assignment name.</param>
        /// <param name="scope">The scope.</param>
        /// <param name="actions">The actions.</param>
        /// <param name="notActions">The not actions.</param>
        public static string CreateDenyAssignmentPolicy(Guid userTenantId, string assignmentName, string scope, IEnumerable<string> actions, IEnumerable<string> notActions)
        {
            var denyPolicyId = Guid.NewGuid().ToString();
            AuthorizationPolicyUtility.AddOrUpdateDenyAssignment(
                tenantId: userTenantId,
                denyAssignmentId: denyPolicyId,
                denyAssignmentName: assignmentName,
                scope: scope,
                actions: actions,
                notActions: notActions,
                principals: EmptyArray<Principal>.Instance,
                excludedPrincipals: EmptyArray<Principal>.Instance);

            return denyPolicyId;
        }

        /// <summary>
        /// Update the deny assignment policy with group principals.
        /// </summary>
        /// <param name="userTenantId">The user tenant id.</param>
        /// <param name="policyId">The policy id.</param>
        /// <param name="userPrincipalIds">The user principal IDs to be denied.</param>
        /// <param name="excludedUserPrincipalIds">The excluded user principal IDs.</param>
        /// <param name="groupPrincipalIds">The group principal IDs to be denied.</param>
        /// <param name="excludedGroupPrincipalIds">The group excluded principal IDs.</param>
        /// <param name="doNotApplyToChildScopes">Whether apply to child scope.</param>
        public static void AddPrincipalsToDenyPolicy(
            Guid userTenantId,
            string policyId,
            IEnumerable<string> userPrincipalIds = null,
            IEnumerable<string> excludedUserPrincipalIds = null,
            IEnumerable<string> groupPrincipalIds = null,
            IEnumerable<string> excludedGroupPrincipalIds = null,
            bool doNotApplyToChildScopes = false)
        {
            var denyAssignmentDataProvider = ObjectDataProviderFactory.CreateDenyAssignmentDataProvider(connStrings: AuthorizationPolicyUtility.PolicyStoreConnectionStrings, dataType: RoleEntityOwnership.Custom);
            var denyPolicy = AuthorizationPolicyUtility.GetDenyPolicy(tenantId: userTenantId, denyPolicyId: policyId);
            if (denyPolicy == null)
            {
                throw new InvalidOperationException($"Deny policy '{policyId}' is missing.");
            }

            var userPrincipals = userPrincipalIds.CoalesceEnumerable().Select(principalId => new Principal { Id = principalId, Type = PrincipalType.User.ToString() });
            var excludedUserPrincipals = excludedUserPrincipalIds.CoalesceEnumerable().Select(principalId => new Principal { Id = principalId, Type = PrincipalType.User.ToString() });
            var groupPrincipals = groupPrincipalIds.CoalesceEnumerable().Select(principalId => new Principal { Id = principalId, Type = PrincipalType.Group.ToString() });
            var excludedGroupPrincipals = excludedGroupPrincipalIds.CoalesceEnumerable().Select(principalId => new Principal { Id = principalId, Type = PrincipalType.Group.ToString() });

            AuthorizationPolicyUtility.AddOrUpdateDenyAssignment(
                tenantId: userTenantId,
                denyAssignmentId: policyId,
                denyAssignmentName: denyPolicy.Name,
                scope: denyPolicy.Scope,
                actions: denyPolicy.Permissions.SelectMany(permission => permission.Actions),
                notActions: denyPolicy.Permissions.SelectMany(permission => permission.NotActions),
                principals: denyPolicy.Principals.Concat(userPrincipals).Concat(groupPrincipals).ToList(),
                excludedPrincipals: denyPolicy.ExcludePrincipals.Concat(excludedUserPrincipals).Concat(excludedGroupPrincipals).ToList(),
                doNotApplyToChildScopes: doNotApplyToChildScopes);
        }

        /// <summary>
        /// Update the deny role assignment with group principals.
        /// </summary>
        /// <param name="userTenantId">The user tenant id.</param>
        /// <param name="assignmentId">The assignment id.</param>
        public static void AddEveryonePrincipalToDenyAssignment(Guid userTenantId, string assignmentId)
        {
            var denyAssignmentDataProvider = ObjectDataProviderFactory.CreateDenyAssignmentDataProvider(connStrings: AuthorizationPolicyUtility.PolicyStoreConnectionStrings, dataType: RoleEntityOwnership.Custom);
            var denyAssignment = AuthorizationPolicyUtility.GetDenyPolicy(tenantId: userTenantId, denyPolicyId: assignmentId);
            if (denyAssignment == null)
            {
                throw new InvalidOperationException($"Deny policy '{assignmentId}' is missing.");
            }

            AuthorizationPolicyUtility.AddOrUpdateDenyAssignment(
                tenantId: userTenantId,
                denyAssignmentId: assignmentId,
                denyAssignmentName: denyAssignment.Name,
                scope: denyAssignment.Scope,
                actions: denyAssignment.Permissions.SelectMany(permission => permission.Actions),
                notActions: denyAssignment.Permissions.SelectMany(permission => permission.NotActions),
                principals: denyAssignment.Principals.Concat(new Principal { Id = Guid.Empty.ToString(), Type = PrincipalType.Everyone.ToString() }).ToList(),
                excludedPrincipals: denyAssignment.ExcludePrincipals);
        }

        #region Private helper methods

        /// <summary>
        /// Adds the role assignment policy.
        /// </summary>
        /// <param name="userTenantId">The user tenant id.</param>
        /// <param name="roleDefinitionId">The role definition id.</param>
        /// <param name="roleAssignmentId">The role assignment id.</param>
        /// <param name="scope">The scope.</param>
        /// <param name="objectIdentifier">The object identifiers.</param>
        /// <param name="principalType">The principal type.</param>
        /// <param name="ownerShipType">The ownership type of role assignment.</param>
        /// <param name="assignedToCustomRole">The assignment is for the custom role.</param>
        /// <param name="canDelegate">This role assignment allows delegation or not</param>
        private static Task AddRoleAssignmentPolicy(Guid userTenantId, Guid roleDefinitionId, Guid roleAssignmentId, string scope, Guid objectIdentifier, string principalType, RoleEntityOwnership ownerShipType = RoleEntityOwnership.Custom, bool assignedToCustomRole = false, bool canDelegate = false)
        {
            var roleAssignmentDataProvider = ObjectDataProviderFactory.CreateRoleAssignmentDataProvider(connStrings: AuthorizationPolicyUtility.PolicyStoreConnectionStrings, dataType: ownerShipType);

            var roleAssignments = new List<StoreRoleAssignment>
            { 
                new StoreRoleAssignment 
                {
                    RoleDefinitionId = roleDefinitionId.ToString(),
                    Id = roleAssignmentId.ToString(),
                    Scope = scope,
                    PrincipalIdWithType = new PrincipalIdentifier(objectIdentifier),
                    PrincipalType = principalType,
                    AssignedToCustomRole = assignedToCustomRole,
                    CanDelegate = canDelegate
                }
            };

            return roleAssignmentDataProvider.AddRoleAssignments(tenantId: userTenantId, appId: AuthorizationPolicyUtility.ApplicationId, assignments: roleAssignments);
        }

        /// <summary>
        /// Add role definition policy.
        /// </summary>
        /// <param name="tenantId">The tenant id.</param>
        /// <param name="roleId">The role id.</param>
        /// <param name="roleName">The role name.</param>
        /// <param name="allowedActions">The allowed actions.</param>
        /// <param name="notAllowedActions">The not allowed actions.</param>
        /// <param name="assignableScopes">The assignable scopes.</param>
        /// <param name="ownerShipType">The ownership type of role definition.</param>
        private static void AddRoleDefinitionPolicy(Guid tenantId, Guid roleId, string roleName, IEnumerable<string> allowedActions, IEnumerable<string> notAllowedActions, IEnumerable<string> assignableScopes, RoleEntityOwnership ownerShipType = RoleEntityOwnership.BuiltIn)
        {
            var roleDefinitionDataProvider = ObjectDataProviderFactory.CreateRoleDefinitionDataProvider(connStrings: AuthorizationPolicyUtility.PolicyStoreConnectionStrings, dataType: ownerShipType);

            var roleDefinitions = new List<StoreRoleDefinition>
            { 
                new StoreRoleDefinition
                {
                    Name = roleName,
                    Scopes = assignableScopes.ToList(),
                    Description = "Role created for CIT",
                    Id = roleId.ToString(),
                    Permissions = new List<Permission> { new Permission(actions: allowedActions.CoalesceEnumerable(), notActions: notAllowedActions.CoalesceEnumerable()) }
                }
            };

            roleDefinitionDataProvider.AddRoleDefinitions(tenantId: tenantId, appId: AuthorizationPolicyUtility.ApplicationId, roleDefinitions: roleDefinitions).Wait();

            TestEnvironment.RestartCaches();
        }

        /// <summary>
        /// Add deny assignment policy.
        /// </summary>
        /// <param name="tenantId">The tenant id.</param>
        /// <param name="denyAssignmentId">The deny assignment id.</param>
        /// <param name="denyAssignmentName">The deny assignment name.</param>
        /// <param name="scope">The scope at which deny policy applies.</param>
        /// <param name="actions">The actions.</param>
        /// <param name="notActions">The not actions.</param>
        /// <param name="principals">The principals.</param>
        /// <param name="excludedPrincipals">The excluded principals.</param>
        /// <param name="doNotApplyToChildScopes">Whether apply to child scope.</param>
        private static void AddOrUpdateDenyAssignment(
            Guid tenantId,
            string denyAssignmentId,
            string denyAssignmentName,
            string scope,
            IEnumerable<string> actions,
            IEnumerable<string> notActions,
            IEnumerable<Principal> principals,
            IEnumerable<Principal> excludedPrincipals,
            bool doNotApplyToChildScopes = false)
        {
            var denyAssignmentDataProvider = ObjectDataProviderFactory.CreateDenyAssignmentDataProvider(connStrings: AuthorizationPolicyUtility.PolicyStoreConnectionStrings, dataType: RoleEntityOwnership.Custom);

            var denyAssignment = new StoreDenyAssignment
            {
                Name = denyAssignmentName,
                Id = denyAssignmentId,
                Scope = scope,
                Description = "Role created for CIT",
                Principals = principals.Select(principal => new Principal { Id = Guid.Parse(principal.Id).ToString("N"), Type = principal.Type }).ToList(),
                ExcludePrincipals = excludedPrincipals.Select(principal => new Principal { Id = Guid.Parse(principal.Id).ToString("N"), Type = principal.Type }).ToList(),
                Permissions = new List<Permission> { new Permission(actions: actions.CoalesceEnumerable(), notActions: notActions.CoalesceEnumerable()) },
                DoNotApplyToChildScopes = doNotApplyToChildScopes,
            };

            denyAssignmentDataProvider.AddOrUpdateDenyAssignment(tenantId: tenantId, appId: AuthorizationPolicyUtility.ApplicationId, storeDenyAssignment: denyAssignment).Wait();
            TestEnvironment.RestartCaches();
        }

        /// <summary>
        /// Get the existing deny policy.
        /// </summary>
        /// <param name="tenantId">The tenant id.</param>
        /// <param name="denyPolicyId">The deny policy id.</param>
        private static StoreDenyAssignment GetDenyPolicy(Guid tenantId, string denyPolicyId)
        {
            var denyAssignmentDataProvider = ObjectDataProviderFactory.CreateDenyAssignmentDataProvider(connStrings: AuthorizationPolicyUtility.PolicyStoreConnectionStrings, dataType: RoleEntityOwnership.Custom);
            return denyAssignmentDataProvider
                .GetDenyAssignmentById(
                    tenantId: tenantId,
                    appId: AuthorizationPolicyUtility.ApplicationId,
                    denyAssignmentId: Guid.Parse(denyPolicyId))
                .Result;
        }
        
        /// <summary>
        /// The available principal types.
        /// </summary>
        private enum PrincipalType
        {
            /// <summary>
            /// The principal type id for user.
            /// </summary>
            User,

            /// <summary>
            /// The principal type is the service principal.
            /// </summary>
            ServicePrincipal,

            /// <summary>
            /// The principal type is the group.
            /// </summary>
            Group,

            /// <summary>
            /// The principal type is everyone.
            /// </summary>
            /// <remarks>
            /// When this is specified principal id must be empty GUID.
            /// This is meant for scenarios where everyone is blocked to the scope except excluded principals.
            /// </remarks>
            Everyone
        }

        #endregion
    }
}