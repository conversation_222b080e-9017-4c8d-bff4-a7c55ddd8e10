﻿//-----------------------------------------------------------
// Copyright (c) Microsoft Corporation.  All rights reserved.
//-----------------------------------------------------------

namespace Internal.WindowsAzure.ResourceStack.Tests.Frontdoor.Common
{
    using System;
    using System.IdentityModel.Tokens;
    using System.Linq;
    using Microsoft.WindowsAzure.ResourceStack.Common.Extensions;

    /// <summary>
    /// This class cracks JWT token and gives different values.
    /// </summary>
    public class AccessTokenParser
    {
        #region Properties
        
        /// <summary>
        /// Gets or sets the claims principal.
        /// </summary>
        private ILookup<string, string> ClaimsPrincipal { get; set; }
        
        #endregion

        /// <summary>
        /// Initializes a new instance of the <see cref="AccessTokenParser"/> class.
        /// </summary>
        /// <param name="accessToken">The access token.</param>
        public AccessTokenParser(string accessToken)
        {
            this.ClaimsPrincipal = this.GetClaims(accessToken);
        }

        #region Public methods

        /// <summary>
        /// Gets the tenant id from JWT token.
        /// </summary>
        public string GetTenantId()
        {
            return this.ClaimsPrincipal["tid"].Single();
        }

        /// <summary>
        /// Gets the  object id form JWT token.
        /// </summary>
        public string GetObjectIdentifier()
        {
            return this.ClaimsPrincipal["oid"].Single();
        }

        #endregion

        #region Private helper methods

        /// <summary>
        /// Converts access token body into lookup of claims.
        /// </summary>
        /// <param name="accessToken">The access token.</param>
        private ILookup<string, string> GetClaims(string accessToken)
        {
            var securityToken = new JwtSecurityTokenHandler().ReadToken(accessToken) as JwtSecurityToken;
            return securityToken.Claims.ToLookupInsensitively(claim => claim.Type, claim => claim.Value);
        }

        #endregion
    }
}