﻿//-----------------------------------------------------------
// Copyright (c) Microsoft Corporation.  All rights reserved.
//-----------------------------------------------------------

namespace Internal.WindowsAzure.ResourceStack.Tests.Frontdoor.Common
{
    using System;
    using System.Linq;
    using Microsoft.WindowsAzure.ResourceStack.Common.Extensions;
    using Microsoft.WindowsAzure.ResourceStack.Frontdoor.Common.Constants;
    using Microsoft.WindowsAzure.ResourceStack.Frontdoor.Data.Configuration;
    using Microsoft.WindowsAzure.ResourceStack.Frontdoor.Data.Entities.Registration;

    /// <summary>
    /// Provides common configuration methods for test resource provider.
    /// </summary>
    public static class ResourceProviderTestHostRegistration
    {
        /// <summary>
        /// The value of the test namespace.
        /// </summary>
        public const string TestNamespace = "ResourceProviderTestHost";

        /// <summary>
        /// The value of the second test namespace. Used to test scenarios spanning namespaces.
        /// </summary>
        public const string TestNamespaceTwo = "ResourceProviderTestHostTwo";

        /// <summary>
        /// The type of the top-level test resource.
        /// </summary>
        public const string TestResourceType = "TestResourceType";

        /// <summary>
        /// The type of the top-level test resource sibling in the same namespace.
        /// </summary>
        public const string TestResourceTypeSibling = "TestResourceTypeSibling";

        /// <summary>
        /// The type of the second top-level test resource.
        /// </summary>
        public const string TestResourceTypeTwo = "TestResourceTypeTwo";
        
        /// <summary>
        /// The type of the top-level test proxy resource.
        /// </summary>
        public const string TestProxyResourceType = "TestProxyResourceType";

        /// <summary>
        /// The type of the second-level test proxy resource segment.
        /// </summary>
        public const string TestNestedProxyResourceTypeSegment = "TestNestedProxyResourceType";

        /// <summary>
        /// The type of the second-level test proxy resource.
        /// </summary>
        public const string TestNestedProxyResourceType = "TestProxyResourceType/" + TestNestedProxyResourceTypeSegment;

        /// <summary>
        /// The type of the top-level test extension resource.
        /// </summary>
        public const string TestExtensionResourceType = "TestExtensionResourceType";

        /// <summary>
        /// The type of the cascade test extension resource.
        /// </summary>
        public const string TestCascadeExtensionResourceType = "TestCascadeExtensionResourceType";

        /// <summary>
        /// The type of the second-level test resource.
        /// </summary>
        public const string TestResourceTypeNestedOne = "TestResourceType/TestResourceTypeNestedOne";

        /// <summary>
        /// The type of the second-level test resource sibling.
        /// </summary>
        public const string TestResourceTypeNestedOneSibling = "TestResourceType/TestResourceTypeNestedOneSibling";

        /// <summary>
        /// The type of the third level test resource.
        /// </summary>
        public const string TestResourceTypeNestedTwo = "TestResourceType/TestResourceTypeNestedOne/TestResourceTypeNestedTwo";

        /// <summary>
        /// The type of the fourth level test resource.
        /// </summary>
        public const string TestResourceTypeNestedThree = "TestResourceType/TestResourceTypeNestedOne/TestResourceTypeNestedTwo/TestResourceTypeNestedThree";

        /// <summary>
        /// The name of the location/region for unit tests.
        /// </summary>
        public const string TestLocation = "DevFabric";

        /// <summary>
        /// The name of a second location/region for unit tests. Used to test scenarios spanning endpoint.
        /// </summary>
        public const string TestLocationTwo = "DevFabricTwo";

        /// <summary>
        /// The name of a third location/region for unit tests. Used to test scenarios spanning endpoint.
        /// </summary>
        public const string TestLocationThree = "DevFabricThree";

        /// <summary>
        /// The name for the test schema owner.
        /// </summary>
        public const string TestSchemaOwner = "TestSchemaOwner";

        /// <summary>
        /// The name for the test manifest owner.
        /// </summary>
        public const string TestManifestOwner = "TestManifestOwner";

        /// <summary>
        /// Gets the test resource types.
        /// </summary>
        public static string[] GetTestResourceTypes()
        {
            return new string[]
            {
                ResourceProviderTestHostRegistration.TestResourceType,
                ResourceProviderTestHostRegistration.TestResourceTypeSibling,
                ResourceProviderTestHostRegistration.TestResourceTypeNestedOne,
                ResourceProviderTestHostRegistration.TestResourceTypeNestedOneSibling,
                ResourceProviderTestHostRegistration.TestResourceTypeNestedTwo,
                ResourceProviderTestHostRegistration.TestResourceTypeNestedThree
            };
        }

        /// <summary>
        /// Register the standard resource types for unit tests.
        /// </summary>
        /// <param name="configuration">Front door configuration.</param>
        /// <param name="resourceProviderServiceUri">Endpoint of resource provider to register.</param>
        public static void RegisterTestResourceTypes(FrontdoorConfiguration configuration, Uri resourceProviderServiceUri)
        {
            var manifest = new ResourceProviderManifest
            {
                Namespace = ResourceProviderTestHostRegistration.TestNamespace,
                ProviderVersion = DataValidation.CurrentResourceProviderVersion,
                ProviderType = ResourceProviderType.Internal,
                ResourceHydrationAccounts = new[]
                {
                    TestEnvironment.GetTestResourceHydrationAccount()
                },
                ResourceTypes = new[]
                {
                    new ResourceType
                    {
                        Name = ResourceProviderTestHostRegistration.TestResourceType,
                        Endpoints = new[]
                        {
                            new ResourceProviderEndpoint
                            {
                                EndpointUri = resourceProviderServiceUri.ToString(),
                                Locations = new[] { ResourceProviderTestHostRegistration.TestLocation },
                                Timeout = TimeSpan.FromSeconds(20),
                                ApiVersion = string.Empty,
                                Enabled = true
                            },
                            new ResourceProviderEndpoint
                            {
                                EndpointUri = resourceProviderServiceUri.ToString(),
                                Locations = new[] { ResourceProviderTestHostRegistration.TestLocation },
                                Timeout = TimeSpan.FromSeconds(20),
                                ApiVersion = TestEnvironment.DefaultApiVersion,
                                Enabled = true
                            }
                        }
                    },
                    new ResourceType
                    {
                        Name = ResourceProviderTestHostRegistration.TestResourceTypeSibling,
                        Endpoints = new[]
                        {
                            new ResourceProviderEndpoint
                            {
                                EndpointUri = resourceProviderServiceUri.ToString(),
                                Locations = new[] { ResourceProviderTestHostRegistration.TestLocation },
                                Timeout = TimeSpan.FromSeconds(20),
                                ApiVersion = string.Empty,
                                Enabled = true
                            }
                        }
                    },
                    new ResourceType
                    {
                        Name = ResourceProviderTestHostRegistration.TestResourceTypeNestedOne,
                        Endpoints = new[]
                        {
                            new ResourceProviderEndpoint
                            {
                                EndpointUri = resourceProviderServiceUri.ToString(),
                                Locations = new[] { ResourceProviderTestHostRegistration.TestLocation },
                                Timeout = TimeSpan.FromSeconds(20),
                                ApiVersion = string.Empty,
                                Enabled = true
                            }
                        }
                    },
                    new ResourceType
                    {
                        Name = ResourceProviderTestHostRegistration.TestResourceTypeNestedOneSibling,
                        Endpoints = new[]
                        {
                            new ResourceProviderEndpoint
                            {
                                EndpointUri = resourceProviderServiceUri.ToString(),
                                Locations = new[] { ResourceProviderTestHostRegistration.TestLocation },
                                Timeout = TimeSpan.FromSeconds(20),
                                ApiVersion = string.Empty,
                                Enabled = true
                            }
                        }
                    },
                    new ResourceType
                    {
                        Name = ResourceProviderTestHostRegistration.TestResourceTypeNestedTwo,
                        Endpoints = new[]
                        {
                            new ResourceProviderEndpoint
                            {
                                EndpointUri = resourceProviderServiceUri.ToString(),
                                Locations = new[] { ResourceProviderTestHostRegistration.TestLocation },
                                Timeout = TimeSpan.FromSeconds(20),
                                ApiVersion = string.Empty,
                                Enabled = true
                            }
                        }
                    },
                    new ResourceType
                    {
                        Name = ResourceProviderTestHostRegistration.TestResourceTypeNestedThree,
                        Endpoints = new[]
                        {
                            new ResourceProviderEndpoint
                            {
                                EndpointUri = resourceProviderServiceUri.ToString(),
                                Locations = new[] { ResourceProviderTestHostRegistration.TestLocation },
                                Timeout = TimeSpan.FromSeconds(20),
                                ApiVersion = string.Empty,
                                Enabled = true
                            }
                        }
                    }
                },
                Management = new ResourceProviderManagement
                {
                    SchemaOwners = new[] { ResourceProviderTestHostRegistration.TestSchemaOwner },
                    ManifestOwners = new[] { ResourceProviderTestHostRegistration.TestManifestOwner }
                }
            };

            var registrations = FrontdoorConfigurationLoader.AvailableLocations
                .Select(location => new ResourceProviderManifestRegistration
                {
                    Manifest = manifest,
                    IsEnabled = true,
                    LastModifiedBy = "Test",
                    Location = location,
                });

            configuration.DataProviders.RegistrationDataProvider.Save(registrations).Wait();
        }

        /// <summary>
        /// Registers the Microsoft.Resources types.
        /// </summary>
        /// <param name="configuration">The configuration.</param>
        public static void RegisterMicrosoftResourcesTypes(FrontdoorConfiguration configuration)
        {
            var manifest = new ResourceProviderManifest
            {
                Namespace = FrontdoorConstants.MicrosoftResourcesNamespace,
                ProviderVersion = DataValidation.CurrentResourceProviderVersion,
                ProviderType = ResourceProviderType.Internal | ResourceProviderType.RegistrationFree,
                ResourceTypes = new[]
                {
                    new ResourceType
                    {
                        Name = "resourceGroups",
                        AllowedUnauthorizedActions = new[]
                        {
                            "Microsoft.Resources/subscriptions/read",
                            "Microsoft.Resources/tenants/read",
                            "Microsoft.Resources/resources/read",
                            "Microsoft.Resources/providers/read",
                            "Microsoft.Resources/checkresourcename/action",
                            "Microsoft.Resources/batch/read",
                            "Microsoft.Resources/locations/read",
                        },
                        LoggingRules = new[]
                        {
                            new LoggingRule
                            {
                                Action = "Microsoft.Resources/subscriptions/resourceGroups/moveResources/action",
                                Direction = LoggingDirections.Request,
                                DetailLevel = LoggingDetails.Body
                            },
                        },
                        Endpoints = new[]
                        {
                            new ResourceProviderEndpoint
                            {
                                EndpointUri = TestEnvironment.ResourceProviderServiceUri.ToString(),
                                ApiVersions = new[]
                                {
                                    TestEnvironment.DefaultApiVersion,
                                    FrontdoorConstants.ApiVersion20160901
                                },
                                Enabled = true,
                                Locations = new[]
                                {
                                    ResourceProviderTestHostRegistration.TestLocation,
                                    ResourceProviderTestHostRegistration.TestLocationTwo,
                                    ResourceProviderTestHostRegistration.TestLocationThree,
                                    "centralus",
                                    "eastasia",
                                    "southeastasia",
                                    "eastus",
                                    "eastus2",
                                    "eastus2euap",
                                    "westus",
                                    "westus2",
                                    "westus3",
                                    "northcentralus",
                                    "southcentralus",
                                    "westcentralus",
                                    "northeurope",
                                    "westeurope",
                                    "japaneast",
                                    "japanwest",
                                    "brazilsouth",
                                    "brazilus",
                                    "australiasoutheast",
                                    "australiaeast",
                                    "westinida",
                                    "southindia",
                                    "centralindia",
                                    "canadacentral",
                                    "canadaeast",
                                    "uknorth",
                                    "uksouth2",
                                    "uksouth",
                                    "ukwest",
                                    "koreacentral",
                                    "koreasouth",
                                    "francecentral",
                                    "francesouth",
                                    "australiacentral",
                                    "australiacentral2",
                                    "uaecentral",
                                    "uaenorth",
                                    "southafricanorth",
                                    "southafricawest",
                                    "mexicocentral"
                                },
                            }
                        },
                        AuthorizationActionMappings = new AuthorizationActionMapping[] 
                        {
                            new AuthorizationActionMapping
                            {
                                 Original = "Microsoft.Resources/batch/action",
                                 Desired = "Microsoft.Resources/batch/read"
                            }
                        },
                    },
                    new ResourceType
                    {
                        Name = "deployments",
                        RoutingType = RoutingType.ProxyOnly,
                        Endpoints = new[]
                        {
                            new ResourceProviderEndpoint
                            {
                                Enabled = true,
                                ApiVersions = new[]
                                {
                                    string.Empty,
                                    "2017-08-01",
                                    "2017-06-01",
                                    "2017-05-10",
                                    "2017-05-01",
                                    "2017-03-01",
                                    "2016-09-01",
                                    "2016-07-01",
                                    "2016-06-01",
                                    "2016-02-01",
                                    "2015-11-01",
                                    "2015-01-01",
                                    "2014-04-01-preview"
                                },
                                Locations = string.Empty.AsArray(),
                                EndpointUri = TestEnvironment.ResourceProviderServiceUri.ToString(),
                            }
                        },
                    }
                },
                Management = new ResourceProviderManagement
                {
                    SchemaOwners = new[] { ResourceProviderTestHostRegistration.TestSchemaOwner },
                    ManifestOwners = new[] { ResourceProviderTestHostRegistration.TestManifestOwner }
                }
            };

            var registrations = FrontdoorConfigurationLoader.AvailableLocations
                .Select(location => new ResourceProviderManifestRegistration
                {
                    Manifest = manifest,
                    IsEnabled = true,
                    LastModifiedBy = "Test",
                    Location = location,
                });

            configuration.DataProviders.RegistrationDataProvider.Save(registrations).Wait();
        }
    }
}
