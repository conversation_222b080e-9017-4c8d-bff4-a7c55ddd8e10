﻿//-----------------------------------------------------------
// Copyright (c) Microsoft Corporation.  All rights reserved.
//-----------------------------------------------------------

namespace Internal.WindowsAzure.ResourceStack.Tests.Frontdoor.Admin
{
    using System;
    using System.ServiceModel;
    using System.ServiceModel.Description;
    using System.Threading.Tasks;
    using System.Web.Http;
    using Microsoft.WindowsAzure.ResourceStack.Frontdoor.Admin.Services;
    using Microsoft.WindowsAzure.ResourceStack.Frontdoor.Data.Configuration;
    using Microsoft.WindowsAzure.ResourceStack.Frontdoor.Data.Contracts;

    /// <summary>
    /// The provisioning service test host.
    /// </summary>
    public class ProvisioningServiceTestHost : IDisposable, IAzureProvisioningAgent
    {
        /// <summary>
        /// Test service address.
        /// </summary>
        private static readonly Uri TestServiceAddress = new UriBuilder(Uri.UriSchemeHttp, Environment.MachineName, 978, "TestService").Uri;

        /// <summary>
        /// Gets or sets the service host.
        /// </summary>
        private ServiceHost ServiceHost { get; set; }

        /// <summary>
        /// Initializes a new instance of the <see cref="ProvisioningServiceTestHost" /> class.
        /// </summary>
        public ProvisioningServiceTestHost()
        {
            this.ServiceHost = new ServiceHost(this.GetProvisioningService());
            this.ServiceHost.Description.Behaviors.Find<ServiceDebugBehavior>().IncludeExceptionDetailInFaults = true;

            ServiceEndpoint testEndpoint = this.ServiceHost.AddServiceEndpoint(typeof(IAzureProvisioningAgent), new BasicHttpBinding(), TestServiceAddress);
            this.ServiceHost.Open();
        }

        /// <summary>
        /// Closes the service host.
        /// </summary>
        public void Dispose()
        {
            this.ServiceHost.Close();
        }

        /// <summary>
        /// Provisions the subscription.
        /// </summary>
        /// <param name="provisioningInfo">The provisioning info.</param>
        public async Task ProvisionSubscription(AzureProvisioningInfo provisioningInfo)
        {
            IAzureProvisioningAgent serviceChannel = ChannelFactory<IAzureProvisioningAgent>.CreateChannel(new BasicHttpBinding(), new EndpointAddress(TestServiceAddress));
            using (serviceChannel as IDisposable)
            {
                await serviceChannel.ProvisionSubscription(provisioningInfo);
            }
        }

        /// <summary>
        /// Enables the subscription.
        /// </summary>
        /// <param name="provisioningInfo">The provisioning info.</param>
        public async Task EnableSubscription(AzureProvisioningInfo provisioningInfo)
        {
            IAzureProvisioningAgent serviceChannel = ChannelFactory<IAzureProvisioningAgent>.CreateChannel(new BasicHttpBinding(), new EndpointAddress(TestServiceAddress));
            using (serviceChannel as IDisposable)
            {
                await serviceChannel.EnableSubscription(provisioningInfo);
            }
        }

        /// <summary>
        /// Disables the subscription.
        /// </summary>
        /// <param name="provisioningInfo">The provisioning info.</param>
        public async Task DisableSubscription(AzureProvisioningInfo provisioningInfo)
        {
            IAzureProvisioningAgent serviceChannel = ChannelFactory<IAzureProvisioningAgent>.CreateChannel(new BasicHttpBinding(), new EndpointAddress(TestServiceAddress));
            using (serviceChannel as IDisposable)
            {
                await serviceChannel.DisableSubscription(provisioningInfo);
            }
        }

        /// <summary>
        /// Warns the subscription.
        /// </summary>
        /// <param name="provisioningInfo">The provisioning info.</param>
        public async Task WarnSubscription(AzureProvisioningInfo provisioningInfo)
        {
            IAzureProvisioningAgent serviceChannel = ChannelFactory<IAzureProvisioningAgent>.CreateChannel(new BasicHttpBinding(), new EndpointAddress(TestServiceAddress));
            using (serviceChannel as IDisposable)
            {
                await serviceChannel.WarnSubscription(provisioningInfo);
            }
        }

        /// <summary>
        /// The subscription is past due.
        /// </summary>
        /// <param name="provisioningInfo">The provisioning info.</param>
        public async Task PastDueSubscription(AzureProvisioningInfo provisioningInfo)
        {
            IAzureProvisioningAgent serviceChannel = ChannelFactory<IAzureProvisioningAgent>.CreateChannel(new BasicHttpBinding(), new EndpointAddress(TestServiceAddress));
            using (serviceChannel as IDisposable)
            {
                await serviceChannel.PastDueSubscription(provisioningInfo);
            }
        }

        /// <summary>
        /// De-provisions the subscription.
        /// </summary>
        /// <param name="provisioningInfo">The provisioning info.</param>
        public async Task DeprovisionSubscription(AzureProvisioningInfo provisioningInfo)
        {
            IAzureProvisioningAgent serviceChannel = ChannelFactory<IAzureProvisioningAgent>.CreateChannel(new BasicHttpBinding(), new EndpointAddress(TestServiceAddress));
            using (serviceChannel as IDisposable)
            {
                await serviceChannel.DeprovisionSubscription(provisioningInfo);
            }
        }

        /// <summary>
        /// Updates the subscription user information.
        /// </summary>
        /// <param name="subscriptionUserInfo">The subscription info.</param>
        public async Task UpdateSubscriptionUserInfo(AzureProvisioningSubscriptionUserInfo subscriptionUserInfo)
        {
            IAzureProvisioningAgent serviceChannel = ChannelFactory<IAzureProvisioningAgent>.CreateChannel(new BasicHttpBinding(), new EndpointAddress(TestServiceAddress));
            using (serviceChannel as IDisposable)
            {
                await serviceChannel.UpdateSubscriptionUserInfo(subscriptionUserInfo);
            }
        }

        /// <summary>
        /// Health check method.
        /// </summary>
        public Task PingEndpoint()
        {
            return Task.FromResult(0);
        }

        /// <summary>
        /// Gets a new instance of the ProvisioningService class.
        /// </summary>
        private ProvisioningService GetProvisioningService()
        {
            FrontdoorConfiguration frontdoorConfiguration = FrontdoorConfigurationLoader.PrimaryFrontdoorConfiguration;
            frontdoorConfiguration.Initialize().Wait();

            ProvisioningService provisioningService = new ProvisioningService(
                frontdoorConfiguration: frontdoorConfiguration,
                httpConfiguration: new HttpConfiguration());

            return provisioningService;
        }
    }
}
