﻿//-----------------------------------------------------------
// Copyright (c) Microsoft Corporation.  All rights reserved.
//-----------------------------------------------------------

namespace Internal.WindowsAzure.ResourceStack.Tests.Frontdoor.Common
{
    using System;
    using System.Collections.Generic;
    using System.Linq;
    using System.Net;
    using System.Net.Http;
    using System.Web.Http;
    using System.Web.Http.Controllers;
    using System.Web.Http.Dependencies;
    using System.Web.Http.Dispatcher;
    using Internal.WindowsAzure.ResourceStack.Tests.Common;
    using Microsoft.WindowsAzure.ResourceStack.Common.Extensions;

    /// <summary>
    /// Delegate used for getting secret.
    /// </summary>
    /// <param name="request">The incoming request.</param>
    /// <param name="secretName">The secret name.</param>
    /// <param name="secretVersion">The secret version.</param>
    public delegate HttpResponseMessage GetSecret(HttpRequestMessage request, string secretName, string secretVersion);

    /// <summary>
    /// The web service host for the KeyVault.
    /// </summary>
    public class KeyVaultTestHost : ServiceTestHost<KeyVaultTestHost.KeyVaultTestHostServer>, IDisposable
    {
        /// <summary>
        /// Initializes a new instance of the <see cref="KeyVaultTestHost"/> class.
        /// </summary>
        /// <param name="serviceUri">The service URI.</param>
        public KeyVaultTestHost(Uri serviceUri)
            : base(serviceUri)
        {
            this.Start();
        }

        /// <summary>
        /// Performs application-defined tasks associated with freeing, releasing, or resetting unmanaged resources.
        /// </summary>
        public void Dispose()
        {
            this.Stop();
        }

        /// <summary>
        /// Restarts this instance.
        /// </summary>
        public void Restart()
        {
            this.Stop();
            this.Start();
        }

        /// <summary>
        /// Gets the state of the test host.
        /// </summary>
        protected override object GetState
        {
            get { return this; }
        }

        /// <summary>
        /// Gets or sets the delegate called when getting secret request is received.
        /// </summary>
        public GetSecret OnGetSecret { get; set; }

        /// <summary>
        /// Called to configure http configuration.
        /// </summary>
        /// <param name="configuration">The http configuration.</param>
        private void OnConfigure(HttpConfiguration configuration)
        {
            configuration.Routes.MapHttpRoute(
                name: "Secret",
                routeTemplate: "secrets/{secretName}/{secretVersion}",
                defaults: new { controller = "Secret", secretVersion = RouteParameter.Optional });

            configuration.Services.Replace(typeof(IHttpControllerSelector), new KeyVaultTestHostControllerSelector(this, configuration));
            configuration.DependencyResolver = new KeyVaultTestHostDependencyResolver(this, configuration);
        }

        /// <summary>
        /// The KeyVault test host server.
        /// </summary>
        public class KeyVaultTestHostServer : ServiceTestHostServer
        {
            /// <summary>
            /// Initializes a new instance of the <see cref="KeyVaultTestHostServer"/> class.
            /// </summary>
            /// <param name="serviceUri">The service URI.</param>
            /// <param name="state">The instance state.</param>
            public KeyVaultTestHostServer(Uri serviceUri, object state)
                : base(serviceUri, state)
            {
            }

            /// <summary>
            /// Called to configure http configuration.
            /// </summary>
            /// <param name="configuration">The http configuration.</param>
            /// <param name="state">The instance state.</param>
            protected override void OnConfigure(HttpConfiguration configuration, object state)
            {
                (state as KeyVaultTestHost).OnConfigure(configuration);
            }
        }

        /// <summary>
        /// The secret request controller.
        /// </summary>
        public class SecretController : ApiController
        {
            /// <summary>
            /// The secret <c>api</c> test host.
            /// </summary>
            private readonly KeyVaultTestHost host;

            /// <summary>
            /// Initializes a new instance of the <see cref="SecretController"/> class.
            /// </summary>
            /// <param name="host">The secret test host.</param>
            public SecretController(KeyVaultTestHost host)
            {
                this.host = host;
            }

            /// <summary>
            /// Responds to get secret request.
            /// </summary>
            /// <param name="secretName">The secret name.</param>
            /// <param name="secretVersion">The secret version.</param>
            [HttpGet]
            public HttpResponseMessage GetSecret(string secretName, string secretVersion = null)
            {
                if (this.host.OnGetSecret != null)
                {
                    return this.host.OnGetSecret(this.Request, secretName, secretVersion);
                }

                return this.Request.CreateResponse(HttpStatusCode.OK);
            }
        }

        /// <summary>
        /// The KeyVault test host controller selector.
        /// </summary>
        private class KeyVaultTestHostControllerSelector : DefaultHttpControllerSelector
        {
            /// <summary>
            /// The KeyVault test host.
            /// </summary>
            private readonly KeyVaultTestHost host;

            /// <summary>
            /// The HTTP configuration.
            /// </summary>
            private readonly HttpConfiguration configuration;

            /// <summary>
            /// Initializes a new instance of the <see cref="KeyVaultTestHostControllerSelector" /> class.
            /// </summary>
            /// <param name="host">The host.</param>
            /// <param name="configuration">The configuration.</param>
            public KeyVaultTestHostControllerSelector(KeyVaultTestHost host, HttpConfiguration configuration)
                : base(configuration)
            {
                this.host = host;
                this.configuration = configuration;
            }

            /// <summary>
            /// Selects the controller.
            /// </summary>
            /// <param name="request">The request.</param>
            public override HttpControllerDescriptor SelectController(HttpRequestMessage request)
            {
                var matchedTypes = typeof(KeyVaultTestHost)
                    .GetNestedTypes()
                    .Where(type => typeof(IHttpController).IsAssignableFrom(type))
                    .ToList();

                var controllerName = this.GetControllerName(request);
                var matchedController = matchedTypes.FirstOrDefault(type => type.Name.EqualsInsensitively(controllerName + "controller"));

                return new HttpControllerDescriptor(this.configuration, controllerName, matchedController);
            }
        }

        /// <summary>
        /// The KeyVault test host dependency resolver.
        /// </summary>
        private class KeyVaultTestHostDependencyResolver : IDependencyResolver
        {
            /// <summary>
            /// The KeyVault test host.
            /// </summary>
            private readonly KeyVaultTestHost host;

            /// <summary>
            /// The HTTP configuration.
            /// </summary>
            private readonly HttpConfiguration configuration;

            /// <summary>
            /// Initializes a new instance of the <see cref="KeyVaultTestHostDependencyResolver" /> class.
            /// </summary>
            /// <param name="host">The host.</param>
            /// <param name="configuration">The configuration.</param>
            public KeyVaultTestHostDependencyResolver(KeyVaultTestHost host, HttpConfiguration configuration)
            {
                this.host = host;
                this.configuration = configuration;
            }

            /// <summary>
            /// Begins the scope.
            /// </summary>
            public IDependencyScope BeginScope()
            {
                return this;
            }

            /// <summary>
            /// Gets the service.
            /// </summary>
            /// <param name="serviceType">Type of the service.</param>
            public object GetService(Type serviceType)
            {
                if (typeof(IHttpController).IsAssignableFrom(serviceType))
                {
                    if (typeof(KeyVaultTestHost).GetNestedTypes().Any(type => type == serviceType))
                    {
                        return Activator.CreateInstance(serviceType, args: new object[] { this.host });
                    }
                }

                return null;
            }

            /// <summary>
            /// Gets the services.
            /// </summary>
            /// <param name="serviceType">Type of the service.</param>
            public IEnumerable<object> GetServices(Type serviceType)
            {
                return Enumerable.Empty<object>();
            }

            /// <summary>
            /// Performs application-defined tasks associated with freeing, releasing, or resetting unmanaged resources.
            /// </summary>
            public void Dispose()
            {
            }
        }
    }
}
