﻿//-----------------------------------------------------------
// Copyright (c) Microsoft Corporation.  All rights reserved.
//-----------------------------------------------------------

namespace UnitTests.Providers.Feature.AccessConnectorTests
{
    using System;
    using System.Collections.Generic;
    using System.Linq;
    using System.Net;
    using System.Net.Http;
    using System.Text;
    using System.Threading.Tasks;
    using Microsoft.AspNetCore.Http;
    using Microsoft.AspNetCore.Mvc;
    using Microsoft.VisualStudio.TestTools.UnitTesting;
    using Microsoft.WindowsAzure.ResourceStack.Common.Collections;
    using Microsoft.WindowsAzure.ResourceStack.Common.EventSources;
    using Microsoft.WindowsAzure.ResourceStack.Common.Extensions;
    using Microsoft.WindowsAzure.ResourceStack.Common.Instrumentation;
    using Microsoft.WindowsAzure.ResourceStack.Common.Json;
    using Microsoft.WindowsAzure.ResourceStack.Common.Storage;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Application.Data.DataProviders;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Common.Constants;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Common.Data;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Common.Definitions;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Common.Definitions.AccessConnector;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Common.Definitions.ManagedIdentity;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Common.Engines;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Common.Entities;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Common.ErrorResponses;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Common.Extensions;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Feature.Components;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Feature.Controllers;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Feature.Definitions.AccessConnector;
    using Moq;

    [TestClass]
    public class ControllerTests
    {
        #region Mocks and Set ups

        private AccessConnectorController accessConnectorController;

        public AccessConnectorController AccessConnectorController
        {
            get
            {
                var httpContext = new DefaultHttpContext();
                var controllerContext = new ControllerContext()
                {
                    HttpContext = httpContext,
                };
                httpContext.Request.Scheme = "https";
                httpContext.Request.Host = new HostString("somehost");
                httpContext.Request.Path = "/accessconnectors?api-version=2024-05-01";

                if (accessConnectorController == null)
                {
                    accessConnectorController = new AccessConnectorController(
                        this.AccessConnectorProviderConfiguration.Object,
                        this.FrontdoorEngine.Object);
                    accessConnectorController.ControllerContext = controllerContext;
                }
                //this.accessConnectorController.Request = this.Request;
                return accessConnectorController;
            }
        }

        private Mock<IAccessConnectorProviderConfiguration> accessConnectorProviderConfiguration;

        public Mock<IAccessConnectorProviderConfiguration> AccessConnectorProviderConfiguration
        {
            get
            {
                if (accessConnectorProviderConfiguration == null)
                {
                    accessConnectorProviderConfiguration = new Mock<IAccessConnectorProviderConfiguration>();

                    accessConnectorProviderConfiguration.Setup(
                        o => o.Logger)
                        .Returns(this.Logger.Object);

                    accessConnectorProviderConfiguration.Setup(
                        o => o.AccessConnectorDataProvider)
                        .Returns(this.AccessConnectorDataProvider.Object);
                }

                return accessConnectorProviderConfiguration;
            }
        }

        private Dictionary<string, string> configurationSettings;
        /// <summary>
        /// Override this to set feature flags
        /// </summary>
        protected virtual Dictionary<string, string> ConfigurationSettings
        {
            get
            {
                if (configurationSettings == null)
                {
                    configurationSettings = new Dictionary<string, string>
                    {
                        { "CloudEnvironment", "public" }
                    };
                }

                return configurationSettings;
            }
        }

        private Mock<IFrontdoorEngine> frontdoorEngine;

        public Mock<IFrontdoorEngine> FrontdoorEngine
        {
            get
            {
                if (frontdoorEngine == null)
                {
                    frontdoorEngine = new Mock<IFrontdoorEngine>();

                    frontdoorEngine.Setup(
                        o => o.GetRegisteredFeaturesInSubscription(
                            It.IsAny<string>(),
                            It.IsAny<string>(),
                            It.IsAny<string>()))
                        .Returns(Task.FromResult(new List<string>()));

                    frontdoorEngine.Setup(
                            o => o.CallDatabricksAsyncAccountApi(
                                It.IsAny<HttpMethod>(),
                                It.IsAny<Uri>(),
                                It.IsAny<string>(),
                                It.IsAny<string>(),
                                It.IsAny<CredMgrNotification>(),
                                It.IsAny<InsensitiveDictionary<string>>()))
                        .Returns(Task.CompletedTask);
                }

                return frontdoorEngine;
            }
        }

        private HttpRequestMessage request;

        public HttpRequestMessage Request
        {
            get
            {
                if (request == null)
                {
                    request = new HttpRequestMessage();

                    if (this.AccessConnector != null)
                    {
                        request.Content = new StringContent(
                            AccessConnector.ToJson(),
                            Encoding.UTF8,
                            Constants.Common.MediaType);
                    }

                    request.Headers.Referrer = new Uri(Constants.Common.Referrer);
                    request.RequestUri = new Uri("https://somehosturlforconnector?api-version=2022-04-01-preview");
                }

                return request;
            }
        }

        private AccessConnector accessConnector;

        public AccessConnector AccessConnector
        {
            get
            {
                return accessConnector ?? (accessConnector = new AccessConnector
                {
                    Location = Constants.Common.Location
                });
            }
        }

        private AccessConnectorUpdate accessConnectorUpdate;

        public AccessConnectorUpdate AccessConnectorUpdate
        {
            get { return accessConnectorUpdate ?? (accessConnectorUpdate = new AccessConnectorUpdate()); }
        }

        private Mock<ICommonEventSource> logger;

        public Mock<ICommonEventSource> Logger
        {
            get { return this.logger ?? (this.logger = new Mock<ICommonEventSource>()); }
        }

        private Mock<IAccessConnectorDataProvider> accessConnectorDataProvider;

        public Mock<IAccessConnectorDataProvider> AccessConnectorDataProvider
        {
            get
            {
                if (this.accessConnectorDataProvider == null)
                {
                    this.accessConnectorDataProvider = new Mock<IAccessConnectorDataProvider>();

                    this.accessConnectorDataProvider.Setup(
                        o => o.SaveAccessConnector(
                            It.IsAny<AccessConnectorEntity>())
                    ).Callback(this.saveAction);
                }

                return this.accessConnectorDataProvider;
            }
        }

        private AccessConnector ConvertResponse(IActionResult result)
        {
            return (result as ObjectResult).Value as AccessConnector;
        }

        private AccessConnector[] ConvertContinuationResponse(IActionResult response)
        {
            return ((response as ObjectResult).Value as ResponseWithContinuation<AccessConnector[]>).Value;
        }

        private readonly Action<AccessConnectorEntity> saveAction = new Action<AccessConnectorEntity>((entity) => { });

        private static void SetUpCorrelationContext(string apiVersion)
        {
            var context = new RequestCorrelationContext();
            context.SetAdditionalProperties(new OrdinalDictionary<string>());
            context.AdditionalProperties.Add(RequestCorrelationContextExtensions.HeaderResourceHomeTenantId, Constants.Common.HomeTenantId);

            var identity = new RequestIdentity
            {
                Claims = new Dictionary<string, string>
                {
                    { Constants.Common.PrincipalOidClaim, Constants.Common.CreatedByPrincipalOid },
                    { Constants.Common.PrincipalLegacyPuidClaim, Constants.Common.CreatedByPrincipalLegacyPuid },
                    { Constants.Common.PrincipalApplicationIdClaim, Constants.Common.CreatedByAppId }
                }
            };

            context.Initialize(apiVersion: apiVersion);
            context.SetAuthenticationIdentity(identity);

            RequestCorrelationContext.Current.Initialize(context);
            RequestCorrelationContext.Current.SetTenantId(Constants.Common.HomeTenantId);
            RequestCorrelationContext.Current.CorrelationId = Constants.Common.CorrelationId;
        }

        private static InsensitiveDictionary<UserAssignedIdentity> SetUpUserAssignedIdentities(uint identityCount)
        {
            InsensitiveDictionary<UserAssignedIdentity> userAssignedIdentities = new InsensitiveDictionary<UserAssignedIdentity>();
            string objectId = Constants.UserAssignedIdentity.Identity1.ObjectId;
            string clientId = Constants.UserAssignedIdentity.Identity1.ClientId;
            string tenantId = Constants.UserAssignedIdentity.Identity1.TenantId;

            for (int i = 0; i < identityCount; i++)
            {
                string resourceId = Constants.UserAssignedIdentity.Identity1.ResourceId + i;
                userAssignedIdentities.Add(resourceId, new UserAssignedIdentity(objectId, clientId, tenantId));
            }

            return userAssignedIdentities;
        }

        [TestInitialize]
        public void SetUp()
        {
            SetUpCorrelationContext(Constants.Common.DatabricksV8ControllerGAApiVersion);
            FrontdoorEngineProviderConfiguration.Instance.InitializeFrontdoorEngine(this.FrontdoorEngine.Object);
        }

        [TestCleanup]
        public void TearDown()
        {
            RequestCorrelationContext.Current.Dispose();
        }

        #endregion

        #region Put accessConnector Tests

        [TestMethod("Creates accessConnector without tags and identity")]
        public void TestCreateAccessConnectorWithoutTags()
        {
            //Set up
            this.AccessConnectorDataProvider.Setup(o => o.FindAccessConnector(
                It.Is<string>(subscription => subscription == Constants.Common.SubscriptionId),
                It.Is<string>(rgName => rgName == Constants.Common.ResourceGroupName),
                It.Is<string>(connectorName => connectorName == Constants.Connector.ConnectorName1)))
                .Returns(Task.FromResult<AccessConnectorEntity>(null));

            //Test Item
            var response = this.AccessConnectorController.PutAccessConnector(
                Constants.Common.SubscriptionId,
                Constants.Common.ResourceGroupName,
                Constants.Connector.ConnectorName1,
                this.AccessConnector,
                null,
                "",
                "",
                ""
                ).Result;

            //Assert
            Assert.AreNotEqual(null, response);
            var result = response as ObjectResult;
            Assert.AreEqual((int)HttpStatusCode.Created, result.StatusCode);

            var responseContent = ConvertResponse(response);

            Assert.AreEqual(null, responseContent.Identity);
            Assert.AreEqual(null, responseContent.Tags);
            Assert.AreEqual(Constants.Common.Location, responseContent.Location);
            Assert.AreEqual(AccessConnectorState.Succeeded, responseContent.Properties.ProvisioningState);
            Assert.AreEqual(Constants.Connector.ConnectorName1, responseContent.Name);
            Assert.AreNotEqual(string.Empty, responseContent.Id);
        }

        [TestMethod("Creates accessConnector with tags and without identity")]
        public void TestCreateAccessConnectorWithTagsAndWithoutIdentity()
        {
            //Set up
            this.AccessConnectorDataProvider.Setup(o => o.FindAccessConnector(
                    It.Is<string>(subscription => subscription == Constants.Common.SubscriptionId),
                    It.Is<string>(rgName => rgName == Constants.Common.ResourceGroupName),
                    It.Is<string>(connectorName => connectorName == Constants.Connector.ConnectorName1)))
                .Returns(Task.FromResult<AccessConnectorEntity>(null));

            this.AccessConnector.Tags =
                new InsensitiveDictionary<string>() { { Constants.Common.Tag1, Constants.Common.Value1 } };

            //Test Item
            var response = this.AccessConnectorController.PutAccessConnector(
                Constants.Common.SubscriptionId,
                Constants.Common.ResourceGroupName,
                Constants.Connector.ConnectorName1,
                this.AccessConnector,
                null,
                "", "", "").Result;

            var responseContent = ConvertResponse(response);

            //Assert
            Assert.AreEqual(null, responseContent.Identity);
            Assert.AreEqual(1, responseContent.Tags.Count);
            Assert.IsTrue(responseContent.Tags.Keys.Contains(Constants.Common.Tag1));
            Assert.IsTrue(responseContent.Tags.Values.Contains(Constants.Common.Value1));
        }

        [TestMethod("Creates accessConnector with SystemAssigned identity only")]
        public void TestCreateAccessConnectorWithIdentity()
        {
            //Set up
            this.AccessConnectorDataProvider.Setup(o => o.FindAccessConnector(
                    It.Is<string>(subscription => subscription == Constants.Common.SubscriptionId),
                    It.Is<string>(rgName => rgName == Constants.Common.ResourceGroupName),
                    It.Is<string>(connectorName => connectorName == Constants.Connector.ConnectorName1)))
                .Returns(Task.FromResult<AccessConnectorEntity>(null));

            this.AccessConnector.Identity = new Identity() { Type = IdentityType.SystemAssigned };
            this.Request.Headers.Add(RequestCorrelationContext.HeaderResourceIdentityUrl, Constants.SystemAssignedIdentity.DataPlaneUrl);
            this.Request.Headers.Add(RequestCorrelationContext.HeaderResourceIdentityPrincipalid, Constants.SystemAssignedIdentity.PrincipalId);
            this.Request.Headers.Add(ProviderConstants.ManagedIdentity.HeaderHomeTenantId, Constants.SystemAssignedIdentity.TenantId);

            //Test Item
            var response = this.AccessConnectorController.PutAccessConnector(
                Constants.Common.SubscriptionId,
                Constants.Common.ResourceGroupName,
                Constants.Connector.ConnectorName1,
                this.AccessConnector,
                null,
                Constants.SystemAssignedIdentity.DataPlaneUrl, Constants.SystemAssignedIdentity.PrincipalId, Constants.SystemAssignedIdentity.TenantId).Result;

            var responseContent = ConvertResponse(response);

            //Assert
            Assert.AreNotEqual(null, responseContent);
            Assert.AreNotEqual(null, responseContent.Identity);
            Assert.AreEqual(IdentityType.SystemAssigned, responseContent.Identity.Type);
            Assert.AreEqual(Constants.SystemAssignedIdentity.PrincipalId, responseContent.Identity.PrincipalId);
            Assert.AreEqual(Constants.SystemAssignedIdentity.TenantId, responseContent.Identity.TenantId);
        }

        [TestMethod("Creates accessConnector with UserAssigned and SystemAssigned identity")]
        public void TestCreateAccessConnectorWithUserAssignedAndSystemAssignedIdentity()
        {
            using (RequestCorrelationContext.NewCorrelationContextScope())
            {
                //Set up
                SetUpCorrelationContext(Constants.Common.Connector20230501APIVersion);

                this.AccessConnectorDataProvider.Setup(o => o.FindAccessConnector(
                        It.Is<string>(subscription => subscription == Constants.Common.SubscriptionId),
                        It.Is<string>(rgName => rgName == Constants.Common.ResourceGroupName),
                        It.Is<string>(connectorName => connectorName == Constants.Connector.ConnectorName1)))
                    .Returns(Task.FromResult<AccessConnectorEntity>(null));

                this.AccessConnector.Identity = new Identity()
                {
                    Type = IdentityType.SystemAssigned | IdentityType.UserAssigned,
                    UserAssignedIdentities = new InsensitiveDictionary<UserAssignedIdentity>
                    {
                        { Constants.UserAssignedIdentity.Identity1.ResourceId, new UserAssignedIdentity() }
                    }
                };

                var identitiesCred = new IdentitiesCredential
                {
                    TenantId = Constants.SystemAssignedIdentity.TenantId,
                    ClientId = Constants.SystemAssignedIdentity.PrincipalId
                };

                var userAssignedIdentity1 = new UserAssignedIdentityCredential
                {
                    ResourceId = Constants.UserAssignedIdentity.Identity1.ResourceId,
                    ObjectId = Constants.UserAssignedIdentity.Identity1.ObjectId,
                    ClientId = Constants.UserAssignedIdentity.Identity1.ClientId
                };

                identitiesCred.UserAssignedIdentities = new List<UserAssignedIdentityCredential>()
                { userAssignedIdentity1 };

                this.FrontdoorEngine.Setup(o => o.GetIdentitiesCredentials(
                    It.IsAny<string>(),
                    It.IsAny<CredentialRequest>())).Returns(Task.FromResult<IdentitiesCredential>(identitiesCred));

                this.Request.Headers.Add(RequestCorrelationContext.HeaderResourceIdentityUrl, Constants.SystemAssignedIdentity.DataPlaneUrl);
                this.Request.Headers.Add(RequestCorrelationContext.HeaderResourceIdentityPrincipalid, Constants.SystemAssignedIdentity.PrincipalId);
                this.Request.Headers.Add(ProviderConstants.ManagedIdentity.HeaderHomeTenantId, Constants.SystemAssignedIdentity.TenantId);

                //Test Item
                using (new AppConfigSettingScope(this.ConfigurationSettings))
                {
                    var response = this.AccessConnectorController.PutAccessConnector(
                        Constants.Common.SubscriptionId,
                        Constants.Common.ResourceGroupName,
                        Constants.Connector.ConnectorName1,
                        this.AccessConnector,
                        null, Constants.SystemAssignedIdentity.DataPlaneUrl, Constants.SystemAssignedIdentity.PrincipalId, Constants.SystemAssignedIdentity.TenantId).Result;

                    this.AccessConnectorDataProvider.Verify(
                            o => o.FindAccessConnector(
                                It.Is<string>(subscription => subscription == Constants.Common.SubscriptionId),
                                It.Is<string>(rgName => rgName == Constants.Common.ResourceGroupName),
                                It.Is<string>(connectorName => connectorName == Constants.Connector.ConnectorName1)), Times.Once);

                    this.FrontdoorEngine.Verify(
                            o => o.CallDatabricksAsyncAccountApi(
                            It.IsAny<HttpMethod>(),
                            It.IsAny<Uri>(),
                            It.IsAny<string>(),
                            It.IsAny<string>(),
                            It.IsAny<CredMgrNotification>(),
                            It.IsAny<InsensitiveDictionary<string>>()), Times.Once);

                    this.AccessConnectorDataProvider.Verify(
                            o => o.SaveAccessConnector(
                                It.IsAny<AccessConnectorEntity>()), Times.Once);

                    var responseContent = ConvertResponse(response);

                    //Assert
                    Assert.AreNotEqual(null, responseContent);
                    Assert.AreNotEqual(null, responseContent.Identity);
                    Assert.AreEqual((int)HttpStatusCode.Created, (response as ObjectResult).StatusCode);

                    Assert.AreEqual(responseContent.Identity.Type, IdentityType.SystemAssigned | IdentityType.UserAssigned);
                    Assert.AreEqual(Constants.SystemAssignedIdentity.PrincipalId, responseContent.Identity.PrincipalId);
                    Assert.AreEqual(Constants.SystemAssignedIdentity.TenantId, responseContent.Identity.TenantId);
                    Assert.AreNotEqual(null, responseContent.Identity.UserAssignedIdentities);
                    Assert.AreEqual(1, responseContent.Identity.UserAssignedIdentities.Count);
                    Assert.IsTrue(responseContent.Identity.UserAssignedIdentities.ContainsKey(Constants.UserAssignedIdentity.Identity1.ResourceId));
                }
            }
        }

        [TestMethod("Creates accessConnector with 3 UserAssigned and SystemAssigned identity")]
        public void TestCreateAccessConnectorWith3UserAssignedAndSystemAssignedIdentity()
        {
            using (RequestCorrelationContext.NewCorrelationContextScope())
            {
                //Set up
                SetUpCorrelationContext(Constants.Common.Connector20230501APIVersion);

                this.AccessConnectorDataProvider.Setup(o => o.FindAccessConnector(
                        It.Is<string>(subscription => subscription == Constants.Common.SubscriptionId),
                        It.Is<string>(rgName => rgName == Constants.Common.ResourceGroupName),
                        It.Is<string>(connectorName => connectorName == Constants.Connector.ConnectorName1)))
                    .Returns(Task.FromResult<AccessConnectorEntity>(null));

                this.AccessConnector.Identity = new Identity()
                {
                    Type = IdentityType.SystemAssigned | IdentityType.UserAssigned,
                    UserAssignedIdentities = new InsensitiveDictionary<UserAssignedIdentity>
                    {
                        { Constants.UserAssignedIdentity.Identity1.ResourceId, new UserAssignedIdentity() },
                        { Constants.UserAssignedIdentity.Identity2.ResourceId, new UserAssignedIdentity() },
                        { Constants.UserAssignedIdentity.Identity3.ResourceId, new UserAssignedIdentity() },
                    }
                };

                var identitiesCred = new IdentitiesCredential
                {
                    TenantId = Constants.SystemAssignedIdentity.TenantId,
                    ClientId = Constants.SystemAssignedIdentity.PrincipalId
                };

                var userAssignedIdentity1 = new UserAssignedIdentityCredential
                {
                    ResourceId = Constants.UserAssignedIdentity.Identity1.ResourceId,
                    ObjectId = Constants.UserAssignedIdentity.Identity1.ObjectId,
                    ClientId = Constants.UserAssignedIdentity.Identity1.ClientId
                };

                var userAssignedIdentity2 = new UserAssignedIdentityCredential
                {
                    ResourceId = Constants.UserAssignedIdentity.Identity2.ResourceId,
                    ObjectId = Constants.UserAssignedIdentity.Identity2.ObjectId,
                    ClientId = Constants.UserAssignedIdentity.Identity2.ClientId
                };

                var userAssignedIdentity3 = new UserAssignedIdentityCredential
                {
                    ResourceId = Constants.UserAssignedIdentity.Identity3.ResourceId,
                    ObjectId = Constants.UserAssignedIdentity.Identity3.ObjectId,
                    ClientId = Constants.UserAssignedIdentity.Identity3.ClientId
                };

                identitiesCred.UserAssignedIdentities = new List<UserAssignedIdentityCredential>()
                { userAssignedIdentity1, userAssignedIdentity2, userAssignedIdentity3 };

                this.FrontdoorEngine.Setup(o => o.GetIdentitiesCredentials(
                    It.IsAny<string>(),
                    It.IsAny<CredentialRequest>())).Returns(Task.FromResult<IdentitiesCredential>(identitiesCred));

                this.Request.Headers.Add(RequestCorrelationContext.HeaderResourceIdentityUrl, Constants.SystemAssignedIdentity.DataPlaneUrl);
                this.Request.Headers.Add(RequestCorrelationContext.HeaderResourceIdentityPrincipalid, Constants.SystemAssignedIdentity.PrincipalId);
                this.Request.Headers.Add(ProviderConstants.ManagedIdentity.HeaderHomeTenantId, Constants.SystemAssignedIdentity.TenantId);

                //Test Item
                using (new AppConfigSettingScope(this.ConfigurationSettings))
                {
                    var response = this.AccessConnectorController.PutAccessConnector(
                        Constants.Common.SubscriptionId,
                        Constants.Common.ResourceGroupName,
                        Constants.Connector.ConnectorName1,
                        this.AccessConnector,
                        null, Constants.SystemAssignedIdentity.DataPlaneUrl, Constants.SystemAssignedIdentity.PrincipalId, Constants.SystemAssignedIdentity.TenantId).Result;

                    this.AccessConnectorDataProvider.Verify(
                            o => o.FindAccessConnector(
                                It.Is<string>(subscription => subscription == Constants.Common.SubscriptionId),
                                It.Is<string>(rgName => rgName == Constants.Common.ResourceGroupName),
                                It.Is<string>(connectorName => connectorName == Constants.Connector.ConnectorName1)), Times.Once);

                    this.FrontdoorEngine.Verify(
                            o => o.CallDatabricksAsyncAccountApi(
                            It.IsAny<HttpMethod>(),
                            It.IsAny<Uri>(),
                            It.IsAny<string>(),
                            It.IsAny<string>(),
                            It.IsAny<CredMgrNotification>(),
                            It.IsAny<InsensitiveDictionary<string>>()), Times.Once);

                    this.AccessConnectorDataProvider.Verify(
                            o => o.SaveAccessConnector(
                                It.IsAny<AccessConnectorEntity>()), Times.Once);

                    var responseContent = ConvertResponse(response);

                    //Assert
                    Assert.AreNotEqual(null, responseContent);
                    Assert.AreNotEqual(null, responseContent.Identity);
                    Assert.AreEqual((int)HttpStatusCode.Created, (response as ObjectResult).StatusCode);

                    Assert.AreEqual(responseContent.Identity.Type, IdentityType.SystemAssigned | IdentityType.UserAssigned);
                    Assert.AreEqual(Constants.SystemAssignedIdentity.PrincipalId, responseContent.Identity.PrincipalId);
                    Assert.AreEqual(Constants.SystemAssignedIdentity.TenantId, responseContent.Identity.TenantId);
                    Assert.AreNotEqual(null, responseContent.Identity.UserAssignedIdentities);
                    Assert.AreEqual(3, responseContent.Identity.UserAssignedIdentities.Count);
                    Assert.IsTrue(responseContent.Identity.UserAssignedIdentities.ContainsKey(Constants.UserAssignedIdentity.Identity1.ResourceId));
                    Assert.IsTrue(responseContent.Identity.UserAssignedIdentities.ContainsKey(Constants.UserAssignedIdentity.Identity2.ResourceId));
                    Assert.IsTrue(responseContent.Identity.UserAssignedIdentities.ContainsKey(Constants.UserAssignedIdentity.Identity3.ResourceId));
                }
            }
        }

        [TestMethod("Update accessConnector with UserAssigned and SystemAssigned identity")]
        public void TestUpdateAccessConnectorWithUserAssignedAndSystemAssignedIdentity()
        {
            using (RequestCorrelationContext.NewCorrelationContextScope())
            {
                //Set up
                var accessConnectorEntity = new AccessConnectorEntity(
                    Constants.Connector.ConnectorName1,
                    Constants.Common.ResourceGroupName,
                    Constants.Common.SubscriptionId,
                    Constants.Common.Location,
                    tags: null)
                {
                    Identity = new Identity()
                    {
                        Type = IdentityType.SystemAssigned | IdentityType.UserAssigned,
                        PrincipalId = Constants.SystemAssignedIdentity.PrincipalId,
                        TenantId = Constants.SystemAssignedIdentity.TenantId,
                        UserAssignedIdentities = new InsensitiveDictionary<UserAssignedIdentity>
                            {
                                { Constants.UserAssignedIdentity.Identity1.ResourceId, new UserAssignedIdentity() }
                            }
                    },
                    IdentityUrl = Constants.SystemAssignedIdentity.DataPlaneUrl,
                    Properties = new AccessConnectorProperties()
                    {
                        ProvisioningState = AccessConnectorState.Failed
                    }
                };

                SetUpCorrelationContext(Constants.Common.Connector20230501APIVersion);

                this.AccessConnectorDataProvider.Setup(o => o.FindAccessConnector(
                        It.Is<string>(subscription => subscription == Constants.Common.SubscriptionId),
                        It.Is<string>(rgName => rgName == Constants.Common.ResourceGroupName),
                        It.Is<string>(connectorName => connectorName == Constants.Connector.ConnectorName1)))
                    .Returns(Task.FromResult<AccessConnectorEntity>(accessConnectorEntity));

                this.AccessConnector.Identity = new Identity()
                {
                    Type = IdentityType.SystemAssigned | IdentityType.UserAssigned,
                    UserAssignedIdentities = new InsensitiveDictionary<UserAssignedIdentity>
                    {
                        { Constants.UserAssignedIdentity.Identity1.ResourceId, new UserAssignedIdentity() }
                    }
                };

                var identitiesCred = new IdentitiesCredential
                {
                    TenantId = Constants.SystemAssignedIdentity.TenantId,
                    ClientId = Constants.SystemAssignedIdentity.PrincipalId
                };

                var userAssignedIdentity1 = new UserAssignedIdentityCredential
                {
                    ResourceId = Constants.UserAssignedIdentity.Identity1.ResourceId,
                    ObjectId = Constants.UserAssignedIdentity.Identity1.ObjectId,
                    ClientId = Constants.UserAssignedIdentity.Identity1.ClientId
                };

                identitiesCred.UserAssignedIdentities = new List<UserAssignedIdentityCredential>()
                { userAssignedIdentity1 };

                this.FrontdoorEngine.Setup(o => o.GetIdentitiesCredentials(
                    It.IsAny<string>(),
                    It.IsAny<CredentialRequest>())).Returns(Task.FromResult<IdentitiesCredential>(identitiesCred));

                this.Request.Headers.Add(RequestCorrelationContext.HeaderResourceIdentityUrl, Constants.SystemAssignedIdentity.DataPlaneUrl);
                this.Request.Headers.Add(RequestCorrelationContext.HeaderResourceIdentityPrincipalid, Constants.SystemAssignedIdentity.PrincipalId);
                this.Request.Headers.Add(ProviderConstants.ManagedIdentity.HeaderHomeTenantId, Constants.SystemAssignedIdentity.TenantId);

                //Test Item
                using (new AppConfigSettingScope(this.ConfigurationSettings))
                {
                    var response = this.AccessConnectorController.PutAccessConnector(
                            Constants.Common.SubscriptionId,
                            Constants.Common.ResourceGroupName,
                            Constants.Connector.ConnectorName1,
                            this.AccessConnector,
                            null, Constants.SystemAssignedIdentity.DataPlaneUrl, Constants.SystemAssignedIdentity.PrincipalId, Constants.SystemAssignedIdentity.TenantId).Result;

                    this.AccessConnectorDataProvider.Verify(
                            o => o.FindAccessConnector(
                                It.Is<string>(subscription => subscription == Constants.Common.SubscriptionId),
                                It.Is<string>(rgName => rgName == Constants.Common.ResourceGroupName),
                                It.Is<string>(connectorName => connectorName == Constants.Connector.ConnectorName1)), Times.Once);

                    this.FrontdoorEngine.Verify(
                            o => o.CallDatabricksAsyncAccountApi(
                            It.IsAny<HttpMethod>(),
                            It.IsAny<Uri>(),
                            It.IsAny<string>(),
                            It.IsAny<string>(),
                            It.IsAny<CredMgrNotification>(),
                            It.IsAny<InsensitiveDictionary<string>>()), Times.Once);

                    this.AccessConnectorDataProvider.Verify(
                            o => o.SaveAccessConnector(
                                It.IsAny<AccessConnectorEntity>()), Times.Once);

                    var responseContent = ConvertResponse(response);

                    //Assert
                    Assert.AreNotEqual(null, responseContent);
                    Assert.AreNotEqual(null, responseContent.Identity);
                    Assert.AreEqual((int)HttpStatusCode.OK, (response as ObjectResult).StatusCode);

                    Assert.AreEqual(responseContent.Identity.Type, IdentityType.SystemAssigned | IdentityType.UserAssigned);
                    Assert.AreEqual(Constants.SystemAssignedIdentity.PrincipalId, responseContent.Identity.PrincipalId);
                    Assert.AreEqual(Constants.SystemAssignedIdentity.TenantId, responseContent.Identity.TenantId);
                    Assert.AreNotEqual(null, responseContent.Identity.UserAssignedIdentities);
                    Assert.AreEqual(1, responseContent.Identity.UserAssignedIdentities.Count);
                    Assert.IsTrue(responseContent.Identity.UserAssignedIdentities.ContainsKey(Constants.UserAssignedIdentity.Identity1.ResourceId));
                }
            }
        }

        [TestMethod("Updates accessConnector with tags")]
        public void TestUpdateAccessConnectorWithTags()
        {
            //Set up
            var accessConnectorEntity = new AccessConnectorEntity(
                Constants.Connector.ConnectorName1,
                Constants.Common.ResourceGroupName,
                Constants.Common.SubscriptionId,
                Constants.Common.Location,
                tags: null,
                identity: null)
            {
                Properties = new AccessConnectorProperties()
                {
                    ProvisioningState = AccessConnectorState.Succeeded
                }
            };

            this.AccessConnectorDataProvider.Setup(o => o.FindAccessConnector(
                    It.Is<string>(subscription => subscription == Constants.Common.SubscriptionId),
                    It.Is<string>(rgName => rgName == Constants.Common.ResourceGroupName),
                    It.Is<string>(connectorName => connectorName == Constants.Connector.ConnectorName1)))
                .Returns(Task.FromResult<AccessConnectorEntity>(accessConnectorEntity));

            this.AccessConnector.Tags = new InsensitiveDictionary<string>() { { Constants.Common.Tag1, Constants.Common.Value1 } };

            //Test Item
            var response = this.AccessConnectorController.PutAccessConnector(
                Constants.Common.SubscriptionId,
                Constants.Common.ResourceGroupName,
                Constants.Connector.ConnectorName1,
                this.AccessConnector,
                null, "", "", "").Result;

            var responseContent = ConvertResponse(response);

            //Assert
            Assert.AreNotEqual(null, responseContent.Tags);
            Assert.AreEqual(1, responseContent.Tags.Count);
            Assert.IsTrue(responseContent.Tags.Keys.Contains(Constants.Common.Tag1));
        }

        [TestMethod("Updates accessConnector with SystemAssigned Identity fails during notification.")]
        public void TestUpdateAccessConnectorFailureForIdentity()
        {
            //Set up
            var accessConnectorEntity = new AccessConnectorEntity(
                Constants.Connector.ConnectorName1,
                Constants.Common.ResourceGroupName,
                Constants.Common.SubscriptionId,
                Constants.Common.Location,
                tags: null,
                identity: null)
            {
                Properties = new AccessConnectorProperties()
                {
                    ProvisioningState = AccessConnectorState.Succeeded
                }
            };

            this.AccessConnector.Identity = new Identity() { Type = IdentityType.SystemAssigned };
            this.Request.Headers.Add(RequestCorrelationContext.HeaderResourceIdentityUrl, Constants.SystemAssignedIdentity.DataPlaneUrl);
            this.Request.Headers.Add(RequestCorrelationContext.HeaderResourceIdentityPrincipalid, Constants.SystemAssignedIdentity.PrincipalId);
            this.Request.Headers.Add(ProviderConstants.ManagedIdentity.HeaderHomeTenantId, Constants.SystemAssignedIdentity.TenantId);

            this.AccessConnectorDataProvider.Setup(o => o.FindAccessConnector(
                    It.Is<string>(subscription => subscription == Constants.Common.SubscriptionId),
                    It.Is<string>(rgName => rgName == Constants.Common.ResourceGroupName),
                    It.Is<string>(connectorName => connectorName == Constants.Connector.ConnectorName1)))
                .Returns(Task.FromResult<AccessConnectorEntity>(accessConnectorEntity));

            this.FrontdoorEngine.Setup(
                            o => o.CallDatabricksAsyncAccountApi(
                                It.IsAny<HttpMethod>(),
                                It.IsAny<Uri>(),
                                It.IsAny<string>(),
                                It.IsAny<string>(),
                                It.IsAny<CredMgrNotification>(),
                                It.IsAny<InsensitiveDictionary<string>>()))
                        .Throws<Exception>();

            //Test Item
            try
            {
                var response = this.AccessConnectorController.PutAccessConnector(
                    Constants.Common.SubscriptionId,
                    Constants.Common.ResourceGroupName,
                    Constants.Connector.ConnectorName1,
                    this.AccessConnector,
                    null, Constants.SystemAssignedIdentity.DataPlaneUrl, Constants.SystemAssignedIdentity.PrincipalId, Constants.SystemAssignedIdentity.TenantId).Result;
            }
            catch (Exception)
            {
                //Ignore exception
            }
            finally
            {
                //Assert
                this.AccessConnectorDataProvider.Verify(
                    o => o.SaveAccessConnector(
                        It.Is<AccessConnectorEntity>(
                            entity => entity.Properties.ProvisioningState == AccessConnectorState.Failed))
                    );
            }
        }

        [TestMethod("Updates previously failed accessConnector with SystemAssigned Identity ensure it notifies CM.")]
        public void TestUpdateFailedAccessConnectorWithIdentityAgain()
        {
            //Set up
            var accessConnectorEntity = new AccessConnectorEntity(
                Constants.Connector.ConnectorName1,
                Constants.Common.ResourceGroupName,
                Constants.Common.SubscriptionId,
                Constants.Common.Location,
                tags: null)
            {
                Identity = new Identity()
                {
                    Type = IdentityType.SystemAssigned,
                    PrincipalId = Constants.SystemAssignedIdentity.PrincipalId,
                    TenantId = Constants.SystemAssignedIdentity.TenantId
                },
                IdentityUrl = Constants.SystemAssignedIdentity.DataPlaneUrl,
                Properties = new AccessConnectorProperties()
                {
                    ProvisioningState = AccessConnectorState.Failed
                }
            };

            this.AccessConnector.Identity = new Identity() { Type = IdentityType.SystemAssigned };
            this.Request.Headers.Add(RequestCorrelationContext.HeaderResourceIdentityUrl, Constants.SystemAssignedIdentity.DataPlaneUrl);
            this.Request.Headers.Add(RequestCorrelationContext.HeaderResourceIdentityPrincipalid, Constants.SystemAssignedIdentity.PrincipalId);
            this.Request.Headers.Add(ProviderConstants.ManagedIdentity.HeaderHomeTenantId, Constants.SystemAssignedIdentity.TenantId);

            this.AccessConnectorDataProvider.Setup(o => o.FindAccessConnector(
                    It.Is<string>(subscription => subscription == Constants.Common.SubscriptionId),
                    It.Is<string>(rgName => rgName == Constants.Common.ResourceGroupName),
                    It.Is<string>(connectorName => connectorName == Constants.Connector.ConnectorName1)))
                .Returns(Task.FromResult<AccessConnectorEntity>(accessConnectorEntity));

            //Test Item
            var response = this.AccessConnectorController.PutAccessConnector(
                    Constants.Common.SubscriptionId,
                    Constants.Common.ResourceGroupName,
                    Constants.Connector.ConnectorName1,
                    this.AccessConnector,
                    null, Constants.SystemAssignedIdentity.DataPlaneUrl, Constants.SystemAssignedIdentity.PrincipalId, Constants.SystemAssignedIdentity.TenantId).Result;
            //Assert
            this.AccessConnectorDataProvider.Verify(
                o => o.SaveAccessConnector(
                    It.Is<AccessConnectorEntity>(
                        entity => entity.Properties.ProvisioningState == AccessConnectorState.Succeeded))
                );

            this.FrontdoorEngine.Verify(
                o => o.CallDatabricksAsyncAccountApi(
                    It.IsAny<HttpMethod>(),
                    It.IsAny<Uri>(),
                    It.IsAny<string>(),
                    It.IsAny<string>(),
                    It.IsAny<CredMgrNotification>(),
                    It.IsAny<InsensitiveDictionary<string>>())
                );
        }

        [TestMethod("Creates accessConnector with SystemData")]
        public void TestCreateAccessConnectorWithSystemData()
        {
            //Set up
            this.AccessConnectorDataProvider.Setup(o => o.FindAccessConnector(
                It.Is<string>(subscription => subscription == Constants.Common.SubscriptionId),
                It.Is<string>(rgName => rgName == Constants.Common.ResourceGroupName),
                It.Is<string>(connectorName => connectorName == Constants.Connector.ConnectorName1)))
                .Returns(Task.FromResult<AccessConnectorEntity>(null));

            var incomingSystemData = SystemDataExtension.GetSystemData(Constants.Common.SystemDataJson);

            //Test Item
            var response = this.AccessConnectorController.PutAccessConnector(
                Constants.Common.SubscriptionId,
                Constants.Common.ResourceGroupName,
                Constants.Connector.ConnectorName1,
                this.AccessConnector,
                Constants.Common.SystemDataJson, "", "", "").Result;

            var responseContent = ConvertResponse(response);

            //Assert
            Assert.IsNotNull(responseContent.SystemData);
            Assert.IsTrue(responseContent.SystemData.ToJson().Equals(incomingSystemData.ToJson()));
        }

        [TestMethod("Updates accessConnector with SystemData")]
        public void TestUpdateAccessConnectorWithSystemData()
        {
            //Set up
            var accessConnectorEntity = new AccessConnectorEntity(
                Constants.Connector.ConnectorName1,
                Constants.Common.ResourceGroupName,
                Constants.Common.SubscriptionId,
                Constants.Common.Location,
                systemData: new SystemData(),
                tags: null,
                identity: null)
            {
                Properties = new AccessConnectorProperties()
                {
                    ProvisioningState = AccessConnectorState.Succeeded
                }
            };

            this.AccessConnectorDataProvider.Setup(o => o.FindAccessConnector(
                    It.Is<string>(subscription => subscription == Constants.Common.SubscriptionId),
                    It.Is<string>(rgName => rgName == Constants.Common.ResourceGroupName),
                    It.Is<string>(connectorName => connectorName == Constants.Connector.ConnectorName1)))
                .Returns(Task.FromResult<AccessConnectorEntity>(accessConnectorEntity));

            var incomingSystemData = SystemDataExtension.GetSystemData(Constants.Common.SystemDataJson);

            //Test Item
            var response = this.AccessConnectorController.PutAccessConnector(
                Constants.Common.SubscriptionId,
                Constants.Common.ResourceGroupName,
                Constants.Connector.ConnectorName1,
                this.AccessConnector,
                Constants.Common.SystemDataJson, "", "", "").Result;

            var responseContent = ConvertResponse(response);

            //Assert
            Assert.IsNotNull(responseContent.SystemData);
            Assert.IsTrue(responseContent.SystemData.ToJson().Equals(incomingSystemData.ToJson()));
        }

        [TestMethod("Create Access Connector with Max+1 UA MI in PUT. This should fail.")]
        public void CreateConnectorswithMaxPlusOneMI()
        {
            using (RequestCorrelationContext.NewCorrelationContextScope())
            {
                //Set up
                SetUpCorrelationContext(Constants.Common.Connector20230501APIVersion);

                this.AccessConnectorDataProvider.Setup(o => o.FindAccessConnector(
                        It.Is<string>(subscription => subscription == Constants.Common.SubscriptionId),
                        It.Is<string>(rgName => rgName == Constants.Common.ResourceGroupName),
                        It.Is<string>(connectorName => connectorName == Constants.Connector.ConnectorName1)))
                    .Returns(Task.FromResult<AccessConnectorEntity>(null));

                this.AccessConnector.Identity = new Identity()
                {
                    Type = IdentityType.UserAssigned,
                    UserAssignedIdentities = SetUpUserAssignedIdentities(ProviderConstants.ManagedIdentity.MaxUserAssignedIdentitiesPerConnector + 1)
                };

                var identitiesCred = new IdentitiesCredential
                {
                    TenantId = Constants.SystemAssignedIdentity.TenantId,
                    ClientId = Constants.SystemAssignedIdentity.PrincipalId
                };

                var userAssignedIdentity1 = new UserAssignedIdentityCredential
                {
                    ResourceId = Constants.UserAssignedIdentity.Identity1.ResourceId,
                    ObjectId = Constants.UserAssignedIdentity.Identity1.ObjectId,
                    ClientId = Constants.UserAssignedIdentity.Identity1.ClientId
                };

                var userAssignedIdentity2 = new UserAssignedIdentityCredential
                {
                    ResourceId = Constants.UserAssignedIdentity.Identity2.ResourceId,
                    ObjectId = Constants.UserAssignedIdentity.Identity2.ObjectId,
                    ClientId = Constants.UserAssignedIdentity.Identity2.ClientId
                };

                identitiesCred.UserAssignedIdentities = new List<UserAssignedIdentityCredential>()
                { userAssignedIdentity1, userAssignedIdentity2 };

                this.FrontdoorEngine.Setup(o => o.GetIdentitiesCredentials(
                    It.IsAny<string>(),
                    It.IsAny<CredentialRequest>())).Returns(Task.FromResult<IdentitiesCredential>(identitiesCred));

                this.Request.Headers.Add(RequestCorrelationContext.HeaderResourceIdentityUrl, Constants.SystemAssignedIdentity.DataPlaneUrl);
                this.Request.Headers.Add(RequestCorrelationContext.HeaderResourceIdentityPrincipalid, Constants.SystemAssignedIdentity.PrincipalId);
                this.Request.Headers.Add(ProviderConstants.ManagedIdentity.HeaderHomeTenantId, Constants.SystemAssignedIdentity.TenantId);

                //Test Item
                using (new AppConfigSettingScope(this.ConfigurationSettings))
                {
                    try
                    {
                        var response = this.AccessConnectorController.PutAccessConnector(
                            Constants.Common.SubscriptionId,
                            Constants.Common.ResourceGroupName,
                            Constants.Connector.ConnectorName1,
                            this.AccessConnector,
                            null, Constants.SystemAssignedIdentity.DataPlaneUrl, Constants.SystemAssignedIdentity.PrincipalId, Constants.SystemAssignedIdentity.TenantId).Result;
                    }
                    catch (Exception ex)
                    {
                        Assert.AreEqual(ex.InnerException.Message,
                            ErrorResponseMessages.LimitedUserAssignedIdentitySupportPerAccessConnector.ToLocalizedMessage(ProviderConstants.ManagedIdentity.MaxUserAssignedIdentitiesPerConnector, ProviderConstants.ManagedIdentity.MaxUserAssignedIdentitiesPerConnector + 1));
                    }
                    finally
                    {
                        //Assert
                        this.AccessConnectorDataProvider.Verify(
                            o => o.FindAccessConnector(
                                It.Is<string>(subscription => subscription == Constants.Common.SubscriptionId),
                                It.Is<string>(rgName => rgName == Constants.Common.ResourceGroupName),
                                It.Is<string>(connectorName => connectorName == Constants.Connector.ConnectorName1)), Times.Never);
                    }
                }
            }
        }

        [TestMethod("Create an Access Connector with one UA MI in PUT. This should Succeed.")]
        public void CreateConnectorswith1MI()
        {
            using (RequestCorrelationContext.NewCorrelationContextScope())
            {
                //Set up
                SetUpCorrelationContext(Constants.Common.Connector20230501APIVersion);

                this.AccessConnectorDataProvider.Setup(o => o.FindAccessConnector(
                        It.Is<string>(subscription => subscription == Constants.Common.SubscriptionId),
                        It.Is<string>(rgName => rgName == Constants.Common.ResourceGroupName),
                        It.Is<string>(connectorName => connectorName == Constants.Connector.ConnectorName1)))
                    .Returns(Task.FromResult<AccessConnectorEntity>(null));

                this.AccessConnector.Identity = new Identity()
                {
                    Type = IdentityType.UserAssigned,
                    UserAssignedIdentities = new InsensitiveDictionary<UserAssignedIdentity>
                    {
                        { Constants.UserAssignedIdentity.Identity1.ResourceId, new UserAssignedIdentity() }
                    }
                };

                var identitiesCred = new IdentitiesCredential
                {
                    TenantId = Constants.SystemAssignedIdentity.TenantId,
                    ClientId = Constants.SystemAssignedIdentity.PrincipalId
                };

                var userAssignedIdentity1 = new UserAssignedIdentityCredential
                {
                    ResourceId = Constants.UserAssignedIdentity.Identity1.ResourceId,
                    ObjectId = Constants.UserAssignedIdentity.Identity1.ObjectId,
                    ClientId = Constants.UserAssignedIdentity.Identity1.ClientId
                };

                identitiesCred.UserAssignedIdentities = new List<UserAssignedIdentityCredential>()
                { userAssignedIdentity1 };

                this.FrontdoorEngine.Setup(o => o.GetIdentitiesCredentials(
                    It.IsAny<string>(),
                    It.IsAny<CredentialRequest>())).Returns(Task.FromResult<IdentitiesCredential>(identitiesCred));

                this.Request.Headers.Add(RequestCorrelationContext.HeaderResourceIdentityUrl, Constants.SystemAssignedIdentity.DataPlaneUrl);
                this.Request.Headers.Add(RequestCorrelationContext.HeaderResourceIdentityPrincipalid, Constants.SystemAssignedIdentity.PrincipalId);
                this.Request.Headers.Add(ProviderConstants.ManagedIdentity.HeaderHomeTenantId, Constants.SystemAssignedIdentity.TenantId);

                //Test Item
                using (new AppConfigSettingScope(this.ConfigurationSettings))
                {
                    var response = this.AccessConnectorController.PutAccessConnector(
                        Constants.Common.SubscriptionId,
                        Constants.Common.ResourceGroupName,
                        Constants.Connector.ConnectorName1,
                        this.AccessConnector,
                        null, Constants.SystemAssignedIdentity.DataPlaneUrl, Constants.SystemAssignedIdentity.PrincipalId, Constants.SystemAssignedIdentity.TenantId).Result;

                    var responseContent = ConvertResponse(response);

                    //Assert
                    Assert.AreNotEqual(null, responseContent);
                    Assert.AreNotEqual(null, responseContent.Identity);
                    Assert.AreEqual(IdentityType.UserAssigned, responseContent.Identity.Type);

                    Assert.AreNotEqual(null, responseContent.Identity.UserAssignedIdentities);
                    Assert.AreEqual(1, responseContent.Identity.UserAssignedIdentities.Count);
                    Assert.IsTrue(responseContent.Identity.UserAssignedIdentities.ContainsKey(Constants.UserAssignedIdentity.Identity1.ResourceId));

                    Assert.AreEqual(Constants.UserAssignedIdentity.Identity1.ObjectId,
                        responseContent.Identity.UserAssignedIdentities[Constants.UserAssignedIdentity.Identity1.ResourceId].PrincipalId);
                    Assert.AreEqual(Constants.UserAssignedIdentity.Identity1.ClientId,
                        responseContent.Identity.UserAssignedIdentities[Constants.UserAssignedIdentity.Identity1.ResourceId].ClientId);

                    Assert.AreEqual((int)HttpStatusCode.Created, (response as ObjectResult).StatusCode);

                    this.AccessConnectorDataProvider.Verify(
                            o => o.FindAccessConnector(
                                It.Is<string>(subscription => subscription == Constants.Common.SubscriptionId),
                                It.Is<string>(rgName => rgName == Constants.Common.ResourceGroupName),
                                It.Is<string>(connectorName => connectorName == Constants.Connector.ConnectorName1)), Times.Once);

                    this.FrontdoorEngine.Verify(
                        o => o.CallDatabricksAsyncAccountApi(
                        It.IsAny<HttpMethod>(),
                        It.IsAny<Uri>(),
                        It.IsAny<string>(),
                        It.IsAny<string>(),
                        It.IsAny<CredMgrNotification>(),
                        It.IsAny<InsensitiveDictionary<string>>()), Times.Once);

                    this.AccessConnectorDataProvider.Verify(
                        o => o.SaveAccessConnector(
                            It.Is<AccessConnectorEntity>(entity =>
                                entity.Properties.ProvisioningState == AccessConnectorState.Succeeded)));
                }
            }
        }

        [TestMethod("Update Old Connector with two UA MI by replacing with two UA MI in PUT. This should Succeed.")]
        public void UpdateOldConnectorswith2MIwith2MI()
        {
            using (RequestCorrelationContext.NewCorrelationContextScope())
            {
                //Set up
                SetUpCorrelationContext(Constants.Common.Connector20230501APIVersion);

                using (new AppConfigSettingScope(this.ConfigurationSettings))
                {
                    var accessConnectorEntity = new AccessConnectorEntity(
                    Constants.Connector.ConnectorName1,
                    Constants.Common.ResourceGroupName,
                    Constants.Common.SubscriptionId,
                    Constants.Common.Location,
                    systemData: new SystemData(),
                    tags: null)
                    {
                        Identity = new Identity()
                        {
                            Type = IdentityType.UserAssigned,
                            UserAssignedIdentities = new InsensitiveDictionary<UserAssignedIdentity>
                            {
                                { Constants.UserAssignedIdentity.Identity1.ResourceId, new UserAssignedIdentity() },
                                { Constants.UserAssignedIdentity.Identity2.ResourceId, new UserAssignedIdentity() }
                            }
                        },
                        Properties = new AccessConnectorProperties()
                        {
                            ProvisioningState = AccessConnectorState.Succeeded
                        }
                    };

                    this.AccessConnectorDataProvider.Setup(o => o.FindAccessConnector(
                        It.Is<string>(subscription => subscription == Constants.Common.SubscriptionId),
                        It.Is<string>(rgName => rgName == Constants.Common.ResourceGroupName),
                        It.Is<string>(connectorName => connectorName == Constants.Connector.ConnectorName1)))
                    .Returns(Task.FromResult<AccessConnectorEntity>(accessConnectorEntity));

                    this.AccessConnector.Identity = new Identity()
                    {
                        Type = IdentityType.UserAssigned,
                        UserAssignedIdentities = new InsensitiveDictionary<UserAssignedIdentity>
                        {
                            { Constants.UserAssignedIdentity.Identity3.ResourceId, new UserAssignedIdentity() },
                            { Constants.UserAssignedIdentity.Identity4.ResourceId, new UserAssignedIdentity() }
                        }
                    };

                    var identitiesCred = new IdentitiesCredential();

                    var userAssignedIdentity3 = new UserAssignedIdentityCredential
                    {
                        ResourceId = Constants.UserAssignedIdentity.Identity3.ResourceId,
                        ObjectId = Constants.UserAssignedIdentity.Identity3.ObjectId,
                        ClientId = Constants.UserAssignedIdentity.Identity3.ClientId
                    };

                    var userAssignedIdentity4 = new UserAssignedIdentityCredential
                    {
                        ResourceId = Constants.UserAssignedIdentity.Identity4.ResourceId,
                        ObjectId = Constants.UserAssignedIdentity.Identity4.ObjectId,
                        ClientId = Constants.UserAssignedIdentity.Identity4.ClientId
                    };

                    identitiesCred.UserAssignedIdentities = new List<UserAssignedIdentityCredential>()
                    { userAssignedIdentity3, userAssignedIdentity4 };

                    this.FrontdoorEngine.Setup(o => o.GetIdentitiesCredentials(
                        It.IsAny<string>(),
                        It.IsAny<CredentialRequest>())).Returns(Task.FromResult<IdentitiesCredential>(identitiesCred));

                    //Test Item
                    var response = this.AccessConnectorController.PutAccessConnector(
                            Constants.Common.SubscriptionId,
                            Constants.Common.ResourceGroupName,
                            Constants.Connector.ConnectorName1,
                            this.AccessConnector,
                            null, "", "", "").Result;

                    var responseContent = ConvertResponse(response);

                    //Assert
                    this.AccessConnectorDataProvider.Verify(
                    o => o.FindAccessConnector(
                        It.Is<string>(subscription => subscription == Constants.Common.SubscriptionId),
                        It.Is<string>(rgName => rgName == Constants.Common.ResourceGroupName),
                        It.Is<string>(connectorName => connectorName == Constants.Connector.ConnectorName1)), Times.Once);

                    Assert.AreNotEqual(null, responseContent);
                    Assert.AreNotEqual(null, responseContent.Identity);
                    Assert.AreEqual((int)HttpStatusCode.OK, (response as ObjectResult).StatusCode);

                    Assert.AreEqual(responseContent.Identity.Type, IdentityType.UserAssigned);
                    Assert.AreNotEqual(null, responseContent.Identity.UserAssignedIdentities);
                    Assert.AreEqual(2, responseContent.Identity.UserAssignedIdentities.Count);
                    Assert.IsFalse(responseContent.Identity.UserAssignedIdentities.ContainsKey(Constants.UserAssignedIdentity.Identity1.ResourceId));
                    Assert.IsFalse(responseContent.Identity.UserAssignedIdentities.ContainsKey(Constants.UserAssignedIdentity.Identity2.ResourceId));
                    Assert.IsTrue(responseContent.Identity.UserAssignedIdentities.ContainsKey(Constants.UserAssignedIdentity.Identity3.ResourceId));
                    Assert.IsTrue(responseContent.Identity.UserAssignedIdentities.ContainsKey(Constants.UserAssignedIdentity.Identity4.ResourceId));
                }
            }
        }

        [TestMethod("Update Old Connector with two UA MI by replacing with one UA MI in PUT. This should Succeed.")]
        public void UpdateOldConnectorswith2MIwith1MI()
        {
            using (RequestCorrelationContext.NewCorrelationContextScope())
            {
                //Set up
                SetUpCorrelationContext(Constants.Common.Connector20230501APIVersion);

                using (new AppConfigSettingScope(this.ConfigurationSettings))
                {
                    var accessConnectorEntity = new AccessConnectorEntity(
                    Constants.Connector.ConnectorName1,
                    Constants.Common.ResourceGroupName,
                    Constants.Common.SubscriptionId,
                    Constants.Common.Location,
                    systemData: new SystemData(),
                    tags: null)
                    {
                        Identity = new Identity()
                        {
                            Type = IdentityType.UserAssigned,
                            UserAssignedIdentities = new InsensitiveDictionary<UserAssignedIdentity>
                            {
                                { Constants.UserAssignedIdentity.Identity1.ResourceId, new UserAssignedIdentity() },
                                { Constants.UserAssignedIdentity.Identity2.ResourceId, new UserAssignedIdentity() }
                            }
                        },
                        Properties = new AccessConnectorProperties()
                        {
                            ProvisioningState = AccessConnectorState.Succeeded
                        }
                    };

                    var identitiesCred = new IdentitiesCredential
                    {
                        TenantId = Constants.SystemAssignedIdentity.TenantId,
                        ClientId = Constants.SystemAssignedIdentity.PrincipalId
                    };

                    var userAssignedIdentity3 = new UserAssignedIdentityCredential
                    {
                        ResourceId = Constants.UserAssignedIdentity.Identity3.ResourceId,
                        ObjectId = Constants.UserAssignedIdentity.Identity3.ObjectId,
                        ClientId = Constants.UserAssignedIdentity.Identity3.ClientId
                    };

                    identitiesCred.UserAssignedIdentities = new List<UserAssignedIdentityCredential>()
                    { userAssignedIdentity3 };

                    this.FrontdoorEngine.Setup(o => o.GetIdentitiesCredentials(
                        It.IsAny<string>(),
                        It.IsAny<CredentialRequest>())).Returns(Task.FromResult<IdentitiesCredential>(identitiesCred));

                    this.Request.Headers.Add(RequestCorrelationContext.HeaderResourceIdentityUrl, Constants.SystemAssignedIdentity.DataPlaneUrl);
                    this.Request.Headers.Add(RequestCorrelationContext.HeaderResourceIdentityPrincipalid, Constants.SystemAssignedIdentity.PrincipalId);
                    this.Request.Headers.Add(ProviderConstants.ManagedIdentity.HeaderHomeTenantId, Constants.SystemAssignedIdentity.TenantId);

                    this.AccessConnectorDataProvider.Setup(o => o.FindAccessConnector(
                        It.Is<string>(subscription => subscription == Constants.Common.SubscriptionId),
                        It.Is<string>(rgName => rgName == Constants.Common.ResourceGroupName),
                        It.Is<string>(connectorName => connectorName == Constants.Connector.ConnectorName1)))
                    .Returns(Task.FromResult<AccessConnectorEntity>(accessConnectorEntity));

                    this.AccessConnector.Identity = new Identity()
                    {
                        Type = IdentityType.UserAssigned,
                        UserAssignedIdentities = new InsensitiveDictionary<UserAssignedIdentity>
                        {
                            { Constants.UserAssignedIdentity.Identity3.ResourceId, new UserAssignedIdentity() }
                        }
                    };

                    //Test Item
                    var response = this.AccessConnectorController.PutAccessConnector(
                                                Constants.Common.SubscriptionId,
                                                Constants.Common.ResourceGroupName,
                                                Constants.Connector.ConnectorName1,
                                                this.AccessConnector,
                                                null, Constants.SystemAssignedIdentity.DataPlaneUrl, Constants.SystemAssignedIdentity.PrincipalId, Constants.SystemAssignedIdentity.TenantId).Result;

                    var responseContent = ConvertResponse(response);

                    //Assert
                    Assert.AreNotEqual(null, responseContent);
                    Assert.AreNotEqual(null, responseContent.Identity);
                    Assert.AreEqual(IdentityType.UserAssigned, responseContent.Identity.Type);
                    Assert.AreEqual((int)HttpStatusCode.OK, (response as ObjectResult).StatusCode);

                    Assert.AreNotEqual(null, responseContent.Identity.UserAssignedIdentities);
                    Assert.AreEqual(1, responseContent.Identity.UserAssignedIdentities.Count);
                    Assert.IsTrue(responseContent.Identity.UserAssignedIdentities.ContainsKey(Constants.UserAssignedIdentity.Identity3.ResourceId));

                    Assert.AreEqual(Constants.UserAssignedIdentity.Identity3.ObjectId,
                        responseContent.Identity.UserAssignedIdentities[Constants.UserAssignedIdentity.Identity3.ResourceId].PrincipalId);
                    Assert.AreEqual(Constants.UserAssignedIdentity.Identity3.ClientId,
                        responseContent.Identity.UserAssignedIdentities[Constants.UserAssignedIdentity.Identity3.ResourceId].ClientId);

                    this.AccessConnectorDataProvider.Verify(
                        o => o.FindAccessConnector(
                            It.Is<string>(subscription => subscription == Constants.Common.SubscriptionId),
                            It.Is<string>(rgName => rgName == Constants.Common.ResourceGroupName),
                            It.Is<string>(connectorName => connectorName == Constants.Connector.ConnectorName1)), Times.Once);

                    this.FrontdoorEngine.Verify(
                        o => o.CallDatabricksAsyncAccountApi(
                        It.IsAny<HttpMethod>(),
                        It.IsAny<Uri>(),
                        It.IsAny<string>(),
                        It.IsAny<string>(),
                        It.IsAny<CredMgrNotification>(),
                        It.IsAny<InsensitiveDictionary<string>>()), Times.Once);

                    this.AccessConnectorDataProvider.Verify(
                        o => o.SaveAccessConnector(
                            It.Is<AccessConnectorEntity>(entity =>
                                entity.Properties.ProvisioningState == AccessConnectorState.Succeeded)));
                }
            }
        }

        [TestMethod("Update old Access Connector with One UA MI with two UA MI in PUT. This should Succeed.")]
        public void UpdateOldConnectorswith1MIwith2MI()
        {
            using (RequestCorrelationContext.NewCorrelationContextScope())
            {
                //Set up
                SetUpCorrelationContext(Constants.Common.Connector20230501APIVersion);

                using (new AppConfigSettingScope(this.ConfigurationSettings))
                {
                    var accessConnectorEntity = new AccessConnectorEntity(
                    Constants.Connector.ConnectorName1,
                    Constants.Common.ResourceGroupName,
                    Constants.Common.SubscriptionId,
                    Constants.Common.Location,
                    systemData: new SystemData(),
                    tags: null)
                    {
                        Identity = new Identity()
                        {
                            Type = IdentityType.UserAssigned,
                            UserAssignedIdentities = new InsensitiveDictionary<UserAssignedIdentity>
                            {
                                { Constants.UserAssignedIdentity.Identity1.ResourceId, new UserAssignedIdentity() }
                            }
                        },
                        Properties = new AccessConnectorProperties()
                        {
                            ProvisioningState = AccessConnectorState.Succeeded
                        }
                    };

                    var identitiesCred = new IdentitiesCredential
                    {
                        TenantId = Constants.SystemAssignedIdentity.TenantId,
                        ClientId = Constants.SystemAssignedIdentity.PrincipalId
                    };

                    var userAssignedIdentity2 = new UserAssignedIdentityCredential
                    {
                        ResourceId = Constants.UserAssignedIdentity.Identity2.ResourceId,
                        ObjectId = Constants.UserAssignedIdentity.Identity2.ObjectId,
                        ClientId = Constants.UserAssignedIdentity.Identity2.ClientId
                    };


                    var userAssignedIdentity3 = new UserAssignedIdentityCredential
                    {
                        ResourceId = Constants.UserAssignedIdentity.Identity3.ResourceId,
                        ObjectId = Constants.UserAssignedIdentity.Identity3.ObjectId,
                        ClientId = Constants.UserAssignedIdentity.Identity3.ClientId
                    };

                    identitiesCred.UserAssignedIdentities = new List<UserAssignedIdentityCredential>()
                    { userAssignedIdentity2, userAssignedIdentity3 };

                    this.FrontdoorEngine.Setup(o => o.GetIdentitiesCredentials(
                        It.IsAny<string>(),
                        It.IsAny<CredentialRequest>())).Returns(Task.FromResult<IdentitiesCredential>(identitiesCred));

                    this.Request.Headers.Add(RequestCorrelationContext.HeaderResourceIdentityUrl, Constants.SystemAssignedIdentity.DataPlaneUrl);
                    this.Request.Headers.Add(RequestCorrelationContext.HeaderResourceIdentityPrincipalid, Constants.SystemAssignedIdentity.PrincipalId);
                    this.Request.Headers.Add(ProviderConstants.ManagedIdentity.HeaderHomeTenantId, Constants.SystemAssignedIdentity.TenantId);


                    this.AccessConnectorDataProvider.Setup(o => o.FindAccessConnector(
                        It.Is<string>(subscription => subscription == Constants.Common.SubscriptionId),
                        It.Is<string>(rgName => rgName == Constants.Common.ResourceGroupName),
                        It.Is<string>(connectorName => connectorName == Constants.Connector.ConnectorName1)))
                    .Returns(Task.FromResult<AccessConnectorEntity>(accessConnectorEntity));

                    this.AccessConnector.Identity = new Identity()
                    {
                        Type = IdentityType.UserAssigned,
                        UserAssignedIdentities = new InsensitiveDictionary<UserAssignedIdentity>
                        {
                            {   Constants.UserAssignedIdentity.Identity2.ResourceId,new UserAssignedIdentity() },
                            {   Constants.UserAssignedIdentity.Identity3.ResourceId,new UserAssignedIdentity() }
                        }
                    };

                    //Test Item
                    var response = this.AccessConnectorController.PutAccessConnector(
                            Constants.Common.SubscriptionId,
                            Constants.Common.ResourceGroupName,
                            Constants.Connector.ConnectorName1,
                            this.AccessConnector,
                            null, Constants.SystemAssignedIdentity.DataPlaneUrl, Constants.SystemAssignedIdentity.PrincipalId, Constants.SystemAssignedIdentity.TenantId).Result;

                    var responseContent = ConvertResponse(response);

                    //Assert
                    this.AccessConnectorDataProvider.Verify(
                    o => o.FindAccessConnector(
                        It.Is<string>(subscription => subscription == Constants.Common.SubscriptionId),
                        It.Is<string>(rgName => rgName == Constants.Common.ResourceGroupName),
                        It.Is<string>(connectorName => connectorName == Constants.Connector.ConnectorName1)), Times.Once);

                    Assert.AreNotEqual(null, responseContent);
                    Assert.AreNotEqual(null, responseContent.Identity);
                    Assert.AreEqual((int)HttpStatusCode.OK, (response as ObjectResult).StatusCode);

                    Assert.AreEqual(responseContent.Identity.Type, IdentityType.UserAssigned);
                    Assert.AreNotEqual(null, responseContent.Identity.UserAssignedIdentities);
                    Assert.AreEqual(2, responseContent.Identity.UserAssignedIdentities.Count);
                    Assert.IsFalse(responseContent.Identity.UserAssignedIdentities.ContainsKey(Constants.UserAssignedIdentity.Identity1.ResourceId));
                    Assert.IsTrue(responseContent.Identity.UserAssignedIdentities.ContainsKey(Constants.UserAssignedIdentity.Identity2.ResourceId));
                    Assert.IsTrue(responseContent.Identity.UserAssignedIdentities.ContainsKey(Constants.UserAssignedIdentity.Identity3.ResourceId));
                }
            }
        }

        [TestMethod("Update old Access Connector with One UA MI with one UA MI in PUT. This should Succeed.")]
        public void UpdateOldConnectorswith1MIwith1MI()
        {
            using (RequestCorrelationContext.NewCorrelationContextScope())
            {
                //Set up
                SetUpCorrelationContext(Constants.Common.Connector20230501APIVersion);

                using (new AppConfigSettingScope(this.ConfigurationSettings))
                {
                    var accessConnectorEntity = new AccessConnectorEntity(
                    Constants.Connector.ConnectorName1,
                    Constants.Common.ResourceGroupName,
                    Constants.Common.SubscriptionId,
                    Constants.Common.Location,
                    systemData: new SystemData(),
                    tags: null)
                    {
                        Identity = new Identity()
                        {
                            Type = IdentityType.UserAssigned,
                            UserAssignedIdentities = new InsensitiveDictionary<UserAssignedIdentity>
                            {
                                { Constants.UserAssignedIdentity.Identity1.ResourceId, new UserAssignedIdentity() }
                            }
                        },
                        Properties = new AccessConnectorProperties()
                        {
                            ProvisioningState = AccessConnectorState.Succeeded
                        }
                    };

                    var identitiesCred = new IdentitiesCredential
                    {
                        TenantId = Constants.SystemAssignedIdentity.TenantId,
                        ClientId = Constants.SystemAssignedIdentity.PrincipalId
                    };

                    var userAssignedIdentity2 = new UserAssignedIdentityCredential
                    {
                        ResourceId = Constants.UserAssignedIdentity.Identity2.ResourceId,
                        ObjectId = Constants.UserAssignedIdentity.Identity2.ObjectId,
                        ClientId = Constants.UserAssignedIdentity.Identity2.ClientId
                    };

                    identitiesCred.UserAssignedIdentities = new List<UserAssignedIdentityCredential>()
                    { userAssignedIdentity2 };

                    this.FrontdoorEngine.Setup(o => o.GetIdentitiesCredentials(
                        It.IsAny<string>(),
                        It.IsAny<CredentialRequest>())).Returns(Task.FromResult<IdentitiesCredential>(identitiesCred));

                    this.Request.Headers.Add(RequestCorrelationContext.HeaderResourceIdentityUrl, Constants.SystemAssignedIdentity.DataPlaneUrl);
                    this.Request.Headers.Add(RequestCorrelationContext.HeaderResourceIdentityPrincipalid, Constants.SystemAssignedIdentity.PrincipalId);
                    this.Request.Headers.Add(ProviderConstants.ManagedIdentity.HeaderHomeTenantId, Constants.SystemAssignedIdentity.TenantId);


                    this.AccessConnectorDataProvider.Setup(o => o.FindAccessConnector(
                        It.Is<string>(subscription => subscription == Constants.Common.SubscriptionId),
                        It.Is<string>(rgName => rgName == Constants.Common.ResourceGroupName),
                        It.Is<string>(connectorName => connectorName == Constants.Connector.ConnectorName1)))
                    .Returns(Task.FromResult<AccessConnectorEntity>(accessConnectorEntity));

                    this.AccessConnector.Identity = new Identity()
                    {
                        Type = IdentityType.UserAssigned,
                        UserAssignedIdentities = new InsensitiveDictionary<UserAssignedIdentity>
                        {
                            { Constants.UserAssignedIdentity.Identity2.ResourceId,new UserAssignedIdentity() }
                        }
                    };

                    //Test Item
                    var response = this.AccessConnectorController.PutAccessConnector(
                        Constants.Common.SubscriptionId,
                        Constants.Common.ResourceGroupName,
                        Constants.Connector.ConnectorName1,
                        this.AccessConnector,
                        null, Constants.SystemAssignedIdentity.DataPlaneUrl, Constants.SystemAssignedIdentity.PrincipalId, Constants.SystemAssignedIdentity.TenantId).Result;

                    var responseContent = ConvertResponse(response);

                    this.AccessConnectorDataProvider.Verify(
                        o => o.FindAccessConnector(
                            It.Is<string>(subscription => subscription == Constants.Common.SubscriptionId),
                            It.Is<string>(rgName => rgName == Constants.Common.ResourceGroupName),
                            It.Is<string>(connectorName => connectorName == Constants.Connector.ConnectorName1)), Times.Once);

                    this.FrontdoorEngine.Verify(
                        o => o.CallDatabricksAsyncAccountApi(
                            It.IsAny<HttpMethod>(),
                            It.IsAny<Uri>(),
                            It.IsAny<string>(),
                            It.IsAny<string>(),
                            It.IsAny<CredMgrNotification>(),
                            It.IsAny<InsensitiveDictionary<string>>()), Times.Once);

                    this.AccessConnectorDataProvider.Verify(
                            o => o.SaveAccessConnector(
                                It.Is<AccessConnectorEntity>(entity =>
                                    entity.Properties.ProvisioningState == AccessConnectorState.Succeeded)));

                    //Assert
                    Assert.AreNotEqual(null, responseContent);
                    Assert.AreNotEqual(null, responseContent.Identity);
                    Assert.AreEqual(IdentityType.UserAssigned, responseContent.Identity.Type);
                    Assert.AreEqual((int)HttpStatusCode.OK, (response as ObjectResult).StatusCode);

                    Assert.AreNotEqual(null, responseContent.Identity.UserAssignedIdentities);
                    Assert.AreEqual(1, responseContent.Identity.UserAssignedIdentities.Count);
                    Assert.IsTrue(responseContent.Identity.UserAssignedIdentities.ContainsKey(Constants.UserAssignedIdentity.Identity2.ResourceId));

                    Assert.AreEqual(Constants.UserAssignedIdentity.Identity2.ObjectId,
                        responseContent.Identity.UserAssignedIdentities[Constants.UserAssignedIdentity.Identity2.ResourceId].PrincipalId);
                    Assert.AreEqual(Constants.UserAssignedIdentity.Identity2.ClientId,
                        responseContent.Identity.UserAssignedIdentities[Constants.UserAssignedIdentity.Identity2.ResourceId].ClientId);
                }
            }
        }

        [TestMethod("Creating UA MI with 2022-04-01-preview apiversion")]
        public void CreatingAccessConnectorWitholdApiwithPutCall()
        {
            using (RequestCorrelationContext.NewCorrelationContextScope())
            {
                //Set up
                SetUpCorrelationContext(Constants.Common.ConnectorUserAssignedOldApiVersion);

                this.AccessConnectorDataProvider.Setup(o => o.FindAccessConnector(
                        It.Is<string>(subscription => subscription == Constants.Common.SubscriptionId),
                        It.Is<string>(rgName => rgName == Constants.Common.ResourceGroupName),
                        It.Is<string>(connectorName => connectorName == Constants.Connector.ConnectorName1)))
                    .Returns(Task.FromResult<AccessConnectorEntity>(null));

                this.AccessConnector.Identity = new Identity()
                {
                    Type = IdentityType.UserAssigned,
                    UserAssignedIdentities = new InsensitiveDictionary<UserAssignedIdentity>
                    {
                        { Constants.UserAssignedIdentity.Identity1.ResourceId, new UserAssignedIdentity() }
                    }
                };

                var identitiesCred = new IdentitiesCredential
                {
                    TenantId = Constants.SystemAssignedIdentity.TenantId,
                    ClientId = Constants.SystemAssignedIdentity.PrincipalId
                };

                var userAssignedIdentity1 = new UserAssignedIdentityCredential
                {
                    ResourceId = Constants.UserAssignedIdentity.Identity1.ResourceId,
                    ObjectId = Constants.UserAssignedIdentity.Identity1.ObjectId,
                    ClientId = Constants.UserAssignedIdentity.Identity1.ClientId
                };

                identitiesCred.UserAssignedIdentities = new List<UserAssignedIdentityCredential>()
                { userAssignedIdentity1 };

                this.FrontdoorEngine.Setup(o => o.GetIdentitiesCredentials(
                    It.IsAny<string>(),
                    It.IsAny<CredentialRequest>())).Returns(Task.FromResult<IdentitiesCredential>(identitiesCred));

                this.Request.Headers.Add(RequestCorrelationContext.HeaderResourceIdentityUrl, Constants.SystemAssignedIdentity.DataPlaneUrl);
                this.Request.Headers.Add(RequestCorrelationContext.HeaderResourceIdentityPrincipalid, Constants.SystemAssignedIdentity.PrincipalId);
                this.Request.Headers.Add(ProviderConstants.ManagedIdentity.HeaderHomeTenantId, Constants.SystemAssignedIdentity.TenantId);


                //Test Item
                try
                {
                    var response = this.AccessConnectorController.PutAccessConnector(
                        Constants.Common.SubscriptionId,
                        Constants.Common.ResourceGroupName,
                        Constants.Connector.ConnectorName1,
                        this.AccessConnector,
                        null, Constants.SystemAssignedIdentity.DataPlaneUrl, Constants.SystemAssignedIdentity.PrincipalId, Constants.SystemAssignedIdentity.TenantId).Result;
                }
                catch (Exception ex)
                {
                    Assert.AreEqual(ex.InnerException.Message, ErrorResponseMessages.UserAssignedIdentityNotSupportedForCurrentApiVersion.ToLocalizedMessage());
                }
                finally
                {
                    //Assert
                    this.AccessConnectorDataProvider.Verify(
                        o => o.FindAccessConnector(
                            It.Is<string>(subscription => subscription == Constants.Common.SubscriptionId),
                            It.Is<string>(rgName => rgName == Constants.Common.ResourceGroupName),
                            It.Is<string>(connectorName => connectorName == Constants.Connector.ConnectorName1)), Times.Never);

                    this.FrontdoorEngine.Verify(
                        o => o.CallDatabricksAsyncAccountApi(
                        It.IsAny<HttpMethod>(),
                        It.IsAny<Uri>(),
                        It.IsAny<string>(),
                        It.IsAny<string>(),
                        It.IsAny<CredMgrNotification>(),
                        It.IsAny<InsensitiveDictionary<string>>()), Times.Never);

                    this.AccessConnectorDataProvider.Verify(
                        o => o.SaveAccessConnector(
                            It.IsAny<AccessConnectorEntity>()), Times.Never);
                }
            }
        }

        [TestMethod("Updating UA MI with 2022-04-01-preview apiversion")]
        public void UpdatingAccessConnectorWitholdApiwithPutCall()
        {
            using (RequestCorrelationContext.NewCorrelationContextScope())
            {
                //Set up
                SetUpCorrelationContext(Constants.Common.ConnectorUserAssignedOldApiVersion);

                using (new AppConfigSettingScope(this.ConfigurationSettings))
                {
                    var accessConnectorEntity = new AccessConnectorEntity(
                    Constants.Connector.ConnectorName1,
                    Constants.Common.ResourceGroupName,
                    Constants.Common.SubscriptionId,
                    Constants.Common.Location,
                    systemData: new SystemData(),
                    tags: null)
                    {
                        Identity = new Identity()
                        {
                            Type = IdentityType.UserAssigned,
                            UserAssignedIdentities = new InsensitiveDictionary<UserAssignedIdentity>
                            {
                                { Constants.UserAssignedIdentity.Identity1.ResourceId, new UserAssignedIdentity() }
                            }
                        },
                        Properties = new AccessConnectorProperties()
                        {
                            ProvisioningState = AccessConnectorState.Succeeded
                        }
                    };

                    var identitiesCred = new IdentitiesCredential
                    {
                        TenantId = Constants.SystemAssignedIdentity.TenantId,
                        ClientId = Constants.SystemAssignedIdentity.PrincipalId
                    };

                    var userAssignedIdentity2 = new UserAssignedIdentityCredential
                    {
                        ResourceId = Constants.UserAssignedIdentity.Identity2.ResourceId,
                        ObjectId = Constants.UserAssignedIdentity.Identity2.ObjectId,
                        ClientId = Constants.UserAssignedIdentity.Identity2.ClientId
                    };

                    identitiesCred.UserAssignedIdentities = new List<UserAssignedIdentityCredential>()
                    { userAssignedIdentity2 };

                    this.FrontdoorEngine.Setup(o => o.GetIdentitiesCredentials(
                        It.IsAny<string>(),
                        It.IsAny<CredentialRequest>())).Returns(Task.FromResult<IdentitiesCredential>(identitiesCred));

                    this.Request.Headers.Add(RequestCorrelationContext.HeaderResourceIdentityUrl, Constants.SystemAssignedIdentity.DataPlaneUrl);
                    this.Request.Headers.Add(RequestCorrelationContext.HeaderResourceIdentityPrincipalid, Constants.SystemAssignedIdentity.PrincipalId);
                    this.Request.Headers.Add(ProviderConstants.ManagedIdentity.HeaderHomeTenantId, Constants.SystemAssignedIdentity.TenantId);


                    this.AccessConnectorDataProvider.Setup(o => o.FindAccessConnector(
                        It.Is<string>(subscription => subscription == Constants.Common.SubscriptionId),
                        It.Is<string>(rgName => rgName == Constants.Common.ResourceGroupName),
                        It.Is<string>(connectorName => connectorName == Constants.Connector.ConnectorName1)))
                    .Returns(Task.FromResult<AccessConnectorEntity>(accessConnectorEntity));

                    this.AccessConnector.Identity = new Identity()
                    {
                        Type = IdentityType.UserAssigned,
                        UserAssignedIdentities = new InsensitiveDictionary<UserAssignedIdentity>
                        {
                            { Constants.UserAssignedIdentity.Identity2.ResourceId,new UserAssignedIdentity() }
                        }
                    };

                    //Test Item
                    try
                    {
                        var response = this.AccessConnectorController.PutAccessConnector(
                            Constants.Common.SubscriptionId,
                            Constants.Common.ResourceGroupName,
                            Constants.Connector.ConnectorName1,
                            this.AccessConnector,
                            null, Constants.SystemAssignedIdentity.DataPlaneUrl, Constants.SystemAssignedIdentity.PrincipalId, Constants.SystemAssignedIdentity.TenantId).Result;
                    }
                    catch (Exception ex)
                    {
                        Assert.AreEqual(ex.InnerException.Message, ErrorResponseMessages.UserAssignedIdentityNotSupportedForCurrentApiVersion.ToLocalizedMessage());
                    }
                    finally
                    {
                        //Assert
                        this.AccessConnectorDataProvider.Verify(
                            o => o.FindAccessConnector(
                                It.Is<string>(subscription => subscription == Constants.Common.SubscriptionId),
                                It.Is<string>(rgName => rgName == Constants.Common.ResourceGroupName),
                                It.Is<string>(connectorName => connectorName == Constants.Connector.ConnectorName1)), Times.Never);

                        this.FrontdoorEngine.Verify(
                            o => o.CallDatabricksAsyncAccountApi(
                            It.IsAny<HttpMethod>(),
                            It.IsAny<Uri>(),
                            It.IsAny<string>(),
                            It.IsAny<string>(),
                            It.IsAny<CredMgrNotification>(),
                            It.IsAny<InsensitiveDictionary<string>>()), Times.Never);

                        this.AccessConnectorDataProvider.Verify(
                            o => o.SaveAccessConnector(
                                It.IsAny<AccessConnectorEntity>()), Times.Never);
                    }
                }
            }
        }

        #endregion

        #region Patch accessConnector Tests

        [TestMethod("Patches accessConnector with tags")]
        public void UpdateAccessConnectorWithTagsViaPatch()
        {
            //Set up
            var accessConnectorEntity = new AccessConnectorEntity(
                Constants.Connector.ConnectorName1,
                Constants.Common.ResourceGroupName,
                Constants.Common.SubscriptionId,
                Constants.Common.Location,
                tags: null,
                identity: null)
            {
                Properties = new AccessConnectorProperties()
                {
                    ProvisioningState = AccessConnectorState.Succeeded
                }
            };

            this.AccessConnectorDataProvider.Setup(o => o.FindAccessConnector(
                    It.Is<string>(subscription => subscription == Constants.Common.SubscriptionId),
                    It.Is<string>(rgName => rgName == Constants.Common.ResourceGroupName),
                    It.Is<string>(connectorName => connectorName == Constants.Connector.ConnectorName1)))
                .Returns(Task.FromResult<AccessConnectorEntity>(accessConnectorEntity));

            this.AccessConnectorUpdate.Tags = new InsensitiveDictionary<string>() { { Constants.Common.Tag1, Constants.Common.Value1 } };

            //Test Item
            var response = this.AccessConnectorController.PatchAccessConnector(
                Constants.Common.SubscriptionId,
                Constants.Common.ResourceGroupName,
                Constants.Connector.ConnectorName1,
                this.AccessConnectorUpdate,
                null, "", "", "").Result;

            var responseContent = ConvertResponse(response);

            //Assert
            Assert.AreNotEqual(null, responseContent.Tags);
            Assert.AreEqual(1, responseContent.Tags.Count);
            Assert.IsTrue(responseContent.Tags.Keys.Contains(Constants.Common.Tag1));
        }

        [TestMethod("Patches accessConnector with SystemAssigned Identity only")]
        public void UpdateAccessConnectorWithSystemAssignedIdentityViaPatch()
        {
            //Set up
            var accessConnectorEntity = new AccessConnectorEntity(
                Constants.Connector.ConnectorName1,
                Constants.Common.ResourceGroupName,
                Constants.Common.SubscriptionId,
                Constants.Common.Location,
                tags: null,
                identity: null)
            {
                Properties = new AccessConnectorProperties()
                {
                    ProvisioningState = AccessConnectorState.Succeeded
                }
            };

            this.AccessConnectorDataProvider.Setup(o => o.FindAccessConnector(
                    It.Is<string>(subscription => subscription == Constants.Common.SubscriptionId),
                    It.Is<string>(rgName => rgName == Constants.Common.ResourceGroupName),
                    It.Is<string>(connectorName => connectorName == Constants.Connector.ConnectorName1)))
                .Returns(Task.FromResult<AccessConnectorEntity>(accessConnectorEntity));

            this.AccessConnectorUpdate.Identity = new Identity() { Type = IdentityType.SystemAssigned };
            this.Request.Headers.Add(RequestCorrelationContext.HeaderResourceIdentityUrl, Constants.SystemAssignedIdentity.DataPlaneUrl);
            this.Request.Headers.Add(RequestCorrelationContext.HeaderResourceIdentityPrincipalid, Constants.SystemAssignedIdentity.PrincipalId);
            this.Request.Headers.Add(ProviderConstants.ManagedIdentity.HeaderHomeTenantId, Constants.SystemAssignedIdentity.TenantId);

            //Test Item
            var response = this.AccessConnectorController.PatchAccessConnector(
                Constants.Common.SubscriptionId,
                Constants.Common.ResourceGroupName,
                Constants.Connector.ConnectorName1,
                this.AccessConnectorUpdate,
                null, Constants.SystemAssignedIdentity.DataPlaneUrl, Constants.SystemAssignedIdentity.PrincipalId, Constants.SystemAssignedIdentity.TenantId).Result;

            var responseContent = ConvertResponse(response);

            //Assert
            Assert.AreNotEqual(null, responseContent);
            Assert.AreNotEqual(null, responseContent.Identity);
            Assert.AreEqual(IdentityType.SystemAssigned, responseContent.Identity.Type);
            Assert.AreEqual(Constants.SystemAssignedIdentity.PrincipalId, responseContent.Identity.PrincipalId);
            Assert.AreEqual(Constants.SystemAssignedIdentity.TenantId, responseContent.Identity.TenantId);
        }

        [TestMethod("Patches accessConnector with SystemAssigned Identity and verify it preserves existing tags")]
        public void PatchOperationWithoutShouldPreserveExistingTags()
        {
            //Set up
            var tags = new InsensitiveDictionary<string>() { { Constants.Common.Tag1, Constants.Common.Value1 } };

            var accessConnectorEntity = new AccessConnectorEntity(
                Constants.Connector.ConnectorName1,
                Constants.Common.ResourceGroupName,
                Constants.Common.SubscriptionId,
                Constants.Common.Location,
                tags: tags,
                identity: null)
            {
                Properties = new AccessConnectorProperties()
                {
                    ProvisioningState = AccessConnectorState.Succeeded
                }
            };

            this.AccessConnectorDataProvider.Setup(o => o.FindAccessConnector(
                    It.Is<string>(subscription => subscription == Constants.Common.SubscriptionId),
                    It.Is<string>(rgName => rgName == Constants.Common.ResourceGroupName),
                    It.Is<string>(connectorName => connectorName == Constants.Connector.ConnectorName1)))
                .Returns(Task.FromResult<AccessConnectorEntity>(accessConnectorEntity));

            this.AccessConnectorUpdate.Identity = new Identity() { Type = IdentityType.SystemAssigned };
            this.Request.Headers.Add(RequestCorrelationContext.HeaderResourceIdentityUrl, Constants.SystemAssignedIdentity.DataPlaneUrl);
            this.Request.Headers.Add(RequestCorrelationContext.HeaderResourceIdentityPrincipalid, Constants.SystemAssignedIdentity.PrincipalId);
            this.Request.Headers.Add(ProviderConstants.ManagedIdentity.HeaderHomeTenantId, Constants.SystemAssignedIdentity.TenantId);

            //Test Item
            var response = this.AccessConnectorController.PatchAccessConnector(
                Constants.Common.SubscriptionId,
                Constants.Common.ResourceGroupName,
                Constants.Connector.ConnectorName1,
                this.AccessConnectorUpdate,
                null, Constants.SystemAssignedIdentity.DataPlaneUrl, Constants.SystemAssignedIdentity.PrincipalId, Constants.SystemAssignedIdentity.TenantId).Result;

            var responseContent = ConvertResponse(response);

            //Assert
            Assert.AreNotEqual(null, responseContent.Tags);
            Assert.AreEqual(1, responseContent.Tags.Count);
            Assert.IsTrue(responseContent.Tags.Keys.Contains(Constants.Common.Tag1));
        }

        [TestMethod("Update accessConnector with UserAssigned and SystemAssigned identity with Patch")]
        public void PatchAccessConnectorWithUserAssignedAndSystemAssignedIdentity()
        {
            using (RequestCorrelationContext.NewCorrelationContextScope())
            {
                //Set up
                SetUpCorrelationContext(Constants.Common.Connector20230501APIVersion);

                var tags = new InsensitiveDictionary<string>() { { Constants.Common.Tag1, Constants.Common.Value1 } };

                var accessConnectorEntity = new AccessConnectorEntity(
                   Constants.Connector.ConnectorName1,
                   Constants.Common.ResourceGroupName,
                   Constants.Common.SubscriptionId,
                   Constants.Common.Location,
                   tags: null)
                {
                    Identity = new Identity()
                    {
                        Type = IdentityType.SystemAssigned | IdentityType.UserAssigned,
                        PrincipalId = Constants.SystemAssignedIdentity.PrincipalId,
                        TenantId = Constants.SystemAssignedIdentity.TenantId,
                        UserAssignedIdentities = new InsensitiveDictionary<UserAssignedIdentity>
                            {
                                { Constants.UserAssignedIdentity.Identity1.ResourceId, new UserAssignedIdentity() }
                            }
                    },
                    IdentityUrl = Constants.SystemAssignedIdentity.DataPlaneUrl,
                    Properties = new AccessConnectorProperties()
                    {
                        ProvisioningState = AccessConnectorState.Failed
                    }
                };

                this.AccessConnectorDataProvider.Setup(o => o.FindAccessConnector(
                        It.Is<string>(subscription => subscription == Constants.Common.SubscriptionId),
                        It.Is<string>(rgName => rgName == Constants.Common.ResourceGroupName),
                        It.Is<string>(connectorName => connectorName == Constants.Connector.ConnectorName1)))
                    .Returns(Task.FromResult<AccessConnectorEntity>(accessConnectorEntity));

                this.AccessConnectorUpdate.Identity = new Identity()
                {
                    Type = IdentityType.SystemAssigned | IdentityType.UserAssigned,
                    UserAssignedIdentities = new InsensitiveDictionary<UserAssignedIdentity>
                    {
                        { Constants.UserAssignedIdentity.Identity1.ResourceId, new UserAssignedIdentity() }
                    }
                };

                var identitiesCred = new IdentitiesCredential
                {
                    TenantId = Constants.SystemAssignedIdentity.TenantId,
                    ClientId = Constants.SystemAssignedIdentity.PrincipalId
                };

                var userAssignedIdentity1 = new UserAssignedIdentityCredential
                {
                    ResourceId = Constants.UserAssignedIdentity.Identity1.ResourceId,
                    ObjectId = Constants.UserAssignedIdentity.Identity1.ObjectId,
                    ClientId = Constants.UserAssignedIdentity.Identity1.ClientId
                };

                identitiesCred.UserAssignedIdentities = new List<UserAssignedIdentityCredential>()
                { userAssignedIdentity1 };

                this.FrontdoorEngine.Setup(o => o.GetIdentitiesCredentials(
                    It.IsAny<string>(),
                    It.IsAny<CredentialRequest>())).Returns(Task.FromResult<IdentitiesCredential>(identitiesCred));

                this.Request.Headers.Add(RequestCorrelationContext.HeaderResourceIdentityUrl, Constants.SystemAssignedIdentity.DataPlaneUrl);
                this.Request.Headers.Add(RequestCorrelationContext.HeaderResourceIdentityPrincipalid, Constants.SystemAssignedIdentity.PrincipalId);
                this.Request.Headers.Add(ProviderConstants.ManagedIdentity.HeaderHomeTenantId, Constants.SystemAssignedIdentity.TenantId);

                //Test Item
                using (new AppConfigSettingScope(this.ConfigurationSettings))
                {
                    var response = this.AccessConnectorController.PatchAccessConnector(
                            Constants.Common.SubscriptionId,
                            Constants.Common.ResourceGroupName,
                            Constants.Connector.ConnectorName1,
                            this.AccessConnectorUpdate,
                            null, Constants.SystemAssignedIdentity.DataPlaneUrl, Constants.SystemAssignedIdentity.PrincipalId, Constants.SystemAssignedIdentity.TenantId).Result;

                    this.AccessConnectorDataProvider.Verify(o => o.FindAccessConnector(
                    It.Is<string>(subscription => subscription == Constants.Common.SubscriptionId),
                    It.Is<string>(rgName => rgName == Constants.Common.ResourceGroupName),
                    It.Is<string>(connectorName => connectorName == Constants.Connector.ConnectorName1)), Times.Once);

                    var responseContent = ConvertResponse(response);

                    //Assert
                    Assert.AreNotEqual(null, responseContent);
                    Assert.AreNotEqual(null, responseContent.Identity);
                    Assert.AreEqual((int)HttpStatusCode.OK, (response as ObjectResult).StatusCode);

                    Assert.AreEqual(responseContent.Identity.Type, IdentityType.SystemAssigned | IdentityType.UserAssigned);
                    Assert.AreEqual(Constants.SystemAssignedIdentity.PrincipalId, responseContent.Identity.PrincipalId);
                    Assert.AreEqual(Constants.SystemAssignedIdentity.TenantId, responseContent.Identity.TenantId);
                    Assert.AreNotEqual(null, responseContent.Identity.UserAssignedIdentities);
                    Assert.AreEqual(1, responseContent.Identity.UserAssignedIdentities.Count);
                    Assert.IsTrue(responseContent.Identity.UserAssignedIdentities.ContainsKey(Constants.UserAssignedIdentity.Identity1.ResourceId));
                }
            }
        }

        [TestMethod("Patches accessConnector with systemData")]
        public void UpdateAccessConnectorWithSystemDataViaPatch()
        {
            //Set up
            var accessConnectorEntity = new AccessConnectorEntity(
                Constants.Connector.ConnectorName1,
                Constants.Common.ResourceGroupName,
                Constants.Common.SubscriptionId,
                Constants.Common.Location,
                tags: null,
                identity: null)
            {
                Properties = new AccessConnectorProperties()
                {
                    ProvisioningState = AccessConnectorState.Succeeded
                }
            };

            this.AccessConnectorDataProvider.Setup(o => o.FindAccessConnector(
                    It.Is<string>(subscription => subscription == Constants.Common.SubscriptionId),
                    It.Is<string>(rgName => rgName == Constants.Common.ResourceGroupName),
                    It.Is<string>(connectorName => connectorName == Constants.Connector.ConnectorName1)))
                .Returns(Task.FromResult<AccessConnectorEntity>(accessConnectorEntity));

            this.AccessConnectorUpdate.Tags = new InsensitiveDictionary<string>() { { Constants.Common.Tag1, Constants.Common.Value1 } };

            //Test Item
            var response = this.AccessConnectorController.PatchAccessConnector(
                Constants.Common.SubscriptionId,
                Constants.Common.ResourceGroupName,
                Constants.Connector.ConnectorName1,
                this.AccessConnectorUpdate,
                Constants.Common.SystemDataJson, "", "", "").Result;

            var responseContent = ConvertResponse(response);

            //Assert
            Assert.IsTrue(responseContent.SystemData.ToJson().Equals(
                SystemDataExtension.GetSystemData(Constants.Common.SystemDataJson).ToJson()));
        }

        [TestMethod("Patches accessConnector with systemData which can't be serialized")]
        public void UpdateAccessConnectorWithSystemDataViaPatchWhichCantBeSerialized()
        {
            //Set up
            var accessConnectorEntity = new AccessConnectorEntity(
                Constants.Connector.ConnectorName1,
                Constants.Common.ResourceGroupName,
                Constants.Common.SubscriptionId,
                Constants.Common.Location,
                tags: null,
                identity: null)
            {
                Properties = new AccessConnectorProperties()
                {
                    ProvisioningState = AccessConnectorState.Succeeded
                }
            };

            this.AccessConnectorDataProvider.Setup(o => o.FindAccessConnector(
                    It.Is<string>(subscription => subscription == Constants.Common.SubscriptionId),
                    It.Is<string>(rgName => rgName == Constants.Common.ResourceGroupName),
                    It.Is<string>(connectorName => connectorName == Constants.Connector.ConnectorName1)))
                .Returns(Task.FromResult<AccessConnectorEntity>(accessConnectorEntity));

            this.AccessConnectorUpdate.Tags = new InsensitiveDictionary<string>() { { Constants.Common.Tag1, Constants.Common.Value1 } };

            var systemData = SystemDataExtension.GetSystemData(Constants.Common.SystemDataJson);
            //Test Item
            var response = this.AccessConnectorController.PatchAccessConnector(
                Constants.Common.SubscriptionId,
                Constants.Common.ResourceGroupName,
                Constants.Connector.ConnectorName1,
                this.AccessConnectorUpdate,
                Constants.Common.SystemDataJson, "", "", "").Result;

            var responseContent = ConvertResponse(response);

            //Assert
            Assert.IsTrue(responseContent.SystemData.ToJson().Equals(SystemDataExtension.GetSystemData(Constants.Common.SystemDataJson).ToJson()));
        }

        [TestMethod("Patches accessConnector with SystemAssigned Identity fails during notification.")]
        public void TestPatchAccessConnectorFailureForIdentity()
        {
            //Set up
            var accessConnectorEntity = new AccessConnectorEntity(
                Constants.Connector.ConnectorName1,
                Constants.Common.ResourceGroupName,
                Constants.Common.SubscriptionId,
                Constants.Common.Location,
                tags: null,
                identity: null)
            {
                Properties = new AccessConnectorProperties()
                {
                    ProvisioningState = AccessConnectorState.Succeeded
                }
            };

            this.AccessConnectorUpdate.Identity = new Identity() { Type = IdentityType.SystemAssigned };
            this.Request.Headers.Add(RequestCorrelationContext.HeaderResourceIdentityUrl, Constants.SystemAssignedIdentity.DataPlaneUrl);
            this.Request.Headers.Add(RequestCorrelationContext.HeaderResourceIdentityPrincipalid, Constants.SystemAssignedIdentity.PrincipalId);
            this.Request.Headers.Add(ProviderConstants.ManagedIdentity.HeaderHomeTenantId, Constants.SystemAssignedIdentity.TenantId);

            this.AccessConnectorDataProvider.Setup(o => o.FindAccessConnector(
                    It.Is<string>(subscription => subscription == Constants.Common.SubscriptionId),
                    It.Is<string>(rgName => rgName == Constants.Common.ResourceGroupName),
                    It.Is<string>(connectorName => connectorName == Constants.Connector.ConnectorName1)))
                .Returns(Task.FromResult<AccessConnectorEntity>(accessConnectorEntity));

            this.FrontdoorEngine.Setup(
                            o => o.CallDatabricksAsyncAccountApi(
                                It.IsAny<HttpMethod>(),
                                It.IsAny<Uri>(),
                                It.IsAny<string>(),
                                It.IsAny<string>(),
                                It.IsAny<CredMgrNotification>(),
                                It.IsAny<InsensitiveDictionary<string>>()))
                        .Throws<Exception>();

            //Test Item
            try
            {
                var response = this.AccessConnectorController.PatchAccessConnector(
                    Constants.Common.SubscriptionId,
                    Constants.Common.ResourceGroupName,
                    Constants.Connector.ConnectorName1,
                    this.AccessConnectorUpdate,
                    null, Constants.SystemAssignedIdentity.DataPlaneUrl, Constants.SystemAssignedIdentity.PrincipalId, Constants.SystemAssignedIdentity.TenantId).Result;
            }
            catch (Exception)
            {
                //Ignore exception
            }
            finally
            {
                //Assert
                this.AccessConnectorDataProvider.Verify(
                    o => o.SaveAccessConnector(
                        It.Is<AccessConnectorEntity>(
                            entity => entity.Properties.ProvisioningState == AccessConnectorState.Failed))
                    );
            }
        }

        [TestMethod("Patches previously failed accessConnector with SystemAssigned Identity ensure it notifies CM.")]
        public void TestPatchFailedAccessConnectorWithIdentityAgain()
        {
            //Set up
            var accessConnectorEntity = new AccessConnectorEntity(
                Constants.Connector.ConnectorName1,
                Constants.Common.ResourceGroupName,
                Constants.Common.SubscriptionId,
                Constants.Common.Location,
                tags: null)
            {
                Identity = new Identity()
                {
                    Type = IdentityType.SystemAssigned,
                    PrincipalId = Constants.SystemAssignedIdentity.PrincipalId,
                    TenantId = Constants.SystemAssignedIdentity.TenantId
                },
                IdentityUrl = Constants.SystemAssignedIdentity.DataPlaneUrl,
                Properties = new AccessConnectorProperties()
                {
                    ProvisioningState = AccessConnectorState.Failed
                }
            };

            this.AccessConnectorUpdate.Identity = new Identity() { Type = IdentityType.SystemAssigned };
            this.Request.Headers.Add(RequestCorrelationContext.HeaderResourceIdentityUrl, Constants.SystemAssignedIdentity.DataPlaneUrl);
            this.Request.Headers.Add(RequestCorrelationContext.HeaderResourceIdentityPrincipalid, Constants.SystemAssignedIdentity.PrincipalId);
            this.Request.Headers.Add(ProviderConstants.ManagedIdentity.HeaderHomeTenantId, Constants.SystemAssignedIdentity.TenantId);

            this.AccessConnectorDataProvider.Setup(o => o.FindAccessConnector(
                    It.Is<string>(subscription => subscription == Constants.Common.SubscriptionId),
                    It.Is<string>(rgName => rgName == Constants.Common.ResourceGroupName),
                    It.Is<string>(connectorName => connectorName == Constants.Connector.ConnectorName1)))
                .Returns(Task.FromResult<AccessConnectorEntity>(accessConnectorEntity));

            //Test Item
            var response = this.AccessConnectorController.PatchAccessConnector(
                    Constants.Common.SubscriptionId,
                    Constants.Common.ResourceGroupName,
                    Constants.Connector.ConnectorName1,
                    this.AccessConnectorUpdate,
                    null, Constants.SystemAssignedIdentity.DataPlaneUrl, Constants.SystemAssignedIdentity.PrincipalId, Constants.SystemAssignedIdentity.TenantId).Result;
            //Assert
            this.AccessConnectorDataProvider.Verify(
                o => o.SaveAccessConnector(
                    It.Is<AccessConnectorEntity>(
                        entity => entity.Properties.ProvisioningState == AccessConnectorState.Succeeded))
                );

            this.FrontdoorEngine.Verify(
                o => o.CallDatabricksAsyncAccountApi(
                    It.IsAny<HttpMethod>(),
                    It.IsAny<Uri>(),
                    It.IsAny<string>(),
                    It.IsAny<string>(),
                    It.IsAny<CredMgrNotification>(),
                    It.IsAny<InsensitiveDictionary<string>>())
                );
        }

        [TestMethod("Add one UA MI Assigned to an existing Access Connector which do not have identity assigned via PATCH request. This should Succeed.")]
        public void AddOneUserAssignedIdentityOnExistingAccessConnectorWhichdoNotHaveidentityAssignedWithPatchCall()
        {
            using (new AppConfigSettingScope(this.ConfigurationSettings))
            {
                using (RequestCorrelationContext.NewCorrelationContextScope())
                {
                    //Set up
                    SetUpCorrelationContext(Constants.Common.Connector20230501APIVersion);

                    var accessConnectorEntity = new AccessConnectorEntity(
                    Constants.Connector.ConnectorName1,
                    Constants.Common.ResourceGroupName,
                    Constants.Common.SubscriptionId,
                    Constants.Common.Location,
                    tags: null,
                    identity: null)
                    {
                        Properties = new AccessConnectorProperties()
                        {
                            ProvisioningState = AccessConnectorState.Succeeded
                        }
                    };

                    this.AccessConnectorDataProvider.Setup(o => o.FindAccessConnector(
                            It.Is<string>(subscription => subscription == Constants.Common.SubscriptionId),
                            It.Is<string>(rgName => rgName == Constants.Common.ResourceGroupName),
                            It.Is<string>(connectorName => connectorName == Constants.Connector.ConnectorName1)))
                        .Returns(Task.FromResult<AccessConnectorEntity>(accessConnectorEntity));

                    this.AccessConnectorUpdate.Identity = new Identity()
                    {
                        Type = IdentityType.UserAssigned,
                        UserAssignedIdentities = new InsensitiveDictionary<UserAssignedIdentity>
                        {
                            { Constants.UserAssignedIdentity.Identity1.ResourceId, new UserAssignedIdentity() }
                        }
                    };

                    var identitiesCred = new IdentitiesCredential();
                    var userAssignedIdentity1 = new UserAssignedIdentityCredential
                    {
                        ResourceId = Constants.UserAssignedIdentity.Identity1.ResourceId,
                        ObjectId = Constants.UserAssignedIdentity.Identity1.ObjectId,
                        ClientId = Constants.UserAssignedIdentity.Identity1.ClientId
                    };

                    identitiesCred.UserAssignedIdentities = new List<UserAssignedIdentityCredential>()
                    { userAssignedIdentity1 };

                    this.FrontdoorEngine.Setup(o => o.GetIdentitiesCredentials(
                        It.Is<string>(identityUrl => identityUrl == Constants.SystemAssignedIdentity.DataPlaneUrl),
                        It.IsAny<CredentialRequest>())).Returns(Task.FromResult<IdentitiesCredential>(identitiesCred));

                    this.Request.Headers.Add(RequestCorrelationContext.HeaderResourceIdentityUrl, Constants.SystemAssignedIdentity.DataPlaneUrl);
                    this.Request.Headers.Add(RequestCorrelationContext.HeaderResourceIdentityPrincipalid, Constants.SystemAssignedIdentity.PrincipalId);
                    this.Request.Headers.Add(ProviderConstants.ManagedIdentity.HeaderHomeTenantId, Constants.SystemAssignedIdentity.TenantId);

                    //Test Item
                    var response = this.AccessConnectorController.PatchAccessConnector(
                       Constants.Common.SubscriptionId,
                       Constants.Common.ResourceGroupName,
                       Constants.Connector.ConnectorName1,
                       this.AccessConnectorUpdate,
                       null, Constants.SystemAssignedIdentity.DataPlaneUrl, Constants.SystemAssignedIdentity.PrincipalId, Constants.SystemAssignedIdentity.TenantId).Result;

                    var responseContent = ConvertResponse(response);

                    // Assert
                    Assert.AreNotEqual(null, responseContent);
                    Assert.AreNotEqual(null, responseContent.Identity);
                    Assert.AreEqual(IdentityType.UserAssigned, responseContent.Identity.Type);
                    Assert.AreEqual((int)HttpStatusCode.OK, (response as ObjectResult).StatusCode);

                    Assert.AreNotEqual(null, responseContent.Identity.UserAssignedIdentities);
                    Assert.AreEqual(1, responseContent.Identity.UserAssignedIdentities.Count);
                    Assert.IsTrue(responseContent.Identity.UserAssignedIdentities.ContainsKey(Constants.UserAssignedIdentity.Identity1.ResourceId));

                    Assert.AreEqual(Constants.UserAssignedIdentity.Identity1.ObjectId,
                        responseContent.Identity.UserAssignedIdentities[Constants.UserAssignedIdentity.Identity1.ResourceId].PrincipalId);
                    Assert.AreEqual(Constants.UserAssignedIdentity.Identity1.ClientId,
                        responseContent.Identity.UserAssignedIdentities[Constants.UserAssignedIdentity.Identity1.ResourceId].ClientId);

                    this.AccessConnectorDataProvider.Verify(
                            o => o.FindAccessConnector(
                                It.Is<string>(subscription => subscription == Constants.Common.SubscriptionId),
                                It.Is<string>(rgName => rgName == Constants.Common.ResourceGroupName),
                                It.Is<string>(connectorName => connectorName == Constants.Connector.ConnectorName1)), Times.Once);

                    this.FrontdoorEngine.Verify(
                        o => o.CallDatabricksAsyncAccountApi(
                            It.IsAny<HttpMethod>(),
                            It.IsAny<Uri>(),
                            It.IsAny<string>(),
                            It.IsAny<string>(),
                            It.IsAny<CredMgrNotification>(),
                            It.IsAny<InsensitiveDictionary<string>>()), Times.Once);

                    this.AccessConnectorDataProvider.Verify(
                        o => o.SaveAccessConnector(
                            It.Is<AccessConnectorEntity>(entity =>
                                entity.Properties.ProvisioningState == AccessConnectorState.Succeeded)), Times.Once);
                }
            }
        }

        [TestMethod("UserAssignedIdentities is null via PATCH call. This should Succeed")]
        public void NullUserAssignedIdentitiesOnExistingAccessConnectorWith3UserAssignedandSystemAssignedIdentityWithPathCall()
        {
            using (new AppConfigSettingScope(this.ConfigurationSettings))
            {
                using (RequestCorrelationContext.NewCorrelationContextScope())
                {
                    //Set up
                    SetUpCorrelationContext(Constants.Common.Connector20230501APIVersion);

                    var accessConnectorEntity = new AccessConnectorEntity(
                    Constants.Connector.ConnectorName1,
                    Constants.Common.ResourceGroupName,
                    Constants.Common.SubscriptionId,
                    Constants.Common.Location,
                    tags: null)
                    {
                        Identity = new Identity()
                        {
                            Type = IdentityType.UserAssigned,
                            UserAssignedIdentities = new InsensitiveDictionary<UserAssignedIdentity>
                            {
                                { Constants.UserAssignedIdentity.Identity1.ResourceId, new UserAssignedIdentity() },
                                { Constants.UserAssignedIdentity.Identity2.ResourceId, new UserAssignedIdentity() },
                                { Constants.UserAssignedIdentity.Identity3.ResourceId, new UserAssignedIdentity() },
                            }
                        },
                        IdentityUrl = Constants.SystemAssignedIdentity.DataPlaneUrl,
                        Properties = new AccessConnectorProperties()
                        {
                            ProvisioningState = AccessConnectorState.Succeeded
                        }
                    };

                    this.AccessConnectorDataProvider.Setup(o => o.FindAccessConnector(
                            It.Is<string>(subscription => subscription == Constants.Common.SubscriptionId),
                            It.Is<string>(rgName => rgName == Constants.Common.ResourceGroupName),
                            It.Is<string>(connectorName => connectorName == Constants.Connector.ConnectorName1)))
                        .Returns(Task.FromResult<AccessConnectorEntity>(accessConnectorEntity));

                    this.AccessConnectorUpdate.Identity = new Identity()
                    {
                        Type = IdentityType.UserAssigned | IdentityType.SystemAssigned,
                    };

                    var identitiesCred = new IdentitiesCredential();
                    var userAssignedIdentity1 = new UserAssignedIdentityCredential
                    {
                        ResourceId = Constants.UserAssignedIdentity.Identity1.ResourceId,
                        ObjectId = Constants.UserAssignedIdentity.Identity1.ObjectId,
                        ClientId = Constants.UserAssignedIdentity.Identity1.ClientId
                    };

                    identitiesCred.UserAssignedIdentities = new List<UserAssignedIdentityCredential>()
                    { userAssignedIdentity1 };

                    this.FrontdoorEngine.Setup(o => o.GetIdentitiesCredentials(
                        It.Is<string>(identityUrl => identityUrl == Constants.SystemAssignedIdentity.DataPlaneUrl),
                        It.IsAny<CredentialRequest>())).Returns(Task.FromResult<IdentitiesCredential>(identitiesCred));

                    this.Request.Headers.Add(RequestCorrelationContext.HeaderResourceIdentityUrl, Constants.SystemAssignedIdentity.DataPlaneUrl);
                    this.Request.Headers.Add(RequestCorrelationContext.HeaderResourceIdentityPrincipalid, Constants.SystemAssignedIdentity.PrincipalId);
                    this.Request.Headers.Add(ProviderConstants.ManagedIdentity.HeaderHomeTenantId, Constants.SystemAssignedIdentity.TenantId);


                    //Test Item
                    var response = this.AccessConnectorController.PatchAccessConnector(
                            Constants.Common.SubscriptionId,
                            Constants.Common.ResourceGroupName,
                            Constants.Connector.ConnectorName1,
                            this.AccessConnectorUpdate,
                            null, Constants.SystemAssignedIdentity.DataPlaneUrl, Constants.SystemAssignedIdentity.PrincipalId, Constants.SystemAssignedIdentity.TenantId).Result;

                    this.AccessConnectorDataProvider.Verify(
                            o => o.FindAccessConnector(
                                It.Is<string>(subscription => subscription == Constants.Common.SubscriptionId),
                                It.Is<string>(rgName => rgName == Constants.Common.ResourceGroupName),
                                It.Is<string>(connectorName => connectorName == Constants.Connector.ConnectorName1)), Times.Once);

                    this.FrontdoorEngine.Verify(
                        o => o.CallDatabricksAsyncAccountApi(
                        It.IsAny<HttpMethod>(),
                        It.IsAny<Uri>(),
                        It.IsAny<string>(),
                        It.IsAny<string>(),
                        It.IsAny<CredMgrNotification>(),
                        It.IsAny<InsensitiveDictionary<string>>()), Times.Once);

                    this.AccessConnectorDataProvider.Verify(
                        o => o.SaveAccessConnector(
                            It.IsAny<AccessConnectorEntity>()), Times.Once);

                    var responseContent = ConvertResponse(response);

                    //Assert
                    Assert.AreNotEqual(null, responseContent);
                    Assert.AreNotEqual(null, responseContent.Identity);
                    Assert.AreEqual((int)HttpStatusCode.OK, (response as ObjectResult).StatusCode);

                    Assert.AreEqual(responseContent.Identity.Type, IdentityType.SystemAssigned | IdentityType.UserAssigned);
                    Assert.AreEqual(Constants.SystemAssignedIdentity.PrincipalId, responseContent.Identity.PrincipalId);
                    Assert.AreEqual(Constants.SystemAssignedIdentity.TenantId, responseContent.Identity.TenantId);
                    Assert.AreNotEqual(null, responseContent.Identity.UserAssignedIdentities);
                    Assert.AreEqual(3, responseContent.Identity.UserAssignedIdentities.Count);
                    Assert.IsTrue(responseContent.Identity.UserAssignedIdentities.ContainsKey(Constants.UserAssignedIdentity.Identity1.ResourceId));
                    Assert.IsTrue(responseContent.Identity.UserAssignedIdentities.ContainsKey(Constants.UserAssignedIdentity.Identity2.ResourceId));
                    Assert.IsTrue(responseContent.Identity.UserAssignedIdentities.ContainsKey(Constants.UserAssignedIdentity.Identity3.ResourceId));

                    Assert.AreEqual(Constants.UserAssignedIdentity.Identity1.ObjectId,
                        responseContent.Identity.UserAssignedIdentities[Constants.UserAssignedIdentity.Identity1.ResourceId].PrincipalId);
                    Assert.AreEqual(Constants.UserAssignedIdentity.Identity1.ClientId,
                        responseContent.Identity.UserAssignedIdentities[Constants.UserAssignedIdentity.Identity1.ResourceId].ClientId);
                }
            }
        }

        [TestMethod("Add One UA MI and SA MI for an existing Access Connector with 3 UA MI via PATCH call. This should Succeed")]
        public void AddOneUserAssignedIdentityandSystemAssignedIdentityForAccessConnectorWith3UserAssignedIdentitiesWithPatchCall()
        {
            using (new AppConfigSettingScope(this.ConfigurationSettings))
            {
                using (RequestCorrelationContext.NewCorrelationContextScope())
                {
                    //Set up
                    SetUpCorrelationContext(Constants.Common.Connector20230501APIVersion);

                    var accessConnectorEntity = new AccessConnectorEntity(
                    Constants.Connector.ConnectorName1,
                    Constants.Common.ResourceGroupName,
                    Constants.Common.SubscriptionId,
                    Constants.Common.Location,
                    tags: null)
                    {
                        Identity = new Identity()
                        {
                            Type = IdentityType.UserAssigned,
                            UserAssignedIdentities = new InsensitiveDictionary<UserAssignedIdentity>
                            {
                                { Constants.UserAssignedIdentity.Identity1.ResourceId, new UserAssignedIdentity() },
                                { Constants.UserAssignedIdentity.Identity2.ResourceId, new UserAssignedIdentity() },
                                { Constants.UserAssignedIdentity.Identity3.ResourceId, new UserAssignedIdentity() },
                            }
                        },
                        IdentityUrl = Constants.SystemAssignedIdentity.DataPlaneUrl,
                        Properties = new AccessConnectorProperties()
                        {
                            ProvisioningState = AccessConnectorState.Succeeded
                        }
                    };

                    this.AccessConnectorDataProvider.Setup(o => o.FindAccessConnector(
                            It.Is<string>(subscription => subscription == Constants.Common.SubscriptionId),
                            It.Is<string>(rgName => rgName == Constants.Common.ResourceGroupName),
                            It.Is<string>(connectorName => connectorName == Constants.Connector.ConnectorName1)))
                        .Returns(Task.FromResult<AccessConnectorEntity>(accessConnectorEntity));

                    this.AccessConnectorUpdate.Identity = new Identity()
                    {
                        Type = IdentityType.UserAssigned | IdentityType.SystemAssigned,
                        UserAssignedIdentities = new InsensitiveDictionary<UserAssignedIdentity>
                        {
                            { Constants.UserAssignedIdentity.Identity4.ResourceId, new UserAssignedIdentity()}
                        }
                    };

                    var userAssignedIdentity4 = new UserAssignedIdentityCredential
                    {
                        ResourceId = Constants.UserAssignedIdentity.Identity4.ResourceId,
                        ObjectId = Constants.UserAssignedIdentity.Identity4.ObjectId,
                        ClientId = Constants.UserAssignedIdentity.Identity4.ClientId
                    };

                    var identitiesCred = new IdentitiesCredential();
                    identitiesCred.UserAssignedIdentities = new List<UserAssignedIdentityCredential>()
                    { userAssignedIdentity4 };

                    this.FrontdoorEngine.Setup(o => o.GetIdentitiesCredentials(
                        It.Is<string>(identityUrl => identityUrl == Constants.SystemAssignedIdentity.DataPlaneUrl),
                        It.IsAny<CredentialRequest>())).Returns(Task.FromResult<IdentitiesCredential>(identitiesCred));

                    this.Request.Headers.Add(RequestCorrelationContext.HeaderResourceIdentityUrl, Constants.SystemAssignedIdentity.DataPlaneUrl);
                    this.Request.Headers.Add(RequestCorrelationContext.HeaderResourceIdentityPrincipalid, Constants.SystemAssignedIdentity.PrincipalId);
                    this.Request.Headers.Add(ProviderConstants.ManagedIdentity.HeaderHomeTenantId, Constants.SystemAssignedIdentity.TenantId);

                    //Test Item
                    var response = this.AccessConnectorController.PatchAccessConnector(
                            Constants.Common.SubscriptionId,
                            Constants.Common.ResourceGroupName,
                            Constants.Connector.ConnectorName1,
                            this.AccessConnectorUpdate,
                            null, Constants.SystemAssignedIdentity.DataPlaneUrl, Constants.SystemAssignedIdentity.PrincipalId, Constants.SystemAssignedIdentity.TenantId).Result;

                    this.AccessConnectorDataProvider.Verify(
                            o => o.FindAccessConnector(
                                It.Is<string>(subscription => subscription == Constants.Common.SubscriptionId),
                                It.Is<string>(rgName => rgName == Constants.Common.ResourceGroupName),
                                It.Is<string>(connectorName => connectorName == Constants.Connector.ConnectorName1)), Times.Once);

                    this.FrontdoorEngine.Verify(
                        o => o.CallDatabricksAsyncAccountApi(
                        It.IsAny<HttpMethod>(),
                        It.IsAny<Uri>(),
                        It.IsAny<string>(),
                        It.IsAny<string>(),
                        It.IsAny<CredMgrNotification>(),
                        It.IsAny<InsensitiveDictionary<string>>()), Times.Once);

                    this.AccessConnectorDataProvider.Verify(
                        o => o.SaveAccessConnector(
                            It.IsAny<AccessConnectorEntity>()), Times.Once);

                    var responseContent = ConvertResponse(response);

                    //Assert
                    Assert.AreNotEqual(null, responseContent);
                    Assert.AreNotEqual(null, responseContent.Identity);
                    Assert.AreEqual((int)HttpStatusCode.OK, (response as ObjectResult).StatusCode);

                    Assert.AreEqual(responseContent.Identity.Type, IdentityType.SystemAssigned | IdentityType.UserAssigned);
                    Assert.AreEqual(Constants.SystemAssignedIdentity.PrincipalId, responseContent.Identity.PrincipalId);
                    Assert.AreEqual(Constants.SystemAssignedIdentity.TenantId, responseContent.Identity.TenantId);
                    Assert.AreNotEqual(null, responseContent.Identity.UserAssignedIdentities);
                    Assert.AreEqual(4, responseContent.Identity.UserAssignedIdentities.Count);
                    Assert.IsTrue(responseContent.Identity.UserAssignedIdentities.ContainsKey(Constants.UserAssignedIdentity.Identity4.ResourceId));
                }
            }
        }

        [TestMethod("Add One UA MI for an existing Access Connector with Max UA MI via PATCH call. This should fail")]
        public void AddOneUserAssignedIdentityForAccessConnectorWithMaxAssignedIdentitiesWithPatchCall()
        {

            using (new AppConfigSettingScope(this.ConfigurationSettings))
            {
                using (RequestCorrelationContext.NewCorrelationContextScope())
                {
                    //Set up
                    SetUpCorrelationContext(Constants.Common.Connector20230501APIVersion);

                    InsensitiveDictionary<UserAssignedIdentity> userAssignedIdentities = SetUpUserAssignedIdentities(ProviderConstants.ManagedIdentity.MaxUserAssignedIdentitiesPerConnector);

                    var accessConnectorEntity = new AccessConnectorEntity(
                    Constants.Connector.ConnectorName1,
                    Constants.Common.ResourceGroupName,
                    Constants.Common.SubscriptionId,
                    Constants.Common.Location,
                    tags: null)
                    {
                        Identity = new Identity()
                        {
                            Type = IdentityType.UserAssigned,
                            UserAssignedIdentities = userAssignedIdentities
                        },
                        IdentityUrl = Constants.SystemAssignedIdentity.DataPlaneUrl,
                        Properties = new AccessConnectorProperties()
                        {
                            ProvisioningState = AccessConnectorState.Succeeded
                        }
                    };

                    this.AccessConnectorDataProvider.Setup(o => o.FindAccessConnector(
                           It.Is<string>(subscription => subscription == Constants.Common.SubscriptionId),
                           It.Is<string>(rgName => rgName == Constants.Common.ResourceGroupName),
                           It.Is<string>(connectorName => connectorName == Constants.Connector.ConnectorName1)))
                       .Returns(Task.FromResult<AccessConnectorEntity>(accessConnectorEntity));

                    this.AccessConnectorUpdate.Identity = new Identity()
                    {
                        Type = IdentityType.UserAssigned,
                        UserAssignedIdentities = new InsensitiveDictionary<UserAssignedIdentity>
                        {
                            { Constants.UserAssignedIdentity.Identity2.ResourceId, new UserAssignedIdentity()},
                        }
                    };

                    var userAssignedIdentity2 = new UserAssignedIdentityCredential
                    {
                        ResourceId = Constants.UserAssignedIdentity.Identity2.ResourceId,
                        ObjectId = Constants.UserAssignedIdentity.Identity2.ObjectId,
                        ClientId = Constants.UserAssignedIdentity.Identity2.ClientId
                    };

                    var identitiesCred = new IdentitiesCredential();
                    identitiesCred.UserAssignedIdentities = new List<UserAssignedIdentityCredential>()
                    { userAssignedIdentity2 };

                    this.FrontdoorEngine.Setup(o => o.GetIdentitiesCredentials(
                        It.Is<string>(identityUrl => identityUrl == Constants.SystemAssignedIdentity.DataPlaneUrl),
                        It.IsAny<CredentialRequest>())).Returns(Task.FromResult<IdentitiesCredential>(identitiesCred));

                    this.Request.Headers.Add(RequestCorrelationContext.HeaderResourceIdentityUrl, Constants.SystemAssignedIdentity.DataPlaneUrl);
                    this.Request.Headers.Add(RequestCorrelationContext.HeaderResourceIdentityPrincipalid, Constants.SystemAssignedIdentity.PrincipalId);
                    this.Request.Headers.Add(ProviderConstants.ManagedIdentity.HeaderHomeTenantId, Constants.SystemAssignedIdentity.TenantId);

                    try
                    {
                        var response = this.AccessConnectorController.PatchAccessConnector(
                            Constants.Common.SubscriptionId,
                            Constants.Common.ResourceGroupName,
                            Constants.Connector.ConnectorName1,
                            this.AccessConnectorUpdate,
                            null, Constants.SystemAssignedIdentity.DataPlaneUrl, Constants.SystemAssignedIdentity.PrincipalId, Constants.SystemAssignedIdentity.TenantId).Result;

                        Assert.IsTrue(false);
                    }
                    catch (Exception ex)
                    {
                        Assert.AreEqual(ex.InnerException.Message, ErrorResponseMessages.LimitedUserAssignedIdentitySupportPerAccessConnector.ToLocalizedMessage(ProviderConstants.ManagedIdentity.MaxUserAssignedIdentitiesPerConnector, ProviderConstants.ManagedIdentity.MaxUserAssignedIdentitiesPerConnector + 1));
                    }
                    finally
                    {
                        //Assert
                        this.AccessConnectorDataProvider.Verify(
                            o => o.FindAccessConnector(
                                It.Is<string>(subscription => subscription == Constants.Common.SubscriptionId),
                                It.Is<string>(rgName => rgName == Constants.Common.ResourceGroupName),
                                It.Is<string>(connectorName => connectorName == Constants.Connector.ConnectorName1)), Times.Once);

                        this.FrontdoorEngine.Verify(
                            o => o.CallDatabricksAsyncAccountApi(
                            It.IsAny<HttpMethod>(),
                            It.IsAny<Uri>(),
                            It.IsAny<string>(),
                            It.IsAny<string>(),
                            It.IsAny<CredMgrNotification>(),
                            It.IsAny<InsensitiveDictionary<string>>()), Times.Never);

                        this.AccessConnectorDataProvider.Verify(
                            o => o.SaveAccessConnector(
                                It.IsAny<AccessConnectorEntity>()), Times.Never);
                    }
                }
            }
        }

        [TestMethod("Add One UA MI for an existing Access Connector with one UA MI via PATCH call.")]
        public void AddOneUserAssignedIdentityForAccessConnectorWithoneAssignedIdentitiesWithPatchCall()
        {
            using (new AppConfigSettingScope(this.ConfigurationSettings))
            {
                using (RequestCorrelationContext.NewCorrelationContextScope())
                {
                    //Set up
                    SetUpCorrelationContext(Constants.Common.Connector20230501APIVersion);

                    var accessConnectorEntity = new AccessConnectorEntity(
                    Constants.Connector.ConnectorName1,
                    Constants.Common.ResourceGroupName,
                    Constants.Common.SubscriptionId,
                    Constants.Common.Location,
                    tags: null)
                    {
                        Identity = new Identity()
                        {
                            Type = IdentityType.UserAssigned,
                            UserAssignedIdentities = new InsensitiveDictionary<UserAssignedIdentity>
                            {
                                { Constants.UserAssignedIdentity.Identity1.ResourceId,
                                    new UserAssignedIdentity(
                                        Constants.UserAssignedIdentity.Identity1.ObjectId,
                                        Constants.UserAssignedIdentity.Identity1.ClientId,
                                        Constants.UserAssignedIdentity.Identity1.TenantId
                                        )
                                },
                            }
                        },
                        IdentityUrl = Constants.SystemAssignedIdentity.DataPlaneUrl,
                        Properties = new AccessConnectorProperties()
                        {
                            ProvisioningState = AccessConnectorState.Succeeded
                        }
                    };

                    this.AccessConnectorDataProvider.Setup(o => o.FindAccessConnector(
                            It.Is<string>(subscription => subscription == Constants.Common.SubscriptionId),
                            It.Is<string>(rgName => rgName == Constants.Common.ResourceGroupName),
                            It.Is<string>(connectorName => connectorName == Constants.Connector.ConnectorName1)))
                        .Returns(Task.FromResult<AccessConnectorEntity>(accessConnectorEntity));

                    this.AccessConnectorUpdate.Identity = new Identity()
                    {
                        Type = IdentityType.UserAssigned,
                        UserAssignedIdentities = new InsensitiveDictionary<UserAssignedIdentity>
                        {
                            { Constants.UserAssignedIdentity.Identity2.ResourceId, new UserAssignedIdentity()},
                        }
                    };

                    var userAssignedIdentity2 = new UserAssignedIdentityCredential
                    {
                        ResourceId = Constants.UserAssignedIdentity.Identity2.ResourceId,
                        ObjectId = Constants.UserAssignedIdentity.Identity2.ObjectId,
                        ClientId = Constants.UserAssignedIdentity.Identity2.ClientId
                    };

                    var identitiesCred = new IdentitiesCredential();
                    identitiesCred.UserAssignedIdentities = new List<UserAssignedIdentityCredential>()
                    { userAssignedIdentity2 };

                    this.FrontdoorEngine.Setup(o => o.GetIdentitiesCredentials(
                        It.Is<string>(identityUrl => identityUrl == Constants.SystemAssignedIdentity.DataPlaneUrl),
                        It.IsAny<CredentialRequest>())).Returns(Task.FromResult<IdentitiesCredential>(identitiesCred));

                    this.Request.Headers.Add(RequestCorrelationContext.HeaderResourceIdentityUrl, Constants.SystemAssignedIdentity.DataPlaneUrl);
                    this.Request.Headers.Add(RequestCorrelationContext.HeaderResourceIdentityPrincipalid, Constants.SystemAssignedIdentity.PrincipalId);
                    this.Request.Headers.Add(ProviderConstants.ManagedIdentity.HeaderHomeTenantId, Constants.SystemAssignedIdentity.TenantId);

                    //Test Item
                    var response = this.AccessConnectorController.PatchAccessConnector(
                        Constants.Common.SubscriptionId,
                        Constants.Common.ResourceGroupName,
                        Constants.Connector.ConnectorName1,
                        this.AccessConnectorUpdate,
                        null, Constants.SystemAssignedIdentity.DataPlaneUrl, Constants.SystemAssignedIdentity.PrincipalId, Constants.SystemAssignedIdentity.TenantId).Result;

                    var responseContent = ConvertResponse(response);

                    // Assert
                    Assert.AreNotEqual(null, responseContent);
                    Assert.AreNotEqual(null, responseContent.Identity);
                    Assert.AreEqual(IdentityType.UserAssigned, responseContent.Identity.Type);
                    Assert.AreEqual((int)HttpStatusCode.OK, (response as ObjectResult).StatusCode);

                    Assert.AreNotEqual(null, responseContent.Identity.UserAssignedIdentities);
                    Assert.AreEqual(2, responseContent.Identity.UserAssignedIdentities.Count);
                    Assert.IsTrue(responseContent.Identity.UserAssignedIdentities.ContainsKey(Constants.UserAssignedIdentity.Identity1.ResourceId));
                    Assert.IsTrue(responseContent.Identity.UserAssignedIdentities.ContainsKey(Constants.UserAssignedIdentity.Identity2.ResourceId));

                    Assert.AreEqual(Constants.UserAssignedIdentity.Identity2.ObjectId,
                        responseContent.Identity.UserAssignedIdentities[Constants.UserAssignedIdentity.Identity2.ResourceId].PrincipalId);
                    Assert.AreEqual(Constants.UserAssignedIdentity.Identity2.ClientId,
                        responseContent.Identity.UserAssignedIdentities[Constants.UserAssignedIdentity.Identity2.ResourceId].ClientId);
                }
            }
        }

        [TestMethod("Remove two UA MI Assigned to an existing Access Connector with 3 UA MI via PATCH request. This should Succeed")]
        public void RemovetwoUserAssignedIdentitiesOnExistingAccessConnectorWith3UAMIviaPatchCall()
        {
            using (new AppConfigSettingScope(this.ConfigurationSettings))
            {
                using (RequestCorrelationContext.NewCorrelationContextScope())
                {
                    //Set up
                    SetUpCorrelationContext(Constants.Common.Connector20230501APIVersion);

                    var accessConnectorEntity = new AccessConnectorEntity(
                    Constants.Connector.ConnectorName1,
                    Constants.Common.ResourceGroupName,
                    Constants.Common.SubscriptionId,
                    Constants.Common.Location,
                    tags: null)
                    {
                        Identity = new Identity()
                        {
                            Type = IdentityType.UserAssigned,
                            UserAssignedIdentities = new InsensitiveDictionary<UserAssignedIdentity>
                            {
                                { Constants.UserAssignedIdentity.Identity1.ResourceId,
                                    new UserAssignedIdentity(
                                        Constants.UserAssignedIdentity.Identity1.ObjectId,
                                        Constants.UserAssignedIdentity.Identity1.ClientId,
                                        Constants.UserAssignedIdentity.Identity1.TenantId
                                        )
                                },
                                { Constants.UserAssignedIdentity.Identity2.ResourceId,
                                    new UserAssignedIdentity(
                                        Constants.UserAssignedIdentity.Identity2.ObjectId,
                                        Constants.UserAssignedIdentity.Identity2.ClientId,
                                        Constants.UserAssignedIdentity.Identity2.TenantId
                                        )
                                },
                                { Constants.UserAssignedIdentity.Identity3.ResourceId,
                                    new UserAssignedIdentity(
                                        Constants.UserAssignedIdentity.Identity3.ObjectId,
                                        Constants.UserAssignedIdentity.Identity3.ClientId,
                                        Constants.UserAssignedIdentity.Identity3.TenantId
                                        )
                                }
                            }
                        },
                        IdentityUrl = Constants.SystemAssignedIdentity.DataPlaneUrl,
                        Properties = new AccessConnectorProperties()
                        {
                            ProvisioningState = AccessConnectorState.Succeeded
                        }
                    };

                    this.AccessConnectorDataProvider.Setup(o => o.FindAccessConnector(
                            It.Is<string>(subscription => subscription == Constants.Common.SubscriptionId),
                            It.Is<string>(rgName => rgName == Constants.Common.ResourceGroupName),
                            It.Is<string>(connectorName => connectorName == Constants.Connector.ConnectorName1)))
                        .Returns(Task.FromResult<AccessConnectorEntity>(accessConnectorEntity));

                    this.AccessConnectorUpdate.Identity = new Identity()
                    {
                        Type = IdentityType.UserAssigned,
                        UserAssignedIdentities = new InsensitiveDictionary<UserAssignedIdentity>
                        {
                            { Constants.UserAssignedIdentity.Identity1.ResourceId, null },
                            { Constants.UserAssignedIdentity.Identity2.ResourceId, null }
                        }
                    };

                    var identitiesCred = new IdentitiesCredential
                    {
                        TenantId = Constants.SystemAssignedIdentity.TenantId,
                        ClientId = Constants.SystemAssignedIdentity.PrincipalId
                    };

                    var userAssignedIdentity3 = new UserAssignedIdentityCredential
                    {
                        ResourceId = Constants.UserAssignedIdentity.Identity3.ResourceId,
                        ObjectId = Constants.UserAssignedIdentity.Identity3.ObjectId,
                        ClientId = Constants.UserAssignedIdentity.Identity3.ClientId
                    };

                    identitiesCred.UserAssignedIdentities = new List<UserAssignedIdentityCredential>()
                    { userAssignedIdentity3 };

                    this.FrontdoorEngine.Setup(o => o.GetIdentitiesCredentials(
                        It.Is<string>(identityUrl => identityUrl == Constants.SystemAssignedIdentity.DataPlaneUrl),
                        It.IsAny<CredentialRequest>())).Returns(Task.FromResult<IdentitiesCredential>(identitiesCred));

                    this.Request.Headers.Add(RequestCorrelationContext.HeaderResourceIdentityUrl, Constants.SystemAssignedIdentity.DataPlaneUrl);
                    this.Request.Headers.Add(RequestCorrelationContext.HeaderResourceIdentityPrincipalid, Constants.SystemAssignedIdentity.PrincipalId);
                    this.Request.Headers.Add(ProviderConstants.ManagedIdentity.HeaderHomeTenantId, Constants.SystemAssignedIdentity.TenantId);

                    //Test Item
                    var response = this.AccessConnectorController.PatchAccessConnector(
                       Constants.Common.SubscriptionId,
                       Constants.Common.ResourceGroupName,
                       Constants.Connector.ConnectorName1,
                       this.AccessConnectorUpdate,
                       null, Constants.SystemAssignedIdentity.DataPlaneUrl, Constants.SystemAssignedIdentity.PrincipalId, Constants.SystemAssignedIdentity.TenantId).Result;

                    var responseContent = ConvertResponse(response);

                    // Assert
                    Assert.AreNotEqual(null, responseContent);
                    Assert.AreNotEqual(null, responseContent.Identity);
                    Assert.AreEqual(IdentityType.UserAssigned, responseContent.Identity.Type);
                    Assert.AreEqual((int)HttpStatusCode.OK, (response as ObjectResult).StatusCode);

                    Assert.AreNotEqual(null, responseContent.Identity.UserAssignedIdentities);
                    Assert.AreEqual(1, responseContent.Identity.UserAssignedIdentities.Count);
                    Assert.IsTrue(responseContent.Identity.UserAssignedIdentities.ContainsKey(Constants.UserAssignedIdentity.Identity3.ResourceId));

                    Assert.AreEqual(Constants.UserAssignedIdentity.Identity3.ObjectId,
                        responseContent.Identity.UserAssignedIdentities[Constants.UserAssignedIdentity.Identity3.ResourceId].PrincipalId);
                    Assert.AreEqual(Constants.UserAssignedIdentity.Identity3.ClientId,
                        responseContent.Identity.UserAssignedIdentities[Constants.UserAssignedIdentity.Identity3.ResourceId].ClientId);

                    this.AccessConnectorDataProvider.Verify(
                            o => o.FindAccessConnector(
                                It.Is<string>(subscription => subscription == Constants.Common.SubscriptionId),
                                It.Is<string>(rgName => rgName == Constants.Common.ResourceGroupName),
                                It.Is<string>(connectorName => connectorName == Constants.Connector.ConnectorName1)), Times.Once);

                    this.FrontdoorEngine.Verify(
                        o => o.CallDatabricksAsyncAccountApi(
                            It.IsAny<HttpMethod>(),
                            It.IsAny<Uri>(),
                            It.IsAny<string>(),
                            It.IsAny<string>(),
                            It.IsAny<CredMgrNotification>(),
                            It.IsAny<InsensitiveDictionary<string>>()), Times.Once);

                    this.AccessConnectorDataProvider.Verify(
                            o => o.SaveAccessConnector(
                                It.Is<AccessConnectorEntity>(entity =>
                                    entity.Properties.ProvisioningState == AccessConnectorState.Succeeded)));
                }
            }
        }

        [TestMethod("Remove two UA MI and add Two UA MI for an existing Access Connector via single PATCH request. This should Succeed")]
        public void RemovetwoAndAddtwoUserAssignedIdentitiesOnExistingAccessConnectorWithPatchCall()
        {
            using (new AppConfigSettingScope(this.ConfigurationSettings))
            {
                using (RequestCorrelationContext.NewCorrelationContextScope())
                {
                    //Set up
                    SetUpCorrelationContext(Constants.Common.Connector20230501APIVersion);

                    var accessConnectorEntity = new AccessConnectorEntity(
                    Constants.Connector.ConnectorName1,
                    Constants.Common.ResourceGroupName,
                    Constants.Common.SubscriptionId,
                    Constants.Common.Location,
                    tags: null)
                    {
                        Identity = new Identity()
                        {
                            Type = IdentityType.UserAssigned,
                            UserAssignedIdentities = new InsensitiveDictionary<UserAssignedIdentity>
                            {
                                { Constants.UserAssignedIdentity.Identity1.ResourceId,
                                    new UserAssignedIdentity(
                                        Constants.UserAssignedIdentity.Identity1.ObjectId,
                                        Constants.UserAssignedIdentity.Identity1.ClientId,
                                        Constants.UserAssignedIdentity.Identity1.TenantId
                                        )
                                },
                                { Constants.UserAssignedIdentity.Identity2.ResourceId,
                                    new UserAssignedIdentity(
                                        Constants.UserAssignedIdentity.Identity2.ObjectId,
                                        Constants.UserAssignedIdentity.Identity2.ClientId,
                                        Constants.UserAssignedIdentity.Identity2.TenantId
                                        )
                                }
                            }
                        },
                        IdentityUrl = Constants.SystemAssignedIdentity.DataPlaneUrl,
                        Properties = new AccessConnectorProperties()
                        {
                            ProvisioningState = AccessConnectorState.Succeeded
                        }
                    };

                    this.AccessConnectorDataProvider.Setup(o => o.FindAccessConnector(
                            It.Is<string>(subscription => subscription == Constants.Common.SubscriptionId),
                            It.Is<string>(rgName => rgName == Constants.Common.ResourceGroupName),
                            It.Is<string>(connectorName => connectorName == Constants.Connector.ConnectorName1)))
                        .Returns(Task.FromResult<AccessConnectorEntity>(accessConnectorEntity));

                    this.AccessConnectorUpdate.Identity = new Identity()
                    {
                        Type = IdentityType.UserAssigned,
                        UserAssignedIdentities = new InsensitiveDictionary<UserAssignedIdentity>
                        {
                            { Constants.UserAssignedIdentity.Identity1.ResourceId, null },
                            { Constants.UserAssignedIdentity.Identity2.ResourceId, null },
                            { Constants.UserAssignedIdentity.Identity3.ResourceId, new UserAssignedIdentity()},
                            { Constants.UserAssignedIdentity.Identity4.ResourceId, new UserAssignedIdentity()},
                        }
                    };

                    var userAssignedIdentity3 = new UserAssignedIdentityCredential
                    {
                        ResourceId = Constants.UserAssignedIdentity.Identity3.ResourceId,
                        ObjectId = Constants.UserAssignedIdentity.Identity3.ObjectId,
                        ClientId = Constants.UserAssignedIdentity.Identity3.ClientId
                    };

                    var userAssignedIdentity4 = new UserAssignedIdentityCredential
                    {
                        ResourceId = Constants.UserAssignedIdentity.Identity4.ResourceId,
                        ObjectId = Constants.UserAssignedIdentity.Identity4.ObjectId,
                        ClientId = Constants.UserAssignedIdentity.Identity4.ClientId
                    };

                    var identitiesCred = new IdentitiesCredential();
                    identitiesCred.UserAssignedIdentities = new List<UserAssignedIdentityCredential>()
                    { userAssignedIdentity3, userAssignedIdentity4 };

                    this.FrontdoorEngine.Setup(o => o.GetIdentitiesCredentials(
                        It.Is<string>(identityUrl => identityUrl == Constants.SystemAssignedIdentity.DataPlaneUrl),
                        It.IsAny<CredentialRequest>())).Returns(Task.FromResult<IdentitiesCredential>(identitiesCred));

                    this.Request.Headers.Add(RequestCorrelationContext.HeaderResourceIdentityUrl, Constants.SystemAssignedIdentity.DataPlaneUrl);
                    this.Request.Headers.Add(RequestCorrelationContext.HeaderResourceIdentityPrincipalid, Constants.SystemAssignedIdentity.PrincipalId);
                    this.Request.Headers.Add(ProviderConstants.ManagedIdentity.HeaderHomeTenantId, Constants.SystemAssignedIdentity.TenantId);

                    //Test Item

                    var response = this.AccessConnectorController.PatchAccessConnector(
                            Constants.Common.SubscriptionId,
                            Constants.Common.ResourceGroupName,
                            Constants.Connector.ConnectorName1,
                            this.AccessConnectorUpdate,
                            null, Constants.SystemAssignedIdentity.DataPlaneUrl, Constants.SystemAssignedIdentity.PrincipalId, Constants.SystemAssignedIdentity.TenantId).Result;

                    this.AccessConnectorDataProvider.Verify(
                            o => o.FindAccessConnector(
                                It.Is<string>(subscription => subscription == Constants.Common.SubscriptionId),
                                It.Is<string>(rgName => rgName == Constants.Common.ResourceGroupName),
                                It.Is<string>(connectorName => connectorName == Constants.Connector.ConnectorName1)), Times.Once);

                    var responseContent = ConvertResponse(response);

                    //Assert
                    Assert.AreNotEqual(null, responseContent);
                    Assert.AreNotEqual(null, responseContent.Identity);
                    Assert.AreEqual((int)HttpStatusCode.OK, (response as ObjectResult).StatusCode);
                    Assert.AreEqual(responseContent.Identity.Type, IdentityType.UserAssigned);
                    Assert.AreEqual(2, responseContent.Identity.UserAssignedIdentities.Count);
                    Assert.IsFalse(responseContent.Identity.UserAssignedIdentities.ContainsKey(Constants.UserAssignedIdentity.Identity1.ResourceId));
                    Assert.IsFalse(responseContent.Identity.UserAssignedIdentities.ContainsKey(Constants.UserAssignedIdentity.Identity2.ResourceId));
                    Assert.IsTrue(responseContent.Identity.UserAssignedIdentities.ContainsKey(Constants.UserAssignedIdentity.Identity3.ResourceId));
                    Assert.IsTrue(responseContent.Identity.UserAssignedIdentities.ContainsKey(Constants.UserAssignedIdentity.Identity4.ResourceId));
                }
            }
        }

        [TestMethod("Remove two UA MI and add only one UA MI for an existing Access Connector via single PATCH request. This should Succeed")]
        public void RemovetwoAndAddOneUserAssignedIdentitiesOnExistingAccessConnectorWithPatchCall()
        {
            using (new AppConfigSettingScope(this.ConfigurationSettings))
            {
                using (RequestCorrelationContext.NewCorrelationContextScope())
                {
                    //Set up
                    SetUpCorrelationContext(Constants.Common.Connector20230501APIVersion);

                    var accessConnectorEntity = new AccessConnectorEntity(
                    Constants.Connector.ConnectorName1,
                    Constants.Common.ResourceGroupName,
                    Constants.Common.SubscriptionId,
                    Constants.Common.Location,
                    tags: null)
                    {
                        Identity = new Identity()
                        {
                            Type = IdentityType.UserAssigned,
                            UserAssignedIdentities = new InsensitiveDictionary<UserAssignedIdentity>
                            {
                                { Constants.UserAssignedIdentity.Identity1.ResourceId,
                                    new UserAssignedIdentity(
                                        Constants.UserAssignedIdentity.Identity1.ObjectId,
                                        Constants.UserAssignedIdentity.Identity1.ClientId,
                                        Constants.UserAssignedIdentity.Identity1.TenantId
                                        )
                                },
                                { Constants.UserAssignedIdentity.Identity2.ResourceId,
                                    new UserAssignedIdentity(
                                        Constants.UserAssignedIdentity.Identity2.ObjectId,
                                        Constants.UserAssignedIdentity.Identity2.ClientId,
                                        Constants.UserAssignedIdentity.Identity2.TenantId
                                        )
                                }
                            }
                        },
                        IdentityUrl = Constants.SystemAssignedIdentity.DataPlaneUrl,
                        Properties = new AccessConnectorProperties()
                        {
                            ProvisioningState = AccessConnectorState.Succeeded
                        }
                    };

                    this.AccessConnectorDataProvider.Setup(o => o.FindAccessConnector(
                            It.Is<string>(subscription => subscription == Constants.Common.SubscriptionId),
                            It.Is<string>(rgName => rgName == Constants.Common.ResourceGroupName),
                            It.Is<string>(connectorName => connectorName == Constants.Connector.ConnectorName1)))
                        .Returns(Task.FromResult<AccessConnectorEntity>(accessConnectorEntity));

                    this.AccessConnectorUpdate.Identity = new Identity()
                    {
                        Type = IdentityType.UserAssigned,
                        UserAssignedIdentities = new InsensitiveDictionary<UserAssignedIdentity>
                        {
                            { Constants.UserAssignedIdentity.Identity1.ResourceId, null },
                            { Constants.UserAssignedIdentity.Identity2.ResourceId, null },
                            { Constants.UserAssignedIdentity.Identity3.ResourceId, new UserAssignedIdentity() },
                        }
                    };

                    var userAssignedIdentity3 = new UserAssignedIdentityCredential
                    {
                        ResourceId = Constants.UserAssignedIdentity.Identity3.ResourceId,
                        ObjectId = Constants.UserAssignedIdentity.Identity3.ObjectId,
                        ClientId = Constants.UserAssignedIdentity.Identity3.ClientId
                    };

                    var identitiesCred = new IdentitiesCredential();

                    identitiesCred.UserAssignedIdentities = new List<UserAssignedIdentityCredential>()
                    { userAssignedIdentity3 };

                    this.FrontdoorEngine.Setup(o => o.GetIdentitiesCredentials(
                        It.Is<string>(identityUrl => identityUrl == Constants.SystemAssignedIdentity.DataPlaneUrl),
                        It.IsAny<CredentialRequest>())).Returns(Task.FromResult<IdentitiesCredential>(identitiesCred));

                    this.Request.Headers.Add(RequestCorrelationContext.HeaderResourceIdentityUrl, Constants.SystemAssignedIdentity.DataPlaneUrl);
                    this.Request.Headers.Add(RequestCorrelationContext.HeaderResourceIdentityPrincipalid, Constants.SystemAssignedIdentity.PrincipalId);
                    this.Request.Headers.Add(ProviderConstants.ManagedIdentity.HeaderHomeTenantId, Constants.SystemAssignedIdentity.TenantId);

                    //Test Item
                    var response = this.AccessConnectorController.PatchAccessConnector(
                       Constants.Common.SubscriptionId,
                       Constants.Common.ResourceGroupName,
                       Constants.Connector.ConnectorName1,
                       this.AccessConnectorUpdate,
                       null, Constants.SystemAssignedIdentity.DataPlaneUrl, Constants.SystemAssignedIdentity.PrincipalId, Constants.SystemAssignedIdentity.TenantId).Result;

                    var responseContent = ConvertResponse(response);

                    // Assert
                    Assert.AreNotEqual(null, responseContent);
                    Assert.AreNotEqual(null, responseContent.Identity);
                    Assert.AreEqual(IdentityType.UserAssigned, responseContent.Identity.Type);
                    Assert.AreEqual((int)HttpStatusCode.OK, (response as ObjectResult).StatusCode);

                    Assert.AreNotEqual(null, responseContent.Identity.UserAssignedIdentities);
                    Assert.AreEqual(1, responseContent.Identity.UserAssignedIdentities.Count);
                    Assert.IsTrue(responseContent.Identity.UserAssignedIdentities.ContainsKey(Constants.UserAssignedIdentity.Identity3.ResourceId));

                    Assert.AreEqual(Constants.UserAssignedIdentity.Identity3.ObjectId,
                        responseContent.Identity.UserAssignedIdentities[Constants.UserAssignedIdentity.Identity3.ResourceId].PrincipalId);
                    Assert.AreEqual(Constants.UserAssignedIdentity.Identity3.ClientId,
                        responseContent.Identity.UserAssignedIdentities[Constants.UserAssignedIdentity.Identity3.ResourceId].ClientId);

                    this.AccessConnectorDataProvider.Verify(
                            o => o.FindAccessConnector(
                                It.Is<string>(subscription => subscription == Constants.Common.SubscriptionId),
                                It.Is<string>(rgName => rgName == Constants.Common.ResourceGroupName),
                                It.Is<string>(connectorName => connectorName == Constants.Connector.ConnectorName1)), Times.Once);

                    this.FrontdoorEngine.Verify(
                        o => o.CallDatabricksAsyncAccountApi(
                        It.IsAny<HttpMethod>(),
                        It.IsAny<Uri>(),
                        It.IsAny<string>(),
                        It.IsAny<string>(),
                        It.IsAny<CredMgrNotification>(),
                        It.IsAny<InsensitiveDictionary<string>>()), Times.Once);

                    this.AccessConnectorDataProvider.Verify(
                        o => o.SaveAccessConnector(
                            It.Is<AccessConnectorEntity>(entity =>
                                entity.Properties.ProvisioningState == AccessConnectorState.Succeeded)));
                }
            }
        }

        [TestMethod("Patching UA MI with 2022-04-01-preview apiversion.this should Fail")]
        public void UpdatingAccessConnectorWithOldApiVersionviaPatchCall()
        {
            using (new AppConfigSettingScope(this.ConfigurationSettings))
            {
                using (RequestCorrelationContext.NewCorrelationContextScope())
                {
                    //Set up
                    SetUpCorrelationContext(Constants.Common.ConnectorUserAssignedOldApiVersion);

                    var accessConnectorEntity = new AccessConnectorEntity(
                    Constants.Connector.ConnectorName1,
                    Constants.Common.ResourceGroupName,
                    Constants.Common.SubscriptionId,
                    Constants.Common.Location,
                    tags: null)
                    {
                        Identity = new Identity()
                        {
                            Type = IdentityType.UserAssigned,
                            UserAssignedIdentities = new InsensitiveDictionary<UserAssignedIdentity>
                            {
                                { Constants.UserAssignedIdentity.Identity1.ResourceId,
                                    new UserAssignedIdentity(
                                        Constants.UserAssignedIdentity.Identity1.ObjectId,
                                        Constants.UserAssignedIdentity.Identity1.ClientId,
                                        Constants.UserAssignedIdentity.Identity1.TenantId
                                        )
                                }
                            }
                        },
                        IdentityUrl = Constants.SystemAssignedIdentity.DataPlaneUrl,
                        Properties = new AccessConnectorProperties()
                        {
                            ProvisioningState = AccessConnectorState.Succeeded
                        }
                    };

                    this.AccessConnectorDataProvider.Setup(o => o.FindAccessConnector(
                            It.Is<string>(subscription => subscription == Constants.Common.SubscriptionId),
                            It.Is<string>(rgName => rgName == Constants.Common.ResourceGroupName),
                            It.Is<string>(connectorName => connectorName == Constants.Connector.ConnectorName1)))
                        .Returns(Task.FromResult<AccessConnectorEntity>(accessConnectorEntity));

                    this.AccessConnectorUpdate.Identity = new Identity()
                    {
                        Type = IdentityType.UserAssigned,
                        UserAssignedIdentities = new InsensitiveDictionary<UserAssignedIdentity>
                        {
                            { Constants.UserAssignedIdentity.Identity1.ResourceId, null },
                            { Constants.UserAssignedIdentity.Identity2.ResourceId, new UserAssignedIdentity() },
                        }
                    };

                    var userAssignedIdentity2 = new UserAssignedIdentityCredential
                    {
                        ResourceId = Constants.UserAssignedIdentity.Identity2.ResourceId,
                        ObjectId = Constants.UserAssignedIdentity.Identity2.ObjectId,
                        ClientId = Constants.UserAssignedIdentity.Identity2.ClientId
                    };

                    var identitiesCred = new IdentitiesCredential();

                    identitiesCred.UserAssignedIdentities = new List<UserAssignedIdentityCredential>()
                    { userAssignedIdentity2 };

                    this.FrontdoorEngine.Setup(o => o.GetIdentitiesCredentials(
                        It.Is<string>(identityUrl => identityUrl == Constants.SystemAssignedIdentity.DataPlaneUrl),
                        It.IsAny<CredentialRequest>())).Returns(Task.FromResult<IdentitiesCredential>(identitiesCred));

                    this.Request.Headers.Add(RequestCorrelationContext.HeaderResourceIdentityUrl, Constants.SystemAssignedIdentity.DataPlaneUrl);
                    this.Request.Headers.Add(RequestCorrelationContext.HeaderResourceIdentityPrincipalid, Constants.SystemAssignedIdentity.PrincipalId);
                    this.Request.Headers.Add(ProviderConstants.ManagedIdentity.HeaderHomeTenantId, Constants.SystemAssignedIdentity.TenantId);

                    //Test Item
                    try
                    {
                        var response = this.AccessConnectorController.PatchAccessConnector(
                            Constants.Common.SubscriptionId,
                            Constants.Common.ResourceGroupName,
                            Constants.Connector.ConnectorName1,
                            this.AccessConnectorUpdate,
                            null, Constants.SystemAssignedIdentity.DataPlaneUrl, Constants.SystemAssignedIdentity.PrincipalId, Constants.SystemAssignedIdentity.TenantId).Result;
                    }
                    catch (Exception ex)
                    {
                        Assert.AreEqual(ex.InnerException.Message, ErrorResponseMessages.UserAssignedIdentityNotSupportedForCurrentApiVersion.ToLocalizedMessage());
                    }
                    finally
                    {
                        //Assert
                        this.AccessConnectorDataProvider.Verify(
                            o => o.FindAccessConnector(
                                It.Is<string>(subscription => subscription == Constants.Common.SubscriptionId),
                                It.Is<string>(rgName => rgName == Constants.Common.ResourceGroupName),
                                It.Is<string>(connectorName => connectorName == Constants.Connector.ConnectorName1)), Times.Never);

                        this.FrontdoorEngine.Verify(
                            o => o.CallDatabricksAsyncAccountApi(
                            It.IsAny<HttpMethod>(),
                            It.IsAny<Uri>(),
                            It.IsAny<string>(),
                            It.IsAny<string>(),
                            It.IsAny<CredMgrNotification>(),
                            It.IsAny<InsensitiveDictionary<string>>()), Times.Never);

                        this.AccessConnectorDataProvider.Verify(
                            o => o.SaveAccessConnector(
                                It.IsAny<AccessConnectorEntity>()), Times.Never);
                    }
                }
            }
        }

        [TestMethod("Remove UA MI which does not exist with Patch.This should fail")]
        public void RemoveUAMIWhichDoesNotExistsWithPatchCall()
        {
            using (new AppConfigSettingScope(this.ConfigurationSettings))
            {
                using (RequestCorrelationContext.NewCorrelationContextScope())
                {
                    //Set up
                    SetUpCorrelationContext(Constants.Common.Connector20230501APIVersion);

                    var accessConnectorEntity = new AccessConnectorEntity(
                    Constants.Connector.ConnectorName1,
                    Constants.Common.ResourceGroupName,
                    Constants.Common.SubscriptionId,
                    Constants.Common.Location,
                    tags: null,
                    identity: null)
                    {
                        Properties = new AccessConnectorProperties()
                        {
                            ProvisioningState = AccessConnectorState.Succeeded
                        }
                    };

                    this.AccessConnectorDataProvider.Setup(o => o.FindAccessConnector(
                            It.Is<string>(subscription => subscription == Constants.Common.SubscriptionId),
                            It.Is<string>(rgName => rgName == Constants.Common.ResourceGroupName),
                            It.Is<string>(connectorName => connectorName == Constants.Connector.ConnectorName1)))
                        .Returns(Task.FromResult<AccessConnectorEntity>(accessConnectorEntity));

                    this.AccessConnectorUpdate.Identity = new Identity()
                    {
                        Type = IdentityType.UserAssigned,
                        UserAssignedIdentities = new InsensitiveDictionary<UserAssignedIdentity>
                        {
                            { Constants.UserAssignedIdentity.Identity1.ResourceId, null },
                        }
                    };

                    //Test Item
                    try
                    {
                        var response = this.AccessConnectorController.PatchAccessConnector(
                            Constants.Common.SubscriptionId,
                            Constants.Common.ResourceGroupName,
                            Constants.Connector.ConnectorName1,
                            this.AccessConnectorUpdate,
                            null, "", "", "").Result;

                        Assert.IsTrue(false);
                    }
                    catch (Exception ex)
                    {
                        var expectedMsg = $"UserAssigned Identity: \'{Constants.UserAssignedIdentity.Identity1.ResourceId}\' doesn't exist on existing accessConnector.";
                        Assert.AreEqual(ex.InnerException.Message, expectedMsg);
                    }
                    finally
                    {
                        //Assert
                        this.AccessConnectorDataProvider.Verify(
                            o => o.FindAccessConnector(
                                It.Is<string>(subscription => subscription == Constants.Common.SubscriptionId),
                                It.Is<string>(rgName => rgName == Constants.Common.ResourceGroupName),
                                It.Is<string>(connectorName => connectorName == Constants.Connector.ConnectorName1)), Times.Once);

                        this.FrontdoorEngine.Verify(
                            o => o.CallDatabricksAsyncAccountApi(
                            It.IsAny<HttpMethod>(),
                            It.IsAny<Uri>(),
                            It.IsAny<string>(),
                            It.IsAny<string>(),
                            It.IsAny<CredMgrNotification>(),
                            It.IsAny<InsensitiveDictionary<string>>()), Times.Never);

                        this.AccessConnectorDataProvider.Verify(
                            o => o.SaveAccessConnector(
                                It.IsAny<AccessConnectorEntity>()), Times.Never);
                    }
                }
            }
        }

        [TestMethod("Patch AC which doesnot exist.This should fail")]
        public void PatchAccessConnectorWhichDoesnotExist()
        {
            using (new AppConfigSettingScope(this.ConfigurationSettings))
            {
                using (RequestCorrelationContext.NewCorrelationContextScope())
                {
                    //Set up
                    SetUpCorrelationContext(Constants.Common.Connector20230501APIVersion);

                    this.AccessConnectorDataProvider.Setup(o => o.FindAccessConnector(
                            It.Is<string>(subscription => subscription == Constants.Common.SubscriptionId),
                            It.Is<string>(rgName => rgName == Constants.Common.ResourceGroupName),
                            It.Is<string>(connectorName => connectorName == Constants.Connector.ConnectorName1)))
                        .Returns(Task.FromResult<AccessConnectorEntity>(null));

                    //Test Item
                    try
                    {
                        var response = this.AccessConnectorController.PatchAccessConnector(
                            Constants.Common.SubscriptionId,
                            Constants.Common.ResourceGroupName,
                            Constants.Connector.ConnectorName1,
                            this.AccessConnectorUpdate,
                            null, "", "", "").Result;
                        Assert.IsTrue(false);
                    }
                    catch (Exception ex)
                    {
                        Assert.AreEqual(ex.InnerException.Message, ErrorResponseMessages.AccessConnectorNotFound.ToLocalizedMessage(Constants.Connector.ConnectorName1));
                    }
                    finally
                    {
                        //Assert
                        this.AccessConnectorDataProvider.Verify(
                            o => o.FindAccessConnector(
                                It.Is<string>(subscription => subscription == Constants.Common.SubscriptionId),
                                It.Is<string>(rgName => rgName == Constants.Common.ResourceGroupName),
                                It.Is<string>(connectorName => connectorName == Constants.Connector.ConnectorName1)), Times.Once);

                        this.FrontdoorEngine.Verify(
                            o => o.CallDatabricksAsyncAccountApi(
                            It.IsAny<HttpMethod>(),
                            It.IsAny<Uri>(),
                            It.IsAny<string>(),
                            It.IsAny<string>(),
                            It.IsAny<CredMgrNotification>(),
                            It.IsAny<InsensitiveDictionary<string>>()), Times.Never);

                        this.AccessConnectorDataProvider.Verify(
                            o => o.SaveAccessConnector(
                                It.IsAny<AccessConnectorEntity>()), Times.Never);
                    }
                }
            }
        }

        [TestMethod("Adds a workspace to Non Empty accessConnector referred by list")]
        public void AddWstoNonEmptyReferedbyAccessConnector()
        {
            //Set up
            var accessConnectorEntity = new AccessConnectorEntity(
                Constants.Connector.ConnectorName1,
                Constants.Common.ResourceGroupName,
                Constants.Common.SubscriptionId,
                Constants.Common.Location,
                tags: null,
                identity: null)
            {
                Properties = new AccessConnectorProperties()
                {
                    ProvisioningState = AccessConnectorState.Succeeded,
                    ReferedBy = new List<string> {"test"}
                }
            };

            this.AccessConnectorDataProvider.Setup(o => o.FindAccessConnector(
                    It.Is<string>(subscription => subscription == Constants.Common.SubscriptionId),
                    It.Is<string>(rgName => rgName == Constants.Common.ResourceGroupName),
                    It.Is<string>(connectorName => connectorName == Constants.Connector.ConnectorName1)))
                .Returns(Task.FromResult<AccessConnectorEntity>(accessConnectorEntity));

            AccessConnectorReferedByUpdate updateBody = new AccessConnectorReferedByUpdate()
            {
                WorkspaceId = "test2",
                Operation = AccessConnectorOperation.Add
            };

            //Test Item

            var response = this.AccessConnectorController.UpdateReferedbyAccessConnector(
            Constants.Common.SubscriptionId,
            Constants.Common.ResourceGroupName,
            Constants.Connector.ConnectorName1,
            updateBody).Result;

            Assert.AreEqual((response as StatusCodeResult).StatusCode, (int)HttpStatusCode.OK);
        }

        [TestMethod("Adds Existing workspace to Non Empty accessConnector referred by list")]
        public void AddExisitngWstoNonEmptyReferedbyAccessConnector()
        {
            //Set up
            var accessConnectorEntity = new AccessConnectorEntity(
                Constants.Connector.ConnectorName1,
                Constants.Common.ResourceGroupName,
                Constants.Common.SubscriptionId,
                Constants.Common.Location,
                tags: null,
                identity: null)
            {
                Properties = new AccessConnectorProperties()
                {
                    ProvisioningState = AccessConnectorState.Succeeded,
                    ReferedBy = new List<string> { "test" }
                }
            };

            this.AccessConnectorDataProvider.Setup(o => o.FindAccessConnector(
                    It.Is<string>(subscription => subscription == Constants.Common.SubscriptionId),
                    It.Is<string>(rgName => rgName == Constants.Common.ResourceGroupName),
                    It.Is<string>(connectorName => connectorName == Constants.Connector.ConnectorName1)))
                .Returns(Task.FromResult<AccessConnectorEntity>(accessConnectorEntity));

            AccessConnectorReferedByUpdate updateBody = new AccessConnectorReferedByUpdate()
            {
                WorkspaceId = "test",
                Operation = AccessConnectorOperation.Add
            };

            //Test Item
            var response = this.AccessConnectorController.UpdateReferedbyAccessConnector(
            Constants.Common.SubscriptionId,
            Constants.Common.ResourceGroupName,
            Constants.Connector.ConnectorName1,
            updateBody).Result;

            Assert.AreEqual((response as StatusCodeResult).StatusCode, (int)HttpStatusCode.OK);
        }

        [TestMethod("Add a Ws from Empty accessConnector referedby list")]
        public void AddWstoEmptyReferedbyAccessConnector()
        {
            //Set up
            var accessConnectorEntity = new AccessConnectorEntity(
                Constants.Connector.ConnectorName1,
                Constants.Common.ResourceGroupName,
                Constants.Common.SubscriptionId,
                Constants.Common.Location,
                tags: null,
                identity: null)
            {
                Properties = new AccessConnectorProperties()
                {
                    ProvisioningState = AccessConnectorState.Succeeded,
                    ReferedBy = null
                }
            };

            this.AccessConnectorDataProvider.Setup(o => o.FindAccessConnector(
                    It.Is<string>(subscription => subscription == Constants.Common.SubscriptionId),
                    It.Is<string>(rgName => rgName == Constants.Common.ResourceGroupName),
                    It.Is<string>(connectorName => connectorName == Constants.Connector.ConnectorName1)))
                .Returns(Task.FromResult<AccessConnectorEntity>(accessConnectorEntity));

            AccessConnectorReferedByUpdate updateBody = new AccessConnectorReferedByUpdate()
            {
                WorkspaceId = "test",
                Operation = AccessConnectorOperation.Add
            };

            //Test Item
            //Test Item
            var response = this.AccessConnectorController.UpdateReferedbyAccessConnector(
            Constants.Common.SubscriptionId,
            Constants.Common.ResourceGroupName,
            Constants.Connector.ConnectorName1,
            updateBody).Result;

            Assert.AreEqual((response as StatusCodeResult).StatusCode, (int)HttpStatusCode.OK);
        }

        [TestMethod("Remove Existing Ws from Non Empty accessConnector referedby list")]
        public void RemoveExisitngWsfromNonEmptyReferedbyAccessConnector()
        {
            //Set up
            var accessConnectorEntity = new AccessConnectorEntity(
                Constants.Connector.ConnectorName1,
                Constants.Common.ResourceGroupName,
                Constants.Common.SubscriptionId,
                Constants.Common.Location,
                tags: null,
                identity: null)
            {
                Properties = new AccessConnectorProperties()
                {
                    ProvisioningState = AccessConnectorState.Succeeded,
                    ReferedBy = new List<string> { "test01", "test" }
                }
            };

            this.AccessConnectorDataProvider.Setup(o => o.FindAccessConnector(
                    It.Is<string>(subscription => subscription == Constants.Common.SubscriptionId),
                    It.Is<string>(rgName => rgName == Constants.Common.ResourceGroupName),
                    It.Is<string>(connectorName => connectorName == Constants.Connector.ConnectorName1)))
                .Returns(Task.FromResult<AccessConnectorEntity>(accessConnectorEntity));

            AccessConnectorReferedByUpdate updateBody = new AccessConnectorReferedByUpdate()
            {
                WorkspaceId = "test01",
                Operation = AccessConnectorOperation.Remove
            };

            //Test Item
            var response = this.AccessConnectorController.UpdateReferedbyAccessConnector(
            Constants.Common.SubscriptionId,
            Constants.Common.ResourceGroupName,
            Constants.Connector.ConnectorName1,
            updateBody).Result;

            Assert.AreEqual((response as StatusCodeResult).StatusCode, (int)HttpStatusCode.OK);
        }

        [TestMethod("Remove a NonExisting Ws from Non Empty accessConnector referedby list")]
        public void RemoveNonExistingWsfromNonEmptyReferedbyAccessConnector()
        {
            //Set up
            var accessConnectorEntity = new AccessConnectorEntity(
                Constants.Connector.ConnectorName1,
                Constants.Common.ResourceGroupName,
                Constants.Common.SubscriptionId,
                Constants.Common.Location,
                tags: null,
                identity: null)
            {
                Properties = new AccessConnectorProperties()
                {
                    ProvisioningState = AccessConnectorState.Succeeded,
                    ReferedBy = new List<string> { "test01", "test" }
                }
            };

            this.AccessConnectorDataProvider.Setup(o => o.FindAccessConnector(
                    It.Is<string>(subscription => subscription == Constants.Common.SubscriptionId),
                    It.Is<string>(rgName => rgName == Constants.Common.ResourceGroupName),
                    It.Is<string>(connectorName => connectorName == Constants.Connector.ConnectorName1)))
                .Returns(Task.FromResult<AccessConnectorEntity>(accessConnectorEntity));

            AccessConnectorReferedByUpdate updateBody = new AccessConnectorReferedByUpdate()
            {
                WorkspaceId = "test02",
                Operation = AccessConnectorOperation.Remove
            };

            //Test Item
            var response = this.AccessConnectorController.UpdateReferedbyAccessConnector(
            Constants.Common.SubscriptionId,
            Constants.Common.ResourceGroupName,
            Constants.Connector.ConnectorName1,
            updateBody).Result;

            Assert.AreEqual((response as StatusCodeResult).StatusCode, (int)HttpStatusCode.OK);
        }

        [TestMethod("Remove a Ws from Empty accessConnector referedby list")]
        public void RemoveWsfromEmptyReferedbyAccessConnector()
        {
            //Set up
            var accessConnectorEntity = new AccessConnectorEntity(
                Constants.Connector.ConnectorName1,
                Constants.Common.ResourceGroupName,
                Constants.Common.SubscriptionId,
                Constants.Common.Location,
                tags: null,
                identity: null)
            {
                Properties = new AccessConnectorProperties()
                {
                    ProvisioningState = AccessConnectorState.Succeeded,
                    ReferedBy = null
                }
            };

            this.AccessConnectorDataProvider.Setup(o => o.FindAccessConnector(
                    It.Is<string>(subscription => subscription == Constants.Common.SubscriptionId),
                    It.Is<string>(rgName => rgName == Constants.Common.ResourceGroupName),
                    It.Is<string>(connectorName => connectorName == Constants.Connector.ConnectorName1)))
                .Returns(Task.FromResult<AccessConnectorEntity>(accessConnectorEntity));

            AccessConnectorReferedByUpdate updateBody = new AccessConnectorReferedByUpdate()
            {
                WorkspaceId = "test",
                Operation = AccessConnectorOperation.Remove
            };

            //Test Item
            var response = this.AccessConnectorController.UpdateReferedbyAccessConnector(
            Constants.Common.SubscriptionId,
            Constants.Common.ResourceGroupName,
            Constants.Connector.ConnectorName1,
            updateBody).Result;

            Assert.AreEqual((response as StatusCodeResult).StatusCode, (int)HttpStatusCode.OK);
        }
        #endregion

        #region Get accessConnector Tests

        [TestMethod("Get accessConnector returns notfound for a nonexisting connector")]
        public void TestGetOnNonExistingAccessConnector()
        {
            //Set up
            this.AccessConnectorDataProvider.Setup(o => o.FindAccessConnector(
                It.Is<string>(subscription => subscription == Constants.Common.SubscriptionId),
                It.Is<string>(rgName => rgName == Constants.Common.ResourceGroupName),
                It.Is<string>(connectorName => connectorName == Constants.Connector.ConnectorName1)))
                .Returns(Task.FromResult<AccessConnectorEntity>(null));

            //Test Item
            var response = this.AccessConnectorController.GetAccessConnector(
                Constants.Common.SubscriptionId,
                Constants.Common.ResourceGroupName,
                Constants.Connector.ConnectorName1).Result;

            //Assert
            Assert.AreNotEqual(null, response);
            Assert.AreEqual((int)HttpStatusCode.NotFound, (response as ObjectResult).StatusCode);

            var responseContent = ConvertResponse(response);
            Assert.IsNull(responseContent);
        }

        [TestMethod("Get accessConnector returns existing connector with system assigned identity")]
        public void TestGetOnExistingAccessConnectorWithSystemAssignedIdentity()
        {
            //Set up
            var accessConnectorEntity = new AccessConnectorEntity(
                Constants.Connector.ConnectorName1,
                Constants.Common.ResourceGroupName,
                Constants.Common.SubscriptionId,
                Constants.Common.Location,
                tags: null)
            {
                Identity = new Identity()
                {
                    Type = IdentityType.SystemAssigned,
                    PrincipalId = Constants.SystemAssignedIdentity.PrincipalId,
                    TenantId = Constants.SystemAssignedIdentity.TenantId
                },
                IdentityUrl = Constants.SystemAssignedIdentity.DataPlaneUrl,
                Properties = new AccessConnectorProperties()
                {
                    ProvisioningState = AccessConnectorState.Succeeded
                }
            };

            this.AccessConnectorDataProvider.Setup(o => o.FindAccessConnector(
                    It.Is<string>(subscription => subscription == Constants.Common.SubscriptionId),
                    It.Is<string>(rgName => rgName == Constants.Common.ResourceGroupName),
                    It.Is<string>(connectorName => connectorName == Constants.Connector.ConnectorName1)))
                .Returns(Task.FromResult<AccessConnectorEntity>(accessConnectorEntity));

            //Test Item
            var response = this.AccessConnectorController.GetAccessConnector(
                Constants.Common.SubscriptionId,
                Constants.Common.ResourceGroupName,
                Constants.Connector.ConnectorName1).Result;

            //Assert
            Assert.AreNotEqual(null, response);
            Assert.AreEqual((int)HttpStatusCode.OK, (response as ObjectResult).StatusCode);

            var responseContent = ConvertResponse(response);

            Assert.AreNotEqual(null, responseContent);
            Assert.AreNotEqual(null, responseContent.Identity);
            Assert.AreEqual(IdentityType.SystemAssigned, responseContent.Identity.Type);
            Assert.AreEqual(Constants.SystemAssignedIdentity.PrincipalId, responseContent.Identity.PrincipalId);
            Assert.AreEqual(Constants.SystemAssignedIdentity.TenantId, responseContent.Identity.TenantId);
        }

        [TestMethod("Get accessConnector returns existing connector with system assigned & user assigned identity using 2023-05-01 api version")]
        public void TestGetOnExistingAccessConnectorWithSystemAssignedAndUserAssignedIdentityWith20230501ApiVersion()
        {
            using (RequestCorrelationContext.NewCorrelationContextScope())
            {
                //Set up
                SetUpCorrelationContext(Constants.Common.Connector20230501APIVersion);

                var referedBy = new List<String> { Constants.Common.FullyQualifiedWorkspaceId };
                var accessConnectorEntity = new AccessConnectorEntity(
                    Constants.Connector.ConnectorName1,
                    Constants.Common.ResourceGroupName,
                    Constants.Common.SubscriptionId,
                    Constants.Common.Location,
                    tags: null)
                {
                    Identity = new Identity()
                    {
                        Type = IdentityType.SystemAssigned | IdentityType.UserAssigned,
                        PrincipalId = Constants.SystemAssignedIdentity.PrincipalId,
                        TenantId = Constants.SystemAssignedIdentity.TenantId,
                        UserAssignedIdentities = new InsensitiveDictionary<UserAssignedIdentity>
                    {
                        { Constants.UserAssignedIdentity.Identity1.ResourceId,
                            new UserAssignedIdentity(
                                Constants.UserAssignedIdentity.Identity1.ObjectId,
                                Constants.UserAssignedIdentity.Identity1.ClientId,
                                Constants.UserAssignedIdentity.Identity1.TenantId
                                )
                        },
                        { Constants.UserAssignedIdentity.Identity2.ResourceId,
                            new UserAssignedIdentity(
                                Constants.UserAssignedIdentity.Identity2.ObjectId,
                                Constants.UserAssignedIdentity.Identity2.ClientId,
                                Constants.UserAssignedIdentity.Identity2.TenantId
                                )
                        }
                    }
                    },
                    IdentityUrl = Constants.SystemAssignedIdentity.DataPlaneUrl,
                    Properties = new AccessConnectorProperties()
                    {
                        ProvisioningState = AccessConnectorState.Succeeded,
                        ReferedBy = referedBy
                    }
                };

                this.AccessConnectorDataProvider.Setup(o => o.FindAccessConnector(
                        It.Is<string>(subscription => subscription == Constants.Common.SubscriptionId),
                        It.Is<string>(rgName => rgName == Constants.Common.ResourceGroupName),
                        It.Is<string>(connectorName => connectorName == Constants.Connector.ConnectorName1)))
                    .Returns(Task.FromResult<AccessConnectorEntity>(accessConnectorEntity));

                //Test Item
                var response = this.AccessConnectorController.GetAccessConnector(
                    Constants.Common.SubscriptionId,
                    Constants.Common.ResourceGroupName,
                    Constants.Connector.ConnectorName1).Result;

                //Assert
                Assert.AreNotEqual(null, response);
                Assert.AreEqual((int)HttpStatusCode.OK, (response as ObjectResult).StatusCode);

                var responseContent = ConvertResponse(response);

                Assert.AreNotEqual(null, responseContent);
                Assert.AreNotEqual(null, responseContent.Identity);

                Assert.AreNotEqual(null, responseContent.Properties);
                Assert.AreEqual(null, responseContent.Properties.ReferedBy);

                Assert.AreEqual(IdentityType.SystemAssigned | IdentityType.UserAssigned, responseContent.Identity.Type);
                Assert.AreEqual(Constants.SystemAssignedIdentity.PrincipalId, responseContent.Identity.PrincipalId);
                Assert.AreEqual(Constants.SystemAssignedIdentity.TenantId, responseContent.Identity.TenantId);

                Assert.AreNotEqual(null, responseContent.Identity.UserAssignedIdentities);
                Assert.AreEqual(2, responseContent.Identity.UserAssignedIdentities.Count);
                Assert.IsTrue(responseContent.Identity.UserAssignedIdentities.ContainsKey(Constants.UserAssignedIdentity.Identity1.ResourceId));

                Assert.AreEqual(Constants.UserAssignedIdentity.Identity1.ObjectId,
                    responseContent.Identity.UserAssignedIdentities[Constants.UserAssignedIdentity.Identity1.ResourceId].PrincipalId);
                Assert.AreEqual(Constants.UserAssignedIdentity.Identity1.ClientId,
                    responseContent.Identity.UserAssignedIdentities[Constants.UserAssignedIdentity.Identity1.ResourceId].ClientId);

                Assert.IsTrue(responseContent.Identity.UserAssignedIdentities.ContainsKey(Constants.UserAssignedIdentity.Identity2.ResourceId));

                Assert.AreEqual(Constants.UserAssignedIdentity.Identity2.ObjectId,
                    responseContent.Identity.UserAssignedIdentities[Constants.UserAssignedIdentity.Identity2.ResourceId].PrincipalId);
                Assert.AreEqual(Constants.UserAssignedIdentity.Identity2.ClientId,
                    responseContent.Identity.UserAssignedIdentities[Constants.UserAssignedIdentity.Identity2.ResourceId].ClientId);
            }
        }

        [TestMethod("Get accessConnector returns existing connector with system assigned & user assigned identity using 2022-04-01-preview api version")]
        public void TestGetOnExistingAccessConnectorWithSystemAssignedAndUserAssignedIdentityWith20220401PreviewApiVersion()
        {
            using (RequestCorrelationContext.NewCorrelationContextScope())
            {

                //Set up
                SetUpCorrelationContext(Constants.Common.ConnectorSystemAssignedApiVersion);

                var referedBy = new List<String> { Constants.Common.FullyQualifiedWorkspaceId };
                var accessConnectorEntity = new AccessConnectorEntity(
                Constants.Connector.ConnectorName1,
                Constants.Common.ResourceGroupName,
                Constants.Common.SubscriptionId,
                Constants.Common.Location,
                tags: null)
                {
                    Identity = new Identity()
                    {
                        Type = IdentityType.SystemAssigned | IdentityType.UserAssigned,
                        PrincipalId = Constants.SystemAssignedIdentity.PrincipalId,
                        TenantId = Constants.SystemAssignedIdentity.TenantId,
                        UserAssignedIdentities = new InsensitiveDictionary<UserAssignedIdentity>
                        {
                            { Constants.UserAssignedIdentity.Identity1.ResourceId,
                                new UserAssignedIdentity(
                                    Constants.UserAssignedIdentity.Identity1.ObjectId,
                                    Constants.UserAssignedIdentity.Identity1.ClientId,
                                    Constants.UserAssignedIdentity.Identity1.TenantId
                                    )
                            },
                            { Constants.UserAssignedIdentity.Identity2.ResourceId,
                                new UserAssignedIdentity(
                                    Constants.UserAssignedIdentity.Identity2.ObjectId,
                                    Constants.UserAssignedIdentity.Identity2.ClientId,
                                    Constants.UserAssignedIdentity.Identity2.TenantId
                                    )
                            }
                        }
                    },
                    IdentityUrl = Constants.SystemAssignedIdentity.DataPlaneUrl,
                    Properties = new AccessConnectorProperties()
                    {
                        ProvisioningState = AccessConnectorState.Succeeded,
                        ReferedBy = referedBy
                    }
                };

                this.AccessConnectorDataProvider.Setup(o => o.FindAccessConnector(
                        It.Is<string>(subscription => subscription == Constants.Common.SubscriptionId),
                        It.Is<string>(rgName => rgName == Constants.Common.ResourceGroupName),
                        It.Is<string>(connectorName => connectorName == Constants.Connector.ConnectorName1)))
                    .Returns(Task.FromResult<AccessConnectorEntity>(accessConnectorEntity));

                this.Request.RequestUri = new Uri("https://somehosturlforconnector?api-version=2022-04-01-preview");

                //Test Item
                var response = this.AccessConnectorController.GetAccessConnector(
                    Constants.Common.SubscriptionId,
                    Constants.Common.ResourceGroupName,
                    Constants.Connector.ConnectorName1).Result;

                //Assert
                Assert.AreNotEqual(null, response);
                Assert.AreEqual((int)HttpStatusCode.OK, (response as ObjectResult).StatusCode);

                var responseContent = ConvertResponse(response);

                Assert.AreNotEqual(null, responseContent);
                Assert.AreNotEqual(null, responseContent.Identity);
                Assert.AreEqual(IdentityType.SystemAssigned, responseContent.Identity.Type);
                Assert.AreEqual(Constants.SystemAssignedIdentity.PrincipalId, responseContent.Identity.PrincipalId);
                Assert.AreEqual(Constants.SystemAssignedIdentity.TenantId, responseContent.Identity.TenantId);

                Assert.AreNotEqual(null, responseContent.Properties);
                Assert.AreEqual(null, responseContent.Properties.ReferedBy);

                Assert.AreEqual(null, responseContent.Identity.UserAssignedIdentities);
            }
        }

        [TestMethod("Get access connector with 2024-05-01 apiversion")]
        public void TestGetOnExistingAccessConnectorWithNewAPIVersion()
        {
            using (RequestCorrelationContext.NewCorrelationContextScope())
            {
                //Set up
                SetUpCorrelationContext(Constants.Common.DatabricksV8ControllerGAApiVersion);

                var referedBy = new List<string> { Constants.Common.FullyQualifiedWorkspaceId };
                var accessConnectorEntity = new AccessConnectorEntity(
                    Constants.Connector.ConnectorName1,
                    Constants.Common.ResourceGroupName,
                    Constants.Common.SubscriptionId,
                    Constants.Common.Location,
                    tags: null)
                {
                    Properties = new AccessConnectorProperties()
                    {
                        ProvisioningState = AccessConnectorState.Succeeded,
                        ReferedBy = referedBy
                    }
                };

                this.AccessConnectorDataProvider.Setup(o => o.FindAccessConnector(
                        It.Is<string>(subscription => subscription == Constants.Common.SubscriptionId),
                        It.Is<string>(rgName => rgName == Constants.Common.ResourceGroupName),
                        It.Is<string>(connectorName => connectorName == Constants.Connector.ConnectorName1)))
                    .Returns(Task.FromResult<AccessConnectorEntity>(accessConnectorEntity));

                //Test Item
                var response = this.AccessConnectorController.GetAccessConnector(
                    Constants.Common.SubscriptionId,
                    Constants.Common.ResourceGroupName,
                    Constants.Connector.ConnectorName1).Result;

                //Assert
                Assert.AreNotEqual(null, response);
                Assert.AreEqual((int)HttpStatusCode.OK, (response as ObjectResult).StatusCode);

                var responseContent = ConvertResponse(response);

                Assert.AreNotEqual(null, responseContent);
                Assert.AreNotEqual(null, responseContent.Properties);
                Assert.AreEqual(referedBy, responseContent.Properties.ReferedBy);
            }
        }

        [TestMethod("Get accessConnector returns all existing connectors in the subscription")]
        public void GetAllAccessConnectorinSubscription()
        {
            using (RequestCorrelationContext.NewCorrelationContextScope())
            {
                //Set up
                SetUpCorrelationContext(Constants.Common.Connector20230501APIVersion);

                var entity1 = new AccessConnectorEntity(
                    "Entity1RG1",
                    Constants.Common.ResourceGroupName,
                    Constants.Common.SubscriptionId,
                    Constants.Common.Location,
                    tags: null)
                {
                    Identity = new Identity()
                    {
                        UserAssignedIdentities = new InsensitiveDictionary<UserAssignedIdentity>
                            {
                                { Constants.UserAssignedIdentity.Identity1.ResourceId, new UserAssignedIdentity() }
                            }

                    },
                    Properties = new AccessConnectorProperties
                    {
                        ProvisioningState = AccessConnectorState.Succeeded
                    }
                };

                var entity2 = new AccessConnectorEntity(
                    "Entity2RG1",
                    Constants.Common.ResourceGroupName,
                    Constants.Common.SubscriptionId,
                    Constants.Common.Location,
                    tags: null)
                {
                    Identity = new Identity()
                    {
                        UserAssignedIdentities = new InsensitiveDictionary<UserAssignedIdentity>
                            {
                                { Constants.UserAssignedIdentity.Identity1.ResourceId, new UserAssignedIdentity() }
                            }
                    },
                    Properties = new AccessConnectorProperties
                    {
                        ProvisioningState = AccessConnectorState.Succeeded
                    }
                };

                var entity3 = new AccessConnectorEntity(
                    "Entity3RG2",
                    "test-rg2",
                    Constants.Common.SubscriptionId,
                    Constants.Common.Location,
                    tags: null)
                {
                    Identity = new Identity()
                    {
                        UserAssignedIdentities = new InsensitiveDictionary<UserAssignedIdentity>
                            {
                                { Constants.UserAssignedIdentity.Identity1.ResourceId, new UserAssignedIdentity() }
                            }
                    },
                    Properties = new AccessConnectorProperties
                    {
                        ProvisioningState = AccessConnectorState.Deleted
                    }
                };

                var entity4 = new AccessConnectorEntity(
                    "Entity4RG2",
                    "test-rg2",
                    Constants.Common.SubscriptionId,
                    Constants.Common.Location,
                    tags: null)
                {
                    Identity = new Identity()
                    {
                        UserAssignedIdentities = new InsensitiveDictionary<UserAssignedIdentity>
                            {
                                { Constants.UserAssignedIdentity.Identity1.ResourceId, new UserAssignedIdentity() }
                            }
                    },
                    Properties = new AccessConnectorProperties
                    {
                        ProvisioningState = AccessConnectorState.Succeeded
                    }
                };


                var segmentedResult = new SegmentedResult<AccessConnectorEntity>
                {
                    Entities = new AccessConnectorEntity[4]
                    {
                        entity1,entity2,entity3,entity4
                    },
                    ContinuationToken = null
                };

                this.AccessConnectorDataProvider.Setup(o => o.FindAccessConnectorsSegmented(
                        It.Is<string>(subscription => subscription == Constants.Common.SubscriptionId),
                        It.IsAny<string>(),
                        It.IsAny<int?>(),
                        It.IsAny<DataContinuationToken>()
                        )).ReturnsAsync(segmentedResult);

                this.Request.RequestUri = new Uri("https://somehosturlforconnector?api-version=2022-04-01-preview");

                //Test Item
                var response = this.AccessConnectorController.GetAccessConnector(Constants.Common.SubscriptionId).Result;
                var accessConnectors = ConvertContinuationResponse(response);

                // Assert
                Assert.IsNotNull(response);
                Assert.AreEqual((int)HttpStatusCode.OK, (response as ObjectResult).StatusCode);

                Assert.IsNotNull(accessConnectors);
                Assert.AreEqual(3, accessConnectors.Length);
                Assert.AreEqual("Entity1RG1", accessConnectors[0].Name);
                Assert.AreEqual("Entity2RG1", accessConnectors[1].Name);
                Assert.AreEqual("Entity4RG2", accessConnectors[2].Name);

            }
        }

        [TestMethod("Get accessConnector returns all existing connectors in the resource group")]
        public void GetAllAccessConnectorinRG()
        {
            using (RequestCorrelationContext.NewCorrelationContextScope())
            {
                //Set up
                SetUpCorrelationContext(Constants.Common.Connector20230501APIVersion);

                var entity1 = new AccessConnectorEntity(
                    "Entity1RG1",
                    Constants.Common.ResourceGroupName,
                    Constants.Common.SubscriptionId,
                    Constants.Common.Location,
                    tags: null)
                {
                    Identity = new Identity()
                    {
                        UserAssignedIdentities = new InsensitiveDictionary<UserAssignedIdentity>
                                {
                                    { Constants.UserAssignedIdentity.Identity1.ResourceId, new UserAssignedIdentity() }
                                }

                    },
                    Properties = new AccessConnectorProperties
                    {
                        ProvisioningState = AccessConnectorState.Succeeded
                    }
                };

                var entity2 = new AccessConnectorEntity(
                    "Entity2RG1",
                    Constants.Common.ResourceGroupName,
                    Constants.Common.SubscriptionId,
                    Constants.Common.Location,
                    tags: null)
                {
                    Identity = new Identity()
                    {
                        UserAssignedIdentities = new InsensitiveDictionary<UserAssignedIdentity>
                                {
                                    { Constants.UserAssignedIdentity.Identity1.ResourceId, new UserAssignedIdentity() }
                                }
                    },
                    Properties = new AccessConnectorProperties
                    {
                        ProvisioningState = AccessConnectorState.Succeeded
                    }
                };

                var entity3 = new AccessConnectorEntity(
                    "Entity3RG2",
                    "test-rg2",
                    Constants.Common.SubscriptionId,
                    Constants.Common.Location,
                    tags: null)
                {
                    Identity = new Identity()
                    {
                        UserAssignedIdentities = new InsensitiveDictionary<UserAssignedIdentity>
                                {
                                    { Constants.UserAssignedIdentity.Identity1.ResourceId, new UserAssignedIdentity() }
                                }
                    },
                    Properties = new AccessConnectorProperties
                    {
                        ProvisioningState = AccessConnectorState.Deleted
                    }
                };

                var entity4 = new AccessConnectorEntity(
                    "Entity4RG2",
                    "test-rg2",
                    Constants.Common.SubscriptionId,
                    Constants.Common.Location,
                    tags: null)
                {
                    Identity = new Identity()
                    {
                        UserAssignedIdentities = new InsensitiveDictionary<UserAssignedIdentity>
                                {
                                    { Constants.UserAssignedIdentity.Identity1.ResourceId, new UserAssignedIdentity() }
                                }
                    },
                    Properties = new AccessConnectorProperties
                    {
                        ProvisioningState = AccessConnectorState.Succeeded
                    }
                };


                var segmentedResult = new SegmentedResult<AccessConnectorEntity>
                {
                    Entities = new AccessConnectorEntity[2]
                    {
                        entity1,entity2
                    },
                    ContinuationToken = null
                };

                this.AccessConnectorDataProvider.Setup(o => o.FindAccessConnectorsSegmented(
                        It.Is<string>(subscription => subscription == Constants.Common.SubscriptionId),
                        It.Is<string>(rgName => rgName == Constants.Common.ResourceGroupName),
                        It.IsAny<int?>(),
                        It.IsAny<DataContinuationToken>()
                        )).ReturnsAsync(segmentedResult);

                this.Request.RequestUri = new Uri("https://somehosturlforconnector?api-version=2022-04-01-preview");

                // Test Item
                var response = this.AccessConnectorController.GetAccessConnector(
                    Constants.Common.SubscriptionId, Constants.Common.ResourceGroupName).Result;

                var accessConnectors = ConvertContinuationResponse(response);

                // Assert
                Assert.IsNotNull(response);
                Assert.AreEqual((int)HttpStatusCode.OK, (response as ObjectResult).StatusCode);

                Assert.IsNotNull(accessConnectors);
                Assert.AreEqual(2, accessConnectors.Length);
                Assert.AreEqual("Entity1RG1", accessConnectors[0].Name);
                Assert.AreEqual("Entity2RG1", accessConnectors[1].Name);
            }
        }
        #endregion

        #region Delete accessConnector Tests

        [TestMethod("Delete Access Connector in Public Cloud, it should be successful")]
        public void DeleteAccessConnectorInPublicCloud()
        {
            using (new AppConfigSettingScope(this.ConfigurationSettings))
            {
                //Set up
                var accessConnectorEntity = new AccessConnectorEntity(
                    Constants.Connector.ConnectorName1,
                    Constants.Common.ResourceGroupName,
                    Constants.Common.SubscriptionId,
                    Constants.Common.Location,
                    tags: null)
                {
                    Properties = new AccessConnectorProperties()
                    {
                        ProvisioningState = AccessConnectorState.Succeeded
                    }
                };

                this.AccessConnectorDataProvider.Setup(o => o.FindAccessConnector(
                        It.Is<string>(subscription => subscription == Constants.Common.SubscriptionId),
                        It.Is<string>(rgName => rgName == Constants.Common.ResourceGroupName),
                        It.Is<string>(connectorName => connectorName == Constants.Connector.ConnectorName1)))
                    .Returns(Task.FromResult<AccessConnectorEntity>(accessConnectorEntity));

                //Test Item
                var response = this.AccessConnectorController.DeleteAccessConnector(
                    Constants.Common.SubscriptionId,
                    Constants.Common.ResourceGroupName,
                    Constants.Connector.ConnectorName1).Result;

                //Assert
                Assert.AreNotEqual(null, response);
                Assert.AreEqual((int)HttpStatusCode.OK, (response as StatusCodeResult).StatusCode);
            }
        }

        [TestMethod("Delete Access Connector In Non Public Cloud it should throw not supported")]
        public void DeleteAccessConnectorInNonPublicCloud()
        {
            var settings = new Dictionary<string, string>
            {
                { "CloudEnvironment", "Fairfax" }
            };

            using (new AppConfigSettingScope(settings))
            {
                try
                {
                    //Test Item
                    var response = this.AccessConnectorController.DeleteAccessConnector(
                        Constants.Common.SubscriptionId,
                        Constants.Common.ResourceGroupName,
                        Constants.Connector.ConnectorName1).Result;
                }
                catch (Exception ex)
                {
                    Assert.AreEqual(ex.InnerException.Message, ErrorResponseMessages.AccessConnectorNotSupported.ToLocalizedMessage());
                }
            }
        }

        [TestMethod("Delete an Access Connector which doesn't exist it should succeed with return code 204")]
        public void DeleteAnAccessConnectorWhichDoesNotExist()
        {
            using (new AppConfigSettingScope(this.ConfigurationSettings))
            {
                //Set up
                this.AccessConnectorDataProvider.Setup(o => o.FindAccessConnector(
                    It.Is<string>(subscription => subscription == Constants.Common.SubscriptionId),
                    It.Is<string>(rgName => rgName == Constants.Common.ResourceGroupName),
                    It.Is<string>(connectorName => connectorName == Constants.Connector.ConnectorName1)))
                .Returns(Task.FromResult<AccessConnectorEntity>(null));

                //Test Item
                var response = this.AccessConnectorController.DeleteAccessConnector(
                    Constants.Common.SubscriptionId,
                    Constants.Common.ResourceGroupName,
                    Constants.Connector.ConnectorName1).Result;

                //Assert
                Assert.AreNotEqual(null, response);
                Assert.AreEqual((int)HttpStatusCode.NoContent, (response as StatusCodeResult).StatusCode);

                this.AccessConnectorDataProvider.Verify(
                    o => o.DeleteAccessConnector(
                        It.IsAny<AccessConnectorEntity>()), Times.Never);
            }
        }

        [TestMethod("Delete an Access Connector which doesn't have Identity Assigned it should succeed but shouldn't send DB Notification. Return code should be 200")]
        public void DeleteAnAccessConnectorWhichExistWithIdentityNull()
        {
            using (new AppConfigSettingScope(this.ConfigurationSettings))
            {
                //Set up
                var accessConnectorEntity = new AccessConnectorEntity(
                Constants.Connector.ConnectorName1,
                Constants.Common.ResourceGroupName,
                Constants.Common.SubscriptionId,
                Constants.Common.Location,
                tags: null,
                identity: null)
                {
                    Properties = new AccessConnectorProperties()
                    {
                        ProvisioningState = AccessConnectorState.Succeeded
                    }
                };

                this.AccessConnectorDataProvider.Setup(o => o.FindAccessConnector(
                        It.Is<string>(subscription => subscription == Constants.Common.SubscriptionId),
                        It.Is<string>(rgName => rgName == Constants.Common.ResourceGroupName),
                        It.Is<string>(connectorName => connectorName == Constants.Connector.ConnectorName1)))
                    .Returns(Task.FromResult<AccessConnectorEntity>(accessConnectorEntity));

                //Test Item
                var response = this.AccessConnectorController.DeleteAccessConnector(
                    Constants.Common.SubscriptionId,
                    Constants.Common.ResourceGroupName,
                    Constants.Connector.ConnectorName1).Result;

                //Assert
                Assert.IsNotNull(response);
                Assert.AreEqual((int)HttpStatusCode.OK, (response as StatusCodeResult).StatusCode);

                this.FrontdoorEngine.Verify(
                    o => o.CallDatabricksAsyncAccountApi(
                        It.IsAny<HttpMethod>(),
                        It.IsAny<Uri>(),
                        It.IsAny<string>(),
                        It.IsAny<string>(),
                        It.IsAny<CredMgrNotification>(),
                        It.IsAny<InsensitiveDictionary<string>>()), Times.Never);

                this.AccessConnectorDataProvider.Verify(
                    o => o.DeleteAccessConnector(
                        It.IsAny<AccessConnectorEntity>()), Times.Once);
            }
        }

        [TestMethod("Delete an Access Connector with Identity Assigned with type as None. Delete should succeed but shouldn't send DB Notification. Return code should be 200")]
        public void DeleteAnAccessConnectorWhichExistWithIdentityNone()
        {
            using (new AppConfigSettingScope(this.ConfigurationSettings))
            {
                //Set up
                var accessConnectorEntity = new AccessConnectorEntity(
                Constants.Connector.ConnectorName1,
                Constants.Common.ResourceGroupName,
                Constants.Common.SubscriptionId,
                Constants.Common.Location,
                tags: null,
                identity: null)
                {
                    Properties = new AccessConnectorProperties()
                    {
                        ProvisioningState = AccessConnectorState.Succeeded
                    }
                };

                accessConnectorEntity.Identity = new Identity() { Type = IdentityType.None };

                this.AccessConnectorDataProvider.Setup(o => o.FindAccessConnector(
                    It.Is<string>(subscription => subscription == Constants.Common.SubscriptionId),
                    It.Is<string>(rgName => rgName == Constants.Common.ResourceGroupName),
                    It.Is<string>(connectorName => connectorName == Constants.Connector.ConnectorName1)))
                .Returns(Task.FromResult<AccessConnectorEntity>(accessConnectorEntity));

                //Test Item
                var response = this.AccessConnectorController.DeleteAccessConnector(
                    Constants.Common.SubscriptionId,
                    Constants.Common.ResourceGroupName,
                    Constants.Connector.ConnectorName1).Result;

                //Assert
                Assert.IsNotNull(response);
                Assert.AreEqual((int)HttpStatusCode.OK, (response as StatusCodeResult).StatusCode);
                this.FrontdoorEngine.Verify(
                    o => o.CallDatabricksAsyncAccountApi(
                        It.IsAny<HttpMethod>(),
                        It.IsAny<Uri>(),
                        It.IsAny<string>(),
                        It.IsAny<string>(),
                        It.IsAny<CredMgrNotification>(),
                        It.IsAny<InsensitiveDictionary<string>>()), Times.Never);

                this.AccessConnectorDataProvider.Verify(
                    o => o.DeleteAccessConnector(
                        It.IsAny<AccessConnectorEntity>()), Times.Once);
            }
        }

        [TestMethod("Delete an Access Connector with Identity Assigned with type as not None and Tenant as non MSFT. Delete should succeed but shouldn't send DB Notification. Return code should be 200")]
        public void DeleteAnAccessConnectorWhichExistWithIdentityNotNoneAndTenantAsNotMsftandNotificationSent()
        {
            using (new AppConfigSettingScope(this.ConfigurationSettings))
            {
                //Set up
                var accessConnectorEntity = new AccessConnectorEntity(
                Constants.Connector.ConnectorName1,
                Constants.Common.ResourceGroupName,
                Constants.Common.SubscriptionId,
                Constants.Common.Location,
                tags: null)
                {
                    Properties = new AccessConnectorProperties()
                    {
                        ProvisioningState = AccessConnectorState.Succeeded

                    }
                };

                accessConnectorEntity.Identity = new Identity() { Type = IdentityType.SystemAssigned };

                this.AccessConnectorDataProvider.Setup(o => o.FindAccessConnector(
                    It.Is<string>(subscription => subscription == Constants.Common.SubscriptionId),
                    It.Is<string>(rgName => rgName == Constants.Common.ResourceGroupName),
                    It.Is<string>(connectorName => connectorName == Constants.Connector.ConnectorName1)))
                .Returns(Task.FromResult<AccessConnectorEntity>(accessConnectorEntity));

                //Test Item
                var response = this.AccessConnectorController
                    .DeleteAccessConnector(
                    Constants.Common.SubscriptionId,
                    Constants.Common.ResourceGroupName,
                    Constants.Connector.ConnectorName1).Result;

                //Assert
                Assert.IsNotNull(response);
                Assert.AreEqual((int)HttpStatusCode.OK, (response as StatusCodeResult).StatusCode);

                this.FrontdoorEngine.Verify(
                    o => o.CallDatabricksAsyncAccountApi(
                        It.IsAny<HttpMethod>(),
                        It.IsAny<Uri>(),
                        It.IsAny<string>(),
                        It.IsAny<string>(),
                        It.IsAny<CredMgrNotification>(),
                        It.IsAny<InsensitiveDictionary<string>>()), Times.Once);

                this.AccessConnectorDataProvider.Verify(
                    o => o.DeleteAccessConnector(
                        It.IsAny<AccessConnectorEntity>()), Times.Once);
            }
        }

        [TestMethod("Delete Old Connectors with 2 UA MI. This should Succeed")]
        public void DeleteOldAccessConnectorWithtwouserAssignedIdentity()
        {
            using (new AppConfigSettingScope(this.ConfigurationSettings))
            {
                //Set up
                var accessConnectorEntity = new AccessConnectorEntity(
                Constants.Connector.ConnectorName1,
                Constants.Common.ResourceGroupName,
                Constants.Common.SubscriptionId,
                Constants.Common.Location,
                tags: null)
                {
                    Identity = new Identity()
                    {
                        Type = IdentityType.UserAssigned,
                        UserAssignedIdentities = new InsensitiveDictionary<UserAssignedIdentity>
                        {
                            { Constants.UserAssignedIdentity.Identity1.ResourceId,
                                new UserAssignedIdentity(
                                    Constants.UserAssignedIdentity.Identity1.ObjectId,
                                    Constants.UserAssignedIdentity.Identity1.ClientId,
                                    Constants.UserAssignedIdentity.Identity1.TenantId
                                    )
                            },
                            { Constants.UserAssignedIdentity.Identity2.ResourceId,
                                new UserAssignedIdentity(
                                    Constants.UserAssignedIdentity.Identity2.ObjectId,
                                    Constants.UserAssignedIdentity.Identity2.ClientId,
                                    Constants.UserAssignedIdentity.Identity2.TenantId
                                    )
                            }
                        }
                    },
                    IdentityUrl = Constants.SystemAssignedIdentity.DataPlaneUrl,
                    Properties = new AccessConnectorProperties()
                    {
                        ProvisioningState = AccessConnectorState.Succeeded
                    }
                };

                this.AccessConnectorDataProvider.Setup(o => o.FindAccessConnector(
                    It.Is<string>(subscription => subscription == Constants.Common.SubscriptionId),
                    It.Is<string>(rgName => rgName == Constants.Common.ResourceGroupName),
                    It.Is<string>(connectorName => connectorName == Constants.Connector.ConnectorName1)))
                .Returns(Task.FromResult<AccessConnectorEntity>(accessConnectorEntity));

                //Test Item
                var response = this.AccessConnectorController.DeleteAccessConnector(
                    Constants.Common.SubscriptionId,
                    Constants.Common.ResourceGroupName,
                    Constants.Connector.ConnectorName1).Result;

                //Assert
                Assert.IsNotNull(response);
                Assert.AreEqual((int)HttpStatusCode.OK, (response as StatusCodeResult).StatusCode);

                this.AccessConnectorDataProvider.Verify(
                            o => o.FindAccessConnector(
                                It.Is<string>(subscription => subscription == Constants.Common.SubscriptionId),
                                It.Is<string>(rgName => rgName == Constants.Common.ResourceGroupName),
                                It.Is<string>(connectorName => connectorName == Constants.Connector.ConnectorName1)), Times.Once);

                this.FrontdoorEngine.Verify(
                    o => o.CallDatabricksAsyncAccountApi(
                    It.IsAny<HttpMethod>(),
                    It.IsAny<Uri>(),
                    It.IsAny<string>(),
                    It.IsAny<string>(),
                    It.IsAny<CredMgrNotification>(),
                    It.IsAny<InsensitiveDictionary<string>>()), Times.Once);

                this.AccessConnectorDataProvider.Verify(
                    o => o.DeleteAccessConnector(
                        It.IsAny<AccessConnectorEntity>()), Times.Once);
            }
        }

        [TestMethod("Delete Old Access Connector with one UA MI.This should Succeed")]
        public void DeleteOldAccessConnectorWithOneuserAssignedIdentity()
        {
            using (new AppConfigSettingScope(this.ConfigurationSettings))
            {
                //Set up
                var accessConnectorEntity = new AccessConnectorEntity(
                Constants.Connector.ConnectorName1,
                Constants.Common.ResourceGroupName,
                Constants.Common.SubscriptionId,
                Constants.Common.Location,
                tags: null)
                {
                    Identity = new Identity()
                    {
                        Type = IdentityType.UserAssigned,
                        UserAssignedIdentities = new InsensitiveDictionary<UserAssignedIdentity>
                        {
                            { Constants.UserAssignedIdentity.Identity1.ResourceId,
                                new UserAssignedIdentity(
                                    Constants.UserAssignedIdentity.Identity1.ObjectId,
                                    Constants.UserAssignedIdentity.Identity1.ClientId,
                                    Constants.UserAssignedIdentity.Identity1.TenantId
                                    )
                            },
                        }
                    },
                    IdentityUrl = Constants.SystemAssignedIdentity.DataPlaneUrl,
                    Properties = new AccessConnectorProperties()
                    {
                        ProvisioningState = AccessConnectorState.Succeeded
                    }
                };

                this.AccessConnectorDataProvider.Setup(o => o.FindAccessConnector(
                    It.Is<string>(subscription => subscription == Constants.Common.SubscriptionId),
                    It.Is<string>(rgName => rgName == Constants.Common.ResourceGroupName),
                    It.Is<string>(connectorName => connectorName == Constants.Connector.ConnectorName1)))
                .Returns(Task.FromResult<AccessConnectorEntity>(accessConnectorEntity));

                //Test Item
                var response = this.AccessConnectorController.DeleteAccessConnector(
                    Constants.Common.SubscriptionId,
                    Constants.Common.ResourceGroupName,
                    Constants.Connector.ConnectorName1).Result;

                //Assert
                Assert.IsNotNull(response);
                Assert.AreEqual((int)HttpStatusCode.OK, (response as StatusCodeResult).StatusCode);

                this.AccessConnectorDataProvider.Verify(
                            o => o.FindAccessConnector(
                                It.Is<string>(subscription => subscription == Constants.Common.SubscriptionId),
                                It.Is<string>(rgName => rgName == Constants.Common.ResourceGroupName),
                                It.Is<string>(connectorName => connectorName == Constants.Connector.ConnectorName1)), Times.Once);

                this.FrontdoorEngine.Verify(
                    o => o.CallDatabricksAsyncAccountApi(
                    It.IsAny<HttpMethod>(),
                    It.IsAny<Uri>(),
                    It.IsAny<string>(),
                    It.IsAny<string>(),
                    It.IsAny<CredMgrNotification>(),
                    It.IsAny<InsensitiveDictionary<string>>()), Times.Once);

                this.AccessConnectorDataProvider.Verify(
                    o => o.DeleteAccessConnector(
                        It.IsAny<AccessConnectorEntity>()), Times.Once);
            }
        }
        #endregion
    }
}
