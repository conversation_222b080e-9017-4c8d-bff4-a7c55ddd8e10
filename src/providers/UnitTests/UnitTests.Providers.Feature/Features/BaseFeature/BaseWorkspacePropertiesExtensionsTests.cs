﻿//------------------------------------------------------------
// Copyright (c) Microsoft Corporation.  All rights reserved.
//------------------------------------------------------------

namespace UnitTests.Providers.Feature.FeatureTests
{
    using Microsoft.VisualStudio.TestTools.UnitTesting;
    using Microsoft.WindowsAzure.ResourceStack.Common.Collections;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Application.Data.Definitions;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Application.Data.Entities;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Common.Definitions;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Feature.Controllers.ControllerV11;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Feature.Features.BaseFeature;
    using Newtonsoft.Json.Linq;
    using System.Text.RegularExpressions;

    [TestClass]
    public class BaseWorkspacePropertiesExtensionsTests
    {
        [TestMethod]
        public void ValidateCustomVNetIdTest()
        {
            Regex re = new Regex(BaseWorkspacePropertiesExtensions.SubnetNameRegExStr);
            /// The name must begin with a letter or number, end with a letter, number or underscore, and may contain only letters, numbers, underscores, periods, or hyphens.
            Assert.IsTrue(re.IsMatch("a"));
            Assert.IsTrue(re.IsMatch("A"));
            Assert.IsTrue(re.IsMatch("1"));
            Assert.IsFalse(re.IsMatch("_"));

            Assert.IsTrue(re.IsMatch("A1"));
            Assert.IsTrue(re.IsMatch("Ab"));
            Assert.IsTrue(re.IsMatch("A_"));
            Assert.IsFalse(re.IsMatch("A-"));

            Assert.IsTrue(re.IsMatch("a.b"));
            Assert.IsTrue(re.IsMatch("a-b"));
            Assert.IsFalse(re.IsMatch("ab."));
            Assert.IsFalse(re.IsMatch("ab-"));

            Assert.IsFalse(re.IsMatch("private-subnet/")); /// Trailing slash in subnet name
            Assert.IsFalse(re.IsMatch("private-subnet ")); /// Trailing whitespace in subnet name
            Assert.IsFalse(re.IsMatch(" private-subnet")); /// Leading whitespace in subnet name

            Assert.IsFalse(re.IsMatch("/subscriptions/c434a315-da4a-4ce0-81c8-583ae9557bcc/resourceGroups/test-kt-bd-rg-01/providers/Microsoft.Databricks/workspaces/test-kt-bd-dbs-01"));
            Assert.IsFalse(re.IsMatch("/subscriptions/c11368cd-3f5e-4d69-93cd-932aedd477d7/resourceGroups/sprint20230426/providers/Microsoft.Network/virtualNetworks/385555407-databricks-vnet/subnets/private-subnet"));
        }

        [TestMethod]
        public void ValidateSetBasicFieldsFromEntityToWorkspaceForV11ServerlessWorkspace()
        {
            var properties = new WorkspacePropertiesV11();

            var applianceEntity = new ApplianceEntity
            {
                Name = "TestWorkspace",
                Location = "East US",
                Properties = new ApplicationProperties
                {
                    ComputeMode = ComputeMode.Serverless
                },
                Tags = new InsensitiveDictionary<string>
                {
                    { "key1", "value1" },
                    { "key2", "value2" }
                }
            };

            properties.SetBasicFieldsFromEntityToWorkspace(applianceEntity);

            Assert.IsTrue(string.IsNullOrWhiteSpace(properties.ManagedResourceGroupId));
            Assert.IsNull(properties.StorageAccountIdentity);

            // Compute Mode should not be set in basic fields
            Assert.AreEqual(ComputeMode.Hybrid, properties.ComputeMode);
        }

        [TestMethod]
        public void ValidateSetBasicFieldsFromEntityToWorkspaceForV11HybridsWorkspace()
        {
            var properties = new WorkspacePropertiesV11();

            var applianceEntity = new ApplianceEntity
            {
                Name = "TestWorkspace",
                Location = "East US",
                Properties = new ApplicationProperties
                {
                    ComputeMode = ComputeMode.Hybrid,
                    ManagedResourceGroupId = Constants.Common.ManagedResourceGroupId,
                    StorageAccountIdentityEntity = new StorageAccountIdentityEntity
                    {
                        PrincipalId = Constants.StorageEncryption.SystemAssignedPrincipalId,
                        TenantId = Constants.StorageEncryption.SystemAssignedTenantId,
                        Type = StorageAccountIdentityType.SystemAssigned
                    }
                },
                Tags = new InsensitiveDictionary<string>
                {
                    { "key1", "value1" },
                    { "key2", "value2" }
                }
            };

            properties.SetBasicFieldsFromEntityToWorkspace(applianceEntity);

            Assert.AreEqual(Constants.Common.ManagedResourceGroupId, properties.ManagedResourceGroupId);
            Assert.IsNotNull(properties.StorageAccountIdentity);
            Assert.AreEqual(Constants.StorageEncryption.SystemAssignedPrincipalId, properties.StorageAccountIdentity.PrincipalId);
            Assert.AreEqual(Constants.StorageEncryption.SystemAssignedTenantId, properties.StorageAccountIdentity.TenantId);
            Assert.AreEqual(StorageAccountIdentityType.SystemAssigned, properties.StorageAccountIdentity.Type);

            // Compute Mode should not be set in basic fields
            Assert.AreEqual(ComputeMode.Hybrid, properties.ComputeMode);
        }

        [TestMethod]
        public void ValidateSetBasicPropertiesFromWorkspaceToEntityForV11ServerkessWorkspace()
        {
            var properties = new WorkspacePropertiesV11
            {
                ComputeMode = ComputeMode.Serverless,
            };

            var applianceEntity = new ApplianceEntity();
            properties.SetBasicPropertiesFromWorkspaceToEntity(applianceEntity);
            Assert.IsNull(applianceEntity.Properties.ManagedResourceGroupId);
            Assert.IsNull(applianceEntity.Properties.StorageAccountIdentityEntity);

            // Compute Mode should not be set in basic properties
            Assert.IsNull(applianceEntity.Properties.ComputeMode);
        }

        [TestMethod]
        public void ValidateSetBasicPropertiesFromWorkspaceToEntityForV11HybridWorkspace()
        {
            var properties = new WorkspacePropertiesV11
            {
                ComputeMode = ComputeMode.Hybrid,
                ManagedResourceGroupId = Constants.Common.ManagedResourceGroupId,
                StorageAccountIdentity = new StorageAccountIdentity
                {
                    PrincipalId = Constants.StorageEncryption.SystemAssignedPrincipalId,
                    TenantId = Constants.StorageEncryption.SystemAssignedTenantId,
                    Type = StorageAccountIdentityType.SystemAssigned
                },
                Parameters = new InsensitiveDictionary<JToken>
                {
                    { "key1", "value1" },
                    { "key2", "value2" }
                }
            };
            var applianceEntity = new ApplianceEntity();
            properties.SetBasicPropertiesFromWorkspaceToEntity(applianceEntity);
            Assert.AreEqual(Constants.Common.ManagedResourceGroupId, applianceEntity.Properties.ManagedResourceGroupId);
            Assert.IsNotNull(applianceEntity.Properties.Parameters);

            // Parameters will be deep cloned
            Assert.AreEqual("value1", applianceEntity.Properties.Parameters["key1"].ToString());
            Assert.AreEqual("value2", applianceEntity.Properties.Parameters["key2"].ToString());

            // Storage Account Identity is nt set in basic properties
            Assert.IsNull(applianceEntity.Properties.StorageAccountIdentityEntity);

            // Compute Mode should not be set in basic properties
            Assert.IsNull(applianceEntity.Properties.ComputeMode);
        }

    }
}