﻿//------------------------------------------------------------
// Copyright (c) Microsoft Corporation.  All rights reserved.
//------------------------------------------------------------

namespace UnitTests.Providers.Feature.ControllerTests.ControllerV2Tests.PatchApplicationTests.TagsTest
{
    using System;
    using System.Collections.Generic;
    using System.Net;
    using System.Net.Http;
    using System.Threading.Tasks;
    using Microsoft.AspNetCore.Mvc;
    using Microsoft.VisualStudio.TestTools.UnitTesting;
    using Microsoft.WindowsAzure.ResourceStack.Common.Collections;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Application.Data.Definitions;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Application.Data.Definitions.Enums;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Application.Data.Entities;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Common.Data;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Common.Definitions;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Common.Engines;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Feature.Controllers.ControllerV2;
    using Moq;
    using Newtonsoft.Json.Linq;

    [TestClass]
    public class AddTagsTest : UpdateBaseControllerV2Test
    {
        private ApplicationProperties existingWorkspaceProperties;
        protected override ApplicationProperties ExistingWorkspaceProperties
        {
            get
            {
                if (existingWorkspaceProperties == null)
                {
                    existingWorkspaceProperties = base.ExistingWorkspaceProperties;
                    existingWorkspaceProperties.EncryptionProperties = new EncryptionPropertiesEntity()
                    {
                        EncryptionEntities = new EncryptionTargetsEntity()
                        {
                            ManagedServices = new ManagedServicesEncryptionEntity()
                            {
                                KeySource = ManagedEncryptionKeySource.KeyVault,
                                KeyVaultProperties = new ManagedEncryptionKeyVaultEntity()
                                {
                                    KeyName = Constants.NotebookEncryption.KeyName,
                                    KeyVaultUri = Constants.NotebookEncryption.KeyVaultUri,
                                    KeyVersion = Constants.NotebookEncryption.KeyVersion
                                }
                            }
                        }
                    };
                }

                return existingWorkspaceProperties;
            }
        }

        private WorkspaceV2 incomingWorkspace;
        protected override WorkspaceV2 IncomingWorkspace
        {
            get
            {
                if (incomingWorkspace == null)
                {
                    incomingWorkspace = base.IncomingWorkspace;
                    incomingWorkspace.Properties = null;
                    incomingWorkspace.Tags = new InsensitiveDictionary<string>();
                    incomingWorkspace.Tags.Add("key1", "value1");
                    incomingWorkspace.Tags.Add("key2", "value2");
                    incomingWorkspace.Sku = this.Sku;
                }

                return incomingWorkspace;
            }
        }

        private List<ARMResourceDefinition> mrgResourcesCreatedByRp;

        /// <summary>
        /// The specific names are chosesn because we use certain prefixes to validate
        /// if the resources were created by databricks RP
        /// </summary>
        protected List<ARMResourceDefinition> MrgResourcesCreatedByRp
        {
            get
            {
                if (mrgResourcesCreatedByRp == null)
                {
                    mrgResourcesCreatedByRp = new List<ARMResourceDefinition>();

                    mrgResourcesCreatedByRp.Add(new ARMResourceDefinition()
                    {
                        Id = Constants.Common.ManagedResourceGroupId + "/providers/Microsoft.Storage/storageAccounts/dbstoragetest",
                        Name = "dbstoragetest"
                    });

                    mrgResourcesCreatedByRp.Add(new ARMResourceDefinition()
                    {
                        Id = Constants.Common.ManagedResourceGroupId + "/providers/Microsoft.Network/networkSecurityGroups/workers-sg-test",
                        Name = "workers-sg-test"
                    });

                    mrgResourcesCreatedByRp.Add(new ARMResourceDefinition()
                    {
                        Id = Constants.Common.ManagedResourceGroupId + "/providers/Microsoft.Network/virtualNetworks/workers-vnet-test",
                        Name = "workers-vnet-test"
                    });

                    mrgResourcesCreatedByRp.Add(new ARMResourceDefinition()
                    {
                        Id = Constants.Common.ManagedResourceGroupId + "/providers/Microsoft.Relay/namespaces/dbrelaytest",
                        Name = "dbrelaytest"
                    });

                    mrgResourcesCreatedByRp.Add(new ARMResourceDefinition()
                    {
                        Id = Constants.Common.ManagedResourceGroupId + "Microsoft.ManagedIdentity/userAssignedIdentities/dbmanagedidentitytest",
                        Name = "dbmanagedidentitytest"
                    });


                }

                return mrgResourcesCreatedByRp;
            }
        }

        private List<ARMResourceDefinition> mrgResourcesNotCreatedByRp;
        protected List<ARMResourceDefinition> MrgResourcesNotCreatedByRp
        {
            get
            {
                if (mrgResourcesNotCreatedByRp == null)
                {
                    mrgResourcesNotCreatedByRp = new List<ARMResourceDefinition>();

                    mrgResourcesNotCreatedByRp.Add(new ARMResourceDefinition()
                    {
                        Id = Constants.Common.ManagedResourceGroupId + "/providers/Microsoft.Storage/storageAccounts/notcreatedbyrp",
                        Name = "notcreatedbyrp"
                    });
                }

                return mrgResourcesNotCreatedByRp;
            }
        }

        private Mock<IFrontdoorEngine> frontdoorEngineMock;
        protected override Mock<IFrontdoorEngine> FrontdoorEngineMock
        {
            get
            {
                if (frontdoorEngineMock == null)
                {
                    frontdoorEngineMock = base.FrontdoorEngineMock;

                    var innerResources = new List<ARMResourceDefinition>();
                    innerResources.AddRange(this.MrgResourcesCreatedByRp);
                    innerResources.AddRange(this.MrgResourcesNotCreatedByRp);

                    frontdoorEngineMock.Setup(o => o.GetResourceGroupResources(
                        It.Is<string>(authTenantId => authTenantId == Constants.Common.PublisherTenantId),
                        It.Is<string>(subId => subId == Constants.Common.SubscriptionId),
                        It.Is<string>(mrg => mrg == Constants.Common.ManagedResourceGroupName),
                        It.Is<string>(rpNamespace => rpNamespace == Constants.Common.ResourceProviderNamespace),
                        resourcesCreatedByRPQuery,
                        It.IsAny<String>())
                    ).ReturnsAsync(MrgResourcesCreatedByRp.ToArray());
                }

                return frontdoorEngineMock;
            }
        }



        [TestMethod("Returns a status code OK")]
        public void TestReturnsCreatedStatusCode()
        {
            using (new AppConfigSettingScope(this.ConfigurationSettings))
            {
                var response = this.DatabricksController.PatchApplication(
                Constants.Common.SubscriptionId,
                Constants.Common.ResourceGroupName,
                Constants.Common.ApplicationName,
                GetRequestObject<WorkspaceV2>()
                ).Result;

                Assert.AreEqual((response as ObjectResult).StatusCode, (int)HttpStatusCode.OK);
            }
        }

        [TestMethod("Sends a DB notification on tags update with existing encryption details")]
        public async Task TestSendsDBNotification()
        {
            using (new AppConfigSettingScope(this.ConfigurationSettings))
            {
                var response = await this.DatabricksController.PatchApplication(
                Constants.Common.SubscriptionId,
                Constants.Common.ResourceGroupName,
                Constants.Common.ApplicationName,
                GetRequestObject<WorkspaceV2>()
                );

                var expectedRequestUri = new Uri(String.Format(
                        "{0}/api/2.0/tenants/{1}/subscriptions/{2}/resourceGroups/{3}/providers/{4}/workspaces/{5}",
                        Constants.Common.FrontDoorEngineUri,
                        Constants.Common.HomeTenantId,
                        Constants.Common.SubscriptionId,
                        Constants.Common.ResourceGroupName,
                        Constants.Common.ResourceProviderNamespace,
                        Constants.Common.ApplicationName));

                Func<JToken, bool> verifyNotification = requestBody =>
                {
                    var notebookEncryption = requestBody["properties"]["encryption"]["entities"]["managedServices"];
                    return notebookEncryption != null &&
                        requestBody["tags"]["key1"].Value<string>() == "value1" &&
                        requestBody["tags"]["key2"].Value<string>() == "value2" &&
                        notebookEncryption["keySource"].ToString() == "Microsoft.Keyvault" &&
                        notebookEncryption["keyVaultProperties"]["keyName"].ToString() == Constants.NotebookEncryption.KeyName &&
                        notebookEncryption["keyVaultProperties"]["keyVaultUri"].ToString() == Constants.NotebookEncryption.KeyVaultUri &&
                        notebookEncryption["keyVaultProperties"]["keyVersion"].ToString() == Constants.NotebookEncryption.KeyVersion;
                };

                this.FrontdoorEngineMock.Verify(
                    o => o.DBWorkspaceNotificationWrapper(
                            It.Is<HttpMethod>(httpMethod => httpMethod.Method == "PUT"),
                            It.Is<Uri>(requestUri => requestUri == expectedRequestUri),
                            It.Is<string>(rpNamespace => rpNamespace == Constants.Common.ResourceProviderNamespace),
                            It.Is<string>(tenantId => tenantId == Constants.Common.HomeTenantId),
                            It.Is<string>(wsId => wsId == Constants.Common.FullyQualifiedWorkspaceId),
                            It.Is<JToken>(requestBody => verifyNotification(requestBody)),
                            It.IsAny<string>(),
                            It.IsAny<bool>(),
                            It.IsAny<KeyValuePair<string, string>[]>()));
            }
        }

        [TestMethod("Should change the tags of the MRG")]
        public async Task TestChangeMRGTags()
        {
            using (new AppConfigSettingScope(this.ConfigurationSettings))
            {
                var response = await this.DatabricksController.PatchApplication(
                Constants.Common.SubscriptionId,
                Constants.Common.ResourceGroupName,
                Constants.Common.ApplicationName,
                GetRequestObject<WorkspaceV2>());

                this.FrontdoorEngineMock.Verify(
                        o => o.UpdateResourceGroup(
                            It.Is<string>(publisherTenantId => publisherTenantId == Constants.Common.PublisherTenantId),
                            It.Is<string>(subId => subId == Constants.Common.SubscriptionId),
                            It.Is<string>(mrg => mrg == Constants.Common.ManagedResourceGroupName),
                            It.Is<ResourceGroupDefinition>(
                                definition => definition.Tags["key1"] == "value1" &&
                                definition.Tags["key2"] == "value2"),
                            It.Is<string>(rpNamespace => rpNamespace == Constants.Common.ResourceProviderNamespace)));
            }
        }

        [TestMethod("Should update tags of resources inside mrg that were created by RP")]
        public async Task TestUpdateManagedResourceTags()
        {
            using (new AppConfigSettingScope(this.ConfigurationSettings))
            {
                var response = await this.DatabricksController.PatchApplication(
                Constants.Common.SubscriptionId,
                Constants.Common.ResourceGroupName,
                Constants.Common.ApplicationName,
                GetRequestObject<WorkspaceV2>());

                foreach (var resource in this.MrgResourcesCreatedByRp)
                {
                    // All other names are created by RP except the resource named "notcreatedbyrp"
                    this.FrontdoorEngineMock.Verify(
                        o => o.UpdateManagedResourceTags(
                            It.Is<string>(publisherTenantId => publisherTenantId == Constants.Common.PublisherTenantId),
                            It.Is<string>(subId => subId == Constants.Common.SubscriptionId),
                            It.Is<string>(rpNamespace => rpNamespace == Constants.Common.ResourceProviderNamespace),
                            It.Is<ARMResourceDefinition>(armResource => armResource.Id == resource.Id),
                            It.Is<InsensitiveDictionary<string>>(tags => tags["key1"] == "value1" && tags["key2"] == "value2")));
                }
            }
        }

        [TestMethod("Should not update tags of resources inside mrg that were not created by RP")]
        public async Task TestDoNotUpdateNonRPResourceTags()
        {
            using (new AppConfigSettingScope(this.ConfigurationSettings))
            {
                var response = await this.DatabricksController.PatchApplication(
                Constants.Common.SubscriptionId,
                Constants.Common.ResourceGroupName,
                Constants.Common.ApplicationName,
                GetRequestObject<WorkspaceV2>());

                foreach (var resource in this.MrgResourcesNotCreatedByRp)
                {
                    // Resources not created by RP should not have their tags updated.
                    this.FrontdoorEngineMock.Verify(
                       o => o.UpdateManagedResourceTags(
                           It.Is<string>(publisherTenantId => publisherTenantId == Constants.Common.PublisherTenantId),
                           It.Is<string>(subId => subId == Constants.Common.SubscriptionId),
                           It.Is<string>(rpNamespace => rpNamespace == Constants.Common.ResourceProviderNamespace),
                           It.Is<ARMResourceDefinition>(armResource => armResource.Id == resource.Id),
                           It.Is<InsensitiveDictionary<string>>(tags => tags["key1"] == "value1" && tags["key2"] == "value2")), Times.Never);
                }
            }
        }

        [TestMethod("Should call ReplaceAppliance with updated tags, encryption details should remain unchanged")]
        public async Task TestShouldCallReplaceAppliance()
        {
            using (new AppConfigSettingScope(this.ConfigurationSettings))
            {
                this.ReplaceAction = new Action<ApplianceEntity>((entity) =>
                {
                    Assert.AreEqual("value1", entity.Tags["key1"]);
                    Assert.AreEqual("value2", entity.Tags["key2"]);

                    Assert.AreEqual(
                        ManagedEncryptionKeySource.KeyVault,
                        entity.Properties.EncryptionProperties.EncryptionEntities.ManagedServices.KeySource);

                    Assert.AreEqual(
                        Constants.NotebookEncryption.KeyName,
                        entity.Properties.EncryptionProperties.EncryptionEntities.ManagedServices.KeyVaultProperties.KeyName);

                    Assert.AreEqual(
                        Constants.NotebookEncryption.KeyVaultUri,
                        entity.Properties.EncryptionProperties.EncryptionEntities.ManagedServices.KeyVaultProperties.KeyVaultUri);

                    Assert.AreEqual(
                        Constants.NotebookEncryption.KeyVersion,
                        entity.Properties.EncryptionProperties.EncryptionEntities.ManagedServices.KeyVaultProperties.KeyVersion);
                });

                var response = await this.DatabricksController.PatchApplication(
                    Constants.Common.SubscriptionId,
                    Constants.Common.ResourceGroupName,
                    Constants.Common.ApplicationName,
                    GetRequestObject<WorkspaceV2>());
            }
        }
    }
}
