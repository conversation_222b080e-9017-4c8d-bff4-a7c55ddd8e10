﻿//------------------------------------------------------------
// Copyright (c) Microsoft Corporation.  All rights reserved.
//------------------------------------------------------------

namespace UnitTests.Providers.Feature.ControllerTests.ControllerV2Tests.PutApplicationTests.UpdateTests.NotebookEncryption
{
    using System;
    using System.Collections.Generic;
    using System.Net;
    using System.Net.Http;
    using System.Runtime.CompilerServices;
    using System.Threading.Tasks;
    using Microsoft.AspNetCore.Mvc;
    using Microsoft.VisualStudio.TestTools.UnitTesting;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Application.Data.Definitions;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Application.Data.Definitions.Enums;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Application.Data.Entities;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Common.ErrorResponses;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Common.Extensions;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Feature.Controllers;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Feature.Controllers.ControllerV2;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Feature.Features.EncryptionDefinitions;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Feature.Features.NotebookEncryptionFeature;
    using Moq;
    using Newtonsoft.Json.Linq;

    /// <summary>
    /// Updates a workspace's encryption properties with a different KeyVersion and fails.
    /// Updating notebook encryption properties is not supported. 
    /// </summary>
    [TestClass]
    public class UpdateSucceedsWithDifferentKeyVersionTest : UpdateBaseControllerV2Test
    {
        protected override JToken Sku
        {
            get
            {
                return JToken.Parse(@"{ ""name"": ""Premium""}");
            }
        }

        private ApplicationProperties existingWorkspaceProperties;
        protected override ApplicationProperties ExistingWorkspaceProperties
        {
            get
            {
                if (existingWorkspaceProperties == null)
                {
                    existingWorkspaceProperties = base.ExistingWorkspaceProperties;
                    existingWorkspaceProperties.EncryptionProperties = new EncryptionPropertiesEntity()
                    {
                        EncryptionEntities = new EncryptionTargetsEntity()
                        {
                            ManagedServices = new ManagedServicesEncryptionEntity()
                            {
                                KeySource = ManagedEncryptionKeySource.KeyVault,
                                KeyVaultProperties = new ManagedEncryptionKeyVaultEntity()
                                {
                                    KeyName = Constants.NotebookEncryption.KeyName,
                                    KeyVaultUri = Constants.NotebookEncryption.KeyVaultUri,
                                    KeyVersion = Constants.NotebookEncryption.KeyVersion
                                }
                            }
                        }
                    };
                }

                return existingWorkspaceProperties;
            }
        }

        private WorkspacePropertiesV2 incomingWorkspaceProperties;
        protected override WorkspacePropertiesV2 IncomingWorkspaceProperties
        {
            get
            {
                if (incomingWorkspaceProperties == null)
                {
                    incomingWorkspaceProperties = base.IncomingWorkspaceProperties;
                    incomingWorkspaceProperties.Encryption = new EncryptionProperties
                    {
                        Entities = new EncryptionTargets()
                        {
                            ManagedServices = new ManagedServicesEncryption()
                            {
                                KeySource = ManagedEncryptionKeySource.KeyVault,
                                KeyVaultProperties = new ManagedServicesKeyVault()
                                {
                                    KeyName = Constants.NotebookEncryption.KeyName,
                                    KeyVaultUri = Constants.NotebookEncryption.KeyVaultUri,
                                    KeyVersion = Constants.NotebookEncryption.KeyVersion2
                                }
                            }
                        }
                    };
                }

                return incomingWorkspaceProperties;
            }
        }

        private WorkspaceV2 incomingWorkspace;
        protected override WorkspaceV2 IncomingWorkspace
        {
            get
            {
                if (incomingWorkspace == null)
                {
                    incomingWorkspace = base.IncomingWorkspace;
                    incomingWorkspace.Properties = this.incomingWorkspaceProperties;
                    incomingWorkspace.Sku = this.Sku;
                }

                return incomingWorkspace;
            }
        }

        [TestMethod("Saves ApplianceEntity object with correct encryption settings ")]
        public async Task TestSavesApplianceEntity()
        {
            using (new AppConfigSettingScope(this.ConfigurationSettings))
            {
                this.ReplaceAction = new Action<ApplianceEntity>((entity) =>
                {
                    var notebookEncryptionEntity = entity.Properties?.EncryptionProperties?.EncryptionEntities?.ManagedServices;
                    Assert.IsNotNull(notebookEncryptionEntity);
                    Assert.IsNotNull(notebookEncryptionEntity.KeyVaultProperties);
                    Assert.AreEqual(ManagedEncryptionKeySource.KeyVault, notebookEncryptionEntity.KeySource);
                    Assert.AreEqual(Constants.NotebookEncryption.KeyName.ToLower(), notebookEncryptionEntity.KeyVaultProperties.KeyName.ToLower());
                    Assert.AreEqual(Constants.NotebookEncryption.KeyVaultUri.ToLower(), notebookEncryptionEntity.KeyVaultProperties.KeyVaultUri.ToLower());
                    Assert.AreEqual(Constants.NotebookEncryption.KeyVersion2.ToLower(), notebookEncryptionEntity.KeyVaultProperties.KeyVersion.ToLower());
                });

                var _ = await this.DatabricksV2Controller.PutApplication(
                    Constants.Common.SubscriptionId,
                    Constants.Common.ResourceGroupName,
                    Constants.Common.ApplicationName,
                    GetRequestObject<WorkspaceV2>());
            }
        }

        [TestMethod("Returns a response containing correct encryption settings")]
        public async Task TestReturnsCorrectResponse()
        {
            using (new AppConfigSettingScope(this.ConfigurationSettings))
            {
                var response = await this.DatabricksV2Controller.PutApplication(
                    Constants.Common.SubscriptionId,
                    Constants.Common.ResourceGroupName,
                    Constants.Common.ApplicationName,
                    GetRequestObject<WorkspaceV2>()) as ObjectResult;

                Assert.IsNotNull(response.Value);
                var returnedWorkspace = ConvertResponse<WorkspaceV2>(response);

                var notebookEncryptionResponse = returnedWorkspace?.Properties?.Encryption?.Entities?.ManagedServices;

                Assert.IsNotNull(notebookEncryptionResponse);
                Assert.AreEqual(ManagedEncryptionKeySource.KeyVault, notebookEncryptionResponse.KeySource);
                Assert.AreEqual(Constants.NotebookEncryption.KeyName.ToLower(), notebookEncryptionResponse.KeyVaultProperties.KeyName.ToLower());
                Assert.AreEqual(Constants.NotebookEncryption.KeyVaultUri.ToLower(), notebookEncryptionResponse.KeyVaultProperties.KeyVaultUri.ToLower());
                Assert.AreEqual(Constants.NotebookEncryption.KeyVersion2.ToLower(), notebookEncryptionResponse.KeyVaultProperties.KeyVersion.ToLower());
            }
        }

        [TestMethod("Sends DB notification with correct encryption data")]
        public async Task TestSendsCorrectDBNotification()
        {
            using (new AppConfigSettingScope(this.ConfigurationSettings))
            {
                var response = await this.DatabricksV2Controller.PutApplication(
                    Constants.Common.SubscriptionId,
                    Constants.Common.ResourceGroupName,
                    Constants.Common.ApplicationName,
                    GetRequestObject<WorkspaceV2>());

                var expectedRequestUri = new Uri(String.Format(
                        "{0}/api/2.0/tenants/{1}/subscriptions/{2}/resourceGroups/{3}/providers/{4}/workspaces/{5}",
                        Constants.Common.FrontDoorEngineUri,
                        Constants.Common.HomeTenantId,
                        Constants.Common.SubscriptionId,
                        Constants.Common.ResourceGroupName,
                        Constants.Common.ResourceProviderNamespace,
                        Constants.Common.ApplicationName));

                Func<JToken, bool> verifyNotification = requestBody =>
                {
                    var notebookEncryption = requestBody["properties"]["encryption"]["entities"]["managedServices"];
                    return notebookEncryption != null &&
                        notebookEncryption["keySource"].ToString() == "Microsoft.Keyvault" &&
                        notebookEncryption["keyVaultProperties"]["keyName"].ToString().ToLower() == Constants.NotebookEncryption.KeyName.ToLower() &&
                        notebookEncryption["keyVaultProperties"]["keyVaultUri"].ToString().ToLower() == Constants.NotebookEncryption.KeyVaultUri.ToLower() &&
                        notebookEncryption["keyVaultProperties"]["keyVersion"].ToString().ToLower() == Constants.NotebookEncryption.KeyVersion2.ToLower();
                };

                this.FrontdoorEngineMock.Verify(
                    o => o.DBWorkspaceNotificationWrapper(
                            It.Is<HttpMethod>(httpMethod => httpMethod.Method == "PUT"),
                            It.Is<Uri>(requestUri => requestUri == expectedRequestUri),
                            It.Is<string>(rpNamespace => rpNamespace == Constants.Common.ResourceProviderNamespace),
                            It.Is<string>(tenantId => tenantId == Constants.Common.HomeTenantId),
                            It.Is<string>(wsId => wsId == Constants.Common.FullyQualifiedWorkspaceId),
                            It.Is<JToken>(requestBody => verifyNotification(requestBody)),
                            It.IsAny<string>(),
                            It.IsAny<bool>(),
                            It.IsAny<KeyValuePair<string, string>[]>()
                        ));
            }
        }
    }
}
