﻿//------------------------------------------------------------
// Copyright (c) Microsoft Corporation.  All rights reserved.
//------------------------------------------------------------

namespace UnitTests.Providers.Feature.ControllerTests.ControllerV7Tests.PutApplicationTests.UpdateTests.RedeployTests
{
    using System;
    using System.Collections.Generic;
    using System.Net.Http;
    using System.Threading.Tasks;
    using Microsoft.VisualStudio.TestTools.UnitTesting;
    using Microsoft.WindowsAzure.ResourceStack.Common.Collections;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Application.Data.DataProviders;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Application.Data.Definitions;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Application.Data.Entities;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Common.Data;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Common.Engines;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Feature.Controllers.ControllerV7;
    using Moq;
    using Newtonsoft.Json.Linq;

    /// <summary>
    /// In this test, we are looking to redeploy an application that has failed to provision.
    /// But we have mocked the ReplaceAppliance method to throw an exception.
    /// This test captures the behaviours as to what should happen when the ReplaceAppliance fails.
    /// </summary>
    [TestClass]
    public class SkipRollbackOnExceptionTest : UpdateBaseControllerV7Test
    {
        protected override JToken Sku
        {
            get
            {
                return JToken.Parse(@"{ ""name"": ""Premium""}");
            }
        }

        private Mock<IApplianceDataProvider> applianceDataProvider;

        protected override Mock<IApplianceDataProvider> ApplianceDataProvider
        {
            get
            {
                if (applianceDataProvider == null)
                {
                    applianceDataProvider = base.ApplianceDataProvider;

                    applianceDataProvider.Setup(
                        o => o.ReplaceAppliance(It.IsAny<ApplianceEntity>())
                        ).Throws(new Exception());

                }

                return applianceDataProvider;
            }

        }

        private int CallCount = 0;

        private Mock<IFrontdoorEngine> frontdoorEngineMock;

        protected override Mock<IFrontdoorEngine> FrontdoorEngineMock
        {
            get
            {
                if (frontdoorEngineMock == null)
                {
                    frontdoorEngineMock = base.FrontdoorEngineMock;

                    void callback(HttpMethod method, Uri uri, string rpNamespace, string tenantId, string workspaceId, JToken requestBody, string audience, bool useArm, KeyValuePair<string, string>[] additionalHeader)
                    {
                        CallCount++;
                    }

                    Action<HttpMethod, Uri, string, string, string, JToken, string, bool, KeyValuePair<string, string>[]> action = callback;
                    frontdoorEngineMock.Setup(o => o.DBWorkspaceNotificationWrapper(
                        It.IsAny<HttpMethod>(),
                        It.IsAny<Uri>(),
                        It.IsAny<string>(),
                        It.IsAny<string>(),
                        It.IsAny<string>(),
                        It.IsAny<JToken>(),
                        It.IsAny<string>(),
                        It.IsAny<bool>(),
                        It.IsAny<KeyValuePair<string, string>[]>()
                        )
                    ).Callback(action);
                }

                return frontdoorEngineMock;
            }
        }

        private ApplicationProperties existingWorkspaceProperties;
        protected override ApplicationProperties ExistingWorkspaceProperties
        {
            get
            {
                if (existingWorkspaceProperties == null)
                {
                    existingWorkspaceProperties = base.ExistingWorkspaceProperties;
                    existingWorkspaceProperties.ProvisioningState = ProvisioningState.Failed;
                }
                return existingWorkspaceProperties;
            }
        }

        private ApplianceMetadata existingApplianceMetadata;
        protected override ApplianceMetadata ExistingApplianceMetadata
        {
            get
            {
                if (existingApplianceMetadata == null)
                {
                    existingApplianceMetadata = base.ExistingApplianceMetadata;
                    existingApplianceMetadata.OperationStatuses = new InsensitiveDictionary<ApplicationOperationStatus>();
                }

                return existingApplianceMetadata;
            }
        }

        [TestMethod("No db notification is sent when a replace operation during an update fails for a failed workspace")]
        public async Task TestSingleNotification()
        {
            using (new AppConfigSettingScope(this.ConfigurationSettings))
            {
                var ex = await Assert.ThrowsExceptionAsync<Exception>(
                async () => await this.DatabricksController.PutApplication(
                     Constants.Common.SubscriptionId,
                     Constants.Common.ResourceGroupName,
                     Constants.Common.ApplicationName,
                     this.GetRequestObject<WorkspaceV7>()
                ));
                Assert.AreEqual(0, this.CallCount);
            }
        }
    }
}
