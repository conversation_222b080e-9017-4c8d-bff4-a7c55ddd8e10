﻿//------------------------------------------------------------
// Copyright (c) Microsoft Corporation.  All rights reserved.
//------------------------------------------------------------

namespace UnitTests.Providers.Feature.ControllerTests.ControllerV11Tests
{
    using Microsoft.WindowsAzure.ResourceStack.Common.Collections;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Application.Data.Definitions;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Feature.Controllers.ControllerV11;
    using Newtonsoft.Json.Linq;

    public class BaseControllerV11ServerlessTest : BaseControllerV11Test
    {
        #region Mock the incoming request object

        private DatabricksWorkspaceDefinition resourceDefinition;

        /// <summary>
        /// Gets the workspace definition.
        /// Override this property for PUT and PATCH requests to set the content of the request object.
        /// </summary>
        protected override DatabricksWorkspaceDefinition WorkspaceDefinition
        {
            get
            {
                if (resourceDefinition == null)
                {
                    resourceDefinition = new DatabricksWorkspaceDefinition();
                    resourceDefinition.Properties = this.ApplianceProperties;
                    resourceDefinition.Properties.ComputeMode = Constants.Common.ServerlessComputeMode;
                    resourceDefinition.Location = Constants.Common.Location;
                    resourceDefinition.Sku = this.Sku;
                }

                return resourceDefinition;
            }
        }

        protected override JToken Sku
        {
            get
            {
                return JToken.Parse(@"{ ""name"": ""Premium""}");
            }
        }

        private WorkspacePropertiesV11 incomingWorkspaceProperties;

        protected override WorkspacePropertiesV11 IncomingWorkspaceProperties
        {
            get
            {
                if (incomingWorkspaceProperties == null)
                {
                    incomingWorkspaceProperties = new WorkspacePropertiesV11
                    {
                        Parameters = this.WorkspaceParameters,
                        ComputeMode = Constants.Common.ServerlessComputeMode
                    };
                }

                return incomingWorkspaceProperties;
            }
        }

        protected override InsensitiveDictionary<JToken> WorkspaceParameters
        {
            get
            {
                return null;
            }
        }
        #endregion
    }
}
