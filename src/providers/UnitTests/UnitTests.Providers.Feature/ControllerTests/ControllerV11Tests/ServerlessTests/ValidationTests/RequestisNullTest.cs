﻿//------------------------------------------------------------
// Copyright (c) Microsoft Corporation.  All rights reserved.
//------------------------------------------------------------

namespace UnitTests.Providers.Feature.ControllerTests.ControllerV11Tests.PutApplicationTests.CreateTests.ValidationTests
{
    using System.Net;
    using System.Net.Http;
    using System.Text;
    using System.Threading.Tasks;
    using Microsoft.VisualStudio.TestTools.UnitTesting;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Common.ErrorResponses;

    [TestClass]
    public class RequestContentisNullTest: BaseControllerV11ServerlessTest
    {
        protected override HttpRequestMessage Request
        {
            get
            {
                var request = base.Request;
                request.Content = new StringContent("", Encoding.UTF8, Constants.Common.MediaType);

                return request;
            }
        }

        [TestMethod("Throws an exception if null request content")]
        public async Task TestThrowsExceptionIfNullRequestContent()
        {
            using (new AppConfigSettingScope(this.ConfigurationSettings))
            {
                var ex = await Assert.ThrowsExceptionAsync<ErrorResponseMessageException>(
                async () => await this.DatabricksV11Controller.PutApplication(
                     Constants.Common.SubscriptionId,
                     Constants.Common.ResourceGroupName,
                     Constants.Common.ApplicationName,
                     null
                ));

                Assert.AreEqual(HttpStatusCode.BadRequest, ex.HttpStatus);
                Assert.AreEqual(ErrorResponseCode.InvalidApplicationDefinition, ex.ErrorCode);
            }
        }
    }
}
