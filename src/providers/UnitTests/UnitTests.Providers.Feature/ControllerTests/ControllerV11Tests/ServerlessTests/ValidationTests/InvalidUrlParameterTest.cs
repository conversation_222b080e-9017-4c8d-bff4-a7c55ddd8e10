﻿//------------------------------------------------------------
// Copyright (c) Microsoft Corporation.  All rights reserved.
//------------------------------------------------------------

namespace UnitTests.Providers.Feature.ControllerTests.ControllerV11Tests.PutApplicationTests.CreateTests.ValidationTests
{
    using System.Net;
    using System.Threading.Tasks;
    using Microsoft.VisualStudio.TestTools.UnitTesting;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Common.ErrorResponses;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Feature.Controllers.ControllerV11;

    [TestClass]
    public class InvalidUrlParametersTest : BaseControllerV11ServerlessTest
    {
        [TestMethod("Throws exception if subscription id is empty")]
        public async Task TestMissingSubId()
        {
            using (new AppConfigSettingScope(this.ConfigurationSettings))
            {
                var ex = await Assert.ThrowsExceptionAsync<ErrorResponseMessageException>(
                async () => await this.DatabricksV11Controller.PutApplication(
                     string.Empty,
                     Constants.Common.ResourceGroupName,
                     Constants.Common.ApplicationName,
                    this.GetRequestObject<WorkspaceV11>()
                ));

                Assert.AreEqual(HttpStatusCode.BadRequest, ex.HttpStatus);
                Assert.AreEqual(ErrorResponseCode.MissingSubscription, ex.ErrorCode);
            }
        }

        [TestMethod("Throws exception if resource group name is empty")]
        public async Task TestMissingRgName()
        {
            using (new AppConfigSettingScope(this.ConfigurationSettings))
            {
                var ex = await Assert.ThrowsExceptionAsync<ErrorResponseMessageException>(
                async () => await this.DatabricksV11Controller.PutApplication(
                     Constants.Common.SubscriptionId,
                     string.Empty,
                     Constants.Common.ApplicationName,
                     this.GetRequestObject<WorkspaceV11>()
                ));

                Assert.AreEqual(HttpStatusCode.BadRequest, ex.HttpStatus);
                Assert.AreEqual(ErrorResponseCode.BadRequest, ex.ErrorCode);
            }
        }

        [TestMethod("Throws exception if application name is empty")]
        public async Task TestMissingApplicationName()
        {
            using (new AppConfigSettingScope(this.ConfigurationSettings))
            {
                var ex = await Assert.ThrowsExceptionAsync<ErrorResponseMessageException>(
                async () => await this.DatabricksV11Controller.PutApplication(
                     Constants.Common.SubscriptionId,
                     Constants.Common.ResourceGroupName,
                     string.Empty,
                     this.GetRequestObject<WorkspaceV11>()
                ));

                Assert.AreEqual(HttpStatusCode.BadRequest, ex.HttpStatus);
                Assert.AreEqual(ErrorResponseCode.InvalidResourceName, ex.ErrorCode);
            }
        }

        [TestMethod("Throws exception if application name is really long")]
        public async Task TestReallyLongApplicationName()
        {
            using (new AppConfigSettingScope(this.ConfigurationSettings))
            {
                var ex = await Assert.ThrowsExceptionAsync<ErrorResponseMessageException>(
                async () => await this.DatabricksV11Controller.PutApplication(
                     Constants.Common.SubscriptionId,
                     Constants.Common.ResourceGroupName,
                     "thisisareallyreallyreallyloooooooooooooooooooooongapplicationname",
                    this.GetRequestObject<WorkspaceV11>()
                ));

                Assert.AreEqual(HttpStatusCode.BadRequest, ex.HttpStatus);
                Assert.AreEqual(ErrorResponseCode.InvalidResourceName, ex.ErrorCode);
            }
        }

        [TestMethod("Throws exception if application name is really short")]
        public async Task TestReallyShortApplicationName()
        {
            using (new AppConfigSettingScope(this.ConfigurationSettings))
            {
                var ex = await Assert.ThrowsExceptionAsync<ErrorResponseMessageException>(
                async () => await this.DatabricksV11Controller.PutApplication(
                     Constants.Common.SubscriptionId,
                     Constants.Common.ResourceGroupName,
                     "ws",
                     this.GetRequestObject<WorkspaceV11>()
                ));

                Assert.AreEqual(HttpStatusCode.BadRequest, ex.HttpStatus);
                Assert.AreEqual(ErrorResponseCode.InvalidResourceName, ex.ErrorCode);
            }
        }


        [TestMethod("Throws exception if application name is really short")]
        public async Task TestInvalidCharactersInApplicationName()
        {
            using (new AppConfigSettingScope(this.ConfigurationSettings))
            {
                var ex = await Assert.ThrowsExceptionAsync<ErrorResponseMessageException>(
                async () => await this.DatabricksV11Controller.PutApplication(
                     Constants.Common.SubscriptionId,
                     Constants.Common.ResourceGroupName,
                     "ws$$$",
                     this.GetRequestObject<WorkspaceV11>()
                ));

                Assert.AreEqual(HttpStatusCode.BadRequest, ex.HttpStatus);
                Assert.AreEqual(ErrorResponseCode.InvalidResourceName, ex.ErrorCode);
            }
        }
    }
}
