﻿//------------------------------------------------------------
// Copyright (c) Microsoft Corporation.  All rights reserved.
//------------------------------------------------------------

namespace UnitTests.Providers.Feature.ControllerTests.ControllerV11Tests.PutApplicationTests.CreateTests.ValidationTests
{
    using System.Net;
    using System.Threading.Tasks;
    using Microsoft.VisualStudio.TestTools.UnitTesting;
    using Microsoft.WindowsAzure.ResourceStack.Common.Instrumentation;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Application.Data.Definitions;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Common.Definitions;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Common.ErrorResponses;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Feature.Controllers.ControllerV11;

    [TestClass]
    public class NullSkuTes : BaseControllerV11Test
    {
        private DatabricksWorkspaceDefinition workspaceDefinition;
        protected override DatabricksWorkspaceDefinition WorkspaceDefinition
        {
            get
            {
                if (workspaceDefinition == null)
                {
                    workspaceDefinition = base.WorkspaceDefinition;
                    workspaceDefinition.Properties = null;
                }

                return workspaceDefinition;
            }
        }

        protected override WorkspacePropertiesV11 IncomingWorkspaceProperties
        {
            get
            {
                return null;
            }
        }

        [TestMethod("Throws an exception if appliance sku is null")]
        public async Task TestThrowsExceptionIfNullSku()
        {
            using (new AppConfigSettingScope(this.ConfigurationSettings))
            {
                var workspace = this.GetRequestObject<WorkspaceV11>();
                workspace.Properties = new WorkspacePropertiesV11
                {
                    ComputeMode = ComputeMode.Serverless
                };
                workspace.Sku = null;

                var ex = await Assert.ThrowsExceptionAsync<ErrorResponseMessageException>(
                async () => await this.DatabricksV11Controller.PutApplication(
                     Constants.Common.SubscriptionId,
                     Constants.Common.ResourceGroupName,
                     Constants.Common.ApplicationName,
                     workspace
                ));

                Assert.AreEqual(HttpStatusCode.BadRequest, ex.HttpStatus);
                Assert.AreEqual(ErrorResponseCode.InvalidApplicationSku, ex.ErrorCode);
                Assert.AreEqual(ErrorResponseMessages.InvalidApplicationSku.ToLocalizedMessage(""), ex.Message);
            }
        }
    }
}
