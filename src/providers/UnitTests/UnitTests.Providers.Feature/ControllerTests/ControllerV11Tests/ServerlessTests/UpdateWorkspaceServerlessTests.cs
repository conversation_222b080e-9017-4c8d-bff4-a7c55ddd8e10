//------------------------------------------------------------
// Copyright (c) Microsoft Corporation.  All rights reserved.
//------------------------------------------------------------

namespace UnitTests.Providers.Feature.ControllerTests.ControllerV11Tests
{
    using System.Net;
    using System.Threading.Tasks;
    using Microsoft.AspNetCore.Mvc;
    using Microsoft.VisualStudio.TestTools.UnitTesting;
    using Microsoft.WindowsAzure.ResourceStack.Common.Collections;
    using Microsoft.WindowsAzure.ResourceStack.Common.Instrumentation;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Application.Data.Definitions;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Application.Data.Definitions.Enums;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Common.Constants;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Common.Definitions;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Common.Definitions.ManagedIdentity;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Common.ErrorResponses;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Feature.Controllers.ControllerV11;
    using Newtonsoft.Json.Linq;

    [TestClass]
    public class UpdateWorkspaceServerlessTests : UpdateBaseControllerV11ServerlessTest
    {
        protected override JToken Sku
        {
            get
            {
                return JToken.Parse(@"{ ""name"": ""Premium""}");
            }
        }

        private ApplicationProperties existingWorkspaceProperties;
        protected override ApplicationProperties ExistingWorkspaceProperties
        {
            get
            {
                if (existingWorkspaceProperties == null)
                {
                    existingWorkspaceProperties = base.ExistingWorkspaceProperties;
                }

                return existingWorkspaceProperties;
            }
        }

        private WorkspacePropertiesV11 incomingWorkspaceProperties;
        protected override WorkspacePropertiesV11 IncomingWorkspaceProperties
        {
            get
            {
                if (incomingWorkspaceProperties == null)
                {
                    incomingWorkspaceProperties = base.IncomingWorkspaceProperties;
                }

                return incomingWorkspaceProperties;
            }
        }

        private WorkspaceV11 incomingWorkspace;
        protected override WorkspaceV11 IncomingWorkspace
        {
            get
            {
                if (incomingWorkspace == null)
                {
                    incomingWorkspace = base.IncomingWorkspace;
                    incomingWorkspace.Properties = this.incomingWorkspaceProperties;
                    incomingWorkspace.Sku = this.Sku;
                }

                return incomingWorkspace;
            }
        }

        [TestMethod("Updating Sku for a serverless workspace should fail")]
        public async Task SkuDowngradeIsNotAllowedForServerlessWorkspace()
        {
            using (new AppConfigSettingScope(this.ConfigurationSettings))
            {
                var incomingWorkspaceProperties = base.IncomingWorkspaceProperties;
                this.IncomingWorkspace.Properties = incomingWorkspaceProperties;
                this.IncomingWorkspace.Sku = JToken.Parse(@"{ ""name"": ""Standard""}");

                var ex = await Assert.ThrowsExceptionAsync<ErrorResponseMessageException>(
                async () => await this.DatabricksV11Controller.PutApplication(
                     Constants.Common.SubscriptionId,
                     Constants.Common.ResourceGroupName,
                     Constants.Common.ApplicationName,
                     this.IncomingWorkspace
                ));

                Assert.AreEqual(HttpStatusCode.BadRequest, ex.HttpStatus);
                Assert.AreEqual(ErrorResponseCode.InvalidSkuForServerlessWorkspace, ex.ErrorCode);
            }
        }

        [TestMethod("Updating Tag for a serverless workspace should success")]
        public async Task UpdateTagForServerlessWorkspace()
        {
            using (new AppConfigSettingScope(this.ConfigurationSettings))
            {
                var incomingWorkspaceProperties = base.IncomingWorkspaceProperties;
                this.IncomingWorkspace.Properties = incomingWorkspaceProperties;

                this.IncomingWorkspace.Tags = new InsensitiveDictionary<string>
                {
                    { "tag1", "value1" },
                    { "tag2", "value2" }
                };

                var response = await this.DatabricksV11Controller.PutApplication(
                   Constants.Common.SubscriptionId,
                   Constants.Common.ResourceGroupName,
                   Constants.Common.ApplicationName,
                   this.IncomingWorkspace) as ObjectResult;

                Assert.IsNotNull(response);
                Assert.AreEqual(HttpStatusCode.Created, (HttpStatusCode)response.StatusCode);
                Assert.IsNotNull(response.Value);
                var returnedWorkspace = ConvertResponse<WorkspaceV11>(response);

                Assert.IsNotNull(returnedWorkspace.Tags);
                Assert.AreEqual("value1", returnedWorkspace.Tags["tag1"]);
                Assert.AreEqual("value2", returnedWorkspace.Tags["tag2"]);
            }
        }

        [TestMethod("Updating amlWorkspaceId for a serverless workspace should Not be allowed")]
        public async Task UpdateAmlWorkspacesIdForServerlessWorkspace()
        {
            using (new AppConfigSettingScope(this.ConfigurationSettings))
            {
                var incomingWorkspaceProperties = base.IncomingWorkspaceProperties;
                incomingWorkspaceProperties.Parameters = new InsensitiveDictionary<JToken>
                {
                    {
                        ProviderConstants.Databricks.AmlWorkspaceIdProperty,
                        JToken.Parse(@"{ ""type"": ""string"", ""value"": """ + Constants.AMLLinking.AMLWorkspaceId + @""" }")
                    }
                };

                this.IncomingWorkspace.Properties = incomingWorkspaceProperties;

                var ex = await Assert.ThrowsExceptionAsync<ErrorResponseMessageException>(
                    async () => await this.DatabricksV11Controller.PutApplication(
                     Constants.Common.SubscriptionId,
                     Constants.Common.ResourceGroupName,
                     Constants.Common.ApplicationName,
                     this.IncomingWorkspace
                ));

                Assert.IsNotNull(ex);
                Assert.AreEqual(HttpStatusCode.BadRequest, ex.HttpStatus);
                Assert.AreEqual(ErrorResponseCode.FeatureNotSupportedForServerlessWorkspace, ex.ErrorCode);
            }
        }

        [TestMethod("Updating Compute Mode for an existing workspace to Hybrid not allowed")]
        public async Task UpdateComputeModeForExistingServerlessWorkspaceNotAllowed()
        {
            using (new AppConfigSettingScope(this.ConfigurationSettings))
            {
                var incomingWorkspaceProperties = base.IncomingWorkspaceProperties;
                incomingWorkspaceProperties.ComputeMode = ComputeMode.Hybrid;
                incomingWorkspaceProperties.ManagedResourceGroupId = Constants.Common.ManagedResourceGroupId;

                this.IncomingWorkspace.Properties = incomingWorkspaceProperties;

                this.ExistingWorkspaceParameters.Remove("loadBalancerBackendPoolName");
                this.ExistingWorkspaceParameters.Remove("loadBalancerId");

                var ex = await Assert.ThrowsExceptionAsync<ErrorResponseMessageException>(
                    async () => await this.DatabricksV11Controller.PutApplication(
                         Constants.Common.SubscriptionId,
                         Constants.Common.ResourceGroupName,
                         Constants.Common.ApplicationName,
                         this.IncomingWorkspace
                    ));

                Assert.AreEqual(HttpStatusCode.BadRequest, ex.HttpStatus);
                Assert.AreEqual(ErrorResponseCode.UpdateComputeModeNotAllowed, ex.ErrorCode);
            }
        }

        #region Feature Not Supported for Serverless Workspace
        [TestMethod("Updating vNet parameter for a serverless workspace isn't allowed")]
        public async Task VNetParameterIsNotAllowedForServerlessWorkspaces()
        {
            using (new AppConfigSettingScope(this.ConfigurationSettings))
            {
                var incomingWorkspaceProperties = base.IncomingWorkspaceProperties;
                incomingWorkspaceProperties.Parameters = new InsensitiveDictionary<JToken>();
                incomingWorkspaceProperties.Parameters.Add("customVirtualNetworkId",
                    JToken.Parse(@"{ ""type"": ""String"", ""value"": """ + Constants.Common.VNetId2 + @"""}"));
                incomingWorkspaceProperties.Parameters.Add("customPublicSubnetName",
                    JToken.Parse(@"{ ""type"": ""String"", ""value"": """ + Constants.Common.CustomPublicSubnetName + @"""}"));
                incomingWorkspaceProperties.Parameters.Add("customPrivateSubnetName",
                    JToken.Parse(@"{ ""type"": ""String"", ""value"": """ + Constants.Common.CustomPrivateSubnetName + @"""}"));

                this.IncomingWorkspace.Properties = incomingWorkspaceProperties;

                var ex = await Assert.ThrowsExceptionAsync<ErrorResponseMessageException>(
                async () => await this.DatabricksV11Controller.PutApplication(
                     Constants.Common.SubscriptionId,
                     Constants.Common.ResourceGroupName,
                     Constants.Common.ApplicationName,
                     this.IncomingWorkspace
                ));

                Assert.AreEqual(HttpStatusCode.BadRequest, ex.HttpStatus);
                Assert.AreEqual(ErrorResponseCode.FeatureNotSupportedForServerlessWorkspace, ex.ErrorCode);
                Assert.AreEqual(ErrorResponseMessages.FeatureNotSupportedForServerlessWorkspace.ToLocalizedMessage("Parameters"), ex.Message);
            }
        }

        [TestMethod("Updating Access Connectors for a serverless workspace isn't allowed")]
        public async Task UpdateAccessConnectorIsNotAllowedForServerlessWorkspace()
        {
            using (new AppConfigSettingScope(this.ConfigurationSettings))
            {
                var incomingWorkspaceProperties = base.IncomingWorkspaceProperties;
                incomingWorkspaceProperties.AccessConnector = new AccessConnectorId
                {
                    Id = Constants.Connector.ConnectorId1,
                    IdentityType = IdentityType.SystemAssigned
                };

                this.IncomingWorkspace.Properties = incomingWorkspaceProperties;

                var ex = await Assert.ThrowsExceptionAsync<ErrorResponseMessageException>(
                async () => await this.DatabricksV11Controller.PutApplication(
                     Constants.Common.SubscriptionId,
                     Constants.Common.ResourceGroupName,
                     Constants.Common.ApplicationName,
                     this.IncomingWorkspace
                ));

                Assert.AreEqual(HttpStatusCode.BadRequest, ex.HttpStatus);
                Assert.AreEqual(ErrorResponseCode.FeatureNotSupportedForServerlessWorkspace, ex.ErrorCode);
            }
        }

        [TestMethod("Updating Default Catalog for a serverless workspace isn't allowed")]
        public async Task UCIsNotAllowedForServerlessWorkspace()
        {
            using (new AppConfigSettingScope(this.ConfigurationSettings))
            {
                var incomingWorkspaceProperties = base.IncomingWorkspaceProperties;

                incomingWorkspaceProperties.IsUcEnabled = true;

                var ex = await Assert.ThrowsExceptionAsync<ErrorResponseMessageException>(
                async () => await this.DatabricksV11Controller.PutApplication(
                     Constants.Common.SubscriptionId,
                     Constants.Common.ResourceGroupName,
                     Constants.Common.ApplicationName,
                     this.IncomingWorkspace
                ));

                Assert.AreEqual(HttpStatusCode.BadRequest, ex.HttpStatus);
                Assert.AreEqual(ErrorResponseCode.FeatureNotSupportedForServerlessWorkspace, ex.ErrorCode);
            }
        }

        [TestMethod("Updating NPIP settings for a serverless workspace isn't allowed")]
        public async Task NPIPIsNotAllowedForServerlessWorkspace()
        {
            using (new AppConfigSettingScope(this.ConfigurationSettings))
            {
                var incomingWorkspaceProperties = base.IncomingWorkspaceProperties;

                incomingWorkspaceProperties.RequiredNsgRules = RequiredNetworkSecurityGroupType.NoAzureDatabricksRules;
                incomingWorkspaceProperties.PublicNetworkAccess = PublicNetworkAccessStatus.Disabled;

                var ex = await Assert.ThrowsExceptionAsync<ErrorResponseMessageException>(
                async () => await this.DatabricksV11Controller.PutApplication(
                     Constants.Common.SubscriptionId,
                     Constants.Common.ResourceGroupName,
                     Constants.Common.ApplicationName,
                     this.IncomingWorkspace
                ));

                Assert.AreEqual(HttpStatusCode.BadRequest, ex.HttpStatus);
                Assert.AreEqual(ErrorResponseCode.FeatureNotSupportedForServerlessWorkspace, ex.ErrorCode);
            }
        }
        #endregion

    }
}
