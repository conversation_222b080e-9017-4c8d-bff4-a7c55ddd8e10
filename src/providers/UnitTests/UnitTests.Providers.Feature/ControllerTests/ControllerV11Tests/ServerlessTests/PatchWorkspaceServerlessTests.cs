//------------------------------------------------------------
// Copyright (c) Microsoft Corporation.  All rights reserved.
//------------------------------------------------------------

namespace UnitTests.Providers.Feature.ControllerTests.ControllerV11Tests
{
    using System.Net;
    using System.Threading.Tasks;
    using Microsoft.AspNetCore.Mvc;
    using Microsoft.VisualStudio.TestTools.UnitTesting;
    using Microsoft.WindowsAzure.ResourceStack.Common.Collections;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Application.Data.Definitions;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Common.ErrorResponses;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Feature.Controllers.ControllerV11;
    using Newtonsoft.Json.Linq;

    [TestClass]
    public class PatchWorkspaceServerlessTests : UpdateBaseControllerV11ServerlessTest
    {
        protected override JToken Sku
        {
            get
            {
                return JToken.Parse(@"{ ""name"": ""Premium""}");
            }
        }

        private ApplicationProperties existingWorkspaceProperties;
        protected override ApplicationProperties ExistingWorkspaceProperties
        {
            get
            {
                if (existingWorkspaceProperties == null)
                {
                    existingWorkspaceProperties = base.ExistingWorkspaceProperties;
                }

                return existingWorkspaceProperties;
            }
        }

        private WorkspaceV11 incomingWorkspace;

        protected override WorkspaceV11 IncomingWorkspace
        {
            get
            {
                if (incomingWorkspace == null)
                {
                    incomingWorkspace = new WorkspaceV11
                    {
                        Location = Constants.Common.Location,
                        Sku = this.Sku
                    };
                }

                return incomingWorkspace;
            }

        }

        private DatabricksWorkspaceDefinition resourceDefinition;
        protected override DatabricksWorkspaceDefinition WorkspaceDefinition
        {
            get
            {
                if (resourceDefinition == null)
                {
                    resourceDefinition = base.WorkspaceDefinition;
                    resourceDefinition.Properties = null;
                    resourceDefinition.Tags = new InsensitiveDictionary<string>
                    {
                        { "key1", "value1" },
                        { "key2", "value2" }
                    };
                }

                return resourceDefinition;
            }
        }

        [TestMethod("Updating Sku for a serverless workspace should fail")]
        public async Task SkuDowngradeIsNotAllowedForServerlessWorkspace()
        {
            using (new AppConfigSettingScope(this.ConfigurationSettings))
            {
                this.IncomingWorkspace.Sku = JToken.Parse(@"{ ""name"": ""Standard""}");

                var ex = await Assert.ThrowsExceptionAsync<ErrorResponseMessageException>(
                async () => await this.DatabricksV11Controller.PatchApplication(
                     Constants.Common.SubscriptionId,
                     Constants.Common.ResourceGroupName,
                     Constants.Common.ApplicationName,
                     this.IncomingWorkspace
                ));

                Assert.AreEqual(HttpStatusCode.BadRequest, ex.HttpStatus);
                Assert.AreEqual(ErrorResponseCode.SkuUpdateNotAllowedUsingPatch, ex.ErrorCode);
            }
        }

        [TestMethod("Patch with not null properties for a serverless workspace should fail")]
        public async Task PatchWithPropertiesNotAllowedForServerlessWorkspace()
        {
            using (new AppConfigSettingScope(this.ConfigurationSettings))
            {
                this.IncomingWorkspace.Properties = this.IncomingWorkspaceProperties;

                var ex = await Assert.ThrowsExceptionAsync<ErrorResponseMessageException>(
                async () => await this.DatabricksV11Controller.PatchApplication(
                     Constants.Common.SubscriptionId,
                     Constants.Common.ResourceGroupName,
                     Constants.Common.ApplicationName,
                     this.IncomingWorkspace
                ));

                Assert.AreEqual(HttpStatusCode.BadRequest, ex.HttpStatus);
                Assert.AreEqual(ErrorResponseCode.InvalidAppliancePropertiesForPatch, ex.ErrorCode);
            }
        }

        [TestMethod("Returns a status code OK")]
        public void TestReturnsCreatedStatusCode()
        {
            using (new AppConfigSettingScope(this.ConfigurationSettings))
            {
                var response = this.DatabricksController.PatchApplication(
                Constants.Common.SubscriptionId,
                Constants.Common.ResourceGroupName,
                Constants.Common.ApplicationName,
                this.IncomingWorkspace
                ).Result;

                Assert.AreEqual((response as ObjectResult).StatusCode, (int)HttpStatusCode.OK);
            }
        }

        [TestMethod("Updating Tag for a serverless workspace should success")]
        public async Task UpdateTagForServerlessWorkspace()
        {
            using (new AppConfigSettingScope(this.ConfigurationSettings))
            {
                this.IncomingWorkspace.Tags = new InsensitiveDictionary<string>
                {
                    { "tag1", "value1" },
                    { "tag2", "value2" }
                };

                var response = await this.DatabricksV11Controller.PatchApplication(
                   Constants.Common.SubscriptionId,
                   Constants.Common.ResourceGroupName,
                   Constants.Common.ApplicationName,
                   this.IncomingWorkspace) as ObjectResult;

                Assert.IsNotNull(response);
                Assert.AreEqual(HttpStatusCode.OK, (HttpStatusCode)response.StatusCode);
                Assert.IsNotNull(response.Value);
                var returnedWorkspace = ConvertResponse<WorkspaceV11>(response);

                Assert.IsNotNull(returnedWorkspace.Tags);
                Assert.AreEqual("value1", returnedWorkspace.Tags["tag1"]);
                Assert.AreEqual("value2", returnedWorkspace.Tags["tag2"]);
            }
        }
    }
}
