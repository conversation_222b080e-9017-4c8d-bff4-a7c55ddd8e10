﻿//------------------------------------------------------------
// Copyright (c) Microsoft Corporation.  All rights reserved.
//------------------------------------------------------------

namespace UnitTests.Providers.Feature.ControllerTests.ControllerV11Tests.GetTests
{
    using System;
    using System.Collections.Generic;
    using System.Net;
    using System.Net.Http;
    using System.Threading.Tasks;
    using Microsoft.AspNetCore.Http;
    using Microsoft.AspNetCore.Mvc;
    using Microsoft.VisualStudio.TestTools.UnitTesting;
    using Microsoft.WindowsAzure.ResourceStack.Common.Collections;
    using Microsoft.WindowsAzure.ResourceStack.Common.EventSources;
    using Microsoft.WindowsAzure.ResourceStack.Common.Extensions;
    using Microsoft.WindowsAzure.ResourceStack.Common.Instrumentation;
    using Microsoft.WindowsAzure.ResourceStack.Common.Storage;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Application.Data.DataProviders;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Application.Data.Definitions;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Application.Data.Entities;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Common.Data;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Common.Definitions;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Common.Engines;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Common.Entities;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Common.Extensions;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Feature.Components;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Feature.Controllers.ControllerV11;
    using Moq;
    using Newtonsoft.Json.Linq;

    [TestClass]
    public class GetServerlessWorkspaceTest
    {
        protected bool includeNullPropertiesWorkspace = false;
        protected void setIncludeNullPropertiesWorkspace() { includeNullPropertiesWorkspace = true; }

        private HttpRequestMessage request;

        /// <summary>
        /// Gets the request object.
        /// For PUT and PATCH requests, the request's Content property should contain the workspace definition.
        /// </summary>
        protected virtual HttpRequestMessage Request
        {
            get
            {
                if (request == null)
                {
                    request = new HttpRequestMessage();
                    request.RequestUri = new Uri("http://localhost");
                    request.Headers.Referrer = new Uri(Constants.Common.Referrer);
                }

                return request;
            }
        }
        private T ConvertResponse<T>(ObjectResult result)
        {
            return (T)result.Value;
        }

        private ApplianceEntity existingApplianceEntity;
        protected ApplianceEntity ExistingApplianceEntity
        {
            get
            {
                if (existingApplianceEntity == null)
                {
                    existingApplianceEntity = getApplianceEntity(true);
                }

                return existingApplianceEntity;
            }
        }

        private ApplianceEntity existingApplianceEntityNullProperties;
        protected ApplianceEntity ExistingApplianceEntityNullProperties
        {
            get
            {
                if (existingApplianceEntityNullProperties == null)
                {
                    existingApplianceEntityNullProperties = getApplianceEntity(false);
                }

                return existingApplianceEntityNullProperties;
            }
        }

        protected ApplianceEntity getApplianceEntity(bool hasProperties)
        {
            ApplianceEntity applianceEntity = new ApplianceEntity
            {
                SubscriptionId = Constants.Common.SubscriptionId,
                ResourceGroup = Constants.Common.ResourceGroupName,
                Name = Constants.Common.ApplicationName,
                Sku = this.ExistingEntitySku,
                Location = string.Empty,
                Metadata = this.ExistingApplianceMetadata,
            };
            if (hasProperties) 
            {
                applianceEntity.Properties = this.ExistingWorkspaceProperties;
            }
  
            return applianceEntity;
        }


        private ApplianceMetadata existingApplianceMetadata;
        protected virtual ApplianceMetadata ExistingApplianceMetadata
        {
            get
            {
                if (existingApplianceMetadata == null)
                {
                    existingApplianceMetadata = new ApplianceMetadata();
                    existingApplianceMetadata.OperationStatuses = new InsensitiveDictionary<ApplicationOperationStatus>();
                }

                return existingApplianceMetadata;
            }
        }

        protected virtual JToken ExistingEntitySku
        {
            get
            {
                return JToken.Parse(@"{ ""name"": ""Premium""}");
            }
        }

        private ApplicationProperties existingWorkspaceProperties;
        protected virtual ApplicationProperties ExistingWorkspaceProperties
        {
            get
            {
                if (existingWorkspaceProperties == null)
                {
                    existingWorkspaceProperties = new ApplicationProperties();
                    ExistingWorkspaceProperties.CreatedBy = new ApplicationClientDetailsEntity();
                    ExistingWorkspaceProperties.CreatedBy.ApplicationId = Constants.Common.CreatedByAppId;
                    ExistingWorkspaceProperties.CreatedBy.Oid = Constants.Common.CreatedByPrincipalOid;
                    ExistingWorkspaceProperties.CreatedBy.Puid = Constants.Common.CreatedByPrincipalLegacyPuid;

                    existingWorkspaceProperties.ComputeMode = ComputeMode.Serverless;
                    existingWorkspaceProperties.ManagedResourceGroupId = null;
                    existingWorkspaceProperties.ProvisioningState = ProvisioningState.Succeeded;
                    existingWorkspaceProperties.WorkspaceId = Constants.Common.InternalWorkspaceId;
                    existingWorkspaceProperties.WorkspaceUrl = Constants.Common.WorkspaceUrl;

                    existingWorkspaceProperties.Parameters = this.ExistingWorkspaceParameters;

                    existingWorkspaceProperties.PublisherPackageId = Constants.Common.PublisherPackageId;
                }

                return existingWorkspaceProperties;
            }
        }

        private InsensitiveDictionary<JToken> existingWorkspaceParameters;
        protected virtual InsensitiveDictionary<JToken> ExistingWorkspaceParameters
        {
            get
            {
                if (existingWorkspaceParameters == null)
                {
                    existingWorkspaceParameters = new InsensitiveDictionary<JToken>();
                    existingWorkspaceParameters.Add("amlWorkspaceId", JToken.Parse(@"{ ""type"": ""String"", ""value"": """ + Constants.AMLLinking.AMLWorkspaceId + @"""}"));
                }

                return existingWorkspaceParameters;
            }
        }


        private Mock<IFrontdoorEngine> frontdoorEngine;

        /// <summary>
        ///  Mocks the FrontdoorEngine class.
        ///  The StorageApiVersion and FrontdoorEndpointUri properties are mocked to return dummy constants.
        /// </summary>
        protected virtual Mock<IFrontdoorEngine> FrontdoorEngineMock
        {
            get
            {
                if (frontdoorEngine == null)
                {
                    frontdoorEngine = new Mock<IFrontdoorEngine>();
                    frontdoorEngine.Setup(o => o.StorageApiVersion).Returns(Constants.StorageEncryption.StorageApiVersion);
                    frontdoorEngine.Setup(o => o.FrontdoorEndpointUri).Returns(new Uri(Constants.Common.FrontDoorEngineUri));
                }

                return frontdoorEngine;
            }
        }


        private DatabricksV11Controller databricksController;

        /// <summary>
        /// Gets the controller object whose public methods are being tested in the unit test.
        /// </summary>
        protected DatabricksV11Controller DatabricksController
        {
            get
            {
                if (databricksController == null)
                {
                    databricksController = new DatabricksV11Controller(
                        providerConfiguration: this.ApplicationProviderConfiguration.Object,
                        accessConnectorProviderConfiguration: this.AccessConnectorProviderConfiguration.Object);
                }
                databricksController.ControllerContext = new ControllerContext
                {
                    HttpContext = new DefaultHttpContext()
                };
                databricksController.ControllerContext.HttpContext.Request.Method = "GET";
                databricksController.ControllerContext.HttpContext.Request.Headers["Referer"] = Constants.Common.Referrer;
                databricksController.ControllerContext.HttpContext.Request.Scheme = "https";
                databricksController.ControllerContext.HttpContext.Request.Host = new HostString("localhost");
                return databricksController;
            }
        }


        private Mock<IApplianceDataProvider> applianceDataProvider;
        /// <summary>
        /// Mocks the ApplianceDataProvider class.
        /// The ApplianceDataProvider class is usually used to retrieve workspace details stored in the data store.
        /// Here, we mock the following class methods:
        ///    * The FindAppliance() method is mocked to return the value of the ExistingApplianceEntity class.
        /// </summary>
        protected virtual Mock<IApplianceDataProvider> ApplianceDataProvider
        {
            get
            {
                if (applianceDataProvider == null)
                {
                    var entities = new List<ApplianceEntity>();
                    entities.Add(this.ExistingApplianceEntity);
                    if (this.includeNullPropertiesWorkspace) 
                    {
                        entities.Add(ExistingApplianceEntityNullProperties);
                    }

                    var listResult = new SegmentedResult<ApplianceEntity>();
                    listResult.Entities = entities.ToArray();


                    applianceDataProvider = new Mock<IApplianceDataProvider>();
                    applianceDataProvider.Setup(
                        o => o.FindAppliancesSegmented(
                            It.IsAny<string>(),
                            It.IsAny<string>(),
                            It.IsAny<int?>(),
                            It.IsAny<DataContinuationToken>()
                    )).ReturnsAsync(listResult);

                    var entityResult = this.includeNullPropertiesWorkspace ? 
                        this.existingApplianceEntityNullProperties : this.existingApplianceEntity;

                    applianceDataProvider.Setup(
                        o => o.FindAppliance(
                            It.IsAny<string>(), It.IsAny<string>(), It.IsAny<string>())
                        ).ReturnsAsync(entityResult);
                }

                return applianceDataProvider;
            }
        }

        private Dictionary<string, string> publisherPackageIdMappings;
        /// <summary>
        /// Gets the  publisher package id mappings.
        /// </summary>
        protected virtual Dictionary<string, string> PublisherPackageIdMappings
        {
            get
            {
                if (publisherPackageIdMappings == null)
                {
                    publisherPackageIdMappings = new Dictionary<string, string>();
                    publisherPackageIdMappings.Add(Constants.Common.ApiVersion, Constants.Common.PublisherPackageId);
                }
                return publisherPackageIdMappings;
            }
        }

        private MarketplaceAppliancePackage marketPlaceAppliancePackage;

        /// <summary>
        /// Gets the dummy MarketPlaceAppliancePackage object.
        /// </summary>
        protected virtual MarketplaceAppliancePackage MarketPlaceAppliancePackage
        {
            get
            {
                if (marketPlaceAppliancePackage == null)
                {
                    marketPlaceAppliancePackage = new MarketplaceAppliancePackage();
                    marketPlaceAppliancePackage.IsEnabled = true;
                    marketPlaceAppliancePackage.Authorizations = this.Authorizations;
                    marketPlaceAppliancePackage.TenantId = Constants.Common.PublisherTenantId;
                }

                return marketPlaceAppliancePackage;
            }
        }

        private List<ApplicationAuthorization> authorizations;

        /// <summary>
        /// Gets the list of authorizations
        /// </summary>
        protected virtual ApplicationAuthorization[] Authorizations
        {
            get
            {
                if (authorizations == null)
                {
                    authorizations = new List<ApplicationAuthorization>();
                    var authorization = new ApplicationAuthorization();
                    authorization.PrincipalId = Constants.Common.AuthorizationPrincipalId;
                    authorization.RoleDefinitionId = Constants.Common.AuthorizationRoleDefinitionId;
                    authorizations.Add(authorization);
                }
                return authorizations.ToArray();
            }
        }

        /// <summary>
        /// Returns a mock of the ApplicationPackageDataProvider object.
        /// The FindMarketplaceAppliancePackage() method is mocked to return the value of the MarketPlaceAppliancePackage object.
        /// </summary>
        private Mock<IApplicationPackageDataProvider> appliancePackageDataProvider;
        protected virtual Mock<IApplicationPackageDataProvider> AppliancePackageDataProvider
        {
            get
            {
                if (appliancePackageDataProvider == null)
                {
                    appliancePackageDataProvider = new Mock<IApplicationPackageDataProvider>();

                    appliancePackageDataProvider.Setup(
                        o => o.FindMarketplaceAppliancePackage(Constants.Common.PublisherPackageId)
                        ).Returns(this.MarketPlaceAppliancePackage);

                    // Provide a MarketPlaceAppliancePackage even when there's no PublisherPackageId, due to corrupted workspaces.
                    appliancePackageDataProvider.Setup(
                        o => o.FindMarketplaceAppliancePackage(string.Empty)
                        ).Returns(this.MarketPlaceAppliancePackage);
                }

                return appliancePackageDataProvider;
            }
        }

        private Mock<IApplicationProviderConfiguration> applicationProviderConfiguration;
        protected virtual Mock<IApplicationProviderConfiguration> ApplicationProviderConfiguration
        {
            get
            {
                if (applicationProviderConfiguration == null)
                {
                    applicationProviderConfiguration = new Mock<IApplicationProviderConfiguration>();

                    applicationProviderConfiguration.Setup(
                        o => o.GetValidatedPublisherPackageIdMappings()
                        ).Returns(PublisherPackageIdMappings);

                    applicationProviderConfiguration.Setup(
                        o => o.AppliancePackageDataProvider
                        ).Returns(this.AppliancePackageDataProvider.Object);

                    applicationProviderConfiguration.Setup(
                        o => o.ApplianceDataProvider
                        ).Returns(this.ApplianceDataProvider.Object);
                }

                return applicationProviderConfiguration;
            }
        }

        private Mock<ICommonEventSource> logger;

        public Mock<ICommonEventSource> Logger
        {
            get { return this.logger ?? (this.logger = new Mock<ICommonEventSource>()); }
        }

        private readonly Action<AccessConnectorEntity> saveAction = new Action<AccessConnectorEntity>((entity) => { });

        private Mock<IAccessConnectorDataProvider> accessConnectorDataProvider;

        public Mock<IAccessConnectorDataProvider> AccessConnectorDataProvider
        {
            get
            {
                if (this.accessConnectorDataProvider == null)
                {
                    this.accessConnectorDataProvider = new Mock<IAccessConnectorDataProvider>();

                    this.accessConnectorDataProvider.Setup(
                        o => o.SaveAccessConnector(
                            It.IsAny<AccessConnectorEntity>())
                    ).Callback(this.saveAction);
                }

                return this.accessConnectorDataProvider;
            }
        }

        private Mock<IAccessConnectorProviderConfiguration> accessConnectorProviderConfiguration;

        protected virtual Mock<IAccessConnectorProviderConfiguration> AccessConnectorProviderConfiguration
        {
            get
            {
                if (accessConnectorProviderConfiguration == null)
                {
                    accessConnectorProviderConfiguration = new Mock<IAccessConnectorProviderConfiguration>();

                    accessConnectorProviderConfiguration.Setup(
                        o => o.Logger)
                        .Returns(this.Logger.Object);

                    accessConnectorProviderConfiguration.Setup(
                        o => o.AccessConnectorDataProvider)
                        .Returns(this.AccessConnectorDataProvider.Object);
                }

                return accessConnectorProviderConfiguration;
            }
        }

        #region Setup the correlation context
        protected virtual void SetUpCorrelationContext(string apiVersion = Constants.Common.DatabricksV11ControllerApiVersion)
        {
            RequestCorrelationContext context = new RequestCorrelationContext();
            context.SetAdditionalProperties(new OrdinalDictionary<string>());
            context.AdditionalProperties.Add(RequestCorrelationContextExtensions.HeaderResourceHomeTenantId, Constants.Common.HomeTenantId);

            var identity = new RequestIdentity();
            identity.Claims = new Dictionary<string, string>();
            identity.Claims.Add(Constants.Common.PrincipalOidClaim, Constants.Common.CreatedByPrincipalOid);
            identity.Claims.Add(Constants.Common.PrincipalLegacyPuidClaim, Constants.Common.CreatedByPrincipalLegacyPuid);
            identity.Claims.Add(Constants.Common.PrincipalApplicationIdClaim, Constants.Common.CreatedByAppId);
            context.Initialize(apiVersion: apiVersion);
            context.SetAuthenticationIdentity(identity);

            RequestCorrelationContext.Current.Initialize(context);
            RequestCorrelationContext.Current.CorrelationId = Constants.Common.CorrelationId;


        }
        #endregion

        [TestInitialize]
        public virtual void SetUp()
        {
            SetUpCorrelationContext();
            FrontdoorEngineProviderConfiguration.Instance.InitializeFrontdoorEngine(this.FrontdoorEngineMock.Object);
        }

        [TestCleanup]
        public virtual void TearDown()
        {
            RequestCorrelationContext.Current.Dispose();
        }

        [TestMethod("Gets all workspaces in a subscription")]
        public async Task TestGetAllWorkspaces()
        {
            var response = await this.DatabricksController.GetApplications(
                Constants.Common.SubscriptionId) as ObjectResult;
            Assert.AreEqual((int) HttpStatusCode.OK, response.StatusCode);

            var workspaces = (response.Value as ResponseWithContinuation<WorkspaceV11[]>).Value;
            Assert.AreEqual(1, workspaces.Length);
            Assert.AreEqual(ComputeMode.Serverless, workspaces.First().Properties.ComputeMode);
        }

        [TestMethod("Gets workspaces by resource group")]
        public async Task TestGetWorkspacesByResourceGroup()
        {
            var response = await this.DatabricksController.GetApplications(
                Constants.Common.SubscriptionId,
                Constants.Common.ResourceGroupName
                ) as ObjectResult;
            Assert.AreEqual((int) HttpStatusCode.OK, response.StatusCode);

            var workspaces = (response.Value as ResponseWithContinuation<WorkspaceV11[]>).Value;
            Assert.AreEqual(1, workspaces.Length);
            Assert.AreEqual(ComputeMode.Serverless, workspaces.First().Properties.ComputeMode);
        }

        [TestMethod("Gets a single workspace")]
        public async Task TestGetWorkspace()
        {
            var response = await this.DatabricksController.GetApplication(
                Constants.Common.SubscriptionId,
                Constants.Common.ResourceGroupName,
                Constants.Common.ApplicationName) as ObjectResult;

            Assert.AreEqual((int) HttpStatusCode.OK, response.StatusCode);
        }

        [TestMethod("Gets all non-corrupted workspaces in a subscription")]
        public async Task TestGetAllNonCorruptedWorkspaces()
        {
            setIncludeNullPropertiesWorkspace();
            var response = await this.DatabricksController.GetApplications(
                Constants.Common.SubscriptionId) as ObjectResult;
            Assert.AreEqual((int)HttpStatusCode.OK, response.StatusCode);

            var workspaces = (response.Value as ResponseWithContinuation<WorkspaceV11[]>).Value;
            Assert.AreEqual(1, workspaces.Length);
            Assert.IsNotNull(workspaces.First().Properties);
        }

        [TestMethod("Gets non-corrupted workspaces by resource group")]
        public async Task TestGetNonCorruptedWorkspacesByResourceGroup()
        {
            setIncludeNullPropertiesWorkspace();
            var response = await this.DatabricksController.GetApplications(
                Constants.Common.SubscriptionId,
                Constants.Common.ResourceGroupName
                ) as ObjectResult;
            Assert.AreEqual((int)HttpStatusCode.OK, response.StatusCode);

            var workspaces = (response.Value as ResponseWithContinuation<WorkspaceV11[]>).Value;
            Assert.AreEqual(1, workspaces.Length);
            Assert.IsNotNull(workspaces.First().Properties);
        }

        [TestMethod("Gets a single corrupted workspace")]
        public async Task TestGetWorkspaceWithNullProperties()
        {
            setIncludeNullPropertiesWorkspace();
            var response = await this.DatabricksController.GetApplication(
                Constants.Common.SubscriptionId,
                Constants.Common.ResourceGroupName,
                Constants.Common.ApplicationName) as ObjectResult;
            Assert.AreEqual((int)HttpStatusCode.OK, response.StatusCode);

            var workspace = ConvertResponse<WorkspaceV11>(response);
            var workspaceProperties = workspace.Properties;
            var emptyProperties = new WorkspacePropertiesV11();
            foreach (var property in workspaceProperties.GetType().GetProperties())
            {
                Assert.AreEqual(property.GetValue(workspaceProperties), property.GetValue(emptyProperties));
            }
        }

        [TestMethod("Saved ApplianceEntity object should contain 'ComputeMode' and UnSet MRG for Serverless")]
        public async Task TestGetWorkspaceShouldContainComputeMode()
        {
            var response = await this.DatabricksController.GetApplication(
                Constants.Common.SubscriptionId,
                Constants.Common.ResourceGroupName,
                Constants.Common.ApplicationName) as ObjectResult;

            var responseObject = ConvertResponse<WorkspaceV11>(response);

            Assert.IsNotNull(responseObject.Properties.ComputeMode);
            Assert.AreEqual(responseObject.Properties.ComputeMode, Constants.Common.ServerlessComputeMode);

            Assert.IsNull(responseObject.Properties.ManagedResourceGroupId);
        }
    }
}
