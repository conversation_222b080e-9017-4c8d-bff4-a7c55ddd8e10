//------------------------------------------------------------
// Copyright (c) Microsoft Corporation.  All rights reserved.
//------------------------------------------------------------

namespace UnitTests.Providers.Feature.ControllerTests.ControllerV11Tests
{
    using System.Net;
    using System.Threading.Tasks;
    using Microsoft.AspNetCore.Mvc;
    using Microsoft.VisualStudio.TestTools.UnitTesting;
    using Microsoft.WindowsAzure.ResourceStack.Common.Collections;
    using Microsoft.WindowsAzure.ResourceStack.Common.Json;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Application.Data.Definitions;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Application.Data.Definitions.Enums;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Common.Constants;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Common.Data;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Common.Definitions;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Common.ErrorResponses;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Feature.Controllers.ControllerV11;
    using Newtonsoft.Json.Linq;

    [TestClass]
    public class UpdateWorkspaceTests : UpdateBaseControllerV11Test
    {
        protected override JToken Sku
        {
            get
            {
                return JToken.Parse(@"{ ""name"": ""Premium""}");
            }
        }

        private InsensitiveDictionary<JToken> existingWorkspaceParameters;
        protected override InsensitiveDictionary<JToken> ExistingWorkspaceParameters
        {
            get
            {
                if (existingWorkspaceParameters == null)
                {
                    existingWorkspaceParameters = base.ExistingWorkspaceParameters;

                    existingWorkspaceParameters.Add("loadBalancerId", JToken.Parse(@"{ ""type"": ""String"", ""value"": """ + Constants.Common.LoadBalancerId + @"""}"));
                    existingWorkspaceParameters.Add("loadBalancerBackendPoolName", JToken.Parse(@"{ ""type"": ""String"", ""value"": """ + Constants.Common.LoadBalancerBackendPoolName + @"""}"));
                }
                return existingWorkspaceParameters;
            }
        }

        private ApplicationProperties existingWorkspaceProperties;
        protected override ApplicationProperties ExistingWorkspaceProperties
        {
            get
            {
                if (existingWorkspaceProperties == null)
                {
                    existingWorkspaceProperties = base.ExistingWorkspaceProperties;
                }

                return existingWorkspaceProperties;
            }
        }

        private WorkspacePropertiesV11 incomingWorkspaceProperties;
        protected override WorkspacePropertiesV11 IncomingWorkspaceProperties
        {
            get
            {
                if (incomingWorkspaceProperties == null)
                {
                    incomingWorkspaceProperties = base.IncomingWorkspaceProperties;
                }

                return incomingWorkspaceProperties;
            }
        }

        private WorkspaceV11 incomingWorkspace;
        protected override WorkspaceV11 IncomingWorkspace
        {
            get
            {
                if (incomingWorkspace == null)
                {
                    incomingWorkspace = base.IncomingWorkspace;
                    incomingWorkspace.Properties = this.incomingWorkspaceProperties;
                    incomingWorkspace.Sku = this.Sku;
                }

                return incomingWorkspace;
            }
        }

        [TestMethod("Updating vNet details for a workspace with managed vNet NPIP and LoadBalancerId configured should fail")]
        public async Task VNetUpdateIsNotAllowedForWorkspacesWithLoadBalancerIdConfigured()
        {
            using (new AppConfigSettingScope(this.ConfigurationSettings))
            {
                var incomingWorkspaceProperties = base.IncomingWorkspaceProperties;
                incomingWorkspaceProperties.Parameters.Add("customVirtualNetworkId",
                    JToken.Parse(@"{ ""type"": ""String"", ""value"": """ + Constants.Common.VNetId2 + @"""}"));
                incomingWorkspaceProperties.Parameters.Add("customPublicSubnetName",
                    JToken.Parse(@"{ ""type"": ""String"", ""value"": """ + Constants.Common.CustomPublicSubnetName + @"""}"));
                incomingWorkspaceProperties.Parameters.Add("customPrivateSubnetName",
                    JToken.Parse(@"{ ""type"": ""String"", ""value"": """ + Constants.Common.CustomPrivateSubnetName + @"""}"));

                this.IncomingWorkspace.Properties = incomingWorkspaceProperties;

                this.ExistingWorkspaceParameters.Remove("loadBalancerBackendPoolName");

                var ex = await Assert.ThrowsExceptionAsync<ErrorResponseMessageException>(
                async () => await this.DatabricksV11Controller.PutApplication(
                     Constants.Common.SubscriptionId,
                     Constants.Common.ResourceGroupName,
                     Constants.Common.ApplicationName,
                     this.IncomingWorkspace
                ));

                Assert.AreEqual(HttpStatusCode.BadRequest, ex.HttpStatus);
                Assert.AreEqual(ErrorResponseCode.VirtualNetworkIdChangeNotAllowed, ex.ErrorCode);
            }
        }

        [TestMethod("Updating vNet details for a workspace with managed vNet NPIP and LoadBalancerBackendPoolName configured should fail")]
        public async Task VNetUpdateIsNotAllowedForWorkspacesWithLoadBalancerBackendPoolNameConfigured()
        {
            using (new AppConfigSettingScope(this.ConfigurationSettings))
            {
                var incomingWorkspaceProperties = base.IncomingWorkspaceProperties;
                incomingWorkspaceProperties.Parameters.Add("customVirtualNetworkId",
                    JToken.Parse(@"{ ""type"": ""String"", ""value"": """ + Constants.Common.VNetId2 + @"""}"));
                incomingWorkspaceProperties.Parameters.Add("customPublicSubnetName",
                    JToken.Parse(@"{ ""type"": ""String"", ""value"": """ + Constants.Common.CustomPublicSubnetName + @"""}"));
                incomingWorkspaceProperties.Parameters.Add("customPrivateSubnetName",
                    JToken.Parse(@"{ ""type"": ""String"", ""value"": """ + Constants.Common.CustomPrivateSubnetName + @"""}"));

                this.IncomingWorkspace.Properties = incomingWorkspaceProperties;

                this.ExistingWorkspaceParameters.Remove("loadBalancerId");

                var ex = await Assert.ThrowsExceptionAsync<ErrorResponseMessageException>(
                async () => await this.DatabricksV11Controller.PutApplication(
                     Constants.Common.SubscriptionId,
                     Constants.Common.ResourceGroupName,
                     Constants.Common.ApplicationName,
                     this.IncomingWorkspace
                ));

                Assert.AreEqual(HttpStatusCode.BadRequest, ex.HttpStatus);
                Assert.AreEqual(ErrorResponseCode.VirtualNetworkIdChangeNotAllowed, ex.ErrorCode);
            }
        }

        [TestMethod("Updating vNet details, along with enableNoPublicIp for a workspace with managed vNet NPIP isn't allowed")]
        public async Task VNetUpdateIsNotAllowedAlongWithNPIPForManagedVNetWorkspace()
        {
            using (new AppConfigSettingScope(this.ConfigurationSettings))
            {
                var incomingWorkspaceProperties = base.IncomingWorkspaceProperties;
                incomingWorkspaceProperties.Parameters.Add("customVirtualNetworkId",
                    JToken.Parse(@"{ ""type"": ""String"", ""value"": """ + Constants.Common.VNetId2 + @"""}"));
                incomingWorkspaceProperties.Parameters.Add("customPublicSubnetName",
                    JToken.Parse(@"{ ""type"": ""String"", ""value"": """ + Constants.Common.CustomPublicSubnetName + @"""}"));
                incomingWorkspaceProperties.Parameters.Add("customPrivateSubnetName",
                    JToken.Parse(@"{ ""type"": ""String"", ""value"": """ + Constants.Common.CustomPrivateSubnetName + @"""}"));
                incomingWorkspaceProperties.Parameters.Add("enableNoPublicIp",
                    JToken.Parse(@"{ ""type"": ""Bool"", ""value"": ""false""}"));

                this.IncomingWorkspace.Properties = incomingWorkspaceProperties;

                this.ExistingWorkspaceParameters.Remove("loadBalancerId");
                this.ExistingWorkspaceParameters.Remove("loadBalancerBackendPoolName");

                var ex = await Assert.ThrowsExceptionAsync<ErrorResponseMessageException>(
                async () => await this.DatabricksV11Controller.PutApplication(
                     Constants.Common.SubscriptionId,
                     Constants.Common.ResourceGroupName,
                     Constants.Common.ApplicationName,
                     this.IncomingWorkspace
                ));

                Assert.AreEqual(HttpStatusCode.BadRequest, ex.HttpStatus);
                Assert.AreEqual(ErrorResponseCode.NetworkUpdateNotSupportedInManagedVnet, ex.ErrorCode);
            }
        }

        [TestMethod("Updating vNet details, along with requiredNsgRules for a workspace with managed vNet NPIP isn't allowed")]
        public async Task VNetUpdateIsNotAllowedAlongWithRequiredNsgRulesForManagedVNetWorkspace()
        {
            using (new AppConfigSettingScope(this.ConfigurationSettings))
            {
                var incomingWorkspaceProperties = base.IncomingWorkspaceProperties;
                incomingWorkspaceProperties.Parameters.Add("customVirtualNetworkId",
                    JToken.Parse(@"{ ""type"": ""String"", ""value"": """ + Constants.Common.VNetId2 + @"""}"));
                incomingWorkspaceProperties.Parameters.Add("customPublicSubnetName",
                    JToken.Parse(@"{ ""type"": ""String"", ""value"": """ + Constants.Common.CustomPublicSubnetName + @"""}"));
                incomingWorkspaceProperties.Parameters.Add("customPrivateSubnetName",
                    JToken.Parse(@"{ ""type"": ""String"", ""value"": """ + Constants.Common.CustomPrivateSubnetName + @"""}"));
                incomingWorkspaceProperties.Parameters.Add("enableNoPublicIp",
                    JToken.Parse(@"{ ""type"": ""Bool"", ""value"": ""true""}"));

                incomingWorkspaceProperties.RequiredNsgRules = RequiredNetworkSecurityGroupType.NoAzureDatabricksRules;

                this.IncomingWorkspace.Properties = incomingWorkspaceProperties;

                this.ExistingWorkspaceParameters.Remove("loadBalancerId");
                this.ExistingWorkspaceParameters.Remove("loadBalancerBackendPoolName");

                var ex = await Assert.ThrowsExceptionAsync<ErrorResponseMessageException>(
                async () => await this.DatabricksV11Controller.PutApplication(
                     Constants.Common.SubscriptionId,
                     Constants.Common.ResourceGroupName,
                     Constants.Common.ApplicationName,
                     this.IncomingWorkspace
                ));

                Assert.AreEqual(HttpStatusCode.BadRequest, ex.HttpStatus);
                Assert.AreEqual(ErrorResponseCode.NetworkUpdateNotSupportedInManagedVnet, ex.ErrorCode);
            }
        }

        [TestMethod("Updating vNet details, along with PublicNetworkAccess for a workspace with managed vNet NPIP isn't allowed")]
        public async Task VNetUpdateIsNotAllowedAlongWithPublicNetworkAccessForManagedVNetWorkspace()
        {
            using (new AppConfigSettingScope(this.ConfigurationSettings))
            {
                var incomingWorkspaceProperties = base.IncomingWorkspaceProperties;
                incomingWorkspaceProperties.Parameters.Add("customVirtualNetworkId",
                    JToken.Parse(@"{ ""type"": ""String"", ""value"": """ + Constants.Common.VNetId2 + @"""}"));
                incomingWorkspaceProperties.Parameters.Add("customPublicSubnetName",
                    JToken.Parse(@"{ ""type"": ""String"", ""value"": """ + Constants.Common.CustomPublicSubnetName + @"""}"));
                incomingWorkspaceProperties.Parameters.Add("customPrivateSubnetName",
                    JToken.Parse(@"{ ""type"": ""String"", ""value"": """ + Constants.Common.CustomPrivateSubnetName + @"""}"));
                incomingWorkspaceProperties.Parameters.Add("enableNoPublicIp",
                    JToken.Parse(@"{ ""type"": ""Bool"", ""value"": ""true""}"));

                incomingWorkspaceProperties.RequiredNsgRules = RequiredNetworkSecurityGroupType.NoAzureDatabricksRules;
                incomingWorkspaceProperties.PublicNetworkAccess = PublicNetworkAccessStatus.Disabled;

                this.IncomingWorkspace.Properties = incomingWorkspaceProperties;

                this.ExistingWorkspaceProperties.RequiredNsgRules = RequiredNetworkSecurityGroupType.NoAzureDatabricksRules;
                this.ExistingWorkspaceProperties.PublicNetworkAccess = PublicNetworkAccessStatus.Enabled;

                this.ExistingWorkspaceParameters.Remove("loadBalancerId");
                this.ExistingWorkspaceParameters.Remove("loadBalancerBackendPoolName");

                var ex = await Assert.ThrowsExceptionAsync<ErrorResponseMessageException>(
                async () => await this.DatabricksV11Controller.PutApplication(
                     Constants.Common.SubscriptionId,
                     Constants.Common.ResourceGroupName,
                     Constants.Common.ApplicationName,
                     this.IncomingWorkspace
                ));

                Assert.AreEqual(HttpStatusCode.BadRequest, ex.HttpStatus);
                Assert.AreEqual(ErrorResponseCode.NetworkUpdateNotSupportedInManagedVnet, ex.ErrorCode);
            }
        }

        [TestMethod("Updating vNet details for a workspace with managed vNet should be allowed")]
        public async Task VNetUpdateAllowedForWorkspacesWithManagedVNet()
        {
            using (new AppConfigSettingScope(this.ConfigurationSettings))
            {
                var incomingWorkspaceProperties = base.IncomingWorkspaceProperties;
                incomingWorkspaceProperties.Parameters.Add("customVirtualNetworkId",
                    JToken.Parse(@"{ ""type"": ""String"", ""value"": """ + Constants.Common.VNetId1 + @"""}"));
                incomingWorkspaceProperties.Parameters.Add("customPublicSubnetName",
                    JToken.Parse(@"{ ""type"": ""String"", ""value"": """ + Constants.Common.CustomPublicSubnetName + @"""}"));
                incomingWorkspaceProperties.Parameters.Add("customPrivateSubnetName",
                    JToken.Parse(@"{ ""type"": ""String"", ""value"": """ + Constants.Common.CustomPrivateSubnetName + @"""}"));
                incomingWorkspaceProperties.Parameters.Add("enableNoPublicIp",
                    JToken.Parse(@"{ ""type"": ""Bool"", ""value"": ""true""}"));

                this.IncomingWorkspace.Properties = incomingWorkspaceProperties;

                this.ExistingWorkspaceParameters.Remove("loadBalancerBackendPoolName");
                this.ExistingWorkspaceParameters.Remove("loadBalancerId");

                var response = await this.DatabricksV11Controller.PutApplication(
                   Constants.Common.SubscriptionId,
                   Constants.Common.ResourceGroupName,
                   Constants.Common.ApplicationName,
                   this.IncomingWorkspace) as ObjectResult;

                Assert.IsNotNull(response);
                Assert.AreEqual(HttpStatusCode.Created, (HttpStatusCode)response.StatusCode);
                Assert.IsNotNull(response.Value);

                var returnedWorkspace = ConvertResponse<WorkspaceV11>(response);
                Assert.IsNotNull(returnedWorkspace.Properties.Parameters);
                Assert.AreEqual(Constants.Common.VNetId1, returnedWorkspace.Properties.Parameters[ProviderConstants.Databricks.CustomVirtualNetworkIdProperty].TryGetProperty<string>("value"));
                Assert.AreEqual(Constants.Common.CustomPublicSubnetName, returnedWorkspace.Properties.Parameters[ProviderConstants.Databricks.CustomPublicSubnetNameProperty].TryGetProperty<string>("value"));
                Assert.AreEqual(Constants.Common.CustomPrivateSubnetName, returnedWorkspace.Properties.Parameters[ProviderConstants.Databricks.CustomPrivateSubnetNameProperty].TryGetProperty<string>("value"));
                Assert.AreEqual(true, returnedWorkspace.Properties.Parameters[ProviderConstants.Databricks.EnableNoPublicIpProperty].TryGetProperty<bool>("value"));
            }
        }

        [TestMethod("Updating vNet details for a workspace in failed state with managed vNet should be allowed")]
        public async Task VNetUpdateAllowedForWorkspacesWithManagedVNetInFailedState()
        {
            using (new AppConfigSettingScope(this.ConfigurationSettings))
            {
                var incomingWorkspaceProperties = base.IncomingWorkspaceProperties;
                incomingWorkspaceProperties.Parameters.Add("customVirtualNetworkId",
                    JToken.Parse(@"{ ""type"": ""String"", ""value"": """ + Constants.Common.VNetId1 + @"""}"));
                incomingWorkspaceProperties.Parameters.Add("customPublicSubnetName",
                    JToken.Parse(@"{ ""type"": ""String"", ""value"": """ + Constants.Common.CustomPublicSubnetName + @"""}"));
                incomingWorkspaceProperties.Parameters.Add("customPrivateSubnetName",
                    JToken.Parse(@"{ ""type"": ""String"", ""value"": """ + Constants.Common.CustomPrivateSubnetName + @"""}"));
                incomingWorkspaceProperties.Parameters.Add("enableNoPublicIp",
                    JToken.Parse(@"{ ""type"": ""Bool"", ""value"": ""true""}"));

                this.IncomingWorkspace.Properties = incomingWorkspaceProperties;

                this.ExistingWorkspaceParameters.Remove("loadBalancerBackendPoolName");
                this.ExistingWorkspaceParameters.Remove("loadBalancerId");
                this.ExistingWorkspaceProperties.ProvisioningState = ProvisioningState.Failed;

                var response = await this.DatabricksV11Controller.PutApplication(
                   Constants.Common.SubscriptionId,
                   Constants.Common.ResourceGroupName,
                   Constants.Common.ApplicationName,
                   this.IncomingWorkspace) as ObjectResult;

                Assert.IsNotNull(response);
                Assert.AreEqual(HttpStatusCode.Created, (HttpStatusCode)response.StatusCode);
                Assert.IsNotNull(response.Value);

                var returnedWorkspace = ConvertResponse<WorkspaceV11>(response);
                Assert.IsNotNull(returnedWorkspace.Properties.Parameters);
                Assert.AreEqual(Constants.Common.VNetId1, returnedWorkspace.Properties.Parameters[ProviderConstants.Databricks.CustomVirtualNetworkIdProperty].TryGetProperty<string>("value"));
                Assert.AreEqual(Constants.Common.CustomPublicSubnetName, returnedWorkspace.Properties.Parameters[ProviderConstants.Databricks.CustomPublicSubnetNameProperty].TryGetProperty<string>("value"));
                Assert.AreEqual(Constants.Common.CustomPrivateSubnetName, returnedWorkspace.Properties.Parameters[ProviderConstants.Databricks.CustomPrivateSubnetNameProperty].TryGetProperty<string>("value"));
                Assert.AreEqual(true, returnedWorkspace.Properties.Parameters[ProviderConstants.Databricks.EnableNoPublicIpProperty].TryGetProperty<bool>("value"));
            }
        }

        [TestMethod("Updating Compute Mode for an existing workspace to Serverless not allowed")]
        public async Task UpdateComputeModeForExistingHybridWorkspaceNotAllowed()
        {
            using (new AppConfigSettingScope(this.ConfigurationSettings))
            {
                var incomingWorkspaceProperties = base.IncomingWorkspaceProperties;
                incomingWorkspaceProperties.ComputeMode = ComputeMode.Serverless;
                incomingWorkspaceProperties.ManagedResourceGroupId = Constants.Common.EmptyMrgId;

                this.IncomingWorkspace.Properties = incomingWorkspaceProperties;

                this.ExistingWorkspaceParameters.Remove("loadBalancerBackendPoolName");
                this.ExistingWorkspaceParameters.Remove("loadBalancerId");

                var ex = await Assert.ThrowsExceptionAsync<ErrorResponseMessageException>(
                    async () => await this.DatabricksV11Controller.PutApplication(
                         Constants.Common.SubscriptionId,
                         Constants.Common.ResourceGroupName,
                         Constants.Common.ApplicationName,
                         this.IncomingWorkspace
                    ));

                Assert.AreEqual(HttpStatusCode.BadRequest, ex.HttpStatus);
                Assert.AreEqual(ErrorResponseCode.FeatureNotSupportedForServerlessWorkspace, ex.ErrorCode);
            }
        }

        [TestMethod("Updating an existing workspace with null properties is not allowed")]
        public async Task UpdateAnExistingWorkspaceWithNullPropertiesNotAllowed()
        {
            using (new AppConfigSettingScope(this.ConfigurationSettings))
            {
                var incomingWorkspaceProperties = base.IncomingWorkspaceProperties;
                incomingWorkspaceProperties.Parameters.Add("customVirtualNetworkId",
                    JToken.Parse(@"{ ""type"": ""String"", ""value"": """ + Constants.Common.VNetId1 + @"""}"));
                incomingWorkspaceProperties.Parameters.Add("customPublicSubnetName",
                    JToken.Parse(@"{ ""type"": ""String"", ""value"": """ + Constants.Common.CustomPublicSubnetName + @"""}"));
                incomingWorkspaceProperties.Parameters.Add("customPrivateSubnetName",
                    JToken.Parse(@"{ ""type"": ""String"", ""value"": """ + Constants.Common.CustomPrivateSubnetName + @"""}"));
                incomingWorkspaceProperties.Parameters.Add("enableNoPublicIp",
                    JToken.Parse(@"{ ""type"": ""Bool"", ""value"": ""true""}"));

                this.IncomingWorkspace.Properties = incomingWorkspaceProperties;

                this.ExistingApplianceEntity.Properties = null;

                var ex = await Assert.ThrowsExceptionAsync<ErrorResponseMessageException>(
                    async () => await this.DatabricksV11Controller.PutApplication(
                         Constants.Common.SubscriptionId,
                         Constants.Common.ResourceGroupName,
                         Constants.Common.ApplicationName,
                         this.IncomingWorkspace
                    ));

                Assert.AreEqual(HttpStatusCode.Conflict, ex.HttpStatus);
                Assert.AreEqual(ErrorResponseCode.UpdateOnCorruptedWorkspaceNotAllowed, ex.ErrorCode);
            }
        }

        [TestMethod("Updating an existing workspace with null parameters is not allowed")]
        public async Task UpdateAnExistingWorkspaceWithNullParametersNotAllowed()
        {
            using (new AppConfigSettingScope(this.ConfigurationSettings))
            {
                var incomingWorkspaceProperties = base.IncomingWorkspaceProperties;
                incomingWorkspaceProperties.Parameters.Add("customVirtualNetworkId",
                    JToken.Parse(@"{ ""type"": ""String"", ""value"": """ + Constants.Common.VNetId1 + @"""}"));
                incomingWorkspaceProperties.Parameters.Add("customPublicSubnetName",
                    JToken.Parse(@"{ ""type"": ""String"", ""value"": """ + Constants.Common.CustomPublicSubnetName + @"""}"));
                incomingWorkspaceProperties.Parameters.Add("customPrivateSubnetName",
                    JToken.Parse(@"{ ""type"": ""String"", ""value"": """ + Constants.Common.CustomPrivateSubnetName + @"""}"));
                incomingWorkspaceProperties.Parameters.Add("enableNoPublicIp",
                    JToken.Parse(@"{ ""type"": ""Bool"", ""value"": ""true""}"));

                this.IncomingWorkspace.Properties = incomingWorkspaceProperties;

                this.ExistingApplianceEntity.Properties.Parameters = null;

                var ex = await Assert.ThrowsExceptionAsync<ErrorResponseMessageException>(
                    async () => await this.DatabricksV11Controller.PutApplication(
                         Constants.Common.SubscriptionId,
                         Constants.Common.ResourceGroupName,
                         Constants.Common.ApplicationName,
                         this.IncomingWorkspace
                    ));

                Assert.AreEqual(HttpStatusCode.Conflict, ex.HttpStatus);
                Assert.AreEqual(ErrorResponseCode.UpdateOnCorruptedWorkspaceNotAllowed, ex.ErrorCode);
            }
        }
    }
}
