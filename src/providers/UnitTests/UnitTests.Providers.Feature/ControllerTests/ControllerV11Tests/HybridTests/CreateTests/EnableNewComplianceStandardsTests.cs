﻿//------------------------------------------------------------
// Copyright (c) Microsoft Corporation.  All rights reserved.
//------------------------------------------------------------

namespace UnitTests.Providers.Feature.ControllerTests.ControllerV11Tests
{
    using System.Threading.Tasks;
    using Microsoft.AspNetCore.Mvc;
    using Microsoft.VisualStudio.TestTools.UnitTesting;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Application.Data.Definitions;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Application.Data.Definitions.Enums;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Feature.Controllers.ControllerV11;
    using Newtonsoft.Json.Linq;

    [TestClass]
    public class EnableNewComplianceStandardsTests : BaseControllerV11Test
    {
        protected override JToken Sku
        {
            get
            {
                return JToken.Parse(@"{ ""name"": ""Premium""}");
            }
        }

        protected override string ApiVersion
        {
            get
            {
                return Constants.Common.Databricks20250301PreviewApiVersion;
            }
        }

        private WorkspacePropertiesV11 incomingWorkspaceProperties;
        protected override WorkspacePropertiesV11 IncomingWorkspaceProperties
        {
            get
            {
                if (incomingWorkspaceProperties == null)
                {
                    incomingWorkspaceProperties = base.IncomingWorkspaceProperties;
                    incomingWorkspaceProperties.Parameters.Add("customVirtualNetworkId",
                        JToken.Parse(@"{ ""type"": ""String"", ""value"": """ + Constants.Common.VNetId2 + @"""}"));
                    incomingWorkspaceProperties.Parameters.Add("customPublicSubnetName",
                        JToken.Parse(@"{ ""type"": ""String"", ""value"": """ + Constants.Common.CustomPublicSubnetName + @"""}"));
                    incomingWorkspaceProperties.Parameters.Add("customPrivateSubnetName",
                        JToken.Parse(@"{ ""type"": ""String"", ""value"": """ + Constants.Common.CustomPrivateSubnetName + @"""}"));

                    incomingWorkspaceProperties.EnhancedSecurityCompliance = new EnhancedSecurityCompliance()
                    {
                        ComplianceSecurityProfile = new ComplianceSecurityProfile()
                        {
                            Value = ComplianceSecurityProfileStatus.Enabled,
                            ComplianceStandards = new[] { ComplianceStandard.PCI_DSS, ComplianceStandard.HIPAA, ComplianceStandard.CYBER_ESSENTIAL_PLUS, ComplianceStandard.HITRUST }
                        },
                        AutomaticClusterUpdate = new AutomaticClusterUpdate()
                        {
                            Value = AutomaticClusterUpdateStatus.Enabled
                        },
                        EnhancedSecurityMonitoring = new EnhancedSecurityMonitoring()
                        {
                            Value = EnhancedSecurityMonitoringStatus.Enabled
                        }
                    };

                }

                return incomingWorkspaceProperties;
            }
        }



        private WorkspaceV11 incomingWorkspace;
        protected override WorkspaceV11 IncomingWorkspace
        {
            get
            {
                if (incomingWorkspace == null)
                {
                    incomingWorkspace = base.IncomingWorkspace;
                    incomingWorkspace.Properties = this.incomingWorkspaceProperties;
                    incomingWorkspace.Location = Constants.Common.UKSouthRegion;
                    incomingWorkspace.Sku = this.Sku;
                }

                return incomingWorkspace;
            }
        }

        [TestMethod("Workspace is properly created with all 4 Compliance Standards selected")]
        public async Task CSPEnabledWithNoComplianceStandards()
        {
            using (new AppConfigSettingScope(this.ConfigurationSettings))
            {
                var response = await this.DatabricksV11Controller.PutApplication(
                    Constants.Common.SubscriptionId,
                    Constants.Common.ResourceGroupName,
                    Constants.Common.ApplicationName,
                    GetRequestObject<WorkspaceV11>()) as ObjectResult;

                Assert.IsNotNull(response.Value);
                var returnedWorkspace = ConvertResponse<WorkspaceV11>(response);

                var enhancedSecurityComplianceResponse = returnedWorkspace?.Properties?.EnhancedSecurityCompliance;

                var correctEnhancedSecurityCompliance = new EnhancedSecurityCompliance()
                {
                    AutomaticClusterUpdate = new AutomaticClusterUpdate()
                    {
                        Value = AutomaticClusterUpdateStatus.Enabled
                    },
                    ComplianceSecurityProfile = new ComplianceSecurityProfile()
                    {
                        Value = ComplianceSecurityProfileStatus.Enabled,
                        ComplianceStandards = new ComplianceStandard[] { ComplianceStandard.PCI_DSS, ComplianceStandard.HIPAA, ComplianceStandard.CYBER_ESSENTIAL_PLUS, ComplianceStandard.HITRUST }
                    },
                    EnhancedSecurityMonitoring = new EnhancedSecurityMonitoring()
                    {
                        Value = EnhancedSecurityMonitoringStatus.Enabled
                    }
                };

                Assert.IsNotNull(enhancedSecurityComplianceResponse);
                Assert.IsNotNull(enhancedSecurityComplianceResponse.AutomaticClusterUpdate);
                Assert.IsNotNull(enhancedSecurityComplianceResponse.ComplianceSecurityProfile);
                Assert.IsNotNull(enhancedSecurityComplianceResponse.ComplianceSecurityProfile.ComplianceStandards);
                Assert.IsNotNull(enhancedSecurityComplianceResponse.EnhancedSecurityMonitoring);
                Assert.AreEqual(AutomaticClusterUpdateStatus.Enabled, enhancedSecurityComplianceResponse.AutomaticClusterUpdate.Value);
                Assert.AreEqual(ComplianceSecurityProfileStatus.Enabled, enhancedSecurityComplianceResponse.ComplianceSecurityProfile.Value);
                Assert.AreEqual(4, enhancedSecurityComplianceResponse.ComplianceSecurityProfile.ComplianceStandards.Length);
                Assert.AreEqual(EnhancedSecurityMonitoringStatus.Enabled, enhancedSecurityComplianceResponse.EnhancedSecurityMonitoring.Value);
            }
        }
    }
}