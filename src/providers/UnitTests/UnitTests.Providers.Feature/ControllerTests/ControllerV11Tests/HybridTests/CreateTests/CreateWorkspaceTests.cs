﻿//------------------------------------------------------------
// Copyright (c) Microsoft Corporation.  All rights reserved.
//------------------------------------------------------------

namespace UnitTests.Providers.Feature.ControllerTests.ControllerV11Tests
{
    using System.Net;
    using System.Threading.Tasks;
    using Microsoft.AspNetCore.Mvc;
    using Microsoft.VisualStudio.TestTools.UnitTesting;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Common.ErrorResponses;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Feature.Controllers.ControllerV11;
    using Newtonsoft.Json.Linq;

    [TestClass]
    public class RequestValidationTests : BaseControllerV11Test
    {
        protected override JToken Sku
        {
            get
            {
                return JToken.Parse(@"{ ""name"": ""Premium""}");
            }
        }

        private WorkspacePropertiesV11 incomingWorkspaceProperties;
        protected override WorkspacePropertiesV11 IncomingWorkspaceProperties
        {
            get
            {
                if (incomingWorkspaceProperties == null)
                {
                    incomingWorkspaceProperties = base.IncomingWorkspaceProperties;
                }

                return incomingWorkspaceProperties;
            }
        }

        private WorkspaceV11 incomingWorkspace;
        protected override WorkspaceV11 IncomingWorkspace
        {
            get
            {
                if (incomingWorkspace == null)
                {
                    incomingWorkspace = base.IncomingWorkspace;
                    incomingWorkspace.Properties = this.incomingWorkspaceProperties;
                    incomingWorkspace.Location = Constants.Common.UKSouthRegion;
                    incomingWorkspace.Sku = this.Sku;
                }

                return incomingWorkspace;
            }
        }

        [TestMethod("Workspace creation fails if the request doesn't provide customerVirtualNetworkId")]
        public async Task CreateWorkspaceRequestFailsWithoutVNetId()
        {
            using (new AppConfigSettingScope(this.ConfigurationSettings))
            {
                var ex = await Assert.ThrowsExceptionAsync<ErrorResponseMessageException>(
                async () => await this.DatabricksV11Controller.PutApplication(
                     Constants.Common.SubscriptionId,
                     Constants.Common.ResourceGroupName,
                     Constants.Common.ApplicationName,
                     GetRequestObject<WorkspaceV11>()
                ));

                Assert.AreEqual(HttpStatusCode.BadRequest, ex.HttpStatus);
                Assert.AreEqual(ErrorResponseCode.vNetInjectionIsMandatory, ex.ErrorCode);
            }
        }

        [TestMethod("Workspace creation fails if the request doesn't provide public/private subnet name, but provided vNet Id")]
        public async Task CreateWorkspaceRequestFailsWithoutPublicSubnetName()
        {
            using (new AppConfigSettingScope(this.ConfigurationSettings))
            {
                var incomingWorkspaceProperties = base.IncomingWorkspaceProperties;
                incomingWorkspaceProperties.Parameters.Add("customVirtualNetworkId",
                    JToken.Parse(@"{ ""type"": ""String"", ""value"": """ + Constants.Common.VNetId1 + @"""}"));

                this.IncomingWorkspace.Properties = incomingWorkspaceProperties;

                var ex = await Assert.ThrowsExceptionAsync<ErrorResponseMessageException>(
                async () => await this.DatabricksV11Controller.PutApplication(
                     Constants.Common.SubscriptionId,
                     Constants.Common.ResourceGroupName,
                     Constants.Common.ApplicationName,
                     this.IncomingWorkspace
                ));

                Assert.AreEqual(HttpStatusCode.BadRequest, ex.HttpStatus);
                Assert.AreEqual(ErrorResponseCode.MissingSubnets, ex.ErrorCode);
            }
        }

        [TestMethod("Workspace creation fails if the request doesn't provide private subnet name, but provided vNet Id, public subnet name")]
        public async Task CreateWorkspaceRequestFailsWithoutPrivateSubnetName()
        {
            using (new AppConfigSettingScope(this.ConfigurationSettings))
            {
                var incomingWorkspaceProperties = base.IncomingWorkspaceProperties;
                incomingWorkspaceProperties.Parameters.Add("customVirtualNetworkId",
                    JToken.Parse(@"{ ""type"": ""String"", ""value"": """ + Constants.Common.VNetId1 + @"""}"));
                incomingWorkspaceProperties.Parameters.Add("customPublicSubnetName",
                    JToken.Parse(@"{ ""type"": ""String"", ""value"": """ + Constants.Common.CustomPublicSubnetName + @"""}"));

                this.IncomingWorkspace.Properties = incomingWorkspaceProperties;

                var ex = await Assert.ThrowsExceptionAsync<ErrorResponseMessageException>(
                async () => await this.DatabricksV11Controller.PutApplication(
                     Constants.Common.SubscriptionId,
                     Constants.Common.ResourceGroupName,
                     Constants.Common.ApplicationName,
                     this.IncomingWorkspace
                ));

                Assert.AreEqual(HttpStatusCode.BadRequest, ex.HttpStatus);
                Assert.AreEqual(ErrorResponseCode.MissingSubnets, ex.ErrorCode);
            }
        }

        [TestMethod("Workspace creation fails if the request doesn't provide vNet Id, but provided public/private subnet name")]
        public async Task CreateWorkspaceRequestFailsWithoutvNetButProvidedWithSubnetNames()
        {
            using (new AppConfigSettingScope(this.ConfigurationSettings))
            {
                var incomingWorkspaceProperties = base.IncomingWorkspaceProperties;
                incomingWorkspaceProperties.Parameters.Add("customPublicSubnetName",
                    JToken.Parse(@"{ ""type"": ""String"", ""value"": """ + Constants.Common.CustomPublicSubnetName + @"""}"));
                incomingWorkspaceProperties.Parameters.Add("customPrivateSubnetName",
                    JToken.Parse(@"{ ""type"": ""String"", ""value"": """ + Constants.Common.CustomPrivateSubnetName + @"""}"));

                this.IncomingWorkspace.Properties = incomingWorkspaceProperties;

                var ex = await Assert.ThrowsExceptionAsync<ErrorResponseMessageException>(
                async () => await this.DatabricksV11Controller.PutApplication(
                     Constants.Common.SubscriptionId,
                     Constants.Common.ResourceGroupName,
                     Constants.Common.ApplicationName,
                     this.IncomingWorkspace
                ));

                Assert.AreEqual(HttpStatusCode.BadRequest, ex.HttpStatus);
                Assert.AreEqual(ErrorResponseCode.vNetInjectionIsMandatory, ex.ErrorCode);
            }
        }

        [TestMethod("Workspace creation request should be accepted with correct values for vNet injection")]
        public async Task CreateWorkspaceWithvNetInjectionCorrectConfiguration()
        {
            using (new AppConfigSettingScope(this.ConfigurationSettings))
            {
                var incomingWorkspaceProperties = base.IncomingWorkspaceProperties;

                incomingWorkspaceProperties.Parameters.Add("customVirtualNetworkId",
                    JToken.Parse(@"{ ""type"": ""String"", ""value"": """ + Constants.Common.VNetId1 + @"""}"));
                incomingWorkspaceProperties.Parameters.Add("customPublicSubnetName",
                    JToken.Parse(@"{ ""type"": ""String"", ""value"": """ + Constants.Common.CustomPublicSubnetName + @"""}"));
                incomingWorkspaceProperties.Parameters.Add("customPrivateSubnetName",
                    JToken.Parse(@"{ ""type"": ""String"", ""value"": """ + Constants.Common.CustomPrivateSubnetName + @"""}"));

                this.IncomingWorkspace.Properties = incomingWorkspaceProperties;

                var response = await this.DatabricksV11Controller.PutApplication(
                    Constants.Common.SubscriptionId,
                    Constants.Common.ResourceGroupName,
                    Constants.Common.ApplicationName,
                    this.IncomingWorkspace) as ObjectResult;

                Assert.IsNotNull(response);
                Assert.AreEqual(HttpStatusCode.Created, (HttpStatusCode)response.StatusCode);
                Assert.IsNotNull(response.Value);

                var returnedWorkspace = ConvertResponse<WorkspaceV11>(response);
                Assert.IsNull(returnedWorkspace.Properties.Parameters);
            }
        }

        [TestMethod("Workspace creation request should be fail with correct values for vNet injection and load Balancer ID")]
        public async Task CreateWorkspaceWithvNetInjectionCorrectConfigurationAndLoadBalancerId()
        {
            using (new AppConfigSettingScope(this.ConfigurationSettings))
            {
                var incomingWorkspaceProperties = base.IncomingWorkspaceProperties;

                incomingWorkspaceProperties.Parameters.Add("customVirtualNetworkId",
                    JToken.Parse(@"{ ""type"": ""String"", ""value"": """ + Constants.Common.VNetId1 + @"""}"));
                incomingWorkspaceProperties.Parameters.Add("customPublicSubnetName",
                    JToken.Parse(@"{ ""type"": ""String"", ""value"": """ + Constants.Common.CustomPublicSubnetName + @"""}"));
                incomingWorkspaceProperties.Parameters.Add("customPrivateSubnetName",
                    JToken.Parse(@"{ ""type"": ""String"", ""value"": """ + Constants.Common.CustomPrivateSubnetName + @"""}"));

                incomingWorkspaceProperties.Parameters.Add("loadBalancerId",
                    JToken.Parse(@"{ ""type"": ""String"", ""value"": """ + Constants.Common.LoadBalancerId + @"""}"));

                this.IncomingWorkspace.Properties = incomingWorkspaceProperties;

                var ex = await Assert.ThrowsExceptionAsync<ErrorResponseMessageException>(
                async () => await this.DatabricksV11Controller.PutApplication(
                     Constants.Common.SubscriptionId,
                     Constants.Common.ResourceGroupName,
                     Constants.Common.ApplicationName,
                     this.IncomingWorkspace
                ));

                Assert.AreEqual(HttpStatusCode.BadRequest, ex.HttpStatus);
                Assert.AreEqual(ErrorResponseCode.InvalidLoadBalancerId, ex.ErrorCode);
            }
        }

        [TestMethod("Workspace creation request should be fail with correct values for vNet injection and empty load Balancer ID")]
        public async Task CreateWorkspaceWithvNetInjectionCorrectConfigurationAndEmptyLoadBalancerId()
        {
            using (new AppConfigSettingScope(this.ConfigurationSettings))
            {
                var incomingWorkspaceProperties = base.IncomingWorkspaceProperties;

                incomingWorkspaceProperties.Parameters.Add("customVirtualNetworkId",
                    JToken.Parse(@"{ ""type"": ""String"", ""value"": """ + Constants.Common.VNetId1 + @"""}"));
                incomingWorkspaceProperties.Parameters.Add("customPublicSubnetName",
                    JToken.Parse(@"{ ""type"": ""String"", ""value"": """ + Constants.Common.CustomPublicSubnetName + @"""}"));
                incomingWorkspaceProperties.Parameters.Add("customPrivateSubnetName",
                    JToken.Parse(@"{ ""type"": ""String"", ""value"": """ + Constants.Common.CustomPrivateSubnetName + @"""}"));

                incomingWorkspaceProperties.Parameters.Add("loadBalancerId",
                    JToken.Parse(@"{ ""type"": ""String"", ""value"": """ + string.Empty + @"""}"));

                this.IncomingWorkspace.Properties = incomingWorkspaceProperties;

                var ex = await Assert.ThrowsExceptionAsync<ErrorResponseMessageException>(
                async () => await this.DatabricksV11Controller.PutApplication(
                     Constants.Common.SubscriptionId,
                     Constants.Common.ResourceGroupName,
                     Constants.Common.ApplicationName,
                     this.IncomingWorkspace
                ));

                Assert.AreEqual(HttpStatusCode.BadRequest, ex.HttpStatus);
                Assert.AreEqual(ErrorResponseCode.InvalidLoadBalancerId, ex.ErrorCode);
            }
        }

        [TestMethod("Workspace creation request should be fail with correct values for vNet injection and load Balancer Backend Pool Name")]
        public async Task CreateWorkspaceWithvNetInjectionCorrectConfigurationAndLoadBalancerBackendPoolName()
        {
            using (new AppConfigSettingScope(this.ConfigurationSettings))
            {
                var incomingWorkspaceProperties = base.IncomingWorkspaceProperties;

                incomingWorkspaceProperties.Parameters.Add("customVirtualNetworkId",
                    JToken.Parse(@"{ ""type"": ""String"", ""value"": """ + Constants.Common.VNetId1 + @"""}"));
                incomingWorkspaceProperties.Parameters.Add("customPublicSubnetName",
                    JToken.Parse(@"{ ""type"": ""String"", ""value"": """ + Constants.Common.CustomPublicSubnetName + @"""}"));
                incomingWorkspaceProperties.Parameters.Add("customPrivateSubnetName",
                    JToken.Parse(@"{ ""type"": ""String"", ""value"": """ + Constants.Common.CustomPrivateSubnetName + @"""}"));

                incomingWorkspaceProperties.Parameters.Add("loadBalancerBackendPoolName",
                    JToken.Parse(@"{ ""type"": ""String"", ""value"": """ + Constants.Common.LoadBalancerBackendPoolName + @"""}"));

                this.IncomingWorkspace.Properties = incomingWorkspaceProperties;

                var ex = await Assert.ThrowsExceptionAsync<ErrorResponseMessageException>(
                async () => await this.DatabricksV11Controller.PutApplication(
                     Constants.Common.SubscriptionId,
                     Constants.Common.ResourceGroupName,
                     Constants.Common.ApplicationName,
                     this.IncomingWorkspace
                ));

                Assert.AreEqual(HttpStatusCode.BadRequest, ex.HttpStatus);
                Assert.AreEqual(ErrorResponseCode.InvalidLoadBalancerBackendPoolName, ex.ErrorCode);
            }
        }

        [TestMethod("Workspace creation request should be fail with correct values for vNet injection and empty load Balancer Backend Pool Name")]
        public async Task CreateWorkspaceWithvNetInjectionCorrectConfigurationAndEmptyLoadBalancerBackendPoolName()
        {
            using (new AppConfigSettingScope(this.ConfigurationSettings))
            {
                var incomingWorkspaceProperties = base.IncomingWorkspaceProperties;

                incomingWorkspaceProperties.Parameters.Add("customVirtualNetworkId",
                    JToken.Parse(@"{ ""type"": ""String"", ""value"": """ + Constants.Common.VNetId1 + @"""}"));
                incomingWorkspaceProperties.Parameters.Add("customPublicSubnetName",
                    JToken.Parse(@"{ ""type"": ""String"", ""value"": """ + Constants.Common.CustomPublicSubnetName + @"""}"));
                incomingWorkspaceProperties.Parameters.Add("customPrivateSubnetName",
                    JToken.Parse(@"{ ""type"": ""String"", ""value"": """ + Constants.Common.CustomPrivateSubnetName + @"""}"));

                incomingWorkspaceProperties.Parameters.Add("loadBalancerBackendPoolName",
                    JToken.Parse(@"{ ""type"": ""String"", ""value"": """ + string.Empty + @"""}"));

                this.IncomingWorkspace.Properties = incomingWorkspaceProperties;

                var ex = await Assert.ThrowsExceptionAsync<ErrorResponseMessageException>(
                async () => await this.DatabricksV11Controller.PutApplication(
                     Constants.Common.SubscriptionId,
                     Constants.Common.ResourceGroupName,
                     Constants.Common.ApplicationName,
                     this.IncomingWorkspace
                ));

                Assert.AreEqual(HttpStatusCode.BadRequest, ex.HttpStatus);
                Assert.AreEqual(ErrorResponseCode.InvalidLoadBalancerBackendPoolName, ex.ErrorCode);
            }
        }

        [TestMethod("Workspace creation request should be fail with correct values for vNet injection and with vNet Address Prefix")]
        public async Task CreateWorkspaceWithvNetInjectionCorrectConfigurationAndvNetPrefix()
        {
            using (new AppConfigSettingScope(this.ConfigurationSettings))
            {
                var incomingWorkspaceProperties = base.IncomingWorkspaceProperties;

                incomingWorkspaceProperties.Parameters.Add("customVirtualNetworkId",
                    JToken.Parse(@"{ ""type"": ""String"", ""value"": """ + Constants.Common.VNetId1 + @"""}"));
                incomingWorkspaceProperties.Parameters.Add("customPublicSubnetName",
                    JToken.Parse(@"{ ""type"": ""String"", ""value"": """ + Constants.Common.CustomPublicSubnetName + @"""}"));
                incomingWorkspaceProperties.Parameters.Add("customPrivateSubnetName",
                    JToken.Parse(@"{ ""type"": ""String"", ""value"": """ + Constants.Common.CustomPrivateSubnetName + @"""}"));

                incomingWorkspaceProperties.Parameters.Add("vNetAddressPrefix",
                    JToken.Parse(@"{ ""type"": ""String"", ""value"": """ + Constants.Common.ManagedVNetAddressPrefix + @"""}"));

                this.IncomingWorkspace.Properties = incomingWorkspaceProperties;

                var ex = await Assert.ThrowsExceptionAsync<ErrorResponseMessageException>(
                async () => await this.DatabricksV11Controller.PutApplication(
                     Constants.Common.SubscriptionId,
                     Constants.Common.ResourceGroupName,
                     Constants.Common.ApplicationName,
                     this.IncomingWorkspace
                ));

                Assert.AreEqual(HttpStatusCode.BadRequest, ex.HttpStatus);
                Assert.AreEqual(ErrorResponseCode.InvalidvNetAddressPrefix, ex.ErrorCode);
            }
        }

        [TestMethod("Workspace creation request should be fail with correct values for vNet injection and with NatGatewayName")]
        public async Task CreateWorkspaceWithvNetInjectionCorrectConfigurationAndNatGatewayName()
        {
            using (new AppConfigSettingScope(this.ConfigurationSettings))
            {
                var incomingWorkspaceProperties = base.IncomingWorkspaceProperties;

                incomingWorkspaceProperties.Parameters.Add("customVirtualNetworkId",
                    JToken.Parse(@"{ ""type"": ""String"", ""value"": """ + Constants.Common.VNetId1 + @"""}"));
                incomingWorkspaceProperties.Parameters.Add("customPublicSubnetName",
                    JToken.Parse(@"{ ""type"": ""String"", ""value"": """ + Constants.Common.CustomPublicSubnetName + @"""}"));
                incomingWorkspaceProperties.Parameters.Add("customPrivateSubnetName",
                    JToken.Parse(@"{ ""type"": ""String"", ""value"": """ + Constants.Common.CustomPrivateSubnetName + @"""}"));

                incomingWorkspaceProperties.Parameters.Add("natGatewayName",
                    JToken.Parse(@"{ ""type"": ""String"", ""value"": """ + Constants.Common.NatGatewayResourceName + @"""}"));

                this.IncomingWorkspace.Properties = incomingWorkspaceProperties;

                var ex = await Assert.ThrowsExceptionAsync<ErrorResponseMessageException>(
                async () => await this.DatabricksV11Controller.PutApplication(
                     Constants.Common.SubscriptionId,
                     Constants.Common.ResourceGroupName,
                     Constants.Common.ApplicationName,
                     this.IncomingWorkspace
                ));

                Assert.AreEqual(HttpStatusCode.BadRequest, ex.HttpStatus);
                Assert.AreEqual(ErrorResponseCode.InvalidNatGatewayNameProperty, ex.ErrorCode);
            }
        }

        [TestMethod("Workspace creation request should be fail with correct values for vNet injection and with Public Ip Name")]
        public async Task CreateWorkspaceWithvNetInjectionCorrectConfigurationAndPublicIpName()
        {
            using (new AppConfigSettingScope(this.ConfigurationSettings))
            {
                var incomingWorkspaceProperties = base.IncomingWorkspaceProperties;

                incomingWorkspaceProperties.Parameters.Add("customVirtualNetworkId",
                    JToken.Parse(@"{ ""type"": ""String"", ""value"": """ + Constants.Common.VNetId1 + @"""}"));
                incomingWorkspaceProperties.Parameters.Add("customPublicSubnetName",
                    JToken.Parse(@"{ ""type"": ""String"", ""value"": """ + Constants.Common.CustomPublicSubnetName + @"""}"));
                incomingWorkspaceProperties.Parameters.Add("customPrivateSubnetName",
                    JToken.Parse(@"{ ""type"": ""String"", ""value"": """ + Constants.Common.CustomPrivateSubnetName + @"""}"));

                incomingWorkspaceProperties.Parameters.Add("publicIpName",
                    JToken.Parse(@"{ ""type"": ""String"", ""value"": """ + Constants.Common.PublicIPAddressResourceName + @"""}"));

                this.IncomingWorkspace.Properties = incomingWorkspaceProperties;

                var ex = await Assert.ThrowsExceptionAsync<ErrorResponseMessageException>(
                async () => await this.DatabricksV11Controller.PutApplication(
                     Constants.Common.SubscriptionId,
                     Constants.Common.ResourceGroupName,
                     Constants.Common.ApplicationName,
                     this.IncomingWorkspace
                ));

                Assert.AreEqual(HttpStatusCode.BadRequest, ex.HttpStatus);
                Assert.AreEqual(ErrorResponseCode.InvalidPublicIpNameProperty, ex.ErrorCode);
            }
        }
    }
}