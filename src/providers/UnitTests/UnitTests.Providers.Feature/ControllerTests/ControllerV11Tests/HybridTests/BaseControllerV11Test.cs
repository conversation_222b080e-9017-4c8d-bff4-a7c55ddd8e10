﻿//------------------------------------------------------------
// Copyright (c) Microsoft Corporation.  All rights reserved.
//------------------------------------------------------------

namespace UnitTests.Providers.Feature.ControllerTests.ControllerV11Tests
{
    using System;
    using System.Net.Http;
    using System.Text;
    using System.Threading.Tasks;
    using Microsoft.WindowsAzure.ResourceStack.Common.Json;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Common.Constants;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Common.Definitions;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Common.Engines;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Feature.Controllers.ControllerV11;
    using Moq;
    using Newtonsoft.Json.Linq;

    public class BaseControllerV11Test : BaseControllerTest
    {
        #region Setup Request Correlation Context

        protected override string ApiVersion
        {
            get
            {
                return Constants.Common.DatabricksV11ControllerApiVersion;
            }
        }

        #endregion

        #region Arrange and mock Http request and configuration
        private HttpRequestMessage request;

        /// <summary>
        /// Gets the request object.
        /// For PUT and PATCH requests, the request's Content property should contain the workspace definition.
        /// </summary>
        protected override HttpRequestMessage Request
        {
            get
            {
                if (request == null)
                {
                    request = new HttpRequestMessage();

                    if (this.WorkspaceDefinition != null)
                    {
                        request.Content = this.RequestContent;
                    }

                    request.Headers.Referrer = new Uri(Constants.Common.Referrer);
                }

                return request;
            }
        }

        private StringContent requestContent;

        protected override StringContent RequestContent
        {
            get
            {
                if (requestContent == null)
                {
                    requestContent = new StringContent(this.IncomingWorkspace.ToJson(), Encoding.UTF8, Constants.Common.MediaType);
                }

                return requestContent;
            }
        }
        #endregion

        #region Mock the controller
        private DatabricksV11Controller databricksV11Controller;

        /// <summary>
        /// Gets the controller object whose public methods are being tested in the unit test.
        /// </summary>
        protected DatabricksV11Controller DatabricksV11Controller
        {
            get
            {
                if (databricksV11Controller == null)
                {
                    databricksV11Controller = new DatabricksV11Controller(
                        providerConfiguration: this.ApplicationProviderConfiguration.Object,
                        accessConnectorProviderConfiguration: this.AccessConnectorProviderConfiguration.Object);
                    SetupController(databricksV11Controller);
                }

                return databricksV11Controller;
            }
        }

        private Mock<IFrontdoorEngine> frontdoorEngineMock;
        protected override Mock<IFrontdoorEngine> FrontdoorEngineMock
        {
            get
            {
                if (frontdoorEngineMock == null)
                {
                    frontdoorEngineMock = base.FrontdoorEngineMock;

                    var subscriptionRegistered = new FeatureDefinition
                    {
                        Properties = JToken.Parse("{\"state\": \"Registered\"}")
                    };

                    this.frontdoorEngineMock.Setup<Task<FeatureDefinition>>(o => o.GetFeatureRegistrationByName(
                            It.Is<string>(tenantId => tenantId == Constants.Common.HomeTenantId),
                            It.Is<string>(subId => subId == Constants.Common.SubscriptionId),
                            It.Is<string>(rpNamespace => rpNamespace == Constants.Common.ResourceProviderNamespace),
                            It.Is<string>(featureName =>
                                featureName == ProviderConstants.Databricks.ServerlessWorkspaceFeatureFlagKey)
                        ))
                        .ReturnsAsync(subscriptionRegistered);
                }

                return this.frontdoorEngineMock;
            }
        }
        #endregion

        #region Mock the request object
        private WorkspaceV11 incomingWorkspace;

        protected virtual WorkspaceV11 IncomingWorkspace
        {
            get
            {
                if (incomingWorkspace == null)
                {
                    incomingWorkspace = new WorkspaceV11
                    {
                        Properties = this.IncomingWorkspaceProperties,
                        Location = Constants.Common.Location,
                        Sku = this.Sku
                    };
                }

                return incomingWorkspace;
            }

        }


        private WorkspacePropertiesV11 incomingWorkspaceProperties;

        protected virtual WorkspacePropertiesV11 IncomingWorkspaceProperties
        {
            get
            {
                if (incomingWorkspaceProperties == null)
                {
                    incomingWorkspaceProperties = new WorkspacePropertiesV11
                    {
                        Parameters = this.WorkspaceParameters,
                        ComputeMode = Constants.Common.HybridComputeMode,
                        ManagedResourceGroupId = Constants.Common.ManagedResourceGroupId
                    };
                }

                return incomingWorkspaceProperties;
            }
        }

        #endregion
    }
}
