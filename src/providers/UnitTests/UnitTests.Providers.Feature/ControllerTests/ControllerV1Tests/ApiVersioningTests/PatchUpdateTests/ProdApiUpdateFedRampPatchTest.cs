﻿//------------------------------------------------------------
// Copyright (c) Microsoft Corporation.  All rights reserved.
//------------------------------------------------------------

namespace UnitTests.Providers.Feature.ControllerTests.ControllerV1Tests.ApiVersioningTests.PatchUpdateTests
{
    using System;
    using System.Collections.Generic;
    using System.Linq;
    using System.Net;
    using System.Net.Http;
    using System.Text;
    using System.Threading.Tasks;
    using System.Web.Http;
    using System.Web.Http.Hosting;
    using Microsoft.VisualStudio.TestTools.UnitTesting;
    using Microsoft.WindowsAzure.ResourceStack.Common.Collections;
    using Microsoft.WindowsAzure.ResourceStack.Common.Instrumentation;
    using Microsoft.WindowsAzure.ResourceStack.Common.Json;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Application.Data.Definitions;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Application.Data.Entities;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Feature.Controllers;
    using Moq;
    using Newtonsoft.Json.Linq;

    [TestClass]
    public class ProdApiUpdateFedRampPatchTest: UpdateBaseControllerV1Test
    {
        protected override string ApiVersion
        {
            get
            {
                return Constants.Common.ApiVersion;
            }
        }

        private InsensitiveDictionary<JToken> existingWorkspaceParameters;
        protected override InsensitiveDictionary<JToken> ExistingWorkspaceParameters
        {
            get
            {
                if (existingWorkspaceParameters == null)
                {
                    existingWorkspaceParameters = base.ExistingWorkspaceParameters;
                    existingWorkspaceParameters["enableFedRampCertification"] = JToken.Parse(@"{ ""type"": ""Bool"", ""value"": true }");
                    existingWorkspaceParameters["storageAccountName"] = JToken.Parse(@"{ ""type"": ""String"", ""value"": """ + Constants.Common.StorageAccountName + @"""}");

                }

                return existingWorkspaceParameters;
            }
        }

        

        private DatabricksWorkspaceDefinition resourceDefinition;
        protected override DatabricksWorkspaceDefinition WorkspaceDefinition
        {
            get
            {
                if (resourceDefinition == null)
                {
                    resourceDefinition = base.WorkspaceDefinition;
                    resourceDefinition.Properties = null;
                    resourceDefinition.Tags = new InsensitiveDictionary<string>();
                    resourceDefinition.Tags.Add("key1", "value1");
                    resourceDefinition.Tags.Add("key2", "value2");
                }

                return resourceDefinition;
            }
        }

        [TestMethod("Sends a DB notification on tags update")]
        public async Task TestSendsDBNotification()
        {
            using (new AppConfigSettingScope(this.ConfigurationSettings))
            {
                var response = await this.DatabricksController.PatchApplication(
                Constants.Common.SubscriptionId,
                Constants.Common.ResourceGroupName,
                Constants.Common.ApplicationName,
                this.GetRequestObject<WorkspaceV1>());

                var expectedRequestUri = new Uri(String.Format(
                        "{0}/api/2.0/tenants/{1}/subscriptions/{2}/resourceGroups/{3}/providers/{4}/workspaces/{5}",
                        Constants.Common.FrontDoorEngineUri,
                        Constants.Common.HomeTenantId,
                        Constants.Common.SubscriptionId,
                        Constants.Common.ResourceGroupName,
                        Constants.Common.ResourceProviderNamespace,
                        Constants.Common.ApplicationName));

                this.FrontdoorEngineMock.Verify(
                    o => o.DBWorkspaceNotificationWrapper(
                            It.Is<HttpMethod>(httpMethod => httpMethod.Method == "PUT"),
                            It.Is<Uri>(requestUri => requestUri == expectedRequestUri),
                            It.Is<string>(rpNamespace => rpNamespace == Constants.Common.ResourceProviderNamespace),
                            It.Is<string>(tenantId => tenantId == Constants.Common.HomeTenantId),
                            It.Is<string>(wsId => wsId == Constants.Common.FullyQualifiedWorkspaceId),
                            It.Is<JToken>(requestBody => requestBody["tags"]["key1"].Value<string>() == "value1" &&
                                                         requestBody["tags"]["key2"].Value<string>() == "value2"),
                            It.IsAny<string>(),
                            It.IsAny<bool>(),
                            It.IsAny<KeyValuePair<string, string>[]>()));
            }
        }
    }
}
