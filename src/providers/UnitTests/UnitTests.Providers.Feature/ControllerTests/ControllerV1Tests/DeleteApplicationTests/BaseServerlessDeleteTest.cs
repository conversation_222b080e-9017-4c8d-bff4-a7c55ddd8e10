﻿//------------------------------------------------------------
// Copyright (c) Microsoft Corporation.  All rights reserved.
//------------------------------------------------------------

namespace UnitTests.Providers.Feature.ControllerTests.ControllerV1Tests.DeleteApplicationTests
{
    using System;
    using System.Collections.Generic;
    using System.Net.Http;
    using System.Threading.Tasks;
    using Microsoft.VisualStudio.TestTools.UnitTesting;
    using Microsoft.WindowsAzure.ResourceStack.Common.Collections;
    using Microsoft.WindowsAzure.ResourceStack.Common.EventSources;
    using Microsoft.WindowsAzure.ResourceStack.Common.Instrumentation;
    using Microsoft.WindowsAzure.ResourceStack.Common.Storage;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Application.Data.DataProviders;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Application.Data.Definitions;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Application.Data.Entities;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Common.Data;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Common.Definitions;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Common.Engines;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Common.Entities;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Common.Extensions;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Common.Responses;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Feature.Components;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Feature.Controllers;
    using Moq;
    using Newtonsoft.Json.Linq;

    /// <summary>
    /// Base class for testing delete scenarios.
    /// </summary>
    [TestClass]
    public class BaseServerlessDeleteTest
    {

        private bool isPropertiesNull = false;

        protected void SetPropertiesNull() { isPropertiesNull = true; }

        private HttpRequestMessage request;

        /// <summary>
        /// Gets the request object.
        /// </summary>
        protected virtual HttpRequestMessage Request
        {
            get
            {
                if (request == null)
                {
                    request = new HttpRequestMessage();
                    request.Headers.Referrer = new Uri(Constants.Common.Referrer);
                }

                return request;
            }
        }

        private ApplianceEntity existingApplianceEntity;

        /// <summary>
        /// Returns the existing workspace that we are trying to delete.
        /// </summary>
        protected virtual ApplianceEntity ExistingApplianceEntity
        {
            get
            {
                if (existingApplianceEntity == null)
                {
                    existingApplianceEntity = new ApplianceEntity();
                    existingApplianceEntity.SubscriptionId = Constants.Common.SubscriptionId;
                    existingApplianceEntity.ResourceGroup = Constants.Common.ResourceGroupName;
                    existingApplianceEntity.Name = Constants.Common.ApplicationName;
                    existingApplianceEntity.Sku = JToken.Parse(@"{ ""name"": ""Premium""}");
                    existingApplianceEntity.Location = Constants.Common.Location;

                    existingApplianceEntity.Properties = this.ExistingWorkspaceProperties;

                    existingApplianceEntity.Metadata = this.ExistingApplianceMetadata;
                }

                return existingApplianceEntity;
            }
        }

        private ApplianceMetadata existingApplianceMetadata;

        /// <summary>
        /// Returns the Existing workspace's metadata property.
        /// </summary>
        protected virtual ApplianceMetadata ExistingApplianceMetadata
        {
            get
            {
                if (existingApplianceMetadata == null)
                {
                    existingApplianceMetadata = new ApplianceMetadata();
                    existingApplianceMetadata.OperationStatuses = new InsensitiveDictionary<ApplicationOperationStatus>();
                }

                return existingApplianceMetadata;
            }
        }

        private ApplicationProperties existingWorkspaceProperties;

        /// <summary>
        /// Returns the existing workspace's properties.
        /// </summary>
        protected virtual ApplicationProperties ExistingWorkspaceProperties
        {
            get
            {
                if (existingWorkspaceProperties == null && !isPropertiesNull)
                {
                    existingWorkspaceProperties = new ApplicationProperties();
                    ExistingWorkspaceProperties.CreatedBy = new ApplicationClientDetailsEntity();
                    ExistingWorkspaceProperties.CreatedBy.ApplicationId = Constants.Common.CreatedByAppId;
                    ExistingWorkspaceProperties.CreatedBy.Oid = Constants.Common.CreatedByPrincipalOid;
                    ExistingWorkspaceProperties.CreatedBy.Puid = Constants.Common.CreatedByPrincipalLegacyPuid;

                    ExistingWorkspaceProperties.ComputeMode = ComputeMode.Serverless;
                    existingWorkspaceProperties.ProvisioningState = ProvisioningState.Succeeded;
                    existingWorkspaceProperties.WorkspaceId = Constants.Common.InternalWorkspaceId;
                    existingWorkspaceProperties.WorkspaceUrl = Constants.Common.WorkspaceUrl;

                    existingWorkspaceProperties.Parameters = this.ExistingWorkspaceParameters;

                    existingWorkspaceProperties.PublisherPackageId = Constants.Common.PublisherPackageId;


                }

                return existingWorkspaceProperties;
            }
        }

        private InsensitiveDictionary<JToken> existingWorkspaceParameters;

        /// <summary>
        /// Returns the existing workspace proeprty parameters.
        /// </summary>
        protected virtual InsensitiveDictionary<JToken> ExistingWorkspaceParameters
        {
            get
            {
                if (existingWorkspaceParameters == null)
                {
                    existingWorkspaceParameters = new InsensitiveDictionary<JToken>();
                }

                return existingWorkspaceParameters;
            }
        }


        private Mock<IFrontdoorEngine> frontdoorEngine;

        /// <summary>
        ///  Mocks the FrontdoorEngine class.
        ///  The StorageApiVersion and FrontdoorEndpointUri properties are mocked to return dummy constants.
        /// </summary>
        protected virtual Mock<IFrontdoorEngine> FrontdoorEngineMock
        {
            get
            {
                if (frontdoorEngine == null)
                {
                    var workspaceDetails = new WorkspaceDetails();

                    frontdoorEngine = new Mock<IFrontdoorEngine>();
                    frontdoorEngine.Setup(o => o.StorageApiVersion).Returns(Constants.StorageEncryption.StorageApiVersion);
                    frontdoorEngine.Setup(o => o.FrontdoorEndpointUri).Returns(new Uri(Constants.Common.FrontDoorEngineUri));
                    frontdoorEngine.Setup(o => o.GetDbWorkspaceDetailsAsync<WorkspaceDetails>(
                        It.IsAny<Uri>(),
                        It.IsAny<string>(),
                        It.IsAny<string>(),
                        It.IsAny<string>(),
                        It.IsAny<int>(),
                        It.IsAny<int>()))
                    .Returns(Task.FromResult(workspaceDetails));
                }

                return frontdoorEngine;
            }
        }


        private DatabricksV1Controller databricksController;

        /// <summary>
        /// Gets the controller object whose public methods are being tested in the unit test.
        /// </summary>
        protected virtual DatabricksV1Controller DatabricksController
        {
            get
            {
                if (databricksController == null)
                {
                    databricksController = new DatabricksV1Controller(
                        providerConfiguration: this.ApplicationProviderConfiguration.Object,
                        accessConnectorProviderConfiguration: this.AccessConnectorProviderConfiguration.Object);
                    BaseControllerTest.SetupController(databricksController);
                }

                return databricksController;
            }
        }

        private Mock<IApplianceDataProvider> applianceDataProvider;
        /// <summary>
        /// Mocks the ApplianceDataProvider class.
        /// The ApplianceDataProvider class is usually used to retrieve workspace details stored in the data store.
        /// Here, we mock the following class methods:
        ///    * The FindAppliance() method is mocked to return the value of the ExistingApplianceEntity class.
        /// </summary>
        protected virtual Mock<IApplianceDataProvider> ApplianceDataProvider
        {
            get
            {
                if (applianceDataProvider == null)
                {
                    var entities = new List<ApplianceEntity>
                    {
                        this.ExistingApplianceEntity
                    };

                    var listResult = new SegmentedResult<ApplianceEntity>();
                    listResult.Entities = entities.ToArray();


                    applianceDataProvider = new Mock<IApplianceDataProvider>();
                    applianceDataProvider.Setup(
                        o => o.FindAppliancesSegmented(
                            It.IsAny<string>(),
                            It.IsAny<string>(),
                            It.IsAny<int?>(),
                            It.IsAny<DataContinuationToken>()
                    )).ReturnsAsync(listResult);

                    applianceDataProvider.Setup(
                        o => o.FindAppliance(
                            It.IsAny<string>(), It.IsAny<string>(), It.IsAny<string>())
                        ).ReturnsAsync(this.ExistingApplianceEntity);
                }

                return applianceDataProvider;
            }
        }

        private Dictionary<string, string> publisherPackageIdMappings;

        /// <summary>
        /// Gets the  publisher package id mappings.
        /// </summary>
        protected virtual Dictionary<string, string> PublisherPackageIdMappings
        {
            get
            {
                if (publisherPackageIdMappings == null)
                {
                    publisherPackageIdMappings = new Dictionary<string, string>();
                    publisherPackageIdMappings.Add(Constants.Common.ApiVersion, Constants.Common.PublisherPackageId);
                }
                return publisherPackageIdMappings;
            }
        }

        private MarketplaceAppliancePackage marketPlaceAppliancePackage;

        /// <summary>
        /// Gets the dummy MarketPlaceAppliancePackage object.
        /// </summary>
        protected virtual MarketplaceAppliancePackage MarketPlaceAppliancePackage
        {
            get
            {
                if (marketPlaceAppliancePackage == null)
                {
                    marketPlaceAppliancePackage = new MarketplaceAppliancePackage();
                    marketPlaceAppliancePackage.IsEnabled = true;
                    marketPlaceAppliancePackage.Authorizations = this.Authorizations;
                    marketPlaceAppliancePackage.TenantId = Constants.Common.PublisherTenantId;
                }

                return marketPlaceAppliancePackage;
            }
        }

        private List<ApplicationAuthorization> authorizations;

        /// <summary>
        /// Gets the list of authorizations
        /// </summary>
        protected virtual ApplicationAuthorization[] Authorizations
        {
            get
            {
                if (authorizations == null)
                {
                    authorizations = new List<ApplicationAuthorization>();
                    var authorization = new ApplicationAuthorization();
                    authorization.PrincipalId = Constants.Common.AuthorizationPrincipalId;
                    authorization.RoleDefinitionId = Constants.Common.AuthorizationRoleDefinitionId;
                    authorizations.Add(authorization);
                }
                return authorizations.ToArray();
            }
        }

        /// <summary>
        /// Returns a mock of the ApplicationPackageDataProvider object.
        /// The FindMarketplaceAppliancePackage() method is mocked to return the value of the MarketPlaceAppliancePackage object.
        /// </summary>
        private Mock<IApplicationPackageDataProvider> appliancePackageDataProvider;
        protected virtual Mock<IApplicationPackageDataProvider> AppliancePackageDataProvider
        {
            get
            {
                if (appliancePackageDataProvider == null)
                {
                    appliancePackageDataProvider = new Mock<IApplicationPackageDataProvider>();

                    string publisherPackageId = null;
                    if (!this.isPropertiesNull)
                    {
                        publisherPackageId = Constants.Common.PublisherPackageId;
                    }

                    appliancePackageDataProvider.Setup(
                        o => o.FindMarketplaceAppliancePackage(publisherPackageId)
                        ).Returns(this.MarketPlaceAppliancePackage);
                }

                return appliancePackageDataProvider;
            }
        }

        protected bool ApplianceDeprovisioningJobScheduled = false;

        private Mock<IJobsDataProvider> jobsDataProvider;

        /// <summary>
        /// Mocks the JobsDataProvider class.
        /// The CreateApplianceProvisioningJob() method is mocked such that the job provisioning parameters passed to the function
        /// is saved in the ProvisioningJobApplianceParameters property.
        /// Assert against ProvisioningJobApplianceParameters to verify if CreateApplianceProvisioningJob was called correctly.
        /// </summary>
        protected virtual Mock<IJobsDataProvider> JobsDataProvider
        {
            get
            {
                if (jobsDataProvider == null)
                {
                    jobsDataProvider = new Mock<IJobsDataProvider>();

                    void callback(
                        string subscriptionId,
                        string resourceGroupName,
                        string resourceType,
                        string resourceProviderNamespace,
                        string applianceName,
                        string location,
                        bool retainUcData,
                        DateTime requestStartDateTime,
                        bool delayJobStart = false)
                    {
                        this.ApplianceDeprovisioningJobScheduled = true;
                    }

                    Action<string, string, string, string, string, string, bool, DateTime, bool> action = callback;

                    jobsDataProvider.Setup(
                        o => o.CreateApplianceDeprovisioningJob(
                            It.Is<string>(subId => subId == Constants.Common.SubscriptionId),
                            It.Is<string>(rgName => rgName == Constants.Common.ResourceGroupName),
                            It.Is<string>(resourceType => resourceType == Constants.Common.ResourceType),
                            It.Is<string>(rpNamespace => rpNamespace == Constants.Common.ResourceProviderNamespace),
                            It.Is<string>(name => name == Constants.Common.ApplicationName),
                            It.Is<string>(location => location == Constants.Common.Location),
                            It.IsAny<bool>(),
                            It.IsAny<DateTime>(),
                            It.IsAny<bool>())).Callback(action);
                }

                return jobsDataProvider;

            }
        }


        private Mock<IApplicationProviderConfiguration> applicationProviderConfiguration;

        /// <summary>
        /// Mocks the application provider configuration container class that contains all the other mocks.
        /// </summary>
        protected virtual Mock<IApplicationProviderConfiguration> ApplicationProviderConfiguration
        {
            get
            {
                if (applicationProviderConfiguration == null)
                {
                    applicationProviderConfiguration = new Mock<IApplicationProviderConfiguration>();

                    applicationProviderConfiguration.Setup(
                        o => o.GetValidatedPublisherPackageIdMappings()
                        ).Returns(PublisherPackageIdMappings);

                    applicationProviderConfiguration.Setup(
                        o => o.AppliancePackageDataProvider
                        ).Returns(this.AppliancePackageDataProvider.Object);

                    applicationProviderConfiguration.Setup(
                        o => o.ApplianceDataProvider
                        ).Returns(this.ApplianceDataProvider.Object);

                    applicationProviderConfiguration.Setup(
                       o => o.JobsDataProvider
                       ).Returns(JobsDataProvider.Object);
                }

                return applicationProviderConfiguration;
            }
        }

        private Mock<ICommonEventSource> logger;

        public Mock<ICommonEventSource> Logger
        {
            get { return this.logger ?? (this.logger = new Mock<ICommonEventSource>()); }
        }

        private readonly Action<AccessConnectorEntity> saveAction = new Action<AccessConnectorEntity>((entity) => { });

        private Mock<IAccessConnectorDataProvider> accessConnectorDataProvider;

        public Mock<IAccessConnectorDataProvider> AccessConnectorDataProvider
        {
            get
            {
                if (this.accessConnectorDataProvider == null)
                {
                    this.accessConnectorDataProvider = new Mock<IAccessConnectorDataProvider>();

                    this.accessConnectorDataProvider.Setup(
                        o => o.SaveAccessConnector(
                            It.IsAny<AccessConnectorEntity>())
                    ).Callback(this.saveAction);
                }

                return this.accessConnectorDataProvider;
            }
        }

        private Mock<IAccessConnectorProviderConfiguration> accessConnectorProviderConfiguration;

        protected virtual Mock<IAccessConnectorProviderConfiguration> AccessConnectorProviderConfiguration
        {
            get
            {
                if (accessConnectorProviderConfiguration == null)
                {
                    accessConnectorProviderConfiguration = new Mock<IAccessConnectorProviderConfiguration>();

                    accessConnectorProviderConfiguration.Setup(
                        o => o.Logger)
                        .Returns(this.Logger.Object);

                    accessConnectorProviderConfiguration.Setup(
                        o => o.AccessConnectorDataProvider)
                        .Returns(this.AccessConnectorDataProvider.Object);
                }

                return accessConnectorProviderConfiguration;
            }
        }

        #region Setup the correlation context

        protected virtual string ApiVersion
        {
            get
            {
                return Constants.Common.ApiVersion;
            }
        }


        protected virtual void SetUpCorrelationContext()
        {
            RequestCorrelationContext context = new RequestCorrelationContext();
            context.SetAdditionalProperties(new OrdinalDictionary<string>());
            context.AdditionalProperties.Add(RequestCorrelationContextExtensions.HeaderResourceHomeTenantId, Constants.Common.HomeTenantId);

            var identity = new RequestIdentity();
            identity.Claims = new Dictionary<string, string>();
            identity.Claims.Add(Constants.Common.PrincipalOidClaim, Constants.Common.CreatedByPrincipalOid);
            identity.Claims.Add(Constants.Common.PrincipalLegacyPuidClaim, Constants.Common.CreatedByPrincipalLegacyPuid);
            identity.Claims.Add(Constants.Common.PrincipalApplicationIdClaim, Constants.Common.CreatedByAppId);
            context.Initialize(apiVersion: this.ApiVersion);
            context.SetAuthenticationIdentity(identity);

            RequestCorrelationContext.Current.Initialize(context);
            RequestCorrelationContext.Current.CorrelationId = Constants.Common.CorrelationId;


        }
        #endregion

        [TestInitialize]
        public virtual void SetUp()
        {
            SetUpCorrelationContext();
            FrontdoorEngineProviderConfiguration.Instance.InitializeFrontdoorEngine(this.FrontdoorEngineMock.Object);
        }

        [TestCleanup]
        public virtual void TearDown()
        {
            RequestCorrelationContext.Current.Dispose();
        }

    }
}
