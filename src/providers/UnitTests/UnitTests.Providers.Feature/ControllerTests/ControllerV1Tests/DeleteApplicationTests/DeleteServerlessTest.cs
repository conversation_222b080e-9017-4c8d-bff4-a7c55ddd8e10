﻿//------------------------------------------------------------
// Copyright (c) Microsoft Corporation.  All rights reserved.
//------------------------------------------------------------

namespace UnitTests.Providers.Feature.ControllerTests.ControllerV1Tests.DeleteApplicationTests
{
    using Microsoft.AspNetCore.Mvc;
    using Microsoft.VisualStudio.TestTools.UnitTesting;
    using Newtonsoft.Json.Linq;
    using System;
    using System.Linq;
    using System.Net;
    using System.Text;
    using System.Threading.Tasks;

    /// <summary>
    /// Tests the happy path delete scenarios.
    /// A delete request should return a 202 status code, and queue up an appliance deprovisioning job.
    /// </summary>
    [TestClass]
    public class DeleteServerlessTest: BaseDeleteTest
    {

        [TestMethod("Deletes a workspace")]
        public async Task TestDeleteWorkspace()
        {
            var response = await this.DatabricksController.DeleteApplication(
               Constants.Common.SubscriptionId,
               Constants.Common.ResourceGroupName,
               Constants.Common.ApplicationName);

            Assert.AreEqual((int) HttpStatusCode.Accepted, (response as StatusCodeResult).StatusCode);
        }

        [TestMethod("Deletes a workspace with null properties")]
        public async Task TestDeleteWorkspaceNullProperties()
        {
            this.SetPropertiesNull();
            var response = await this.DatabricksController.DeleteApplication(
               Constants.Common.SubscriptionId,
               Constants.Common.ResourceGroupName,
               Constants.Common.ApplicationName);

            Assert.AreEqual((int)HttpStatusCode.NoContent, (response as StatusCodeResult).StatusCode);
        }

        [TestMethod("Queues up an appliance deprovisioning job")]
        public async Task TestQueuesDeprovisioningJob()
        {
            var response = await this.DatabricksController.DeleteApplication(
               Constants.Common.SubscriptionId,
               Constants.Common.ResourceGroupName,
               Constants.Common.ApplicationName);

            Assert.IsTrue(this.ApplianceDeprovisioningJobScheduled);
        }

        [TestMethod("Verify that the correct job id is returned")]
        public async Task TestVerifyJobId()
        {
            var response = await this.DatabricksController.DeleteApplication(
                Constants.Common.SubscriptionId,
                Constants.Common.ResourceGroupName,
                Constants.Common.ApplicationName);

            var jobHeader = this.DatabricksController.HttpContext.Response.Headers["Location"].ToString();
            var urlTokens = jobHeader.Split('/').Where(str => !String.IsNullOrEmpty(str)).ToArray();

            var encoded = urlTokens.Last().Replace("?api-version=" + this.ApiVersion, "");
            if (encoded.Length % 4 != 0) encoded += new string('=', 4 - encoded.Length % 4);

            var bytesArray = Convert.FromBase64String(encoded);
            var content = Encoding.UTF8.GetString(bytesArray);

            var metadata = JToken.Parse(content);

            Assert.AreEqual(metadata["fullyQualifiedApplianceId"], Constants.Common.FullyQualifiedWorkspaceId);
            Assert.IsTrue(metadata["jobId"].Value<string>().Contains("ApplianceDeprovisioningJob"));
        }
    }
}
