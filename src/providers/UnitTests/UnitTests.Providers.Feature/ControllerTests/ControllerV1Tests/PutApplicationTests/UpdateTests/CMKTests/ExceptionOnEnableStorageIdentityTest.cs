﻿//------------------------------------------------------------
// Copyright (c) Microsoft Corporation.  All rights reserved.
//------------------------------------------------------------

namespace UnitTests.Providers.Feature.ControllerTests.ControllerV1Tests.PutApplicationTests.UpdateTests.CMKTests
{
    using System;
    using System.Collections.Generic;
    using System.Linq;
    using System.Net;
    using System.Net.Http;
    using System.Text;
    using System.Threading.Tasks;
    using System.Web.Http;
    using System.Web.Http.Hosting;
    using Microsoft.VisualStudio.TestTools.UnitTesting;
    using Microsoft.WindowsAzure.ResourceStack.Common.Collections;
    using Microsoft.WindowsAzure.ResourceStack.Common.Instrumentation;
    using Microsoft.WindowsAzure.ResourceStack.Common.Json;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Application.Data.Definitions;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Application.Data.Entities;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Common.Definitions;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Common.Engines;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Common.ErrorResponses;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Feature.Controllers;
    using Moq;
    using Newtonsoft.Json.Linq;

    /// <summary>
    /// In this test case, we are trying to enable the identity on the storage account
    /// by passing the prepareEncryption parameter.
    /// But we have mocked the frontdoor call to throw an exception.
    /// The test documents the expected behaviour when the call to enable identity on storage account fails.
    /// </summary>
    [TestClass]
    public class ExceptionOnEnableStorageIdentity : UpdateBaseControllerV1Test
    {
        protected override JToken Sku
        {
            get
            {
                return JToken.Parse(@"{ ""name"": ""Premium""}");
            }
        }

        private InsensitiveDictionary<JToken> workspaceParameters;

        protected override InsensitiveDictionary<JToken> WorkspaceParameters
        {
            get
            {
                if (workspaceParameters == null)
                {
                    workspaceParameters =base.WorkspaceParameters;
                    workspaceParameters.Add("prepareEncryption", JToken.Parse(@"{ ""type"": ""Bool"", ""value"": true }"));
                }

                return workspaceParameters;
            }
        }

        protected override JToken ExistingEntitySku
        {
            get
            {
                return JToken.Parse(@"{ ""name"": ""Premium""}");
            }
        }


        private Mock<IFrontdoorEngine> frontdoorEngineMock;

        protected override Mock<IFrontdoorEngine> FrontdoorEngineMock
        {
            get
            {
                frontdoorEngineMock = base.FrontdoorEngineMock;

                var storageDefinition = new StorageDefinition();
                storageDefinition.Identity = new JObject
                {
                    new JProperty("principalid", Constants.StorageEncryption.SystemAssignedPrincipalId),
                    new JProperty("tenantid", Constants.StorageEncryption.SystemAssignedTenantId)
                };


                frontdoorEngineMock.Setup(o => o.CallFrontdoor(
                    It.IsAny<HttpMethod>(),
                    It.IsAny<Uri>(),
                    It.IsAny<string>(),
                    It.IsAny<string>(),
                    It.IsAny<StorageDefinition>(),
                    It.IsAny<string>(),
                    It.IsAny<KeyValuePair<string, string>[]>()
                    )
                ).Throws(new ServerErrorResponseMessageException(HttpStatusCode.InternalServerError, "someerrorcode", "someerrormessage", null));

                return frontdoorEngineMock;
            }
        }


        [TestMethod("When call to storage for prepare encryption fails it should return a conflict status code")]
        public async Task TestReturnsConflictStatusCode()
        {
            using (new AppConfigSettingScope(this.ConfigurationSettings))
            {
                var ex = await Assert.ThrowsExceptionAsync<ErrorResponseMessageException>(
                async () => await this.DatabricksController.PutApplication(
                     Constants.Common.SubscriptionId,
                     Constants.Common.ResourceGroupName,
                     Constants.Common.ApplicationName,
                     this.GetRequestObject<WorkspaceV1>()));


                Assert.AreEqual(ErrorResponseCode.EnableMsiForStorageAccountError, ex.ErrorCode);
                Assert.AreEqual(HttpStatusCode.Conflict, ex.HttpStatus);
            }
            
        }
    }
}
