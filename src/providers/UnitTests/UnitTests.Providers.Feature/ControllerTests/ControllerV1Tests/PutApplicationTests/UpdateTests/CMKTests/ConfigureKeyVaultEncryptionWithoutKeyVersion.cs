﻿//------------------------------------------------------------
// Copyright (c) Microsoft Corporation.  All rights reserved.
//------------------------------------------------------------

namespace UnitTests.Providers.Feature.ControllerTests.ControllerV1Tests.PutApplicationTests.UpdateTests.CMKTests
{
    using System;
    using System.Collections.Generic;
    using System.Linq;
    using System.Net;
    using System.Net.Http;
    using System.Text;
    using System.Web.Http;
    using System.Web.Http.Hosting;
    using Microsoft.AspNetCore.Mvc;
    using Microsoft.VisualStudio.TestTools.UnitTesting;
    using Microsoft.WindowsAzure.ResourceStack.Common.Collections;
    using Microsoft.WindowsAzure.ResourceStack.Common.Instrumentation;
    using Microsoft.WindowsAzure.ResourceStack.Common.Json;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Application.Data.Definitions;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Application.Data.Entities;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Common.Definitions;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Common.Engines;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Feature.Controllers;
    using Moq;
    using Newtonsoft.Json.Linq;

    // <summary>
    /// THis test case verifies the behaviour for DBFS encryption using the customer maneged encryption keys.
    /// In this specific test case, we verify that it is possible to configure the encryption without specifying
    /// a specific version of the secret.
    /// 
    /// Upon making this request, the RP invokes the Storage RP via frontdoor to enable encryption
    /// on the storage account. This test case verifies that behavior by asserting on the front door call and
    /// verifying the parameters of the call.
    /// 
    /// Also, we verify whether a notification was sent to DBCS with the encryption parameters, by asserting
    /// on the parameters passed to the DBCS notification frontdoor call.
    /// 
    /// Lastly, we also verify that the appliance entity is saved with the updated encryption parameters.
    /// </summary>
    [TestClass]
    public class ConfigureKeyVaultEncryptionWithoutKeyVersion : UpdateBaseControllerV1Test
    {
        protected override JToken Sku
        {
            get
            {
                return JToken.Parse(@"{ ""name"": ""Premium""}");
            }
        }

        private ApplicationProperties existingWorkspaceProperties;
        protected override ApplicationProperties ExistingWorkspaceProperties
        {
            get
            {
                if (existingWorkspaceProperties == null)
                {
                    existingWorkspaceProperties = base.ExistingWorkspaceProperties;
                    existingWorkspaceProperties.StorageAccountIdentityEntity = new StorageAccountIdentityEntity();
                    existingWorkspaceProperties.StorageAccountIdentityEntity.PrincipalId = Constants.StorageEncryption.SystemAssignedPrincipalId;
                    existingWorkspaceProperties.StorageAccountIdentityEntity.TenantId = Constants.StorageEncryption.SystemAssignedTenantId;
                    existingWorkspaceProperties.StorageAccountIdentityEntity.Type = StorageAccountIdentityType.SystemAssigned;
                }

                return existingWorkspaceProperties;
            }
        }

        
        private InsensitiveDictionary<JToken> workspaceParameters;

        protected override InsensitiveDictionary<JToken> WorkspaceParameters
        {
            get
            {
                if (workspaceParameters == null)
                {
                    workspaceParameters = base.WorkspaceParameters;
                    workspaceParameters.Add("prepareEncryption", JToken.Parse(@"{ ""type"": ""Bool"", ""value"": true }"));
                    workspaceParameters.Add("encryption", JToken.Parse(
                        @"{
                            ""type"": ""Object"", 
                            ""value"": { 
                                ""keySource"": ""Microsoft.Keyvault"",
                                ""keyVaultUri"": ""https://somekeyvault.vault.azure.net"",
                                ""keyName"": ""somekeyname"",
                            }
                        }"
                    ));
                }

                return workspaceParameters;
            }
        }

        [TestMethod("Returns a status code OK")]
        public void TestReturnsCreatedStatusCode()
        {
            using (new AppConfigSettingScope(this.ConfigurationSettings))
            {
                var response = this.DatabricksController.PutApplication(
                Constants.Common.SubscriptionId,
                Constants.Common.ResourceGroupName,
                Constants.Common.ApplicationName,
                this.GetRequestObject<WorkspaceV1>()
                ).Result;

                Assert.AreEqual((response as ObjectResult).StatusCode, (int) HttpStatusCode.OK);
            }
        }

        [TestMethod("ApplianceEntity is saved with storage parameters")]
        public void TestEntityReplacedWithEncryptionParameters()
        {
            using (new AppConfigSettingScope(this.ConfigurationSettings))
            {
                this.ReplaceAction = new Action<ApplianceEntity>((entity) =>
            {
                Assert.IsNotNull(entity.Properties.Parameters["encryption"]);
                Assert.AreEqual("Microsoft.Keyvault", entity.Properties.Parameters["encryption"]["value"]["keySource"].Value<string>());
                Assert.AreEqual("https://somekeyvault.vault.azure.net", entity.Properties.Parameters["encryption"]["value"]["keyvaulturi"].Value<string>());
                Assert.AreEqual("somekeyname", entity.Properties.Parameters["encryption"]["value"]["KeyName"].Value<string>());
            });

                var _ = this.DatabricksController.PutApplication(
                    Constants.Common.SubscriptionId,
                    Constants.Common.ResourceGroupName,
                    Constants.Common.ApplicationName,
                    this.GetRequestObject<WorkspaceV1>()
                    ).Result;
            }
        }


        [TestMethod("Notification sent to databricks")]
        public void TestDBCSNotificationContainsEncryptionParameters()
        {
            using (new AppConfigSettingScope(this.ConfigurationSettings))
            {
                var _ = this.DatabricksController.PutApplication(
                Constants.Common.SubscriptionId,
                Constants.Common.ResourceGroupName,
                Constants.Common.ApplicationName,
                this.GetRequestObject<WorkspaceV1>()
                ).Result;

                var expectedRequestUri = new Uri(String.Format(
                        "{0}/api/2.0/tenants/{1}/subscriptions/{2}/resourceGroups/{3}/providers/{4}/workspaces/{5}",
                        Constants.Common.FrontDoorEngineUri,
                        Constants.Common.HomeTenantId,
                        Constants.Common.SubscriptionId,
                        Constants.Common.ResourceGroupName,
                        Constants.Common.ResourceProviderNamespace,
                        Constants.Common.ApplicationName));

                this.FrontdoorEngineMock.Verify(
                    o => o.DBWorkspaceNotificationWrapper(
                            It.Is<HttpMethod>(httpMethod => httpMethod.Method == "PUT"),
                            It.Is<Uri>(requestUri => requestUri == expectedRequestUri),
                            It.Is<string>(rpNamespace => rpNamespace == Constants.Common.ResourceProviderNamespace),
                            It.Is<string>(tenantId => tenantId == Constants.Common.HomeTenantId),
                            It.Is<string>(wsId => wsId == Constants.Common.FullyQualifiedWorkspaceId),
                            It.Is<JToken>(
                                requestBody => requestBody != null),
                            It.IsAny<string>(),
                            It.IsAny<bool>(),
                            It.IsAny<KeyValuePair<string, string>[]>()));
            }
        }

        [TestMethod("Storage encryption call made with correct parameters")]
        public void TestUpdateStorageEncryptionWithByok()
        {
            using (new AppConfigSettingScope(this.ConfigurationSettings))
            {
                var _ = this.DatabricksController.PutApplication(
                Constants.Common.SubscriptionId,
                Constants.Common.ResourceGroupName,
                Constants.Common.ApplicationName,
                this.GetRequestObject<WorkspaceV1>()
                ).Result;

                this.FrontdoorEngineMock.Verify(
                    o => o.UpdateStorageEncryptionWithByok(
                            It.Is<string>(subscriptionId => subscriptionId == Constants.Common.SubscriptionId),
                            It.Is<string>(mrg => mrg == Constants.Common.ManagedResourceGroupName),
                            It.Is<string>(storageAccount => storageAccount == Constants.Common.StorageAccountName),
                            It.Is<string>(tenantId => tenantId == Constants.Common.PublisherTenantId),
                            It.Is<string>(rpNamespace => rpNamespace == Constants.Common.ResourceProviderNamespace),
                            It.Is<StorageDefinition>(
                                storageDefinition => storageDefinition.Properties["encryption"]["keySource"].Value<string>() == "Microsoft.Keyvault" &&
                                    storageDefinition.Properties["encryption"]["keyvaultproperties"]["keyvaulturi"].Value<string>() == "https://somekeyvault.vault.azure.net" &&
                                    storageDefinition.Properties["encryption"]["keyvaultproperties"]["keyname"].Value<string>() == "somekeyname"
                            )
                        )
                    );
            }
        }

    }
}
