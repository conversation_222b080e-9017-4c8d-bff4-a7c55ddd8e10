﻿//------------------------------------------------------------
// Copyright (c) Microsoft Corporation.  All rights reserved.
//------------------------------------------------------------

namespace UnitTests.Providers.Feature.ControllerTests.ControllerV1Tests.PutApplicationTests.UpdateTests.CMKTests
{
    using System;
    using System.Collections.Generic;
    using System.Linq;
    using System.Net;
    using System.Net.Http;
    using System.Text;
    using System.Web.Http;
    using System.Web.Http.Hosting;
    using Microsoft.AspNetCore.Mvc;
    using Microsoft.VisualStudio.TestTools.UnitTesting;
    using Microsoft.WindowsAzure.ResourceStack.Common.Collections;
    using Microsoft.WindowsAzure.ResourceStack.Common.Instrumentation;
    using Microsoft.WindowsAzure.ResourceStack.Common.Json;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Application.Data.Definitions;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Application.Data.Entities;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Common.Definitions;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Common.Engines;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Feature.Controllers;
    using Moq;
    using Newtonsoft.Json.Linq;

    /// <summary>
    /// Configuring encryption for storage is a multi step process.
    /// Step 1:
    ///     Storage accounts need to be "prepared", i.e. they need to be updated to be assigned a system assigned identity.
    ///     This is done by passing a prepareEncryption parameter, whose value is set to true.
    /// 
    /// Step 2:
    ///     "Preparing" a storage account assigns it a system managed identity. This details of 
    ///     this identity needs to then be retrieved, so as to be given permissions to a key vault
    ///     containing the key to be used for encryption.
    ///     
    /// Step 3:
    ///     The identity needs to be given read access to a key vault, so that storage can actually
    ///     fetch the key details from the key vault to encrypt the file system.
    ///     
    /// Step 4:
    ///     Once the read access is given, we need to then provide the details of the keyvault to
    ///     the storage account, so that it actually knows where to fetch the keys from.
    /// 
    /// THis test case captures that behaviour during a prepare request, i.e. step 1, as a part of an update
    /// done on a workspace.
    [TestClass]
    public class PrepareEncryptionTest : UpdateBaseControllerV1Test
    {
        protected override JToken Sku
        {
            get
            {
                return JToken.Parse(@"{ ""name"": ""Premium""}");
            }
        }

        private InsensitiveDictionary<JToken> workspaceParameters;

        protected override InsensitiveDictionary<JToken> WorkspaceParameters
        {
            get
            {
                if (workspaceParameters == null)
                {
                    workspaceParameters = base.WorkspaceParameters;
                    workspaceParameters.Add("prepareEncryption", JToken.Parse(@"{ ""type"": ""Bool"", ""value"": true }"));
                }

                return workspaceParameters;
            }
        }

        protected override JToken ExistingEntitySku
        {
            get
            {
                return JToken.Parse(@"{ ""name"": ""Premium""}");
            }
        }


        private Mock<IFrontdoorEngine> frontdoorEngineMock;

        protected override Mock<IFrontdoorEngine> FrontdoorEngineMock
        {
            get
            {
                frontdoorEngineMock = base.FrontdoorEngineMock;

                var storageDefinition = new StorageDefinition();
                storageDefinition.Identity = new JObject
                {
                    new JProperty("principalid", Constants.StorageEncryption.SystemAssignedPrincipalId),
                    new JProperty("tenantid", Constants.StorageEncryption.SystemAssignedTenantId)
                };


                frontdoorEngineMock.Setup(o => o.CallFrontdoor(
                    It.IsAny<HttpMethod>(),
                    It.IsAny<Uri>(),
                    It.IsAny<string>(),
                    It.IsAny<string>(),
                    It.IsAny<StorageDefinition>(),
                    It.IsAny<string>(),
                    It.IsAny<KeyValuePair<string, string>[]>()
                    )
                ).ReturnsAsync(storageDefinition);

                return frontdoorEngineMock;
            }
        }


        [TestMethod("Request with prepare encryption set to true returns a status code OK")]
        public void TestReturnsOKStatusCode()
        {
            using (new AppConfigSettingScope(this.ConfigurationSettings))
            {
                var response = this.DatabricksController.PutApplication(
                Constants.Common.SubscriptionId,
                Constants.Common.ResourceGroupName,
                Constants.Common.ApplicationName,
                this.GetRequestObject<WorkspaceV1>()
                ).Result;

                Assert.AreEqual((response as ObjectResult).StatusCode, (int) HttpStatusCode.OK);
            }
        }

        [TestMethod("Frontdoor call is made to update the storage")]
        public void TestVerifyFrontdoorCallForStorageUpdate()
        {
            using (new AppConfigSettingScope(this.ConfigurationSettings))
            {
                var expectedStorageUpdateUri = new Uri(
                String.Format(
                    "{0}/subscriptions/{1}/resourceGroups/{2}/providers/Microsoft.Storage/storageAccounts/{3}?api-version={4}",
                    Constants.Common.FrontDoorEngineUri,
                    Constants.Common.SubscriptionId,
                    Constants.Common.ManagedResourceGroupName,
                    "dummystorageaccountname",
                    Constants.StorageEncryption.StorageApiVersion));

                var response = this.DatabricksController.PutApplication(
                    Constants.Common.SubscriptionId,
                    Constants.Common.ResourceGroupName,
                    Constants.Common.ApplicationName,
                    this.GetRequestObject<WorkspaceV1>()
                    ).Result;



                this.FrontdoorEngineMock.Verify(
                    o => o.CallFrontdoor(
                        It.Is<HttpMethod>(method => method.Method == "PATCH"),
                        It.Is<Uri>(uri => uri == expectedStorageUpdateUri),
                        It.Is<string>(rpNamespace => rpNamespace == Constants.Common.ResourceProviderNamespace),
                        It.Is<string>(tenantId => tenantId == Constants.Common.PublisherTenantId),
                        It.IsAny<StorageDefinition>(),
                        It.IsAny<string>(),
                        It.IsAny<KeyValuePair<string, string>[]>()));
            }
        }

        [TestMethod("Appliance entity saved after the update contains prepare encryption key")]
        public void TestReplacedEntityContainsPrepareEncryption()
        {
            using (new AppConfigSettingScope(this.ConfigurationSettings))
            {
                this.ReplaceAction = new Action<ApplianceEntity>((entity) =>
            {
                Assert.IsNotNull(entity);
                Assert.IsTrue(entity.Properties.Parameters.ContainsKey("prepareEncryption"));
                Assert.AreEqual("True", entity.Properties.Parameters["prepareEncryption"]["value"].ToString());
            });


                var _ = this.DatabricksController.PutApplication(
                    Constants.Common.SubscriptionId,
                    Constants.Common.ResourceGroupName,
                    Constants.Common.ApplicationName,
                    this.GetRequestObject<WorkspaceV1>()
                    ).Result;
            }
        }

        [TestMethod("Appliance entity saved after update contains identity details")]
        public void TestApplianceEntityContainsIdentityDetails()
        {
            using (new AppConfigSettingScope(this.ConfigurationSettings))
            {
                this.ReplaceAction = new Action<ApplianceEntity>((entity) =>
            {
                Assert.IsNotNull(entity);

                Assert.AreEqual(Constants.StorageEncryption.SystemAssignedPrincipalId,
                    entity.Properties.StorageAccountIdentityEntity.PrincipalId);

                Assert.AreEqual(Constants.StorageEncryption.SystemAssignedTenantId,
                    entity.Properties.StorageAccountIdentityEntity.TenantId);

                Assert.AreEqual(StorageAccountIdentityType.SystemAssigned, entity.Properties.StorageAccountIdentityEntity.Type);
            });

                var _ = this.DatabricksController.PutApplication(
                    Constants.Common.SubscriptionId,
                    Constants.Common.ResourceGroupName,
                    Constants.Common.ApplicationName,
                    this.GetRequestObject<WorkspaceV1>()
                    ).Result;
            }
        }


        [TestMethod("DB notification called for storage encryption with appropriate parameters")]
        public void TestDBNotificationForStorageEncryption()
        {
            using (new AppConfigSettingScope(this.ConfigurationSettings))
            {
                var response = this.DatabricksController.PutApplication(
                Constants.Common.SubscriptionId,
                Constants.Common.ResourceGroupName,
                Constants.Common.ApplicationName,
                this.GetRequestObject<WorkspaceV1>()
                ).Result;

                var expectedRequestUri = new Uri(String.Format(
                        "{0}/api/2.0/tenants/{1}/subscriptions/{2}/resourceGroups/{3}/providers/{4}/workspaces/{5}",
                        Constants.Common.FrontDoorEngineUri,
                        Constants.Common.HomeTenantId,
                        Constants.Common.SubscriptionId,
                        Constants.Common.ResourceGroupName,
                        Constants.Common.ResourceProviderNamespace,
                        Constants.Common.ApplicationName));

                this.FrontdoorEngineMock.Verify(
                    o => o.DBWorkspaceNotificationWrapper(
                            It.Is<HttpMethod>(httpMethod => httpMethod.Method == "PUT"),
                            It.Is<Uri>(requestUri => requestUri == expectedRequestUri),
                            It.Is<string>(rpNamespace => rpNamespace == Constants.Common.ResourceProviderNamespace),
                            It.Is<string>(tenantId => tenantId == Constants.Common.HomeTenantId),
                            It.Is<string>(wsId => wsId == Constants.Common.FullyQualifiedWorkspaceId),
                            It.Is<JToken>(requestBody => requestBody != null),
                            It.IsAny<string>(),
                            It.IsAny<bool>(),
                            It.IsAny<KeyValuePair<string, string>[]>()
                        ));
            }
        }

    }
}
