﻿//------------------------------------------------------------
// Copyright (c) Microsoft Corporation.  All rights reserved.
//------------------------------------------------------------

namespace UnitTests.Providers.Feature.ControllerTests.ControllerV1Tests.PutApplicationTests.CreateTests.CMKTests
{
    using System.Net;
    using System.Threading.Tasks;
    using Microsoft.VisualStudio.TestTools.UnitTesting;
    using Microsoft.WindowsAzure.ResourceStack.Common.Collections;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Common.ErrorResponses;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Feature.Controllers;
    using Newtonsoft.Json.Linq;

    /// <summary>
    /// The "requireInfrastructureEncryption" feature is available only for premium workspaces.
    /// In this test case, we verify that an error is thrown when it is tried on a trial workspace.
    /// </summary>
    [TestClass]
    public class NoInfrastructureEncryptionForStandardTest : BaseControllerV1Test
    {
        protected override JToken Sku
        {
            get
            {
                return JToken.Parse(@"{ ""name"": ""Standard""}");
            }
        }

        private InsensitiveDictionary<JToken> workspaceParameters;

        protected override InsensitiveDictionary<JToken> WorkspaceParameters
        {
            get
            {
                if (workspaceParameters == null)
                {
                    workspaceParameters = base.WorkspaceParameters;
                    workspaceParameters.Add("requireInfrastructureEncryption", JToken.Parse(@"{ ""type"": ""Bool"", ""value"": true }"));
                }

                return workspaceParameters;
            }
        }

        [TestMethod("For standard workspaces, enabling infrastructure encryption throws an exception with invalid SKU status code")]
        public async Task TestThrowsErrorWithInvalidSkuStatusCode()
        {
            using (new AppConfigSettingScope(this.ConfigurationSettings))
            {
                var ex = await Assert.ThrowsExceptionAsync<ErrorResponseMessageException>(
                async () => await this.DatabricksController.PutApplication(
                     Constants.Common.SubscriptionId,
                     Constants.Common.ResourceGroupName,
                     Constants.Common.ApplicationName,
                     this.GetRequestObject<WorkspaceV1>()
                ));
                Assert.AreEqual(ErrorResponseCode.InvalidSkuForDbfsEncryption, ex.ErrorCode);
                Assert.AreEqual(HttpStatusCode.BadRequest, ex.HttpStatus);
            }
        }
    }
}