﻿//------------------------------------------------------------
// Copyright (c) Microsoft Corporation.  All rights reserved.
//------------------------------------------------------------

namespace UnitTests.Providers.Feature.FeatureTests.ServerlessWorkspaceTests
{
    using Microsoft.VisualStudio.TestTools.UnitTesting;
    using Microsoft.WindowsAzure.ResourceStack.Common.Instrumentation;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Common.Definitions;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Common.ErrorResponses;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Feature.Controllers.ControllerV11;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Feature.Features.ServerlessWorkspaceFeature;

    [TestClass]
    public class ValidateComputeModeTest
    {
        [TestMethod]
        [DataRow(ComputeMode.Serverless, null)]
        [DataRow(ComputeMode.Hybrid, "testMRG")]
        public void ValidateComputeModeTest_ShouldSuccess(ComputeMode inputComputeMode, string MRG)
        {
            WorkspacePropertiesV11 workspacePropertiesV11 = new WorkspacePropertiesV11()
            {
                ComputeMode = inputComputeMode,
                ManagedResourceGroupId = MRG
            };
            workspacePropertiesV11.ValidateMrgForComputeMode();
        }

        [TestMethod]
        public void ValidateComputeModeTest_HybridWithoutMRG_ShouldFail()
        {
            WorkspacePropertiesV11 workspacePropertiesV11 = new WorkspacePropertiesV11()
            {
                ComputeMode = ComputeMode.Hybrid,
                ManagedResourceGroupId = ""
            };
            var ex = Assert.ThrowsException<ErrorResponseMessageException>(() =>
            {
                workspacePropertiesV11.ValidateMrgForComputeMode();
            });

            Assert.AreEqual(ErrorResponseCode.InvalidApplicationPropertiesForPut, ex.ErrorCode);
            Assert.AreEqual(ErrorResponseMessages.ApplicationPropertiesNull.ToLocalizedMessage("ManagedResourceGroupId"), ex.Message);
        }

        [TestMethod]
        [DataRow(null)]
        public void ValidateComputeModeTest_ComputeModeNotGiven_ShouldFail(ComputeMode inputComputeMode)
        {
            WorkspacePropertiesV11 workspacePropertiesV11 = new WorkspacePropertiesV11()
            {
                ComputeMode = inputComputeMode
            };

            var ex = Assert.ThrowsException<ErrorResponseMessageException>(() =>
            {
                workspacePropertiesV11.ValidateMrgForComputeMode();
            });

            Assert.AreEqual(ErrorResponseCode.InvalidApplicationPropertiesForPut, ex.ErrorCode);
            Assert.AreEqual(ErrorResponseMessages.ApplicationPropertiesNull.ToLocalizedMessage("ManagedResourceGroupId"), ex.Message);
        }

        [TestMethod]
        public void ValidateComputeModeTest_ManagedResourceGroupIdGivenWithServerless_ShouldFail()
        {
            WorkspacePropertiesV11 workspacePropertiesV11 = new WorkspacePropertiesV11()
            {
                ComputeMode = ComputeMode.Serverless,
                ManagedResourceGroupId = "ManagedResourceGroupId"
            };

            var ex = Assert.ThrowsException<ErrorResponseMessageException>(() =>
            {
                workspacePropertiesV11.ValidateMrgForComputeMode();
            });

            Assert.AreEqual(ErrorResponseCode.FeatureNotSupportedForServerlessWorkspace, ex.ErrorCode);
            Assert.AreEqual(ErrorResponseMessages.FeatureNotSupportedForServerlessWorkspace.ToLocalizedMessage("ManagedResourceGroupId"), ex.Message);
        }
    }
}