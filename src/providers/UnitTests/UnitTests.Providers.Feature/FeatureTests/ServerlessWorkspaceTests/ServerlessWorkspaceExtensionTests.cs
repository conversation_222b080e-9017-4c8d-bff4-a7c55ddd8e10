﻿//------------------------------------------------------------
// Copyright (c) Microsoft Corporation.  All rights reserved.
//------------------------------------------------------------

namespace Microsoft.WindowsAzure.ResourceStack.Providers.Feature.Tests
{
    using System.Collections.Generic;
    using Microsoft.VisualStudio.TestTools.UnitTesting;
    using Microsoft.WindowsAzure.ResourceStack.Common.Collections;
    using Microsoft.WindowsAzure.ResourceStack.Common.Instrumentation;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Application.Data.Definitions;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Application.Data.Definitions.Enums;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Application.Data.Entities;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Common.Definitions;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Common.ErrorResponses;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Feature.Controllers.ControllerV11;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Feature.Features.EncryptionDefinitions;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Feature.Features.ManagedDiskEncryptionFeature;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Feature.Features.ServerlessWorkspaceFeature;
    using Moq;
    using Newtonsoft.Json.Linq;

    [TestClass]
    public class ServerlessWorkspaceExtensionsTests
    {
        private JToken premiumSku = JToken.Parse(@"{ ""name"": ""Premium""}");

        [TestMethod]
        [DataRow(ComputeMode.Serverless)]
        [DataRow(ComputeMode.Hybrid)]
        public void ValidateServerlessWorkspace_AllComputeMode_ShouldSucceed(ComputeMode computeMode)
        {
            var properties = new WorkspacePropertiesV11
            {
                ComputeMode = computeMode,
            };

            properties.ValidateServerlessNotSupportedFeatures(premiumSku);
        }

        [TestMethod]
        public void ValidateServerlessNotSupportedFeatures_StandardSku_ThrowsException()
        {
            var properties = new WorkspacePropertiesV11
            {
                ComputeMode = ComputeMode.Serverless
            };

            var ex = Assert.ThrowsException<ErrorResponseMessageException>(() => properties.ValidateServerlessNotSupportedFeatures(JToken.Parse(@"{ ""name"": ""Standard""}")));

            Assert.AreEqual(ErrorResponseCode.InvalidSkuForServerlessWorkspace, ex.ErrorCode);
            Assert.AreEqual(ErrorResponseMessages.InvalidSkuForServerlessWorkspace, ex.Message);
        }

        [TestMethod]
        public void ValidateServerlessWorkspace_ServerlessMode_AccessConnector_ThrowsException()
        {
            // Arrange
            var properties = new WorkspacePropertiesV11
            {
                ComputeMode = ComputeMode.Serverless,
                AccessConnector = new AccessConnectorId
                {
                    Id = "accessConnectorResourceId"
                }
            };

            // Act & Assert
            var ex = Assert.ThrowsException<ErrorResponseMessageException>(() => properties.ValidateServerlessNotSupportedFeatures(premiumSku));
            Assert.AreEqual(ErrorResponseCode.FeatureNotSupportedForServerlessWorkspace, ex.ErrorCode);
            Assert.AreEqual(ErrorResponseMessages.FeatureNotSupportedForServerlessWorkspace.ToLocalizedMessage($"accessConnector"), ex.Message);
        }

        [TestMethod]
        public void ValidateServerlessWorkspace_ServerlessMode_DefaultCatalog_ThrowsException()
        {
            // Arrange
            var properties = new WorkspacePropertiesV11
            {
                ComputeMode = ComputeMode.Serverless,
                DefaultCatalog = new DefaultCatalog
                {
                    InitialName = "defaultCatalogName"
                },
                IsUcEnabled = true
            };

            // Act & Assert
            var ex = Assert.ThrowsException<ErrorResponseMessageException>(() => properties.ValidateServerlessNotSupportedFeatures(premiumSku));
            Assert.AreEqual(ErrorResponseCode.FeatureNotSupportedForServerlessWorkspace, ex.ErrorCode);
            Assert.AreEqual(ErrorResponseMessages.FeatureNotSupportedForServerlessWorkspace.ToLocalizedMessage("defaultCatalog"), ex.Message);
        }

        [TestMethod]
        public void ValidateServerlessWorkspace_ServerlessMode_IsUcEnabled_ThrowsException()
        {
            // Arrange
            var properties = new WorkspacePropertiesV11
            {
                ComputeMode = ComputeMode.Serverless,
                IsUcEnabled = true
            };

            // Act & Assert
            var ex = Assert.ThrowsException<ErrorResponseMessageException>(() => properties.ValidateServerlessNotSupportedFeatures(premiumSku));
            Assert.AreEqual(ErrorResponseCode.FeatureNotSupportedForServerlessWorkspace, ex.ErrorCode);
            Assert.AreEqual(ErrorResponseMessages.FeatureNotSupportedForServerlessWorkspace.ToLocalizedMessage("defaultCatalog"), ex.Message);
        }

        [TestMethod]
        public void ValidateServerlessWorkspace_ServerlessMode_PrivateLink_ThrowsException()
        {
            // Arrange
            var properties = new WorkspacePropertiesV11
            {
                ComputeMode = ComputeMode.Serverless,
                PrivateLinkAssets = new List<string>()
            };

            // Act & Assert
            var ex = Assert.ThrowsException<ErrorResponseMessageException>(() => properties.ValidateServerlessNotSupportedFeatures(premiumSku));
            Assert.AreEqual(ErrorResponseCode.FeatureNotSupportedForServerlessWorkspace, ex.ErrorCode);
            Assert.AreEqual(ErrorResponseMessages.FeatureNotSupportedForServerlessWorkspace.ToLocalizedMessage("PrivateLinkAssets"), ex.Message);
        }

        [TestMethod]
        public void ValidateServerlessWorkspace_ServerlessMode_PrivateEndPointConnections_ThrowsException()
        {
            // Arrange
            var properties = new WorkspacePropertiesV11
            {
                ComputeMode = ComputeMode.Serverless,
                PrivateEndpointConnections = new List<PrivateEndpointConnection>()
            };

            // Act & Assert
            var ex = Assert.ThrowsException<ErrorResponseMessageException>(() => properties.ValidateServerlessNotSupportedFeatures(premiumSku));
            Assert.AreEqual(ErrorResponseCode.FeatureNotSupportedForServerlessWorkspace, ex.ErrorCode);
            Assert.AreEqual(ErrorResponseMessages.FeatureNotSupportedForServerlessWorkspace.ToLocalizedMessage("PrivateEndpointConnections"), ex.Message);
        }

        [TestMethod]
        public void ValidateServerlessWorkspace_ServerlessMode_PublicNetworkAccess_ThrowsException()
        {
            // Arrange
            var properties = new WorkspacePropertiesV11
            {
                ComputeMode = ComputeMode.Serverless,
                PublicNetworkAccess = PublicNetworkAccessStatus.Disabled
            };

            // Act & Assert
            var ex = Assert.ThrowsException<ErrorResponseMessageException>(() => properties.ValidateServerlessNotSupportedFeatures(premiumSku));
            Assert.AreEqual(ErrorResponseCode.FeatureNotSupportedForServerlessWorkspace, ex.ErrorCode);
            Assert.AreEqual(ErrorResponseMessages.FeatureNotSupportedForServerlessWorkspace.ToLocalizedMessage("PublicNetworkAccess"), ex.Message);
        }

        [TestMethod]
        public void ValidateServerlessWorkspace_ServerlessMode_RequiredNsgRules_ThrowsException()
        {
            // Arrange
            var properties = new WorkspacePropertiesV11
            {
                ComputeMode = ComputeMode.Serverless,
                RequiredNsgRules = RequiredNetworkSecurityGroupType.AllRules
            };

            // Act & Assert
            var ex = Assert.ThrowsException<ErrorResponseMessageException>(() => properties.ValidateServerlessNotSupportedFeatures(premiumSku));
            Assert.AreEqual(ErrorResponseCode.FeatureNotSupportedForServerlessWorkspace, ex.ErrorCode);
            Assert.AreEqual(ErrorResponseMessages.FeatureNotSupportedForServerlessWorkspace.ToLocalizedMessage("RequiredNsgRules"), ex.Message);
        }

        [TestMethod]
        public void ValidateServerlessWorkspace_ServerlessMode_ManagedDiskEncryption_ThrowsException()
        {
            // Arrange
            var properties = new WorkspacePropertiesV11
            {
                ComputeMode = ComputeMode.Serverless,
                Encryption = new EncryptionProperties
                {
                    Entities = new EncryptionTargets
                    {
                        ManagedDisk = new ManagedDiskEncryption()
                        {
                            KeySource = ManagedEncryptionKeySource.KeyVault,
                            KeyVaultProperties = new ManagedDiskKeyVault() { },
                            RotationToLatestKeyVersionEnabled = true
                        }
                    }
                }
            };

            // Act & Assert
            var ex = Assert.ThrowsException<ErrorResponseMessageException>(() => properties.ValidateServerlessNotSupportedFeatures(premiumSku));
            Assert.AreEqual(ErrorResponseCode.FeatureNotSupportedForServerlessWorkspace, ex.ErrorCode);
            Assert.AreEqual(ErrorResponseMessages.FeatureNotSupportedForServerlessWorkspace.ToLocalizedMessage("ManagedDiskEncryption"), ex.Message);
        }

        [TestMethod]
        public void ValidateServerlessWorkspace_ServerlessMode_ManagedDiskIdentity_ThrowsException()
        {
            // Arrange
            var properties = new WorkspacePropertiesV11
            {
                ComputeMode = ComputeMode.Serverless,
                ManagedDiskIdentity = new ManagedDiskIdentity
                {
                    PrincipalId = "principalId",
                    TenantId = "tenantId",
                    Type = ManagedDiskIdentityType.SystemAssigned
                }
            };
            // Act & Assert
            var ex = Assert.ThrowsException<ErrorResponseMessageException>(() => properties.ValidateServerlessNotSupportedFeatures(premiumSku));
            Assert.AreEqual(ErrorResponseCode.FeatureNotSupportedForServerlessWorkspace, ex.ErrorCode);
            Assert.AreEqual(ErrorResponseMessages.FeatureNotSupportedForServerlessWorkspace.ToLocalizedMessage("ManagedDiskIdentity"), ex.Message);
        }

        [TestMethod]
        public void ValidateServerlessWorkspace_ServerlessMode_DiskEncryptionSetId_ThrowsException()
        {
            // Arrange
            var properties = new WorkspacePropertiesV11
            {
                ComputeMode = ComputeMode.Serverless,
                DiskEncryptionSetId = "diskEncryptionSetId"
            };
            // Act & Assert
            var ex = Assert.ThrowsException<ErrorResponseMessageException>(() => properties.ValidateServerlessNotSupportedFeatures(premiumSku));
            Assert.AreEqual(ErrorResponseCode.FeatureNotSupportedForServerlessWorkspace, ex.ErrorCode);
            Assert.AreEqual(ErrorResponseMessages.FeatureNotSupportedForServerlessWorkspace.ToLocalizedMessage("DiskEncryptionSetId"), ex.Message);
        }

        [TestMethod]
        public void ValidateServerlessWorkspace_ServerlessMode_DefaultStorageFirewall_ThrowsException()
        {
            // Arrange
            var properties = new WorkspacePropertiesV11
            {
                ComputeMode = ComputeMode.Serverless,
                DefaultStorageFirewall = DefaultStorageFirewall.Enabled
            };

            // Act & Assert
            var ex = Assert.ThrowsException<ErrorResponseMessageException>(() => properties.ValidateServerlessNotSupportedFeatures(premiumSku));
            Assert.AreEqual(ErrorResponseCode.FeatureNotSupportedForServerlessWorkspace, ex.ErrorCode);
            Assert.AreEqual(ErrorResponseMessages.FeatureNotSupportedForServerlessWorkspace.ToLocalizedMessage("defaultStorageFirewall"), ex.Message);
        }

        [TestMethod]
        public void ValidateServerlessWorkspace_ServerlessMode_StorageAccountIdentity_ThrowsException()
        {
            // Arrange
            var properties = new WorkspacePropertiesV11
            {
                ComputeMode = ComputeMode.Serverless,
                StorageAccountIdentity = new StorageAccountIdentity
                {
                    PrincipalId = "principalId",
                    TenantId = "tenantId",
                    Type = StorageAccountIdentityType.SystemAssigned
                }
            };
            // Act & Assert
            var ex = Assert.ThrowsException<ErrorResponseMessageException>(() => properties.ValidateServerlessNotSupportedFeatures(premiumSku));
            Assert.AreEqual(ErrorResponseCode.FeatureNotSupportedForServerlessWorkspace, ex.ErrorCode);
            Assert.AreEqual(ErrorResponseMessages.FeatureNotSupportedForServerlessWorkspace.ToLocalizedMessage("StorageAccountIdentity"), ex.Message);
        }

        [TestMethod]
        [DataRow("customVirtualNetworkId")]
        [DataRow("loadBalancerId")]
        [DataRow("publicIpName")]
        [DataRow("customPrivateSubnetName")]
        [DataRow("enableFedRampCertification")]
        [DataRow("prepareEncryption")]
        [DataRow("amlWorkspaceId")]
        public void ValidateServerlessWorkspace_ServerlessMode_UnsupportedParameters_ThrowsException(string parameterKey)
        {
            // Arrange
            var properties = new WorkspacePropertiesV11
            {
                ComputeMode = ComputeMode.Serverless,
                Parameters = new InsensitiveDictionary<JToken>()
            };
            properties.Parameters.Add(parameterKey, "value");

            // Act & Assert
            var ex = Assert.ThrowsException<ErrorResponseMessageException>(() => properties.ValidateServerlessNotSupportedFeatures(premiumSku));
            Assert.AreEqual(ErrorResponseCode.FeatureNotSupportedForServerlessWorkspace, ex.ErrorCode);
            Assert.AreEqual(ErrorResponseMessages.FeatureNotSupportedForServerlessWorkspace.ToLocalizedMessage("Parameters"), ex.Message);
        }


        [TestMethod]
        public void ValidateManagedDiskIdentity_NullProperties_ShouldSucceed()
        {
            // Arrange
            IManagedDiskEncryptionProperties managedDiskEncryptionProperties = null;

            // Act & Assert
            // No exception should be thrown
            ServerlessWorkspaceExtensions.ValidateManagedDiskIdentity(managedDiskEncryptionProperties);
        }

        [TestMethod]
        public void ValidateManagedDiskIdentity_DiskEncryptionSetId_ThrowsException()
        {
            // Arrange
            var managedDiskEncryptionProperties = new Mock<IManagedDiskEncryptionProperties>();
            managedDiskEncryptionProperties.Setup(p => p.DiskEncryptionSetId).Returns("diskEncryptionSetId");

            // Act & Assert
            var ex = Assert.ThrowsException<ErrorResponseMessageException>(() =>
                ServerlessWorkspaceExtensions.ValidateManagedDiskIdentity(managedDiskEncryptionProperties.Object));

            Assert.AreEqual(ErrorResponseCode.FeatureNotSupportedForServerlessWorkspace, ex.ErrorCode);
            Assert.AreEqual(ErrorResponseMessages.FeatureNotSupportedForServerlessWorkspace.ToLocalizedMessage("DiskEncryptionSetId"), ex.Message);
        }

        [TestMethod]
        public void ValidateManagedDiskIdentity_ManagedDiskIdentity_ThrowsException()
        {
            // Arrange
            var managedDiskEncryptionProperties = new Mock<IManagedDiskEncryptionProperties>();
            managedDiskEncryptionProperties.Setup(p => p.ManagedDiskIdentity).Returns(new ManagedDiskIdentity
            {
                PrincipalId = "principalId",
                TenantId = "tenantId",
                Type = ManagedDiskIdentityType.SystemAssigned
            });

            // Act & Assert
            var ex = Assert.ThrowsException<ErrorResponseMessageException>(() =>
                ServerlessWorkspaceExtensions.ValidateManagedDiskIdentity(managedDiskEncryptionProperties.Object));

            Assert.AreEqual(ErrorResponseCode.FeatureNotSupportedForServerlessWorkspace, ex.ErrorCode);
            Assert.AreEqual(ErrorResponseMessages.FeatureNotSupportedForServerlessWorkspace.ToLocalizedMessage("ManagedDiskIdentity"), ex.Message);
        }

    }
}