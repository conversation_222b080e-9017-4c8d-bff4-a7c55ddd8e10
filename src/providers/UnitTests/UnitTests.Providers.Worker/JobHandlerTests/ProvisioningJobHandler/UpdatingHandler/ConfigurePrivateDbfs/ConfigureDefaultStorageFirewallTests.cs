﻿namespace UnitTests.Providers.Worker.JobHandlerTests.ProvisioningJobHandler.UpdatingHandler.ConfigureEncryptionOnStorage
{
    using System;
    using System.Net;
    using System.Net.Http;
    using Microsoft.VisualStudio.TestTools.UnitTesting;
    using Microsoft.WindowsAzure.ResourceStack.Common.BackgroundJobs;
    using Microsoft.WindowsAzure.ResourceStack.Common.Collections;
    using Microsoft.WindowsAzure.ResourceStack.Common.Json;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Application.Data.Definitions;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Application.Data.Definitions.Enums;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Application.Data.Entities;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Common.Constants;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Common.Data;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Common.Definitions;
    using Moq;
    using Newtonsoft.Json.Linq;

    [TestClass]
    public class ConfigureDefaultStorageFirewallTests : BaseWorkspaceInitializingTest
    {
        private InsensitiveDictionary<JToken> incomingWorkspaceParameters;
        protected override InsensitiveDictionary<JToken> IncomingWorkspaceParameters
        {
            get
            {
                if (incomingWorkspaceParameters == null)
                {
                    incomingWorkspaceParameters = new InsensitiveDictionary<JToken>
                    {
                        { ProviderConstants.Databricks.StorageAccountNameParameter, JToken.Parse(@"{ ""type"": ""String"", ""value"": """ + Constants.StorageAccountName + @"""}")},
                        { ProviderConstants.Databricks.CustomVirtualNetworkIdProperty, JToken.Parse(@"{ ""type"": ""String"", ""value"": """ + Constants.VNetId + @"""}") },
                        { ProviderConstants.Databricks.CustomPublicSubnetNameProperty, JToken.Parse(@"{ ""type"": ""String"", ""value"": """ + Constants.CustomPublicSubnetName + @"""}") },
                        { ProviderConstants.Databricks.CustomPrivateSubnetNameProperty, JToken.Parse(@"{ ""type"": ""String"", ""value"": """ + Constants.CustomPrivateSubnetName + @"""}") },
                        { "vnetAddressPrefix", JToken.Parse(@"{ ""type"": ""String"", ""value"": ""10.139""}") },
                        { "resourceTags", JToken.Parse(@"{ ""type"": ""Object"", ""value"":  { ""application"": ""databricks"", ""databricks-environment"": ""true"" }}") }
                    };
                }

                return incomingWorkspaceParameters;
            }
        }

        [TestInitialize]
        public void InitializeDefaultStorageFirewallEnableTest()
        {
            this.Metadata.ResourceOperation = ProvisioningOperation.Updating;
            this.IncomingWorkspaceProperties.Parameters = this.IncomingWorkspaceParameters;
            this.IncomingApplianceEntity.Properties.AccessConnector = new AccessConnectorIdEntity
            {
                Id = Constants.AccessConnectorId,
                PrincipalId = Constants.SystemAssignedIdentity.PrincipalId,
                TenantId = Constants.SystemAssignedIdentity.TenantId
            };

            this.IncomingApplianceEntity.Properties.DefaultStorageFirewall = DefaultStorageFirewall.Enabled;

            this.Metadata.Parameters = new InsensitiveDictionary<JToken>
            {
                { ProviderConstants.WorkspaceUpdateParameters.Properties, this.IncomingWorkspaceProperties?.ToJToken() },
                { ProviderConstants.WorkspaceUpdateParameters.Sku, this.IncomingEntitySku?.ToJToken() },
                { ProviderConstants.WorkspaceUpdateParameters.Tags, this.IncomingApplianceEntity?.Tags?.ToJToken() }
            };

            this.ApplianceEntity.Properties.Parameters = incomingWorkspaceParameters;

            var response = new HttpResponseMessage(HttpStatusCode.OK)
            {
                Content = new StringContent(@"{ ""storageAccountName"": """ + Constants.StorageAccountName + @"""}")
            };

            this.FrontdoorEngine.Setup(
                        o => o.PatchStorageResource(
                            It.IsAny<Uri>(),
                            ProviderConstants.Databricks.ResourceProviderNamespace,
                            Constants.PublisherTenantId,
                            It.IsAny<StorageDefinition>()
                            )).ReturnsAsync(response);
        }

        [TestMethod("Configure DefaultStorageFirewall Access on workspace")]
        public void TestApplianceProvisioningJobStatus()
        {
            this.FrontdoorEngine.Setup(
                o => o.GetDatabricksAccountApiEnpointAndAudience(
                    It.IsAny<string>(),
                    It.IsAny<string>())).ReturnsAsync
                    ((new Uri("http://anyurl.com"), "", true));

            using (new AppConfigSettingScope(this.ConfigurationSettings))
            {
                var result = this.ProvisioningHandler.OnJobExecute().Result;
                Assert.AreEqual(JobExecutionStatus.Succeeded, result.Status);
            }

            this.ApplianceEngine.Verify(
                o => o.ConfigureDefaultStorageFirewall(
                    It.IsAny<ApplianceEntity>(),
                    It.IsAny<AccessConnectorIdEntity>(),
                    It.IsAny<DefaultStorageFirewall?>(),
                    It.IsAny<string>(),
                    It.IsAny<string>()));
        }

        [TestMethod("Configure DefaultStorageFirewall Access on workspace with AccountApi")]
        public void TestApplianceProvisioningJobStatusWithAccountApi()
        {
            this.FrontdoorEngine.Setup(
                o => o.GetDatabricksAccountApiEnpointAndAudience(
                    It.IsAny<string>(),
                    It.IsAny<string>())).ReturnsAsync
                    ((new Uri("http://anyurl.com"), "", false));

            using (new AppConfigSettingScope(this.ConfigurationSettings))
            {
                var result = this.ProvisioningHandler.OnJobExecute().Result;
                Assert.AreEqual(JobExecutionStatus.Postponed, result.Status);
            }

            this.ApplianceEngine.Verify(
                o => o.ConfigureDefaultStorageFirewall(
                    It.IsAny<ApplianceEntity>(),
                    It.IsAny<AccessConnectorIdEntity>(),
                    It.IsAny<DefaultStorageFirewall?>(),
                    It.IsAny<string>(),
                    It.IsAny<string>()));
        }
    }
}
