﻿//------------------------------------------------------------
// Copyright (c) Microsoft Corporation.  All rights reserved.
//------------------------------------------------------------


namespace UnitTests.Providers.Worker.JobHandlerTests.ProvisioningJobHandler.UpdatingHandler.ManagedDiskEncryptionUpdateTests
{
    using System;
    using Microsoft.VisualStudio.TestTools.UnitTesting;
    using Microsoft.WindowsAzure.ResourceStack.Common.BackgroundJobs;
    using Microsoft.WindowsAzure.ResourceStack.Common.Collections;
    using Microsoft.WindowsAzure.ResourceStack.Common.Json;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Application.Data.Definitions;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Application.Data.Definitions.Enums;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Common.Constants;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Common.Data;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Common.Definitions;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Common.Responses;
    using Moq;
    using Newtonsoft.Json.Linq;

    [TestClass]
    public class DisksArePresentInsideMrgTest : BaseWorkspaceUpdateTest
    {
        [TestInitialize]
        public void InitializeDisksArePresentInsideMrgTest()
        {
            this.Metadata.ResourceOperation = ProvisioningOperation.Updating;

            var encryptionProperties = new EncryptionPropertiesEntity()
            {
                EncryptionEntities = new EncryptionTargetsEntity()
                {
                    ManagedDisk = new ManagedDiskEncryptionEntity()
                    {
                        KeySource = ManagedEncryptionKeySource.KeyVault,
                        KeyVaultProperties = new ManagedEncryptionKeyVaultEntity()
                        {
                            KeyName = Constants.ManagedDiskEncryption.KeyName,
                            KeyVaultUri = Constants.ManagedDiskEncryption.KeyVaultUri,
                            KeyVersion = Constants.ManagedDiskEncryption.KeyVersion
                        }
                    }
                }
            };

            this.IncomingWorkspaceProperties.EncryptionProperties = encryptionProperties;

            this.Metadata.Parameters = new InsensitiveDictionary<JToken>
            {
                { ProviderConstants.WorkspaceUpdateParameters.Properties, this.IncomingWorkspaceProperties?.ToJToken() },
                { ProviderConstants.WorkspaceUpdateParameters.Sku, this.IncomingEntitySku?.ToJToken() },
                { ProviderConstants.WorkspaceUpdateParameters.Tags, this.IncomingApplianceEntity?.Tags?.ToJToken() }
            };

            var disksExistInResourceGroupResponse = new DisksExistInResourceGroupResponse()
            {
                Status = OperationStatus.Success,
                DisksExistsInResourceGroup = true
            };

            var response = new System.Net.Http.HttpResponseMessage()
            {
                StatusCode = System.Net.HttpStatusCode.OK
            };

            response.Headers.Location = new Uri("https://localhost");

            var diskEncryptionSetOperationResponse = new DiskEncryptionSetOperationResponse()
            {
                DiskEncryptionSetDefinition = new DiskEncryptionSetDefinition()
                {
                    Identity = new JObject()
                },
                Status = OperationStatus.Success
            };

            this.ApplianceEngine.Setup(
                o => o.GetDiskEncryptionSetOperationStatus(
                    DiskEncryptionSetOperation.CreateDiskEncryptionSet,
                    It.IsAny<string>(),
                    It.IsAny<string>(),
                    Constants.ResourceProviderNamespace,
                    It.IsAny<Uri>()
                )).ReturnsAsync(diskEncryptionSetOperationResponse);

            this.FrontdoorEngine.Setup(
                        o => o.CheckIfDisksExistInResourceGroup(
                             Constants.PublisherTenantId,
                             ProviderConstants.Databricks.ResourceProviderNamespace,
                             this.Metadata.SubscriptionId,
                             Constants.ManagedResourceGroupName,
                             ProviderConstants.Compute.DiskEncryptionSetApiVersion)).
                             ReturnsAsync(disksExistInResourceGroupResponse);

            this.FrontdoorEngine.Setup(
                    o => o.CreateManagedDiskEncryptionSetWithCmk(
                        It.IsAny<string>(),
                        It.IsAny<string>(),
                        It.IsAny<string>(),
                        It.IsAny<string>(),
                        It.IsAny<DiskEncryptionSetDefinition>()))
                .ReturnsAsync(response);
        }

        [TestMethod("Job execution is set to faulted")]
        public void TestApplianceProvisioningState()
        {
            using (new AppConfigSettingScope(this.ConfigurationSettings))
            {
                this.FrontdoorEngine.Setup(
                o=>o.GetDatabricksAccountApiEnpointAndAudience(
                    It.IsAny<string>(),
                    It.IsAny<string>())).ReturnsAsync
                    ((new Uri("http://anyurl.com"),"",true));
                var jobExecutionResult = this.ProvisioningHandler.OnJobExecute().Result;
                Assert.AreEqual(JobExecutionStatus.Succeeded, jobExecutionResult.Status);
            }
        }
    }
}
