﻿//------------------------------------------------------------
// Copyright (c) Microsoft Corporation.  All rights reserved.
//------------------------------------------------------------

namespace UnitTests.Providers.Worker.JobHandlerTests.ProvisioningJobHandler.UpdatingHandler.MetadataUpdateTests
{
    using System;
    using System.Net;
    using System.Collections.Generic;
    using System.Net.Http;
    using System.Threading.Tasks;
    using Microsoft.VisualStudio.TestTools.UnitTesting;
    using Microsoft.WindowsAzure.ResourceStack.Common.BackgroundJobs;
    using Microsoft.WindowsAzure.ResourceStack.Common.Collections;
    using Microsoft.WindowsAzure.ResourceStack.Common.Json;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Common.Definitions;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Common.ErrorResponses;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Common.Constants;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Common.Data;
    using Moq;
    using Newtonsoft.Json.Linq;

    [TestClass]
    public class WorkspaceTagUpdateTests : BaseWorkspaceUpdateTest
    {
        [TestInitialize]
        public void InitializeWorkspaceTagUpdateTests()
        {
            this.Metadata.ResourceOperation = ProvisioningOperation.Updating;
            this.Metadata.Parameters = new InsensitiveDictionary<JToken>
            {
                { ProviderConstants.WorkspaceUpdateParameters.Properties, this.IncomingWorkspaceProperties?.ToJToken() },
                { ProviderConstants.WorkspaceUpdateParameters.Sku, this.IncomingEntitySku?.ToJToken() },
                { ProviderConstants.WorkspaceUpdateParameters.Tags, this.IncomingApplianceEntity?.Tags?.ToJToken() }
            };
        }

        [TestMethod("Notification sent to databricks with correct tags data")]
        public void TestDBCSNotificationContainsEncryptionParameters()
        {
            using (new AppConfigSettingScope(this.ConfigurationSettings))
            {
                var result = this.ProvisioningHandler.OnJobExecute().Result;

                var expectedRequestUri = new Uri(String.Format(
                        "{0}/api/2.0/tenants/{1}/subscriptions/{2}/resourceGroups/{3}/providers/{4}/workspaces/{5}",
                        Constants.FrontDoorEngineUri,
                        Constants.PublisherTenantId,
                        Constants.SubscriptionId,
                        Constants.ResourceGroupName,
                        Constants.ResourceProviderNamespace,
                        Constants.ApplicationName));

                this.FrontdoorEngine.Verify(
                o => o.DBWorkspaceNotificationWrapper(
                        It.Is<HttpMethod>(httpMethod => httpMethod.Method == "PUT"),
                        It.Is<Uri>(requestUri => requestUri == expectedRequestUri),
                        It.Is<string>(rpNamespace => rpNamespace == Constants.ResourceProviderNamespace),
                        It.Is<string>(tenantId => tenantId == Constants.PublisherTenantId),
                        It.Is<string>(wsId => wsId == this.ApplianceEntity.GetFullyQualifiedResourceId()),
                        It.Is<JToken>(requestBody => requestBody.ToString().Contains("testtag")),
                        It.IsAny<string>(),
                        It.IsAny<bool>(),
                        It.IsAny<KeyValuePair<string, string>[]>()));
            }
        }

        [TestMethod("Job execution is set to Succeeded")]
        public void TestApplianceProvisioningJobStatus()
        {
            this.FrontdoorEngine.Setup(
            o => o.GetDatabricksAccountApiEnpointAndAudience(
                It.IsAny<string>(),
                It.IsAny<string>())).ReturnsAsync
                ((new Uri("http://anyurl.com"), "", true));
            using (new AppConfigSettingScope(this.ConfigurationSettings))
            {
                var postExecutionResult = this.ProvisioningHandler.OnJobExecute().Result;

                Assert.AreEqual(JobExecutionStatus.Succeeded, postExecutionResult.Status);
            }
        }

        [TestMethod("Job execution is set to Postponed with AccountApi")]
        public void TestApplianceProvisioningJobStatusWithAccountApi()
        {
            this.FrontdoorEngine.Setup(
            o => o.GetDatabricksAccountApiEnpointAndAudience(
                It.IsAny<string>(),
                It.IsAny<string>())).ReturnsAsync
                ((new Uri("http://anyurl.com"), "", false));
            using (new AppConfigSettingScope(this.ConfigurationSettings))
            {
                var postExecutionResult = this.ProvisioningHandler.OnJobExecute().Result;

                Assert.AreEqual(JobExecutionStatus.Postponed, postExecutionResult.Status);
            }
        }

        [TestMethod("Job execution is set to Postponed")]
        public void TestJobProvisioningStateForUpdateResourceGroupFail_UnauthorizedResponseCode()
        {
            using (new AppConfigSettingScope(this.ConfigurationSettings))
            {
                this.IncomingApplianceEntity.Properties.ProvisioningState = ProvisioningState.Updating;
                this.FrontdoorEngine.Setup(
                    o => o.UpdateResourceGroup(
                        It.IsAny<String>(),
                        It.IsAny<String>(),
                        It.IsAny<String>(),
                        It.IsAny<ResourceGroupDefinition>(),
                        It.IsAny<String>())
                ).ThrowsAsync(new AuthorizationOperationException(
                    HttpStatusCode.Forbidden,
                    ErrorResponseCode.UnauthorizedRequest.ToString(),
                    "Unauthorized Access"));

                var postExecutionResult = this.ProvisioningHandler.OnJobExecute().Result;
                Assert.AreEqual(JobExecutionStatus.Postponed, postExecutionResult.Status);
                Assert.AreEqual(this.IncomingApplianceEntity.Properties.ProvisioningState, ProvisioningState.Updating);
            }
        }

        [TestMethod("Job execution is set to Postponed")]
        public void TestJobProvisioningStateForUpdateManagedResourceTagsFail_UnauthorizedResponseCode()
        {
            using (new AppConfigSettingScope(this.ConfigurationSettings))
            {
                this.IncomingApplianceEntity.Properties.ProvisioningState = ProvisioningState.Updating;
                this.FrontdoorEngine.Setup(
                    o => o.GetResourceGroupResources(
                        It.IsAny<String>(),
                        It.IsAny<String>(),
                        It.IsAny<String>(),
                        It.IsAny<String>(),
                        It.IsAny<String>(),
                        It.IsAny<String>())).ThrowsAsync(new AuthorizationOperationException(
                            HttpStatusCode.Forbidden,
                            ErrorResponseCode.UnauthorizedRequest.ToString(),
                            "Unauthorized Access"));

                this.FrontdoorEngine.Setup(
                    o => o.UpdateManagedResourceTags(
                        It.IsAny<String>(),
                        It.IsAny<String>(),
                        It.IsAny<String>(),
                        It.IsAny<ARMResourceDefinition>(),
                        It.IsAny<InsensitiveDictionary<string>>())
                    ).ThrowsAsync(new AuthorizationOperationException(
                        HttpStatusCode.Forbidden,
                        ErrorResponseCode.UnauthorizedRequest.ToString(),
                        "Unauthorized Access"));

                var postExecutionResult = this.ProvisioningHandler.OnJobExecute().Result;
                Assert.AreEqual(JobExecutionStatus.Postponed, postExecutionResult.Status);
                Assert.AreEqual(this.IncomingApplianceEntity.Properties.ProvisioningState, ProvisioningState.Updating);
            }
        }


        [TestMethod("Job execution is set to Postponed for throttling error")]
        public void TestJobProvisioningStateForThrottlingResponseCode()
        {
            using (new AppConfigSettingScope(this.ConfigurationSettings))
            {
                this.IncomingApplianceEntity.Properties.ProvisioningState = ProvisioningState.Updating;
                this.FrontdoorEngine.Setup(
                    o => o.UpdateResourceGroup(
                        It.IsAny<String>(),
                        It.IsAny<String>(),
                        It.IsAny<String>(),
                        It.IsAny<ResourceGroupDefinition>(),
                        It.IsAny<String>()))
                    .ThrowsAsync(new ServerErrorResponseMessageException(
                        (HttpStatusCode)429,
                        ErrorResponseCode.TooManyRequests.ToString(),
                        "Too many requests"));

                var postExecutionResult = this.ProvisioningHandler.OnJobExecute().Result;

                Assert.AreEqual(JobExecutionStatus.Postponed, postExecutionResult.Status);
                Assert.AreEqual(this.IncomingApplianceEntity.Properties.ProvisioningState, ProvisioningState.Updating);
            }
        }

        [TestMethod("Job execution is set to Postponed")]
        public void TestJobProvisioningStateForWorkspaceUpdate_UnauthorizedResponseCode()
        {
            using (new AppConfigSettingScope(this.ConfigurationSettings))
            {
                this.IncomingApplianceEntity.Properties.ProvisioningState = ProvisioningState.Updating;
                this.FrontdoorEngine.Setup(
                    o => o.UpdateResourceGroup(
                        It.IsAny<String>(),
                        It.IsAny<String>(),
                        It.IsAny<String>(),
                        It.IsAny<ResourceGroupDefinition>(),
                        It.IsAny<String>())
                ).ThrowsAsync(new Exception("Internal Server Error"));

                var updatedApplianceEntity = this.IncomingApplianceEntity;
                updatedApplianceEntity.Properties.ProvisioningState = ProvisioningState.Succeeded;

                this.ApplianceDataProvider.Setup(
                    o => o.SetApplianceProvisioningState(
                        It.IsAny<string>(),
                        It.IsAny<string>(),
                        It.IsAny<string>(),
                        ProvisioningState.Succeeded))
                    .Returns(Task.FromResult(updatedApplianceEntity));                    

                var postExecutionResult = this.ProvisioningHandler.OnJobExecute().Result;
                Assert.AreEqual(JobExecutionStatus.Faulted, postExecutionResult.Status);
                Assert.AreEqual(updatedApplianceEntity.Properties.ProvisioningState, ProvisioningState.Succeeded);
            }
        }
    }
}
