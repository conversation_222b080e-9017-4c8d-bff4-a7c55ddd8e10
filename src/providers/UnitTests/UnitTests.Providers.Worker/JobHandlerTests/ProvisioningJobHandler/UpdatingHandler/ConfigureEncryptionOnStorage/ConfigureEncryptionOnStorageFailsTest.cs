﻿//------------------------------------------------------------
// Copyright (c) Microsoft Corporation.  All rights reserved.
//------------------------------------------------------------

namespace UnitTests.Providers.Worker.JobHandlerTests.ProvisioningJobHandler.UpdatingHandler.ConfigureEncryptionOnStorage
{
    using System;
    using System.Collections.Generic;
    using System.Net;
    using System.Net.Http;
    using System.Threading.Tasks;
    using Microsoft.VisualStudio.TestTools.UnitTesting;
    using Microsoft.WindowsAzure.ResourceStack.Common.BackgroundJobs;
    using Microsoft.WindowsAzure.ResourceStack.Common.Collections;
    using Microsoft.WindowsAzure.ResourceStack.Common.Json;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Common.Constants;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Common.Data;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Common.Definitions;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Common.ErrorResponses;
    using Moq;
    using Newtonsoft.Json.Linq;

    [TestClass]
    public class ConfigureEncryptionOnStorageFailsTest : BaseWorkspaceUpdateTest
    {
        private InsensitiveDictionary<JToken> incomingWorkspaceParameters;
        protected override InsensitiveDictionary<JToken> IncomingWorkspaceParameters
        {
            get
            {
                if (incomingWorkspaceParameters == null)
                {
                    incomingWorkspaceParameters = new InsensitiveDictionary<JToken>();
                    incomingWorkspaceParameters.Add("enableFedRampCertification", JToken.Parse(@"{ ""type"": ""Bool"", ""value"": false}"));
                    incomingWorkspaceParameters.Add("enableNoPublicIp", JToken.Parse(@"{ ""type"": ""Bool"", ""value"": false}"));
                    incomingWorkspaceParameters.Add("prepareEncryption", JToken.Parse(@"{ ""type"": ""Bool"", ""value"": false}"));
                    incomingWorkspaceParameters.Add("requireInfrastructureEncryption", JToken.Parse(@"{ ""type"": ""Bool"", ""value"": false}"));
                    incomingWorkspaceParameters.Add(ProviderConstants.Databricks.StorageAccountNameParameter, JToken.Parse(@"{ ""type"": ""String"", ""value"": """ + Constants.StorageAccountName + @"""}"));
                    incomingWorkspaceParameters.Add("storageAccountSkuName", JToken.Parse(@"{ ""type"": ""String"", ""value"": ""Standard_GRS""}"));
                    incomingWorkspaceParameters.Add("vnetAddressPrefix", JToken.Parse(@"{ ""type"": ""String"", ""value"": ""10.139""}"));
                    incomingWorkspaceParameters.Add("resourceTags", JToken.Parse(@"{ ""type"": ""Object"", ""value"":  { ""application"": ""databricks"", ""databricks-environment"": ""true"" }}"));

                    var paramString = @"{
                    ""value"": {
                        ""keySource"": ""Microsoft.Keyvault"",
                        ""keyvaultUri"": ""https://canarytestkv.vault.azure.net"",
                        ""keyName"": ""encryption_key""
                    }
                }";
                    incomingWorkspaceParameters.Add("encryption", JObject.Parse(paramString));

                }

                return incomingWorkspaceParameters;
            }
        }

        [TestInitialize]
        public void InitializeConfigureEncryptionOnStorageFailsTest()
        {
            this.Metadata.ResourceOperation = ProvisioningOperation.Updating;
            this.IncomingWorkspaceProperties.Parameters = this.IncomingWorkspaceParameters;

            this.Metadata.Parameters = new InsensitiveDictionary<JToken>
            {
                { ProviderConstants.WorkspaceUpdateParameters.Properties, this.IncomingWorkspaceProperties?.ToJToken() },
                { ProviderConstants.WorkspaceUpdateParameters.Sku, this.IncomingEntitySku?.ToJToken() },
                { ProviderConstants.WorkspaceUpdateParameters.Tags, this.IncomingApplianceEntity?.Tags?.ToJToken() }
            };

            this.ApplianceEntity.Properties.Parameters = incomingWorkspaceParameters;

            var storageOperationexception = new StorageOperationException(
                                HttpStatusCode.InternalServerError,
                                ErrorResponseCode.StorageAccountOperationFailed.ToString(),
                                ErrorResponseMessages.StorageAccountUpdateFailed
                                );

            FrontdoorEngine.Setup(
                        o => o.UpdateStorageEncryptionWithByok(
                            Constants.SubscriptionId,
                            Constants.ManagedResourceGroupName,
                            Constants.StorageAccountName,
                            Constants.PublisherTenantId,
                            Constants.ResourceProviderNamespace,
                            It.IsAny<StorageDefinition>()
                            )).ThrowsAsync(storageOperationexception);
        }

        [TestMethod("Job execution is set to faulted")]
        public void TestApplianceProvisioningJobStatus()
        {
            using (new AppConfigSettingScope(this.ConfigurationSettings))
            {
                var result = this.ProvisioningHandler.OnJobExecute().Result;
                Assert.AreEqual(JobExecutionStatus.Faulted, result.Status);
            }
        }

        [TestMethod("SetApplianceProvisioningState is called to mark Provisioning state as succeeded")]
        public void TestApplianceProvisioningState()
        {
            var _ = this.ProvisioningHandler.OnJobExecute().Result;

            this.ApplianceDataProvider.Verify(o => o.SetApplianceProvisioningState(
                It.Is<string>(subId => subId == Constants.SubscriptionId),
                It.Is<string>(rgName => rgName == Constants.ResourceGroupName),
                It.Is<string>(name => name == Constants.ApplicationName),
                It.Is<ProvisioningState>(state => state == ProvisioningState.Succeeded)));
        }

        [TestMethod("SetApplianceProvisioningState is called to mark Provisioning state as succeeded")]
        public void TestJobProvisioningStateForWorkspaceUpdate_BYOKException()
        {
            using (new AppConfigSettingScope(this.ConfigurationSettings))
            {
                this.IncomingApplianceEntity.Properties.ProvisioningState = ProvisioningState.Updating;
                this.FrontdoorEngine.Setup(
                        o => o.CallFrontdoor(
                            new HttpMethod("PATCH"),
                            It.IsAny<Uri>(),
                            It.IsAny<String>(),
                            It.IsAny<string>(),
                            It.IsAny<StorageDefinition>(),
                            It.IsAny<string>(), It.IsAny<KeyValuePair<string, string>[]>())
                    )
                    .ReturnsAsync(() => null);

                var updatedApplianceEntity = this.IncomingApplianceEntity;
                updatedApplianceEntity.Properties.ProvisioningState = ProvisioningState.Succeeded;

                this.ApplianceDataProvider.Setup(
                        o => o.SetApplianceProvisioningState(
                            It.IsAny<string>(),
                            It.IsAny<string>(),
                            It.IsAny<string>(),
                            ProvisioningState.Succeeded))
                    .Returns(Task.FromResult(updatedApplianceEntity));

                var postExecutionResult = this.ProvisioningHandler.OnJobExecute()
                    .Result;
                Assert.AreEqual(JobExecutionStatus.Faulted,
                    postExecutionResult.Status);
                Assert.AreEqual(updatedApplianceEntity.Properties.ProvisioningState,
                    ProvisioningState.Succeeded);
            }
        }
    }
}
