﻿//------------------------------------------------------------
// Copyright (c) Microsoft Corporation.  All rights reserved.
//------------------------------------------------------------

namespace UnitTests.Providers.Worker.JobHandlerTests.ProvisioningJobHandler.UpdatingHandler.EnableManagedIdentityOnStorage
{
    using System;
    using System.Collections.Generic;
    using System.Net.Http;
    using System.Threading.Tasks;
    using Microsoft.VisualStudio.TestTools.UnitTesting;
    using Microsoft.WindowsAzure.ResourceStack.Common.BackgroundJobs;
    using Microsoft.WindowsAzure.ResourceStack.Common.Collections;
    using Microsoft.WindowsAzure.ResourceStack.Common.Json;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Common.Constants;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Common.Data;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Common.Definitions;
    using Moq;
    using Newtonsoft.Json.Linq;

    [TestClass]
    public class UpdateManagedIdentityForStorageAccountFailsTest : BaseWorkspaceUpdateTest
    {
        [TestInitialize]
        public void InitializeUpdateManagedIdentityForStorageAccountFailsTest()
        {
            this.Metadata.ResourceOperation = ProvisioningOperation.Updating;
            this.ApplianceEntity.Properties.Parameters = this.IncomingWorkspaceParameters;

            this.Metadata.Parameters = new InsensitiveDictionary<JToken>
            {
                { ProviderConstants.WorkspaceUpdateParameters.Properties, this.IncomingWorkspaceProperties?.ToJToken() },
                { ProviderConstants.WorkspaceUpdateParameters.Sku, this.IncomingEntitySku?.ToJToken() },
                { ProviderConstants.WorkspaceUpdateParameters.Tags, this.IncomingApplianceEntity?.Tags?.ToJToken() }
            };

            StorageDefinition storageUpdateRequest = new StorageDefinition()
            {
                Identity = new JObject()
                        {
                            new JProperty(ProviderConstants.Storage.ParameterType, ProviderConstants.Storage.SystemAssigned)
                        }
            };

            FrontdoorEngine.Setup(
                        o => o.CallFrontdoor(
                            HttpMethod.Put,
                            It.IsAny<Uri>(),
                            Constants.ResourceProviderNamespace,
                            Constants.HomeTenantId,
                            storageUpdateRequest,
                            It.IsAny<string>(),
                            It.IsAny<KeyValuePair<string, string>[]>()
                            )).Returns<StorageDefinition>(null);
        }

        [TestMethod("Job execution is set to faulted")]
        public void TestApplianceProvisioningJobStatus()
        {
            using (new AppConfigSettingScope(this.ConfigurationSettings))
            {
                var result = this.ProvisioningHandler.OnJobExecute().Result;
                Assert.AreEqual(JobExecutionStatus.Faulted, result.Status);
            }
        }

        [TestMethod("SetApplianceProvisioningState is called to mark Provisioning state as succeeded")]
        public void TestApplianceProvisioningState()
        {
            _ = this.ProvisioningHandler.OnJobExecute().Result;

            var result = this.ProvisioningHandler.OnJobExecute().Result;
            this.ApplianceDataProvider.Verify(o => o.SetApplianceProvisioningState(
                It.Is<string>(subId => subId == Constants.SubscriptionId),
                It.Is<string>(rgName => rgName == Constants.ResourceGroupName),
                It.Is<string>(name => name == Constants.ApplicationName),
                It.Is<ProvisioningState>(state => state == ProvisioningState.Succeeded)));
        }
    }
}
