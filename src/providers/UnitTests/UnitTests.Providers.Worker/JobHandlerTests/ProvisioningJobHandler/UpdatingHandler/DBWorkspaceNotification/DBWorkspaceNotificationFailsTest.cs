﻿//------------------------------------------------------------
// Copyright (c) Microsoft Corporation.  All rights reserved.
//------------------------------------------------------------

namespace UnitTests.Providers.Worker.JobHandlerTests.ProvisioningJobHandler.UpdatingHandler.DBWorkspaceNotification
{
    using System;
    using System.Collections.Generic;
    using System.Net;
    using System.Net.Http;
    using System.Threading.Tasks;
    using Microsoft.VisualStudio.TestTools.UnitTesting;
    using Microsoft.WindowsAzure.ResourceStack.Common.BackgroundJobs;
    using Microsoft.WindowsAzure.ResourceStack.Common.Collections;
    using Microsoft.WindowsAzure.ResourceStack.Common.Json;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Application.Data.Metadata;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Common.Constants;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Common.Data;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Common.Definitions;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Common.ErrorResponses;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Common.Metadata;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Common.Metrics;
    using Moq;
    using Newtonsoft.Json;
    using Newtonsoft.Json.Linq;

    [TestClass]
    public class DBWorkspaceNotificationFailsTest : BaseWorkspaceUpdateTest
    {
        [TestInitialize]
        public void InitializeDBWorkspaceNotificationFailsTest()
        {
            this.Metadata.ResourceOperation = ProvisioningOperation.Updating;
            this.Metadata.Parameters = new InsensitiveDictionary<JToken>
            {
                { ProviderConstants.WorkspaceUpdateParameters.Properties, this.IncomingWorkspaceProperties?.ToJToken() },
                { ProviderConstants.WorkspaceUpdateParameters.Sku, this.IncomingEntitySku?.ToJToken() },
                { ProviderConstants.WorkspaceUpdateParameters.Tags, this.IncomingApplianceEntity?.Tags?.ToJToken() }
            };

            FrontdoorEngine.Setup(o => o.DBWorkspaceNotificationWrapper(
                       It.IsAny<HttpMethod>(),
                       It.IsAny<Uri>(),
                       It.IsAny<string>(),
                       It.IsAny<string>(),
                       It.IsAny<string>(),
                       It.IsAny<JToken>(),
                       It.IsAny<string>(),
                       It.IsAny<bool>(),
                       It.IsAny<KeyValuePair<string, string>[]>()
                       )
                   ).Throws(new Exception());
        }

        [TestMethod("Job execution is set to Faulted")]
        public void TestApplianceProvisioningJobStatus()
        {
            using (new AppConfigSettingScope(this.ConfigurationSettings))
            {
                var result = this.ProvisioningHandler.OnJobExecute().Result;

                Assert.AreEqual(JobExecutionStatus.Faulted, result.Status);
            }
        }

        [TestMethod("SetApplianceProvisioningState is called to rollback the provisioning state")]
        public void TestApplianceProvisioningStatus()
        {
            using (new AppConfigSettingScope(this.ConfigurationSettings))
            {
                var result = this.ProvisioningHandler.OnJobExecute().Result;
                this.ApplianceDataProvider.Verify(o => o.SetApplianceProvisioningState(
                    It.Is<string>(subId => subId == Constants.SubscriptionId),
                    It.Is<string>(rgName => rgName == Constants.ResourceGroupName),
                    It.Is<string>(name => name == Constants.ApplicationName),
                    It.Is<ProvisioningState>(state => state == ProvisioningState.Failed)));
            }
        }

        [TestMethod("SetApplianceProvisioningState is called to mark Provisioning state as succeeded")]
        public void TestJobProvisioningStateForWorkspaceUpdate_DBPermissionException()
        {
            using (new AppConfigSettingScope(this.ConfigurationSettings))
            {
                this.IncomingApplianceEntity.Properties.ProvisioningState = ProvisioningState.Updating;
                FrontdoorEngine.Setup(o => o.DBWorkspaceNotificationWrapper(
                            It.IsAny<HttpMethod>(),
                            It.IsAny<Uri>(),
                            It.IsAny<string>(),
                            It.IsAny<string>(),
                            It.IsAny<string>(),
                            It.IsAny<JToken>(),
                            It.IsAny<string>(),
                            It.IsAny<bool>(),
                            It.IsAny<KeyValuePair<string, string>[]>()
                        )
                    )
                    .Throws(new ServerErrorResponseMessageException(
                        System.Net.HttpStatusCode.BadRequest,
                        ErrorResponseCode.PermissionDenied.ToString(),
                        " PERMISSION_DENIED: Invalid permissions on the specified KeyVault "));

                var updatedApplianceEntity = this.IncomingApplianceEntity;
                updatedApplianceEntity.Properties.ProvisioningState = ProvisioningState.Succeeded;

                this.ApplianceDataProvider.Setup(
                    o => o.SetApplianceProvisioningState(
                        It.IsAny<string>(),
                        It.IsAny<string>(),
                        It.IsAny<string>(),
                        ProvisioningState.Succeeded))
                    .Returns(Task.FromResult(updatedApplianceEntity));

                var postExecutionResult = this.ProvisioningHandler.OnJobExecute().Result;
                Assert.AreEqual(JobExecutionStatus.Faulted, postExecutionResult.Status);
                Assert.AreEqual(updatedApplianceEntity.Properties.ProvisioningState, ProvisioningState.Succeeded);
            }
        }

        [TestMethod("SLI Error is set correctly")]
        public void TestSLIErrorWhenDBWorkspaceCallIsFailedWithPermissionDeniedCode()
        {
            var expectedErrorCode = ErrorResponseCode.PermissionDenied.ToString();
                FrontdoorEngine.Setup(o => o.DBWorkspaceNotificationWrapper(
                            It.IsAny<HttpMethod>(),
                            It.IsAny<Uri>(),
                            It.IsAny<string>(),
                            It.IsAny<string>(),
                            It.IsAny<string>(),
                            It.IsAny<JToken>(),
                            It.IsAny<string>(),
                            It.IsAny<bool>(),
                            It.IsAny<KeyValuePair<string, string>[]>()
                        )
                    ).Throws(new ServerErrorResponseMessageException(HttpStatusCode.Forbidden, expectedErrorCode, string.Empty));

            // Setting MaxRetries to 0 to avoid retries
            if (ConfigurationSettings.ContainsKey("Microsoft.WindowsAzure.ResourceStack.Providers.ApplianceProvisioningJob.MaxRetries"))
                this.ConfigurationSettings["Microsoft.WindowsAzure.ResourceStack.Providers.ApplianceProvisioningJob.MaxRetries"] = "0";
            else
                this.ConfigurationSettings.Add("Microsoft.WindowsAzure.ResourceStack.Providers.ApplianceProvisioningJob.MaxRetries", "0");

            using (new AppConfigSettingScope(this.ConfigurationSettings))
            {
                var result = this.ProvisioningHandler.OnJobExecute().Result;
                var metadata = JsonConvert.DeserializeObject<AsyncOperationJobMetadata>(result.NextMetadata);
                var sliError = SLIErrorInfo.ConvertToSLIErrorInfo(metadata.SLIErrorInfo, metadata);
                Assert.AreEqual(expectedErrorCode, sliError.FailureCategory);
                Assert.AreEqual(ResponsibleParty.Client.ToString(), sliError.ResponsibleParty);
                Assert.AreEqual(JobExecutionStatus.Faulted, result.Status);
            }
        }
    }
}
