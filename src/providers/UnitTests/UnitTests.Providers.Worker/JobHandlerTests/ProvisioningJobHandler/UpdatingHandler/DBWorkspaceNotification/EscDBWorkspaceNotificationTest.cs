﻿using Microsoft.VisualStudio.TestTools.UnitTesting;
using Microsoft.WindowsAzure.ResourceStack.Common.BackgroundJobs;
using Microsoft.WindowsAzure.ResourceStack.Common.Collections;
using Microsoft.WindowsAzure.ResourceStack.Common.Json;
using Microsoft.WindowsAzure.ResourceStack.Providers.Application.Data.Definitions.Enums;
using Microsoft.WindowsAzure.ResourceStack.Providers.Application.Data.Definitions;
using Microsoft.WindowsAzure.ResourceStack.Providers.Application.Data.Entities;
using Microsoft.WindowsAzure.ResourceStack.Providers.Common.Constants;
using Microsoft.WindowsAzure.ResourceStack.Providers.Common.Data;
using Moq;
using Newtonsoft.Json.Linq;
using System;

namespace UnitTests.Providers.Worker.JobHandlerTests.ProvisioningJobHandler.UpdatingHandler.DBWorkspaceNotification
{
    [TestClass]
    public class EscDBWorkspaceNotificationTest : BaseWorkspaceUpdateTest
    {
        private ApplicationProperties incomingWorkspaceProperties;
        protected override ApplicationProperties IncomingWorkspaceProperties
        {
            get
            {
                if (incomingWorkspaceProperties == null)
                {
                    incomingWorkspaceProperties = base.IncomingWorkspaceProperties;
                    incomingWorkspaceProperties.EnhancedSecurityCompliance = new EnhancedSecurityCompliance()
                    {
                        AutomaticClusterUpdate = new AutomaticClusterUpdate()
                        {
                            Value = AutomaticClusterUpdateStatus.Enabled
                        },
                        ComplianceSecurityProfile = new ComplianceSecurityProfile()
                        {
                            Value = ComplianceSecurityProfileStatus.Enabled,
                            ComplianceStandards = new ComplianceStandard[] { ComplianceStandard.PCI_DSS, ComplianceStandard.HIPAA }
                        },
                        EnhancedSecurityMonitoring = new EnhancedSecurityMonitoring()
                        {
                            Value = EnhancedSecurityMonitoringStatus.Enabled
                        }
                    };
                }

                return incomingWorkspaceProperties;
            }
        }

        [TestInitialize]
        public void InitializeDBWorkspaceNotificationSucceedsTest()
        {
            this.Metadata.ResourceOperation = ProvisioningOperation.Updating;
            this.Metadata.Parameters = new InsensitiveDictionary<JToken>
            {
                { ProviderConstants.WorkspaceUpdateParameters.Properties, this.IncomingWorkspaceProperties?.ToJToken() },
                { ProviderConstants.WorkspaceUpdateParameters.Sku, this.IncomingEntitySku?.ToJToken() },
                { ProviderConstants.WorkspaceUpdateParameters.Tags, this.IncomingApplianceEntity?.Tags?.ToJToken() }
            };
        }

        [TestMethod("Job execution is set to succeeded")]
        public void TestApplianceProvisioningJobStatus()
        {
            this.FrontdoorEngine.Setup(
            o => o.GetDatabricksAccountApiEnpointAndAudience(
                It.IsAny<string>(),
                It.IsAny<string>())).ReturnsAsync
                ((new Uri("http://anyurl.com"), "", true));
            using (new AppConfigSettingScope(this.ConfigurationSettings))
            {
                var postponeExecutionResult = this.ProvisioningHandler.OnJobExecute().Result;
                Assert.AreEqual(JobExecutionStatus.Succeeded, postponeExecutionResult.Status);
            }
        }

        [TestMethod("Job execution is set to postponed with AccountApi")]
        public void TestApplianceProvisioningJobStatusWithAccountApi()
        {
            this.FrontdoorEngine.Setup(
            o => o.GetDatabricksAccountApiEnpointAndAudience(
                It.IsAny<string>(),
                It.IsAny<string>())).ReturnsAsync
                ((new Uri("http://anyurl.com"), "", false));
            using (new AppConfigSettingScope(this.ConfigurationSettings))
            {
                var postponeExecutionResult = this.ProvisioningHandler.OnJobExecute().Result;
                Assert.AreEqual(JobExecutionStatus.Postponed, postponeExecutionResult.Status);
            }
        }


        [TestMethod("ReplaceAppliance is called to replace existing appliance with updated appliance")]
        public void VerifyReplaceApplianceIsCalled()
        {
            using (new AppConfigSettingScope(this.ConfigurationSettings))
            {
                var result = this.ProvisioningHandler.OnJobExecute().Result;

                this.ApplianceDataProvider.Verify(
                        o => o.ReplaceAppliance(
                            It.Is<ApplianceEntity>(
                                entity =>
                                    entity == this.ApplianceEntity
                                    )));
            }
        }
    }
}
