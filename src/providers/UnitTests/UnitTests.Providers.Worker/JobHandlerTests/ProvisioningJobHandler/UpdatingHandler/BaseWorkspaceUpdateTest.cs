﻿//------------------------------------------------------------
// Copyright (c) Microsoft Corporation.  All rights reserved.
//------------------------------------------------------------

namespace UnitTests.Providers.Worker.JobHandlerTests.ProvisioningJobHandler.UpdatingHandler
{
    using System.Collections.Generic;
    using Microsoft.VisualStudio.TestTools.UnitTesting;
    using Microsoft.WindowsAzure.ResourceStack.Common.Collections;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Application.Data.Definitions;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Application.Data.Entities;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Common.Constants;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Common.Data;
    using Newtonsoft.Json.Linq;

    [TestClass]
    public abstract class BaseWorkspaceUpdateTest : BaseWorkspaceProvisioningTest
    {
        protected virtual JToken IncomingEntitySku
        {
            get
            {
                return JToken.Parse(@"{ ""name"": ""Standard""}");
            }
        }

        private ApplianceEntity incomingApplianceEntity;
        protected virtual ApplianceEntity IncomingApplianceEntity
        {
            get
            {
                if (incomingApplianceEntity == null)
                {
                    incomingApplianceEntity = new ApplianceEntity();
                    incomingApplianceEntity.SubscriptionId = Constants.SubscriptionId;
                    incomingApplianceEntity.ResourceGroup = Constants.ResourceGroupName;
                    incomingApplianceEntity.Name = Constants.ApplicationName;
                    incomingApplianceEntity.Sku = this.IncomingEntitySku;
                    incomingApplianceEntity.Location = Constants.Location;
                    incomingApplianceEntity.Properties = this.incomingWorkspaceProperties;
                    incomingApplianceEntity.Metadata = this.incomingApplianceMetadata;

                    var tags = new InsensitiveDictionary<string>();
                    tags.Add("resourceTags", "testtag");

                    incomingApplianceEntity.Tags = tags;
                }
                return incomingApplianceEntity;
            }
        }

        private ApplicationProperties incomingWorkspaceProperties;
        protected virtual ApplicationProperties IncomingWorkspaceProperties
        {
            get
            {
                if (incomingWorkspaceProperties == null)
                {
                    incomingWorkspaceProperties = new ApplicationProperties();
                    incomingWorkspaceProperties.CreatedBy = new ApplicationClientDetailsEntity();
                    incomingWorkspaceProperties.CreatedBy.ApplicationId = Constants.CreatedByAppId;
                    incomingWorkspaceProperties.CreatedBy.Oid = Constants.CreatedByPrincipalOid;
                    incomingWorkspaceProperties.CreatedBy.Puid = Constants.CreatedByPrincipalLegacyPuid;

                    incomingWorkspaceProperties.ComputeMode = Microsoft.WindowsAzure.ResourceStack.Providers.Common.Definitions.ComputeMode.Hybrid;
                    incomingWorkspaceProperties.ManagedResourceGroupId = $"/subscriptions/{Constants.SubscriptionId}/resourceGroups/{Constants.ManagedResourceGroupName}";
                    incomingWorkspaceProperties.ProvisioningState = ProvisioningState.Succeeded;
                    incomingWorkspaceProperties.WorkspaceId = Constants.InternalWorkspaceId;
                    incomingWorkspaceProperties.WorkspaceUrl = Constants.WorkspaceUrl;

                    incomingWorkspaceProperties.Parameters = this.incomingWorkspaceParameters;
                    incomingWorkspaceProperties.PublisherPackageId = Constants.PublisherPackageId;
                }
                return incomingWorkspaceProperties;
            }
        }

        private List<ApplicationAuthorization> incomingAuthorizationsList;
        protected virtual ApplicationAuthorization[] ExistingAuthorizations
        {
            get
            {
                if (incomingAuthorizationsList == null)
                {
                    var authorization = new ApplicationAuthorization();
                    authorization.PrincipalId = Constants.AuthorizationPrincipalId;
                    authorization.RoleDefinitionId = Constants.AuthorizationRoleDefinitionId;

                    incomingAuthorizationsList = new List<ApplicationAuthorization>();
                    incomingAuthorizationsList.Add(authorization);
                }

                return incomingAuthorizationsList.ToArray();
            }
        }

        private InsensitiveDictionary<JToken> incomingWorkspaceParameters;
        protected virtual InsensitiveDictionary<JToken> IncomingWorkspaceParameters
        {
            get
            {
                if (incomingWorkspaceParameters == null)
                {
                    incomingWorkspaceParameters = new InsensitiveDictionary<JToken>();
                    incomingWorkspaceParameters.Add("enableFedRampCertification", JToken.Parse(@"{ ""type"": ""Bool"", ""value"": false}"));
                    incomingWorkspaceParameters.Add("enableNoPublicIp", JToken.Parse(@"{ ""type"": ""Bool"", ""value"": false}"));
                    incomingWorkspaceParameters.Add("prepareEncryption", JToken.Parse(@"{ ""type"": ""Bool"", ""value"": true}"));
                    incomingWorkspaceParameters.Add("requireInfrastructureEncryption", JToken.Parse(@"{ ""type"": ""Bool"", ""value"": false}"));
                    incomingWorkspaceParameters.Add(ProviderConstants.Databricks.StorageAccountNameParameter, JToken.Parse(@"{ ""type"": ""String"", ""value"": """ + Constants.StorageAccountName + @"""}"));
                    incomingWorkspaceParameters.Add("storageAccountSkuName", JToken.Parse(@"{ ""type"": ""String"", ""value"": ""Standard_GRS""}"));
                    incomingWorkspaceParameters.Add("vnetAddressPrefix", JToken.Parse(@"{ ""type"": ""String"", ""value"": ""10.139""}"));
                    incomingWorkspaceParameters.Add("resourceTags", JToken.Parse(@"{ ""type"": ""Object"", ""value"":  { ""application"": ""databricks"", ""databricks-environment"": ""true"" }}"));
                }

                return incomingWorkspaceParameters;
            }
        }

        private ApplianceMetadata incomingApplianceMetadata;
        protected virtual ApplianceMetadata IncomingApplianceMetadata
        {
            get
            {
                if (incomingApplianceMetadata == null)
                {
                    incomingApplianceMetadata = new ApplianceMetadata();
                }

                return incomingApplianceMetadata;
            }
        }
    }
}