﻿//-----------------------------------------------------------
// Copyright (c) Microsoft Corporation.  All rights reserved.
//-----------------------------------------------------------

namespace UnitTests.Providers.Worker.JobHandlerTests.ProvisioningJobHandler.InitializingHandler.ServerlessWorkspace
{
    using System.Net;
    using Microsoft.VisualStudio.TestTools.UnitTesting;
    using Microsoft.WindowsAzure.ResourceStack.Common.BackgroundJobs;
    using Microsoft.WindowsAzure.ResourceStack.Common.Json;
    using UnitTests.Providers.Worker.JobHandlerTests.ProvisioningJobHandler.UpdatingHandler;

    [TestClass]
    public class HybridWorkspaceProvisioningTests : BaseWorkspaceInitializingTest
    {
        [TestMethod]
        public void TestApplianceProvisioningJobStatus()
        {
            using (new AppConfigSettingScope(this.ConfigurationSettings))
            {
                var result = this.ProvisioningHandler.OnJobExecute().Result;
                Assert.AreEqual(JobExecutionStatus.Postponed, result.Status);
                Assert.IsNotNull(result.NextExecutionTime);
                Assert.IsFalse(result.Message.Contains("serverless workspace"));
                Assert.IsTrue(result.Message.Contains("ARM"));
                Assert.AreEqual(HttpStatusCode.OK.ToString(), result.Details);
                Assert.AreEqual(this.Metadata.ToJson(), result.NextMetadata);
            }
        }
    }
}
