﻿//-----------------------------------------------------------
// Copyright (c) Microsoft Corporation.  All rights reserved.
//-----------------------------------------------------------

namespace UnitTests.Providers.Worker.WorkspaceScanJobTest.Actions
{
    using System;
    using System.Collections.Generic;
    using System.Threading.Tasks;
    using global::Providers.Job.Actions;
    using global::Providers.Job.Configuration;
    using Microsoft.VisualStudio.TestTools.UnitTesting;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Common.Engines;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Common.Entities;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Common.Responses;
    using Moq;

    [TestClass]
    public class GetResourceGroupLocksTest : BaseWorkspaceScanJobTest
    {
        private GetResourceGroupLocks GetResourceGroupLocksObj;
        private LockResponse lockResponse;
        private ScanJobConfiguration scanJobConfiguration;

        [TestInitialize]
        public void Setup()
        {
            lockResponse = new LockResponse
            {
                Value = new List<Lock> {
                                new Lock() { Name = "readonly", Id= "test",
                                             Properties = new LockProperties() { Level = "ReadOnly", Owners = new List<Owner>() }}
                                       }
            };
        }

        private ScanJobConfiguration GetWorkspaceScanJobConfiguration(IFrontdoorEngine frontdoorEngine)
        {
            return scanJobConfiguration = new ScanJobConfiguration
            {
                ProvidersLocation = this.Metadata.ProvidersLocation,
                CloudEnvironment = CloudEnvironment.AzureDatabricksRPWarm,
                ApplicationDataProvidersContainer = null,
                FrontdoorEngine = frontdoorEngine
            };

        }

        [TestMethod("Should Execute method should return false if appliance is deleted")]
        public void ShouldExecuteMethodReturnsCorrectValue()
        {
            using (new AppConfigSettingScope(this.ConfigurationSettings))
            {
                this.scanJobConfiguration = GetWorkspaceScanJobConfiguration(this.FrontdoorEngine.Object);
                GetResourceGroupLocksObj = new GetResourceGroupLocks(scanJobConfiguration, this.Logger.Object);
                Assert.IsNotNull(GetResourceGroupLocksObj);

                var applianceEntity = GetApplianceEntity();
                applianceEntity.DeletedTime = DateTime.UtcNow;

                Assert.IsFalse(GetResourceGroupLocksObj.ShouldExecuteAction(applianceEntity));
            }
        }

        [TestMethod("Handle Job Action method throws exception if it could not fetch locks")]
        public void HandleJobActionThrowsException()
        {
            this.FrontdoorEngine.Setup(
                x => x.GetResourcesFromFrontdoor<LockResponse>(It.IsAny<string>(), It.IsAny<Uri>(), It.IsAny<string>(),It.IsAny<string>())).ReturnsAsync(() => null);

            this.scanJobConfiguration = GetWorkspaceScanJobConfiguration(this.FrontdoorEngine.Object);
            GetResourceGroupLocksObj = new GetResourceGroupLocks(scanJobConfiguration, this.Logger.Object);
            Assert.IsNotNull(GetResourceGroupLocksObj);

            using (new AppConfigSettingScope(this.ConfigurationSettings))
            {
                Assert.IsNotNull(GetResourceGroupLocksObj);

                var applianceEntity = GetApplianceEntity();

                Assert.ThrowsException<Exception>(() => GetResourceGroupLocksObj.HandleJobAction(applianceEntity).GetAwaiter().GetResult(), "Could not fetch resourceGroupLockObject.");
            }
        }

        [TestMethod("Handle Job Action method completes sucessfully ")]
        public async Task HandleJobActionCompletesSucessfully()
        {
            this.FrontdoorEngine.Setup(
                x => x.GetResourcesFromFrontdoor<LockResponse>(It.IsAny<string>(), It.IsAny<Uri>(), It.IsAny<string>(), It.IsAny<string>())).ReturnsAsync(() => lockResponse);
            this.scanJobConfiguration = GetWorkspaceScanJobConfiguration(this.FrontdoorEngine.Object);
            GetResourceGroupLocksObj = new GetResourceGroupLocks(scanJobConfiguration, this.Logger.Object);

            using (new AppConfigSettingScope(this.ConfigurationSettings))
            {
                Assert.IsNotNull(GetResourceGroupLocksObj);

                var applianceEntity = GetApplianceEntity();
                await GetResourceGroupLocksObj.HandleJobAction(applianceEntity);
            }
        }

        [TestMethod("Handle Job Action method completes if there is no lock on the resource")]
        public async Task HandleJobActionCompletesIfNoLocksOnWorkspace()
        {
            lockResponse = new LockResponse
            {
                Value = new List<Lock> { }
            };

            this.FrontdoorEngine.Setup(
                x => x.GetResourcesFromFrontdoor<LockResponse>(It.IsAny<string>(), It.IsAny<Uri>(), It.IsAny<string>(),It.IsAny<string>())).ReturnsAsync(() => lockResponse);

            this.scanJobConfiguration = GetWorkspaceScanJobConfiguration(this.FrontdoorEngine.Object);
            GetResourceGroupLocksObj = new GetResourceGroupLocks(scanJobConfiguration, this.Logger.Object);
            Assert.IsNotNull(GetResourceGroupLocksObj);

            using (new AppConfigSettingScope(this.ConfigurationSettings))
            {
                Assert.IsNotNull(GetResourceGroupLocksObj);
                var applianceEntity = GetApplianceEntity();
                await GetResourceGroupLocksObj.HandleJobAction(applianceEntity);
            }
        }
    }
}
