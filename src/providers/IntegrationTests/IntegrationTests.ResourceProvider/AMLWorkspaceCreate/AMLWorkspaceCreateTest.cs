﻿//------------------------------------------------------------
// Copyright (c) Microsoft Corporation.  All rights reserved.
//------------------------------------------------------------

namespace IntegrationTests.ResourceProvider.AMLWorkspaceCreate
{
    using System.Linq;
    using System.Net;
    using System.Threading;
    using System.Threading.Tasks;
    using IntegrationTests.ResourceProvider.BaseClasses;
    using Microsoft.VisualStudio.TestTools.UnitTesting;
    using Newtonsoft.Json.Linq;
    using RestSharp;

    [TestClass]
    public class AMLWorkspaceCreateTest : BaseIntegrationTest<AMLWorkspaceCreateFrontDoorBuilder, BaseDatabaseSeeder, BaseCustomSettings>
    {
        [TestMethod]
        public async Task TestBasicWorkspaceSuccessfulCreation()
        {
            using (var rp = KestrelHost.Start(Constants.EndPoints.RPHost))
            {
                var putClient = new RestClient(Constants.RequestMetadata.RequestUrl);
                var putRequest = new AMLWorkspaceCreateRequestBuilder().Build();
                var putResponse = await putClient.PutAsync(putRequest);
                Assert.IsNotNull(putResponse);
                Assert.AreEqual(HttpStatusCode.Created, putResponse.StatusCode);

                var jobUrl = (string)putResponse.Headers.Single(x => x.Name == "Azure-AsyncOperation").Value;

                // Url response will point to frontdoor host, because all requests come through ARM.
                // But we are only testing the RP, so the call needs to be made to the RP Edpoint.
                jobUrl = jobUrl.Replace(Constants.EndPoints.FrontDoorHost, Constants.EndPoints.RPHost);

                var jobCompleted = false;

                var timeOutCount = Constants.TimeOuts.TimeoutCount;

                RestResponse jobStatusCheckResponse = null;

                while (timeOutCount > 0)
                {
                    var getJobStatusClient = new RestClient(jobUrl);
                    var jobStatusCheckRequest = new BaseRequestBuilder().Build();
                    jobStatusCheckResponse = await getJobStatusClient.GetAsync(jobStatusCheckRequest);
                    Assert.IsNotNull(jobStatusCheckResponse);

                    if (jobStatusCheckResponse.StatusCode != HttpStatusCode.Accepted)
                    {
                        jobCompleted = true;
                        break;
                    }

                    Thread.Sleep(Constants.TimeOuts.TimeoutPeriod);
                    timeOutCount--;

                }


                Assert.IsTrue(jobCompleted);

                var client = new RestClient(Constants.RequestMetadata.RequestUrl);
                var request = new BaseRequestBuilder().Build();
                var response = await client.GetAsync(request);

                Assert.IsNotNull(response);
                Assert.AreEqual(HttpStatusCode.OK, response.StatusCode);

                var ws = JObject.Parse(response.Content);

                Assert.AreEqual(Constants.RequestMetadata.Name, ws["name"].Value<string>());
                Assert.AreEqual(Constants.RequestMetadata.ResourceId, ws["id"].Value<string>());
                Assert.AreEqual(Constants.RequestBody.Location, ws["location"].Value<string>());
                Assert.AreEqual(Constants.RequestBody.ManagedResourceGroupId, ws["properties"]["managedResourceGroupId"].Value<string>());
                Assert.AreEqual("Succeeded", ws["properties"]["provisioningState"].Value<string>());
                Assert.AreEqual(Constants.DBWorkspace.WorkspaceId, ws["properties"]["workspaceId"].Value<string>());
                Assert.AreEqual(Constants.DBWorkspace.WorkspaceUrl, ws["properties"]["workspaceUrl"].Value<string>());

                // Check the AMLWorkspaceId Value is passed during the deployment url
                var deploymentUrlIndex = this.Frontdoor.LogEntries.ToList().FindIndex(x => x.RequestMessage.AbsoluteUrl == Constants.AMLWorkspaceIdRequestMetadata.DeploymentUrl);
                var deploymentBody = this.Frontdoor.LogEntries.ToList()[deploymentUrlIndex].RequestMessage.Body;
                var amlWorkspaceIdValue = JObject.Parse(deploymentBody)["properties"]["parameters"]["amlWorkspaceId"]["value"];

                Assert.AreEqual(Constants.AMLWorkspaceIdRequestMetadata.AMLWorkspaceID, amlWorkspaceIdValue.Value<string>());
            }
        }
    }
}
