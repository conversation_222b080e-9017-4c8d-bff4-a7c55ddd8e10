﻿//------------------------------------------------------------
// Copyright (c) Microsoft Corporation.  All rights reserved.
//------------------------------------------------------------

namespace IntegrationTests.ResourceProvider.AMLWorkspaceCreate
{
    using IntegrationTests.ResourceProvider.BaseClasses;
    using RestSharp;

    public class AMLWorkspaceCreateRequestBuilder : BaseRequestBuilder
    {
        public override RestRequest Update(RestRequest request)
        {
            var requestBody =
                "{" +
                    "\"sku\": { \"name\": \"" + Constants.RequestBody.SkuPremium + "\" }," +
                    "\"location\": \"" + Constants.RequestBody.Location + "\"," +
                    "\"tags\": { }," +
                    "\"properties\": { \"ManagedResourceGroupId\": \"" +

                        Constants.RequestBody.ManagedResourceGroupId + "\"," +
                        "\"parameters\" : {" +
                                "\"amlWorkspaceId\" : {" +
                                    "\"value\" : \"" + Constants.AMLWorkspaceIdRequestMetadata.AMLWorkspaceID + "\" " +
                                "}" +
                            "}" +
                        "}" +
                    "}";


            request.AddStringBody(requestBody, DataFormat.Json);
            return request;
        }
    }
}
