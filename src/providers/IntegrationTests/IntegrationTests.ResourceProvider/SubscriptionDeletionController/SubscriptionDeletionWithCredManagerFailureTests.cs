﻿//------------------------------------------------------------
// Copyright (c) Microsoft Corporation.  All rights reserved.
//------------------------------------------------------------

namespace IntegrationTests.ResourceProvider.FeatureSubscriptionTest
{
    using System.Linq;
    using System.Net;
    using System.Threading.Tasks;
    using IntegrationTests.ResourceProvider.BaseClasses;
    using IntegrationTests.ResourceProvider.Compression;
    using Microsoft.VisualStudio.TestTools.UnitTesting;
    using RestSharp;

    [TestClass]
    public class SubscriptionDeletionWithCredManagerFailureTests : BaseIntegrationTest<CredManagerCallFailsWithErrorFrontdoorBuilder, SubscriptionDeletionWithAccessConnectorDatabaseSeeder, PutSubscriptionCustomSetting>
    {
        [TestMethod]
        public async Task SubscriptionDeletionWhenCredManagerCallFails()
        {
            using var rp = KestrelHost.Start(Constants.EndPoints.RPHost);
            var subscriptionPutClient = new RestClient(Constants.FeatureSubscriptionRequestMetadata.RequestUrl);
            var requestBuilder = new FeatureSubscriptionRequestBuilderWithDevControlPlaneFeature();
            var featureSubscriptionPutRequest = requestBuilder.Build();
            var subscriptionPutResponse = await subscriptionPutClient.ExecutePutAsync(featureSubscriptionPutRequest);
            Assert.AreEqual(subscriptionPutResponse.StatusCode, HttpStatusCode.Accepted);

            var jobUrl = (string)subscriptionPutResponse.Headers.CoalesceEnumerable().Single(x => x.Name == "Azure-AsyncOperation").Value;
            var response = await ApplianceTestHelper.WaitForJobCompletion(jobUrl);

            // Verify that access connectors are cleaned up in OnJobMaxLifetimeExceeded
            await ApplianceTestHelper.VerifyAllAccessConnectorsAreDeletedFromSubscription();
        }
    }
}
