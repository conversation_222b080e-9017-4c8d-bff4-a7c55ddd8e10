﻿//------------------------------------------------------------
// Copyright (c) Microsoft Corporation.  All rights reserved.
//------------------------------------------------------------

namespace IntegrationTests.ResourceProvider.FeatureSubscriptionTest
{
    using System.Net;
    using System.Threading.Tasks;
    using IntegrationTests.ResourceProvider.BaseClasses;
    using Microsoft.VisualStudio.TestTools.UnitTesting;
    using RestSharp;

    [TestClass]
    public class FeatureSubscriptionControllerTest : BaseIntegrationTest<FeatureSubscriptionFrontdoorBuilder, GetWorkspacesBySubscriptionIdDatabaseSeeder, BaseCustomSettings>
    {
        [TestMethod]
        public async Task FeatureSubscriptionController_SyncSuccess()
        {
            using var rp = KestrelHost.Start(Constants.EndPoints.RPHost);
            var subscriptionPutClient = new RestClient(Constants.FeatureSubscriptionRequestMetadata.RequestUrl);
            var featureSubscriptionRequestBuilder = new FeatureSubscriptionRequestBuilder();
            var featureSubscriptionPutRequest = featureSubscriptionRequestBuilder.Build();
            var subscriptionPutResponse = await subscriptionPutClient.ExecutePutAsync(featureSubscriptionPutRequest);
            Assert.AreEqual(subscriptionPutResponse.StatusCode, HttpStatusCode.OK);
        }
    }
}
