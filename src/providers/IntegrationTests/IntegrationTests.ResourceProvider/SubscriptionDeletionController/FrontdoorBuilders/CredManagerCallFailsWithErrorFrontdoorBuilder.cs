﻿//------------------------------------------------------------
// Copyright (c) Microsoft Corporation.  All rights reserved.
//------------------------------------------------------------

namespace IntegrationTests.ResourceProvider.FeatureSubscriptionTest
{
    using System.Net;
    using IntegrationTests.ResourceProvider.BaseClasses;
    using WireMock.RequestBuilders;
    using WireMock.ResponseBuilders;
    using WireMock.Server;

    public class CredManagerCallFailsWithErrorFrontdoorBuilder : IWireMockServerBuilder
    {
        public WireMockServer Build()
        {
            var frontDoor = WireMockServer.StartWithAdminInterface(Constants.EndPoints.FrontDoorPort);
            
            frontDoor.Given(
                Request.Create()
                    .WithPath(Constants.RequestMetadata.AccountsApiWorkspaceUrl)
                    .UsingDelete())
            .RespondWith(Response.Create()
                .WithStatusCode(HttpStatusCode.OK)
                .WithBody(string.Empty));

            var errorResponseJson = @"{
                        ""error_code"": ""InternalServerError"",
                        ""message"": ""An error occurred.""
                      }";

            frontDoor.Given(
                    Request.Create()
                        .WithPath(Constants.RequestMetadata.AccessConnectorCredManagerUrl)
                        .UsingPut())
                .RespondWith(Response.Create()
                    .WithStatusCode((HttpStatusCode)500)
                    .WithHeader("Content-Type", "application/json")
                    .WithBody(errorResponseJson));

            return frontDoor;
        }
    }
}
