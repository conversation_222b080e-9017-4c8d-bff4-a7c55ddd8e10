﻿//------------------------------------------------------------
// Copyright (c) Microsoft Corporation.  All rights reserved.
//------------------------------------------------------------

namespace IntegrationTests.ResourceProvider.FeatureSubscriptionTest
{
    using System;
    using System.Net;
    using IntegrationTests.ResourceProvider.BaseClasses;
    using WireMock.RequestBuilders;
    using WireMock.ResponseBuilders;
    using WireMock.Server;

    public class FeatureSubscriptionFrontdoorBuilder : IWireMockServerBuilder
    {
        public WireMockServer Build()
        {
            var frontDoor = WireMockServer.StartWithAdminInterface(Constants.EndPoints.FrontDoorPort);

            frontDoor.AllowPartialMapping(false);

            var dbWorkspaceUrl = $@"/{Constants.RequestMetadata.ResourceId}/dbworkspaces/dbworkspace";

            frontDoor.Given(
                Request.Create()
                .WithPath(dbWorkspaceUrl)
                .UsingDelete()
            ).RespondWith(
                Response.Create()
                .WithHeader("Content-Type", "application/json")
                .WithStatusCode(HttpStatusCode.OK)
                .WithBody(String.Empty));

            return frontDoor;
        }
    }
}
