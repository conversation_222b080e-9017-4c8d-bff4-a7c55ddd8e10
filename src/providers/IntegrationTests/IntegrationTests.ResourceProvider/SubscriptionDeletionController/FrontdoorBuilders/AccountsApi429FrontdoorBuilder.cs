﻿//------------------------------------------------------------
// Copyright (c) Microsoft Corporation.  All rights reserved.
//------------------------------------------------------------

namespace IntegrationTests.ResourceProvider.FeatureSubscriptionTest
{
    using System.Net;
    using IntegrationTests.ResourceProvider.BaseClasses;
    using WireMock.RequestBuilders;
    using WireMock.ResponseBuilders;
    using WireMock.Server;

    public class AccountsApi429FrontdoorBuilder : IWireMockServerBuilder
    {
        public WireMockServer Build()
        {
            var frontDoor = WireMockServer.StartWithAdminInterface(Constants.EndPoints.FrontDoorPort);

            frontDoor.AllowPartialMapping(false);

            var errorResponseJson = @"{
                        ""error_code"": ""Too Many Requests"",
                        ""message"": ""An error occurred.""
                      }";

            frontDoor.Given(
                    Request.Create()
                        .WithPath(Constants.RequestMetadata.AccountsApiWorkspaceUrl)
                        .UsingDelete())
                .RespondWith(Response.Create()
                    .WithStatusCode((HttpStatusCode)429)
                    .WithHeader("Content-Type", "application/json")
                    .WithBody(errorResponseJson));

            return frontDoor;
        }
    }
}
