﻿//------------------------------------------------------------
// Copyright (c) Microsoft Corporation.  All rights reserved.
//------------------------------------------------------------

namespace IntegrationTests.ResourceProvider.FeatureSubscriptionTest
{
    using System.Net;
    using IntegrationTests.ResourceProvider.BaseClasses;
    using WireMock.RequestBuilders;
    using WireMock.ResponseBuilders;
    using WireMock.Server;

    public class CredManagerFrontdoorBuilder : IWireMockServerBuilder
    {
        public WireMockServer Build()
        {
            var frontDoor = WireMockServer.StartWithAdminInterface(Constants.EndPoints.FrontDoorPort);

            frontDoor.Given(
                    Request.Create()
                        .WithPath(Constants.RequestMetadata.AccessConnectorCredManagerUrl)
                        .UsingPut())
                .RespondWith(Response.Create()
                    .WithStatusCode((HttpStatusCode)200)
                    .WithHeader("Content-Type", "application/json"));

            return frontDoor;
        }
    }
}
