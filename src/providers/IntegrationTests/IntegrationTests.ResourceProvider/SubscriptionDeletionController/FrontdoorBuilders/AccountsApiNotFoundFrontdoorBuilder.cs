﻿//------------------------------------------------------------
// Copyright (c) Microsoft Corporation.  All rights reserved.
//------------------------------------------------------------

namespace IntegrationTests.ResourceProvider.FeatureSubscriptionTest
{
    using System.Net;
    using IntegrationTests.ResourceProvider.BaseClasses;
    using WireMock.RequestBuilders;
    using WireMock.ResponseBuilders;
    using WireMock.Server;

    public class AccountsApiNotFoundFrontdoorBuilder : IWireMockServerBuilder
    {
        public WireMockServer Build()
        {
            var frontDoor = WireMockServer.StartWithAdminInterface(Constants.EndPoints.FrontDoorPort);

            frontDoor.AllowPartialMapping(false);

            frontDoor.Given(
                    Request.Create()
                        .WithPath(Constants.RequestMetadata.AccountsApiWorkspaceUrl)
                        .UsingDelete())
                .RespondWith(Response.Create()
                    .WithStatusCode(HttpStatusCode.NotFound)
                    .WithBody(string.Empty));

            return frontDoor;
        }
    }
}
