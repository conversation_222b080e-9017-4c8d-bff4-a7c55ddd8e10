﻿//------------------------------------------------------------
// Copyright (c) Microsoft Corporation.  All rights reserved.
//------------------------------------------------------------

namespace IntegrationTests.ResourceProvider.FeatureSubscriptionTest
{
    using System.Net;
    using IntegrationTests.ResourceProvider.BaseClasses;
    using WireMock.RequestBuilders;
    using WireMock.ResponseBuilders;
    using WireMock.Server;

    public class AccountsApiBadRequestFrontdoorBuilder : IWireMockServerBuilder
    {
        public WireMockServer Build()
        {
            var frontDoor = WireMockServer.StartWithAdminInterface(Constants.EndPoints.FrontDoorPort);

            var errorResponseJson = @"{
                        ""error_code"": ""BadRequest"",
                        ""message"": ""An error occurred.""
                      }";

            frontDoor.Given(
                    Request.Create()
                        .WithPath(Constants.RequestMetadata.AccountsApiWorkspaceUrl)
                        .UsingDelete())
                .RespondWith(Response.Create()
                    .WithStatusCode(HttpStatusCode.BadRequest)
                    .WithHeader("Content-Type", "application/json")
                    .WithBody(errorResponseJson));

            return frontDoor;
        }
    }
}
