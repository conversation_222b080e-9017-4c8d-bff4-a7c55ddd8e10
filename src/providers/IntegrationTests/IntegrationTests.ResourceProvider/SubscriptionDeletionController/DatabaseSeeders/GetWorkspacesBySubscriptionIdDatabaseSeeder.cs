﻿//------------------------------------------------------------
// Copyright (c) Microsoft Corporation.  All rights reserved.
//------------------------------------------------------------

namespace IntegrationTests.ResourceProvider.FeatureSubscriptionTest
{
    using System;
    using System.Collections.Generic;
    using IntegrationTests.ResourceProvider.BaseClasses;
    using IntegrationTests.ResourceProvider.Compression;
    using Newtonsoft.Json.Linq;

    public class GetWorkspacesBySubscriptionIdDatabaseSeeder : BaseDatabaseSeeder
    {
        public override List<string> UpdateRequestBody()
        {
            string properties = ApplianceTestHelper.GetAppliancePropertiesWithDBWorkspaceDetailJson()
                .Replace("testManagedResourceGroupId", Constants.RequestBody.ManagedResourceGroupId)
                .Replace("testPublisherPackageId", "databricks.databricks-workspace-previewdatabricks-workspace.0.0.2");

            var PropertiesV2EntityPropery = ResrourceStackCompression.EncodeToBinaryEnvelope(properties, new ModernCompressionUtility());
            var bodies = new List<string>();

            bodies.Add(
                " {" +
                    "\"PartitionKey\": \"AEB88\"," +
                    "\"RowKey\": \"12345678ABCDABCDABCDBA9876543B10_PLAN-2018:2D04:2D01-TESTWS\"," +
                     "\"CreatedTime\": \"2022-10-26T17:49:51.142Z\"," +
                    "\"<EMAIL>\": \"Edm.DateTime\"," +
                    "\"ChangedTime\": \"2022-10-26T17:49:57.033Z\"," +
                    "\"<EMAIL>\": \"Edm.DateTime\"," +
                    "\"DeletedTime\": \"1970-01-01T00:00:00.000Z\"," +
                    "\"<EMAIL>\": \"Edm.DateTime\"," +
                    "\"ChangedOperationId\": \"562d4a00-2295-4e21-9355-3c68bb2cd68f\"," +
                    "\"SubscriptionId\": \"12345678-abcd-abcd-abcd-ba9876543b10\"," +
                    "\"ResourceGroup\": \"testrg\"," +
                    "\"Name\": \"testws\"," +
                    "\"Sku\": \"{\\\"name\\\": \\\"Premium\\\"}\"," +
                    "\"Location\": \"wakandacentral\"," +
                    "\"Tags\": \"{\\\"team\\\": \\\"adb\\\"}\"," +
                    "\"Metadata\": \"XY2xCkIxDEX/JbPo/jZBcFEH/YKYRim1TUlSHo/Sf7c4ul04nHM7ZCz45nBnk6bEZ5VWL0LphplhAWfz1WAHgetHtszF/4lUVvQo5eHozdhg6XCNpGLy8v0JHZ8aKdlhFU1WkXhOjT77HewnzacjEVfnAGOMLw==\"," +
                    "\"<EMAIL>\": \"Edm.Binary\"," +
                    "\"PropertiesV2\": \"" + Convert.ToBase64String(PropertiesV2EntityPropery.BinaryValue) + "\"," +
                    "\"<EMAIL>\": \"Edm.Binary\"" +
                " }");

            bodies.Add(
                "{ " +
                    "\"PartitionKey\": \"AEB88\"," +
                    "\"RowKey\": \"12345678ABCDABCDABCDBA9876543B10_RGA-TESTRG-TESTWS\"," +
                    "\"CreatedTime\": \"2022-10-26T17:49:51.142Z\"," +
                    "\"<EMAIL>\": \"Edm.DateTime\"," +
                    "\"ChangedTime\": \"2022-10-26T17:49:57.033Z\"," +
                    "\"<EMAIL>\": \"Edm.DateTime\"," +
                    "\"DeletedTime\": \"1970-01-01T00:00:00.000Z\"," +
                    "\"<EMAIL>\": \"Edm.DateTime\"," +
                    "\"ChangedOperationId\": \"562d4a00-2295-4e21-9355-3c68bb2cd68f\"," +
                    "\"SubscriptionId\": \"12345678-abcd-abcd-abcd-ba9876543b10\"," +
                    "\"ResourceGroup\": \"testrg\"," +
                    "\"Name\": \"testws\"," +
                    "\"Sku\": \"{\\\"name\\\": \\\"Premium\\\"}\"," +
                    "\"Location\": \"wakandacentral\"," +
                    "\"Tags\": \"{\\\"team\\\": \\\"adb\\\"}\"," +
                    "\"Metadata\": \"XZDdboMwDIXfJddlgdJQ4K7SpN1007SfBwjBQAYlmW2KpqrvvnRb2bQby5J97HO+kzjoUbdQPwG5CQ3coZv83pn+QR9AlIKBeCaxEjX4wX0cYOT/E+cBNVs3PrPmiYBEeRL31qAj1/DNrWZdoTU9ydlhT14bCC1aDvdPgr5E4dPOGPAMdfi1XHxBbXo7tq9ow0bH7EspB2f00DniMo/jRNJUkUHrLw5IJut0o7JtHunK1H9KpYt8m6lNWiWxxJ+w7SUsyXpxGGEbzRR1vJ3fG+7evAIrPbqjrQFJ/oa64grihQvJb1hysX8FIuNc5SpVaVEkWZHEKlsXmTifV8LSI9qjZtjbsd8Ng5sDgLLRA8H5Ew==\"," +
                    "\"<EMAIL>\": \"Edm.Binary\"," +
                    "\"PropertiesV2\": \"" + Convert.ToBase64String(PropertiesV2EntityPropery.BinaryValue) + "\"," +
                    "\"<EMAIL>\": \"Edm.Binary\"" +
                " }");
            return bodies;
        }
    }
}
