﻿//------------------------------------------------------------
// Copyright (c) Microsoft Corporation.  All rights reserved.
//------------------------------------------------------------

namespace IntegrationTests.ResourceProvider.FeatureSubscriptionTest
{
    using System.Linq;
    using System.Net;
    using System.Threading;
    using System.Threading.Tasks;
    using IntegrationTests.ResourceProvider.BaseClasses;
    using Microsoft.Owin.Hosting;
    using Microsoft.VisualStudio.TestTools.UnitTesting;
    using Newtonsoft.Json.Linq;
    using RestSharp;

    [TestClass]
    public class FeatureSubscriptionWithNoActiveWorkspacesControllerTest : BaseIntegrationTest<FeatureSubscriptionFrontdoorBuilder, BaseDatabaseSeeder, BaseCustomSettings>
    {
        [TestMethod]
        public async Task FeatureSubscriptionControllerNoActiveWorkspaces_Success()
        {
            using var rp = KestrelHost.Start(Constants.EndPoints.RPHost);
            var subscriptionPutClient = new RestClient(Constants.FeatureSubscriptionRequestMetadata.RequestUrl);
            var featureSubscriptionRequestBuilder = new FeatureSubscriptionRequestBuilder();
            var featureSubscriptionPutRequest = featureSubscriptionRequestBuilder.Build();
            var subscriptionPutResponse = await subscriptionPutClient.ExecutePutAsync(featureSubscriptionPutRequest);
            Assert.AreEqual(subscriptionPutResponse.StatusCode, HttpStatusCode.OK);
        }
    }
}
