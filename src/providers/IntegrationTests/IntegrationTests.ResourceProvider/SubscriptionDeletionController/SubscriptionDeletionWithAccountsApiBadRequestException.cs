﻿//------------------------------------------------------------
// Copyright (c) Microsoft Corporation.  All rights reserved.
//------------------------------------------------------------

namespace IntegrationTests.ResourceProvider.FeatureSubscriptionTest
{
    using System.Linq;
    using System.Net;
    using System.Threading;
    using System.Threading.Tasks;
    using IntegrationTests.ResourceProvider.BaseClasses;
    using IntegrationTests.ResourceProvider.Compression;
    using Microsoft.VisualStudio.TestTools.UnitTesting;
    using RestSharp;

    [TestClass]
    public class SubscriptionDeletionWithAccountsApiBadRequestException : BaseIntegrationTest<AccountsApiBadRequestFrontdoorBuilder, GetWorkspacesBySubscriptionIdDatabaseSeeder, PutSubscriptionCustomSetting>
    {
        [TestMethod]
        public async Task SubscriptionDeletionWithAccountsApi_BadRequestException()
        {
            var jobCompleted = false;

            using var rp = KestrelHost.Start(Constants.EndPoints.RPHost);
            var subscriptionPutClient = new RestClient(Constants.FeatureSubscriptionRequestMetadata.RequestUrl);
            var featureSubscriptionRequestBuilder = new FeatureSubscriptionRequestBuilder();
            var featureSubscriptionPutRequest = featureSubscriptionRequestBuilder.Build();
            var subscriptionPutResponse = await subscriptionPutClient.ExecutePutAsync(featureSubscriptionPutRequest);
            Assert.AreEqual(subscriptionPutResponse.StatusCode, HttpStatusCode.Accepted);

            var jobUrl = (string)subscriptionPutResponse.Headers.CoalesceEnumerable().Single(x => x.Name == "Azure-AsyncOperation").Value;

            //// Url response will point to frontdoor host, because all requests come through ARM.
            //// But we are only testing the RP, so the call needs to be made to the RP Endpoint.
            jobUrl = jobUrl.Replace(Constants.EndPoints.FrontDoorHost, Constants.EndPoints.RPHost);

            var timeOutCount = Constants.TimeOuts.TimeoutCount;

            while (timeOutCount > 0)
            {
                var getJobStatusClient = new RestClient(jobUrl);
                var jobStatusCheckRequest = new BaseRequestBuilder().Build();
                var checkJobResponse = await getJobStatusClient.GetAsync(jobStatusCheckRequest);
                Assert.IsNotNull(checkJobResponse);

                if (checkJobResponse.StatusCode != HttpStatusCode.Accepted)
                {
                    jobCompleted = true;
                    break;
                }

                Thread.Sleep(Constants.TimeOuts.TimeoutPeriod * 10);
                timeOutCount--;
            }

            Assert.IsTrue(jobCompleted);
        }
    }
}
