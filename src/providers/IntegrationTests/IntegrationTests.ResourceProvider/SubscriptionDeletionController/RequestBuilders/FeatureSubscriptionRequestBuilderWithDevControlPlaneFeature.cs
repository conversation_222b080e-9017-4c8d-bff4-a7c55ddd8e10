﻿//------------------------------------------------------------
// Copyright (c) Microsoft Corporation.  All rights reserved.
//------------------------------------------------------------

namespace IntegrationTests.ResourceProvider.FeatureSubscriptionTest
{
    using IntegrationTests.ResourceProvider.BaseClasses;
    using RestSharp;

    public class FeatureSubscriptionRequestBuilderWithDevControlPlaneFeature : BaseRequestBuilder
    {
        public override RestRequest Update(RestRequest request)
        {
            var requestBody = @"
                    {
                        ""state"": ""Deleted"",
                        ""registrationDate"": ""Tue, 28 Jul 2020 05:47:19 GMT"",
                        ""properties"": {
                            ""tenantId"": """ + Constants.EncryptionSettings.TenantId + @""",
                            ""registeredFeatures"": [
                                {
                                    ""name"": ""Microsoft.Databricks/DevControlPlane"",
                                    ""state"": ""Registered""
                                }
                            ]
                        }
                    }";

            request.AddStringBody(requestBody, DataFormat.<PERSON>son);
            return request;
        }
    }
}
