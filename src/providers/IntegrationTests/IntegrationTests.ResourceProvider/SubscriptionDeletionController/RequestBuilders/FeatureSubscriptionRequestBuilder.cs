﻿//------------------------------------------------------------
// Copyright (c) Microsoft Corporation.  All rights reserved.
//------------------------------------------------------------

namespace IntegrationTests.ResourceProvider.FeatureSubscriptionTest
{
    using IntegrationTests.ResourceProvider.BaseClasses;
    using RestSharp;

    public class FeatureSubscriptionRequestBuilder : BaseRequestBuilder
    {
        public override RestRequest Update(RestRequest request)
        {
            var requestBody =
                "{" +
                "\"state\": \"Deleted\"," +
                "\"registrationDate\": \"Thu, 13 Apr 2023 11:03:13 GMT\"," +
                "\"properties\": { \"tenantId\": \"" + Constants.EncryptionSettings.TenantId + "\" }" +
                "}";

            request.AddStringBody(requestBody, DataFormat.Json);
            return request;
        }
    }
}
