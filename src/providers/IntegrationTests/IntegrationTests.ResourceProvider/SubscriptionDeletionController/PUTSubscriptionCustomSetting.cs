﻿//------------------------------------------------------------
// Copyright (c) Microsoft Corporation.  All rights reserved.
//------------------------------------------------------------

namespace IntegrationTests.ResourceProvider.FeatureSubscriptionTest
{
    using System;
    using IntegrationTests.ResourceProvider.BaseClasses;
    using System.Collections.Generic;

    public class PutSubscriptionCustomSetting : BaseCustomSettings
    {
        private IDictionary<string, string> settings;
        public PutSubscriptionCustomSetting()
        {
            settings = GetSettings();
            settings["EnableSubscriptionDeletionJob"] = bool.TrueString;
            settings["Microsoft.WindowsAzure.ResourceStack.SubscriptionDeleteJob.MaxRetryAttempts"] = "2";
        }
    }
}
