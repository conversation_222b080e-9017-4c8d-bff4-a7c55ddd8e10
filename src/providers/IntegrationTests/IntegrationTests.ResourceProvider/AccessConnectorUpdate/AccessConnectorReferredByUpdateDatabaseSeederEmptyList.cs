﻿//------------------------------------------------------------
// Copyright (c) Microsoft Corporation.  All rights reserved.
//------------------------------------------------------------

namespace IntegrationTests.ResourceProvider.AccessConnectorUpdate
{
    using System.Collections.Generic;
    using IntegrationTests.ResourceProvider.BaseClasses;
    using Microsoft.WindowsAzure.ResourceStack.Common.Json;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Common.Definitions.AccessConnector;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Common.Definitions.ManagedIdentity;
    public class AccessConnectorReferredByemptyListUpdateDatabaseSeeder : BaseDatabaseSeeder
    {
        public override List<string> UpdateAccessConnectorRequestBody()
        {
            var identity = new Identity { PrincipalId = "test", TenantId = "test" };
            var accessConnectorProperties = new AccessConnectorProperties()
            {
                ProvisioningState = AccessConnectorState.Succeeded,
                ReferedBy = new List<string>() {}
            };

            return ApplianceTestHelper.GetAccessConnectorRequestBody(accessConnectorProperties.ToJson(), identity.ToJson());
             
        }
    }

    public class NonExistingAccessConnectorReferredByUpdateDatabaseSeeder : BaseDatabaseSeeder
    {
        public override List<string> UpdateAccessConnectorRequestBody()
        {
            var identity = new Identity { PrincipalId = "test", TenantId = "test" };
            var accessConnectorProperties = new AccessConnectorProperties()
            {
                ProvisioningState = AccessConnectorState.Succeeded,
                ReferedBy = new List<string>() { }
            };

            return ApplianceTestHelper.GetEmptyAccessConnectorRequestBody(accessConnectorProperties.ToJson(), identity.ToJson());

        }
    }
}
