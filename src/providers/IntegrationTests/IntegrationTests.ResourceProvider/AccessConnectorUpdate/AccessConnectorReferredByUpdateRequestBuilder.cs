﻿//------------------------------------------------------------
// Copyright (c) Microsoft Corporation.  All rights reserved.
//------------------------------------------------------------

namespace IntegrationTests.ResourceProvider.AccessConnectorUpdate
{
    using IntegrationTests.ResourceProvider.BaseClasses;
    using RestSharp;

    public class AccessConnectorReferredByListWsAddRequestBuilder : BaseRequestBuilder
    {
        public override RestRequest Update(RestRequest request)
        {
            var requestBody =
            "{" +
                "\"workspaceId\": \"/subscriptions/7a9974b4-6ef3-490d-a02d-b9b3a07332f6/resourceGroups/RG_Navya_080823/providers/Microsoft.Databricks/workspaces/plainws\"," +
                "\"operation\": \"Add\"" +
            "}";

            request.AddStringBody(requestBody, DataFormat.Json);
            return request;
        }
    }

    public class AccessConnectorReferredByListWsAddDuplicateWSRequestBuilder : BaseRequestBuilder
    {
        public override RestRequest Update(RestRequest request)
        {
            var requestBody =
            "{" +
                "\"workspaceId\": \"testws\"," +
                "\"operation\": \"Add\"" +
            "}";

            request.AddStringBody(requestBody, DataFormat.Json);
            return request;
        }
    }

    public class AccessConnectorReferredByListWsRemoveRequestBuilder : BaseRequestBuilder
    {
        public override RestRequest Update(RestRequest request)
        {
            var requestBody =
            "{" +
                "\"workspaceId\": \"/subscriptions/7a9974b4-6ef3-490d-a02d-b9b3a07332f6/resourceGroups/RG_Navya_080823/providers/Microsoft.Databricks/workspaces/plainws\"," +
                "\"operation\": \"Remove\"" +
            "}";

            request.AddStringBody(requestBody, DataFormat.Json);
            return request;
        }
    }

    public class AccessConnectorReferredByListWsRemoveAllRequestBuilder : BaseRequestBuilder
    {
        public override RestRequest Update(RestRequest request)
        {
            var requestBody =
            "{" +
                "\"workspaceId\": \"testws\"," +
                "\"operation\": \"Remove\"" +
            "}";

            request.AddStringBody(requestBody, DataFormat.Json);
            return request;
        }
    }
}
