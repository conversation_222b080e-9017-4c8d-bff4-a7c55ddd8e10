﻿//------------------------------------------------------------
// Copyright (c) Microsoft Corporation.  All rights reserved.
//------------------------------------------------------------

namespace IntegrationTests.ResourceProvider.AccessConnectorUpdate
{
    using System.Net;
    using System.Threading.Tasks;
    using IntegrationTests.ResourceProvider.BaseClasses;
    using Microsoft.VisualStudio.TestTools.UnitTesting;
    using RestSharp;

    [TestClass]
    public class AccessConnectorReferredByUpdateTest : BaseIntegrationTest<AccessConnectorReferredByUpdateFrontDoorBuilder, AccessConnectorReferredByUpdateDatabaseSeeder
        , BaseCustomSettings>
    {
        [TestMethod]
        public async Task TestAccessConnectorReferredByListwsADD()
        {
            using (var rp = KestrelHost.Start(Constants.EndPoints.RPHost))
            {
                var patchClient = new RestClient(Constants.RequestMetadata.AccessConnectorUpdateUrl);
                var patchRequest = new AccessConnectorReferredByListWsAddRequestBuilder().Build();
                var patchResponse = await patchClient.ExecutePostAsync(patchRequest);

                Assert.IsNotNull(patchResponse);
                Assert.AreEqual(HttpStatusCode.OK, patchResponse.StatusCode);
            }
        }

        //Add a existing ws
        [TestMethod]
        public async Task TestAccessConnectorReferredByListADDExistingWS()
        {
            using (var rp = KestrelHost.Start(Constants.EndPoints.RPHost))
            {
                var patchClient = new RestClient(Constants.RequestMetadata.AccessConnectorUpdateUrl);
                var patchRequest = new AccessConnectorReferredByListWsAddDuplicateWSRequestBuilder().Build();
                var patchResponse = await patchClient.ExecutePostAsync(patchRequest);

                Assert.IsNotNull(patchResponse);
                Assert.AreEqual(HttpStatusCode.OK, patchResponse.StatusCode);
            }
        }

        [TestMethod]
        public async Task TestAccessConnectorReferredByListwsRemove()
        {
            using (var rp = KestrelHost.Start(Constants.EndPoints.RPHost))
            {
                var patchClient = new RestClient(Constants.RequestMetadata.AccessConnectorUpdateUrl);
                var patchRequest = new AccessConnectorReferredByListWsRemoveRequestBuilder().Build();
                var patchResponse = await patchClient.ExecutePostAsync(patchRequest);

                Assert.AreEqual(HttpStatusCode.OK, patchResponse.StatusCode);
            }
        }

        [TestMethod]
        public async Task TestAccessConnectorReferredByListRemoveNonExistingWS()
        {
            using (var rp = KestrelHost.Start(Constants.EndPoints.RPHost))
            {
                var patchClient = new RestClient(Constants.RequestMetadata.AccessConnectorUpdateUrl);
                var patchRequest = new AccessConnectorReferredByListWsRemoveAllRequestBuilder().Build();
                var patchResponse = await patchClient.ExecutePostAsync(patchRequest);

                Assert.AreEqual(HttpStatusCode.OK, patchResponse.StatusCode);
            }
        }

    }

    [TestClass]
    public class AccessConnectorReferredByEmptyListUpdateTest : BaseIntegrationTest<AccessConnectorReferredByUpdateFrontDoorBuilder, AccessConnectorReferredByemptyListUpdateDatabaseSeeder
        , BaseCustomSettings>
    {

        [TestMethod]
        public async Task TestAccessConnectorReferredByListADDWsToemptyList()
        {
            using (var rp = KestrelHost.Start(Constants.EndPoints.RPHost))
            {
                var patchClient = new RestClient(Constants.RequestMetadata.AccessConnectorUpdateUrl);
                var patchRequest = new AccessConnectorReferredByListWsAddRequestBuilder().Build();
                var patchResponse = await patchClient.ExecutePostAsync(patchRequest);

                Assert.AreEqual(HttpStatusCode.OK, patchResponse.StatusCode);
            }
        }
    }

    [TestClass]
    public class NonExistingAccessConnectorReferredByUpdateTest : BaseIntegrationTest<AccessConnectorReferredByUpdateFrontDoorBuilder, NonExistingAccessConnectorReferredByUpdateDatabaseSeeder
        , BaseCustomSettings>
    {

        [TestMethod]
        public async Task TestnonExistingAccessConnectorReferredByListADDWs()
        {
            using (var rp = KestrelHost.Start(Constants.EndPoints.RPHost))
            {
                var patchClient = new RestClient(Constants.RequestMetadata.AccessConnectorUpdateUrl);
                var patchRequest = new AccessConnectorReferredByListWsAddRequestBuilder().Build();
                var patchResponse = await patchClient.ExecutePostAsync(patchRequest);

                Assert.AreEqual(HttpStatusCode.NotFound, patchResponse.StatusCode);
            }
        }
    }


}
