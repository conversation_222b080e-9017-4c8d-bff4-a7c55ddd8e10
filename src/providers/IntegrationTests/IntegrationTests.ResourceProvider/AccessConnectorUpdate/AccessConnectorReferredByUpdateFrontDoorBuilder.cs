﻿//------------------------------------------------------------
// Copyright (c) Microsoft Corporation.  All rights reserved.
//------------------------------------------------------------

namespace IntegrationTests.ResourceProvider.AccessConnectorUpdate
{
    using IntegrationTests.ResourceProvider.BaseClasses;
    using WireMock.RequestBuilders;
    using WireMock.ResponseBuilders;
    using WireMock.Server;

    public class AccessConnectorReferredByUpdateFrontDoorBuilder : IWireMockServerBuilder
    {
        public WireMockServer Build()
        {
            var frontDoor = WireMockServer.StartWithAdminInterface(Constants.EndPoints.FrontDoorPort);

            frontDoor.AllowPartialMapping(false);

            var accessConnectorUpdateUrl = @"/subscriptions/12345678-abcd-abcd-abcd-ba9876543b10/resourceGroups/testrg/providers/Microsoft.Databricks/accessConnectors/testaccessconnector/updateReferredByListForConnector";

            frontDoor.Given(
                Request.Create()
                .WithPath(accessConnectorUpdateUrl)
                .UsingPost()
            ).RespondWith(
                Response.Create()
                .WithHeader("Content-Type", "application/json")
                .WithStatusCode(200)
                .WithBody(""));

            return frontDoor;
        }
    }
}
