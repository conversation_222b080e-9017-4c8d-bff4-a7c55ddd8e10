﻿//------------------------------------------------------------
// Copyright (c) Microsoft Corporation.  All rights reserved.
//------------------------------------------------------------

namespace IntegrationTests.ResourceProvider.WorkspaceCreate.CreateNoAzureServiceRulesWorkspace
{
    using System.Diagnostics;
    using System.Net;
    using System.Threading.Tasks;
    using IntegrationTests.ResourceProvider.BaseClasses;
    using IntegrationTests.ResourceProvider.WorkspaceCreate.RequestBuilder.CreateNoAzureServiceRulesWorkspace;
    using Microsoft.VisualStudio.TestTools.UnitTesting;
    using RestSharp;

    [TestClass]
    public class NoAzureServiceRulesWorkspaceCreateTests : BaseIntegrationTest<NoAzureServiceRulesWorkspaceFrontDoorBuilder, BaseDatabaseSeeder, BaseCustomSettings>
    {
        /// <summary>
        /// [Validation Error] Attempt to create NoAzureServiceRule workspace in an unsupported region
        /// </summary>
        [TestMethod]
        public async Task CreateNoAzureServiceRulesWorkspaceRegionNotValidError()
        {
            using (var rp = KestrelHost.Start(Constants.EndPoints.RPHost))
            {
                var putClient = new RestClient(Constants.RequestMetadata.RequestUrl);
                var putRequest = new NoAzureServiceRulesWorkspaceRequestBuilder().Build();
                var putResponse = await putClient.ExecutePutAsync(putRequest);
                Assert.IsNotNull(putResponse);
                Assert.AreEqual(HttpStatusCode.BadRequest, putResponse.StatusCode);
                var putResponseContent = putResponse.Content.ToString();
                Assert.IsTrue(putResponseContent.Contains(ErrorResponseCode.NoAzureServiceRuleNotSupportedInRegion.ToString()));
            }
        }
    }
}