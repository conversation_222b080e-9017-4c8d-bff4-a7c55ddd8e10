﻿//------------------------------------------------------------
// Copyright (c) Microsoft Corporation.  All rights reserved.
//------------------------------------------------------------

namespace IntegrationTests.ResourceProvider.WorkspaceCreate.RequestBuilder.CreateNoAzureServiceRulesWorkspace
{
    using IntegrationTests.ResourceProvider.BaseClasses;
    using RestSharp;

    public class NoAzureServiceRulesWorkspaceRequestBuilder : BaseRequestBuilder
    {
        public override RestRequest Update(RestRequest request)
        {
            var requestBody = ApplianceTestHelper.GetAppliancePutContentPrivateLinkWorkspace();
            requestBody = requestBody
                .Replace("\"publicNetworkAccess\":\"Enabled\"", "\"publicNetworkAccess\":\"Disabled\"")
                .Replace("\"requiredNsgRules\":\"AllRules\"", "\"requiredNsgRules\":\"NoAzureServiceRules\"");
            request.AddStringBody(requestBody, DataFormat.Json);

            return request;
        }
    }
}
