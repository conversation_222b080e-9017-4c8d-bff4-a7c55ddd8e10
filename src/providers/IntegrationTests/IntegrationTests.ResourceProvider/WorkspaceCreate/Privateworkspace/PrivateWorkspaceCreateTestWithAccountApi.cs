﻿//------------------------------------------------------------
// Copyright (c) Microsoft Corporation.  All rights reserved.
//------------------------------------------------------------

namespace IntegrationTests.ResourceProvider.WorkspaceCreate.Privateworkspace
{
    using System.Net;
    using System.Threading.Tasks;
    using IntegrationTests.ResourceProvider.BaseClasses;
    using Microsoft.Owin.Hosting;
    using Microsoft.VisualStudio.TestTools.UnitTesting;
    using RestSharp;

    [TestClass]
    public class PrivateWorkspaceCreateTestWithAccountApi : BaseIntegrationTest<PrivateWorkspaceAsyncFrontDoorBuilder, BaseDatabaseSeeder, BaseCustomSettings>
    {
        /// <summary>
        /// [Validation Error] Attempt workspace creation with publicNetworkAccess property in non NPIP workspace
        /// </summary>
        [TestMethod]
        public async Task PrivateWorkspace_PublicNetworkAccess_NonNPIPWorkspace_ValidationError()
        {
            using (var rp = KestrelHost.Start(Constants.EndPoints.RPHost))
            {
                var putClient = new RestClient(Constants.RequestMetadata.RequestUrl);
                var putRequest = new PrivateWorkspaceCreateRequestBuilder().Build();
                var putResponse = await putClient.ExecutePutAsync(putRequest);
                Assert.IsNotNull(putResponse);
                Assert.AreEqual(HttpStatusCode.BadRequest, putResponse.StatusCode);
                var putResponseContent = putResponse.Content.ToString();
                Assert.IsTrue(putResponseContent.Contains(ErrorResponseCode.PrivateWorkspaceNotSupportedForPublicIP.ToString()));
            }
        }

        /// <summary>
        /// [Validation Error] Attempt workspace creation with publicNetworkAccess property in non VNet injected workspace
        /// </summary>
        [TestMethod]
        public async Task PrivateWorkspace_PublicNetworkAccess_NonVNetInjectedWorkspace_ValidationError()
        {
            using (var rp = KestrelHost.Start(Constants.EndPoints.RPHost))
            {
                var putClient = new RestClient(Constants.RequestMetadata.RequestUrl);
                var putRequest = new PrivateWorkspaceWithPublicNetworkDisabledNonVnetRequestBuilder().Build();
                var putResponse = await putClient.ExecutePutAsync(putRequest);
                Assert.IsNotNull(putResponse);
                Assert.AreEqual(HttpStatusCode.BadRequest, putResponse.StatusCode);
                var putResponseContent = putResponse.Content.ToString();
                Assert.IsTrue(putResponseContent.Contains(ErrorResponseCode.InvalidPrivateWorkspaceForNonVNetInjectedWorkspace.ToString()));
            }
        }

        /// <summary>
        /// [Validation Error] Attempt workspace creation with <c>requiredNsgRules</c> property in non VNet injected workspace
        /// </summary>
        [TestMethod]
        public async Task PrivateWorkspace_RequiredNsgRules_NonVNetInjectedWorkspace_ValidationError()
        {
            using (var rp = KestrelHost.Start(Constants.EndPoints.RPHost))
            {
                var putClient = new RestClient(Constants.RequestMetadata.RequestUrl);
                var putRequest = new PrivateWorkspaceWithRequiredNsgRulesInNonVnetWorkspaceRequestBuilder().Build();
                var putResponse = await putClient.ExecutePutAsync(putRequest);
                Assert.IsNotNull(putResponse);
                Assert.AreEqual(HttpStatusCode.BadRequest, putResponse.StatusCode);
                var putResponseContent = putResponse.Content.ToString();
                Assert.IsTrue(putResponseContent.Contains(ErrorResponseCode.RequiredNsgRuleNotSupportedForNonVNetInjectedWorkspace.ToString()));
            }
        }

        /// <summary>
        /// [Validation Error] Attempt workspace creation with <c>requiredNsgRules</c> property in not NPIP workspace
        /// </summary>
        [TestMethod]
        public async Task PrivateWorkspace_RequiredNsgRules_NotNPIPWorkspace_ValidationError()
        {
            using (var rp = KestrelHost.Start(Constants.EndPoints.RPHost))
            {
                var putClient = new RestClient(Constants.RequestMetadata.RequestUrl);
                var putRequest = new PrivateWorkspaceWithRequiredNsgRulesInNotNPIPWorkspaceRequestBuilder().Build();
                var putResponse = await putClient.ExecutePutAsync(putRequest);
                Assert.IsNotNull(putResponse);
                Assert.AreEqual(HttpStatusCode.BadRequest, putResponse.StatusCode);
                var putResponseContent = putResponse.Content.ToString();
                Assert.IsTrue(putResponseContent.Contains(ErrorResponseCode.PrivateWorkspaceNotSupportedForPublicIP.ToString()));
            }
        }

        /// <summary>
        /// [Validation Error] Attempt to update <c>requiredNsgRules</c> property
        /// </summary>
        [TestMethod]
        public async Task PrivateWorkspace_RequiredNsgRules_NotAllowedSubscription_NoAzureServiceRules_ValidationError()
        {
            using (var rp = KestrelHost.Start(Constants.EndPoints.RPHost))
            {
                var putClient = new RestClient(Constants.RequestMetadata.RequestUrl);
                var putRequest = new PrivateWorkspaceWithRequiredNsgRulesNoAzureServiceRuleNotSupportedForSubscription().Build();
                var putResponse = await putClient.ExecutePutAsync(putRequest);
                Assert.IsNotNull(putResponse);
                Assert.AreEqual(HttpStatusCode.BadRequest, putResponse.StatusCode);
                var putResponseContent = putResponse.Content.ToString();
                Assert.IsTrue(putResponseContent.Contains(ErrorResponseCode.NoAzureServiceRuleNotSupportedForSubscription.ToString()));
            }
        }

        /// <summary>
        /// [Success] Create Private Workspace with publicNetworkAccess property
        /// </summary>
        [TestMethod]
        [Ignore("Temporarily disabled as this test needs to be updated.")]
        public async Task PrivateWorkspace_PublicNetworkAccess_Success()
        {
            using (var rp = KestrelHost.Start(Constants.EndPoints.RPHost))
            {
                var putClient = new RestClient(Constants.RequestMetadata.RequestUrl);
                var putRequest = new PrivateWorkspaceWithPublicNetworkDisabledRequestBuilder().Build();
                var putResponse = await putClient.PutAsync(putRequest);
                Assert.IsNotNull(putResponse);
                Assert.AreEqual(HttpStatusCode.Created, putResponse.StatusCode);

                await ApplianceTestHelper.WaitForCompletionAndVerifyResult(putResponse, ProvisioningState.Succeeded, string.Empty);
            }
        }

        /// <summary>
        /// [Success] Create Private Workspace with <c>requiredNsgRules</c> property
        /// </summary>
        [TestMethod]
        [Ignore("Temporarily disabled as this test needs to be updated.")]
        public async Task PrivateWorkspace_RequiredNsgRules_Success()
        {
            using (var rp = KestrelHost.Start(Constants.EndPoints.RPHost))
            {
                var putClient = new RestClient(Constants.RequestMetadata.RequestUrl);
                var putRequest = new PrivateWorkspaceWithRequiredNsgRulesRequestBuilder().Build();
                var putResponse = await putClient.PutAsync(putRequest);
                Assert.IsNotNull(putResponse);
                Assert.AreEqual(HttpStatusCode.Created, putResponse.StatusCode);

                await ApplianceTestHelper.WaitForCompletionAndVerifyResult(putResponse, ProvisioningState.Succeeded, string.Empty);
            }
        }
    }
}