﻿//------------------------------------------------------------
// Copyright (c) Microsoft Corporation.  All rights reserved.
//------------------------------------------------------------

namespace IntegrationTests.ResourceProvider.WorkspaceCreate.Privateworkspace
{
    using IntegrationTests.ResourceProvider.BaseClasses;
    using RestSharp;

    public class PrivateWorkspaceWithRequiredNsgRulesInNotNPIPWorkspaceRequestBuilder : BaseRequestBuilder
    {
        public override RestRequest Update(RestRequest request)
        {
            var requestBody = ApplianceTestHelper.GetAppliancePutContentPrivateLinkWorkspace();
            requestBody = requestBody
                .Replace("\"requiredNsgRules\":\"AllRules\"", "\"requiredNsgRules\":\"NoAzureDatabricksRules\"")
                .Replace("\"publicNetworkAccess\":\"Enabled\"", "\"publicNetworkAccess\":\"Disabled\"")
                .Replace("\"enableNoPublicIp\":{\"value\":true}}", "\"enableNoPublicIp\":{\"value\":false}}");
            request.AddStringBody(requestBody, DataFormat.Json);
            return request;
        }
    }
}
