﻿//------------------------------------------------------------
// Copyright (c) Microsoft Corporation.  All rights reserved.
//------------------------------------------------------------

namespace IntegrationTests.ResourceProvider.WorkspaceCreate.Privateworkspace
{
    using IntegrationTests.ResourceProvider.BaseClasses;
    using RestSharp;

    public class PrivateWorkspaceWithRequiredNsgRulesRequestBuilder : BaseRequestBuilder
    {
        public override RestRequest Update(RestRequest request)
        {
            var requestBody = ApplianceTestHelper.GetAppliancePutContentPrivateLinkWorkspace();
            requestBody = requestBody.
                Replace("\"publicNetworkAccess\":\"Enabled\"", "\"publicNetworkAccess\":\"Disabled\"").
                Replace("\"testPrivateSubnetName\"", "\"private-subnet\"").
                Replace("\"testPublicSubnetName\"", "\"public-subnet\"").
                Replace("\"requiredNsgRules\":\"AllRules\"", "\"requiredNsgRules\":\"NoAzureDatabricksRules\"");
            request.AddStringBody(requestBody, DataFormat.Json);
            return request;
        }
    }
}
