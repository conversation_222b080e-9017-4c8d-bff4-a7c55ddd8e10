﻿//------------------------------------------------------------
// Copyright (c) Microsoft Corporation.  All rights reserved.
//------------------------------------------------------------

namespace IntegrationTests.ResourceProvider.WorkspaceCreate.Privateworkspace
{
    using IntegrationTests.ResourceProvider.BaseClasses;
    using RestSharp;

    public class PrivateWorkspaceWithPublicNetworkDisabledNonVnetRequestBuilder : BaseRequestBuilder
    {
        public override RestRequest Update(RestRequest request)
        {
            var requestBody = ApplianceTestHelper.GetAppliancePutContentPrivateLinkWorkspace_VNETError();
            requestBody = requestBody
                .Replace("\"publicNetworkAccess\":\"Enabled\"", "\"publicNetworkAccess\":\"Disabled\"");
            request.AddStringBody(requestBody, DataFormat.Json);
            return request;
        }
    }
}
