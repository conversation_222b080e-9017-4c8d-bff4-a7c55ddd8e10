﻿//------------------------------------------------------------
// Copyright (c) Microsoft Corporation.  All rights reserved.
//------------------------------------------------------------

namespace IntegrationTests.ResourceProvider.WorkspaceCreate.Privateworkspace
{
    using IntegrationTests.ResourceProvider.BaseClasses;
    using RestSharp;

    public class PrivateWorkspaceWithPublicNetworkDisabledRequestBuilder : BaseRequestBuilder
    {
        public override RestRequest Update(RestRequest request)
        {
            var requestBody = ApplianceTestHelper.GetAppliancePutContentPrivateLinkWorkspace();
            requestBody = requestBody.
                Replace("\"requiredNsgRules\":\"AllRules\"", "\"requiredNsgRules\":\"NoAzureDatabricksRules\"").
                Replace("\"testPrivateSubnetName\"", "\"private-subnet\"").
                Replace("\"testPublicSubnetName\"", "\"public-subnet\"").
                <PERSON>lace("\"publicNetworkAccess\":\"Enabled\"", "\"publicNetworkAccess\":\"Disabled\"");
            request.AddStringBody(requestBody, DataFormat.Json);
            return request;
        }
    }
}
