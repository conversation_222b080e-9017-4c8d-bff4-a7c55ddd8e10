﻿namespace IntegrationTests.ResourceProvider.WorkspaceCreate.CreateWorkspaceFailsByMRGUsedByOtherWorkspace.FrontDoorBuilder
{
    using IntegrationTests.ResourceProvider.BaseClasses;
    using IntegrationTests.ResourceProvider.WorkspaceCreate.BasicWorkspace;
    using WireMock.RequestBuilders;
    using WireMock.ResponseBuilders;
    using WireMock.Server;

    public class CreateWorkspaceFailsIfMrgUsedByOtherWorkspaceAsyncFrontDoorBuilder : IWireMockServerBuilder
    {
        public WireMockServer Build()
        {
            var frontDoor = WireMockServer.StartWithAdminInterface(Constants.EndPoints.FrontDoorPort);

            frontDoor.AllowPartialMapping(false);

            BasicWorkspaceTestHelper.SetupAccountApiFeature(frontDoor);

            var EnableManagedByTenantApiVersionFeatureResponse =
                "{" +
                    "\"properties\": { \"state\": \"Registered\" }, " +
                    "\"id\": \"/subscriptions/********-abcd-abcd-abcd-ba9876543b10/providers/Microsoft.Features/providers/Microsoft.Databricks/features/EnableManagedByTenantApiVersion20190301\"," +
                    "\"type\": \"Microsoft.Features/providers/features\"," +
                    "\"name\": \"Microsoft.Databricks/EnableManagedByTenantApiVersion20190301\"" +
                "}";

            frontDoor.Given(
                Request.Create()
                .WithPath(Constants.FrontDoor.EnableManagedByTenantApiVersionFeatureUrl)
                .UsingGet()
            ).RespondWith(
                Response.Create()
                .WithHeader("Content-Type", "application/json")
                .WithStatusCode(200)
                .WithBody(EnableManagedByTenantApiVersionFeatureResponse));

            var registerTenantUrl = $@"/subscriptions/{Constants.RequestMetadata.SubscriptionId}/providers/Microsoft.Resources/tenants/{Constants.Settings.PublisherTenantId}/register";

            frontDoor.Given(
                Request.Create()
                .WithPath(registerTenantUrl)
                .UsingPost()
            ).RespondWith(
                Response.Create()
                .WithHeader("Content-Type", "application/json")
                .WithStatusCode(200)
                .WithBody(""));

            var existingMRGbody = $@"{{
                  ""id"": ""/subscriptions/{Constants.RequestMetadata.SubscriptionId}/resourceGroups/{Constants.RequestBody.ManagedResourceGroupName}"",
                  ""name"": ""{Constants.RequestBody.ManagedResourceGroupName}"",
                  ""location"": ""{Constants.RequestBody.Location}"",
                  ""managedBy"": ""/subscriptions/{Constants.RequestMetadata.SubscriptionId}/resourceGroups/{Constants.RequestMetadata.ResourceGroup}/providers/Microsoft.Databricks/workspaces/testws2"",
                  ""properties"": {{
                      ""provisioningState"": ""Succeeded""
                                  }}
             }}";

            frontDoor.Given(
                    Request.Create()
                    .WithUrl(Constants.RefreshPermissionRequestMetadata.CreateMRGUrl)
                    .UsingGet()
                ).RespondWith(
                    Response.Create()
                    .WithHeader("Content-Type", "application/json")
                    .WithStatusCode(200)
            .WithBody(existingMRGbody));
             
            return frontDoor;
        }
    }
}
