﻿namespace IntegrationTests.ResourceProvider.WorkspaceCreate.CreateWorkspaceFailsApplianceManagedResourceGroupManagedByMismatch
{
    using System.Threading.Tasks;
    using IntegrationTests.ResourceProvider.BaseClasses;
    using IntegrationTests.ResourceProvider.WorkspaceCreate.CreateWorkspaceFailsByMRGUsedByOtherWorkspace.FrontDoorBuilder;
    using Microsoft.VisualStudio.TestTools.UnitTesting;
    using RestSharp;

    [TestClass]
    public class CreateWorkspaceFailsIfMrgUsedByOtherWorkspaceTest : BaseIntegrationTest<CreateWorkspaceFailsIfMrgUsedByOtherWorkspaceFrontDoorBuilder, BaseDatabaseSeeder, BaseCustomSettings>
    {
        [TestMethod]
        public async Task CreateWorkspaceFailsIfMrgUsedByOtherWorkspace()
        {

            using (var rp = KestrelHost.Start(Constants.EndPoints.RPHost))
            {
                var putClient = new RestClient(Constants.RequestMetadata.RequestUrl);
                var putRequest = new CreateWorkspaceFailsIfMrgUsedByOtherWorkspaceRequestBuilder().Build();
                var putResponse = await putClient.ExecutePutAsync(putRequest);
                Assert.IsNotNull(putResponse);
                await ApplianceTestHelper.WaitForCompletionAndVerifyResult(putResponse, ProvisioningState.Failed, ErrorResponseCode.ApplianceProvisioningFailed.ToString());
            }
        }
    }
}
