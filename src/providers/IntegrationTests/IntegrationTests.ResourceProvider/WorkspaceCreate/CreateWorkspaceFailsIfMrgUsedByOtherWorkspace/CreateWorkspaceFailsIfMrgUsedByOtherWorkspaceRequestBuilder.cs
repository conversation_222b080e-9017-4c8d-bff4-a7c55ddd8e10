﻿namespace IntegrationTests.ResourceProvider.WorkspaceCreate.CreateWorkspaceFailsApplianceManagedResourceGroupManagedByMismatch
{
    using IntegrationTests.ResourceProvider.BaseClasses;
    using IntegrationTests.ResourceProvider.WorkspaceCreate.BasicWorkspace;
    using RestSharp;

    public class CreateWorkspaceFailsIfMrgUsedByOtherWorkspaceRequestBuilder : BaseRequestBuilder
    {
        public override RestRequest Update(RestRequest request)
        {
            var requestBody = BasicWorkspaceTestHelper.GetWorkspacecontent();
            request.AddStringBody(requestBody, DataFormat.Json);
            return request;
        }
    }
}
