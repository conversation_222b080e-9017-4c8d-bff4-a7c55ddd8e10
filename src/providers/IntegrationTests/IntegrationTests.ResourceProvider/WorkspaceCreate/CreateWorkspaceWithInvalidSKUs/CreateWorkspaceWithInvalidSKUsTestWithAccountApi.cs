﻿
namespace IntegrationTests.ResourceProvider.WorkspaceCreate.CreateWorkspaceWithInvalidSKUs
{
    using System;
    using System.Net;
    using System.Threading.Tasks;
    using IntegrationTests.ResourceProvider.BaseClasses;
    using IntegrationTests.ResourceProvider.WorkspaceCreate.BasicWorkspace;
    using IntegrationTests.ResourceProvider.WorkspaceCreate.CreateWorkspaceWithInvalidSKUs.RequestBuilders;
    using Microsoft.Owin.Hosting;
    using Microsoft.VisualStudio.TestTools.UnitTesting;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Common.Constants;
    using RestSharp;

    [TestClass]
    public class CreateWorkspaceWithInvalidSKUsTestWithAccountApi : BaseIntegrationTest<BasicWorkspaceCreateAsyncFrontDoorBuilder, BaseDatabaseSeeder, BaseCustomSettings>
    {
        [TestMethod]
        public async Task CreateWorkspaceWithEmptySKU()
        {
            using (var rp = KestrelHost.Start(Constants.EndPoints.RPHost))
            {
                var putClient = new RestClient(Constants.RequestMetadata.RequestUrl);
                var putRequest = new CreateWorkspaceWithEmptySKURequestBuilder().Build();
                #region  Create workspaces with empty SKU name
                var putResponse = await putClient.ExecutePutAsync(putRequest);
                Assert.IsNotNull(putResponse);
                var putResponseContent = putResponse.Content.ToString();            
                Assert.AreEqual(HttpStatusCode.BadRequest, putResponse.StatusCode);
                Assert.IsTrue(putResponseContent.Contains(ErrorResponseCode.InvalidDatabricksSku.ToString()));
                #endregion

            }
        }
        [TestMethod]
        public async Task CreateWorkspaceWithInvalidSKU()
        {
            using (var rp = KestrelHost.Start(Constants.EndPoints.RPHost))
            {
                var putClient = new RestClient(Constants.RequestMetadata.RequestUrl);
                var putRequest = new CreateWorkspaceWithInvalidSKURequestBuilder().Build();
                #region  Create workspaces with invalid SKU name
                var putResponse = await putClient.ExecutePutAsync(putRequest);
                Assert.IsNotNull(putResponse);
                var putResponseContent = putResponse.Content.ToString();
                Assert.AreEqual(HttpStatusCode.BadRequest, putResponse.StatusCode);
                Assert.IsTrue(putResponseContent.Contains(ErrorResponseCode.InvalidDatabricksSku.ToString()));
                #endregion

            }
        }
        [TestMethod]
        public async Task CreateWorkspaceWithNotSpecifiedSKU()
        {
            using (var rp = KestrelHost.Start(Constants.EndPoints.RPHost))
            {
                var putClient = new RestClient(Constants.RequestMetadata.RequestUrl);
                var putRequest = new CreateWorkspaceWithNotSpecifiedSKURequestBuilder().Build();
                #region  Create workspaces with NotSpecified SKU name
                var putResponse = await putClient.ExecutePutAsync(putRequest);
                Assert.IsNotNull(putResponse);
                var putResponseContent = putResponse.Content.ToString();
                Assert.AreEqual(HttpStatusCode.BadRequest, putResponse.StatusCode);
                Assert.IsTrue(putResponseContent.Contains(ErrorResponseCode.InvalidDatabricksSku.ToString()));
                #endregion

            }
        }
    }
}
