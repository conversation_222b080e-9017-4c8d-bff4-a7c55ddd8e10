﻿
namespace IntegrationTests.ResourceProvider.WorkspaceCreate.CreateWorkspaceWithInvalidSKUs.RequestBuilders
{
    using IntegrationTests.ResourceProvider.BaseClasses;
    using RestSharp;
    public class CreateWorkspaceWithEmptySKURequestBuilder : BaseRequestBuilder
    {
        public override RestRequest Update(RestRequest request)
        {
 
            var requestBody = CreateWorkspaceInvalidSKUTestHelper.GetWorkspaceInvalidSKU();
            requestBody = requestBody
                    .Replace("skuname", "");
            request.AddStringBody(requestBody, DataFormat.Json);
            return request;
        }
    }
}
