﻿

namespace IntegrationTests.ResourceProvider.WorkspaceCreate.CreateWorkspaceWithInvalidSKUs
{
    using IntegrationTests.ResourceProvider.BaseClasses;
    using RestSharp;
    public class CreateWorkspaceWithInvalidSKURequestBuilder : BaseRequestBuilder
    {
        public override RestRequest Update(RestRequest request)
        {
            var requestBody = CreateWorkspaceInvalidSKUTestHelper.GetWorkspaceInvalidSKU();
            requestBody = requestBody
                    .Replace("skuname", "ABC");
            request.AddStringBody(requestBody, DataFormat.Json);
            return request;
        }
    }
}
