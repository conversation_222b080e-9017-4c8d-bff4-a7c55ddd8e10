﻿
namespace IntegrationTests.ResourceProvider.WorkspaceCreate.CreateWorkspaceWithInvalidSKUs.RequestBuilders
{
    using IntegrationTests.ResourceProvider.BaseClasses;
    using RestSharp;
    public class CreateWorkspaceWithNotSpecifiedSKURequestBuilder : BaseRequestBuilder
    {
        public override RestRequest Update(RestRequest request)
        {
            var requestBody = CreateWorkspaceInvalidSKUTestHelper.GetWorkspaceInvalidSKU();
            requestBody = requestBody
                    .Replace("skuname", "NotSpecified");
            request.AddStringBody(requestBody, DataFormat.Json);
            return request;
        }
    }
}
