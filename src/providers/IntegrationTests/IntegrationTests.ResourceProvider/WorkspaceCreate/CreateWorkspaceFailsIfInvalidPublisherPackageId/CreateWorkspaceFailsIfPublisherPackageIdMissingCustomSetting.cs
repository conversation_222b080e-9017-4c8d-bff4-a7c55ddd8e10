﻿namespace IntegrationTests.ResourceProvider.WorkspaceCreate.DatabricksWorkspaceFailsInvalidPublisherPackageIdMappings
{
    using IntegrationTests.ResourceProvider.BaseClasses;
    using System.Collections.Generic;

    public class CreateWorkspaceFailsIfPublisherPackageIdMissingCustomSetting : BaseCustomSettings
    {
        private IDictionary<string, string> Settings;
        public CreateWorkspaceFailsIfPublisherPackageIdMissingCustomSetting()
        {
            Settings = GetSettings();
            Settings["Microsoft.WindowsAzure.ResourceStack.Providers.Databricks.Workspace.PublisherPackageIdMappings"] = string.Empty;
        }
    }
}
