﻿namespace IntegrationTests.ResourceProvider.WorkspaceCreate.DatabricksWorkspaceFailsInvalidPublisherPackageIdMappings
{
    using System.Net;
    using System.Threading.Tasks;
    using IntegrationTests.ResourceProvider.BaseClasses;
    using IntegrationTests.ResourceProvider.WorkspaceCreate.BasicWorkspace;
    using IntegrationTests.ResourceProvider.WorkspaceCreate.DatabricksWorkspaceFailsInvalidPublisherPackageIdMappings.RequestBuilder;
    using Microsoft.VisualStudio.TestTools.UnitTesting;
    using RestSharp;

    [TestClass]
    public class CreateWorkspaceFailsIfInvalidPublisherPackageIdTest : BaseIntegrationTest<BasicWorkspaceCreateFrontDoorBuilder, BaseDatabaseSeeder, CreateWorkspaceFailsIfInvalidPublisherPackageIdCustomSetting>
    {
        [TestMethod]
        public async Task CreateWorkspaceFailsIfInvalidPublisherPackageIdMappings()
        {
            using (var rp = KestrelHost.Start(Constants.EndPoints.RPHost))
            {
                var putClient = new RestClient(Constants.RequestMetadata.RequestUrl);
                var putRequest = new CreateWorkspaceFailsIfInvalidPublisherPackageIdRequestBuilder().Build();
                var putResponse = await putClient.ExecutePutAsync(putRequest);
                Assert.IsNotNull(putResponse);
                var putResponseContent = putResponse.Content.ToString();
                Assert.AreEqual(HttpStatusCode.InternalServerError, putResponse.StatusCode);
                Assert.IsTrue(putResponseContent.Contains(ErrorResponseCode.InvalidPublisherPackageIdMappings.ToString()));
            }
        }
    }
}
