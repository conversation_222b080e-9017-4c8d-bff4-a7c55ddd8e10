﻿namespace IntegrationTests.ResourceProvider.WorkspaceCreate.DatabricksWorkspaceFailsInvalidPublisherPackageIdMappings
{
    using IntegrationTests.ResourceProvider.BaseClasses;
    using System.Collections.Generic;

    public class CreateWorkspaceFailsIfInvalidPublisherPackageIdCustomSetting : BaseCustomSettings
    {
        private IDictionary<string, string> Settings;
        public CreateWorkspaceFailsIfInvalidPublisherPackageIdCustomSetting()
        {
            Settings = GetSettings();
            Settings["Microsoft.WindowsAzure.ResourceStack.Providers.Databricks.Workspace.PublisherPackageIdMappings"] = "2016-09-01-preview#";
        }
    }
}
