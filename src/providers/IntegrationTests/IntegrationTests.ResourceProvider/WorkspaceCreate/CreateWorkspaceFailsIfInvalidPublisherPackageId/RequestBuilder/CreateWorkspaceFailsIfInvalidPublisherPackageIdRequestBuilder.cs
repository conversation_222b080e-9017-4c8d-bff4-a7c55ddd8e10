﻿namespace IntegrationTests.ResourceProvider.WorkspaceCreate.DatabricksWorkspaceFailsInvalidPublisherPackageIdMappings.RequestBuilder
{
    using IntegrationTests.ResourceProvider.BaseClasses;
    using IntegrationTests.ResourceProvider.WorkspaceCreate.BasicWorkspace;
    using RestSharp;
    public class CreateWorkspaceFailsIfInvalidPublisherPackageIdRequestBuilder : BaseRequestBuilder
    {
        public override RestRequest Update(RestRequest request)
        {
            var requestBody = BasicWorkspaceTestHelper.GetWorkspacecontent();
            request.AddStringBody(requestBody, DataFormat.Json);
            return request;
        }
    }
}
