﻿//------------------------------------------------------------
// Copyright (c) Microsoft Corporation.  All rights reserved.
//------------------------------------------------------------

namespace IntegrationTests.ResourceProvider.WorkspaceCreate.NPIPWorkspaceCreateGAAPIVersion.PrivateWorkspace
{
    using IntegrationTests.ResourceProvider.BaseClasses;
    using RestSharp;

    public class PrivateWorkspaceWithNoPublicIp20230201RequestBuilder : BaseRequestBuilder
    {
        public override RestRequest Update(RestRequest request)
        {
            var requestBody = ApplianceTestHelper.GetAppliancePutContentPrivateLinkWorkspace20230201();
            request.AddStringBody(requestBody, DataFormat.Json);

            return request;
        }
    }
}

