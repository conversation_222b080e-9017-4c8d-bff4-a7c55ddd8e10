﻿//------------------------------------------------------------
// Copyright (c) Microsoft Corporation.  All rights reserved.
//------------------------------------------------------------

namespace IntegrationTests.ResourceProvider.WorkspaceCreate.NPIPWorkspaceCreateGAAPIVersion.PrivateWorkspace
{
    using IntegrationTests.ResourceProvider.BaseClasses;
    using RestSharp;

    public class PrivateWorkspaceWithNoPublicIpRequestBuilder : BaseRequestBuilder
    {
        public override RestRequest Update(RestRequest request)
        {
            var requestBody = ApplianceTestHelper.GetAppliancePutContentPrivateLinkWorkspace();
            requestBody = requestBody.
                Replace("\"requiredNsgRules\":\"AllRules\"", "\"requiredNsgRules\":\"NoAzureDatabricksRules\"").
                Replace("\"publicNetworkAccess\":\"Enabled\"", "\"publicNetworkAccess\":\"Disabled\"").
                Remove(525,34);
            request.AddStringBody(requestBody, DataFormat.Json);

            return request;
        }
    }
}

