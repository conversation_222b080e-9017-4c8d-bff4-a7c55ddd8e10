﻿//------------------------------------------------------------
// Copyright (c) Microsoft Corporation.  All rights reserved.
//------------------------------------------------------------

namespace IntegrationTests.ResourceProvider.WorkspaceCreate.NPIPWorkspaceCreateGAAPIVersion.PrivateWorkspace
{
    using IntegrationTests.ResourceProvider.BaseClasses;
    using Microsoft.VisualStudio.TestTools.UnitTesting;
    using Newtonsoft.Json.Linq;
    using RestSharp;
    using System.Linq;
    using System.Net;
    using System.Threading.Tasks;

    [TestClass]
    public class PrivateWorkspaceCreateTest : BaseIntegrationTest<PrivateWorkspaceFrontDoorBuilder, BaseDatabaseSeeder, BaseCustomSettings>
    {
        /// <summary>
        /// [Success] Create Private Workspace with No NPIP property
        /// </summary>
        [TestMethod]
        [Ignore("Temporarily disabled as this test needs to be updated.")]
        public async Task PrivateWorkspace_NoNPIP_Success()
        {
            using (var rp = KestrelHost.Start(Constants.EndPoints.RPHost))
            {
                var putClient = new RestClient(Constants.RequestMetadata.RequestUrl.Replace(Constants.RequestMetadata.RequestUrl, Constants.NPIPRequestMetadata.RequestUrl));
                var putRequest = new PrivateWorkspaceWithNoPublicIpRequestBuilder().Build();
                var putResponse = await putClient.PutAsync(putRequest);
                Assert.IsNotNull(putResponse);
                Assert.AreEqual(HttpStatusCode.Created, putResponse.StatusCode);

                await ApplianceTestHelper.WaitForCompletionAndVerifyResult(putResponse, ProvisioningState.Succeeded, string.Empty);

                // Check the enableNoPublicIp Value is passed during the deployment url
                var deploymentUrlIndex = this.Frontdoor.LogEntries.ToList().FindIndex(x => x.RequestMessage.AbsoluteUrl == Constants.RequireInfrastructureEncryptionMetadata.DeploymentUrl);
                var deploymentBody = this.Frontdoor.LogEntries.ToList()[deploymentUrlIndex].RequestMessage.Body;
                var enableNoPublicIpValue = JObject.Parse(deploymentBody)["properties"]["parameters"]["enableNoPublicIp"]["value"];

                Assert.AreEqual(enableNoPublicIpValue.Value<string>(), "True");
            }
        }

        /// <summary>
        /// [Success] Create Private Workspace with NPIP true property
        /// </summary>
        [TestMethod]
        [Ignore("Temporarily disabled as this test needs to be updated.")]
        public async Task PrivateWorkspace_NPIP_Success()
        {
            using (var rp = KestrelHost.Start(Constants.EndPoints.RPHost))
            {
                var putClient = new RestClient(Constants.RequestMetadata.RequestUrl.Replace(Constants.RequestMetadata.RequestUrl, Constants.NPIPRequestMetadata.RequestUrl));
                var putRequest = new PrivateWorkspaceWithPublicNetworkDisabledRequestBuilder().Build();
                var putResponse = await putClient.PutAsync(putRequest);
                Assert.IsNotNull(putResponse);
                Assert.AreEqual(HttpStatusCode.Created, putResponse.StatusCode);

                await ApplianceTestHelper.WaitForCompletionAndVerifyResult(putResponse, ProvisioningState.Succeeded, string.Empty);

                // Check the enableNoPublicIp Value is passed during the deployment url
                var deploymentUrlIndex = this.Frontdoor.LogEntries.ToList().FindIndex(x => x.RequestMessage.AbsoluteUrl == Constants.RequireInfrastructureEncryptionMetadata.DeploymentUrl);
                var deploymentBody = this.Frontdoor.LogEntries.ToList()[deploymentUrlIndex].RequestMessage.Body;
                var enableNoPublicIpValue = JObject.Parse(deploymentBody)["properties"]["parameters"]["enableNoPublicIp"]["value"];

                Assert.AreEqual(enableNoPublicIpValue.Value<string>(), "True");
            }
        }

        /// <summary>
        /// [Success] Create Private Workspace with NPIP False property 2023-02-01
        /// </summary>
        [TestMethod]
        [Ignore("Temporarily disabled as this test needs to be updated.")]
        public async Task PrivateWorkspace_NoNPIP_20230201_Success()
        {
            using (var rp = KestrelHost.Start(Constants.EndPoints.RPHost))
            {
                var putClient = new RestClient(Constants.RequestMetadata.RequestUrl.Replace(Constants.RequestMetadata.RequestUrl, Constants.NPIPRequestMetadata.RequestUrl20230201));
                var putRequest = new PrivateWorkspaceWithNoPublicIp20230201RequestBuilder().Build();
                var putResponse = await putClient.PutAsync(putRequest);
                Assert.IsNotNull(putResponse);
                Assert.AreEqual(HttpStatusCode.Created, putResponse.StatusCode);

                await ApplianceTestHelper.WaitForCompletionAndVerifyResult(putResponse, ProvisioningState.Succeeded, string.Empty);

                // Check the enableNoPublicIp Value is passed during the deployment url
                var deploymentUrlIndex = this.Frontdoor.LogEntries.ToList().FindIndex(x => x.RequestMessage.AbsoluteUrl == Constants.RequireInfrastructureEncryptionMetadata.DeploymentUrl);
                var deploymentBody = this.Frontdoor.LogEntries.ToList()[deploymentUrlIndex].RequestMessage.Body;
                var enableNoPublicIpValue = JObject.Parse(deploymentBody)["properties"]["parameters"]["enableNoPublicIp"]["value"];

                Assert.AreEqual(enableNoPublicIpValue.Value<string>(), "False");
            }
        }
    }
}