﻿//------------------------------------------------------------
// Copyright (c) Microsoft Corporation.  All rights reserved.
//------------------------------------------------------------

namespace IntegrationTests.ResourceProvider.WorkspaceCreate.NPIPWorkspaceCreateGAAPIVersion.BasicWorkspaceCreateNPIPFalse
{
    using IntegrationTests.ResourceProvider.BaseClasses;
    using RestSharp;

    public class BasicWorkspaceCreateNPIPFalseRequestBuilder : BaseRequestBuilder
    {
        public override RestRequest Update(RestRequest request)
        {
            var requestBody =
                "{" +
                    "\"sku\": { \"name\": \"" + Constants.RequestBody.SkuPremium + "\" }," +
                    "\"location\": \"" + Constants.RequestBody.Location + "\"," +
                    "\"tags\": { }," +
                    "\"properties\": { \"isUcEnabled\": \"" + Constants.RequestBody.IsUCEnabled + "\", \"ManagedResourceGroupId\": \"" + Constants.RequestBody.ManagedResourceGroupId + "\", " +
                    "\"parameters\": { \"enableNoPublicIp\": { \"type\": \" Bool \", \"value\": \"" + Constants.RequestBody.EnablePublicIpFalse + "\" }" + "}" + "}" +
                    "}";

            request.AddStringBody(requestBody, DataFormat.Json);

            return request;
        }
    }
}
