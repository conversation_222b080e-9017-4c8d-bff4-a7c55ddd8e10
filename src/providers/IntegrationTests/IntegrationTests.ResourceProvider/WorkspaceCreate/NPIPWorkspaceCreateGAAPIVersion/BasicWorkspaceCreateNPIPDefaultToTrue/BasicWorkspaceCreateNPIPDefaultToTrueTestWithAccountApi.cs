﻿//------------------------------------------------------------
// Copyright (c) Microsoft Corporation.  All rights reserved.
//------------------------------------------------------------

namespace IntegrationTests.ResourceProvider.WorkspaceCreate.NPIPWorkspaceCreateGAAPIVersion.BasicWorkspaceCreateNPIPDefaultToTrue
{
    using IntegrationTests.ResourceProvider.BaseClasses;
    using IntegrationTests.ResourceProvider.WorkspaceCreate.NPIPWorkspaceCreateGAAPIVersion.BasicWorkspaceNPIPDefaultToTrue;
    using Microsoft.VisualStudio.TestTools.UnitTesting;
    using Newtonsoft.Json.Linq;
    using RestSharp;
    using System.Linq;
    using System.Threading.Tasks;

    [TestClass]
    public class BasicWorkspaceCreateNPIPDefaultToTrueTestWithAccountApi : BaseIntegrationTest<BasicWorkspaceCreateNPIPDefaultToTrueAsyncF<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, BaseDatabaseSeeder, BaseCustomSettings>
    {
        [TestMethod]
        public async Task TestBasicWorkspaceSuccessfulCreation()
        {
            using (var rp = KestrelHost.Start(Constants.EndPoints.RPHost))
            {
                var putClient = new RestClient(Constants.RequestMetadata.RequestUrl.Replace(Constants.RequestMetadata.RequestUrl, Constants.NPIPRequestMetadata.RequestUrl));
                var putRequest = new BasicWorkspaceCreateNPIPDefaultToTrueRequestBuilder().Build();
                var putResponse = await putClient.PutAsync(putRequest);
                Assert.IsNotNull(putResponse);
                Assert.AreEqual(System.Net.HttpStatusCode.Created, putResponse.StatusCode);

                await ApplianceTestHelper.WaitForCompletionAndVerifyResult(putResponse, ProvisioningState.Succeeded, string.Empty);

                // Check the enableNoPublicIp Value is passed during the deployment url
                var deploymentUrlIndex = this.Frontdoor.LogEntries.ToList().FindIndex(x => x.RequestMessage.AbsoluteUrl == Constants.RequireInfrastructureEncryptionMetadata.DeploymentUrl);
                var deploymentBody = this.Frontdoor.LogEntries.ToList()[deploymentUrlIndex].RequestMessage.Body;
                var enableNoPublicIpValue = JObject.Parse(deploymentBody)["properties"]["parameters"]["enableNoPublicIp"]["value"];

                Assert.AreEqual(enableNoPublicIpValue.Value<string>(), "True");
            }
        }
    }
}
