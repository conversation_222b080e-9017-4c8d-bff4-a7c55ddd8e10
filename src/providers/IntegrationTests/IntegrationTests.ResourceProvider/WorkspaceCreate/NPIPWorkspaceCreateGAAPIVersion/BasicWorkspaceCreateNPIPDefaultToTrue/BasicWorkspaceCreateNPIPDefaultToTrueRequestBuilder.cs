﻿//------------------------------------------------------------
// Copyright (c) Microsoft Corporation.  All rights reserved.
//------------------------------------------------------------

namespace IntegrationTests.ResourceProvider.WorkspaceCreate.NPIPWorkspaceCreateGAAPIVersion.BasicWorkspaceCreateNPIPDefaultToTrue
{
    using IntegrationTests.ResourceProvider.BaseClasses;
    using RestSharp;

    public class BasicWorkspaceCreateNPIPDefaultToTrueRequestBuilder : BaseRequestBuilder
    {
        public override RestRequest Update(RestRequest request)
        {
            var requestBody =
                "{" +
                    "\"sku\": { \"name\": \"" + Constants.RequestBody.SkuPremium + "\" }," +
                    "\"location\": \"" + Constants.RequestBody.Location + "\"," +
                    "\"tags\": { }," +
                    "\"properties\": { \"isUcEnabled\": \"" + Constants.RequestBody.IsUCEnabled + "\", \"ManagedResourceGroupId\": \"" + Constants.RequestBody.ManagedResourceGroupId + "\" }" +
                    "}";

            request.AddStringBody(requestBody, DataFormat.Json);

            return request;
        }
    }
}
