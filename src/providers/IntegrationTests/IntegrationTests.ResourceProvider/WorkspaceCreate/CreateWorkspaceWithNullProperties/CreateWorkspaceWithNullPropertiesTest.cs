﻿namespace IntegrationTests.ResourceProvider.WorkspaceCreate.CreateWorkspaceWithNullProperties
{
    using System;
    using System.Net;
    using System.Threading.Tasks;
    using IntegrationTests.ResourceProvider.BaseClasses;
    using IntegrationTests.ResourceProvider.WorkspaceCreate.BasicWorkspace;
    using IntegrationTests.ResourceProvider.WorkspaceCreate.CreateWorkspaceWithInvalidSKUs.RequestBuilders;
    using Microsoft.Owin.Hosting;
    using Microsoft.VisualStudio.TestTools.UnitTesting;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Common.Constants;
    using RestSharp;

    [TestClass]
    public class CreateWorkspaceWithNullPropertiesTest : BaseIntegrationTest<BasicWorkspaceCreateFrontDoorBuilder, BaseDatabaseSeeder, BaseCustomSettings>
    {
        [TestMethod]
        /// <summary>
        /// Create new workspaces with properties as null.
        /// </summary>
        public async Task CreateWorkspaceWithNullProperties()
        {
            using (var rp = KestrelHost.Start(Constants.EndPoints.RPHost))
            {
                var putClient = new RestClient(Constants.RequestMetadata.RequestUrl);
                var putRequest = new CreateWorkspaceWithNullPropertiesRequestBuilder().Build();
                putRequest.Method = Method.Put;
                var putResponse = await putClient.ExecuteAsync(putRequest);
                Assert.IsNotNull(putResponse);
                var putResponseContent = putResponse.Content.ToString();
                Assert.AreEqual(HttpStatusCode.BadRequest, putResponse.StatusCode);
                Assert.IsTrue(putResponseContent.Contains(ErrorResponseCode.InvalidApplicationPropertiesForPut.ToString()));
            }
        }       
    }
}
