﻿namespace IntegrationTests.ResourceProvider.WorkspaceCreate.CreateWorkspaceWithNullProperties
{
    using IntegrationTests.ResourceProvider.BaseClasses;
    using RestSharp;

    public class CreateWorkspaceWithNullPropertiesRequestBuilder : BaseRequestBuilder
    {
        public override RestRequest Update(RestRequest request)
        {
            var requestBody =
                "{" +
                    "\"sku\": { " +
                        "\"name\": \"" + Constants.RequestBody.SkuPremium + "" +
                          "\" }," +
                    "\"location\": \"" + Constants.RequestBody.Location + "\"," +
                "}";
            request.AddStringBody(requestBody, DataFormat.Json);
            return request;
        }
    }
}
