﻿//------------------------------------------------------------
// Copyright (c) Microsoft Corporation.  All rights reserved.
//------------------------------------------------------------

namespace IntegrationTests.ResourceProvider.WorkspaceCreate.CreateWorkspaceWithExistingURL
{
    using IntegrationTests.ResourceProvider.BaseClasses;
    using RestSharp;

    public class CreateWorkspaceWithExistingURLRequestBuilder : BaseRequestBuilder
    {
        public override RestRequest Update(RestRequest request)
        {
            var requestBody =
                "{" +
                    "\"sku\": { \"name\": \"" + Constants.RequestBody.SkuPremium + "\" }," +
                    "\"location\": \"" + Constants.RequestBody.Location + "\"," +
                    "\"tags\": { }," +
                    "\"sku\": { \"name\": \"" + Constants.RequestBody.SkuPremium + "\" }," +
                    "\"properties\": { \"ManagedResourceGroupId\": \"" + Constants.RequestBody.ManagedResourceGroupId + "\", \"parameters\": {\"storageAccountName\": {\"value\": \"" + Constants.RequestBody.StorageAccountName + "\"}} }" +
                    "}";

            request.AddStringBody(requestBody, DataFormat.Json);
            return request;
        }
    }
}