﻿//------------------------------------------------------------
// Copyright (c) Microsoft Corporation.  All rights reserved.
//------------------------------------------------------------

namespace IntegrationTests.ResourceProvider.WorkspaceCreate.CreateWorkspaceWithExistingURL
{
    using System;
    using System.Net;
    using System.Net.Http;
    using System.Net.Http.Headers;
    using global::IntegrationTests.ResourceProvider.BaseClasses;
    using IntegrationTests.ResourceProvider.WorkspaceCreate.BasicWorkspace;
    using Microsoft.WindowsAzure.ResourceStack.Common.Json;
    using WireMock.RequestBuilders;
    using WireMock.ResponseBuilders;
    using WireMock.Server;
    using WireMock.Settings;

    public class CreateWorkspaceWithExistingURLAsyncFrontDoorBuilder : IWireMockServerBuilder
    {
        public WireMockServer Build()
        {
            var options = new WireMockServerSettings();
            options.Port = Constants.EndPoints.FrontDoorPort;
            var frontDoor = WireMockServer.StartWithAdminInterface(Constants.EndPoints.FrontDoorPort);

            frontDoor.AllowPartialMapping(false);

            var nameAvilabilityResponse =
                "{" +
                    "\"nameAvailable\": true" +
                "}";

            frontDoor.Given(
                Request.Create()
                .WithPath(Constants.FrontDoor.DbfsNameAvailability)
                .UsingPost()
                ).RespondWith(
                Response.Create()
                .WithHeader("Content-Type", "application/json")
                .WithStatusCode(200)
                .WithBody(nameAvilabilityResponse));

            BasicWorkspaceTestHelper.SetupAccountApiFeature(frontDoor);

            BasicWorkspaceTestHelper.SetupWorkspaceAccountApiCreateWithPolling(frontDoor);

            var asyncOperationUrl = $@"{Constants.RequestBody.ManagedResourceGroupId}/providers/Microsoft.Resources/deployments/{Constants.RequestMetadata.Name}/operationStatuses/08585353991691056296";

            var message = new HttpResponseMessage(HttpStatusCode.Accepted)
            {
                Headers =
                    {
                        RetryAfter = RetryConditionHeaderValue.Parse("1"),
                        Location = new Uri(Constants.EndPoints.FrontDoorHost + asyncOperationUrl)
                    }
            };

            frontDoor.Given(
                Request.Create().WithPath(Constants.FrontDoor.PreparedVNet1PublicRequestUrl).UsingPost())
                .RespondWith(Response.Create()
                .WithStatusCode(HttpStatusCode.Accepted)
                .WithBody(message.ToJson()));

            var ucDefaultFeatureResponse =
                "{" +
                    "\"properties\": { \"state\": \"Registered\" }, " +
                    "\"id\": \"/subscriptions/59d64684-e7c9-4397-8982-6b775a473b74/providers/Microsoft.Features/providers/Microsoft.Databricks/features/EnableUcDefaultFeature\"," +
                    "\"type\": \"Microsoft.Features/providers/features\"," +
                    "\"name\": \"Microsoft.Databricks/EnableUcDefaultFeature\"" +
                "}";

            frontDoor.Given(
                Request.Create()
                .WithPath(Constants.FrontDoor.UcDefaultFeatureUrl)
                .UsingGet()
            ).RespondWith(
                Response.Create()
                .WithHeader("Content-Type", "application/json")
                .WithStatusCode(200)
                .WithBody(ucDefaultFeatureResponse));

            var EnableManagedByTenantApiVersionFeatureResponse =
                "{" +
                    "\"properties\": { \"state\": \"NotRegistered\" }, " +
                    "\"id\": \"/subscriptions/12345678-abcd-abcd-abcd-ba9876543b10/providers/Microsoft.Features/providers/Microsoft.Databricks/features/EnableManagedByTenantApiVersion20190301\"," +
                    "\"type\": \"Microsoft.Features/providers/features\"," +
                    "\"name\": \"Microsoft.Databricks/EnableManagedByTenantApiVersion20190301\"" +
                "}";

            frontDoor.Given(
                Request.Create()
                .WithPath(Constants.FrontDoor.EnableManagedByTenantApiVersionFeatureUrl)
                .UsingPost()
            ).RespondWith(
                Response.Create()
                .WithHeader("Content-Type", "application/json")
                .WithStatusCode(200)
                .WithBody(EnableManagedByTenantApiVersionFeatureResponse));

            var registerTenantUrl = $@"/subscriptions/{Constants.RequestMetadata.SubscriptionId}/providers/Microsoft.Resources/tenants/{Constants.Settings.PublisherTenantId}/register";

            frontDoor.Given(
                Request.Create()
                .WithPath(registerTenantUrl)
                .UsingPost()
            ).RespondWith(
                Response.Create()
                .WithHeader("Content-Type", "application/json")
                .WithStatusCode(200)
                .WithBody(""));

            frontDoor.Given(
                Request.Create()
                .WithPath(Constants.RequestBody.ManagedResourceGroupId)
                .UsingGet()
            ).RespondWith(
                Response.Create()
                .WithStatusCode(HttpStatusCode.NotFound)
                .WithBody(""));

            frontDoor.Given(
                Request.Create()
                .WithPath(Constants.RequestBody.ManagedResourceGroupId)
                .UsingPut()
            ).RespondWith(
                Response.Create()
                .WithStatusCode(HttpStatusCode.Created)
                .WithBody(""));

            var dbfsRoleAssignentsUrl = $@"/{Constants.RequestBody.ManagedResourceGroupId}/providers/Microsoft.Storage/storageAccounts//providers/Microsoft.Authorization/roleAssignments";

            frontDoor.Given(
                Request.Create()
                .WithPath(dbfsRoleAssignentsUrl)
                .UsingGet()
            ).RespondWith(
                Response.Create()
                .WithHeader("Content-Type", "application/json")
                .WithStatusCode(HttpStatusCode.OK)
                .WithBody("{ \"value\":[] }"));

            var dbfsRoleAssignmentsCreateUrl = $@"{Constants.RequestBody.ManagedResourceGroupId}/providers/Microsoft.Storage/storageAccounts/providers/Microsoft.Authorization/roleAssignments/*";

            frontDoor.Given(
                Request.Create()
                .WithPath(dbfsRoleAssignmentsCreateUrl)
                .UsingPut()
            ).RespondWith(
                Response.Create()
                .WithHeader("Content-Type", "application/json")
                .WithStatusCode(HttpStatusCode.Created)
                .WithBody(""));

            var roleAssignentsUrl = $@"/{Constants.RequestBody.ManagedResourceGroupId}/providers/Microsoft.Authorization/roleAssignments";

            frontDoor.Given(
                Request.Create()
                .WithPath(roleAssignentsUrl)
                .UsingGet()
            ).RespondWith(
                Response.Create()
                .WithHeader("Content-Type", "application/json")
                .WithStatusCode(HttpStatusCode.OK)
                .WithBody("{ \"value\":[] }"));

            var roleAssignmentsCreateUrl = $@"{Constants.RequestBody.ManagedResourceGroupId}/providers/Microsoft.Authorization/roleAssignments/*";

            frontDoor.Given(
                Request.Create()
                .WithPath(roleAssignmentsCreateUrl)
                .UsingPut()
            ).RespondWith(
                Response.Create()
                .WithHeader("Content-Type", "application/json")
                .WithStatusCode(HttpStatusCode.Created)
                .WithBody(""));

            var denyAssignmentsUrl = $@"/{Constants.RequestBody.ManagedResourceGroupId}/providers/Microsoft.Authorization/denyAssignments";

            frontDoor.Given(
                Request.Create()
                .WithPath(denyAssignmentsUrl)
                .UsingGet()
            ).RespondWith(
                Response.Create()
                .WithHeader("Content-Type", "application/json")
                .WithStatusCode(HttpStatusCode.OK)
                .WithBody("{ \"value\":[] }"));

            var denyAssignmentsCreateUrl = $@"{Constants.RequestBody.ManagedResourceGroupId}/providers/Microsoft.Authorization/denyAssignments/*";

            frontDoor.Given(
                Request.Create()
                .WithPath(denyAssignmentsCreateUrl)
                .UsingPut()
            ).RespondWith(
                Response.Create()
                .WithHeader("Content-Type", "application/json")
                .WithStatusCode(HttpStatusCode.Created)
                .WithBody(""));

            var deploymentCreateUrl = $@"{Constants.RequestBody.ManagedResourceGroupId}/providers/Microsoft.Resources/deployments/testws";

            var deploymentResponse =
            "{" +
                "\"id\": \"" + Guid.NewGuid().ToString() + "\"," +
                "\"name\": \"testws\"," +
                "\"properties\":{" +
                    "\"templateLink\":{" +
                        "\"uri\":\"http://localhost\"," +
                        "\"contentVersion\":\"*******\"" +
                    "}," +
                    "\"parameters\":{" +
                        "\"resourceTags\":{" +
                            "\"value\":{" +
                                "\"application\":\"databricks\"," +
                                "\"databricks-environment\":\"true\"" +
                            "}" +
                        "}" +
                    "}," +
                    "\"mode\":\"Complete\"," +
                    "\"securityMode\":\"Secured\"," +
                    "\"outputs\": {" +
                        "\"ucConnectorPrincipalId\": {" +
                            "\"value\": \"" + Guid.NewGuid().ToString() + "\"" +
                        "}," +
                        "\"ucConnectorTenantId\": {" +
                            "\"value\": \"" + Guid.NewGuid().ToString() + "\"" +
                        "}" +
                    "}" +
                "}" +
            "}";

            var deploymentTrackingUrl = $@"{Constants.RequestBody.ManagedResourceGroupId}/providers/Microsoft.Resources/deployments/{Constants.RequestMetadata.Name}/operationStatuses/08585353991691056296";

            frontDoor.Given(
                Request.Create()
                .WithPath(deploymentCreateUrl)
                .UsingPut()
            ).RespondWith(
                Response.Create()
                .WithHeader("Content-Type", "application/json")
                .WithStatusCode(HttpStatusCode.Created)
                .WithHeader("Azure-AsyncOperation", Constants.EndPoints.FrontDoorHost + deploymentTrackingUrl)
                .WithBody(deploymentResponse));

            frontDoor.Given(
                Request.Create()
                .WithPath(deploymentCreateUrl)
                .UsingGet()
            ).RespondWith(
                Response.Create()
                .WithHeader("Content-Type", "application/json")
                .WithStatusCode(HttpStatusCode.OK)
                .WithBody(deploymentResponse));

            frontDoor.Given(
                Request.Create()
                .WithPath(deploymentTrackingUrl)
                .UsingGet()
            ).RespondWith(
                Response.Create()
                .WithHeader("Content-Type", "application/json")
                .WithStatusCode(HttpStatusCode.OK)
                .WithBody("{ \"status\":\"Succeeded\" }"));

            var workspaceInitializationUrl = $@"/{Constants.RequestMetadata.ResourceId}/dbworkspaces/dbworkspace";

            var workspaceDetails =
             "{" +
                "\"WorkspaceId\":\"" + Constants.DBWorkspace.WorkspaceId + "\"," +
                "\"WorkspaceURL\":\"" + Constants.DBWorkspace.WorkspaceUrl + "\"," +
                "\"isPrivateLinkAllowed\":false," +
                "\"isUcEnabled\":false" +
             "}";

            frontDoor.Given(
                Request.Create()
                .WithPath(workspaceInitializationUrl)
                .UsingGet()
            ).RespondWith(
                Response.Create()
                .WithHeader("Content-Type", "application/json")
                .WithStatusCode(HttpStatusCode.OK)
                .WithBody(workspaceDetails));

            frontDoor.Given(
                Request.Create()
                .WithPath(workspaceInitializationUrl)
                .UsingPatch()
            ).RespondWith(
                Response.Create()
                .WithHeader("Content-Type", "application/json")
                .WithStatusCode(HttpStatusCode.OK)
                .WithBody(""));

            return frontDoor;
        }
    }
}