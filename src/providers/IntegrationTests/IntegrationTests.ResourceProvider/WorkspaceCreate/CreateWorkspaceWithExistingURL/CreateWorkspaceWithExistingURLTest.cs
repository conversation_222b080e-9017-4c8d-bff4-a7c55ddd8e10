﻿//------------------------------------------------------------
// Copyright (c) Microsoft Corporation.  All rights reserved.
//------------------------------------------------------------

namespace IntegrationTests.ResourceProvider.WorkspaceCreate.CreateWorkspaceWithExistingURL
{
    using System.Net;
    using System.Threading.Tasks;
    using IntegrationTests.ResourceProvider.BaseClasses;
    using IntegrationTests.ResourceProvider.BasicWorkspaceGet;
    using Microsoft.VisualStudio.TestTools.UnitTesting;
    using Newtonsoft.Json.Linq;
    using RestSharp;

    [TestClass]
    public class CreateWorkspaceWithExistingURLTest : BaseIntegrationTest<CreateWorkspaceWithExistingURLFrontDoorBuilder, BaseDatabaseSeeder, BaseCustomSettings>
    {
        [TestMethod]
        public async Task TestCreateWorkspaceWithExistingURLTest()
        {
            using (var rp = KestrelHost.Start(Constants.EndPoints.RPHost))
            {
                var putClient = new RestClient(Constants.RequestMetadata.RequestUrl);
                var putRequest = new CreateWorkspaceWithExistingURLRequestBuilder().Build();
                var putResponse = await putClient.PutAsync(putRequest);
                Assert.IsNotNull(putResponse);
                Assert.AreEqual(System.Net.HttpStatusCode.Created, putResponse.StatusCode);

                await ApplianceTestHelper.WaitForCompletionAndVerifyResult(putResponse, ProvisioningState.Succeeded, string.Empty);

                // Get the Workspace resource and validate that the workspaceUrl and workspaceId are set properly
                var getClient = new RestClient(Constants.RequestMetadata.RequestUrl);
                var getRequest = new BasicWorkspaceGetRequestBuilder().Build();
                var getResponse = await getClient.GetAsync(getRequest);
                Assert.AreEqual(HttpStatusCode.OK, getResponse.StatusCode);
                JToken workspace = JObject.Parse(getResponse.Content.ToString()).Root;

                Assert.AreEqual(Constants.DBWorkspace.WorkspaceUrl, workspace["properties"]["workspaceUrl"].Value<string>());
                Assert.AreEqual(Constants.DBWorkspace.WorkspaceId, workspace["properties"]["workspaceId"].Value<string>());
            }
        }
    }
}