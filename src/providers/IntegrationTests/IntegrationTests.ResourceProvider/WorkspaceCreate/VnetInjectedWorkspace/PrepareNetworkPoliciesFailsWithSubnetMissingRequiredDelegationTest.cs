﻿//------------------------------------------------------------
// Copyright (c) Microsoft Corporation.  All rights reserved.
//------------------------------------------------------------

namespace IntegrationTests.ResourceProvider.WorkspaceCreate.VnetInjected
{
    using System.Net;
    using System.Threading.Tasks;
    using IntegrationTests.ResourceProvider.BaseClasses;
    using IntegrationTests.ResourceProvider.WorkspaceCreate.VnetInjectedWorkspace.AsyncFrontDoorBuilders;
    using IntegrationTests.ResourceProvider.WorkspaceCreate.VnetInjectedWorkspace.FrontDoorBuilders;
    using IntegrationTests.ResourceProvider.WorkspaceCreate.VnetInjectedWorkspace.RequestBuilders;
    using Microsoft.Owin.Hosting;
    using Microsoft.VisualStudio.TestTools.UnitTesting;
    using RestSharp;

    [TestClass]
    public class PrepareNetworkPoliciesFailsWithSubnetMissingRequiredDelegationFrontDoorTest : BaseIntegrationTest<PrepareNetworkPoliciesFailsWithSubnetMissingRequiredDelegationFrontDoorBuilder, BaseDatabaseSeeder, BaseCustomSettings>
    {
        /// <summary>
        /// Test creation operation which fails with during prepare subnet with subnet missing delegation.
        /// </summary>
        [TestMethod]
        public async Task VNetInjectedDatabricksWorkspaceTests_ApplianceProvisioningJob_Fails_SubnetMissingRequiredDelegation()
        {
            using (var rp = KestrelHost.Start(Constants.EndPoints.RPHost))
            {
                var putClient = new RestClient(Constants.RequestMetadata.RequestUrl);
                var putRequest = new VnetInjectedWorkspaceCreateRequestBuilder().Build();
                var putResponse = await putClient.ExecutePutAsync(putRequest);
                Assert.IsNotNull(putResponse);
                Assert.AreEqual(HttpStatusCode.Created, putResponse.StatusCode);

                await ApplianceTestHelper.WaitForCompletionAndVerifyResult(putResponse, ProvisioningState.Failed, ErrorResponseCode.SubnetMissingRequiredDelegation.ToString());
            }
        }
    }

    [TestClass]
    public class PrepareNetworkPoliciesFailsWithSubnetMissingRequiredDelegationAsyncTests : BaseIntegrationTest<PrepareNetworkPoliciesFailsWithSubnetMissingRequiredDelegationAsyncFrontDoorBuilder, BaseDatabaseSeeder, BaseCustomSettings>
    {
        /// <summary>
        /// Test creation operation which fails with during prepare subnet with subnet missing delegation.
        /// </summary>
        [TestMethod]
        public async Task VNetInjectedDatabricksWorkspaceTests_ApplianceProvisioningJob_Fails_SubnetMissingRequiredDelegation()
        {
            using (var rp = KestrelHost.Start(Constants.EndPoints.RPHost))
            {
                var putClient = new RestClient(Constants.RequestMetadata.RequestUrl);
                var putRequest = new VnetInjectedWorkspaceCreateRequestBuilder().Build();
                var putResponse = await putClient.ExecutePutAsync(putRequest);
                Assert.IsNotNull(putResponse);
                Assert.AreEqual(HttpStatusCode.Created, putResponse.StatusCode);

                await ApplianceTestHelper.WaitForCompletionAndVerifyResult(putResponse, ProvisioningState.Failed, ErrorResponseCode.SubnetMissingRequiredDelegation.ToString());
            }
        }
    }
}