﻿//------------------------------------------------------------
// Copyright (c) Microsoft Corporation.  All rights reserved.
//------------------------------------------------------------

namespace IntegrationTests.ResourceProvider.WorkspaceCreate.VnetInjected
{
    using System.Net;
    using System.Threading.Tasks;
    using global::IntegrationTests.ResourceProvider.BaseClasses;
    using global::IntegrationTests.ResourceProvider.WorkspaceCreate.VnetInjectedWorkspace;
    using IntegrationTests.ResourceProvider.BaseClasses;
    using IntegrationTests.ResourceProvider.BasicWorkspaceGet;
    using IntegrationTests.ResourceProvider.WorkspaceCreate.VnetInjectedWorkspace;
    using IntegrationTests.ResourceProvider.WorkspaceCreate.VnetInjectedWorkspace.DatabaseSeeders;
    using IntegrationTests.ResourceProvider.WorkspaceCreate.VnetInjectedWorkspace.FrontDoorBuilders;
    using IntegrationTests.ResourceProvider.WorkspaceCreate.VnetInjectedWorkspace.RequestBuilders;
    using Microsoft.Owin.Hosting;
    using Microsoft.VisualStudio.TestTools.UnitTesting;
    using Newtonsoft.Json.Linq;
    using RestSharp;

    [TestClass]
    public class VnetInjectedWorkspaceCreateTest_VirtualNetworkResourceNotFound : BaseIntegrationTest<VnetInjectedWorkspaceFrontDoorBuilder_VirtualNetworkResourceNotFound, VnetInjectedWorkspaceDatabaseSeeder, BaseCustomSettings>
    {
        /// <summary>
        /// Test creation operation which fails with Virtual Network Resource not found error.
        /// </summary>
        [TestMethod]
        public async Task VNetInjectedDatabricksWorkspaceTests_ApplianceProvisioningJob_Fails_VirtualNetworkResourceNotFound()
        {
            using (var rp = KestrelHost.Start(Constants.EndPoints.RPHost))
            {
                var putClient = new RestClient(Constants.RequestMetadata.RequestUrl);
                var putRequest = new VnetInjectedWorkspaceCreateWithInvalidVnetResourceGroupRequestBuilder().Build();
                var putResponse = await putClient.ExecutePutAsync(putRequest);
                Assert.IsNotNull(putResponse);
                Assert.AreEqual(HttpStatusCode.Created, putResponse.StatusCode);

                await ApplianceTestHelper.WaitForCompletionAndVerifyResult(putResponse, ProvisioningState.Failed, ErrorResponseCode.InvalidVirtualNetworkId.ToString());
            }
        }
    }

    [TestClass]
    public class VnetInjectedWorkspaceCreateTest_VirtualNetworkResourceNotFoundAsyncTests : BaseIntegrationTest<VnetInjectedWorkspaceFrontDoorBuilder_VirtualNetworkResourceNotFoundAsync, VnetInjectedWorkspaceDatabaseSeeder, BaseCustomSettings>
    {
        /// <summary>
        /// Test creation operation which fails with Virtual Network Resource not found error.
        /// </summary>
        [TestMethod]
        public async Task VNetInjectedDatabricksWorkspaceTests_ApplianceProvisioningJob_Fails_VirtualNetworkResourceNotFound()
        {
            using (var rp = KestrelHost.Start(Constants.EndPoints.RPHost))
            {
                var putClient = new RestClient(Constants.RequestMetadata.RequestUrl);
                var putRequest = new VnetInjectedWorkspaceCreateWithInvalidVnetResourceGroupRequestBuilder().Build();
                var putResponse = await putClient.ExecutePutAsync(putRequest);
                Assert.IsNotNull(putResponse);
                Assert.AreEqual(HttpStatusCode.Created, putResponse.StatusCode);

                await ApplianceTestHelper.WaitForCompletionAndVerifyResult(putResponse, ProvisioningState.Failed, ErrorResponseCode.InvalidVirtualNetworkId.ToString());
            }
        }
    }
}
