﻿//------------------------------------------------------------
// Copyright (c) Microsoft Corporation.  All rights reserved.
//------------------------------------------------------------

namespace IntegrationTests.ResourceProvider.WorkspaceCreate.VnetInjectedWorkspace.RequestBuilders
{
    using IntegrationTests.ResourceProvider.BaseClasses;
    using RestSharp;

    public class VnetInjectedWorkspaceCreateWithInvalidVnetLocationRequestBuilder : BaseRequestBuilder
    {
        public override RestRequest Update(RestRequest request)
        {
            var requestBody = VnetInjectedWorkspaceTestHelper.GetAppliancePutContentWithVNetInjection();
            requestBody = requestBody
                    .Replace("testVNetId", Constants.FrontDoor.VirtualNetworkUrl)
                    .Replace("testMrgId", Constants.RequestBody.ManagedResourceGroupId)
                    // Vnet is in old location hence creating the workspace in different location for test
                    .Replace(Constants.RequestBody.Location, Constants.RequestBody.NewLocation); 
            request.AddStringBody(requestBody, DataFormat.Json);
            return request;
        }
    }
}
