﻿//------------------------------------------------------------
// Copyright (c) Microsoft Corporation.  All rights reserved.
//------------------------------------------------------------

namespace IntegrationTests.ResourceProvider.WorkspaceCreate.VnetInjectedWorkspace.RequestBuilders
{
    using IntegrationTests.ResourceProvider.BaseClasses;
    using RestSharp;

    public class VnetInjectedWorkspaceCreateWithInvalidVnetResourceGroupRequestBuilder : BaseRequestBuilder
    {
        public override RestRequest Update(RestRequest request)
        {
            var requestBody = VnetInjectedWorkspaceTestHelper.GetAppliancePutContentWithVNetInjection();
           var invalidVnet = Constants.FrontDoor.VirtualNetworkUrl.Replace("testVNetId", "testVNetIdDoesNotExists");
            requestBody = requestBody
                    .Replace("testVNetId", invalidVnet)
                    .Replace("testMrgId", Constants.RequestBody.ManagedResourceGroupId);
            request.AddStringBody(requestBody, DataFormat.Json);
            return request;
        }
    }
}
