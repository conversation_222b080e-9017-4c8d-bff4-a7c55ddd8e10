﻿//------------------------------------------------------------
// Copyright (c) Microsoft Corporation.  All rights reserved.
//------------------------------------------------------------

namespace IntegrationTests.ResourceProvider.WorkspaceCreate.VnetInjectedWorkspace.RequestBuilders
{
    using IntegrationTests.ResourceProvider.BaseClasses;
    using RestSharp;

    public class VnetInjectedWorkspaceCreateWithDuplicateSubnetRequestBuilder : BaseRequestBuilder
    {
        public override RestRequest Update(RestRequest request)
        {
            var requestBody = VnetInjectedWorkspaceTestHelper.GetAppliancePutContentWithVNetInjection();
            requestBody = requestBody
                    .Replace("testVNetId", Constants.FrontDoor.VirtualNetworkUrl)
                    .Replace("testMrgId", Constants.RequestBody.ManagedResourceGroupId)
                    .Replace("testPrivateSubnetName", "testPrivateSubnetName")
                    .<PERSON>lace("testPublicSubnetName", "testPrivateSubnetName");
            request.AddStringBody(requestBody, DataFormat.Json);
            return request;
        }
    }
}
