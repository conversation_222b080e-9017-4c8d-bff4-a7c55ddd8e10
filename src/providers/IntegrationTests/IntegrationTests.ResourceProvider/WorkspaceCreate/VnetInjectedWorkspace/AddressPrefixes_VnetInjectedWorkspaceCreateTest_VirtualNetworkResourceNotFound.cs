﻿//------------------------------------------------------------
// Copyright (c) Microsoft Corporation.  All rights reserved.
//------------------------------------------------------------

namespace IntegrationTests.ResourceProvider.WorkspaceCreate.VnetInjected
{
    using IntegrationTests.ResourceProvider.BaseClasses;
    using IntegrationTests.ResourceProvider.WorkspaceCreate.VnetInjectedWorkspace;
    using IntegrationTests.ResourceProvider.WorkspaceCreate.VnetInjectedWorkspace.DatabaseSeeders;
    using IntegrationTests.ResourceProvider.WorkspaceCreate.VnetInjectedWorkspace.FrontDoorBuilders;
    using IntegrationTests.ResourceProvider.WorkspaceCreate.VnetInjectedWorkspace.RequestBuilders;
    using Microsoft.VisualStudio.TestTools.UnitTesting;
    using RestSharp;
    using System.Net;
    using System.Threading.Tasks;

    [TestClass]
    public class AddressPrefixes_VnetInjectedWorkspaceCreateTest_VirtualNetworkResourceNotFound : BaseIntegrationTest<AddressPrefixesVnetInjectedWorkspaceWithVNetNotfoundFrontDoorBuilder, VnetInjectedWorkspaceDatabaseSeeder, BaseCustomSettings>
    {
        /// <summary>
        /// Test creation operation which fails with Virtual Network Resource not found error.
        /// </summary>
        [TestMethod]
        public async Task AddressPrefixes_VNetInjectedDatabricksWorkspaceTests_ApplianceProvisioningJob_Fails_VirtualNetworkResourceNotFound()
        {
            using (var rp = KestrelHost.Start(Constants.EndPoints.RPHost))
            {
                var putClient = new RestClient(Constants.RequestMetadata.RequestUrl);
                var putRequest = new VnetInjectedWorkspaceCreateWithInvalidVnetResourceGroupRequestBuilder().Build();
                var putResponse = await putClient.ExecutePutAsync(putRequest);
                Assert.IsNotNull(putResponse);
                Assert.AreEqual(HttpStatusCode.Created, putResponse.StatusCode);

                await ApplianceTestHelper.WaitForCompletionAndVerifyResult(putResponse, ProvisioningState.Failed, ErrorResponseCode.InvalidVirtualNetworkId.ToString());
            }
        }
    }

    [TestClass]
    public class AddressPrefixes_VnetInjectedWorkspaceCreateTest_VirtualNetworkResourceNotFoundAsyncTests : BaseIntegrationTest<AddressPrefixesVnetInjectedWorkspaceWithVNetNotfoundAsyncFrontDoorBuilder, VnetInjectedWorkspaceDatabaseSeeder, BaseCustomSettings>
    {
        /// <summary>
        /// Test creation operation which fails with Virtual Network Resource not found error.
        /// </summary>
        [TestMethod]
        public async Task AddressPrefixes_VNetInjectedDatabricksWorkspaceTests_ApplianceProvisioningJob_Fails_VirtualNetworkResourceNotFound()
        {
            using (var rp = KestrelHost.Start(Constants.EndPoints.RPHost))
            {
                var putClient = new RestClient(Constants.RequestMetadata.RequestUrl);
                var putRequest = new VnetInjectedWorkspaceCreateWithInvalidVnetResourceGroupRequestBuilder().Build();
                var putResponse = await putClient.ExecutePutAsync(putRequest);
                Assert.IsNotNull(putResponse);
                Assert.AreEqual(HttpStatusCode.Created, putResponse.StatusCode);

                await ApplianceTestHelper.WaitForCompletionAndVerifyResult(putResponse, ProvisioningState.Failed, ErrorResponseCode.InvalidVirtualNetworkId.ToString());
            }
        }
    }
}
