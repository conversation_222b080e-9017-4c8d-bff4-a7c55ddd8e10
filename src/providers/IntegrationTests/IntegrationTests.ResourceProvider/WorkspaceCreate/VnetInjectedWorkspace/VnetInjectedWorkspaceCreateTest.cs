﻿//------------------------------------------------------------
// Copyright (c) Microsoft Corporation.  All rights reserved.
//------------------------------------------------------------

namespace IntegrationTests.ResourceProvider.WorkspaceCreate.VnetInjected
{
    using System.Net;
    using System.Threading.Tasks;
    using IntegrationTests.ResourceProvider.BaseClasses;
    using IntegrationTests.ResourceProvider.BasicWorkspaceGet;
    using IntegrationTests.ResourceProvider.WorkspaceCreate.VnetInjectedWorkspace;
    using IntegrationTests.ResourceProvider.WorkspaceCreate.VnetInjectedWorkspace.DatabaseSeeders;
    using IntegrationTests.ResourceProvider.WorkspaceCreate.VnetInjectedWorkspace.AsyncFrontDoorBuilders;
    using IntegrationTests.ResourceProvider.WorkspaceCreate.VnetInjectedWorkspace.RequestBuilders;
    using Microsoft.Owin.Hosting;
    using Microsoft.VisualStudio.TestTools.UnitTesting;
    using Newtonsoft.Json.Linq;
    using RestSharp;

    [TestClass]
    public class VnetInjectedWorkspaceCreateTest : BaseIntegrationTest<VnetInjectedWorkspaceFrontDoorBuilder, VnetInjectedWorkspaceDatabaseSeeder, BaseCustomSettings>
    {
        [TestMethod]
        public async Task TestWorkspaceReadWithVNETContainingTrailingSlash()
        {
            using (var rp = KestrelHost.Start(Constants.EndPoints.RPHost))
            {
                var client = new RestClient(Constants.RequestMetadata.RequestUrl + "2");
                var request = new BasicWorkspaceGetRequestBuilder().Build();
                var response = await client.GetAsync(request);

                Assert.IsNotNull(response);
                Assert.AreEqual(HttpStatusCode.OK, response.StatusCode);

                var ws = JObject.Parse(response.Content);
                string customVNETId = (string) ws["properties"]["parameters"]["customVirtualNetworkId"]["value"];
                Assert.IsFalse(customVNETId.EndsWith("/"));
            }
        }

        /// <summary>
        /// Test create virtual network injected workspace creation with invalid virtual network id.
        /// </summary>
        [TestMethod]
        public async Task VNetInjectedDatabricksWorkspaceCreateTests_Validation_Fails_InvalidVirtualNetworkId()
        {
            using (var rp = KestrelHost.Start(Constants.EndPoints.RPHost))
            {
                var putClient = new RestClient(Constants.RequestMetadata.RequestUrl);
                var putRequest = new VnetInjectedWorkspaceCreateWithInvalidVnetRequestBuilder().Build();
                var putResponse = await putClient.ExecutePutAsync(putRequest);
                Assert.IsNotNull(putResponse);
                Assert.AreEqual(HttpStatusCode.BadRequest, putResponse.StatusCode);
                var putResponseContent = putResponse.Content.ToString();
                Assert.IsTrue(putResponseContent.Contains(ErrorResponseCode.InvalidVirtualNetworkId.ToString()));
            }
        }

        /// <summary>
        /// Test create virtual network injected workspace creation with invalid virtual network id.
        /// </summary>
        [TestMethod]
        public async Task VNetInjectedDatabricksWorkspaceCreateTests_Validation_Fails_MissingSubnets()
        {
            using (var rp = KestrelHost.Start(Constants.EndPoints.RPHost))
            {
                var putClient = new RestClient(Constants.RequestMetadata.RequestUrl);
                var putRequest = new VnetInjectedWorkspaceCreateWithMissingSubnetsRequestBuilder().Build();
                var putResponse = await putClient.ExecutePutAsync(putRequest);
                Assert.IsNotNull(putResponse);
                Assert.AreEqual(HttpStatusCode.BadRequest, putResponse.StatusCode);
                var putResponseContent = putResponse.Content.ToString();
                Assert.IsTrue(putResponseContent.Contains(ErrorResponseCode.MissingSubnets.ToString()));
            }
        }

        /// <summary>
        /// Test create virtual network injected workspace creation with invalid virtual network id.
        /// </summary>
        [TestMethod]
        public async Task VNetInjectedDatabricksWorkspaceTests_ApplianceProvisioningJob_Fails_SubnetInUseError()
        {
            using (var rp = KestrelHost.Start(Constants.EndPoints.RPHost))
            {
                var putClient = new RestClient(Constants.RequestMetadata.RequestUrl);
                var putRequest = new VnetInjectedWorkspaceCreateWithSubnetInUseRequestBuilder().Build();
                var putResponse = await putClient.ExecutePutAsync(putRequest);
                Assert.IsNotNull(putResponse);
                Assert.AreEqual(HttpStatusCode.Created, putResponse.StatusCode);
                await ApplianceTestHelper.WaitForCompletionAndVerifyResult(putResponse, ProvisioningState.Failed, ErrorResponseCode.SubnetInUseError.ToString());
            }
        }

        /// <summary>
        /// Test create virtual network injected workspace creation with invalid virtual network id.
        /// </summary>
        [TestMethod]
        public async Task VNetInjectedDatabricksWorkspaceCreateTests_Validation_Fails_SubscriptionMismatchError()
        {
            using (var rp = KestrelHost.Start(Constants.EndPoints.RPHost))
            {
                var putClient = new RestClient(Constants.RequestMetadata.RequestUrl);
                var putRequest = new VnetInjectedWorkspaceCreateWithSubscriptionMismatchRequestBuilder().Build();
                var putResponse = await putClient.ExecutePutAsync(putRequest);
                Assert.IsNotNull(putResponse);
                Assert.AreEqual(HttpStatusCode.BadRequest, putResponse.StatusCode);
                var putResponseContent = putResponse.Content.ToString();
                Assert.IsTrue(putResponseContent.Contains(ErrorResponseCode.SubscriptionMismatchError.ToString()));
            }
        }

        /// <summary>
        /// Test create virtual network injected workspace creation with invalid virtual network id.
        /// </summary>
        [TestMethod]
        public async Task VNetInjectedDatabricksWorkspaceCreateTests_Validation_Fails_SubnetsMustBeUniqueError()
        {
            using (var rp = KestrelHost.Start(Constants.EndPoints.RPHost))
            {
                var putClient = new RestClient(Constants.RequestMetadata.RequestUrl);
                var putRequest = new VnetInjectedWorkspaceCreateWithDuplicateSubnetRequestBuilder().Build();
                var putResponse = await putClient.ExecutePutAsync(putRequest);
                Assert.IsNotNull(putResponse);
                Assert.AreEqual(HttpStatusCode.BadRequest, putResponse.StatusCode);
                var putResponseContent = putResponse.Content.ToString();
                Assert.IsTrue(putResponseContent.Contains(ErrorResponseCode.SubnetsMustBeUniqueError.ToString()));
            }
        }

        [TestMethod]
        public async Task VNetInjectedDatabricksWorkspaceTests_ApplianceProvisioningJob_Fails_VNetLocationMismatch()
        {
            using (var rp = KestrelHost.Start(Constants.EndPoints.RPHost))
            {
                var putClient = new RestClient(Constants.RequestMetadata.RequestUrl);
                var putRequest = new VnetInjectedWorkspaceCreateWithInvalidVnetLocationRequestBuilder().Build();
                var putResponse = await putClient.ExecutePutAsync(putRequest);
                Assert.IsNotNull(putResponse);
                Assert.AreEqual(HttpStatusCode.Created, putResponse.StatusCode);
                //Vnet location - wakandacentral
                //workspace locaion - wakandacentralnew
                await ApplianceTestHelper.WaitForCompletionAndVerifyResult(putResponse, ProvisioningState.Failed, ErrorResponseCode.VNetLocationMismatch.ToString(), Constants.RequestBody.NewLocation);

            }
        }

        /// <summary>
        /// Test creation operation which fails with private or public subnets not found error.
        /// </summary>
        [TestMethod]
        public async Task VNetInjectedDatabricksWorkspaceTests_ApplianceProvisioningJob_Fails_MissingPrivateOrPublicSubnet()
        {
            using (var rp = KestrelHost.Start(Constants.EndPoints.RPHost))
            {
                var putClient = new RestClient(Constants.RequestMetadata.RequestUrl);
                var putRequest = new VnetInjectedWorkspaceCreateRequestBuilder().Build();
                var putResponse = await putClient.ExecutePutAsync(putRequest);
                Assert.IsNotNull(putResponse);
                Assert.AreEqual(HttpStatusCode.Created, putResponse.StatusCode);

                await ApplianceTestHelper.WaitForCompletionAndVerifyResult(putResponse, ProvisioningState.Failed, ErrorResponseCode.MissingSubnets.ToString());
            }
        }       
    }

    [TestClass]
    public class VnetInjectedWorkspaceCreateAsyncTests : BaseIntegrationTest<VnetInjectedWorkspaceAsyncFrontDoorBuilder, VnetInjectedWorkspaceDatabaseSeeder, BaseCustomSettings>
    {
        [TestMethod]
        public async Task TestWorkspaceReadWithVNETContainingTrailingSlash()
        {
            using (var rp = KestrelHost.Start(Constants.EndPoints.RPHost))
            {
                var client = new RestClient(Constants.RequestMetadata.RequestUrl + "2");
                var request = new BasicWorkspaceGetRequestBuilder().Build();
                var response = await client.GetAsync(request);

                Assert.IsNotNull(response);
                Assert.AreEqual(HttpStatusCode.OK, response.StatusCode);

                var ws = JObject.Parse(response.Content);
                string customVNETId = (string) ws["properties"]["parameters"]["customVirtualNetworkId"]["value"];
                Assert.IsFalse(customVNETId.EndsWith("/"));
            }
        }

        /// <summary>
        /// Test create virtual network injected workspace creation with invalid virtual network id.
        /// </summary>
        [TestMethod]
        public async Task VNetInjectedDatabricksWorkspaceCreateTests_Validation_Fails_InvalidVirtualNetworkId()
        {
            using (var rp = KestrelHost.Start(Constants.EndPoints.RPHost))
            {
                var putClient = new RestClient(Constants.RequestMetadata.RequestUrl);
                var putRequest = new VnetInjectedWorkspaceCreateWithInvalidVnetRequestBuilder().Build();
                var putResponse = await putClient.ExecutePutAsync(putRequest);
                Assert.IsNotNull(putResponse);
                Assert.AreEqual(HttpStatusCode.BadRequest, putResponse.StatusCode);
                var putResponseContent = putResponse.Content.ToString();
                Assert.IsTrue(putResponseContent.Contains(ErrorResponseCode.InvalidVirtualNetworkId.ToString()));
            }
        }

        /// <summary>
        /// Test create virtual network injected workspace creation with invalid virtual network id.
        /// </summary>
        [TestMethod]
        public async Task VNetInjectedDatabricksWorkspaceCreateTests_Validation_Fails_MissingSubnets()
        {
            using (var rp = KestrelHost.Start(Constants.EndPoints.RPHost))
            {
                var putClient = new RestClient(Constants.RequestMetadata.RequestUrl);
                var putRequest = new VnetInjectedWorkspaceCreateWithMissingSubnetsRequestBuilder().Build();
                var putResponse = await putClient.ExecutePutAsync(putRequest);
                Assert.IsNotNull(putResponse);
                Assert.AreEqual(HttpStatusCode.BadRequest, putResponse.StatusCode);
                var putResponseContent = putResponse.Content.ToString();
                Assert.IsTrue(putResponseContent.Contains(ErrorResponseCode.MissingSubnets.ToString()));
            }
        }

        /// <summary>
        /// Test create virtual network injected workspace creation with invalid virtual network id.
        /// </summary>
        [TestMethod]
        public async Task VNetInjectedDatabricksWorkspaceTests_ApplianceProvisioningJob_Fails_SubnetInUseError()
        {
            using (var rp = KestrelHost.Start(Constants.EndPoints.RPHost))
            {
                var putClient = new RestClient(Constants.RequestMetadata.RequestUrl);
                var putRequest = new VnetInjectedWorkspaceCreateWithSubnetInUseRequestBuilder().Build();
                var putResponse = await putClient.ExecutePutAsync(putRequest);
                Assert.IsNotNull(putResponse);
                Assert.AreEqual(HttpStatusCode.Created, putResponse.StatusCode);
                await ApplianceTestHelper.WaitForCompletionAndVerifyResult(putResponse, ProvisioningState.Failed, ErrorResponseCode.SubnetInUseError.ToString());
            }
        }

        /// <summary>
        /// Test create virtual network injected workspace creation with invalid virtual network id.
        /// </summary>
        [TestMethod]
        public async Task VNetInjectedDatabricksWorkspaceCreateTests_Validation_Fails_SubscriptionMismatchError()
        {
            using (var rp = KestrelHost.Start(Constants.EndPoints.RPHost))
            {
                var putClient = new RestClient(Constants.RequestMetadata.RequestUrl);
                var putRequest = new VnetInjectedWorkspaceCreateWithSubscriptionMismatchRequestBuilder().Build();
                var putResponse = await putClient.ExecutePutAsync(putRequest);
                Assert.IsNotNull(putResponse);
                Assert.AreEqual(HttpStatusCode.BadRequest, putResponse.StatusCode);
                var putResponseContent = putResponse.Content.ToString();
                Assert.IsTrue(putResponseContent.Contains(ErrorResponseCode.SubscriptionMismatchError.ToString()));
            }
        }

        /// <summary>
        /// Test create virtual network injected workspace creation with invalid virtual network id.
        /// </summary>
        [TestMethod]
        public async Task VNetInjectedDatabricksWorkspaceCreateTests_Validation_Fails_SubnetsMustBeUniqueError()
        {
            using (var rp = KestrelHost.Start(Constants.EndPoints.RPHost))
            {
                var putClient = new RestClient(Constants.RequestMetadata.RequestUrl);
                var putRequest = new VnetInjectedWorkspaceCreateWithDuplicateSubnetRequestBuilder().Build();
                var putResponse = await putClient.ExecutePutAsync(putRequest);
                Assert.IsNotNull(putResponse);
                Assert.AreEqual(HttpStatusCode.BadRequest, putResponse.StatusCode);
                var putResponseContent = putResponse.Content.ToString();
                Assert.IsTrue(putResponseContent.Contains(ErrorResponseCode.SubnetsMustBeUniqueError.ToString()));
            }
        }

        [TestMethod]
        public async Task VNetInjectedDatabricksWorkspaceTests_ApplianceProvisioningJob_Fails_VNetLocationMismatch()
        {
            using (var rp = KestrelHost.Start(Constants.EndPoints.RPHost))
            {
                var putClient = new RestClient(Constants.RequestMetadata.RequestUrl);
                var putRequest = new VnetInjectedWorkspaceCreateWithInvalidVnetLocationRequestBuilder().Build();
                var putResponse = await putClient.ExecutePutAsync(putRequest);
                Assert.IsNotNull(putResponse);
                Assert.AreEqual(HttpStatusCode.Created, putResponse.StatusCode);
                //Vnet location - wakandacentral
                //workspace locaion - wakandacentralnew
                await ApplianceTestHelper.WaitForCompletionAndVerifyResult(putResponse, ProvisioningState.Failed, ErrorResponseCode.VNetLocationMismatch.ToString(), Constants.RequestBody.NewLocation);

            }
        }

        /// <summary>
        /// Test creation operation which fails with private or public subnets not found error.
        /// </summary>
        [TestMethod]
        public async Task VNetInjectedDatabricksWorkspaceTests_ApplianceProvisioningJob_Fails_MissingPrivateOrPublicSubnet()
        {
            using (var rp = KestrelHost.Start(Constants.EndPoints.RPHost))
            {
                var putClient = new RestClient(Constants.RequestMetadata.RequestUrl);
                var putRequest = new VnetInjectedWorkspaceCreateRequestBuilder().Build();
                var putResponse = await putClient.ExecutePutAsync(putRequest);
                Assert.IsNotNull(putResponse);
                Assert.AreEqual(HttpStatusCode.Created, putResponse.StatusCode);

                await ApplianceTestHelper.WaitForCompletionAndVerifyResult(putResponse, ProvisioningState.Failed, ErrorResponseCode.MissingSubnets.ToString());
            }
        }       
    }
}