﻿//------------------------------------------------------------
// Copyright (c) Microsoft Corporation.  All rights reserved.
//------------------------------------------------------------

using WireMock.RequestBuilders;
using WireMock.ResponseBuilders;
using WireMock.Server;

namespace IntegrationTests.ResourceProvider.WorkspaceCreate.VnetInjectedWorkspace
{
    public class VnetInjectedWorkspaceTestHelper
    {
        /// <summary>
        /// Gets the appliance put content with virtual network injection.
        /// </summary>
        public static string GetAppliancePutContentWithVNetInjection()
        {
            return "{\"properties\":{\"managedResourceGroupId\":\"testMrgId\",\"parameters\":{\"name\":{\"value\":\"testws\"},\"location\":{\"value\":\"wakandacentral\"},\"customVirtualNetworkId\":{\"value\":\"testVNetId\"},\"customPublicSubnetName\":{\"value\":\"testPublicSubnetName\"},\"customPrivateSubnetName\":{\"value\":\"testPrivateSubnetName\"}},\"createdDateTime\":\"0001-01-01T00:00:00Z\"},\"type\":\"Microsoft.Solutions/applications\",\"sku\":{\"name\":\"premium\"},\"location\":\"wakandacentral\",\"tags\":{\"department\":\"Finance\"}}";
        }

        /// <summary>
        /// Get test virtual network content without subnets.
        /// </summary>
        public static string GetTestVirtualNetworkWithoutSubnets()
        {
            return @"{
              ""name"": ""testVNetName"",
              ""id"": ""testVNetId"",
              ""type"": ""Microsoft.Network/virtualNetworks"",
              ""location"": ""wakandacentral"",
              ""properties"": {
                ""addressSpace"": {
                  ""addressPrefixes"": [
                    ""********/16""
                  ]
                }
              }
            }";
        }
        public static void SetupAccountApiDeleteResponse(WireMockServer frontDoor)
        {
            string applianceJson = $@"{{
                ""id"": ""/subscriptions/59d64684-e7c9-4397-8982-6b775a473b74/resourceGroups/mockRG/providers/Microsoft.Databricks/workspaces/MockWs"",
                ""location"": ""eastus2euap"",
                ""name"": ""MockWs"",
                ""properties"": {{
                    ""authorizations"": [
                        {{
                            ""principalId"": ""MockPrincipalId"",
                            ""roleDefinitionId"": ""MockRoleDefinitionId""
                        }}
                    ],
                    ""createdBy"": {{
                        ""oid"": ""MockOid"",
                        ""applicationId"": ""MockAppId"",
                        ""puid"": ""MockPuid""
                    }},
                    ""managedResourceGroupId"": ""/subscriptions/59d64684-e7c9-4397-8982-6b775a473b74/resourceGroups/MockMRG"",
                    ""parameters"": {{
                        ""enableNoPublicIp"": {{
                            ""type"": ""Bool"",
                            ""value"": false
                        }}
                    }},
                    ""provisioningState"": ""Accepted"",
                    ""updatedBy"": {{
                        ""oid"": ""MockOid"",
                        ""applicationId"": ""MockAppId"",
                        ""puid"": ""MockPuid""
                    }},
                    ""workspaceId"": ""{Constants.DBWorkspace.WorkspaceId}"",
                    ""workspaceUrl"": ""{Constants.DBWorkspace.WorkspaceUrl}"",
                    ""accessConnector"": {{
                        ""id"": ""/subscriptions/bba910f5-3a9d-4c3a-a897-696933a28884/resourceGroups/MockMRG/providers/Microsoft.Databricks/accessConnectors/unity-catalog-access-connector"",
                        ""identityType"": ""SystemAssigned""
                    }},
                    ""defaultCatalog"": {{
                        ""initialType"": ""UnityCatalog"",
                        ""initialName"": """"
                    }},
                    ""isUcEnabled"": false,
                    ""operationStatus"": {{
                        ""status"": ""Running""
                    }},
                    ""dbWorkspaceState"": ""Inactive""
                }},
                ""sku"": {{
                    ""name"": ""premium""
                }},
                ""type"": ""Microsoft.Databricks/workspaces""
            }}";

            frontDoor.Given(
               Request.Create()
               .WithPath(Constants.FrontDoor.WorkspaceInitializationUrl)
               .UsingDelete())
                .InScenario("AccountApiDelete")
                .WillSetStateTo("FirstPolling")
                .RespondWith(
               Response.Create()
               .WithHeader("Content-Type", "application/json")
               .WithStatusCode(200));

            frontDoor.Given(
               Request.Create()
               .WithPath(Constants.FrontDoor.WorkspaceInitializationUrl)
               .UsingGet())
                .InScenario("AccountApiDelete")
                .WhenStateIs("FirstPolling")
                .WillSetStateTo("SecondPolling")
                .RespondWith(
               Response.Create()
               .WithHeader("Content-Type", "application/json")
               .WithStatusCode(200)
               .WithBody(applianceJson));
            frontDoor.Given(
               Request.Create()
               .WithPath(Constants.FrontDoor.WorkspaceInitializationUrl)
               .UsingGet())
                .InScenario("AccountApiDelete")
                .WhenStateIs("SecondPolling")
                .WillSetStateTo("Deleted")
                .RespondWith(
               Response.Create()
               .WithHeader("Content-Type", "application/json")
               .WithStatusCode(404));
            frontDoor.Given(
               Request.Create()
               .WithPath(Constants.FrontDoor.WorkspaceInitializationUrl)
               .UsingGet())
                .InScenario("AccountApiDelete")
                .WhenStateIs("Deleted")
                .WillSetStateTo("Deleted")
                .RespondWith(
               Response.Create()
               .WithHeader("Content-Type", "application/json")
               .WithStatusCode(404));
        }


        public static void SetupAccountApiFeature(WireMockServer frontDoor)
        {
            var accountApiFeatureResponse =
               "{" +
                   "\"properties\": { \"state\": \"Registered\" }, " +
                   "\"id\": \"/subscriptions/59d64684-e7c9-4397-8982-6b775a473b74/providers/Microsoft.Features/providers/Microsoft.Databricks/features/AccountApi\"," +
                   "\"type\": \"Microsoft.Features/providers/features\"," +
                   "\"name\": \"Microsoft.Databricks/AccountApi\"" +
               "}";
            frontDoor.Given(
                Request.Create()
                .WithPath(Constants.FrontDoor.FeaturesUrl)
                .UsingGet()
            ).RespondWith(
                Response.Create()
                .WithHeader("Content-Type", "application/json")
                .WithStatusCode(200)
                .WithBody("{ \"value\":[" + accountApiFeatureResponse + "] }"));
        }
        /// <summary>
        /// Get test virtual network content without network security group.
        /// </summary>
        public static string GetTestVirtualNetworkWithoutNetworkSecurityGroup()
        {
            return @"{
              ""name"": ""testVNetName"",
              ""id"": ""testVNetId"",
              ""type"": ""Microsoft.Network/virtualNetworks"",
              ""location"": ""wakandacentral"",
              ""properties"": {
                ""addressSpace"": {
                  ""addressPrefixes"": [
                    ""********/16""
                  ]
                },
                ""subnets"": [
                  {
                    ""name"": ""private-subnet"",
                    ""id"": ""testVNetId/subnets/testPrivateSubnetName"",
                    ""properties"": {
                        ""addressPrefix"": ""**********/24"",
                        ""routeTable"": {
                            ""id"": ""/subscriptions/testSubscriptionId/resourceGroups/testResourceGroupName/providers/Microsoft.Network/routeTables/databricksrt3mauuvh3nm5um""
                        }
                    }
                  },
                  {
                    ""name"": ""public-subnet"",
                    ""id"": ""testVNetId/subnets/testPublicSubnetName"",
                    ""properties"": {
                        ""addressPrefix"": ""********/24"",
                        ""networkSecurityGroup"": {
                            ""id"": ""/subscriptions/testSubscriptionId/resourceGroups/testResourceGroupName/providers/Microsoft.Network/networkSecurityGroup/databricksrt3mauuvh3nm5um""
                        },
                        ""routeTable"": {
                            ""id"": ""/subscriptions/testSubscriptionId/resourceGroups/testResourceGroupName/providers/Microsoft.Network/routeTables/databricksrt3mauuvh3nm5um""
                        }
                    }
                  }
                ]
              }
            }";
        }


        /// <summary>
        /// Get test virtual network content without network security group.
        /// </summary>
        public static string GetTestVirtualNetworkWithoutNetworkSecurityGroup_AddressPrefixes()
        {
            return @"{
              ""name"": ""testVNetName"",
              ""id"": ""testVNetId"",
              ""type"": ""Microsoft.Network/virtualNetworks"",
              ""location"": ""wakandacentral"",
              ""properties"": {
                ""addressSpace"": {
                  ""addressPrefixes"": [
                    ""********/16""
                  ]
                },
                ""subnets"": [
                  {
                    ""name"": ""private-subnet"",
                    ""id"": ""testVNetId/subnets/testPrivateSubnetName"",
                    ""properties"": {
                        ""addressPrefixes"": [""**********/24""],
                        ""routeTable"": {
                            ""id"": ""/subscriptions/testSubscriptionId/resourceGroups/testResourceGroupName/providers/Microsoft.Network/routeTables/databricksrt3mauuvh3nm5um""
                        }
                    }
                  },
                  {
                    ""name"": ""public-subnet"",
                    ""id"": ""testVNetId/subnets/testPublicSubnetName"",
                    ""properties"": {
                        ""addressPrefixes"": [""********/24""],
                        ""networkSecurityGroup"": {
                            ""id"": ""/subscriptions/testSubscriptionId/resourceGroups/testResourceGroupName/providers/Microsoft.Network/networkSecurityGroup/databricksrt3mauuvh3nm5um""
                        },
                        ""routeTable"": {
                            ""id"": ""/subscriptions/testSubscriptionId/resourceGroups/testResourceGroupName/providers/Microsoft.Network/routeTables/databricksrt3mauuvh3nm5um""
                        }
                    }
                  }
                ]
              }
            }";
        }

        /// <summary>
        /// Get test virtual network content.
        /// </summary>
        public static string GetTestVirtualNetwork()
        {
            return @"{
              ""name"": ""testVNetName"",
              ""id"": ""testVNetId"",
              ""type"": ""Microsoft.Network/virtualNetworks"",
              ""tags"": { ""department"": ""Finance"" },
              ""location"": ""wakandacentral"",
              ""properties"": {
                ""addressSpace"": {
                  ""addressPrefixes"": [
                    ""********/16""
                  ]
                },
                ""subnets"": [
                  {
                    ""name"": ""private-subnet"",
                    ""id"": ""testVNetId/subnets/testPrivateSubnetName"",
                    ""properties"": {
                        ""addressPrefix"": ""**********/24"",
                        ""networkSecurityGroup"": {
                            ""id"": ""/subscriptions/testSubscriptionId/resourceGroups/testResourceGroupName/providers/Microsoft.Network/networkSecurityGroup/databricksrt3mauuvh3nm5um""
                        },
                        ""routeTable"": {
                            ""id"": ""/subscriptions/testSubscriptionId/resourceGroups/testResourceGroupName/providers/Microsoft.Network/routeTables/databricksrt3mauuvh3nm5um""
                        }
                    }
                  },
                  {
                    ""name"": ""public-subnet"",
                    ""id"": ""testVNetId/subnets/testPublicSubnetName"",
                    ""properties"": {
                        ""addressPrefix"": ""********/24"",
                        ""networkSecurityGroup"": {
                            ""id"": ""/subscriptions/testSubscriptionId/resourceGroups/testResourceGroupName/providers/Microsoft.Network/networkSecurityGroup/databricksrt3mauuvh3nm5um""
                        },
                        ""routeTable"": {
                            ""id"": ""/subscriptions/testSubscriptionId/resourceGroups/testResourceGroupName/providers/Microsoft.Network/routeTables/databricksrt3mauuvh3nm5um""
                        }
                    }
                  }
                ]
              }
            }";
        }

        /// <summary>
        /// Get test virtual network content.
        /// </summary>
        public static string GetTestVirtualNetwork_privatesubnet()
        {
            return @"{
              ""name"": ""testVNetName"",
              ""id"": ""testVNetId"",
              ""type"": ""Microsoft.Network/virtualNetworks"",
              ""tags"": { ""department"": ""Finance"" },
              ""location"": ""wakandacentral"",
              ""properties"": {
                ""addressSpace"": {
                  ""addressPrefixes"": [
                    ""********/16""
                  ]
                },
                ""subnets"": [                  
                  {
                    ""name"": ""public-subnet"",
                    ""id"": ""testVNetId/subnets/testPublicSubnetName"",
                    ""properties"": {
                        ""addressPrefix"": ""********/24"",
                        ""networkSecurityGroup"": {
                            ""id"": ""/subscriptions/testSubscriptionId/resourceGroups/testResourceGroupName/providers/Microsoft.Network/networkSecurityGroup/databricksrt3mauuvh3nm5um""
                        },
                        ""routeTable"": {
                            ""id"": ""/subscriptions/testSubscriptionId/resourceGroups/testResourceGroupName/providers/Microsoft.Network/routeTables/databricksrt3mauuvh3nm5um""
                        }
                    }
                  }
                ]
              }
            }";
        }

        /// <summary>
        /// Get test virtual network content.
        /// </summary>
        public static string GetTestVirtualNetworkWithAddressPrefixes()
        {
            return @"{
              ""name"": ""testVNetName"",
              ""id"": ""testVNetId"",
              ""type"": ""Microsoft.Network/virtualNetworks"",
              ""tags"": { ""department"": ""Finance"" },
              ""location"": ""wakandacentral"",
              ""properties"": {
                ""addressSpace"": {
                  ""addressPrefixes"": [
                    ""********/16""
                  ]
                },
                ""subnets"": [
                  {
                    ""name"": ""private-subnet"",
                    ""id"": ""testVNetId/subnets/testPrivateSubnetName"",
                    ""properties"": {
                        ""addressPrefixes"": [""**********/24"", ""**********/24""],
                        ""networkSecurityGroup"": {
                            ""id"": ""/subscriptions/testSubscriptionId/resourceGroups/testResourceGroupName/providers/Microsoft.Network/networkSecurityGroup/databricksrt3mauuvh3nm5um""
                        },
                        ""routeTable"": {
                            ""id"": ""/subscriptions/testSubscriptionId/resourceGroups/testResourceGroupName/providers/Microsoft.Network/routeTables/databricksrt3mauuvh3nm5um""
                        }
                    }
                  },
                  {
                    ""name"": ""public-subnet"",
                    ""id"": ""testVNetId/subnets/testPublicSubnetName"",
                    ""properties"": {
                        ""addressPrefixes"": [""********/24""],
                        ""networkSecurityGroup"": {
                            ""id"": ""/subscriptions/testSubscriptionId/resourceGroups/testResourceGroupName/providers/Microsoft.Network/networkSecurityGroup/databricksrt3mauuvh3nm5um""
                        },
                        ""routeTable"": {
                            ""id"": ""/subscriptions/testSubscriptionId/resourceGroups/testResourceGroupName/providers/Microsoft.Network/routeTables/databricksrt3mauuvh3nm5um""
                        }
                    }
                  }
                ]
              }
            }";
        }

        /// <summary>
        /// Get test virtual network content.
        /// </summary>
        public static string GetTestVirtualNetworkWithAddressPrefixeswithNULLCIDR()
        {
            return @"{
              ""name"": ""testVNetName"",
              ""id"": ""testVNetId"",
              ""type"": ""Microsoft.Network/virtualNetworks"",
              ""tags"": { ""department"": ""Finance"" },
              ""location"": ""wakandacentral"",
              ""properties"": {
                ""addressSpace"": {
                  ""addressPrefixes"": [
                    ""********/16""
                  ]
                },
                ""subnets"": [
                  {
                    ""name"": ""private-subnet"",
                    ""id"": ""testVNetId/subnets/testPrivateSubnetName"",
                    ""properties"": {
                        ""addressPrefixes"": [""""],
                        ""networkSecurityGroup"": {
                            ""id"": ""/subscriptions/testSubscriptionId/resourceGroups/testResourceGroupName/providers/Microsoft.Network/networkSecurityGroup/databricksrt3mauuvh3nm5um""
                        },
                        ""routeTable"": {
                            ""id"": ""/subscriptions/testSubscriptionId/resourceGroups/testResourceGroupName/providers/Microsoft.Network/routeTables/databricksrt3mauuvh3nm5um""
                        }
                    }
                  },
                  {
                    ""name"": ""public-subnet"",
                    ""id"": ""testVNetId/subnets/testPublicSubnetName"",
                    ""properties"": {
                        ""addressPrefixes"": [""""],
                        ""networkSecurityGroup"": {
                            ""id"": ""/subscriptions/testSubscriptionId/resourceGroups/testResourceGroupName/providers/Microsoft.Network/networkSecurityGroup/databricksrt3mauuvh3nm5um""
                        },
                        ""routeTable"": {
                            ""id"": ""/subscriptions/testSubscriptionId/resourceGroups/testResourceGroupName/providers/Microsoft.Network/routeTables/databricksrt3mauuvh3nm5um""
                        }
                    }
                  }
                ]
              }
            }";
        }

        /// <summary>
        /// Get test virtual network content after prepared.
        /// </summary>
        public static string GetTestPreparedVirtualNetwork()
        {
            return @"{
              ""name"": ""testVNetName"",
              ""id"": ""testVNetId"",
              ""type"": ""Microsoft.Network/virtualNetworks"",
              ""location"": ""DevFabric"",
              ""tags"": { ""department"": ""Finance"" },
              ""properties"": {
                ""addressSpace"": {
                  ""addressPrefixes"": [
                    ""********/16""
                  ]
                },
                ""subnets"": [
                  {
                    ""name"": ""private-subnet"",
                    ""id"": ""testVNetId/subnets/testPrivateSubnetName"",
                    ""properties"": {
                        ""addressPrefix"": ""**********/24"",
                        ""networkIntentPolicies"": [{
                            ""id"": ""/subscriptions/testSubscriptionId/resourceGroups/testResourceGroupName/providers/Microsoft.Network/networkIntentPolicies/testNetworkIntentPolicyName""
                        }],
                        ""networkSecurityGroup"": {
                            ""id"": ""/subscriptions/testSubscriptionId/resourceGroups/testResourceGroupName/providers/Microsoft.Network/networkSecurityGroup/databricksrt3mauuvh3nm5um""
                        },
                        ""routeTable"": {
                            ""id"": ""/subscriptions/testSubscriptionId/resourceGroups/testResourceGroupName/providers/Microsoft.Network/routeTables/databricksrt3mauuvh3nm5um""
                        }
                    }
                  },
                  {
                    ""name"": ""public-subnet"",
                    ""id"": ""testVNetId/subnets/testPublicSubnetName"",
                    ""properties"": {
                        ""addressPrefix"": ""********/24"",
                        ""networkIntentPolicies"": [{
                            ""id"": ""/subscriptions/testSubscriptionId/resourceGroups/testResourceGroupName/providers/Microsoft.Network/networkIntentPolicies/testNetworkIntentPolicyName""
                        }],
                        ""networkSecurityGroup"": {
                            ""id"": ""/subscriptions/testSubscriptionId/resourceGroups/testResourceGroupName/providers/Microsoft.Network/networkSecurityGroup/databricksrt3mauuvh3nm5um""
                        },
                        ""routeTable"": {
                            ""id"": ""/subscriptions/testSubscriptionId/resourceGroups/testResourceGroupName/providers/Microsoft.Network/routeTables/databricksrt3mauuvh3nm5um""
                        }
                    }
                  }
                ]
              }
            }";
        }
    }
}
