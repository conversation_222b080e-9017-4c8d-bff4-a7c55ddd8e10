﻿//------------------------------------------------------------
// Copyright (c) Microsoft Corporation.  All rights reserved.
//------------------------------------------------------------

namespace IntegrationTests.ResourceProvider.WorkspaceCreate.VnetInjected
{
    using IntegrationTests.ResourceProvider.BaseClasses;
    using IntegrationTests.ResourceProvider.WorkspaceCreate.VnetInjectedWorkspace.AsyncFrontDoorBuilders;
    using IntegrationTests.ResourceProvider.WorkspaceCreate.VnetInjectedWorkspace.FrontDoorBuilders;
    using IntegrationTests.ResourceProvider.WorkspaceCreate.VnetInjectedWorkspace.RequestBuilders;
    using Microsoft.VisualStudio.TestTools.UnitTesting;
    using RestSharp;
    using System.Net;
    using System.Threading.Tasks;

    [TestClass]
    public class AddressPrefixes_PreparePrivateSubnetFailsWithConflictNetworkIntentPoliciesTest : BaseIntegrationTest<AddressPrefixesPreparePrivateSubnetFailsFrontDoorBuilder, BaseDatabaseSeeder, BaseCustomSettings>
    {
        /// <summary>
        /// Test creation operation which fails with during prepare subnet because conflict With existing network intent policy.
        /// </summary>
        [TestMethod]
        public async Task AddressPrefixes_VNetInjectedDatabricksWorkspaceTests_ApplianceProvisioningJob_PrivateSubnet_Fails_ConflictWithNetworkIntentPolicy()
        {
            using (var rp = KestrelHost.Start(Constants.EndPoints.RPHost))
            {
                var putClient = new RestClient(Constants.RequestMetadata.RequestUrl);
                var putRequest = new VnetInjectedWorkspaceCreateRequestBuilder().Build();
                var putResponse = await putClient.ExecutePutAsync(putRequest);
                Assert.IsNotNull(putResponse);
                Assert.AreEqual(HttpStatusCode.Created, putResponse.StatusCode);

                await ApplianceTestHelper.WaitForCompletionAndVerifyResult(putResponse, ProvisioningState.Failed, ErrorResponseCode.ConflictWithNetworkIntentPolicy.ToString());
            }
        }
    }

    [TestClass]
    public class AddressPrefixes_PreparePrivateSubnetFailsWithConflictNetworkIntentPoliciesAsyncTests : BaseIntegrationTest<AddressPrefixesPreparePrivateSubnetFailsAsyncFrontDoorBuilder, BaseDatabaseSeeder, BaseCustomSettings>
    {
        /// <summary>
        /// Test creation operation which fails with during prepare subnet because conflict With existing network intent policy.
        /// </summary>
        [TestMethod]
        public async Task AddressPrefixes_VNetInjectedDatabricksWorkspaceTests_ApplianceProvisioningJob_PrivateSubnet_Fails_ConflictWithNetworkIntentPolicy()
        {
            using (var rp = KestrelHost.Start(Constants.EndPoints.RPHost))
            {
                var putClient = new RestClient(Constants.RequestMetadata.RequestUrl);
                var putRequest = new VnetInjectedWorkspaceCreateRequestBuilder().Build();
                var putResponse = await putClient.ExecutePutAsync(putRequest);
                Assert.IsNotNull(putResponse);
                Assert.AreEqual(HttpStatusCode.Created, putResponse.StatusCode);

                await ApplianceTestHelper.WaitForCompletionAndVerifyResult(putResponse, ProvisioningState.Failed, ErrorResponseCode.ConflictWithNetworkIntentPolicy.ToString());
            }
        }
    }
}