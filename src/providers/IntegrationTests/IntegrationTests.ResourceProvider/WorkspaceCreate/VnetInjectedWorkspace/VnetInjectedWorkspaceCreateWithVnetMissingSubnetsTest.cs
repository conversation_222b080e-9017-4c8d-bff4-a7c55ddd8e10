﻿//------------------------------------------------------------
// Copyright (c) Microsoft Corporation.  All rights reserved.
//------------------------------------------------------------

namespace IntegrationTests.ResourceProvider.WorkspaceCreate.VnetInjected
{
    using System.Net;
    using System.Threading.Tasks;
    using IntegrationTests.ResourceProvider.BaseClasses;
    using IntegrationTests.ResourceProvider.WorkspaceCreate.VnetInjectedWorkspace.AsyncFrontDoorBuilders;
    using IntegrationTests.ResourceProvider.WorkspaceCreate.VnetInjectedWorkspace.FrontDoorBuilders.IntegrationTests.ResourceProvider.WorkspaceCreate.VnetInjectedWorkspace;
    using IntegrationTests.ResourceProvider.WorkspaceCreate.VnetInjectedWorkspace.RequestBuilders;
    using Microsoft.Owin.Hosting;
    using Microsoft.VisualStudio.TestTools.UnitTesting;
    using RestSharp;

    [TestClass]
    public class VnetInjectedWorkspaceCreateWithVnetMissingSubnetsTest : BaseIntegrationTest<VnetInjectedWorkspaceWithVnetWithoutSubnetsFrontDoorBuilder, BaseDatabaseSeeder, BaseCustomSettings>
    {
        /// <summary>
        /// Test creation operation which fails with private or public subnets not found error.
        /// </summary>
        [TestMethod]
        public async Task VNetInjectedDatabricksWorkspaceTests_ApplianceProvisioningJob_Fails_MissingPrivateOrPublicSubnet()
        {
            using (var rp = KestrelHost.Start(Constants.EndPoints.RPHost))
            {
                var putClient = new RestClient(Constants.RequestMetadata.RequestUrl);
                var putRequest = new VnetInjectedWorkspaceCreateRequestBuilder().Build();
                var putResponse = await putClient.ExecutePutAsync(putRequest);
                Assert.IsNotNull(putResponse);
                Assert.AreEqual(HttpStatusCode.Created, putResponse.StatusCode);

                await ApplianceTestHelper.WaitForCompletionAndVerifyResult(putResponse, ProvisioningState.Failed, ErrorResponseCode.MissingSubnets.ToString());
            }
        }
    }

    [TestClass]
    public class VnetInjectedWorkspaceCreateWithVnetMissingSubnetsAsyncTests : BaseIntegrationTest<VnetInjectedWorkspaceWithVnetWithoutSubnetsAsyncFrontDoorBuilder, BaseDatabaseSeeder, BaseCustomSettings>
    {
        /// <summary>
        /// Test creation operation which fails with private or public subnets not found error.
        /// </summary>
        [TestMethod]
        public async Task VNetInjectedDatabricksWorkspaceTests_ApplianceProvisioningJob_Fails_MissingPrivateOrPublicSubnet()
        {
            using (var rp = KestrelHost.Start(Constants.EndPoints.RPHost))
            {
                var putClient = new RestClient(Constants.RequestMetadata.RequestUrl);
                var putRequest = new VnetInjectedWorkspaceCreateRequestBuilder().Build();
                var putResponse = await putClient.ExecutePutAsync(putRequest);
                Assert.IsNotNull(putResponse);
                Assert.AreEqual(HttpStatusCode.Created, putResponse.StatusCode);

                await ApplianceTestHelper.WaitForCompletionAndVerifyResult(putResponse, ProvisioningState.Failed, ErrorResponseCode.MissingSubnets.ToString());
            }
        }
    }
}