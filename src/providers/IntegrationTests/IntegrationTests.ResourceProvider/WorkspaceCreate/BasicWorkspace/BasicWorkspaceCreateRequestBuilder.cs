﻿//------------------------------------------------------------
// Copyright (c) Microsoft Corporation.  All rights reserved.
//------------------------------------------------------------

namespace IntegrationTests.ResourceProvider.WorkspaceCreate.BasicWorkspace
{
    using IntegrationTests.ResourceProvider.BaseClasses;
    using RestSharp;

    public class BasicWorkspaceCreateRequestBuilder : BaseRequestBuilder
    {
        public override RestRequest Update(RestRequest request)
        {
            var requestBody =
                "{" +
                    "\"sku\": { \"name\": \"" + Constants.RequestBody.SkuPremium + "\" }," +
                    "\"location\": \"" + Constants.RequestBody.Location + "\"," +
                    "\"tags\": { }," +
                    "\"properties\": { \"ManagedResourceGroupId\": \"" + Constants.RequestBody.ManagedResourceGroupId + "\" }" +
                    "}";


            request.AddStringBody(requestBody, DataFormat.Json);
            request.AddHeader(Constants.RequestHeaders.SystemDataHeaderKey, Constants.RequestHeaders.SystemDataHeaderValue);
            return request;
        }
    }
}
