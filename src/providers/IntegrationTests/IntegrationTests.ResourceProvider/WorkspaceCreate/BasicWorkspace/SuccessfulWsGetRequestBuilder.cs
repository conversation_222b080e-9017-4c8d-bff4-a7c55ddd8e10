﻿//------------------------------------------------------------
// Copyright (c) Microsoft Corporation.  All rights reserved.
//------------------------------------------------------------

namespace IntegrationTests.ResourceProvider.WorkspaceCreate.BasicWorkspace
{
    using IntegrationTests.ResourceProvider.BaseClasses;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Common.Constants;
    using RestSharp;

    public class SuccessfulWsGetRequestBuilder(string apiVersion = ProviderConstants.ApiVersion20240201Preview) : BaseRequestBuilder
    {
        private readonly string apiVersion = apiVersion;

        public override RestRequest Update(RestRequest request)
        {
            request.AddQueryParameter("api-version", this.apiVersion);
            return request;
        }
    }
}
