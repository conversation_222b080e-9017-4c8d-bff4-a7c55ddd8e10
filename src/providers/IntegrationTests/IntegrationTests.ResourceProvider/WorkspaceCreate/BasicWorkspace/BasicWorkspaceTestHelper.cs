﻿using WireMock.RequestBuilders;
using WireMock.ResponseBuilders;
using WireMock.Server;

namespace IntegrationTests.ResourceProvider.WorkspaceCreate.BasicWorkspace
{
    public class BasicWorkspaceTestHelper
    {
        public static string GetWorkspacecontent()
        {
                return "{" +
                         "\"sku\": { \"name\": \"" + Constants.RequestBody.SkuPremium + "\" }," +
                        "\"location\": \"" + Constants.RequestBody.Location + "\"," +
                        "\"tags\": { }," +
                        "\"properties\": { \"ManagedResourceGroupId\": \"" + Constants.RequestBody.ManagedResourceGroupId + "\" }" +
                        "}";
        }

        public static void SetupAccountApiFeature(WireMockServer frontDoor)
        {
            var accountApiFeatureResponse =
               "{" +
                   "\"properties\": { \"state\": \"Registered\" }, " +
                   "\"id\": \"/subscriptions/59d64684-e7c9-4397-8982-6b775a473b74/providers/Microsoft.Features/providers/Microsoft.Databricks/features/AccountApi\"," +
                   "\"type\": \"Microsoft.Features/providers/features\"," +
                   "\"name\": \"Microsoft.Databricks/AccountApi\"" +
               "}";
            frontDoor.Given(
                Request.Create()
                .WithPath(Constants.FrontDoor.FeaturesUrl)
                .UsingGet()
            ).RespondWith(
                Response.Create()
                .WithHeader("Content-Type", "application/json")
                .WithStatusCode(200)
                .WithBody("{ \"value\":[" + accountApiFeatureResponse + "] }"));
        }

        public static void SetupWorkspaceAccountApiCreateWithPolling(WireMockServer frontDoor)
        {
            string applianceJson = $@"{{
                ""id"": ""/subscriptions/59d64684-e7c9-4397-8982-6b775a473b74/resourceGroups/mockRG/providers/Microsoft.Databricks/workspaces/MockWs"",
                ""location"": ""eastus2euap"",
                ""name"": ""MockWs"",
                ""properties"": {{
                    ""authorizations"": [
                        {{
                            ""principalId"": ""MockPrincipalId"",
                            ""roleDefinitionId"": ""MockRoleDefinitionId""
                        }}
                    ],
                    ""createdBy"": {{
                        ""oid"": ""MockOid"",
                        ""applicationId"": ""MockAppId"",
                        ""puid"": ""MockPuid""
                    }},
                    ""managedResourceGroupId"": ""/subscriptions/59d64684-e7c9-4397-8982-6b775a473b74/resourceGroups/MockMRG"",
                    ""parameters"": {{
                        ""enableNoPublicIp"": {{
                            ""type"": ""Bool"",
                            ""value"": false
                        }}
                    }},
                    ""provisioningState"": ""Accepted"",
                    ""updatedBy"": {{
                        ""oid"": ""MockOid"",
                        ""applicationId"": ""MockAppId"",
                        ""puid"": ""MockPuid""
                    }},
                    ""workspaceId"": ""{Constants.DBWorkspace.WorkspaceId}"",
                    ""workspaceUrl"": ""{Constants.DBWorkspace.WorkspaceUrl}"",
                    ""accessConnector"": {{
                        ""id"": ""/subscriptions/bba910f5-3a9d-4c3a-a897-696933a28884/resourceGroups/MockMRG/providers/Microsoft.Databricks/accessConnectors/unity-catalog-access-connector"",
                        ""identityType"": ""SystemAssigned""
                    }},
                    ""defaultCatalog"": {{
                        ""initialType"": ""UnityCatalog"",
                        ""initialName"": """"
                    }},
                    ""isUcEnabled"": false,
                    ""operationStatus"": {{
                        ""status"": ""Running""
                    }},
                    ""dbWorkspaceState"": ""Inactive""
                }},
                ""sku"": {{
                    ""name"": ""premium""
                }},
                ""type"": ""Microsoft.Databricks/workspaces""
            }}";


            frontDoor.Given(
                Request.Create()
                .WithPath(Constants.RequestMetadata.AccountsApiWorkspaceUrl)
                .UsingGet())
                .InScenario("Account Api")
                .WillSetStateTo("workspace put")
                .RespondWith(
                Response.Create()
                .WithHeader("Content-Type", "application/json")
                .WithStatusCode(404));
            frontDoor.Given(
                Request.Create()
                .WithPath(Constants.RequestMetadata.AccountsApiWorkspaceUrl)
                .UsingPut())
                .InScenario("Account Api")
                .WhenStateIs("workspace put")
                .WillSetStateTo("first Polling")
                .RespondWith(
                Response.Create()
                .WithHeader("Content-Type", "application/json")
                .WithStatusCode(200)
                .WithBody(applianceJson));
            frontDoor.Given(
               Request.Create()
                .WithPath(Constants.RequestMetadata.AccountsApiWorkspaceUrl)
               .UsingGet())
               .InScenario("Account Api")
               .WhenStateIs("first Polling")
               .WillSetStateTo("second Polling")
               .RespondWith(
               Response.Create()
               .WithHeader("Content-Type", "application/json")
               .WithStatusCode(200)
                .WithBody(applianceJson));
            frontDoor.Given(
               Request.Create()
               .WithPath(Constants.RequestMetadata.AccountsApiWorkspaceUrl)
               .UsingGet())
                .InScenario("Account Api")
               .WhenStateIs("second Polling")
               .RespondWith(
               Response.Create()
               .WithHeader("Content-Type", "application/json")
               .WithStatusCode(200)
                .WithBody(applianceJson.Replace("Running", "Succeeded")));
        }

        public static void SetupWorkspaceAccountApiCreateWithPollingWithEmptyResponse(WireMockServer frontDoor)
        {
            string applianceJson = $@"{{
                ""id"": ""/subscriptions/59d64684-e7c9-4397-8982-6b775a473b74/resourceGroups/mockRG/providers/Microsoft.Databricks/workspaces/MockWs"",
                ""location"": ""eastus2euap"",
                ""name"": ""MockWs"",
                ""properties"": {{
                    ""authorizations"": [
                        {{
                            ""principalId"": ""MockPrincipalId"",
                            ""roleDefinitionId"": ""MockRoleDefinitionId""
                        }}
                    ],
                    ""createdBy"": {{
                        ""oid"": ""MockOid"",
                        ""applicationId"": ""MockAppId"",
                        ""puid"": ""MockPuid""
                    }},
                    ""managedResourceGroupId"": ""/subscriptions/59d64684-e7c9-4397-8982-6b775a473b74/resourceGroups/MockMRG"",
                    ""parameters"": {{
                        ""enableNoPublicIp"": {{
                            ""type"": ""Bool"",
                            ""value"": false
                        }}
                    }},
                    ""provisioningState"": ""Accepted"",
                    ""updatedBy"": {{
                        ""oid"": ""MockOid"",
                        ""applicationId"": ""MockAppId"",
                        ""puid"": ""MockPuid""
                    }},
                    ""workspaceId"": ""{Constants.DBWorkspace.WorkspaceId}"",
                    ""workspaceUrl"": ""{Constants.DBWorkspace.WorkspaceUrl}"",
                    ""accessConnector"": {{
                        ""id"": ""/subscriptions/bba910f5-3a9d-4c3a-a897-696933a28884/resourceGroups/MockMRG/providers/Microsoft.Databricks/accessConnectors/unity-catalog-access-connector"",
                        ""identityType"": ""SystemAssigned""
                    }},
                    ""defaultCatalog"": {{
                        ""initialType"": ""UnityCatalog"",
                        ""initialName"": """"
                    }},
                    ""isUcEnabled"": false,
                    ""operationStatus"": {{
                        ""status"": ""Running""
                    }},
                    ""dbWorkspaceState"": ""Inactive""
                }},
                ""sku"": {{
                    ""name"": ""premium""
                }},
                ""type"": ""Microsoft.Databricks/workspaces""
            }}";


            frontDoor.Given(
                Request.Create()
                .WithPath(Constants.RequestMetadata.AccountsApiWorkspaceUrl)
                .UsingGet())
                .InScenario("Account Api")
                .WillSetStateTo("workspace put")
                .RespondWith(
                Response.Create()
                .WithHeader("Content-Type", "application/json")
                .WithStatusCode(404));
            frontDoor.Given(
                Request.Create()
                .WithPath(Constants.RequestMetadata.AccountsApiWorkspaceUrl)
                .UsingPut())
                .InScenario("Account Api")
                .WhenStateIs("workspace put")
                .WillSetStateTo("first Polling")
                .RespondWith(
                Response.Create()
                .WithHeader("Content-Type", "application/json")
                .WithStatusCode(200)
                .WithBody(applianceJson));
            frontDoor.Given(
               Request.Create()
                .WithPath(Constants.RequestMetadata.AccountsApiWorkspaceUrl)
               .UsingGet())
               .InScenario("Account Api")
               .WhenStateIs("first Polling")
               .WillSetStateTo("second Polling")
               .RespondWith(
               Response.Create()
               .WithHeader("Content-Type", "application/json")
               .WithStatusCode(200)
                .WithBody(applianceJson));
            frontDoor.Given(
               Request.Create()
               .WithPath(Constants.RequestMetadata.AccountsApiWorkspaceUrl)
               .UsingGet())
                .InScenario("Account Api")
               .WhenStateIs("second Polling")
               .RespondWith(
               Response.Create()
               .WithHeader("Content-Type", "application/json")
               .WithStatusCode(500)
                .WithBody(""));
        }
    }
}
