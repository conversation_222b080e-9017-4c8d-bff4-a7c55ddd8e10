﻿//------------------------------------------------------------
// Copyright (c) Microsoft Corporation.  All rights reserved.
//------------------------------------------------------------

namespace IntegrationTests.ResourceProvider.WorkspaceCreate.BasicWorkspace
{
    using System.Threading.Tasks;
    using IntegrationTests.ResourceProvider.BaseClasses;
    using Microsoft.Owin.Hosting;
    using Microsoft.VisualStudio.TestTools.UnitTesting;
    using RestSharp;

    [TestClass]
    public class BasicWorkspaceCreateTestWithAccountApi : BaseIntegrationTest<BasicWorkspaceCreateAsyncFrontDoorBuilder, BaseDatabaseSeeder, BaseCustomSettings>
    {
        [TestMethod]
        public async Task TestBasicWorkspaceSuccessfulCreationWithDBAccountApi()
        {
            using (var rp = KestrelHost.Start(Constants.EndPoints.RPHost))
            {
                var putClient = new RestClient(Constants.RequestMetadata.RequestUrl);
                var putRequest = new BasicWorkspaceCreateRequestBuilder().Build();
                var putResponse = await putClient.PutAsync(putRequest);
                Assert.IsNotNull(putResponse);
                Assert.AreEqual(System.Net.HttpStatusCode.Created, putResponse.StatusCode);

                await ApplianceTestHelper.WaitForCompletionAndVerifyResult(putResponse, ProvisioningState.Succeeded, string.Empty, verifySystemData: true);
            }
        }

        /// <summary>
        /// The 'systemData' property should not exist in the response when using API version 2018-04-01
        /// </summary>
        /// <returns></returns>
        [TestMethod]
        public async Task TestBasicWorkspaceSuccessfulCreationWithApi20180401AndDBAccountApi()
        {
            using var rp = KestrelHost.Start(Constants.EndPoints.RPHost);
            var putClient = new RestClient(Constants.RequestMetadata.RequestUrl);
            var putRequest = new BasicWorkspaceCreateRequestBuilder().Build();
            var putResponse = await putClient.PutAsync(putRequest);
            Assert.IsNotNull(putResponse);
            Assert.AreEqual(System.Net.HttpStatusCode.Created, putResponse.StatusCode);

            await ApplianceTestHelper.WaitForCompletionAndVerifyResult(putResponse, ProvisioningState.Succeeded, string.Empty, verifySystemData: true, useApi20180401: true);
        }
    }
}
