﻿//------------------------------------------------------------
// Copyright (c) Microsoft Corporation.  All rights reserved.
//------------------------------------------------------------

namespace IntegrationTests.ResourceProvider.WorkspaceCreate.ServerlessWorkspace
{
    using System.Threading.Tasks;
    using IntegrationTests.ResourceProvider.BaseClasses;
    using IntegrationTests.ResourceProvider.WorkspaceCreate.ServerlessWorkspace.FrontDoorBuilders;
    using IntegrationTests.ResourceProvider.WorkspaceCreate.ServerlessWorkspace.RequestBuilders;
    using Microsoft.VisualStudio.TestTools.UnitTesting;
    using RestSharp;

    [TestClass]
    public class ServerlessFeatureNotEnabledCreateAsyncTest : BaseIntegrationTest<ServerlessFeatureNotEnabledAsyncFrontDoorBuilder, BaseDatabaseSeeder, BaseCustomSettings>
    {
        [TestMethod("Serverless Workspace Creation is protected by feature Flag")]
        public async Task TestServerlessWorkspaceFeatureNotEnabledCreation()
        {
            using (var rp = KestrelHost.Start(Constants.EndPoints.RPHost))
            {
                var putClient = new RestClient(Constants.ServerlessRequestMetadata.RequestUrl);
                var putRequest = new ServerlessWorkspaceCreateRequestBuilder().Build();
                var putResponse = await putClient.ExecutePutAsync(putRequest);
                Assert.IsNotNull(putResponse);
                Assert.AreEqual(System.Net.HttpStatusCode.BadRequest, putResponse.StatusCode);
                Assert.IsTrue(putResponse.Content.Contains(ErrorResponseCode.ServerlessWorkspaceFeatureNotEnabled.ToString()));
            }
        }

        [TestMethod("Hybrid Workspace Creation will also be blocked with computeMode input without Feature Flag")]
        public async Task TestHybridWorkspaceFeatureNotEnabledCreation()
        {
            using (var rp = KestrelHost.Start(Constants.EndPoints.RPHost))
            {
                var putClient = new RestClient(Constants.ServerlessRequestMetadata.RequestUrl);
                var putRequest = new HybridWorkspaceCreateRequestBuilder().Build();
                var putResponse = await putClient.ExecutePutAsync(putRequest);
                Assert.IsNotNull(putResponse);
                Assert.AreEqual(System.Net.HttpStatusCode.BadRequest, putResponse.StatusCode);
            }
        }

        [TestMethod("Hybrid Workspace Creation will NOT be blocked without computeMode input without Feature Flag")]
        public async Task TestHybridWorkspaceWithoutComputeModeFeatureNotEnabledCreation()
        {
            using (var rp = KestrelHost.Start(Constants.EndPoints.RPHost))
            {
                var putClient = new RestClient(Constants.ServerlessRequestMetadata.RequestUrl);
                var putRequest = new HybridWorkspaceWithoutComputeModeCreateRequestBuilder().Build();
                var putResponse = await putClient.ExecutePutAsync(putRequest);
                Assert.IsNotNull(putResponse);
                Assert.AreEqual(System.Net.HttpStatusCode.BadRequest, putResponse.StatusCode);
            }
        }
    }
}
