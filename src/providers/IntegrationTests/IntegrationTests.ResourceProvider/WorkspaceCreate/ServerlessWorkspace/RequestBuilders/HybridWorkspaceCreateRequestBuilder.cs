﻿//------------------------------------------------------------
// Copyright (c) Microsoft Corporation.  All rights reserved.
//------------------------------------------------------------

namespace IntegrationTests.ResourceProvider.WorkspaceCreate.ServerlessWorkspace.RequestBuilders
{
    using IntegrationTests.ResourceProvider.BaseClasses;
    using RestSharp;

    public class HybridWorkspaceCreateRequestBuilder: BaseRequestBuilder
    {
        public override RestRequest Update(RestRequest request)
        {
            var requestBody =
                "{" +
                    "\"sku\": { \"name\": \"" + Constants.RequestBody.SkuStandard + "\" }," +
                    "\"location\": \"" + Constants.RequestBody.Location + "\"," +
                    "\"tags\": { }," +
                    "\"properties\": { \"computeMode\": \"" + Constants.RequestBody.HybridComputeMode + "\"," +
                                      "\"managedResourceGroupId\": \"" + Constants.RequestBody.ManagedResourceGroupId + "\", " +
                                      "\"parameters\": {\"customVirtualNetworkId\":{\"value\":\"testVNetId\"}," +
                                                       "\"customPublicSubnetName\":{\"value\":\"testPublicSubnetName\"}," +
                                                       "\"customPrivateSubnetName\":{\"value\":\"testPrivateSubnetName\"}" +
                                                       "}" +
                                    "}" +
                "}";

            requestBody = requestBody
                    .Replace("testVNetId", Constants.FrontDoor.VirtualNetworkUrl);

            request.AddStringBody(requestBody, DataFormat.Json);
            request.AddHeader(Constants.RequestHeaders.SystemDataHeaderKey, Constants.RequestHeaders.SystemDataHeaderValue);

            return request;
        }
    }
}
