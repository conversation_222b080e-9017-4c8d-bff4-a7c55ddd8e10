﻿//------------------------------------------------------------
// Copyright (c) Microsoft Corporation.  All rights reserved.
//------------------------------------------------------------

namespace IntegrationTests.ResourceProvider.WorkspaceCreate.ServerlessWorkspace.RequestBuilders
{
    using IntegrationTests.ResourceProvider.BaseClasses;
    using RestSharp;

    public class ServerlessWorkspaceCreateRequestBuilder: BaseRequestBuilder
    {
        public override RestRequest Update(RestRequest request)
        {
            var requestBody =
                "{" +
                    "\"sku\": { \"name\": \"" + Constants.RequestBody.SkuPremium + "\" }," +
                    "\"location\": \"" + Constants.RequestBody.Location + "\"," +
                    "\"tags\": { }," +
                    "\"properties\": { \"computeMode\": \"" + Constants.RequestBody.ServerlessComputeMode + "\" }" +
                    "}";

            request.AddStringBody(requestBody, DataFormat.Json);
            request.AddHeader(Constants.RequestHeaders.SystemDataHeaderKey, Constants.RequestHeaders.SystemDataHeaderValue);
            return request;
        }
    }
}
