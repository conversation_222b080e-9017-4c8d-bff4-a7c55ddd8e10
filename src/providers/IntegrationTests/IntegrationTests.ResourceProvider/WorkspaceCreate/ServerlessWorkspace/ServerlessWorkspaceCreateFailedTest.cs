﻿//------------------------------------------------------------
// Copyright (c) Microsoft Corporation.  All rights reserved.
//------------------------------------------------------------

namespace IntegrationTests.ResourceProvider.WorkspaceCreate.ServerlessWorkspace
{
    using System.Net;
    using System.Threading.Tasks;
    using IntegrationTests.ResourceProvider.BaseClasses;
    using IntegrationTests.ResourceProvider.WorkspaceCreate.ServerlessWorkspace.FrontDoorBuilders;
    using IntegrationTests.ResourceProvider.WorkspaceCreate.ServerlessWorkspace.RequestBuilders;
    using Microsoft.VisualStudio.TestTools.UnitTesting;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Common.Constants;
    using RestSharp;

    [TestClass]
    public class ServerlessWorkspaceCreateFailedTest : BaseIntegrationTest<ServerlessWorkspaceDBEmptyReturnFrontDoorBuilder, BaseDatabaseSeeder, BaseCustomSettings>
    {
        [TestMethod]
        public async Task TestServerlessWorkspaceCreation_DBEmptyReturn_Failed()
        {
            using (var rp = KestrelHost.Start(Constants.EndPoints.RPHost))
            {
                var putClient = new RestClient(Constants.ServerlessRequestMetadata.RequestUrl);
                var putRequest = new ServerlessWorkspaceCreateRequestBuilder().Build();
                var putResponse = await putClient.PutAsync(putRequest);
                Assert.IsNotNull(putResponse);
                Assert.AreEqual(HttpStatusCode.Created, putResponse.StatusCode);

                await ApplianceTestHelper.WaitForCompletionAndVerifyResult(
                    putResponse,
                    ProvisioningState.Failed,
                    string.Empty,
                    verifySystemData: true,
                    isServerless: true,
                    apiVersion: ProviderConstants.ApiVersion20250601Preview);
            }
        }
    }
}
