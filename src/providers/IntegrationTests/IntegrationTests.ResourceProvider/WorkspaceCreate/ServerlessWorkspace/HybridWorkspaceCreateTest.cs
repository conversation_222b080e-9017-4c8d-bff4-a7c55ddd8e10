﻿//------------------------------------------------------------
// Copyright (c) Microsoft Corporation.  All rights reserved.
//------------------------------------------------------------

namespace IntegrationTests.ResourceProvider.WorkspaceCreate.ServerlessWorkspace
{
    using System.Net;
    using System.Threading.Tasks;
    using IntegrationTests.ResourceProvider.BaseClasses;
    using IntegrationTests.ResourceProvider.WorkspaceCreate.ServerlessWorkspace.FrontDoorBuilders;
    using IntegrationTests.ResourceProvider.WorkspaceCreate.ServerlessWorkspace.RequestBuilders;
    using Microsoft.VisualStudio.TestTools.UnitTesting;
    using RestSharp;

    [TestClass]
    public class HybridWorkspaceCreateTest : BaseIntegrationTest<HybridWorkspaceFrontDoorBuilder, BaseDatabaseSeeder, BaseCustomSettings>
    {
        [TestMethod]
        public async Task TestHybridWorkspaceFailedCreation()
        {
            using (var rp = KestrelHost.Start(Constants.EndPoints.RPHost))
            {
                var putClient = new RestClient(Constants.ServerlessRequestMetadata.RequestUrl);
                var putRequest = new HybridWorkspaceCreateRequestBuilder().Build();
                var putResponse = await putClient.PutAsync(putRequest);
                Assert.IsNotNull(putResponse);
                Assert.AreEqual(HttpStatusCode.Created, putResponse.StatusCode);

                await ApplianceTestHelper.WaitForCompletionAndVerifyResult(putResponse, ProvisioningState.Failed, string.Empty, verifySystemData: false);
            }
        }
    }
}
