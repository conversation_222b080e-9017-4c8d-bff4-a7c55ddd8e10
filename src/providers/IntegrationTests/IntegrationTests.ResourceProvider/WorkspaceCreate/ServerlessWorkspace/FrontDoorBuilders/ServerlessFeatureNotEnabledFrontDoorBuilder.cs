﻿//------------------------------------------------------------
// Copyright (c) Microsoft Corporation.  All rights reserved.
//------------------------------------------------------------

namespace IntegrationTests.ResourceProvider.WorkspaceCreate.ServerlessWorkspace.FrontDoorBuilders
{
    using System.Net;
    using IntegrationTests.ResourceProvider.BaseClasses;
    using WireMock.RequestBuilders;
    using WireMock.ResponseBuilders;
    using WireMock.Server;

    public class ServerlessFeatureNotEnabledFrontDoorBuilder : IWireMockServerBuilder
    {
        public WireMockServer Build()
        {
            var frontDoor = WireMockServer.StartWithAdminInterface(Constants.EndPoints.FrontDoorPort);

            frontDoor.AllowPartialMapping(false);

            frontDoor.Given(
                Request.Create()
                .WithPath(Constants.FrontDoor.FeaturesUrl)
                .UsingGet()
            ).RespondWith(
                Response.Create()
                .WithHeader("Content-Type", "application/json")
                .WithStatusCode(200)
                .WithBody("{ \"value\":[] }"));


            var registerTenantUrl = $@"/subscriptions/{Constants.RequestMetadata.SubscriptionId}/providers/Microsoft.Resources/tenants/{Constants.Settings.PublisherTenantId}/register";

            frontDoor.Given(
                Request.Create()
                .WithPath(registerTenantUrl)
                .UsingPost()
            ).RespondWith(
                Response.Create()
                .WithHeader("Content-Type", "application/json")
                .WithStatusCode(200)
                .WithBody(""));


            var workspaceInitializationUrl = $@"/{Constants.RequestMetadata.ResourceId}/dbworkspaces/dbworkspace";

            var workspaceDetails =
             "{" +
                "\"WorkspaceId\":\"" + Constants.DBWorkspace.WorkspaceId + "\"," +
                "\"WorkspaceURL\":\"" + Constants.DBWorkspace.WorkspaceUrl + "\"," +
                "\"isPrivateLinkAllowed\":false," +
                "\"isUcEnabled\":false" +
             "}";

            frontDoor.Given(
                Request.Create()
                .WithPath(workspaceInitializationUrl)
                .UsingPut()
            ).RespondWith(
                Response.Create()
                .WithHeader("Content-Type", "application/json")
                .WithStatusCode(HttpStatusCode.OK)
                .WithBody(workspaceDetails));

            var serverlessWorkspaceFeatureResponse =
"{" +
    "\"properties\": { \"state\": \"NotRegistered\" }, " +
    "\"id\": \"/subscriptions/12345678-abcd-abcd-abcd-ba9876543b10/providers/Microsoft.Features/providers/Microsoft.Databricks/features/EnableServerlessWorkspaceFeature\"," +
    "\"type\": \"Microsoft.Features/providers/features\"," +
    "\"name\": \"Microsoft.Databricks/EnableServerlessWorkspaceFeature\"" +
"}";

            frontDoor.Given(
                Request.Create()
                .WithPath(Constants.FrontDoor.EnableServerlessWorkspaceFeatureUrl)
                .UsingGet()
            ).RespondWith(
                Response.Create()
                .WithHeader("Content-Type", "application/json")
                .WithStatusCode(200)
                .WithBody(serverlessWorkspaceFeatureResponse));

            return frontDoor;
        }
    }
}
