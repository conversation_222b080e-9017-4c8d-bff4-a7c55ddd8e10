using System.Reflection;
using System.Runtime.CompilerServices;
using System.Runtime.InteropServices;

[assembly: AssemblyTitle("IntegrationTests.ResourceProvider")]
[assembly: AssemblyDescription("")]
[assembly: AssemblyConfiguration("")]
[assembly: AssemblyCompany("Microsoft IT")]
[assembly: AssemblyProduct("IntegrationTests.ResourceProvider")]
[assembly: AssemblyCopyright("Copyright © Microsoft IT 2022")]
[assembly: AssemblyTrademark("")]
[assembly: AssemblyCulture("")]

[assembly: ComVisible(false)]

[assembly: Guid("2a651fd0-77b5-4961-a5dd-ad41e2d9499e")]

// [assembly: AssemblyVersion("1.0.*")]
[assembly: AssemblyVersion("*******")]
[assembly: AssemblyFileVersion("*******")]
