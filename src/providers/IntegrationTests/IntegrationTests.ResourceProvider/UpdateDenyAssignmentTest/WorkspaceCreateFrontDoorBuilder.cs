﻿//------------------------------------------------------------
// Copyright (c) Microsoft Corporation.  All rights reserved.
//------------------------------------------------------------

namespace IntegrationTests.ResourceProvider.UpdateDenyAssignmentTest
{
    using System;
    using System.Net;
    using IntegrationTests.ResourceProvider.BaseClasses;
    using WireMock.RequestBuilders;
    using WireMock.ResponseBuilders;
    using WireMock.Server;

    public class WorkspaceCreateFrontDoorBuilder : IWireMockServerBuilder
    {
        public WireMockServer Build()
        {
            var frontDoor = WireMockServer.StartWithAdminInterface(Constants.EndPoints.FrontDoorPort);

            frontDoor.AllowPartialMapping(false);

            var EnableManagedByTenantApiVersionFeatureResponse =
                "{" +
                    "\"properties\": { \"state\": \"NotRegistered\" }, " +
                    "\"id\": \"/subscriptions/********-abcd-abcd-abcd-ba9876543b10/providers/Microsoft.Features/providers/Microsoft.Databricks/features/EnableManagedByTenantApiVersion20190301\"," +
                    "\"type\": \"Microsoft.Features/providers/features\"," +
                    "\"name\": \"Microsoft.Databricks/EnableManagedByTenantApiVersion20190301\"" +
                "}";


            frontDoor.Given(
                Request.Create()
                .WithPath(Constants.FrontDoor.EnableManagedByTenantApiVersionFeatureUrl)
                .UsingGet()
            ).RespondWith(
                Response.Create()
                .WithHeader("Content-Type", "application/json")
                .WithStatusCode(200)
                .WithBody(EnableManagedByTenantApiVersionFeatureResponse));


            var registerTenantUrl = $@"/subscriptions/{Constants.RequestMetadata.SubscriptionId}/providers/Microsoft.Resources/tenants/{Constants.Settings.PublisherTenantId}/register";

            frontDoor.Given(
                Request.Create()
                .WithPath(registerTenantUrl)
                .UsingPost()
            ).RespondWith(
                Response.Create()
                .WithHeader("Content-Type", "application/json")
                .WithStatusCode(200)
                .WithBody(""));

            frontDoor.Given(
                Request.Create()
                .WithPath(Constants.RequestBody.ManagedResourceGroupId)
                .UsingGet()
            ).RespondWith(
                Response.Create()
                .WithStatusCode(HttpStatusCode.NotFound)
                .WithBody(""));

            frontDoor.Given(
                Request.Create()
                .WithPath(Constants.RequestBody.ManagedResourceGroupId)
                .UsingPut()
            ).RespondWith(
                Response.Create()
                .WithStatusCode(HttpStatusCode.Created)
                .WithBody(""));

            var dbfsRoleAssignentsUrl = $@"/{Constants.RequestBody.ManagedResourceGroupId}/providers/Microsoft.Storage/storageAccounts//providers/Microsoft.Authorization/roleAssignments";

            frontDoor.Given(
                Request.Create()
                .WithPath(dbfsRoleAssignentsUrl)
                .UsingGet()
            ).RespondWith(
                Response.Create()
                .WithHeader("Content-Type", "application/json")
                .WithStatusCode(HttpStatusCode.OK)
                .WithBody("{ \"value\":[] }"));

            var dbfsRoleAssignmentsCreateUrl = $@"{Constants.RequestBody.ManagedResourceGroupId}/providers/Microsoft.Storage/storageAccounts/providers/Microsoft.Authorization/roleAssignments/*";

            frontDoor.Given(
                Request.Create()
                .WithPath(dbfsRoleAssignmentsCreateUrl)
                .UsingPut()
            ).RespondWith(
                Response.Create()
                .WithHeader("Content-Type", "application/json")
                .WithStatusCode(HttpStatusCode.Created)
                .WithBody(""));

            var roleAssignentsUrl = $@"/{Constants.RequestBody.ManagedResourceGroupId}/providers/Microsoft.Authorization/roleAssignments";

            frontDoor.Given(
                Request.Create()
                .WithPath(roleAssignentsUrl)
                .UsingGet()
            ).RespondWith(
                Response.Create()
                .WithHeader("Content-Type", "application/json")
                .WithStatusCode(HttpStatusCode.OK)
                .WithBody("{ \"value\":[] }"));


            var roleAssignmentsCreateUrl = $@"{Constants.RequestBody.ManagedResourceGroupId}/providers/Microsoft.Authorization/roleAssignments/*";

            frontDoor.Given(
                Request.Create()
                .WithPath(roleAssignmentsCreateUrl)
                .UsingPut()
            ).RespondWith(
                Response.Create()
                .WithHeader("Content-Type", "application/json")
                .WithStatusCode(HttpStatusCode.Created)
                .WithBody(""));


            var denyAssignmentsUrl = $@"/{Constants.RequestBody.ManagedResourceGroupId}/providers/Microsoft.Authorization/denyAssignments";

            var getDenyAssignmentsResponse = "{ " +
                    " \"value\": [ " +
                        "{" +
                            "\"properties\": { " +
                                "\"denyAssignmentName\": \"System deny assignment created by Azure Databricks /subscriptions/********-abcd-abcd-abcd-ba9876543b10/resourceGroups/testrg/providers/Microsoft.Databricks/workspaces/testws\", " +
                                "\"description\": \"System deny assignment created by Azure Databricks '/subscriptions/********-abcd-abcd-abcd-ba9876543b10/resourceGroups/testrg/providers/Microsoft.Databricks/workspaces/testws' in scope: '/subscriptions/********-abcd-abcd-abcd-ba9876543b10/resourceGroups/databricks-rg-ws-ht7wqfthjp5ei'\"," +
                                "\"permissions\": [ " +
                                    "{" +
                                        "\"actions\": [ " +
                                            "\"*\" " +
                                        "], " +
                                        "\"notActions\": [ " +
                                            "\"*/read\", " +
                                            "\"Microsoft.Network/virtualNetworks/peer/action\", " +
                                            "\"Microsoft.Security/advancedThreatProtectionSettings/*\", " +
                                            "\"Microsoft.Storage/storageAccounts/services/diagnosticSettings/*\", " +
                                            "\"Microsoft.Authorization/policyExemptions/*\", " +
                                            "\"Microsoft.Insights/diagnosticsettings/*\", " +
                                            "\"Microsoft.CostManagement/*\", " +
                                            "\"Microsoft.Consumption/*\", " +
                                            "\"Microsoft.Storage/storageAccounts/privateEndpointConnections/*\", " +
                                            "\"Microsoft.Storage/storageAccounts/PrivateEndpointConnectionsApproval/action\", " +
                                            "\"Microsoft.Authorization/roleAssignments/write\", " +
                                            "\"Microsoft.Security/locations/alerts/dismiss/action\", " +
                                            "\"Microsoft.Advisor/*\", " +
                                            "\"Microsoft.Security/DefenderForStorageSettings/write\" " +
                                        "], " +
                                        "\"dataActions\": [ " +
                                            "\"*\" " +
                                        "], " +
                                        "\"notDataActions\": [] " +
                                    "} " +
                                "], " +
                                "\"scope\": \"/subscriptions/********-abcd-abcd-abcd-ba9876543b10/resourceGroups/databricks-rg-ws-ht7wqfthjp5ei\", " +
                                "\"doNotApplyToChildScopes\": false, " +
                                "\"principals\": [ " +
                                    "{ " +
                                        "\"id\": \"00000000-0000-0000-0000-000000000000\", " +
                                        "\"type\": \"SystemDefined\" " +
                                    "}" +
                                "], " +
                                "\"excludePrincipals\": [], " +
                                "\"condition\": \"@Subject[ResourceId] StringNotStartsWithIgnoreCase '/subscriptions/********-abcd-abcd-abcd-ba9876543b10/resourceGroups/databricks-rg-ws-ht7wqfthjp5ei' && @Subject[tid] StringNotEqualsAnyOfIgnoreCase {'2f4a9838-26b7-47ee-be60-ccc1fdec5953'}\", " +
                                "\"conditionVersion\": \"1.0\", " +
                                "\"isSystemProtected\": true, " +
                                "\"createdOn\": \"2022-09-21T16:14:41.2796876Z\", " +
                                "\"updatedOn\": \"2022-09-21T16:14:40.9830237Z\", " +
                                "\"createdBy\": \"33fc06ec-61b1-450d-94f0-353824389ca2\", " +
                                "\"updatedBy\": \"33fc06ec-61b1-450d-94f0-353824389ca2\" " +
                            "}, " +
                            "\"id\": \"/subscriptions/********-abcd-abcd-abcd-ba9876543b10/resourceGroups/databricks-rg-ws-ht7wqfthjp5ei/providers/Microsoft.Authorization/denyAssignments/751aa58a-a7bd-404a-8d72-260b3fd6ce57\", " +
                            "\"type\": \"Microsoft.Authorization/denyAssignments\", " +
                            "\"name\": \"751aa58a-a7bd-404a-8d72-260b3fd6ce57\" " +
                        "} " +
                    "] " +
                "}";



            frontDoor.Given(
                Request.Create()
                .WithPath(denyAssignmentsUrl)
                .UsingGet()
            )
            .RespondWith(
                Response.Create()
                .WithHeader("Content-Type", "application/json")
                .WithStatusCode(HttpStatusCode.OK)
                .WithBody(getDenyAssignmentsResponse));


            var denyAssignmentsCreateUrl = $@"{Constants.RequestBody.ManagedResourceGroupId}/providers/Microsoft.Authorization/denyAssignments/*";

            frontDoor.Given(
                Request.Create()
                .WithPath(denyAssignmentsCreateUrl)
                .UsingPut()
            ).RespondWith(
                Response.Create()
                .WithHeader("Content-Type", "application/json")
                .WithStatusCode(HttpStatusCode.Created)
                .WithBody(""));

            frontDoor.Given(
                Request.Create()
                .WithPath(denyAssignmentsCreateUrl)
                .WithParam("notActions", "*/read;*/read;Microsoft.Resources/subscriptions/resourceGroups/delete")
                .UsingPut()
            ).RespondWith(
                Response.Create()
                .WithHeader("Content-Type", "application/json")
                .WithStatusCode(HttpStatusCode.BadRequest)
                .WithBody(""));

            frontDoor.Given(
                Request.Create()
                .WithPath(denyAssignmentsCreateUrl)
                .WithParam("notActions", "Microsoft.Resources/subscriptions/resourceGroups/delete")
                .UsingPut()
            ).RespondWith(
                Response.Create()
                .WithHeader("Content-Type", "application/json")
                .WithStatusCode(HttpStatusCode.BadRequest)
                .WithBody(""));

            frontDoor.Given(
                Request.Create()
                .WithPath(denyAssignmentsCreateUrl)
                .WithParam("notActions", "*/read;##1;Microsoft.Resources/subscriptions/resourceGroups/delete")
                .UsingPut()
            ).RespondWith(
                Response.Create()
                .WithHeader("Content-Type", "application/json")
                .WithStatusCode(HttpStatusCode.BadRequest)
                .WithBody(""));

            frontDoor.Given(
                Request.Create()
                .WithPath(denyAssignmentsCreateUrl)
                .WithParam("notActions", "*/read;*/*;Microsoft.Resources/subscriptions/resourceGroups/delete")
                .UsingPut()
            ).RespondWith(
                Response.Create()
                .WithHeader("Content-Type", "application/json")
                .WithStatusCode(HttpStatusCode.BadRequest)
                .WithBody(""));

            frontDoor.Given(
                Request.Create()
                .WithPath(denyAssignmentsCreateUrl)
                .WithParam("notActions", "*/read;Microsoft.Resources/subscriptions/resourceGroups/invalid;Microsoft.Resources/subscriptions/resourceGroups/delete")
                .UsingPut()
            ).RespondWith(
                Response.Create()
                .WithHeader("Content-Type", "application/json")
                .WithStatusCode(HttpStatusCode.BadRequest)
                .WithBody(""));

            frontDoor.Given(
                Request.Create()
                .WithPath(denyAssignmentsCreateUrl)
                .WithParam("notActions", "*/read;Microsoft.Resources/subscriptions/a*/resourceGroups/delete;Microsoft.Resources/subscriptions/resourceGroups/delete")
                .UsingPut()
            ).RespondWith(
                Response.Create()
                .WithHeader("Content-Type", "application/json")
                .WithStatusCode(HttpStatusCode.BadRequest)
                .WithBody(""));

            frontDoor.Given(
                Request.Create()
                .WithPath(denyAssignmentsCreateUrl)
                .WithParam("notActions", "*/read;*/delete;Microsoft.Resources/subscriptions/resourceGroups/delete")
                .UsingPut()
            ).RespondWith(
                Response.Create()
                .WithHeader("Content-Type", "application/json")
                .WithStatusCode(HttpStatusCode.BadRequest)
                .WithBody(""));


            var deploymentCreateUrl = $@"{Constants.RequestBody.ManagedResourceGroupId}/providers/Microsoft.Resources/deployments/testws";

            var deploymentResponse =
            "{" +
                "\"id\": \"" + Guid.NewGuid().ToString() + "\"," +
                "\"name\": \"testws\"," +
                "\"properties\":{" +
                    "\"templateLink\":{" +
                        "\"uri\":\"http://localhost\"," +
                        "\"contentVersion\":\"*******\"" +
                    "}," +
                    "\"parameters\":{" +
                        "\"resourceTags\":{" +
                            "\"value\":{" +
                                "\"application\":\"databricks\"," +
                                "\"databricks-environment\":\"true\"" +
                            "}" +
                        "}" +
                    "}," +
                    "\"mode\":\"Complete\"," +
                    "\"securityMode\":\"Secured\"," +
                    "\"outputs\": {" +
                        "\"ucConnectorPrincipalId\": {" +
                            "\"value\": \"" + Guid.NewGuid().ToString() + "\"" +
                        "}," +
                        "\"ucConnectorTenantId\": {" +
                            "\"value\": \"" + Guid.NewGuid().ToString() + "\"" +
                        "}" +
                    "}" +
                "}" +
            "}";

            var deploymentTrackingUrl = $@"{Constants.RequestBody.ManagedResourceGroupId}/providers/Microsoft.Resources/deployments/{Constants.RequestMetadata.Name}/operationStatuses/08585353991691056296";

            frontDoor.Given(
                Request.Create()
                .WithPath(deploymentCreateUrl)
                .UsingPut()
            ).RespondWith(
                Response.Create()
                .WithHeader("Content-Type", "application/json")
                .WithStatusCode(HttpStatusCode.Created)
                .WithHeader("Azure-AsyncOperation", Constants.EndPoints.FrontDoorHost + deploymentTrackingUrl)
                .WithBody(deploymentResponse));

            frontDoor.Given(
                Request.Create()
                .WithPath(deploymentCreateUrl)
                .UsingGet()
            ).RespondWith(
                Response.Create()
                .WithHeader("Content-Type", "application/json")
                .WithStatusCode(HttpStatusCode.OK)
                .WithBody(deploymentResponse));


            frontDoor.Given(
                Request.Create()
                .WithPath(deploymentTrackingUrl)
                .UsingGet()
            ).RespondWith(
                Response.Create()
                .WithHeader("Content-Type", "application/json")
                .WithStatusCode(HttpStatusCode.OK)
                .WithBody("{ \"status\":\"Succeeded\" }"));

            var workspaceInitializationUrl = $@"/{Constants.RequestMetadata.ResourceId}/dbworkspaces/dbworkspace";

            var workspaceDetails =
             "{" +
                "\"WorkspaceId\":\"" + Constants.DBWorkspace.WorkspaceId + "\"," +
                "\"WorkspaceURL\":\"" + Constants.DBWorkspace.WorkspaceUrl + "\"," +
                "\"isPrivateLinkAllowed\":false," +
                "\"isUcEnabled\":false" +
             "}";

            frontDoor.Given(
                Request.Create()
                .WithPath(workspaceInitializationUrl)
                .UsingPut()
            ).RespondWith(
                Response.Create()
                .WithHeader("Content-Type", "application/json")
                .WithStatusCode(HttpStatusCode.OK)
                .WithBody(workspaceDetails));

            return frontDoor;
        }
    }
}
