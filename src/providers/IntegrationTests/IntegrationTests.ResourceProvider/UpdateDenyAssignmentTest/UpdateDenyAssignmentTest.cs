﻿//------------------------------------------------------------
// Copyright (c) Microsoft Corporation.  All rights reserved.
//------------------------------------------------------------

namespace IntegrationTests.ResourceProvider.UpdateDenyAssignmentTest
{
    using System.Net;
    using System.Threading.Tasks;
    using IntegrationTests.ResourceProvider.BaseClasses;
    using Microsoft.Owin.Hosting;
    using Microsoft.VisualStudio.TestTools.UnitTesting;
    using RestSharp;

    [TestClass]
    public class UpdateDenyAssignmentTest : BaseIntegrationTest<WorkspaceCreateFrontDoorBuilder, GetWorkspaceDatabaseSeeder, BaseCustomSettings>
    {
        public async Task UpdateDenyAssignment_Tests(string notActions, HttpStatusCode expectedStatusCode)
        {
            using (var rp = KestrelHost.Start(Constants.EndPoints.RPHost))
            {
                // Checking Update Deny Assignment
                var updateDenyAssignmentputClient = new RestClient(Constants.UpdateDenyAssignmentRequestMetadata.RequestUrl);
                var denyAssignmentRequestBuilder = new DenyAssignmentRequestBuilder(notActions);
                var updateDenyAssignmentRequest = denyAssignmentRequestBuilder.Build();
                var updateDenyAssignmentResponse = await updateDenyAssignmentputClient.ExecutePostAsync(updateDenyAssignmentRequest);

                Assert.AreEqual(updateDenyAssignmentResponse.StatusCode, expectedStatusCode);

            }
        }

        /// <summary>
        /// Test Update Deny Assignment endpoint with success
        /// </summary>
        [TestMethod]
        public async Task UpdateDenyAssignment_Success()
        {
            await UpdateDenyAssignment_Tests(
                "*/read;Microsoft.Resources/subscriptions/resourceGroups/delete",
                HttpStatusCode.OK);
        }

        /// <summary>
        /// Test Update Deny Assignment endpoint with fails Invalid DenyAssignment Id
        /// </summary>
        [TestMethod]
        public async Task UpdateDenyAssignment_Validation_Fails_InvalidAllowedCustomerNotAction_DenyAssignmentDuplicateExclusionAction()
        {
            await this.UpdateDenyAssignment_Tests(
                "*/read;*/read;Microsoft.Resources/subscriptions/resourceGroups/delete",
                HttpStatusCode.BadRequest);
        }

        /// <summary>
        /// Test Update Deny Assignment endpoint with fails Missing Read Only Exclusion Action
        /// </summary>
        [TestMethod]
        public async Task UpdateDenyAssignment_Validation_Fails_InvalidAllowedCustomerNotAction_DenyAssignmentMissingReadOnlyExclusionAction()
        {
            await this.UpdateDenyAssignment_Tests(
                "Microsoft.Resources/subscriptions/resourceGroups/delete",
                HttpStatusCode.BadRequest);
        }

        /// <summary>
        /// Test Update Deny Assignment endpoint with fails Multiple Wild cards
        /// </summary>
        [TestMethod]
        public async Task UpdateDenyAssignment_Validation_Fails_InvalidAllowedCustomerNotAction_DenyAssignmentExclusionActionMultipleWildCards()
        {
            await this.UpdateDenyAssignment_Tests(
                "*/read;*/*;Microsoft.Resources/subscriptions/resourceGroups/delete",
                HttpStatusCode.BadRequest);
        }

        /// <summary>
        /// Test Update Deny Assignment endpoint with fails Invalid Action Verb
        /// </summary>
        [TestMethod]
        public async Task UpdateDenyAssignment_Validation_Fails_InvalidAllowedCustomerNotAction_DenyAssignmentExclusionInvalidActionVerb()
        {
            await this.UpdateDenyAssignment_Tests(
                "*/read;Microsoft.Resources/subscriptions/resourceGroups/invalid;Microsoft.Resources/subscriptions/resourceGroups/delete",
                HttpStatusCode.BadRequest);
        }

        /// <summary>
        /// Test Update Deny Assignment endpoint with fails Partial Wild Card
        /// </summary>
        [TestMethod]
        public async Task UpdateDenyAssignment_Validation_Fails_InvalidAllowedCustomerNotAction_DenyAssignmentExclusionActionPartialWildCard()
        {
            await this.UpdateDenyAssignment_Tests(
                "*/read;Microsoft.Resources/subscriptions/a*/resourceGroups/delete;Microsoft.Resources/subscriptions/resourceGroups/delete",
                HttpStatusCode.BadRequest);
        }

        /// <summary>
        /// Test Update Deny Assignment endpoint with fails Leading Wild Card
        /// </summary>
        [TestMethod]
        public async Task UpdateDenyAssignment_Validation_Fails_InvalidAllowedCustomerNotAction_DenyAssignmentExclusionActionLeadingWildCard()
        {
            await this.UpdateDenyAssignment_Tests(
                "*/read;*/delete;Microsoft.Resources/subscriptions/resourceGroups/delete",
                HttpStatusCode.BadRequest);
        }

        /// <summary>
        /// Test Update Deny Assignment endpoint with fails Invalid Characters
        /// </summary>
        [TestMethod]
        public async Task UpdateDenyAssignment_Validation_Fails_InvalidAllowedCustomerNotAction_DenyAssignmentExclusionActionInvalidCharacters()
        {
            await this.UpdateDenyAssignment_Tests(
                "*/read;##1;Microsoft.Resources/subscriptions/resourceGroups/delete",
                HttpStatusCode.BadRequest);
        }

    }
}
