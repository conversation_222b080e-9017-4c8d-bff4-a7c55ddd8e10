﻿//------------------------------------------------------------
// Copyright (c) Microsoft Corporation.  All rights reserved.
//------------------------------------------------------------

namespace IntegrationTests.ResourceProvider.UpdateDenyAssignmentTest
{
    using IntegrationTests.ResourceProvider.BaseClasses;
    using RestSharp;

    public class DenyAssignmentRequestBuilder : BaseRequestBuilder
    {
        private readonly string RequestBody;

        public DenyAssignmentRequestBuilder (string requestBody)
        {
            this.RequestBody = requestBody;
        }

        public override RestRequest Update(RestRequest request )
        {
            var requestBody =
                "{" +
                    "\"notActions\": \"" + this.RequestBody + "\" " +
                "}";

            request.AddStringBody(requestBody, DataFormat.Json);
            return request;
        }
    }
}
