﻿//------------------------------------------------------------
// Copyright (c) Microsoft Corporation.  All rights reserved.
//------------------------------------------------------------

namespace IntegrationTests.ResourceProvider.UpdateDenyAssignmentTest
{
    using System.Collections.Generic;
    using IntegrationTests.ResourceProvider.BaseClasses;

    public class GetWorkspaceDatabaseSeeder : BaseDatabaseSeeder
    {
        public override List<string> UpdateRequestBody()
        {
            var bodies = new List<string>();

            bodies.Add(
                " {" +
                    "\"PartitionKey\": \"AEB88\"," +
                    "\"RowKey\": \"12345678ABCDABCDABCDBA9876543B10_PLAN-2018:2D04:2D01-TESTWS\"," +
                     "\"CreatedTime\": \"2022-10-26T17:49:51.142Z\"," +
                    "\"<EMAIL>\": \"Edm.DateTime\"," +
                    "\"ChangedTime\": \"2022-10-26T17:49:57.033Z\"," +
                    "\"<EMAIL>\": \"Edm.DateTime\"," +
                    "\"DeletedTime\": \"1970-01-01T00:00:00.000Z\"," +
                    "\"<EMAIL>\": \"Edm.DateTime\"," +
                    "\"ChangedOperationId\": \"562d4a00-2295-4e21-9355-3c68bb2cd68f\"," +
                    "\"SubscriptionId\": \"12345678-abcd-abcd-abcd-ba9876543b10\"," +
                    "\"ResourceGroup\": \"testrg\"," +
                    "\"Name\": \"testws\"," +
                    "\"Sku\": \"{\\\"name\\\": \\\"Premium\\\"}\"," +
                    "\"Location\": \"wakandacentral\"," +
                    "\"Tags\": \"{\\\"team\\\": \\\"adb\\\"}\"," +
                    "\"Metadata\": \"XY2xCkIxDEX/JbPo/jZBcFEH/YKYRim1TUlSHo/Sf7c4ul04nHM7ZCz45nBnk6bEZ5VWL0LphplhAWfz1WAHgetHtszF/4lUVvQo5eHozdhg6XCNpGLy8v0JHZ8aKdlhFU1WkXhOjT77HewnzacjEVfnAGOMLw==\"," +
                    "\"<EMAIL>\": \"Edm.Binary\"," +
                    "\"PropertiesV2\": \"TY5JDsIwDEXvknWjzgPsYIPYIThBBtOGQhvshApVvTsBCakbS/b/8nsze4hBtKDPQKNHBQccvT1qtmUxeUkKjXVmHChOs7woq7rhQiq9GlJsmroqi1ymSYzrLxRr4YREo3ri2PKJeOfq6Xl13c2WYFjErJd3Qx3gSag+aPzAWZI2PCl4kn4bOL4MBQMztBcnHASznVJgHegQK4Rw0/s3285LxLwNyP+6fAA=\"," +
                    "\"<EMAIL>\": \"Edm.Binary\"" +
                " }");

            bodies.Add(
                "{ " +
                    "\"PartitionKey\": \"AEB88\"," +
                    "\"RowKey\": \"12345678ABCDABCDABCDBA9876543B10_RGA-TESTRG-TESTWS\"," +
                    "\"CreatedTime\": \"2022-10-26T17:49:51.142Z\"," +
                    "\"<EMAIL>\": \"Edm.DateTime\"," +
                    "\"ChangedTime\": \"2022-10-26T17:49:57.033Z\"," +
                    "\"<EMAIL>\": \"Edm.DateTime\"," +
                    "\"DeletedTime\": \"1970-01-01T00:00:00.000Z\"," +
                    "\"<EMAIL>\": \"Edm.DateTime\"," +
                    "\"ChangedOperationId\": \"562d4a00-2295-4e21-9355-3c68bb2cd68f\"," +
                    "\"SubscriptionId\": \"12345678-abcd-abcd-abcd-ba9876543b10\"," +
                    "\"ResourceGroup\": \"testrg\"," +
                    "\"Name\": \"testws\"," +
                    "\"Sku\": \"{\\\"name\\\": \\\"Premium\\\"}\"," +
                    "\"Location\": \"wakandacentral\"," +
                    "\"Tags\": \"{\\\"team\\\": \\\"adb\\\"}\"," +
                    "\"Metadata\": \"XZDdboMwDIXfJddlgdJQ4K7SpN1007SfBwjBQAYlmW2KpqrvvnRb2bQby5J97HO+kzjoUbdQPwG5CQ3coZv83pn+QR9AlIKBeCaxEjX4wX0cYOT/E+cBNVs3PrPmiYBEeRL31qAj1/DNrWZdoTU9ydlhT14bCC1aDvdPgr5E4dPOGPAMdfi1XHxBbXo7tq9ow0bH7EspB2f00DniMo/jRNJUkUHrLw5IJut0o7JtHunK1H9KpYt8m6lNWiWxxJ+w7SUsyXpxGGEbzRR1vJ3fG+7evAIrPbqjrQFJ/oa64grihQvJb1hysX8FIuNc5SpVaVEkWZHEKlsXmTifV8LSI9qjZtjbsd8Ng5sDgLLRA8H5Ew==\"," +
                    "\"<EMAIL>\": \"Edm.Binary\"," +
                    "\"PropertiesV2\": \"XZHLbtswEEX/hevQlizqkSyLAEF2QdOsgi6G5EhmLVPskLTRGPr3jpSHi24IDjj33DvDiziChwHtd4xTJoMPNOXwaMWd2MasoyEXkpt83Ja7StVN20nQxv5zaLjt2qZWlS6LLX1QhoUStxYSaHLmECUN8hzlPrXn333a/wo1OnEjQtaji3ukJzAHjrEa74qyk4WSRbl0AMERE1IUdxfxyf8Bw1qfYMy4PEAIozOwROXoV18mXAuJ/uRo8kf0ibsSsXaeZzah6eQia50fnhMkRornbAyiRcsIyGk/kXtb+Wz8emGJ88YFGNfIFa8AqrKUqsZeqhYKqVVnpYWubpRWtjKKOTSNeI+9824Juio7rKBv6lZC17NSVUZCWxu567EzShvdVLWYf94IQ8jB7Lc/PC5HzoHnupbniQ4xgHnf4Odf3RbvN7b+anihkacDq+X/XZtyA2+Z8Lqwjcck5r8=\"," +
                    "\"<EMAIL>\": \"Edm.Binary\"" +
                " }");
            return bodies;
        }
    }
}
