﻿//------------------------------------------------------------
// Copyright (c) Microsoft Corporation.  All rights reserved.
//------------------------------------------------------------

namespace IntegrationTests.ResourceProvider.BaseClasses
{
    using System;
    using System.Threading.Tasks;
    using Microsoft.VisualStudio.TestTools.UnitTesting;
    using Microsoft.WindowsAzure.ResourceStack.Common.Extensions;
    using Microsoft.WindowsAzure.ResourceStack.Common.Services;
    using Microsoft.WindowsAzure.ResourceStack.Common.Storage;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Common.Logging;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Worker.Dispatcher;
    using Microsoft.WindowsAzure.Storage;
    using Microsoft.WindowsAzure.Storage.Queue;
    using Microsoft.WindowsAzure.Storage.Table;
    using WireMock.Server;

    [TestClass]
    public class BaseIntegrationTest<FrontDoorBuilder, DatabaseSeeder, CustomSettings>
        where FrontDoorBuilder : IWireMockServerBuilder, new()
        where DatabaseSeeder : BaseDatabaseSeeder, new()
        where CustomSettings : BaseCustomSettings, new()
    {
        protected string TestStorageAccountConnectionString
        {
            get
            {
                return CloudConfigurationManager.GetConfiguration(
                    settingName: Constants.Settings.StorageConnStringSetting,
                    defaultValue: Constants.Settings.StorageConnDefaultValue);
            }
        }

        protected CloudStorageAccount TestStorageAccount { get; set; }

        protected CloudTableClient TableClient { get; set; }

        protected CloudQueueClient QueueClient { get; set; }

        protected WireMockServer Frontdoor { get; set; }

        protected AppConfigSettingScope SettingsScope { get; set; }

        protected ProvidersJobDispatcherClient JobDispatcherClient { get; set; }

        [TestInitialize]
        public async Task TestInitialize()
        {
            try
            {
                SettingsScope = new AppConfigSettingScope(new CustomSettings().GetSettings());
                TestStorageAccount = CloudStorageAccount.Parse(TestStorageAccountConnectionString);

                TableClient = TestStorageAccount.CreateCloudTableClient();
                QueueClient = TestStorageAccount.CreateCloudQueueClient();
                TableClient.ListTables().ForEach(t => StorageArtifactsHelper.DeleteTableIfExistAsync(TableClient, t.Name, ProvidersLog.Current).Wait());
                QueueClient.ListQueues().ForEach(q => StorageArtifactsHelper.DeleteQueueIfExistAsync(QueueClient, q.Name, ProvidersLog.Current).Wait());

                var databaseSeeder = new DatabaseSeeder();
                await databaseSeeder.Seed(TestStorageAccount);

                Frontdoor = new FrontDoorBuilder().Build();

                JobDispatcherClient = JobsDispatcherClientHelper.CreateJobDispatcherClient();
                JobDispatcherClient.Start();
                JobDispatcherClient.ProvisionSystemConsistencyJob().Wait();
            }
            catch (Exception e)
            {
                Console.WriteLine(e);
                throw;
            }

        }

        [TestCleanup]
        public async Task TestCleanup()
        {
            try
            {
                TableClient.ListTables().ForEach(t => StorageArtifactsHelper.DeleteTableIfExistAsync(TableClient, t.Name, ProvidersLog.Current).Wait());
                QueueClient.ListQueues().ForEach(q => StorageArtifactsHelper.DeleteQueueIfExistAsync(QueueClient, q.Name, ProvidersLog.Current).Wait());

                Frontdoor.Dispose();

                await JobDispatcherClient.StopAsync();
                JobDispatcherClient.Dispose();
                SettingsScope.Dispose();
            }
            catch (Exception e)
            {
                Console.WriteLine(e);
            }
        }
    }
}
