﻿//------------------------------------------------------------
// Copyright (c) Microsoft Corporation.  All rights reserved.
//------------------------------------------------------------

namespace IntegrationTests.ResourceProvider.BaseClasses
{
    using System;
    using System.Collections.Concurrent;
    using System.Collections.Generic;
    using System.Configuration;
    using System.Linq;
    using System.Threading;
    using Microsoft.WindowsAzure.ResourceStack.Common.Extensions;
    using Microsoft.WindowsAzure.ResourceStack.Common.Services;

    /// <summary>
    /// Class to add limited-scope usage of Configuration settings.
    /// </summary>
    /// <remarks>
    /// This should be used before any creation of front door instances.
    /// </remarks>
    public class AppConfigSettingScope : IDisposable
    {
        /// <summary>
        /// Gets or sets the Configuration value in this scope.
        /// </summary>
        private ConcurrentDictionary<string, string> ConfigValues { get; set; }

        /// <summary>
        /// Gets or sets the original Configuration values in this scope.
        /// </summary>
        private ConcurrentDictionary<string, string> ConfigOriginalValues { get; set; }

        /// <summary>
        /// Gets or sets the configuration.
        /// </summary>
        private Configuration Configuration { get; set; }

        /// <summary>
        /// Initializes a new instance of the <see cref="AppConfigSettingScope" /> class.
        /// </summary>
        /// <param name="name">The name.</param>
        /// <param name="value">The value.</param>
        public AppConfigSettingScope(string name, string value)
            : this(new Dictionary<string, string> { { name, value } })
        {
        }

        /// <summary>
        /// Initializes a new instance of the <see cref="AppConfigSettingScope" /> class.
        /// </summary>
        /// <param name="configValues">The temporary config values to use.</param>
        public AppConfigSettingScope(IDictionary<string, string> configValues)
        {
            this.ConfigValues = new ConcurrentDictionary<string, string>(configValues);
            this.Configuration = ConfigurationManager.OpenExeConfiguration(ConfigurationUserLevel.None);
            this.ConfigOriginalValues = new ConcurrentDictionary<string, string>(
                this.ConfigValues.Keys.ToDictionary(name => name, name => this.GetAppConfigSetting(name)));

            this.UpdateAppConfigSettings(this.ConfigValues);
        }

        /// <summary>
        /// Disposes the scope and resets the Configuration setting
        /// </summary>
        public void Dispose()
        {
            this.UpdateAppConfigSettings(this.ConfigOriginalValues);
        }

        /// <summary>
        /// Get a value in the app Configuration.
        /// </summary>
        /// <param name="name">Name of value to get.</param>
        private string GetAppConfigSetting(string name)
        {
            return !this.ContainsAppConfigSetting(name: name) ? null : this.Configuration.AppSettings.Settings[name].Value;
        }

        /// <summary>
        /// Update values in the app Configuration.
        /// </summary>
        /// <param name="configSettings">The dictionary of values to update.</param>
        public void UpdateAppConfigSettings(IDictionary<string, string> configSettings)
        {
            foreach (var setting in configSettings)
            {
                if (this.ContainsAppConfigSetting(setting.Key))
                {
                    this.Configuration.AppSettings.Settings.Remove(setting.Key);
                }

                if (setting.Value != null)
                {
                    this.Configuration.AppSettings.Settings.Add(setting.Key, setting.Value);
                }
            }

            this.UpdateAppConfigSetting();
            CloudConfigurationManager.ResetCache();
        }

        /// <summary>
        /// Update a value in the app Configuration.
        /// </summary>
        private void UpdateAppConfigSetting()
        {
            this.Configuration.AppSettings.SectionInformation.ForceSave = true;

            const int maxRetries = 3;
            const int delayBetweenRetries = 5000; // in milliseconds

            for (int retry = 0; retry < maxRetries; retry++)
            {
                try
                {
                    this.Configuration.Save(ConfigurationSaveMode.Full);
                    ConfigurationManager.RefreshSection(this.Configuration.AppSettings.SectionInformation.Name);
                    break; // Exit the loop if save is successful
                }
                catch (ConfigurationErrorsException)
                {
                    if (retry == maxRetries - 1)
                    {
                        throw; // Re-throw the exception if max retries reached
                    }
                    Thread.Sleep(delayBetweenRetries); // Wait before retrying
                }
            }
        }

        /// <summary>
        /// Indicates if the app config has the setting.
        /// </summary>
        /// <param name="name">Name of the setting to check.</param>
        private bool ContainsAppConfigSetting(string name)
        {
            return this.Configuration.AppSettings.Settings.AllKeys.ContainsInsensitively(value: name);
        }
    }
}
