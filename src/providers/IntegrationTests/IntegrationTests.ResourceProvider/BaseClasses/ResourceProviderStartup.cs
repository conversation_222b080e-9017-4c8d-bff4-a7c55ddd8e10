﻿//------------------------------------------------------------
// Copyright (c) Microsoft Corporation.  All rights reserved.
//------------------------------------------------------------

namespace IntegrationTests.ResourceProvider.BaseClasses
{
    using System;
    using System.Collections.Generic;
    using System.Net.Http.Formatting;
    using System.Reflection;
    using System.Threading.Tasks;
    using Microsoft.AspNetCore;
    using Microsoft.AspNetCore.Authorization;
    using Microsoft.AspNetCore.Builder;
    using Microsoft.AspNetCore.Hosting;
    using Microsoft.AspNetCore.Mvc;
    using Microsoft.AspNetCore.Mvc.Infrastructure;
    using Microsoft.Extensions.DependencyInjection;
    using Microsoft.WindowsAzure.ResourceStack.Common.EventSources;
    using Microsoft.WindowsAzure.ResourceStack.Common.Json;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Common.Components;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Common.DataProviders;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Common.Engines;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Common.ErrorResponses;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Common.Extensions;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Common.Filters;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Common.Logging;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Common.Middleware;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Common.Selectors;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Feature.Components;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Feature.Controllers;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Feature.Metrics;
    using Newtonsoft.Json;
    using Newtonsoft.Json.Converters;
    using Newtonsoft.Json.Serialization;

    public class KestrelConfiguration
    {
        /// <summary>
        /// Service Name
        /// </summary>
        private const string ServiceName = "Microsoft.Databricks.ServiceFabric.Feature";

        /// <summary>
        /// Service version
        /// </summary>
        private const string ServiceVersion = "*******";

        /// <summary>
        /// Gets the event source to use for tracing.
        /// </summary>
        private static IProvidersEventSource EventSource
        {
            get { return ProvidersLog.Current; }
        }

        public void ConfigureServices(IServiceCollection services)
        {
            EventSource.ServiceStarting(serviceName: ServiceName, version: ServiceVersion);

            services.AddSingleton<MediaTypeFormatter>(sp => new JsonMediaTypeFormatter() { SerializerSettings = Microsoft.WindowsAzure.ResourceStack.Providers.Common.Extensions.JsonExtensions.MediaTypeFormatterSettings });
            services.AddSingleton<ICommonEventSource>(ProvidersLog.Current);
            services.AddSingleton<ICacheProvidersContainer, CacheProvidersContainer>();
            services.AddSingleton<IFrontdoorEngine, FrontdoorEngine>();
            services.AddSingleton<IArmMetadataProvider, ArmMetadataProvider>();
            services.AddSingleton<IApplicationProviderConfiguration>(ApplicationProviderConfiguration.Instance);
            services.AddSingleton(ApplicationProviderConfiguration.AadAuthenticationDataProvider);
            services.AddSingleton<IAccessConnectorProviderConfiguration>(AccessConnectorProviderConfiguration.Instance);
            services.AddSingleton<IAuthorizationHandler, NrpAuthorizationHandler>();
            services.AddSingleton<IAuthorizationHandler, AcisAuthorizationHandler>();
            services.AddSingleton<IActionSelector, ProviderApiControllerActionSelector>();
            FrontdoorEngineProviderConfiguration.Instance.InitializeFrontdoorEngine(services.BuildServiceProvider().GetService<IFrontdoorEngine>());

            services.AddAuthorization(options =>
            {
                options.AddPolicy("Acis", policy =>
                    policy.Requirements.Add(new AcisRequirement()));
                options.AddPolicy("Nrp", policy =>
                    policy.Requirements.Add(new NrpRequirement()));
            });
            services.AddSingleton<IAuthorizationHandler, AcisAuthorizationHandler>();
            var assembly = typeof(GatewayProbeController).GetTypeInfo().Assembly;

            services.AddMvc(options =>
            {
                options.EnableEndpointRouting = false;
                options.Filters.Add(typeof(ErrorResponseFilter));
            }).ConfigureApiBehaviorOptions(options =>
            {
                options.InvalidModelStateResponseFactory = context =>
                {
                    return ErrorResponseHandling.ConvertModelBindingError(context);
                };
            }).AddApplicationPart(assembly).SetCompatibilityVersion(CompatibilityVersion.Version_2_2).AddJsonOptions(options =>
            {
                options.SerializerSettings.MaxDepth = Microsoft.WindowsAzure.ResourceStack.Providers.Common.Extensions.JsonExtensions.JsonSerializationMaxDepth;
                options.SerializerSettings.TypeNameHandling = TypeNameHandling.None;
                options.SerializerSettings.DateParseHandling = DateParseHandling.None;
                options.SerializerSettings.DateTimeZoneHandling = DateTimeZoneHandling.Utc;
                options.SerializerSettings.NullValueHandling = NullValueHandling.Ignore;
                options.SerializerSettings.MissingMemberHandling = MissingMemberHandling.Error;
                options.SerializerSettings.Formatting = Formatting.Indented;
                options.SerializerSettings.ContractResolver = new CamelCasePropertyNamesWithOverridesContractResolver();
                options.SerializerSettings.MetadataPropertyHandling = MetadataPropertyHandling.Ignore;
                options.SerializerSettings.Converters = new List<JsonConverter>
                    {
                        new LineInfoConverter(),
                        new TimeSpanConverter(),
                        new StringEnumConverter { NamingStrategy = new DefaultNamingStrategy() },
                        new AdjustToUniversalIsoDateTimeConverter()
                    };
            });
        }
        public void Configure(IApplicationBuilder app, IHostingEnvironment env)
        {
            app.Use(async (context, next) =>
            {
                context.Response.OnStarting(() =>
                {
                    RequestCorrelationHandler.PopulateResponseHeaders(context);
                    return Task.FromResult(0);
                });
                await next();
            }
            );
            app.UseMiddleware<RequestCorrelationHandler>();
            app.UseMiddleware<ExceptionMiddleware>();
            app.UseMiddleware<AuthorizationMiddleware>();
            app.UseMiddleware<RequestObserverMiddleware>();
            app.UseMvc();
            EventSource.ServiceStarted(serviceName: ServiceName, version: ServiceVersion);

        }
    }

    public class KestrelHost : IDisposable
    {
        private static IWebHost webHost;

        private KestrelHost(IWebHost localhost)
        {
            webHost = localhost;
        }

        public static IDisposable Start(string url)
        {
            var rp = WebHost.CreateDefaultBuilder().UseUrls(url).UseStartup<KestrelConfiguration>();
            webHost = rp.Build();
            webHost.Start();
            return new KestrelHost(webHost);
        }

        public void Dispose()
        {
            webHost.StopAsync().GetAwaiter().GetResult();
        }
    }
}
