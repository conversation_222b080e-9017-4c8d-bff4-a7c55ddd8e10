﻿//------------------------------------------------------------
// Copyright (c) Microsoft Corporation.  All rights reserved.
//------------------------------------------------------------

namespace IntegrationTests.ResourceProvider.BaseClasses
{
    using System;
    using System.Collections.Generic;
    using System.Globalization;
    using System.Threading.Tasks;
    using Microsoft.WindowsAzure.ResourceStack.Common.Storage;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Common.Logging;
    using Microsoft.WindowsAzure.Storage;
    using RestSharp;

    public class BaseDatabaseSeeder
    {
        public async Task Seed(CloudStorageAccount cloudStorageAccount)
        {
            var accessPolicy = new SharedAccessAccountPolicy();
            accessPolicy.SharedAccessStartTime = DateTimeOffset.UtcNow;
            accessPolicy.SharedAccessExpiryTime = DateTimeOffset.UtcNow.AddMinutes(10);

            accessPolicy.Permissions = SharedAccessAccountPermissions.Read |
                SharedAccessAccountPermissions.Add |
                SharedAccessAccountPermissions.Create |
                SharedAccessAccountPermissions.Update |
                SharedAccessAccountPermissions.ProcessMessages |
                SharedAccessAccountPermissions.Write |
                SharedAccessAccountPermissions.Delete |
                SharedAccessAccountPermissions.List;

            accessPolicy.Protocols = SharedAccessProtocol.HttpsOrHttp;
            accessPolicy.Services = SharedAccessAccountServices.Table |
                SharedAccessAccountServices.Queue |
                SharedAccessAccountServices.Blob |
                SharedAccessAccountServices.File;

            accessPolicy.ResourceTypes = SharedAccessAccountResourceTypes.Object | SharedAccessAccountResourceTypes.Service | SharedAccessAccountResourceTypes.Container;

            var sasToken = cloudStorageAccount.GetSharedAccessSignature(accessPolicy);
            var tableStorageUri = cloudStorageAccount.TableStorageUri;

            var tableClient = cloudStorageAccount.CreateCloudTableClient();
            await StorageArtifactsHelper.CreateTableIfNotExistAsync(tableClient, "appliances", ProvidersLog.Current);
            await StorageArtifactsHelper.CreateTableIfNotExistAsync(tableClient, "accessConnectors", ProvidersLog.Current);

            SeedApplianceTable(sasToken, tableStorageUri);
            SeedAccessConnectorTable(sasToken, tableStorageUri);
        }

        private void SeedApplianceTable(string sasToken, StorageUri tableStorageUri)
        {
            var uri = tableStorageUri.PrimaryUri + "/appliances" + sasToken;

            List<string> bodies = UpdateRequestBody();

            foreach (var body in bodies)
            {
                InsertRecordIntoTable(uri, body);
            }
        }

        private void SeedAccessConnectorTable(string sasToken, StorageUri tableStorageUri)
        {
            var uri = tableStorageUri.PrimaryUri + "/accessConnectors" + sasToken;

            List<string> bodies = UpdateAccessConnectorRequestBody();

            foreach (var body in bodies)
            {
                InsertRecordIntoTable(uri, body);
            }
        }

        private static void InsertRecordIntoTable(string tableUri, string body)
        {
            var request = new RestRequest
            {
                Method = Method.Post
            };

            request.AddHeader("x-ms-date", DateTime.Now.ToString("R", CultureInfo.InvariantCulture));
            request.AddHeader("Content-Type", "application/json");
            request.AddHeader("Accept", "application/json;odata=minimalmetadata");

            request.AddParameter("application/json", body, ParameterType.RequestBody);
            var restClient = new RestClient(tableUri);
            restClient.Execute(request);
        }

        public virtual List<string> UpdateRequestBody()
        {
            return new List<string>();
        }

        public virtual List<string> UpdateAccessConnectorRequestBody()
        {
            return new List<string>();
        }
    }
}
