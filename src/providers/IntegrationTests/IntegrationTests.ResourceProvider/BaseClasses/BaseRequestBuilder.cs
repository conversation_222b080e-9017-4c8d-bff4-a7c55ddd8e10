﻿//------------------------------------------------------------
// Copyright (c) Microsoft Corporation.  All rights reserved.
//------------------------------------------------------------

namespace IntegrationTests.ResourceProvider.BaseClasses
{
    using RestSharp;

    public class BaseRequestBuilder
    {
        private RestRequest _build()
        {
            var request = new RestRequest();
            request.AddHeader(Constants.RequestHeaders.RefererKey, Constants.RequestHeaders.RefererValue);
            request.AddHeader(Constants.RequestHeaders.ConnectionKey, Constants.RequestHeaders.ConnectionValue);
            request.AddHeader(Constants.RequestHeaders.AcceptEncodingKey, Constants.RequestHeaders.AcceptEncodingValue);
            request.AddHeader(Constants.RequestHeaders.AcceptL<PERSON>ua<PERSON><PERSON><PERSON>, Constants.RequestHeaders.AcceptLanguageValue);
            request.AddHeader(Constants.RequestHeaders.UserAgentKey, Constants.RequestHeaders.UserAgentValue);
            request.AddHeader(Constants.RequestHeaders.ClientRequestIdKey, Constants.RequestHeaders.ClientRequestIdValue);
            request.AddHeader(Constants.RequestHeaders.ClientLocationKey, Constants.RequestHeaders.ClientLocationValue);
            request.AddHeader(Constants.RequestHeaders.CorrelationIdKey, Constants.RequestHeaders.CorrelationIdValue);
            request.AddHeader(Constants.RequestHeaders.HomeTenantKey, Constants.RequestHeaders.HomeTenantValue);
            request.AddHeader(Constants.RequestHeaders.ClientIpAddressKey, Constants.RequestHeaders.ClientIpAddressValue);
            request.AddHeader(Constants.RequestHeaders.ClientScopeKey, Constants.RequestHeaders.ClientScopeValue);
            request.AddHeader(Constants.RequestHeaders.ClientObjectIdKey, Constants.RequestHeaders.ClientObjectIdValue);
            request.AddHeader(Constants.RequestHeaders.ClientPuidKey, Constants.RequestHeaders.ClientPuidValue);
            request.AddHeader(Constants.RequestHeaders.ClientPrincipalIdKey, Constants.RequestHeaders.ClientPrincipalIdValue);
            request.AddHeader(Constants.RequestHeaders.ClientPrincipalNameKey, Constants.RequestHeaders.ClientPrincipalNameValue);
            request.AddHeader(Constants.RequestHeaders.ClientAuthSourceKey, Constants.RequestHeaders.ClientAuthSourceValue);
            request.AddHeader(Constants.RequestHeaders.NetworkSourceKey, Constants.RequestHeaders.NetworkSourceValue);

            return request;
        }

        public virtual RestRequest Update(RestRequest request)
        {
            return request;
        }

        public RestRequest Build()
        {
            var request = _build();
            return Update(request);
        }
    }
}
