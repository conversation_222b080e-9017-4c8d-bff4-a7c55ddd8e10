﻿//------------------------------------------------------------
// Copyright (c) Microsoft Corporation.  All rights reserved.
//------------------------------------------------------------

namespace IntegrationTests.ResourceProvider.BaseClasses
{
    using Microsoft.WindowsAzure.ResourceStack.Common.Services;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Common.Logging;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Worker.Configuration;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Worker.Dispatcher;

    public static class JobsDispatcherClientHelper
    {
        private static string RoleLocation
        {
            get { return CloudConfigurationManager.GetConfiguration(Constants.Settings.RoleLocationSetting2); }
        }

        /// <summary>
        /// Creates the providers job dispatcher client.
        /// </summary>
        public static ProvidersJobDispatcherClient CreateJobDispatcherClient()
        {
            return ProvidersJobDispatcherClient.CreateProvidersJobDispatcherClient(
                location: JobsDispatcherClientHelper.RoleLocation,
                eventSource: ProvidersLog.Current);
        }
    }
}
