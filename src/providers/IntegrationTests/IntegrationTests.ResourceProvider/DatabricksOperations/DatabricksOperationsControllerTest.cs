//------------------------------------------------------------
// Copyright (c) Microsoft Corporation.  All rights reserved.
//------------------------------------------------------------

namespace IntegrationTests.ResourceProvider.DatabricksOperations
{
    using System.Net;
    using System.Threading.Tasks;
    using IntegrationTests.ResourceProvider.BaseClasses;
    using Microsoft.VisualStudio.TestTools.UnitTesting;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Common.Definitions;
    using Newtonsoft.Json;
    using RestSharp;
    using WireMock.Server;

    public class DatabricksOperationsFrontDoorBuilder : IWireMockServerBuilder
    {
        public WireMockServer Build()
        {
            var frontDoor = WireMockServer.StartWithAdminInterface(Constants.EndPoints.FrontDoorPort);
            frontDoor.AllowPartialMapping(false);
            return frontDoor;
        }
    }

    [TestClass]
    public class DatabricksOperationsControllerTest : BaseIntegrationTest<DatabricksOperationsFrontDoorBuilder, BaseDatabaseSeeder, BaseCustomSettings>
    {
        [TestMethod]
        public async Task TestDatabricksOperationsController()
        {
            using (var rp = KestrelHost.Start(Constants.EndPoints.RPHost))
            {
                var client = new RestClient(Constants.DatabricksOperationsRequestMetadata.RequestUrl);
                var request = new BaseRequestBuilder().Build();
                var response = await client.GetAsync(request);

                Assert.IsNotNull(response);
                Assert.AreEqual(HttpStatusCode.OK, response.StatusCode);
                // Deserialize into OperationDefinitionList to verify that the response is compliant with swagger spec
                JsonConvert.DeserializeObject<OperationDefinitionList>(response.Content);
            }
        }
    }
}
