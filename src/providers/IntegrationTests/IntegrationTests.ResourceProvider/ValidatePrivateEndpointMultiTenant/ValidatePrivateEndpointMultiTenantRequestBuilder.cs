//------------------------------------------------------------
// Copyright (c) Microsoft Corporation.  All rights reserved.
//------------------------------------------------------------

namespace IntegrationTests.ResourceProvider.ValidatePrivateEndpointMultiTenant
{
    using IntegrationTests.ResourceProvider.BaseClasses;
    using Microsoft.CodeAnalysis;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Common.Definitions;
    using RestSharp;

    public class ValidatePrivateEndpointMultiTenantRequestBuilder : BaseRequestBuilder
    {
        public string RequestBody { get; set; }

        public override RestRequest Update(RestRequest request)
        {
            var requestBody = @"{
                    ""id"": ""/subscriptions/12345678-abcd-abcd-abcd-ba9876543b10/resourceGroups/testrg/providers/Microsoft.Databricks/workspaces/testws/privateEndpointConnectionProxies/testpeproxy-1hf93d"",
                    ""name"": ""testpeproxy-1hf93d"",
                    ""remotePrivateEndpoint"": {
                        ""id"": ""/subscriptions/87654321-dcba-dcba-dcba-ba9876543b10/resourceGroups/testrg/providers/Microsoft.Network/privateEndpoints/testpeproxy"",
                        ""manualPrivateLinkServiceConnections"": [{
                            ""name"": ""testpeproxy"",
                            ""groupIds"": [ ""Browser_authentication"" ],
                            ""requestMessage"": ""Please approve my connection, thanks."",
                        }],
                        ""privateLinkServiceProxies"": [{
                            ""id"": ""/subscriptions/87654321-dcba-dcba-dcba-ba9876543b10/resourceGroups/testrg/providers/Microsoft.Network/privateEndpoints/testpeproxy"",
                            ""groupConnectivityInformation"": []
                        }]
                    }
                }"
            ;

            request.AddStringBody(requestBody, DataFormat.Json);
            return request;
        }
    }
}
