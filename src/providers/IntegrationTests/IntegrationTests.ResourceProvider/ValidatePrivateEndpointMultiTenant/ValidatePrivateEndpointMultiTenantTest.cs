//------------------------------------------------------------
// Copyright (c) Microsoft Corporation.  All rights reserved.
//------------------------------------------------------------

namespace IntegrationTests.ResourceProvider.ValidatePrivateEndpointMultiTenant
{
    using System;
    using System.Threading.Tasks;
    using IntegrationTests.ResourceProvider.BaseClasses;
    using Microsoft.VisualStudio.TestTools.UnitTesting;
    using RestSharp;
    using WireMock.Pact.Models.V2;

    [TestClass]
    public class ValidatePrivateEndpointMultiTenantTest : BaseIntegrationTest<ValidatePrivateEndpointMultiTenantFrontDoorBuilder, ValidatePrivateEndpointMultiTenantDatabaseSeeder, BaseCustomSettings>
    {
        /// <summary>
        /// Configure private endpoint tenant not registered for RP workspace
        /// </summary>
        [TestMethod]
        public async Task ValidatePrivateEndpointMultiTenant_Request()
        {
            using (var rp = KestrelHost.Start(Constants.EndPoints.RPHost))
            {
                var putClient = new RestClient(Constants.PrivateEndpointRequestMetadata.RequestUrl);
                var putRequest = new ValidatePrivateEndpointMultiTenantRequestBuilder().Build();
                var putResponse = await putClient.ExecutePostAsync(putRequest);
                Assert.IsNotNull(putResponse.Request);
                Assert.AreEqual(System.Net.HttpStatusCode.BadRequest, putResponse.StatusCode);
                var putResponseContent = putResponse.Content.ToString();
                Assert.IsTrue(putResponseContent.Contains(ErrorResponseCode.DatabricksResourceProviderNotRegistered.ToString()));
            }
        }
    }
}
