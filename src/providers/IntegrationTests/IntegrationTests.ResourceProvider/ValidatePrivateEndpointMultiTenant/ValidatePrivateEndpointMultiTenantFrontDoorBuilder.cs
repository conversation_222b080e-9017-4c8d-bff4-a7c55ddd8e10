//------------------------------------------------------------
// Copyright (c) Microsoft Corporation.  All rights reserved.
//------------------------------------------------------------

namespace IntegrationTests.ResourceProvider.ValidatePrivateEndpointMultiTenant
{
    using System;
    using System.Net;
    using IntegrationTests.ResourceProvider.BaseClasses;
    using WireMock.RequestBuilders;
    using WireMock.ResponseBuilders;
    using WireMock.Server;

    public class ValidatePrivateEndpointMultiTenantFrontDoorBuilder : IWireMockServerBuilder
    {
        public WireMockServer Build()
        {
            var frontDoor = WireMockServer.StartWithAdminInterface(Constants.EndPoints.FrontDoorPort);

            frontDoor.AllowPartialMapping(false);

            var rpRegisteredResponse =
                "{" +
                    "\"id\": \"/subscriptions/87654321-dcba-dcba-dcba-ba9876543b10/providers/Microsoft.ManagedIdentity\"," +
                    "\"resourceTypes\": []," +
                    "\"namespace\": \"Microsoft.ManagedIdentity\"," +
                    "\"registrationState\": \"UnRegistered\"" +
                "}";

            frontDoor.Given(
                Request.Create()
                .WithPath(Constants.FrontDoor.isResourceProviderRegisteredUrl)
                .UsingGet()
            ).RespondWith(
                Response.Create()
                .WithHeader("Content-Type", "application/json")
                .WithStatusCode(200)
                .WithBody(rpRegisteredResponse));

            return frontDoor;
        }
    }
}
