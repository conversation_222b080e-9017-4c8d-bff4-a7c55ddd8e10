//------------------------------------------------------------
// Copyright (c) Microsoft Corporation.  All rights reserved.
//------------------------------------------------------------

namespace IntegrationTests.ResourceProvider.ValidatePrivateEndpointMultiTenant
{
    using System;
    using System.Collections.Generic;
    using IntegrationTests.ResourceProvider.BaseClasses;
    using IntegrationTests.ResourceProvider.Compression;

    public class ValidatePrivateEndpointMultiTenantDatabaseSeeder : BaseDatabaseSeeder
    {
        public override List<string> UpdateRequestBody()
        {
            var bodies = new List<string>();

            string properties = ApplianceTestHelper.GetAppliancePropertiesWithVnetDetails()
                                        .Replace("testManagedResourceGroupId", Constants.RequestBody.ManagedResourceGroupId)
                                        .Replace("\"PublicNetworkAccess\":\"Disabled\"", "\"PublicNetworkAccess\":\"Enabled\"")
                                        .Replace("NoAzureDatabricksRules", "AllRules")
                                        .Replace("\"enableNoPublicIp\":{ \"value\":false}", "\"enableNoPublicIp\":{ \"value\":true}");

            var PropertiesV2EntityPropery = ResrourceStackCompression.EncodeToBinaryEnvelope(properties, new ModernCompressionUtility());

            bodies.Add(
                " {" +
                    "\"PartitionKey\": \"AEB88\"," +
                    "\"RowKey\": \"12345678ABCDABCDABCDBA9876543B10_PLAN-2018:2D04:2D01-TESTWS\"," +
                     "\"CreatedTime\": \"2022-10-26T17:49:51.142Z\"," +
                    "\"<EMAIL>\": \"Edm.DateTime\"," +
                    "\"ChangedTime\": \"2022-10-26T17:49:57.033Z\"," +
                    "\"<EMAIL>\": \"Edm.DateTime\"," +
                    "\"DeletedTime\": \"1970-01-01T00:00:00.000Z\"," +
                    "\"<EMAIL>\": \"Edm.DateTime\"," +
                    "\"ChangedOperationId\": \"562d4a00-2295-4e21-9355-3c68bb2cd68f\"," +
                    "\"SubscriptionId\": \"12345678-abcd-abcd-abcd-ba9876543b10\"," +
                    "\"ResourceGroup\": \"testrg\"," +
                    "\"Name\": \"testws\"," +
                    "\"Sku\": \"{\\\"name\\\": \\\"Premium\\\"}\"," +
                    "\"Location\": \"wakandacentral\"," +
                    "\"Tags\": \"{\\\"team\\\": \\\"adb\\\"}\"," +
                    "\"Metadata\": \"XY2xCkIxDEX/JbPo/jZBcFEH/YKYRim1TUlSHo/Sf7c4ul04nHM7ZCz45nBnk6bEZ5VWL0LphplhAWfz1WAHgetHtszF/4lUVvQo5eHozdhg6XCNpGLy8v0JHZ8aKdlhFU1WkXhOjT77HewnzacjEVfnAGOMLw==\"," +
                    "\"<EMAIL>\": \"Edm.Binary\"," +
                    "\"PropertiesV2\": \"" + Convert.ToBase64String(PropertiesV2EntityPropery.BinaryValue) + "\"," +
                    "\"<EMAIL>\": \"Edm.Binary\"" +
                " }");

            bodies.Add(
                "{ " +
                    "\"PartitionKey\": \"AEB88\"," +
                    "\"RowKey\": \"12345678ABCDABCDABCDBA9876543B10_RGA-TESTRG-TESTWS\"," +
                    "\"CreatedTime\": \"2022-10-26T17:49:51.142Z\"," +
                    "\"<EMAIL>\": \"Edm.DateTime\"," +
                    "\"ChangedTime\": \"2022-10-26T17:49:57.033Z\"," +
                    "\"<EMAIL>\": \"Edm.DateTime\"," +
                    "\"DeletedTime\": \"1970-01-01T00:00:00.000Z\"," +
                    "\"<EMAIL>\": \"Edm.DateTime\"," +
                    "\"ChangedOperationId\": \"562d4a00-2295-4e21-9355-3c68bb2cd68f\"," +
                    "\"SubscriptionId\": \"12345678-abcd-abcd-abcd-ba9876543b10\"," +
                    "\"ResourceGroup\": \"testrg\"," +
                    "\"Name\": \"testws\"," +
                    "\"Sku\": \"{\\\"name\\\": \\\"Premium\\\"}\"," +
                    "\"Location\": \"wakandacentral\"," +
                    "\"Tags\": \"{\\\"team\\\": \\\"adb\\\"}\"," +
                    "\"Metadata\": \"XY2xCkIxDEX/JbPo/jZBcFEH/YKYRim1TUlSHo/Sf7c4ul04nHM7ZCz45nBnk6bEZ5VWL0LphplhAWfz1WAHgetHtszF/4lUVvQo5eHozdhg6XCNpGLy8v0JHZ8aKdlhFU1WkXhOjT77HewnzacjEVfnAGOMLw==\"," +
                    "\"<EMAIL>\": \"Edm.Binary\"," +
                    "\"PropertiesV2\": \"" + Convert.ToBase64String(PropertiesV2EntityPropery.BinaryValue) + "\"," +
                    "\"<EMAIL>\": \"Edm.Binary\"" +
                " }");

            return bodies;
        }
    }
}
