﻿//------------------------------------------------------------
// Copyright (c) Microsoft Corporation.  All rights reserved.
//------------------------------------------------------------

namespace IntegrationTests.ResourceProvider.Backfill
{
    using System.Net;
    using IntegrationTests.ResourceProvider.BaseClasses;
    using IntegrationTests.ResourceProvider.WorkspaceCreate.BasicWorkspace;
    using WireMock.Matchers;
    using WireMock.RequestBuilders;
    using WireMock.ResponseBuilders;
    using WireMock.Server;

    public class BackfillFrontDoorBuilder : IWireMockServerBuilder
    {
        public WireMockServer Build()
        {
            var frontDoor = new BasicWorkspaceCreateFrontDoorBuilder().Build();

            var sshRuleResponse = """{"name":"databricks-control-plane-ssh","id":"/subscriptions/0140911e-1040-48da-8bc9-b99fb3dd88a6/resourceGroups/databricks-rg-adb--001-oxbqitum3k5cq/providers/Microsoft.Network/networkSecurityGroups/workers-sg/securityRules/databricks-control-plane-ssh","etag":"W/\"7af4fae0-401d-463e-97f8-55a1815b8f45\"","type":"Microsoft.Network/networkSecurityGroups/securityRules","properties":{"provisioningState":"Succeeded","description":"Required for Databricks control plane management of worker nodes.","protocol":"*","sourcePortRange":"*","destinationPortRange":"22","destinationAddressPrefix":"*","access":"Allow","priority":100,"direction":"Inbound","sourcePortRanges":[],"destinationPortRanges":[],"sourceAddressPrefixes":["************/32"],"destinationAddressPrefixes":[]}}""";

            var proxyRuleResponse = """{"name":"databricks-control-plane-worker-proxy","id":"/subscriptions/0140911e-1040-48da-8bc9-b99fb3dd88a6/resourceGroups/databricks-rg-adb--001-oxbqitum3k5cq/providers/Microsoft.Network/networkSecurityGroups/workers-sg/securityRules/databricks-control-plane-worker-proxy","etag":"W/\"7af4fae0-401d-463e-97f8-55a1815b8f45\"","type":"Microsoft.Network/networkSecurityGroups/securityRules","properties":{"provisioningState":"Succeeded","description":"Required for Databricks control plane communication with worker nodes.","protocol":"*","sourcePortRange":"*","destinationPortRange":"5557","destinationAddressPrefix":"*","access":"Allow","priority":110,"direction":"Inbound","sourcePortRanges":[],"destinationPortRanges":[],"sourceAddressPrefixes":["************/32"],"destinationAddressPrefixes":[]}}""";

            var workerRuleResponse = """{"name":"databricks-worker-to-worker","id":"/subscriptions/0140911e-1040-48da-8bc9-b99fb3dd88a6/resourceGroups/databricks-rg-adb--001-oxbqitum3k5cq/providers/Microsoft.Network/networkSecurityGroups/workers-sg/securityRules/databricks-worker-to-worker","etag":"W/\"7af4fae0-401d-463e-97f8-55a1815b8f45\"","type":"Microsoft.Network/networkSecurityGroups/securityRules","properties":{"provisioningState":"Succeeded","description":"Required for worker nodes communication within a cluster.","protocol":"*","sourcePortRange":"*","destinationPortRange":"*","sourceAddressPrefix":"VirtualNetwork","destinationAddressPrefix":"*","access":"Allow","priority":200,"direction":"Inbound","sourcePortRanges":[],"destinationPortRanges":[],"sourceAddressPrefixes":[],"destinationAddressPrefixes":[]}}""";

            frontDoor.Given(
                Request.Create()
                //.WithPath(Constants.FrontDoor.SshSecurityRuleUrl)
                .WithPath(new RegexMatcher(".*/securityRules/databricks-control-plane-ssh"))
                .UsingGet()
            ).RespondWith(
                Response.Create()
                .WithHeader("Content-Type", "application/json")
                .WithStatusCode(HttpStatusCode.OK)
                .WithBody(sshRuleResponse));

            frontDoor.Given(
                Request.Create()
                .WithPath(new RegexMatcher(".*/securityRules/databricks-control-plane-worker-proxy"))
                .UsingGet()
            ).RespondWith(
                Response.Create()
                .WithHeader("Content-Type", "application/json")
                .WithStatusCode(HttpStatusCode.OK)
                .WithBody(proxyRuleResponse));

            frontDoor.Given(
                Request.Create()
                .WithPath(new RegexMatcher(".*/securityRules/databricks-worker-to-worker"))
                .UsingGet()
            ).RespondWith(
                Response.Create()
                .WithHeader("Content-Type", "application/json")
                .WithStatusCode(HttpStatusCode.OK)
                .WithBody(workerRuleResponse));

            frontDoor.Given(
                Request.Create()
                .WithPath(new RegexMatcher(".*/securityRules"))
                .UsingPut()
            ).RespondWith(
                Response.Create()
                .WithStatusCode(HttpStatusCode.Created)
                .WithBody(""));
            return frontDoor;
        }
    }
}
