﻿//------------------------------------------------------------
// Copyright (c) Microsoft Corporation.  All rights reserved.
//------------------------------------------------------------

namespace IntegrationTests.ResourceProvider.BackfillJob
{
    using IntegrationTests.ResourceProvider.BaseClasses;
    using RestSharp;

    public class BackfillWorkspaceCreateRequestBuilder : BaseRequestBuilder
    {
        public string Name { get; set; }
        public string Location { get; set; }
        public string ManagedResourceGroupId {
            get
            {
                return Constants.RequestBody.ManagedResourceGroupId;
            }
        }

        public BackfillWorkspaceCreateRequestBuilder(string wsName, string location = null)
        {
            this.Name = wsName;
            Location = location ?? Constants.RequestBody.Location;
        }

        public override RestRequest Update(RestRequest request)
        {
            var requestBody =
                "{" +
                    "\"sku\": { \"name\": \"" + Constants.RequestBody.SkuPremium + "\" }," +
                    "\"location\": \"" + Location + "\"," +
                    "\"tags\": { }," +
                    "\"properties\": { \"ManagedResourceGroupId\": \"" + ManagedResourceGroupId + "\" }" +
                    "}";

            request.AddStringBody(requestBody, DataFormat.Json);
            request.AddHeader(Constants.RequestHeaders.SystemDataHeaderKey, Constants.RequestHeaders.SystemDataHeaderValue);
            return request;
        }
    }
}
