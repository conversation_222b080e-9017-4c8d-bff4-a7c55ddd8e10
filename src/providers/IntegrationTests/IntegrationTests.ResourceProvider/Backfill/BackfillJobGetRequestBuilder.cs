﻿//------------------------------------------------------------
// Copyright (c) Microsoft Corporation.  All rights reserved.
//------------------------------------------------------------

namespace IntegrationTests.ResourceProvider.BackfillJob
{
    using IntegrationTests.ResourceProvider.BaseClasses;
    using RestSharp;

    public class BackfillJobGetRequestBuilder : BaseRequestBuilder
    {
        public string JobId { get; set; }

        public BackfillJobGetRequestBuilder(string jobId) => JobId = jobId;

        public override RestRequest Update(RestRequest request)
        {
            request.AddUrlSegment("jobId", this.JobId);
            return base.Update(request);
        }
    }
}
