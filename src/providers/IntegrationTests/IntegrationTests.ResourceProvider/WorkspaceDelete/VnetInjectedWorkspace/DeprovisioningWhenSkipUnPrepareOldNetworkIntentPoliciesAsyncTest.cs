﻿using IntegrationTests.ResourceProvider.BaseClasses;
using IntegrationTests.ResourceProvider.WorkspaceDelete.VnetInjectedWorkspace.DatabaseSeeder;
using IntegrationTests.ResourceProvider.WorkspaceDelete.VnetInjectedWorkspace.FrontDoorBuilder;
using IntegrationTests.ResourceProvider.WorkspaceDelete.VnetInjectedWorkspace.RequestBuilder;
//------------------------------------------------------------
// Copyright (c) Microsoft Corporation.  All rights reserved.
//------------------------------------------------------------

namespace IntegrationTests.ResourceProvider.WorkspaceDelete.VnetInjectedWorkspace
{
    using System.Threading.Tasks;
    using Microsoft.VisualStudio.TestTools.UnitTesting;
    using RestSharp;

    [TestClass]
    public class DeprovisioningWhenSkipUnPrepareOldNetworkIntentPoliciesAsyncTest : BaseIntegrationTest<DeprovisioningWhenSkipUnPrepareOldNetworkIntentPoliciesAsyncFrontDoorBuilder, VnetInjectedWorkspaceDatabaseSeeder, BaseCustomSettings>
    {
        /// <summary>
        /// Test deletion operation skips the un prepare since, subnet has old network intent policy (not yet migrated to GA).
        /// </summary>
        [TestMethod]
        public async Task VNetInjectedDatabricksWorkspaceTests_ApplianceDeprovisioningJob_Skipped_SkipUnprepareOldNetworkPolicies()
        {
            using (var rp = KestrelHost.Start(Constants.EndPoints.RPHost))
            {
                var putClient = new RestClient(Constants.RequestMetadata.RequestUrl);
                var putRequest = new DeprovisioningVnetInjectWorkspaceRequestBuilder().Build();
                var putResponse = await putClient.DeleteAsync(putRequest);
                Assert.IsNotNull(putResponse);

                await ApplianceTestHelper.WaitForDeprovisioningCompletionAndVerifyResult(putResponse);
            }
        }
    }
}
