﻿//------------------------------------------------------------
// Copyright (c) Microsoft Corporation.  All rights reserved.
//------------------------------------------------------------

namespace IntegrationTests.ResourceProvider.WorkspaceDelete.VnetInjectedWorkspace
{
    using System.Threading.Tasks;
    using IntegrationTests.ResourceProvider.BaseClasses;
    using IntegrationTests.ResourceProvider.WorkspaceDelete.VnetInjectedWorkspace.DatabaseSeeder;
    using IntegrationTests.ResourceProvider.WorkspaceDelete.VnetInjectedWorkspace.FrontDoorBuilder;
    using IntegrationTests.ResourceProvider.WorkspaceDelete.VnetInjectedWorkspace.RequestBuilder;
    using Microsoft.Owin.Hosting;
    using Microsoft.VisualStudio.TestTools.UnitTesting;
    using RestSharp;

    [TestClass]
    public class DeprovisioningWhenSubnetsAreDeletedAsyncTest : BaseIntegrationTest<DeprovisioningWhenSubnetsAreDeletedAsyncFrontDoorBuilder, VnetInjectedWorkspaceDatabaseSeeder, BaseCustomSettings>
    {
        /// <summary>
        /// Test deletion operation skips the un prepare since, subnets were deleted
        /// </summary>
        [TestMethod]
        public async Task VNetInjectedDatabricksWorkspaceTests_ApplianceDeprovisioningJob_Skipped_MissingSubnets()
        {
            using (var rp = KestrelHost.Start(Constants.EndPoints.RPHost))
            {
                var putClient = new RestClient(Constants.RequestMetadata.RequestUrl);
                var putRequest = new DeprovisioningVnetInjectWorkspaceRequestBuilder().Build();
                var putResponse = await putClient.DeleteAsync(putRequest);
                Assert.IsNotNull(putResponse);

                await ApplianceTestHelper.WaitForDeprovisioningCompletionAndVerifyResult(putResponse);
            }
        }
    }
}
