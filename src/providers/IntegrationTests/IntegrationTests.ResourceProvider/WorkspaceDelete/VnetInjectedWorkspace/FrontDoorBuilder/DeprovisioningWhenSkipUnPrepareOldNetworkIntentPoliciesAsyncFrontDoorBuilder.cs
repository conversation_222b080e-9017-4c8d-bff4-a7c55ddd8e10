﻿//------------------------------------------------------------
// Copyright (c) Microsoft Corporation.  All rights reserved.
//------------------------------------------------------------

namespace IntegrationTests.ResourceProvider.WorkspaceDelete.VnetInjectedWorkspace.FrontDoorBuilder
{
    using System;
    using System.Net;
    using System.Net.Http;
    using System.Net.Http.Headers;
    using IntegrationTests.ResourceProvider.BaseClasses;
    using IntegrationTests.ResourceProvider.WorkspaceCreate.VnetInjectedWorkspace;
    using Microsoft.WindowsAzure.ResourceStack.Common.Json;
    using WireMock.RequestBuilders;
    using WireMock.ResponseBuilders;
    using WireMock.Server;

    public class DeprovisioningWhenSkipUnPrepareOldNetworkIntentPoliciesAsyncFrontDoorBuilder : IWireMockServerBuilder
    {
        public WireMockServer Build()
        {
            var frontDoor = WireMockServer.StartWithAdminInterface(Constants.EndPoints.FrontDoorPort);
            frontDoor.AllowPartialMapping(true);
            VnetInjectedWorkspaceTestHelper.SetupAccountApiFeature(frontDoor);
            VnetInjectedWorkspaceTestHelper.SetupAccountApiDeleteResponse(frontDoor);

            var testVirtualNetworkString = VnetInjectedWorkspaceTestHelper.GetTestPreparedVirtualNetwork()
                .Replace("testSubscriptionId", Constants.RequestMetadata.SubscriptionId)
                .Replace("testResourceGroupName", Constants.RequestMetadata.ResourceGroup)
                .Replace("testPrivateSubnetName", "private-subnet")
                .Replace("testPublicSubnetName", "public-subnet")
                .Replace("testNetworkIntentPolicyName", "databricks-***********")
                .Replace("testVNetId", Constants.FrontDoor.VirtualNetworkUrl);


            frontDoor.Given(
                Request.Create().WithPath(Constants.FrontDoor.VirtualNetworkUrl).UsingGet())
                .AtPriority(1)
                .RespondWith(Response.Create()
                .WithHeader("Content-Type", "application/json")
                .WithStatusCode(200).
                WithBody(testVirtualNetworkString));

            var workspaceDetails =
             "{" +
                "\"WorkspaceId\":\"" + Constants.DBWorkspace.WorkspaceId + "\"," +
                "\"WorkspaceURL\":\"" + Constants.DBWorkspace.WorkspaceUrl + "\"," +
                "\"isPrivateLinkAllowed\":false," +
                "\"privateLinkAssets\": []," +
                "\"isUcEnabled\":false" +
             "}";

            frontDoor.Given(
                Request.Create().WithPath(Constants.FrontDoor.WorkspaceInitializationUrl)
                .UsingGet())
                .AtPriority(1)
                .RespondWith(Response.Create()
                .WithHeader("Content-Type", "application/json")
                .WithStatusCode(200)
                .WithBody(workspaceDetails));

            var asyncOperationUrl = $@"{Constants.RequestBody.ManagedResourceGroupId}/providers/Microsoft.Resources/deployments/{Constants.RequestMetadata.Name}/operationStatuses/08585353991691056296";

            frontDoor.Given(
                Request.Create()
                .WithPath(asyncOperationUrl)
                .UsingGet()).RespondWith(Response.Create()
                .WithHeader("Content-Type", "application/json")
                .WithStatusCode(HttpStatusCode.OK)
                .WithBody(""));

            var message = new HttpResponseMessage(HttpStatusCode.Accepted)
            {
                Headers =
                    {
                        RetryAfter = RetryConditionHeaderValue.Parse("1"),
                        Location = new Uri(Constants.EndPoints.FrontDoorHost + asyncOperationUrl)
                    }
            };

            frontDoor.Given(
                Request.Create().WithPath(Constants.FrontDoor.UnpreparedVNet1PrivateRequestUrl).UsingPost())
                .RespondWith(Response.Create()
                .WithHeader("Location", Constants.EndPoints.FrontDoorHost + asyncOperationUrl)
                .WithStatusCode(HttpStatusCode.Accepted).
                WithBody(message.ToJson()));

            frontDoor.Given(
                Request.Create().WithPath(Constants.FrontDoor.UnpreparedVNet1PublicRequestUrl).UsingPost())
                .RespondWith(Response.Create()
                .WithHeader("Location", Constants.EndPoints.FrontDoorHost + asyncOperationUrl)
                .WithStatusCode(HttpStatusCode.Accepted).
                WithBody(message.ToJson()));

            var managedResourceGroup = "{" +
                    "\"Id\":\"" + Constants.RequestBody.ManagedResourceGroupId + "\"," +
                    "\"Name\":\"" + Constants.RequestBody.ManagedResourceGroupName + "\"," +
                    "\"Location\":\"" + Constants.RequestBody.Location + "\"," +
                    "\"ManagedBy\":\"" + Constants.RequestMetadata.ResourceId + "\"," +
                 "}";

            frontDoor.Given(
                Request.Create()
                .WithPath(Constants.FrontDoor.GetManagedResourceGroup)
                .UsingGet())
                .AtPriority(1)
                .RespondWith(Response.Create()
                .WithHeader("Content-Type", "application/json")
                .WithStatusCode(200)
                .WithBody(managedResourceGroup));

            frontDoor.Given(
                Request.Create()
                .WithPath(Constants.FrontDoor.GetManagedResourceGroup)
                .UsingPut())
                .AtPriority(1)
                .RespondWith(Response.Create()
                .WithHeader("Content-Type", "application/json")
                .WithStatusCode(200)
                .WithBody(managedResourceGroup));

            return frontDoor;
        }
    }
}

