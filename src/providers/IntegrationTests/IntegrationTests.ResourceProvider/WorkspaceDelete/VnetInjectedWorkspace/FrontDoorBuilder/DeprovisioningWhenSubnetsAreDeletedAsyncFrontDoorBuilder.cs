﻿//------------------------------------------------------------
// Copyright (c) Microsoft Corporation.  All rights reserved.
//------------------------------------------------------------

namespace IntegrationTests.ResourceProvider.WorkspaceDelete.VnetInjectedWorkspace.FrontDoorBuilder
{
    using System.Net;
    using IntegrationTests.ResourceProvider.BaseClasses;
    using IntegrationTests.ResourceProvider.WorkspaceCreate.VnetInjectedWorkspace;
    using WireMock.RequestBuilders;
    using WireMock.ResponseBuilders;
    using WireMock.Server;

    public class DeprovisioningWhenSubnetsAreDeletedAsyncFrontDoorBuilder : IWireMockServerBuilder
    {
        public WireMockServer Build()
        {
            var frontDoor = WireMockServer.StartWithAdminInterface(Constants.EndPoints.FrontDoorPort);
            frontDoor.AllowPartialMapping(true);
            VnetInjectedWorkspaceTestHelper.SetupAccountApiFeature(frontDoor);
            VnetInjectedWorkspaceTestHelper.SetupAccountApiDeleteResponse(frontDoor);

            var testVirtualNetworkString = VnetInjectedWorkspaceTestHelper.GetTestVirtualNetworkWithoutSubnets()
                .Replace("testSubscriptionId", Constants.RequestMetadata.SubscriptionId)
                .Replace("testResourceGroupName", Constants.RequestMetadata.ResourceGroup)
                .Replace("testVNetId", Constants.FrontDoor.VirtualNetworkUrl);

            frontDoor.Given(
                Request.Create().WithPath(Constants.FrontDoor.VirtualNetworkUrl).UsingPost())
                .RespondWith(Response.Create()
                .WithHeader("Content-Type", "application/json")
                .WithStatusCode(200)
                .WithBody(testVirtualNetworkString));

            frontDoor.Given(
                Request.Create()
                .WithPath(Constants.FrontDoor.UnpreparedVNet1PrivateRequestUrl).UsingPost())
                .RespondWith(Response.Create()
                .WithStatusCode(HttpStatusCode.InternalServerError)
                .WithBody(string.Empty));

            frontDoor.Given(
                Request.Create()
                 .WithPath(Constants.FrontDoor.UnpreparedVNet1PublicRequestUrl).UsingPost())
                 .RespondWith(Response.Create()
                 .WithStatusCode(HttpStatusCode.InternalServerError)
                 .WithBody(string.Empty));

            var managedResourceGroup = "{" +
                "\"Id\":\"" + Constants.RequestBody.ManagedResourceGroupId + "\"," +
                "\"Name\":\"" + Constants.RequestBody.ManagedResourceGroupName + "\"," +
                "\"Location\":\"" + Constants.RequestBody.Location + "\"," +
                "\"ManagedBy\":\"" + Constants.RequestMetadata.ResourceId + "\"," +
                "}";

            frontDoor.Given(
                Request.Create()
                .WithPath(Constants.FrontDoor.GetManagedResourceGroup)
                .UsingGet())
                .AtPriority(1)
                .RespondWith(Response.Create()
                .WithHeader("Content-Type", "application/json")
                .WithStatusCode(200)
                .WithBody(managedResourceGroup));

            frontDoor.Given(
                Request.Create()
                .WithPath(Constants.FrontDoor.GetManagedResourceGroup)
                .UsingPut())
                .AtPriority(1)
                .RespondWith(Response.Create()
                .WithHeader("Content-Type", "application/json")
                .WithStatusCode(200)
                .WithBody(managedResourceGroup));

            return frontDoor;
        }
    }
}

