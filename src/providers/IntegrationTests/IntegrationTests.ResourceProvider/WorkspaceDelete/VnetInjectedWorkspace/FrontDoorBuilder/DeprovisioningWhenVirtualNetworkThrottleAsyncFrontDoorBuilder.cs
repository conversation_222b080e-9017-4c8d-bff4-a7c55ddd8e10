﻿//------------------------------------------------------------
// Copyright (c) Microsoft Corporation.  All rights reserved.
//------------------------------------------------------------

namespace IntegrationTests.ResourceProvider.WorkspaceDelete.VnetInjectedWorkspace.FrontDoorBuilder
{
    using System.Net;
    using IntegrationTests.ResourceProvider.BaseClasses;
    using IntegrationTests.ResourceProvider.WorkspaceCreate.VnetInjectedWorkspace;
    using WireMock.RequestBuilders;
    using WireMock.ResponseBuilders;
    using WireMock.Server;

    public class DeprovisioningWhenVirtualNetworkThrottleAsyncFrontDoorBuilder : IWireMockServerBuilder
    {
        public WireMockServer Build()
        {
            var frontDoor = WireMockServer.StartWithAdminInterface(Constants.EndPoints.FrontDoorPort);
            frontDoor.AllowPartialMapping(true);
            VnetInjectedWorkspaceTestHelper.SetupAccountApiFeature(frontDoor);
            VnetInjectedWorkspaceTestHelper.SetupAccountApiDeleteResponse(frontDoor);

            var testVirtualNetworkString = VnetInjectedWorkspaceTestHelper.GetTestVirtualNetwork()
                .Replace("testSubscriptionId", Constants.RequestMetadata.SubscriptionId)
                .Replace("testResourceGroupName", Constants.RequestMetadata.ResourceGroup)
                .Replace("testPrivateSubnetName", "private-subnet")
                .Replace("testPublicSubnetName", "public-subnet")
                .Replace("testVNetId", Constants.FrontDoor.VirtualNetworkUrl);

            frontDoor.Given(
                    Request.Create()
                        .WithPath(Constants.FrontDoor.VirtualNetworkUrl).UsingGet())
                .AtPriority(1)
                .RespondWith(Response.Create()
                    .WithHeader("Content-Type", "application/json")
                    .WithStatusCode(200)
                    .WithBody(testVirtualNetworkString));

            var errorResponseJson = @"{
                      ""error"": {
                        ""code"": ""RetryableError"",
                        ""message"": ""A retryable error occurred."",
                        ""details"": []
                      }
                    }";

            frontDoor.Given(
                    Request.Create()
                        .WithPath(Constants.FrontDoor.UnpreparedVNet1PrivateRequestUrl).UsingPost())
                .RespondWith(Response.Create()
                    .WithStatusCode((HttpStatusCode)429)
                    .WithBody(errorResponseJson));

            var workspaceDetails =
                 "{" +
                    "\"WorkspaceId\":\"" + Constants.DBWorkspace.WorkspaceId + "\"," +
                    "\"WorkspaceURL\":\"" + Constants.DBWorkspace.WorkspaceUrl + "\"," +
                    "\"isPrivateLinkAllowed\":false," +
                    "\"privateLinkAssets\": []," +
                    "\"isUcEnabled\":false" +
                 "}";

            frontDoor.Given(
                Request.Create().WithPath(Constants.FrontDoor.WorkspaceInitializationUrl)
                .UsingGet())
                .AtPriority(1)
                .RespondWith(Response.Create()
                .WithHeader("Content-Type", "application/json")
                .WithStatusCode(200)
                .WithBody(workspaceDetails));

            var managedResourceGroup = "{" +
                "\"Id\":\"" + Constants.RequestBody.ManagedResourceGroupId + "\"," +
                "\"Name\":\"" + Constants.RequestBody.ManagedResourceGroupName + "\"," +
                "\"Location\":\"" + Constants.RequestBody.Location + "\"," +
                "\"ManagedBy\":\"" + Constants.RequestMetadata.ResourceId + "\"," +
             "}";

            frontDoor.Given(
                Request.Create()
                .WithPath(Constants.FrontDoor.GetManagedResourceGroup)
                .UsingGet())
                .AtPriority(1)
                .RespondWith(Response.Create()
                .WithHeader("Content-Type", "application/json")
                .WithStatusCode(200)
                .WithBody(managedResourceGroup));

            frontDoor.Given(
                Request.Create()
                .WithPath(Constants.FrontDoor.GetManagedResourceGroup)
                .UsingPut())
                .AtPriority(1)
                .RespondWith(Response.Create()
                .WithHeader("Content-Type", "application/json")
                .WithStatusCode(200)
                .WithBody(managedResourceGroup));

            return frontDoor;
        }
    }
}

