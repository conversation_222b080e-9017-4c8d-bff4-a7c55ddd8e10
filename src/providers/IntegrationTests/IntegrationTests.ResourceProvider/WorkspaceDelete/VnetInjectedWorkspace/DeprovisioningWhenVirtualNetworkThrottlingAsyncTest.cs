﻿//------------------------------------------------------------
// Copyright (c) Microsoft Corporation.  All rights reserved.
//------------------------------------------------------------

using System.Net;

namespace IntegrationTests.ResourceProvider.WorkspaceDelete.WorkspaceDeleteWithPrivateEndpoint
{
    using System.Threading.Tasks;
    using IntegrationTests.ResourceProvider.BaseClasses;
    using IntegrationTests.ResourceProvider.WorkspaceDelete.VnetInjectedWorkspace.DatabaseSeeder;
    using IntegrationTests.ResourceProvider.WorkspaceDelete.VnetInjectedWorkspace.FrontDoorBuilder;
    using Microsoft.VisualStudio.TestTools.UnitTesting;
    using RestSharp;

    [TestClass]
    public class DeprovisioningWhenVirtualNetworkThrottlingAsyncTest : BaseIntegrationTest<DeprovisioningWhenVirtualNetworkThrottleAsyncFrontDoor<PERSON><PERSON><PERSON>, VnetInjectedWorkspaceDatabaseSeeder, BaseCustomSettings>
    {
        [TestMethod]
        public async Task WorkspaceDeleteWithNetworkThrottleSucceedTest()
        {
            string errorMessage = "A retryable error occurred.";
            using var rp = KestrelHost.Start(Constants.EndPoints.RPHost);
            var putClient = new RestClient(Constants.RequestMetadata.RequestUrl);
           
            var putRequest = new WorkspaceDeleteWithPrivateEndpointRequestBuilder().Build();
            putRequest.Method = Method.Delete;

            var putResponse = await putClient.ExecuteAsync(putRequest);
            Assert.IsNotNull(putResponse);

            await ApplianceTestHelper.WaitForDeprovisioningCompletionAndVerifyResult(putResponse,
                HttpStatusCode.OK,
                ErrorResponseCode.RetryableError.ToString(),
                errorMessage);
        }
    }
}
