﻿//------------------------------------------------------------
// Copyright (c) Microsoft Corporation.  All rights reserved.
//------------------------------------------------------------

namespace IntegrationTests.ResourceProvider.WorkspaceDelete.ServerlessWorkspace
{
    using Microsoft.VisualStudio.TestTools.UnitTesting;
    using RestSharp;
    using System.Threading.Tasks;
    using IntegrationTests.ResourceProvider.BaseClasses;
    using IntegrationTests.ResourceProvider.WorkspaceDelete.ServerlessWorkspace.DatabaseSeeder;
    using IntegrationTests.ResourceProvider.WorkspaceDelete.ServerlessWorkspace.FrontDoorBuilder;
    using IntegrationTests.ResourceProvider.WorkspaceDelete.ServerlessWorkspace.RequestBuilder;

    [TestClass]
    public class DeprovisioningServerlessWorkspaceTest : BaseIntegrationTest<DeprovisioningServerlessWorkspaceF<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Server<PERSON>WorkspaceDatabaseSeeder, BaseCustomSettings>
    {
        /// <summary>
        /// Test deletion operation which fails with during un prepare of private subnet with conflict in network intent policy.
        /// </summary>
        [TestMethod]
        public async Task DatabricksServerlessWorkspaceTests_ApplianceDeprovisioningJob_Successful()
        {
            using (var rp = KestrelHost.Start(Constants.EndPoints.RPHost))
            {
                var putClient = new RestClient(Constants.ServerlessRequestMetadata.RequestUrl);
                var putRequest = new DeprovisioningServerlessWorkspaceRequestBuilder().Build();
                var putResponse = await putClient.DeleteAsync(putRequest);
                Assert.IsNotNull(putResponse);

                await ApplianceTestHelper.WaitForDeprovisioningCompletionAndVerifyResult(putResponse);
            }
        }
    }
}
