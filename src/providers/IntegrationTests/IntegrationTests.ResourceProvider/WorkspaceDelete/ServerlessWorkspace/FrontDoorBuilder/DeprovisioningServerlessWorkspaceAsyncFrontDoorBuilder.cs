﻿//------------------------------------------------------------
// Copyright (c) Microsoft Corporation.  All rights reserved.
//------------------------------------------------------------

namespace IntegrationTests.ResourceProvider.WorkspaceDelete.ServerlessWorkspace.FrontDoorBuilder
{
    using System.Net;
    using IntegrationTests.ResourceProvider.BaseClasses;
    using IntegrationTests.ResourceProvider.WorkspaceCreate.ServerlessWorkspace;
    using IntegrationTests.ResourceProvider.WorkspaceCreate.VnetInjectedWorkspace;
    using WireMock.RequestBuilders;
    using WireMock.ResponseBuilders;
    using WireMock.Server;

    public class DeprovisioningServerlessWorkspaceAsyncFrontDoorBuilder : IWireMockServerBuilder
    {
        public WireMockServer Build()
        {
            var frontDoor = WireMockServer.StartWithAdminInterface(Constants.EndPoints.FrontDoorPort);
            frontDoor.AllowPartialMapping(true);
            VnetInjectedWorkspaceTestHelper.SetupAccountApiFeature(frontDoor);
            VnetInjectedWorkspaceTestHelper.SetupAccountApiDeleteResponse(frontDoor);

            var workspaceDetails =
             "{" +
                "\"WorkspaceId\":\"" + Constants.DBWorkspace.WorkspaceId + "\"," +
                "\"WorkspaceURL\":\"" + Constants.DBWorkspace.WorkspaceUrl + "\"," +
                "\"isPrivateLinkAllowed\":false," +
                "\"privateLinkAssets\": []," +
                "\"isUcEnabled\":false" +
             "}";

            frontDoor.Given(
                Request.Create().WithPath(Constants.FrontDoor.WorkspaceInitializationUrl)
                .UsingGet())
                .AtPriority(1)
                .RespondWith(Response.Create()
                .WithHeader("Content-Type", "application/json")
                .WithStatusCode(200)
                .WithBody(workspaceDetails));

            return frontDoor;
        }
    }
}

