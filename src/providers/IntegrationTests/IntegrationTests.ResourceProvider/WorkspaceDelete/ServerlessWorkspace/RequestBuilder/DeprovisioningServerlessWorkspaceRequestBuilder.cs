﻿//------------------------------------------------------------
// Copyright (c) Microsoft Corporation.  All rights reserved.
//------------------------------------------------------------

namespace IntegrationTests.ResourceProvider.WorkspaceDelete.ServerlessWorkspace.RequestBuilder
{
    using IntegrationTests.ResourceProvider.BaseClasses;
    using RestSharp;

    internal class DeprovisioningServerlessWorkspaceRequestBuilder : BaseRequestBuilder
    {
        public override RestRequest Update(RestRequest request)
        {
            var requestBody = ApplianceTestHelper.GetAppliancePutContentServerlessWorkspace();
            request.AddStringBody(requestBody, DataFormat.Json);
            return request;
        }
    }
}
