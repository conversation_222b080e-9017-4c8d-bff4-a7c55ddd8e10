﻿//-----------------------------------------------------------
// Copyright (c) Microsoft Corporation.  All rights reserved.
//-----------------------------------------------------------

namespace IntegrationTests.ResourceProvider.WorkspaceDelete.WorkspaceDeleteRegisterMrgFailsTests
{
    using System;
    using System.Net;
    using System.Net.Http;
    using System.Net.Http.Headers;
    using IntegrationTests.ResourceProvider.BaseClasses;
    using IntegrationTests.ResourceProvider.WorkspaceCreate.VnetInjectedWorkspace;
    using Microsoft.WindowsAzure.ResourceStack.Common.Json;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Common.ErrorResponses;
    using WireMock.RequestBuilders;
    using WireMock.ResponseBuilders;
    using WireMock.Server;

    public class DeleteWorkspaceCreateMrgFailureAsMaxRgQuotaReachedForSubFrontDoorBuilder : IWireMockServerBuilder
    {
        public WireMockServer Build()
        {
            var frontDoor = WireMockServer.StartWithAdminInterface(Constants.EndPoints.FrontDoorPort);
            frontDoor.AllowPartialMapping(true);
            frontDoor.Given(
                Request.Create()
                .WithPath(Constants.FrontDoor.FeaturesUrl)
                .UsingGet()
            ).RespondWith(
                Response.Create()
                .WithHeader("Content-Type", "application/json")
                .WithStatusCode(200)
                .WithBody("{ \"value\":[] }"));
            var privateEndpointRemovalJobAsyncUri = $@"{Constants.RequestBody.ManagedResourceGroupId}/providers/Microsoft.Resources/deployments/{Constants.RequestMetadata.Name}/operationStatuses/08585353991691056296";

            frontDoor.Given(
                Request.Create()
                 .WithPath(Constants.FrontDoor.PrivateEndpointConnectionUrl).UsingDelete())
                 .RespondWith(Response.Create()
                 .WithStatusCode(HttpStatusCode.Accepted)
                 .WithHeader("Location", Constants.EndPoints.FrontDoorHost + privateEndpointRemovalJobAsyncUri)
            .WithBody(string.Empty));

            var workspaceDetails =
                 "{" +
                    "\"WorkspaceId\":\"" + Constants.DBWorkspace.WorkspaceId + "\"," +
                    "\"WorkspaceURL\":\"" + Constants.DBWorkspace.WorkspaceUrl + "\"," +
                    "\"isPrivateLinkAllowed\":false," +
                    "\"privateLinkAssets\": []," +
                    "\"isUcEnabled\":false" +
                 "}";

            frontDoor.Given(
                Request.Create().WithPath(Constants.FrontDoor.WorkspaceInitializationUrl)
                .UsingGet())
                .AtPriority(1)
                .RespondWith(Response.Create()
                .WithHeader("Content-Type", "application/json")
                .WithStatusCode(200)
                .WithBody(workspaceDetails));

            var managedResourceGroup = "{" +
                "\"Id\":\"" + Constants.RequestBody.ManagedResourceGroupId + "\"," +
                "\"Name\":\"" + Constants.RequestBody.ManagedResourceGroupName + "\"," +
                "\"Location\":\"" + Constants.RequestBody.Location + "\"," +
                "\"ManagedBy\":\"" + Constants.RequestMetadata.ResourceId + "\"," +
             "}";

            frontDoor.Given(
                Request.Create()
                .WithPath(Constants.FrontDoor.GetManagedResourceGroup)
                .UsingGet())
                .AtPriority(0)
                .RespondWith(Response.Create()
                .WithHeader("Content-Type", "application/json")
                .WithStatusCode(200)
                .WithBody(managedResourceGroup));

            frontDoor.Given(
                Request.Create()
                .WithPath(Constants.FrontDoor.GetManagedResourceGroup)
                .UsingDelete())
                .AtPriority(0)
                .RespondWith(Response.Create()
                .WithHeader("Content-Type", "application/json")
                .WithStatusCode(200));

            var mrgCreationFailure = $@"{{
                      ""error"": {{
                        ""code"": ""{ErrorResponseCode.CreateRgFailedAsMaxRGQuotaReachedForSubscription}"",
                        ""message"": ""{ErrorResponseMessages.CreateRgFailedAsMaxRGQuotaReachedForSubscription}"",
                        ""details"": []
                      }}
                    }}";

            frontDoor.Given(
                Request.Create()
                .WithPath(Constants.FrontDoor.GetManagedResourceGroup)
                .UsingPut())
                .AtPriority(0)
                .RespondWith(Response.Create()
                .WithHeader("Content-Type", "application/json")
                .WithStatusCode(409)
                .WithBody(mrgCreationFailure));


            var asyncOperationUrl = $@"{Constants.RequestBody.ManagedResourceGroupId}/providers/Microsoft.Resources/deployments/{Constants.RequestMetadata.Name}/operationStatuses/08585353991691056296";
            var message = new HttpResponseMessage(HttpStatusCode.Accepted)
            {
                Headers =
                    {
                        RetryAfter = RetryConditionHeaderValue.Parse("1"),
                        Location = new Uri(Constants.EndPoints.FrontDoorHost + asyncOperationUrl)
                    }
            };

            frontDoor.Given(
                Request.Create().WithPath(Constants.FrontDoor.UnPrepareNetworkPoliciesForVNet1PrivateSubnetUrl).UsingPost())
                .RespondWith(Response.Create()
                .WithHeader("Location", Constants.EndPoints.FrontDoorHost + asyncOperationUrl)
                .WithStatusCode(HttpStatusCode.Accepted).
                WithBody(message.ToJson()));

            frontDoor.Given(
                Request.Create().WithPath(Constants.FrontDoor.UnPrepareNetworkPoliciesForVNet1PublicSubnetUrl).UsingPost())
                .RespondWith(Response.Create()
                .WithHeader("Location", Constants.EndPoints.FrontDoorHost + asyncOperationUrl)
                .WithStatusCode(HttpStatusCode.Accepted).
                WithBody(message.ToJson()));

            return frontDoor;
        }
    }
}
