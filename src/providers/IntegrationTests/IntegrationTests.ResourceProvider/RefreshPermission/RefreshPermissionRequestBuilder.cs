﻿//------------------------------------------------------------
// Copyright (c) Microsoft Corporation.  All rights reserved.
//------------------------------------------------------------

namespace IntegrationTests.ResourceProvider.RefreshPermissionTest
{
    using IntegrationTests.ResourceProvider.BaseClasses;
    using RestSharp;

    public class RefreshPermissionRequestBuilder : BaseRequestBuilder
    {
        public string RequestBody { get; set; }

        public override RestRequest Update(RestRequest request)
        {
            var requestBody = "{}";

            request.AddStringBody(requestBody, DataFormat.Json);
            return request;
        }
    }
}
