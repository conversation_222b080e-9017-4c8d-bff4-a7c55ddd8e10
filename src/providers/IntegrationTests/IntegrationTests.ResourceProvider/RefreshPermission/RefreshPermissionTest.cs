﻿//------------------------------------------------------------
// Copyright (c) Microsoft Corporation.  All rights reserved.
//------------------------------------------------------------

namespace IntegrationTests.ResourceProvider.RefreshPermissionTest
{
    using System.Linq;
    using System.Net;
    using System.Threading;
    using System.Threading.Tasks;
    using IntegrationTests.ResourceProvider.BaseClasses;
    using Microsoft.Owin.Hosting;
    using Microsoft.VisualStudio.TestTools.UnitTesting;
    using Newtonsoft.Json.Linq;
    using RestSharp;

    [TestClass]
    public class RefreshPermissionTest : BaseIntegrationTest<RefreshPermissionFrontDoorBuilder, GetWorkspaceDatabaseSeeder, BaseCustomSettings>
    {
        [TestMethod]
        public async Task RefreshPermissions_Success()
        {
            using (var rp = KestrelHost.Start(Constants.EndPoints.RPHost))
            {
                var refreshPermssionPostClient = new RestClient(Constants.RefreshPermissionRequestMetadata.RequestUrl);
                var refreshPermissionRequestBuilder = new RefreshPermissionRequestBuilder();
                refreshPermissionRequestBuilder.RequestBody = "";
                var refreshPermissionRequest = refreshPermissionRequestBuilder.Build();
                var refreshPermissionResponse = await refreshPermssionPostClient.ExecutePostAsync(refreshPermissionRequest);

                var refreshJobUrl = (string)refreshPermissionResponse.Headers.Single(x => x.Name == "Location").Value;

                // Url response will point to frontdoor host, because all requests come through ARM.
                // But we are only testing the RP, so the call needs to be made to the RP Edpoint.
                refreshJobUrl = refreshJobUrl.Replace(Constants.EndPoints.FrontDoorHost, Constants.EndPoints.RPHost);

                RestResponse refreshJobStatusCheckResponse = null;
                var timeOutCount = Constants.TimeOuts.TimeoutCount;

                while (timeOutCount > 0)
                {
                    var getJobStatusClient = new RestClient(refreshJobUrl);
                    var jobStatusCheckRequest = new BaseRequestBuilder().Build();
                    refreshJobStatusCheckResponse = await getJobStatusClient.GetAsync(jobStatusCheckRequest);
                    Assert.IsNotNull(refreshJobStatusCheckResponse);

                    if (refreshJobStatusCheckResponse.StatusCode != HttpStatusCode.Accepted)
                    {
                        break;
                    }

                    Thread.Sleep(Constants.TimeOuts.TimeoutPeriod);
                    timeOutCount--;

                }

                var logs = this.Frontdoor.LogEntries.ToList();

                Assert.AreEqual(refreshJobStatusCheckResponse.StatusCode, HttpStatusCode.OK);

                // Assert frontdoor calls
                int registerUrlIndex = this.Frontdoor.LogEntries.ToList().FindIndex(x => x.RequestMessage.AbsoluteUrl == Constants.RefreshPermissionRequestMetadata.registerUrl);
                var registerUrlBody = JObject.Parse(this.Frontdoor.LogEntries.ToList()[registerUrlIndex].RequestMessage.Body.ToString());
                int mrgUrlIndex = this.Frontdoor.LogEntries.ToList().FindIndex(x => x.RequestMessage.AbsoluteUrl == Constants.RefreshPermissionRequestMetadata.CreateMRGUrl);
                int roleAssignmenttUrlIndex = this.Frontdoor.LogEntries.ToList().FindIndex(x => x.RequestMessage.AbsoluteUrl.Contains(Constants.RefreshPermissionRequestMetadata.CreateRoleAssignmentUrl));
                var roleAssignmenttUrlBody = JObject.Parse(this.Frontdoor.LogEntries.ToList()[registerUrlIndex].RequestMessage.Body.ToString());
                int denyAssignmentUrlIndex = this.Frontdoor.LogEntries.ToList().FindIndex(x => x.RequestMessage.AbsoluteUrl.Contains(Constants.RefreshPermissionRequestMetadata.CreateDenyAssignmentUrl));
                var denyAssignmentUrlBody = JObject.Parse(this.Frontdoor.LogEntries.ToList()[registerUrlIndex].RequestMessage.Body.ToString());


                Assert.AreNotEqual(registerUrlIndex.ToString(), "-1");
                Assert.AreEqual(registerUrlBody["resourceId"], Constants.RequestMetadata.ResourceId);
                Assert.AreNotEqual(mrgUrlIndex.ToString(), "-1");
                Assert.AreNotEqual(roleAssignmenttUrlIndex.ToString(), "-1");
                Assert.IsNotNull(roleAssignmenttUrlBody);
                Assert.AreNotEqual(denyAssignmentUrlIndex.ToString(), "-1");
                Assert.IsNotNull(denyAssignmentUrlBody);
            }
        }
    }
}
