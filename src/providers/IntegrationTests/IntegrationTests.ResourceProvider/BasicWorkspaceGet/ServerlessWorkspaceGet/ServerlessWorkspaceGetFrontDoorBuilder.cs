﻿//------------------------------------------------------------
// Copyright (c) Microsoft Corporation.  All rights reserved.
//------------------------------------------------------------

namespace IntegrationTests.ResourceProvider.ServerlessWorkspaceGet
{
    using IntegrationTests.ResourceProvider.BaseClasses;
    using WireMock.Server;

    public class ServerlessWorkspaceGetFrontDoorBuilder : IWireMockServerBuilder
    {
        public WireMockServer Build()
        {
            var frontDoor = WireMockServer.Start(Constants.EndPoints.FrontDoorPort);
            return frontDoor;
        }
    }
}
