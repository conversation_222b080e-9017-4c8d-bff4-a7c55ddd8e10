﻿//------------------------------------------------------------
// Copyright (c) Microsoft Corporation.  All rights reserved.
//------------------------------------------------------------

namespace IntegrationTests.ResourceProvider.BasicWorkspaceGet
{
    using System.Net;
    using System.Threading.Tasks;
    using IntegrationTests.ResourceProvider.BaseClasses;
    using Microsoft.Owin.Hosting;
    using Microsoft.VisualStudio.TestTools.UnitTesting;
    using Newtonsoft.Json.Linq;
    using RestSharp;

    [TestClass]
    public class BasicWorkspaceGetTest : BaseIntegrationTest<BasicWorkspaceGetFrontDoorBuilder, GetWorkspaceDatabaseSeeder, BaseCustomSettings>
    {
        [TestMethod]
        public async Task TestBasicWorkspaceRead()
        {
            using (var rp = KestrelHost.Start(Constants.EndPoints.RPHost))
            {
                var client = new RestClient(Constants.RequestMetadata.RequestUrl);
                var request = new BasicWorkspaceGetRequestBuilder().Build();
                var response = await client.GetAsync(request);

                Assert.IsNotNull(response);
                Assert.AreEqual(HttpStatusCode.OK, response.StatusCode);

                var ws = JObject.Parse(response.Content);

                Assert.AreEqual(Constants.RequestMetadata.Name, ws["name"].Value<string>());
                Assert.AreEqual(Constants.RequestMetadata.ResourceId, ws["id"].Value<string>());
                Assert.AreEqual(Constants.RequestBody.Location, ws["location"].Value<string>());
                Assert.AreEqual(Constants.RequestBody.ManagedResourceGroupId.ToLowerInvariant(), ws["properties"]["managedResourceGroupId"].Value<string>().ToLowerInvariant());
                Assert.AreEqual("Accepted", ws["properties"]["provisioningState"].Value<string>());

            }
        }
    }
}
