﻿//------------------------------------------------------------
// Copyright (c) Microsoft Corporation.  All rights reserved.
//------------------------------------------------------------

namespace IntegrationTests.ResourceProvider.BasicWorkspaceGet
{
    using IntegrationTests.ResourceProvider.BaseClasses;
    using WireMock.Server;

    public class BasicWorkspaceGetFrontDoorBuilder : IWireMockServerBuilder
    {
        public WireMockServer Build()
        {
            var frontDoor = WireMockServer.Start(Constants.EndPoints.FrontDoorPort);
            return frontDoor;
        }
    }
}
