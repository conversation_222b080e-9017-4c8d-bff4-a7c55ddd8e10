﻿//------------------------------------------------------------
// Copyright (c) Microsoft Corporation.  All rights reserved.
//------------------------------------------------------------

namespace IntegrationTests.ResourceProvider.DatabricksOperations
{
    using System.Net;
    using System.Threading.Tasks;
    using IntegrationTests.ResourceProvider.BaseClasses;
    using Microsoft.VisualStudio.TestTools.UnitTesting;
    using RestSharp;
    using WireMock.Server;
    using Newtonsoft.Json;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Common.Data;

    public class HealthCheckFrontDoorBuilder : IWireMockServerBuilder
    {
        public WireMockServer Build()
        {
            var frontDoor = WireMockServer.StartWithAdminInterface(Constants.EndPoints.FrontDoorPort);
            frontDoor.AllowPartialMapping(false);
            return frontDoor;
        }
    }

    [TestClass]
    public class HealthCheckControllerTest : BaseIntegrationTest<HealthCheckFrontDoorBuilder, BaseDatabaseSeeder, BaseCustomSettings>
    {
        [TestMethod]
        public async Task TestHealthCheckController_StorageCheck()
        {
            using (var rp = KestrelHost.Start(Constants.EndPoints.RPHost))
            {
                var client = new RestClient(Constants.HealthCheckRequestMetadata.RequestUrl + "/storage");
                var request = new BaseRequestBuilder().Build();
                var response = await client.GetAsync(request);

                Assert.IsNotNull(response);
                Assert.AreEqual(HttpStatusCode.OK, response.StatusCode);
            }
        }

        [TestMethod]
        public async Task TestHealthCheckController_ComputeCheck()
        {
            using (var rp = KestrelHost.Start(Constants.EndPoints.RPHost))
            {
                var client = new RestClient(Constants.HealthCheckRequestMetadata.RequestUrl + "/compute");
                var request = new BaseRequestBuilder().Build();
                var response = await client.GetAsync(request);

                Assert.IsNotNull(response);
                Assert.AreEqual(HttpStatusCode.OK, response.StatusCode);
            }
        }
    }
}
