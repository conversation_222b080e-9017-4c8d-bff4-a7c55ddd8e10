{"$schema": "https://schema.management.azure.com/schemas/2015-01-01/deploymentTemplate.json#", "contentVersion": "*******", "parameters": {"amlWorkspaceId": {"defaultValue": "", "type": "string"}, "customPrivateSubnetName": {"defaultValue": "", "type": "string"}, "customPublicSubnetName": {"defaultValue": "", "type": "string"}, "customVirtualNetworkId": {"defaultValue": "", "type": "string"}, "diskEncryptionSetName": {"defaultValue": "diskencryptionset", "type": "string"}, "enableFedRampCertification": {"defaultValue": "[bool('false')]", "type": "bool"}, "enableNoPublicIp": {"defaultValue": "[bool('false')]", "type": "bool"}, "isWorkspaceUpdateOperation": {"defaultValue": "[bool('false')]", "type": "bool"}, "isUcDefaultFeatureEnabled": {"defaultValue": "[bool('false')]", "type": "bool"}, "managedDisksCmkKeyAutoRotation": {"defaultValue": "[bool('false')]", "type": "bool"}, "managedDisksCmkKeyUri": {"defaultValue": "", "type": "string"}, "natGatewayName": {"defaultValue": "nat-gateway", "metadata": {"description": "Name of the NAT gateway"}, "type": "string"}, "prepareEncryption": {"defaultValue": "[bool('false')]", "type": "bool"}, "publicIpName": {"defaultValue": "nat-gw-public-ip", "metadata": {"description": "Name of the Public IP associated with the NAT gateway"}, "type": "string"}, "requireInfrastructureEncryption": {"defaultValue": "[bool('false')]", "type": "bool"}, "resourceTags": {"defaultValue": {"application": "databricks", "databricks-environment": "true"}, "type": "object"}, "storageAccountName": {"defaultValue": "[concat('dbstorage', uniqueString(resourceGroup().id, subscription().id))]", "type": "string"}, "storageAccountSkuName": {"defaultValue": "[if(contains(createArray('qatarcentral','mexicocentral'), resourceGroup().location), 'Standard_ZRS', 'Standard_GRS')]", "type": "string"}, "vnetAddressPrefix": {"defaultValue": "10.139", "type": "string"}}, "resources": [{"apiVersion": "2019-06-01", "condition": "[not(variables('enabledVnetInjection'))]", "location": "[resourceGroup().location]", "name": "workers-sg", "properties": {"securityRules": "[if(parameters('enableNoPublicIp'), variables('securityRulesNPIP'), variables('securityRulesPIP'))]"}, "tags": "[parameters('resourceTags')]", "type": "Microsoft.Network/networkSecurityGroups"}, {"apiVersion": "2019-06-01", "condition": "[variables('npipWithManagedVnet')]", "location": "[resourceGroup().location]", "name": "[parameters('publicIpName')]", "properties": {"idleTimeoutInMinutes": 20, "publicIPAddressVersion": "IPv4", "publicIPAllocationMethod": "Static"}, "sku": {"name": "Standard"}, "tags": "[parameters('resourceTags')]", "type": "Microsoft.Network/publicIPAddresses"}, {"apiVersion": "2019-06-01", "condition": "[variables('npipWithManagedVnet')]", "dependsOn": ["[resourceId('Microsoft.Network/publicIPAddresses', parameters('publicIpName'))]"], "location": "[resourceGroup().location]", "name": "[parameters('natGatewayName')]", "properties": {"idleTimeoutInMinutes": 20, "publicIpAddresses": [{"id": "[resourceId('Microsoft.Network/publicIPAddresses', parameters('publicIpName'))]"}]}, "sku": {"name": "Standard"}, "tags": "[parameters('resourceTags')]", "type": "Microsoft.Network/natGateways"}, {"apiVersion": "2016-03-30", "condition": "[not(variables('enabledVnetInjection'))]", "dependsOn": ["[resourceId('Microsoft.Network/networkSecurityGroups', 'workers-sg')]", "[resourceId('Microsoft.Network/natGateways', parameters('natGatewayName'))]"], "location": "[resourceGroup().location]", "name": "workers-vnet", "properties": {"addressSpace": {"addressPrefixes": ["[variables('vnetCidr')]"]}, "subnets": [{"name": "public-subnet", "properties": "[if(parameters('enableNoPublicIp'), variables('publicSubnetPropertiesNPIP'), variables('publicSubnetPropertiesPIP'))]"}, {"name": "private-subnet", "properties": "[if(parameters('enableNoPublicIp'), variables('privateSubnetPropertiesNPIP'), variables('privateSubnetPropertiesPIP'))]"}]}, "tags": "[parameters('resourceTags')]", "type": "Microsoft.Network/virtualNetworks"}, {"apiVersion": "2021-12-01", "condition": "[variables('enabledManagedDisksCmk')]", "identity": {"type": "SystemAssigned"}, "location": "[resourceGroup().location]", "name": "[parameters('diskEncryptionSetName')]", "properties": {"activeKey": {"keyUrl": "[parameters('managedDisksCmkKeyUri')]"}, "encryptionType": "EncryptionAtRestWithCustomerKey", "rotationToLatestKeyVersionEnabled": "[parameters('managedDisksCmkKeyAutoRotation')]"}, "tags": "[parameters('resourceTags')]", "type": "Microsoft.Compute/diskEncryptionSets"}, {"apiVersion": "2019-06-01", "comments": "Provides storage for the Databricks File System.", "kind": "StorageV2", "location": "[resourceGroup().location]", "name": "[parameters('storageAccountName')]", "condition": "[not(parameters('isWorkspaceUpdateOperation'))]", "properties": {"accessTier": "Hot", "minimumTlsVersion": "TLS1_2", "allowBlobPublicAccess": false, "encryption": "[if(equals(parameters('requireInfrastructureEncryption'), bool('true')), variables('doubleEncryptionSettings'), variables('singleEncryptionSettings'))]", "networkAcls": {"defaultAction": "Allow"}, "supportsHttpsTrafficOnly": true, "isHnsEnabled": true}, "resources": [{"apiVersion": "2019-06-01", "condition": "[not(parameters('isWorkspaceUpdateOperation'))]", "dependsOn": ["[parameters('storageAccountName')]"], "name": "default/ephemeral", "type": "blobServices/containers"}, {"apiVersion": "2019-06-01", "condition": "[not(parameters('isWorkspaceUpdateOperation'))]", "dependsOn": ["[parameters('storageAccountName')]"], "name": "default/jobs", "type": "blobServices/containers"}, {"apiVersion": "2019-06-01", "condition": "[not(parameters('isWorkspaceUpdateOperation'))]", "dependsOn": ["[parameters('storageAccountName')]"], "name": "default/logs", "type": "blobServices/containers"}, {"apiVersion": "2019-06-01", "condition": "[not(parameters('isWorkspaceUpdateOperation'))]", "dependsOn": ["[parameters('storageAccountName')]"], "name": "default/meta", "type": "blobServices/containers"}, {"apiVersion": "2019-06-01", "condition": "[not(parameters('isWorkspaceUpdateOperation'))]", "dependsOn": ["[parameters('storageAccountName')]"], "name": "default/root", "type": "blobServices/containers"}, {"apiVersion": "2019-06-01", "condition": "[variables('shouldUcArtifactsCreated')]", "dependsOn": ["[parameters('storageAccountName')]"], "name": "default/unity-catalog-storage", "type": "blobServices/containers"}], "sku": {"name": "[parameters('storageAccountSkuName')]"}, "tags": "[parameters('resourceTags')]", "type": "Microsoft.Storage/storageAccounts"}, {"type": "Microsoft.Storage/storageAccounts/managementPolicies", "apiVersion": "2019-06-01", "name": "[concat(parameters('storageAccountName'), '/default')]", "properties": {"policy": {"rules": [{"name": "deleteTempFiles", "enabled": true, "type": "Lifecycle", "definition": {"actions": {"baseBlob": {"delete": {"daysAfterModificationGreaterThan": 7}}}, "filters": {"blobTypes": ["blockBlob"], "prefixMatch": ["root/_$azuretmpfolder$/", "meta/_$azuretmpfolder$/", "jobs/_$azuretmpfolder$/", "ephemeral/_$azuretmpfolder$/", "logs/_$azuretmpfolder$/"]}}}, {"name": "deleteExpiredLogFiles", "enabled": true, "type": "Lifecycle", "definition": {"actions": {"baseBlob": {"delete": {"daysAfterModificationGreaterThan": 30}}}, "filters": {"blobTypes": ["blockBlob"], "prefixMatch": ["logs/"]}}}]}}, "dependsOn": ["[resourceId('Microsoft.Storage/storageAccounts', parameters('storageAccountName'))]"]}, {"apiVersion": "2015-08-31-PREVIEW", "comments": "Used to securely bootstrap databricks VMs", "location": "[resourceGroup().location]", "name": "dbmanagedidentity", "tags": "[parameters('resourceTags')]", "type": "Microsoft.ManagedIdentity/userAssignedIdentities"}, {"apiVersion": "2023-05-01", "condition": "[variables('shouldUcArtifactsCreated')]", "comments": "Default accessConnector used for connecting to DBFS for workspaces enabled with default UC.", "location": "[resourceGroup().location]", "name": "[variables('ucAccessConnectorName')]", "identity": {"type": "SystemAssigned"}, "tags": "[parameters('resourceTags')]", "type": "Microsoft.Databricks/accessConnectors"}, {"apiVersion": "2017-05-10", "name": "pid-6f37b3d5-5aba-4311-a591-86c1b077c046", "properties": {"mode": "Incremental", "template": {"$schema": "http://schema.management.azure.com/schemas/2015-01-01/deploymentTemplate.json#", "contentVersion": "*******", "resources": []}}, "type": "Microsoft.Resources/deployments"}], "variables": {"doubleEncryptionSettings": {"keySource": "Microsoft.Storage", "requireInfrastructureEncryption": true, "services": {"blob": {"enabled": true}}}, "enabledVnetInjection": "[not(or(or(empty(parameters('customVirtualNetworkId')), empty(parameters('customPublicSubnetName'))), empty(parameters('customPrivateSubnetName'))))]", "npipWithManagedVnet": "[and(parameters('enableNoPublicIp'),not(variables('enabledVnetInjection')))]", "shouldUcArtifactsCreated": "[and(not(parameters('isWorkspaceUpdateOperation')), parameters('isUcDefaultFeatureEnabled'))]", "enabledManagedDisksCmk": "[and(not(parameters('isWorkspaceUpdateOperation')), not(empty(parameters('managedDisksCmkKeyUri'))), not(equals(parameters('diskEncryptionSetName'), 'diskencryptionset')))]", "privateSubnetCidr": "[concat(parameters('vnetAddressPrefix'), '.64.0/18')]", "privateSubnetPropertiesNPIP": {"addressPrefix": "[variables('privateSubnetCidr')]", "natGateway": {"id": "[resourceId('Microsoft.Network/natGateways', parameters('natGatewayName'))]"}, "networkSecurityGroup": {"id": "[resourceId('Microsoft.Network/networkSecurityGroups', 'workers-sg')]"}}, "privateSubnetPropertiesPIP": {"addressPrefix": "[variables('privateSubnetCidr')]", "networkSecurityGroup": {"id": "[resourceId('Microsoft.Network/networkSecurityGroups', 'workers-sg')]"}}, "publicSubnetCidr": "[concat(parameters('vnetAddressPrefix'), '.0.0/18')]", "publicSubnetPropertiesNPIP": {"addressPrefix": "[variables('publicSubnetCidr')]", "natGateway": {"id": "[resourceId('Microsoft.Network/natGateways', parameters('natGatewayName'))]"}, "networkSecurityGroup": {"id": "[resourceId('Microsoft.Network/networkSecurityGroups', 'workers-sg')]"}}, "publicSubnetPropertiesPIP": {"addressPrefix": "[variables('publicSubnetCidr')]", "networkSecurityGroup": {"id": "[resourceId('Microsoft.Network/networkSecurityGroups', 'workers-sg')]"}}, "securityRuleSSH": {"name": "databricks-control-plane-ssh", "properties": {"access": "Allow", "description": "Required for Databricks control plane management of worker nodes.", "destinationAddressPrefix": "VirtualNetwork", "destinationPortRange": "22", "direction": "Inbound", "priority": 100, "protocol": "TCP", "sourceAddressPrefix": "AzureDatabricks", "sourcePortRange": "*"}}, "securityRuleWorkerProxy": {"name": "databricks-control-plane-worker-proxy", "properties": {"access": "Allow", "description": "Required for Databricks control plane communication with worker nodes.", "destinationAddressPrefix": "VirtualNetwork", "destinationPortRange": "5557", "direction": "Inbound", "priority": 110, "protocol": "TCP", "sourceAddressPrefix": "AzureDatabricks", "sourcePortRange": "*"}}, "securityRuleWorkerToWorker": {"name": "databricks-worker-to-worker", "properties": {"access": "Allow", "description": "Required for worker nodes communication within a cluster.", "destinationAddressPrefix": "VirtualNetwork", "destinationPortRange": "*", "direction": "Inbound", "priority": 200, "protocol": "*", "sourceAddressPrefix": "VirtualNetwork", "sourcePortRange": "*"}}, "securityRulesNPIP": "[createArray( variables('securityRuleWorkerToWorker') )]", "securityRulesPIP": "[createArray( variables('securityRuleWorkerToWorker'), variables('securityRuleSSH'), variables('securityRuleWorkerProxy') )]", "singleEncryptionSettings": {"keySource": "Microsoft.Storage", "services": {"blob": {"enabled": true}}}, "ucAccessConnectorName": "unity-catalog-access-connector", "vnetCidr": "[concat(parameters('vnetAddressPrefix'), '.0.0/16')]"}, "outputs": {"ucConnectorPrincipalId": {"condition": "[variables('shouldUcArtifactsCreated')]", "type": "string", "value": "[reference(resourceId('Microsoft.Databricks/accessConnectors', variables('ucAccessConnectorName')), '2023-05-01', 'full').identity.principalId]"}, "ucConnectorTenantId": {"condition": "[variables('shouldUcArtifactsCreated')]", "type": "string", "value": "[reference(resourceId('Microsoft.Databricks/accessConnectors', variables('ucAccessConnectorName')), '2023-05-01', 'full').identity.tenantId]"}}}