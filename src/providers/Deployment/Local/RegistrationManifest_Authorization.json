{
  "namespace": "Microsoft.Authorization",
  "providerVersion": "2.0",
  "providerType": "Internal, RegistrationFree",
  "resourceTypes": [
    {
      "name": "roleAssignments",
      "routingType": "Extension, ProxyOnly",
      "endpoints": [
        {
          "enabled": true,
          "apiVersion": "2014-07-01-preview",
          "endpointUri": "https://dev-shimrp.resources.windows-int.net:11717",
          "locations": [
            "",
          ],
          "timeout": "PT1M"
        }
      ],
      "loggingRules": [
        {
          "action": "Microsoft.Authorization/roleAssignments/write",
          "direction": "Request",
          "detailLevel": "Body"
        },
      ],
    },
    {
      "name": "roleDefinitions",
      "routingType": "Extension, ProxyOnly",
      "allowedUnauthorizedActions": [ "Microsoft.Authorization/roleDefinitions/read" ],
      "endpoints": [
        {
          "enabled": true,
          "apiVersion": "2014-07-01-preview",
          "endpointUri": "https://dev-shimrp.resources.windows-int.net:11717",
          "locations": [
            "",
          ],
          "timeout": "PT1M"
        }
      ]
    },
    {
      "name": "classicAdministrators",
      "routingType": "ProxyOnly",
      "allowedUnauthorizedActions": [
        "Microsoft.Authorization/classicAdministrators/read"
      ],
      "endpoints": [
        {
          "enabled": true,
          "apiVersion": "2014-07-01-preview",
          "endpointUri": "https://dev-shimrp.resources.windows-int.net:11717",
          "locations": [
            ""
          ],
          "timeout": "PT1M"
        },
        {
          "enabled": true,
          "apiVersion": "2014-10-01-preview",
          "endpointUri": "https://dev-shimrp.resources.windows-int.net:11717",
          "locations": [
            ""
          ],
          "timeout": "PT1M"
        }
      ]
    },
    {
      "name": "permissions",
      "routingType": "ProxyOnly, Extension",
      "endpoints": [
        {
          "enabled": true,
          "apiVersion": "2014-07-01-preview",
          "endpointUri": "https://dev-shimrp.resources.windows-int.net:11717",
          "locations": [
            ""
          ],
          "timeout": "PT1M"
        }
      ]
    },
    {
      "name": "locks",
      "routingType": "Extension, ProxyOnly",
      "endpoints": [
        {
          "enabled": true,
          "apiVersion": "2014-11-01-preview",
          "endpointUri": "https://dev-shimrp.resources.windows-int.net:11717",
          "locations": [
            "",
          ],
          "timeout": "PT1M"
        }
      ]
    }
  ]
}