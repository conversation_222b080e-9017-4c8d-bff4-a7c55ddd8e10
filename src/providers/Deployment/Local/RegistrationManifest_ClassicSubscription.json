{"namespace": "Microsoft.ClassicSubscription", "providerVersion": "2.0", "providerType": "Internal, RegistrationFree", "resourceTypes": [{"name": "operations", "routingType": "ProxyOnly", "resourceDeletionPolicy": "NotSpecified", "defaultApiVersion": "2017-06-01", "endpoints": [{"enabled": true, "apiVersion": "2017-06-01", "endpointUri": "https://dev-shimrp.resources.windows-int.net:11707", "locations": [""], "timeout": "PT1M"}], "marketplaceType": "NotSpecified"}]}