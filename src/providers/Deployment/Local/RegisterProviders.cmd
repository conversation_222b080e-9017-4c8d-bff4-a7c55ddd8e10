@echo off
setlocal

::****************** Register Providers.Test with the frontdoor ******************
call :registerResourceProvider put %SRCROOT%\providers\Deployment\Local\RegistrationManifest_Test.json "UseDevelopmentStorage=true;"

::****************** Register Providers.ClassicCompute with the frontdoor ******************
call :registerResourceProvider put %SRCROOT%\providers\Deployment\Local\RegistrationManifest_ClassicCompute.json "UseDevelopmentStorage=true;"

::****************** Register Providers.ClassicStorage with the frontdoor ******************
call :registerResourceProvider put %SRCROOT%\providers\Deployment\Local\RegistrationManifest_ClassicStorage.json "UseDevelopmentStorage=true;"

::****************** Register Providers.ClassicNetwork with the frontdoor ******************
call :registerResourceProvider put %SRCROOT%\providers\Deployment\Local\RegistrationManifest_ClassicNetwork.json "UseDevelopmentStorage=true;"

::****************** Register Providers.ClassicNetwork with the frontdoor ******************
call :registerResourceProvider put %SRCROOT%\providers\Deployment\Local\RegistrationManifest_ClassicSubscription.json "UseDevelopmentStorage=true;"

::****************** Register Providers.Authorization with the frontdoor ******************
call :registerResourceProvider put %SRCROOT%\providers\Deployment\Local\RegistrationManifest_Authorization.json "UseDevelopmentStorage=true;"

::****************** Register Providers.Authorization with the frontdoor ******************
call :registerResourceProvider put %SRCROOT%\providers\Deployment\Local\RegistrationManifest_Feature.json "UseDevelopmentStorage=true;"

::****************** Register Providers.Resources with the frontdoor ******************
call :registerResourceProvider put %SRCROOT%\providers\Deployment\Local\RegistrationManifest_Resources.json "UseDevelopmentStorage=true;"

::****************** Register Providers.Scheduler with the frontdoor ******************
call :registerResourceProvider put %SRCROOT%\providers\Deployment\Local\RegistrationManifest_Scheduler.json "UseDevelopmentStorage=true;"

::****************** Register Providers.Gallery with the frontdoor ******************
call :registerResourceProvider put %SRCROOT%\providers\Deployment\Local\RegistrationManifest_Gallery.json "UseDevelopmentStorage=true;"

goto :EOF


::****************** Register resource provider ********************
:registerResourceProvider
%OUTPUTROOT%\debug-AMD64\RPRegistrationTool\RPRegistrationTool.exe %1 %2 %3
goto :EOF

:EOF
