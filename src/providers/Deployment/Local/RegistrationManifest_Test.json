{"namespace": "Providers.Test", "providerVersion": "2.0", "providerType": "Internal, Hidden", "resourceTypes": [{"name": "statelessResources", "endpoints": [{"enabled": true, "apiVersion": "", "endpointUri": "https://dev-testrp.resources.windows-int.net:17665", "locations": ["DevFabric"], "timeout": "PT1M"}]}, {"name": "statefulResources", "endpoints": [{"enabled": true, "apiVersion": "", "endpointUri": "https://dev-testrp.resources.windows-int.net:17665", "locations": ["DevFabric"], "timeout": "PT1M"}]}, {"name": "statefulResources/nestedResources", "endpoints": [{"enabled": true, "apiVersion": "", "endpointUri": "https://dev-testrp.resources.windows-int.net:17665", "locations": ["DevFabric"], "timeout": "PT1M"}]}, {"name": "operations", "routingType": "ProxyOnly", "endpoints": [{"enabled": true, "apiVersion": "", "endpointUri": "https://dev-testrp.resources.windows-int.net:17665", "locations": ["DevFabric"], "timeout": "PT1M"}]}]}