{"namespace": "Microsoft.Portal", "providerVersion": "2.0", "providerType": "Internal", "resourceTypes": [{"name": "dashboards", "endpoints": [{"enabled": true, "apiVersion": "2015-08-01-preview", "endpointUri": "https://dev-shimrp.resources.windows-int.net:11707", "locations": ["DevFabric"], "timeout": "PT40S"}]}, {"name": "operations", "routingType": "<PERSON>xy<PERSON><PERSON><PERSON>, Tenant", "resourceDeletionPolicy": "NotSpecified", "allowedUnauthorizedActions": ["Microsoft.Portal/operations/read"], "endpoints": [{"enabled": true, "apiVersion": "2015-08-01-preview", "endpointUri": "https://dev-shimrp.resources.windows-int.net:11707", "locations": [""], "timeout": "PT40S"}], "marketplaceType": "NotSpecified"}]}