SET CSPACK="C:\Program Files\Microsoft SDKs\Azure\.NET SDK\v2.5\bin\cspack.exe"
SET CSRUN="C:\Program Files\Microsoft SDKs\Azure\Emulator\csrun.exe"

SET PackageRoot=%OUTPUTROOT%\debug-AMD64\RDPackage\Providers

:: Starting the storage emulator
%CSRUN% /devstore

:: Remove previous package layout
rmdir /S /Q %PackageRoot%\CSX

:: Run cspack to create package layout
%CSPACK% %PackageRoot%\ServiceDefinition.csdef /role:Providers.Test.razzle;%PackageRoot%\Roles\Test;Microsoft.WindowsAzure.ResourceStack.Providers.Test.Cloud.dll /rolePropertiesFile:Providers.Test.razzle;%PackageRoot%\Roles\Providers.Test.Properties.txt /role:Providers.Shim.razzle;%PackageRoot%\Roles\Shim;Microsoft.WindowsAzure.ResourceStack.Providers.Shim.Cloud.dll /rolePropertiesFile:Providers.Shim.razzle;%PackageRoot%\Roles\Providers.Shim.Properties.txt /role:Providers.Feature.razzle;%PackageRoot%\Roles\Feature;Microsoft.WindowsAzure.ResourceStack.Providers.Feature.Cloud.dll /rolePropertiesFile:Providers.Feature.razzle;%PackageRoot%\Roles\Providers.Feature.Properties.txt /role:Providers.Authorization.razzle;%PackageRoot%\Roles\Authorization;Microsoft.WindowsAzure.ResourceStack.Providers.Authorization.Cloud.dll /rolePropertiesFile:Providers.Authorization.razzle;%PackageRoot%\Roles\Providers.Authorization.Properties.txt /copyOnly /out:%PackageRoot%\CSX

:: Call csrun to run in Azure devfabric emulator
%CSRUN% /run:%PackageRoot%\CSX;%PackageRoot%\ServiceConfiguration.cscfg
