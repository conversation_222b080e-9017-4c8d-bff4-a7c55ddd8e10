{
  "namespace": "Microsoft.Resources",
  "providerVersion": "2.0",
  "providerType": "Internal, RegistrationFree",
  "resourceTypes": [
    {
      "name": "subscriptions",
      "routingType": "ProxyOnly",
      "resourceDeletionPolicy": "NotSpecified",
      "allowedUnauthorizedActions": [
        "Microsoft.Resources/Subscriptions/read"
      ],
      "endpoints": [
        {
          "enabled": true,
          "apiVersion": "",
          "endpointUri": "https://localhost:101/",
          "locations": [
            ""
          ],
          "timeout": "PT1M"
        }
      ],
      "marketplaceType": "NotSpecified"
    },
    {
      "name": "subscriptions/providers",
      "routingType": "ProxyOnly",
      "allowedUnauthorizedActions": [
        "Microsoft.Resources/subscriptions/Providers/read"
      ],
      "endpoints": [
        {
          "enabled": true,
          "apiVersion": "",
          "endpointUri": "https://localhost:101/",
          "locations": [
            ""
          ],
          "timeout": "PT1M"
        }
      ],
      "marketplaceType": "NotSpecified"
    },
    {
      "name": "subscriptions/operationresults",
      "routingType": "ProxyOnly",
      "allowedUnauthorizedActions": [
        "Microsoft.Resources/subscriptions/operationresults/read"
      ],
      "endpoints": [
        {
          "enabled": true,
          "apiVersion": "",
          "endpointUri": "https://localhost:101/",
          "locations": [
            ""
          ],
          "timeout": "PT1M"
        }
      ],
      "marketplaceType": "NotSpecified"
    },
    {
      "name": "resourceGroups",
      "routingType": "ProxyOnly",
      "linkedAccessChecks": [
        {
          "actionName": "Microsoft.Resources/subscriptions/resourceGroups/moveResources/action",
          "linkedProperty": "targetResourceGroup",
          "linkedAction": "Microsoft.Resources/subscriptions/resourceGroups/write"
        }
      ],
      "endpoints": [
        {
          "enabled": true,
          "apiVersion": "",
          "endpointUri": "https://localhost:101/",
          "locations": [
            "DevFabric"
          ],
          "timeout": "PT1M"
        }
      ],
      "loggingRules": [
        {
          "action": "Microsoft.Resources/subscriptions/resourceGroups/write",
          "direction": "Request,Response",
          "detailLevel": "Body"
        },
      ],
      "marketplaceType": "NotSpecified"
    },
    {
      "name": "subscriptions/locations",
      "routingType": "ProxyOnly",
      "allowedUnauthorizedActions": [
        "Microsoft.Resources/subscriptions/locations/read"
      ],
      "endpoints": [
        {
          "enabled": true,
          "apiVersion": "",
          "endpointUri": "https://localhost:101/",
          "locations": [
            ""
          ],
          "timeout": "PT1M"
        }
      ],
      "marketplaceType": "NotSpecified"
    },
    {
      "name": "subscriptions/tagnames",
      "routingType": "ProxyOnly",
      "allowedUnauthorizedActions": [
        "Microsoft.Resources/subscriptions/tagnames/read"
      ],
      "endpoints": [
        {
          "enabled": true,
          "apiVersion": "",
          "endpointUri": "https://localhost:101/",
          "locations": [
            ""
          ],
          "timeout": "PT1M"
        }
      ],
      "marketplaceType": "NotSpecified"
    },
    {
      "name": "links",
      "routingType": "Extension, ProxyOnly",
      "endpoints": [
        {
          "enabled": true,
          "apiVersion": "",
          "endpointUri": "https://dev-shimrp.resources.windows-int.net:11707",
          "locations": [
            "DevFabric"
          ],
          "timeout": "PT1M"
        }
      ],
    }
  ]
}
