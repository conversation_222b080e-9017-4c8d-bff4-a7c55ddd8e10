{"namespace": "Microsoft.Features", "providerVersion": "2.0", "providerType": "Internal, RegistrationFree", "resourceTypes": [{"name": "features", "routingType": "ProxyOnly", "allowedUnauthorizedActions": ["Microsoft.Features/features/read"], "endpoints": [{"enabled": true, "apiVersion": "2014-08-01-preview", "endpointUri": "https://dev-shimrp.resources.windows-int.net:11707", "locations": [""], "timeout": "PT1M"}]}, {"name": "providers", "routingType": "ProxyOnly", "allowedUnauthorizedActions": ["Microsoft.Features/providers/features/read"], "endpoints": [{"enabled": true, "apiVersion": "2014-08-01-preview", "endpointUri": "https://dev-shimrp.resources.windows-int.net:11707", "locations": [""], "timeout": "PT1M"}]}]}