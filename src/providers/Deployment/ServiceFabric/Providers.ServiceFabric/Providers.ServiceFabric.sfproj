﻿<?xml version="1.0" encoding="utf-8"?>
<!-- Settings DefaultTargets to "Package" to ensure sfproj packaging happens when executing MSBuild from the command-line without explicitly specifying the "Package" target. -->
<Project ToolsVersion="14.0" DefaultTargets="Build;Package" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <!-- Microsoft.VisualStudio.Azure.Fabric.Application.props sets the OutputPath to something cloud build doesn't like,
       set it to what it should be before importing it. -->
  <Import Project="..\..\..\..\packages\Microsoft.VisualStudio.Azure.Fabric.MSBuild.1.7.6\build\Microsoft.VisualStudio.Azure.Fabric.Application.props" Condition="Exists('..\..\..\..\packages\Microsoft.VisualStudio.Azure.Fabric.MSBuild.1.7.6\build\Microsoft.VisualStudio.Azure.Fabric.Application.props')" />
  <PropertyGroup Condition="'$(Configuration)'=='Debug'">
    <OutputPath>bin\Debug\</OutputPath>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)'=='Release'">
    <OutputPath>bin\Release\</OutputPath>
  </PropertyGroup>
  <PropertyGroup Label="Globals">
    <ProjectGuid>9eca8c49-9ef1-45d5-821e-2706e34737d7</ProjectGuid>
    <ProjectVersion>2.6</ProjectVersion>
    <MinToolsVersion>16.10</MinToolsVersion>
    <SupportedMSBuildNuGetPackageVersion>1.7.6</SupportedMSBuildNuGetPackageVersion>
    <TargetFrameworkVersion>v4.8</TargetFrameworkVersion>
  </PropertyGroup>
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug|x64">
      <Configuration>Debug</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|x64">
      <Configuration>Release</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <ItemGroup>
    <None Include="ApplicationPackageRoot\ApplicationManifest.xml" />
    <None Include="ApplicationParameters\Cloud.xml" />
    <None Include="ApplicationParameters\Local.1Node.xml" />
    <None Include="ApplicationParameters\Local.5Node.xml" />
    <None Include="PublishProfiles\Cloud.xml" />
    <None Include="PublishProfiles\Local.1Node.xml" />
    <None Include="PublishProfiles\Local.5Node.xml" />
    <None Include="Scripts\Deploy-FabricApplication.ps1" />
    <None Include="StartupServiceParameters\Cloud.xml" />
    <None Include="StartupServiceParameters\Local.1Node.xml" />
    <None Include="StartupServiceParameters\Local.5Node.xml" />
    <None Include="StartupServices.xml" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="packages.config" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\Providers.ServiceFabric.FeatureRole\Providers.ServiceFabric.FeatureRole.csproj" />
    <ProjectReference Include="..\Providers.ServiceFabric.JobRole\Providers.ServiceFabric.JobRole.csproj" />
    <ProjectReference Include="..\Providers.ServiceFabric.WorkerRole\Providers.ServiceFabric.WorkerRole.csproj" />
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.Common.targets" />
  <PropertyGroup>
    <ApplicationProjectTargetsPath>$(MSBuildExtensionsPath32)\Microsoft\VisualStudio\v$(VisualStudioVersion)\Service Fabric Tools\Microsoft.VisualStudio.Azure.Fabric.ApplicationProject.targets</ApplicationProjectTargetsPath>
  </PropertyGroup>
  <Import Project="$(ApplicationProjectTargetsPath)" Condition="Exists('$(ApplicationProjectTargetsPath)')" />
  <Import Project="..\..\..\..\packages\Microsoft.VisualStudio.Azure.Fabric.MSBuild.1.7.6\build\Microsoft.VisualStudio.Azure.Fabric.Application.targets" Condition="Exists('..\..\..\..\packages\Microsoft.VisualStudio.Azure.Fabric.MSBuild.1.7.6\build\Microsoft.VisualStudio.Azure.Fabric.Application.targets')" />
  <Target Name="ValidateMSBuildFiles" BeforeTargets="PrepareForBuild">
    <Error Condition="!Exists('..\..\..\..\packages\Microsoft.VisualStudio.Azure.Fabric.MSBuild.1.7.6\build\Microsoft.VisualStudio.Azure.Fabric.Application.props')" Text="Unable to find the '..\..\..\..\packages\Microsoft.VisualStudio.Azure.Fabric.MSBuild.1.7.6\build\Microsoft.VisualStudio.Azure.Fabric.Application.props' file. Please restore the 'Microsoft.VisualStudio.Azure.Fabric.MSBuild' Nuget package." />
    <Error Condition="!Exists('..\..\..\..\packages\Microsoft.VisualStudio.Azure.Fabric.MSBuild.1.7.6\build\Microsoft.VisualStudio.Azure.Fabric.Application.targets')" Text="Unable to find the '..\..\..\..\packages\Microsoft.VisualStudio.Azure.Fabric.MSBuild.1.7.6\build\Microsoft.VisualStudio.Azure.Fabric.Application.targets' file. Please restore the 'Microsoft.VisualStudio.Azure.Fabric.MSBuild' Nuget package." />
  </Target>
</Project>