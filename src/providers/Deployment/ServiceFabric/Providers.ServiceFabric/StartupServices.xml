﻿<?xml version="1.0" encoding="utf-8"?>
<StartupServicesManifest xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://schemas.microsoft.com/2011/01/fabric">
  <Parameters>
    <Parameter Name="Providers.ServiceFabric.JobRole_InstanceCount" DefaultValue="-1" />
    <Parameter Name="Providers.ServiceFabric.WorkerRole_InstanceCount" DefaultValue="-1" />
    <Parameter Name="Providers.ServiceFabric.FeatureRole_InstanceCount" DefaultValue="-1" />
  </Parameters>
  <Services>
    <!-- The section below creates instances of service types, when an instance of this 
         application type is created. You can also create one or more instances of service type using the 
         ServiceFabric PowerShell module.

         The attribute ServiceTypeName below must match the name defined in the imported ServiceManifest.xml file. -->
    <Service Name="Providers.ServiceFabric.JobRole" ServicePackageActivationMode="ExclusiveProcess">
      <StatelessService ServiceTypeName="Providers.ServiceFabric.JobRoleType" InstanceCount="[Providers.ServiceFabric.JobRole_InstanceCount]">
        <SingletonPartition />
      </StatelessService>
    </Service>
    <Service Name="Providers.ServiceFabric.WorkerRole" ServicePackageActivationMode="ExclusiveProcess">
      <StatelessService ServiceTypeName="Providers.ServiceFabric.WorkerRoleType" InstanceCount="[Providers.ServiceFabric.WorkerRole_InstanceCount]">
        <SingletonPartition />
      </StatelessService>
    </Service>
    <Service Name="Providers.ServiceFabric.FeatureRole" ServicePackageActivationMode="ExclusiveProcess">
      <StatelessService ServiceTypeName="Providers.ServiceFabric.FeatureRoleType" InstanceCount="[Providers.ServiceFabric.FeatureRole_InstanceCount]">
        <SingletonPartition />
      </StatelessService>
    </Service>
  </Services>
</StartupServicesManifest>