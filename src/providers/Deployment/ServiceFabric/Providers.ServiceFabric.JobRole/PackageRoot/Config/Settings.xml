﻿<?xml version="1.0" encoding="utf-8" ?>
<Settings xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://schemas.microsoft.com/2011/01/fabric">
  <!-- Add your custom configuration sections and parameters here -->
  <Section Name="JobRoleConfigSection">
    <Parameter Name="CloudEnvironment" Value="" />
    <Parameter Name="Microsoft.WindowsAzure.ResourceStack.Providers.RoleLocation" Value="" />
    <Parameter Name="MainTemplateFilePath_databricks.databricks-workspace-dev-previewdatabricks-workspace-dev.0.0.12" Value="" />
    <Parameter Name="MainTemplateFilePath_databricks.databricks-workspace-staging-previewdatabricks-workspace-staging.0.0.12" Value="" />
    <Parameter Name="MainTemplateFilePath_databricks.databricks-workspace-previewdatabricks-workspace.0.0.2" Value="" />
    <Parameter Name="KeyVault_Endpoint" Value="" />
    <Parameter Name="MdmAccount" Value="" />
    <Parameter Name="Microsoft.WindowsAzure.ResourceStack.Providers.FrontdoorEndpointUri" Value="" />
    <Parameter Name="Microsoft.WindowsAzure.ResourceStack.Providers.ArmMetadataEndpoint" Value="" />
    <Parameter Name="Microsoft.WindowsAzure.ResourceStack.Providers.Appliances.DatabricksApplicationId" Value="" />
    <Parameter Name="Microsoft.WindowsAzure.ResourceStack.Providers.Appliances.ServicePrincipalCertificateThumbprint" Value="" />
    <Parameter Name="Microsoft.WindowsAzure.ResourceStack.Providers.Appliances.ServicePrincipalCertificateSubjectName" Value="" />
    <Parameter Name="Microsoft.WindowsAzure.ResourceStack.Providers.Appliances.AadAllowedAudience" Value="" />
    <Parameter Name="AzureAD_SecretsAppId" Value="" />
    <Parameter Name="AzureAD_SecretsTenantId" Value="" />
    <Parameter Name="AzureAD_SecretsLoginTemplate" Value="" />
    <Parameter Name="PublisherTenantId_databricks.databricks-workspace-dev-previewdatabricks-workspace-dev.0.0.12" Value="" />
    <Parameter Name="AuthPrincipalId_databricks.databricks-workspace-dev-previewdatabricks-workspace-dev.0.0.12" Value="" />
    <Parameter Name="AuthPrincipalType_databricks.databricks-workspace-dev-previewdatabricks-workspace-dev.0.0.12" Value="" />
    <Parameter Name="PublisherTenantId_databricks.databricks-workspace-staging-previewdatabricks-workspace-staging.0.0.12" Value="" />
    <Parameter Name="AuthPrincipalId_databricks.databricks-workspace-staging-previewdatabricks-workspace-staging.0.0.12" Value="" />
    <Parameter Name="AuthPrincipalType_databricks.databricks-workspace-staging-previewdatabricks-workspace-staging.0.0.12" Value="" />
    <Parameter Name="PublisherTenantId_databricks.databricks-workspace-previewdatabricks-workspace.0.0.2" Value="" />
    <Parameter Name="AuthPrincipalId_databricks.databricks-workspace-previewdatabricks-workspace.0.0.2" Value="" />
    <Parameter Name="AuthPrincipalType_databricks.databricks-workspace-previewdatabricks-workspace.0.0.2" Value="" />
    <Parameter Name="MonitoringAgentVersion" Value="" />
    <Parameter Name="MonitoringInitConfig" Value="" />
    <Parameter Name="Monitoring_GCS_Environment" Value="" />
    <Parameter Name="Monitoring_GCS_Account" Value="" />
    <Parameter Name="Monitoring_GCS_Namespace" Value="" />
    <Parameter Name="Monitoring_GCS_Thumbprint" Value="" />
    <Parameter Name="Monitoring_Config_Version" Value="" />
    <Parameter Name="Monitoring_Agent_Version" Value="" />
    <Parameter Name="Monitoring_Use_Geneva_Config_Service" Value="" />
    <Parameter Name="SLIMetricsMonitoringAccount" Value="" />
    <Parameter Name="SLIMetricsNamespace" Value="" />
    <Parameter Name="EnableSLIMetricsCapture" Value="" />
    <Parameter Name="AllowedSLISyncOperations" Value="" />
    <Parameter Name="Microsoft.WindowsAzure.ResourceStack.Providers.FailHealthCheck" Value="" />
    <Parameter Name="Microsoft.WindowsAzure.ResourceStack.Providers.AllowedReadOnlyClientCertificates" Value="" />
    <Parameter Name="Microsoft.WindowsAzure.ResourceStack.Providers.AllowedAdminCertificates" Value="" />
    <Parameter Name="Microsoft.WindowsAzure.ResourceStack.Providers.Appliances.DefaultRetryInterval" Value="" />
    <Parameter Name="Microsoft.WindowsAzure.ResourceStack.Providers.Appliances.AadLoginTemplate" Value="" />
    <Parameter Name="Microsoft.WindowsAzure.ResourceStack.Providers.Databricks.Workspace.PublisherPackageIdMappings" Value="" />
    <Parameter Name="Microsoft.WindowsAzure.ResourceStack.Providers.Feature.ConfigurationCache.CacheInitializationTimeout" Value="" />
    <Parameter Name="Microsoft.WindowsAzure.ResourceStack.Providers.ServiceClient.RequestTimeout" Value="" />
    <Parameter Name="Microsoft.WindowsAzure.ResourceStack.Providers.ServiceClient.ConnectionLeaseTimeout" Value="" />
    <Parameter Name="Microsoft.WindowsAzure.ResourceStack.Providers.ServiceClient.MaxResponseContentSize" Value="" />
    <Parameter Name="Microsoft.WindowsAzure.ResourceStack.Providers.BackgroundJobs.JobDefinitionsTableName" Value="" />
    <Parameter Name="Microsoft.WindowsAzure.ResourceStack.Providers.BackgroundJobs.JobTriggersQueuePrefix" Value="" />
    <Parameter Name="Microsoft.WindowsAzure.ResourceStack.Providers.Feature.TelemetryRequest.TotalEventsLimit" Value="" />
    <Parameter Name="Microsoft.WindowsAzure.ResourceStack.Providers.Feature.TelemetryRequest.AllowedApplications" Value="" />
    <Parameter Name="DatabricksNetworkIntentPoliciesFilePath_databricks.databricks-workspace-dev-previewdatabricks-workspace-dev.0.0.12" Value="" />
    <Parameter Name="DatabricksNetworkIntentPoliciesFilePath_databricks.databricks-workspace-staging-previewdatabricks-workspace-staging.0.0.12" Value="" />
    <Parameter Name="DatabricksNetworkIntentPoliciesFilePath_databricks.databricks-workspace-previewdatabricks-workspace.0.0.2" Value="" />
    <Parameter Name="Microsoft.WindowsAzure.ResourceStack.Providers.Appliances.RegisterManagedByTenantApiVersion" Value="" />
    <Parameter Name="Microsoft.WindowsAzure.ResourceStack.Providers.Appliances.UnregisterManagedByTenantEnabled" Value="" />
    <Parameter Name="ServiceFabricMSIClientId" Value="" />
    <Parameter Name="Microsoft.WindowsAzure.ResourceStack.Providers.Region" Value="" />
    <Parameter Name="RecurringActions" Value="" />
    <Parameter Name="Microsoft.WindowsAzure.ResourceStack.Providers.BackgroundJobs.WorkspaceScanJobDefinitionsTableName" Value="" />
    <Parameter Name="Microsoft.WindowsAzure.ResourceStack.Providers.BackgroundJobs.WorkspaceScanJobTriggersQueuePrefix" Value="" />
    <Parameter Name="PrimaryJobsDataStorageAccountName" Value="" />
    <Parameter Name="WorkspaceDataStorageAccountName" Value="" />
    <Parameter Name="DSCPackageStorageAccountNameSuffix" Value="" />
    <Parameter Name="ARGEndpoint" Value="" />
    <Parameter Name="ShortRegionName" Value="" />
  </Section>
</Settings>
