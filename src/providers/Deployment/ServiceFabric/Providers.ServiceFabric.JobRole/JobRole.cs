﻿//------------------------------------------------------------
// Copyright (c) Microsoft Corporation.  All rights reserved.
//------------------------------------------------------------

namespace Providers.ServiceFabric.JobRole
{
    using System;
    using System.Collections.Generic;
    using System.Fabric;
    using System.Threading;
    using System.Threading.Tasks;
    using Microsoft.ServiceFabric.Services.Communication.Runtime;
    using Microsoft.ServiceFabric.Services.Runtime;
    using Microsoft.WindowsAzure.ResourceStack.Common.EventSources;
    using Microsoft.WindowsAzure.ResourceStack.Common.Services;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Common.Logging;
    using Providers.Job.Dispatcher;

    /// <summary>
    /// An instance of this class is created for each service instance by the Service Fabric runtime.
    /// </summary>
    public sealed class JobRole<TEventSource> : StatelessService
        where TEventSource : class, IProvidersEventSource
    {
        private TEventSource EventSource { get; }

        /// <summary>
        /// Gets or sets the workspace scan jobs dispatcher.
        /// </summary>
        private WorkspaceScanJobDispatcherClient WorkspaceScanJobDispatcherClient { get; set; }

        /// <summary>
        /// Gets the role location.
        /// </summary>
        private static string RoleLocation
        {
            get { return CloudConfigurationManager.GetConfiguration("Microsoft.WindowsAzure.ResourceStack.Providers.RoleLocation"); }
        }
        public JobRole(StatelessServiceContext context)
            : base(context)
        { }

        /// <summary>
        /// Optional override to create listeners (e.g., TCP, HTTP) for this service replica to handle client or user requests.
        /// </summary>
        /// <returns>A collection of listeners.</returns>
        protected override IEnumerable<ServiceInstanceListener> CreateServiceInstanceListeners()
        {
            return new ServiceInstanceListener[0];
        }

        /// <summary>
        /// Initializes a new instance of the <see cref="JobService{TEventSource}"/> class.
        /// </summary>
        /// <param name="context">Instance of the StatelessServiceContext.</param>
        /// <param name="eventSource"> Event source type.</param>
        public JobRole(StatelessServiceContext context, TEventSource eventSource)
            : base(context)
        {
            this.EventSource = eventSource;
            this.WorkspaceScanJobDispatcherClient = WorkspaceScanJobDispatcherClient.CreateWorkspaceScanJobDispatcherClient(
                eventSource: ProvidersLog.Current,
                location: RoleLocation);
        }

        /// <summary>
        /// This is the main entry point for your service instance.
        /// </summary>
        /// <param name="cancellationToken">Canceled when Service Fabric needs to shut down this service instance.</param>
        protected override async Task RunAsync(CancellationToken cancellationToken)
        {
            this.WorkspaceScanJobDispatcherClient = WorkspaceScanJobDispatcherClient.CreateWorkspaceScanJobDispatcherClient(
                eventSource: ProvidersLog.Current,
                location: RoleLocation);

            try
            {
                this.WorkspaceScanJobDispatcherClient.Start();

                this.EventSource.Debug(operationName: "JobRole.RunAsync", message: "Job role dispatcher is started");
                this.WorkspaceScanJobDispatcherClient.ProvisionSystemConsistencyJob().Wait();
                this.EventSource.Debug(operationName: "JobRole.RunAsync", message: "System consistency job is provisioned");
            }
            catch (Exception ex)
            {
                this.EventSource.Critical(
                    operationName: "JobRole.RunAsync",
                    message: "Unable to start Job Dispatcher.",
                    exception: ex);

                throw;
            }

            // The entry point for a service in Service Fabric is different from Cloud Servic entry point.
            // In Azure Cloud Service, runtime events are Event based, meaning that we need to register callback.
            // In Service Fabric, for a worker role, the RunAsync method is called, and use the cancellationToken a event to stop the execution.
            // As the current does not support Async scenario and does not support CancellationToken support, we need to loop on the cancellation status.
            while (!cancellationToken.IsCancellationRequested)
            {
                await Task.Delay(10000, cancellationToken);
                await Task.FromResult(true);
            }
        }
    }
}
