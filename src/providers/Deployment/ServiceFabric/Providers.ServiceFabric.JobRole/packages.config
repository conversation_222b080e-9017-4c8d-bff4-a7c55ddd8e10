﻿<?xml version="1.0" encoding="utf-8"?>
<packages>
  <package id="Microsoft.Azure.KeyVault.Core" version="1.0.0" targetFramework="net472" />
  <package id="Microsoft.Bcl.AsyncInterfaces" version="8.0.0" targetFramework="net472" />
  <package id="Microsoft.Bcl.Build" version="1.0.21" targetFramework="net48" />
  <package id="Microsoft.Extensions.DependencyInjection" version="8.0.0" targetFramework="net472" />
  <package id="Microsoft.Extensions.DependencyInjection.Abstractions" version="8.0.0" targetFramework="net472" />
  <package id="Microsoft.PowerPlatform.ResourceStack" version="6.0.0.1386" targetFramework="net48" />
  <package id="Microsoft.ServiceFabric" version="9.1.1833" targetFramework="net472" />
  <package id="Microsoft.ServiceFabric.Data" version="6.1.1833" targetFramework="net472" />
  <package id="Microsoft.ServiceFabric.Data.Extensions" version="6.1.1833" targetFramework="net472" />
  <package id="Microsoft.ServiceFabric.Data.Interfaces" version="6.1.1833" targetFramework="net472" />
  <package id="Microsoft.ServiceFabric.Diagnostics.Internal" version="6.1.1833" targetFramework="net472" />
  <package id="Microsoft.ServiceFabric.Services" version="6.1.1833" targetFramework="net472" />
  <package id="Newtonsoft.Json" version="13.0.2" targetFramework="net472" />
  <package id="System.Buffers" version="4.5.1" targetFramework="net472" />
  <package id="System.Memory" version="4.5.5" targetFramework="net48" />
  <package id="System.Numerics.Vectors" version="4.5.0" targetFramework="net472" />
  <package id="System.Runtime.CompilerServices.Unsafe" version="6.0.0" targetFramework="net472" />
  <package id="System.Threading.Tasks.Extensions" version="4.5.4" targetFramework="net472" />
  <package id="WindowsAzure.Storage" version="9.3.3" targetFramework="net472" />
</packages>