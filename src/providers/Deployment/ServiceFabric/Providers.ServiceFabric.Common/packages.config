﻿<?xml version="1.0" encoding="utf-8"?>
<packages>
  <package id="GenevaMonitoringAgent" version="45.6.1" targetFramework="net451" />
  <package id="Microsoft.AspNetCore.Authentication.Abstractions" version="2.2.0" targetFramework="net472" />
  <package id="Microsoft.AspNetCore.Authentication.Core" version="2.2.0" targetFramework="net472" />
  <package id="Microsoft.AspNetCore.Authorization" version="2.2.0" targetFramework="net472" />
  <package id="Microsoft.AspNetCore.Authorization.Policy" version="2.2.0" targetFramework="net472" />
  <package id="Microsoft.AspNetCore.Hosting.Abstractions" version="2.2.0" targetFramework="net472" />
  <package id="Microsoft.AspNetCore.Hosting.Server.Abstractions" version="2.2.0" targetFramework="net472" />
  <package id="Microsoft.AspNetCore.Http" version="2.2.0" targetFramework="net472" />
  <package id="Microsoft.AspNetCore.Http.Abstractions" version="2.2.0" targetFramework="net472" />
  <package id="Microsoft.AspNetCore.Http.Extensions" version="2.2.0" targetFramework="net472" />
  <package id="Microsoft.AspNetCore.Http.Features" version="2.2.0" targetFramework="net472" />
  <package id="Microsoft.AspNetCore.JsonPatch" version="2.2.0" targetFramework="net472" />
  <package id="Microsoft.AspNetCore.Mvc.Abstractions" version="2.2.0" targetFramework="net472" />
  <package id="Microsoft.AspNetCore.Mvc.Core" version="2.2.0" targetFramework="net472" />
  <package id="Microsoft.AspNetCore.Mvc.Formatters.Json" version="2.2.0" targetFramework="net472" />
  <package id="Microsoft.AspNetCore.Mvc.WebApiCompatShim" version="2.2.0" targetFramework="net472" />
  <package id="Microsoft.AspNetCore.ResponseCaching.Abstractions" version="2.2.0" targetFramework="net472" />
  <package id="Microsoft.AspNetCore.Routing" version="2.2.0" targetFramework="net472" />
  <package id="Microsoft.AspNetCore.Routing.Abstractions" version="2.2.0" targetFramework="net472" />
  <package id="Microsoft.AspNetCore.WebUtilities" version="2.2.0" targetFramework="net472" />
  <package id="Microsoft.Azure.KeyVault.Core" version="1.0.0" targetFramework="net472" />
  <package id="Microsoft.Bcl.AsyncInterfaces" version="8.0.0" targetFramework="net472" />
  <package id="Microsoft.Bcl.Build" version="1.0.21" targetFramework="net472" />
  <package id="Microsoft.CSharp" version="4.5.0" targetFramework="net472" />
  <package id="Microsoft.DotNet.PlatformAbstractions" version="2.1.0" targetFramework="net472" />
  <package id="Microsoft.Extensions.Configuration.Abstractions" version="8.0.0" targetFramework="net472" />
  <package id="Microsoft.Extensions.DependencyInjection" version="8.0.0" targetFramework="net472" />
  <package id="Microsoft.Extensions.DependencyInjection.Abstractions" version="8.0.0" targetFramework="net472" />
  <package id="Microsoft.Extensions.DependencyModel" version="2.1.0" targetFramework="net472" />
  <package id="Microsoft.Extensions.FileProviders.Abstractions" version="2.2.0" targetFramework="net472" />
  <package id="Microsoft.Extensions.Hosting.Abstractions" version="2.2.0" targetFramework="net472" />
  <package id="Microsoft.Extensions.Logging.Abstractions" version="8.0.0" targetFramework="net472" />
  <package id="Microsoft.Extensions.ObjectPool" version="8.0.2" targetFramework="net472" />
  <package id="Microsoft.Extensions.Options" version="8.0.2" targetFramework="net472" />
  <package id="Microsoft.Extensions.Primitives" version="8.0.0" targetFramework="net472" />
  <package id="Microsoft.Net.Http.Headers" version="2.2.0" targetFramework="net472" />
  <package id="Microsoft.PowerPlatform.ResourceStack" version="6.0.0.1386" targetFramework="net472" />
  <package id="Microsoft.ServiceFabric" version="6.0.211" targetFramework="net451" />
  <package id="Newtonsoft.Json" version="13.0.2" targetFramework="net472" />
  <package id="System.Buffers" version="4.5.1" targetFramework="net472" />
  <package id="System.Diagnostics.DiagnosticSource" version="8.0.0" targetFramework="net472" />
  <package id="System.Memory" version="4.5.5" targetFramework="net472" />
  <package id="System.Numerics.Vectors" version="4.5.0" targetFramework="net472" />
  <package id="System.Runtime.CompilerServices.Unsafe" version="6.0.0" targetFramework="net472" />
  <package id="System.Text.Encodings.Web" version="8.0.0" targetFramework="net472" />
  <package id="System.Threading.Tasks.Extensions" version="4.5.4" targetFramework="net472" />
  <package id="System.ValueTuple" version="4.5.0" targetFramework="net472" />
  <package id="WindowsAzure.Storage" version="9.3.3" targetFramework="net472" />
</packages>