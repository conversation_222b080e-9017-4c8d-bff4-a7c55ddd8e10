﻿//------------------------------------------------------------
// Copyright (c) Microsoft Corporation.  All rights reserved.
//------------------------------------------------------------

namespace Providers.ServiceFabric.Common
{
    using System.Collections.Generic;
    using System.Configuration;
    using System.Fabric;
    using Microsoft.WindowsAzure.ResourceStack.Common.Extensions;

    /// <summary>
    /// Configuration helper for Providers to load configuration file.
    /// </summary>
    public static class ProviderConfigurationHelper
    {
        private const string ConfigFileName = nameof(ConfigFileName);

        /// <summary>
        /// Load the service configuration from configuration file based on ConfigFileName.
        /// </summary>
        /// <param name="context">The service context used to load the configuration file.</param>
        /// <param name="sectionName">The section name of the configuration file.</param>
        public static void LoadConfiguration(this StatelessServiceContext context, string sectionName)
        {
            LoadConfigurationFile(context, sectionName);
        }

        private static void LoadConfigurationFile(StatelessServiceContext context, string sectionName)
        {
            var configs = new Dictionary<string, string>();
            var configurationPackage = context.CodePackageActivationContext.GetConfigurationPackageObject("Config");
            var cloudServicesConfig = configurationPackage.Settings.Sections[sectionName];
            foreach (var parameter in cloudServicesConfig.Parameters)
            {
                configs.Add(parameter.Name, parameter.Value);
            }

            ProviderConfigurationHelper.UpdateConfig(configs);
        }

        private static void UpdateConfig(IEnumerable<KeyValuePair<string, string>> parameters)
        {
            var configuration = ConfigurationManager.OpenExeConfiguration(ConfigurationUserLevel.None);

            foreach (var parameter in parameters)
            {
                UpdateAppConfigSetting(configuration, name: parameter.Key, value: parameter.Value);
            }

            configuration.Save(ConfigurationSaveMode.Full);
            ConfigurationManager.RefreshSection("appSettings");
        }

        private static void UpdateAppConfigSetting(Configuration configuration, string name, string value)
        {
            if (configuration.AppSettings.Settings.AllKeys.ContainsInsensitively(name))
            {
                configuration.AppSettings.Settings.Remove(name);
            }

            if (value != null)
            {
                configuration.AppSettings.Settings.Add(name, value);
            }
        }
    }
}
