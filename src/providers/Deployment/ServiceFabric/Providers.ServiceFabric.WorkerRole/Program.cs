﻿//------------------------------------------------------------
// Copyright (c) Microsoft Corporation.  All rights reserved.
//------------------------------------------------------------

namespace Providers.ServiceFabric.WorkerRole
{
    using System;
    using System.Linq;
    using System.Threading;
    using Microsoft.ServiceFabric.Services.Runtime;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Common.Logging;
    using Providers.ServiceFabric.Common;

    /// <summary>
    /// Main entry point for the Worker Role.
    /// </summary>
    internal static class Program
    {
        /// <summary>
        /// This is the entry point of the service host process.
        /// </summary>
        private static void Main(string[] args)
        {
            if (args != null && args.Any())
            {
                try
                {
                    // The ServiceManifest.XML file defines one or more service type names.
                    // Registering a service maps a service type name to a .NET type.
                    // When Service Fabric creates an instance of this service type,
                    // an instance of the class is created in this host process.

                    ServiceRuntime.RegisterServiceAsync(
                        "Providers.ServiceFabric.WorkerRoleType",
                        context =>
                        {
                            // We need to load the configuration from file to ConfigurationManager before calling any method.
                            context.LoadConfiguration("WorkerRoleConfigSection");

                            return new WorkerRole<IProvidersEventSource>(
                                    context,
                                    ProvidersLog.Current
                                );
                        }).GetAwaiter().GetResult();

                    // Prevents this host process from terminating so services keep running.
                    Thread.Sleep(Timeout.Infinite);
                }
                catch (Exception e)
                {
                    ServiceEventSource.Current.ServiceHostInitializationFailed(e.ToString());
                    throw;
                }
            }
        }
    }
}
