﻿//------------------------------------------------------------
// Copyright (c) Microsoft Corporation.  All rights reserved.
//------------------------------------------------------------

namespace Providers.ServiceFabric.WorkerRole
{
    using System;
    using System.Fabric;
    using System.Threading;
    using System.Threading.Tasks;
    using Microsoft.ServiceFabric.Services.Runtime;
    using Microsoft.WindowsAzure.ResourceStack.Common.EventSources;
    using Microsoft.WindowsAzure.ResourceStack.Common.Services;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Common.Logging;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Worker.Configuration;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Worker.Dispatcher;

    /// <summary>
    /// An instance of this class is created for each service instance by the Service Fabric runtime.
    /// </summary>
    public sealed class WorkerRole<TEventSource> : StatelessService
        where TEventSource : class, IProvidersEventSource
    {
        private TEventSource EventSource { get; }

        /// <summary>
        /// Gets the role location.
        /// </summary>
        private static string RoleLocation
        {
            get { return CloudConfigurationManager.GetConfiguration("Microsoft.WindowsAzure.ResourceStack.Providers.RoleLocation"); }
        }

        /// <summary>
        /// Gets or sets the providers jobs dispatcher.
        /// </summary>
        private ProvidersJobDispatcherClient ProvidersJobDispatcherClient { get; set; }

        /// <summary>
        /// Initializes a new instance of the <see cref="WorkerService{TEventSource}"/> class.
        /// </summary>
        /// <param name="context">Instance of the StatelessServiceContext.</param>
        /// <param name="workerFactory">Factory to create a new WorkerRole object.</param>
        public WorkerRole(StatelessServiceContext context, TEventSource eventSource)
            : base(context)
        {
            this.EventSource = eventSource;

            this.ProvidersJobDispatcherClient = ProvidersJobDispatcherClient.CreateProvidersJobDispatcherClient(
                eventSource: ProvidersLog.Current,
                location: RoleLocation);
        }

        /// <summary>
        /// This is the main entry point for your service instance.
        /// </summary>
        /// <param name="cancellationToken">Canceled when Service Fabric needs to shut down this service instance.</param>
        /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
        protected override async Task RunAsync(CancellationToken cancellationToken)
        {
            this.ProvidersJobDispatcherClient = ProvidersJobDispatcherClient.CreateProvidersJobDispatcherClient(
                eventSource: ProvidersLog.Current,
                location: RoleLocation);

            try
            {
                this.ProvidersJobDispatcherClient.Start();
                this.ProvidersJobDispatcherClient.ProvisionSystemConsistencyJob().Wait();
            }
            catch (Exception ex)
            {
                this.EventSource.Critical(
                    operationName: "WorkerRole.RunAsync",
                    message: "Unable to start Job Dispatcher.",
                    exception: ex);

                throw;
            }

            // The entry point for a service in Service Fabric is different from Cloud Servic entry point.
            // In Azure Cloud Service, runtime events are Event based, meaning that we need to register callback.
            // In Service Fabric, for a worker role, the RunAsync method is called, and use the cancellationToken a event to stop the execution.
            // As the current does not support Async scenario and does not support CancellationToken support, we need to loop on the cancellation status.
            while (!cancellationToken.IsCancellationRequested)
            {
                await Task.Delay(10000, cancellationToken);
                await Task.FromResult(true);
            }
        }

        /// <summary>
        /// This method is called as the final step of closing the service
        /// </summary>
        /// <param name="cancellationToken">Canceled when Service Fabric needs to shut down this service instance.</param>
        /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
        protected override async Task OnCloseAsync(CancellationToken cancellationToken)
        {
            await this.ProvidersJobDispatcherClient?.StopAsync();
        }

        /// <summary>
        /// Notification that the service is being aborted.
        /// </summary>
        protected override async void OnAbort()
        {
            await this.ProvidersJobDispatcherClient?.StopAsync();
        }
    }
}

