﻿<?xml version="1.0" encoding="utf-8" ?>
<Settings xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://schemas.microsoft.com/2011/01/fabric">
  <!-- Add your custom configuration sections and parameters here -->
  <Section Name="WorkerRoleConfigSection">
    <Parameter Name="CloudEnvironment" Value="" />
    <Parameter Name="Microsoft.WindowsAzure.ResourceStack.Providers.RoleLocation" Value="" />
    <Parameter Name="MainTemplateFilePath_databricks.databricks-workspace-dev-previewdatabricks-workspace-dev.0.0.12" Value="" />
    <Parameter Name="MainTemplateFilePath_databricks.databricks-workspace-staging-previewdatabricks-workspace-staging.0.0.12" Value="" />
    <Parameter Name="MainTemplateFilePath_databricks.databricks-workspace-previewdatabricks-workspace.0.0.2" Value="" />
    <Parameter Name="KeyVault_Endpoint" Value="" />
    <Parameter Name="MdmAccount" Value="" />
    <Parameter Name="JarvisEndpoint" Value="" />
    <Parameter Name="Microsoft.WindowsAzure.ResourceStack.Providers.FrontdoorEndpointUri" Value="" />
    <Parameter Name="Microsoft.WindowsAzure.ResourceStack.Providers.ArmMetadataEndpoint" Value="" />
    <Parameter Name="Microsoft.WindowsAzure.ResourceStack.Providers.Appliances.DatabricksApplicationId" Value="" />
    <Parameter Name="Microsoft.WindowsAzure.ResourceStack.Providers.Appliances.ServicePrincipalCertificateThumbprint" Value="" />
    <Parameter Name="Microsoft.WindowsAzure.ResourceStack.Providers.Appliances.ServicePrincipalCertificateSubjectName" Value="" />
    <Parameter Name="Microsoft.WindowsAzure.ResourceStack.Providers.Appliances.AadAllowedAudience" Value="" />
    <Parameter Name="AzureAD_SecretsAppId" Value="" />
    <Parameter Name="AzureAD_SecretsTenantId" Value="" />
    <Parameter Name="AzureAD_SecretsLoginTemplate" Value="" />
    <Parameter Name="PublisherTenantId_databricks.databricks-workspace-dev-previewdatabricks-workspace-dev.0.0.12" Value="" />
    <Parameter Name="AuthPrincipalId_databricks.databricks-workspace-dev-previewdatabricks-workspace-dev.0.0.12" Value="" />
    <Parameter Name="AuthPrincipalType_databricks.databricks-workspace-dev-previewdatabricks-workspace-dev.0.0.12" Value="" />
    <Parameter Name="PublisherTenantId_databricks.databricks-workspace-staging-previewdatabricks-workspace-staging.0.0.12" Value="" />
    <Parameter Name="AuthPrincipalId_databricks.databricks-workspace-staging-previewdatabricks-workspace-staging.0.0.12" Value="" />
    <Parameter Name="AuthPrincipalType_databricks.databricks-workspace-staging-previewdatabricks-workspace-staging.0.0.12" Value="" />
    <Parameter Name="PublisherTenantId_databricks.databricks-workspace-previewdatabricks-workspace.0.0.2" Value="" />
    <Parameter Name="AuthPrincipalId_databricks.databricks-workspace-previewdatabricks-workspace.0.0.2" Value="" />
    <Parameter Name="AuthPrincipalType_databricks.databricks-workspace-previewdatabricks-workspace.0.0.2" Value="" />
    <Parameter Name="Microsoft.WindowsAzure.ResourceStack.VulnScanRule.LocalPorts" Value="" />
    <Parameter Name="Microsoft.WindowsAzure.ResourceStack.VulnScanRule.ConfigureRules" Value="" />
    <Parameter Name="Microsoft.WindowsAzure.ResourceStack.VulnScanRule.RemoteIPs" Value="" />
    <Parameter Name="MonitoringAgentVersion" Value="" />
    <Parameter Name="MonitoringInitConfig" Value="" />
    <Parameter Name="Monitoring_GCS_Environment" Value="" />
    <Parameter Name="Monitoring_GCS_Account" Value="" />
    <Parameter Name="Monitoring_GCS_Namespace" Value="" />
    <Parameter Name="Monitoring_GCS_Thumbprint" Value="" />
    <Parameter Name="Monitoring_Config_Version" Value="" />
    <Parameter Name="Monitoring_Agent_Version" Value="" />
    <Parameter Name="Monitoring_Use_Geneva_Config_Service" Value="" />
    <Parameter Name="SLIMetricsMonitoringAccount" Value="" />
    <Parameter Name="SLIMetricsNamespace" Value="" />
    <Parameter Name="EnableSLIMetricsCapture" Value="" />
    <Parameter Name="AllowedSLISyncOperations" Value="" />
    <Parameter Name="Microsoft.WindowsAzure.ResourceStack.DisableSN" Value="" />
    <Parameter Name="Microsoft.WindowsAzure.ResourceStack.Providers.FailHealthCheck" Value="" />
    <Parameter Name="Microsoft.WindowsAzure.ResourceStack.Providers.AllowedReadOnlyClientCertificates" Value="" />
    <Parameter Name="Microsoft.WindowsAzure.ResourceStack.Providers.AllowedAdminCertificates" Value="" />
    <Parameter Name="Microsoft.WindowsAzure.ResourceStack.Providers.FeatureDataStorage.RequestOptionsServerTimeout" Value="" />
    <Parameter Name="Microsoft.WindowsAzure.ResourceStack.Providers.FeatureDataStorage.RequestOptionsMaximumExecutionTime" Value="" />
    <Parameter Name="Microsoft.WindowsAzure.ResourceStack.Providers.FeatureDataStorage.RequestOptionsDeltaBackoff" Value="" />
    <Parameter Name="Microsoft.WindowsAzure.ResourceStack.Providers.FeatureDataStorage.RequestOptionsMaximumAttempts" Value="" />
    <Parameter Name="Microsoft.WindowsAzure.ResourceStack.Providers.ApplianceDataStorage.RequestOptionsServerTimeout" Value="" />
    <Parameter Name="Microsoft.WindowsAzure.ResourceStack.Providers.ApplianceDataStorage.RequestOptionsMaximumExecutionTime" Value="" />
    <Parameter Name="Microsoft.WindowsAzure.ResourceStack.Providers.ApplianceDataStorage.RequestOptionsDeltaBackoff" Value="" />
    <Parameter Name="Microsoft.WindowsAzure.ResourceStack.Providers.ApplianceDataStorage.RequestOptionsMaximumAttempts" Value="" />
    <Parameter Name="Microsoft.WindowsAzure.ResourceStack.Providers.AppliancePackageDataStorage.RequestOptionsServerTimeout" Value="" />
    <Parameter Name="Microsoft.WindowsAzure.ResourceStack.Providers.AppliancePackageDataStorage.RequestOptionsMaximumExecutionTime" Value="" />
    <Parameter Name="Microsoft.WindowsAzure.ResourceStack.Providers.AppliancePackageDataStorage.RequestOptionsDeltaBackoff" Value="" />
    <Parameter Name="Microsoft.WindowsAzure.ResourceStack.Providers.AppliancePackageDataStorage.RequestOptionsMaximumAttempts" Value="" />
    <Parameter Name="Microsoft.WindowsAzure.ResourceStack.Providers.Appliances.DefaultRetryInterval" Value="" />
    <Parameter Name="Microsoft.WindowsAzure.ResourceStack.Providers.Appliances.AadLoginTemplate" Value="" />
    <Parameter Name="Microsoft.WindowsAzure.ResourceStack.Providers.Databricks.Workspace.PublisherPackageIdMappings" Value="" />
    <Parameter Name="Microsoft.WindowsAzure.ResourceStack.Providers.ApplianceProvisioningJob.RetryInterval" Value="" />
    <Parameter Name="Microsoft.WindowsAzure.ResourceStack.Providers.ApplianceProvisioningJob.StartTime.Immediate" Value="" />
    <Parameter Name="Microsoft.WindowsAzure.ResourceStack.Providers.ApplianceProvisioningJob.EndTime" Value="" />
    <Parameter Name="Microsoft.WindowsAzure.ResourceStack.Providers.ApplianceProvisioningJob.Timeout" Value="" />
    <Parameter Name="Microsoft.WindowsAzure.ResourceStack.Providers.ApplianceDeprovisioningJob.RetryInterval" Value="" />
    <Parameter Name="Microsoft.WindowsAzure.ResourceStack.Providers.ApplianceDeprovisioningJob.PostponeInterval" Value="" />
    <Parameter Name="Microsoft.WindowsAzure.ResourceStack.Providers.ApplianceDeprovisioningJob.MaxRetries" Value="" />
    <Parameter Name="Microsoft.WindowsAzure.ResourceStack.Providers.ApplianceDeprovisioningJob.StartTime.Immediate" Value="" />
    <Parameter Name="Microsoft.WindowsAzure.ResourceStack.Providers.ApplianceDeprovisioningJob.EndTime" Value="" />
    <Parameter Name="Microsoft.WindowsAzure.ResourceStack.Providers.ApplianceDeprovisioningJob.Timeout" Value="" />
    <Parameter Name="Microsoft.WindowsAzure.ResourceStack.Providers.Feature.ConfigurationCache.CacheInitializationTimeout" Value="" />
    <Parameter Name="Microsoft.WindowsAzure.ResourceStack.Providers.ServiceClient.RequestTimeout" Value="" />
    <Parameter Name="Microsoft.WindowsAzure.ResourceStack.Providers.ServiceClient.ConnectionLeaseTimeout" Value="" />
    <Parameter Name="Microsoft.WindowsAzure.ResourceStack.Providers.ServiceClient.MaxResponseContentSize" Value="" />
    <Parameter Name="Microsoft.WindowsAzure.ResourceStack.Providers.BackgroundJobs.JobDefinitionsTableName" Value="" />
    <Parameter Name="Microsoft.WindowsAzure.ResourceStack.Providers.BackgroundJobs.JobTriggersQueuePrefix" Value="" />
    <Parameter Name="Microsoft.WindowsAzure.ResourceStack.Providers.Feature.TelemetryRequest.TotalEventsLimit" Value="" />
    <Parameter Name="Microsoft.WindowsAzure.ResourceStack.Providers.Feature.TelemetryRequest.AllowedApplications" Value="" />
    <Parameter Name="Microsoft.WindowsAzure.ResourceStack.Providers.ServicePointDefaultConnectionLimitPerProcessor" Value="" />
    <Parameter Name="Microsoft.WindowsAzure.ResourceStack.Providers.ServicePointMaxServicePointIdleTime" Value="" />
    <Parameter Name="Microsoft.WindowsAzure.ResourceStack.Providers.ServicePointUseNagleAlgorithm" Value="" />
    <Parameter Name="Microsoft.WindowsAzure.ResourceStack.Providers.ServicePointExpect100Continue" Value="" />
    <Parameter Name="Microsoft.WindowsAzure.ResourceStack.Providers.ThreadPoolMinWorkerThreads" Value="" />
    <Parameter Name="Microsoft.WindowsAzure.ResourceStack.Providers.ThreadPoolMaxWorkerThreads" Value="" />
    <Parameter Name="Microsoft.WindowsAzure.ResourceStack.Providers.ThreadPoolMinIoThreads" Value="" />
    <Parameter Name="Microsoft.WindowsAzure.ResourceStack.Providers.ThreadPoolMaxIoThreads" Value="" />
    <Parameter Name="DatabricksNetworkIntentPoliciesFilePath_databricks.databricks-workspace-dev-previewdatabricks-workspace-dev.0.0.12" Value="" />
    <Parameter Name="DatabricksNetworkIntentPoliciesFilePath_databricks.databricks-workspace-staging-previewdatabricks-workspace-staging.0.0.12" Value="" />
    <Parameter Name="DatabricksNetworkIntentPoliciesFilePath_databricks.databricks-workspace-previewdatabricks-workspace.0.0.2" Value="" />
    <Parameter Name="GetNetworkIntentPoliciesAllowList" Value="" />
    <Parameter Name="VirtualNetworkInjectionOperationsApiVersion" Value="" />
    <Parameter Name="DenyAssignmentsApiVersion" Value="" />
    <Parameter Name="DenyAssignmentsActions" Value="" />
    <Parameter Name="DenyAssignmentsNotActions" Value="" />
    <Parameter Name="DenyAssignmentsDataActions" Value="" />
    <Parameter Name="DenyAssignmentsNotDataActions" Value="" />
    <Parameter Name="FeaturesApiVersion" Value="" />
    <Parameter Name="StorageApiVersion" Value="" />
    <Parameter Name="ServiceEndpointsApiVersion" Value="" />
    <Parameter Name="SupportedServiceEndpointServicesOnManagedVNet" Value="" />
    <Parameter Name="WorkspaceInitializationRetryInterval" Value="" />
    <Parameter Name="WorkspaceInitializationRetryCount" Value="" />
    <Parameter Name="DisableFedRampCertificationCheck" Value="" />
    <Parameter Name="PrivateLinkMinApiVersion" Value="" />
    <Parameter Name="PrivateLinkOperationsApiVersion" Value="" />
    <Parameter Name="PrivateLinkPostponeInterval" Value="" />
    <Parameter Name="PrivateLinkMaxLifetime" Value="" />
    <Parameter Name="EnableDatabricksNotification" Value="" />
    <Parameter Name="PrivateLinkMaxRetries" Value="" />    
    <Parameter Name="Microsoft.WindowsAzure.ResourceStack.Providers.Appliances.RegisterManagedByTenantApiVersion" Value="" />
    <Parameter Name="Microsoft.WindowsAzure.ResourceStack.Providers.Appliances.UnregisterManagedByTenantEnabled" Value="" />
    <Parameter Name="EnableDBWorkspaceNotification" Value="" />
    <Parameter Name="DBWorkspaceNotificationAllowedApiVersions" Value="" />
    <Parameter Name="DBWorkspaceNotificationRetryIntervalSecs" Value="" />
    <Parameter Name="DBWorkspaceNotificationRetryCount" Value="" />
    <Parameter Name="AmlWorkspaceApiVersion" Value="" />
    <Parameter Name="DBNotificationPropagationPostponeInterval" Value="" />
    <Parameter Name="KustoDatabaseName" Value="" />
    <Parameter Name="ActiveWorkspacesTableName" Value="" />
    <Parameter Name="KustoClusterUri" Value="" />
    <Parameter Name="Microsoft.WindowsAzure.ResourceStack.Providers.ActiveWorkpsacesJob.RetryInterval" Value=""/>
    <Parameter Name="Microsoft.WindowsAzure.ResourceStack.Providers.Region" Value="" />
    <Parameter Name="PrimaryJobsDataStorageAccountName" Value="" />
    <Parameter Name="WorkspaceDataStorageAccountName" Value="" />
    <Parameter Name="DSCPackageStorageAccountNameSuffix" Value="" />
    <Parameter Name="ARGEndpoint" Value="" />
    <Parameter Name="ShortRegionName" Value="" />
  </Section>
</Settings>