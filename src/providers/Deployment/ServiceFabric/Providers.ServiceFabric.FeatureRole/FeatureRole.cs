﻿//------------------------------------------------------------
// Copyright (c) Microsoft Corporation.  All rights reserved.
//------------------------------------------------------------

namespace Providers.ServiceFabric.FeatureRole
{
    using Microsoft.ServiceFabric.Services.Communication.AspNetCore;
    using Microsoft.ServiceFabric.Services.Runtime;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Common.Logging;
    using Owin;
    using System;
    using System.Fabric;
    using System.Threading;
    using System.Threading.Tasks;
    using Microsoft.AspNetCore.Server.Kestrel.Https;
    using Microsoft.AspNetCore.Hosting;
    using Microsoft.WindowsAzure.ResourceStack.Common.EventSources;
    using System.Collections.Generic;
    using System.Linq;
    using System.Fabric.Description;
    using Microsoft.ServiceFabric.Services.Communication.Runtime;
    using Microsoft.Extensions.DependencyInjection;
    using System.Net;
    using System.IO;
    using System.Security.Authentication;

    /// <summary>
    /// An instance of this class is created for each service instance by the Service Fabric runtime.
    /// </summary>
    internal sealed class FeatureRole<TEventSource> : StatelessService
        where TEventSource : class, IProvidersEventSource
    {
        private TEventSource EventSource { get; }

        private Action<IAppBuilder> Startup { get; }

        private readonly SSLCertificateProvider SSLCertificateProvider;

        public FeatureRole(StatelessServiceContext context, TEventSource eventSource)
            : base(context)
        {
            this.EventSource = eventSource;
            this.SSLCertificateProvider = new SSLCertificateProvider();
        }

        /// <summary>
        /// This is the main entry point for your service instance.
        /// </summary>
        /// <param name="cancellationToken">Canceled when Service Fabric needs to shut down this service instance.</param>
        /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
        protected override Task RunAsync(CancellationToken cancellationToken)
        {
            try
            {
                return base.RunAsync(cancellationToken);
            }
            catch (Exception ex)
            {
                this.EventSource.Critical(
                    operationName: "WebService_RunAsync",
                    message: "Unable to start web application.",
                    exception: ex);
                Thread.Sleep(TimeSpan.FromMinutes(2));

                throw;
            }
        }

        /// <summary>
        /// Optional override to create listeners (e.g., TCP, HTTP) for this service replica to handle client or user requests.
        /// </summary>
        /// <returns>A collection of listeners.</returns>
        protected override IEnumerable<ServiceInstanceListener> CreateServiceInstanceListeners()
        {
            try
            {
                var endpoints = this.Context.CodePackageActivationContext.GetEndpoints()
                                   .Where(endpoint => endpoint.Protocol == EndpointProtocol.Http || endpoint.Protocol == EndpointProtocol.Https)
                                   .Select(endpoint => endpoint.Name);

                var kestrelEndpointListener = new ServiceInstanceListener(serviceContext =>
                    new KestrelCommunicationListener(serviceContext, "ProvidersFeatureServiceEndpoint", (url, listener) =>
                    {
                        EventSource.Debug("Kestrel-CreateServiceInstanceListeners", $"Starting Kestrel on {url}");

                        return new WebHostBuilder()
                                    .UseKestrel(
                                    opt =>
                                    {
                                        int port = serviceContext.CodePackageActivationContext.GetEndpoint("ProvidersFeatureServiceEndpoint").Port;
                                        opt.Listen(IPAddress.IPv6Any, port,
                                            listenOptions =>
                                            {
                                                listenOptions.UseHttps((httpsOptions) =>
                                                {
                                                    httpsOptions.SslProtocols = SslProtocols.None;
                                                    httpsOptions.ServerCertificateSelector = (connectionContext, name) =>
                                                    {
                                                        return SSLCertificateProvider.Certificate;
                                                    };
                                                    // this is what will make the browser display the client certificate dialog
                                                    httpsOptions.ClientCertificateMode = ClientCertificateMode.AllowCertificate;
                                                });
                                            });
                                        //opt.Limits.MinRequestBodyDataRate = null;
                                        //opt.Limits.MinResponseDataRate = null;
                                        opt.AddServerHeader = false;
                                    })
                                    .ConfigureServices(
                                        services => services
                                            .AddSingleton<StatelessServiceContext>(serviceContext))
                                    .UseContentRoot(Directory.GetCurrentDirectory())
                                    .UseStartup<KestrelConfiguration>()
                                    .UseServiceFabricIntegration(listener, ServiceFabricIntegrationOptions.None)
                                    .UseUrls(url)
                                    .Build();
                    }), name: "ADBKestrel");

                return new List<ServiceInstanceListener> { kestrelEndpointListener };
            }
            catch (Exception ex)
            {
                this.EventSource.Critical(
                    operationName: "FeatureRole.CreateServiceInstanceListeners",
                    message: "Unable to start web application.",
                    exception: ex);
                Thread.Sleep(TimeSpan.FromMinutes(2));

                throw;
            }
        }
    }
}
