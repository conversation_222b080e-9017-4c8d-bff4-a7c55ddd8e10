﻿using System;
using System.Collections.Generic;
using System.Net.Http.Formatting;
using System.Reflection;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Infrastructure;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.WindowsAzure.ResourceStack.Common.EventSources;
using Microsoft.WindowsAzure.ResourceStack.Common.Instrumentation;
using Microsoft.WindowsAzure.ResourceStack.Common.Json;
using Microsoft.WindowsAzure.ResourceStack.Common.Services;
using Microsoft.WindowsAzure.ResourceStack.Providers.Application.Data.Definitions.Enums;
using Microsoft.WindowsAzure.ResourceStack.Providers.Application.Data.Metadata;
using Microsoft.WindowsAzure.ResourceStack.Providers.Common;
using Microsoft.WindowsAzure.ResourceStack.Providers.Common.Components;
using Microsoft.WindowsAzure.ResourceStack.Providers.Common.Constants;
using Microsoft.WindowsAzure.ResourceStack.Providers.Common.Data;
using Microsoft.WindowsAzure.ResourceStack.Providers.Common.DataProviders;
using Microsoft.WindowsAzure.ResourceStack.Providers.Common.Engines;
using Microsoft.WindowsAzure.ResourceStack.Providers.Common.ErrorResponses;
using Microsoft.WindowsAzure.ResourceStack.Providers.Common.Extensions;
using Microsoft.WindowsAzure.ResourceStack.Providers.Common.Filters;
using Microsoft.WindowsAzure.ResourceStack.Providers.Common.Logging;
using Microsoft.WindowsAzure.ResourceStack.Providers.Common.Middleware;
using Microsoft.WindowsAzure.ResourceStack.Providers.Common.Selectors;
using Microsoft.WindowsAzure.ResourceStack.Providers.Feature.Components;
using Microsoft.WindowsAzure.ResourceStack.Providers.Feature.Controllers;
using Microsoft.WindowsAzure.ResourceStack.Providers.Feature.Metrics;
using Newtonsoft.Json;
using Newtonsoft.Json.Converters;
using Newtonsoft.Json.Serialization;

namespace Providers.ServiceFabric.FeatureRole
{
    public class KestrelConfiguration
    {
        /// <summary>
        /// Service Name
        /// </summary>
        private const string ServiceName = "Microsoft.Databricks.ServiceFabric.Feature";

        /// <summary>
        /// Service version
        /// </summary>
        private const string ServiceVersion = "*******";

        private const string AzurePublicCloudEnvironment = "public";

        /// <summary>
        /// Gets the event source to use for tracing.
        /// </summary>
        private static IProvidersEventSource EventSource
        {
            get { return ProvidersLog.Current; }
        }

        public void ConfigureServices(IServiceCollection services)
        {
            ConfigureRole();
            LogEnvironment();
            EventSource.ServiceStarting(serviceName: ServiceName, version: ServiceVersion);

            services.AddSingleton<MediaTypeFormatter>(sp => new JsonMediaTypeFormatter() { SerializerSettings = Microsoft.WindowsAzure.ResourceStack.Providers.Common.Extensions.JsonExtensions.MediaTypeFormatterSettings });
            services.AddSingleton<ICommonEventSource>(ProvidersLog.Current);
            services.AddSingleton<ICacheProvidersContainer, CacheProvidersContainer>();
            services.AddSingleton<IFrontdoorEngine, FrontdoorEngine>();
            services.AddSingleton<IArmMetadataProvider, ArmMetadataProvider>();
            services.AddSingleton<IApplicationProviderConfiguration>(ApplicationProviderConfiguration.Instance);
            services.AddSingleton(ApplicationProviderConfiguration.AadAuthenticationDataProvider);
            services.AddSingleton<IAccessConnectorProviderConfiguration>(AccessConnectorProviderConfiguration.Instance);
            services.AddSingleton<IAuthorizationHandler, NrpAuthorizationHandler>();
            services.AddSingleton<IAuthorizationHandler, AcisAuthorizationHandler>();
            services.AddSingleton<IActionSelector, ProviderApiControllerActionSelector>();
            FrontdoorEngineProviderConfiguration.Instance.InitializeFrontdoorEngine(services.BuildServiceProvider().GetService<IFrontdoorEngine>());

            services.AddAuthorization(options =>
            {
                options.AddPolicy("Acis", policy =>
                    policy.Requirements.Add(new AcisRequirement()));
                options.AddPolicy("Nrp", policy =>
                    policy.Requirements.Add(new NrpRequirement()));
            });
            var assembly = typeof(GatewayProbeController).GetTypeInfo().Assembly;

            services.AddMvc(options =>
            {
                options.EnableEndpointRouting = false;
                options.Filters.Add(typeof(ErrorResponseFilter));
            }).ConfigureApiBehaviorOptions(options =>
            {
                options.InvalidModelStateResponseFactory = context =>
                {
                    return ErrorResponseHandling.ConvertModelBindingError(context);
                };
            }).AddApplicationPart(assembly).SetCompatibilityVersion(CompatibilityVersion.Version_2_2).AddJsonOptions(options =>
            {
                options.SerializerSettings.MaxDepth = Microsoft.WindowsAzure.ResourceStack.Providers.Common.Extensions.JsonExtensions.JsonSerializationMaxDepth;
                options.SerializerSettings.TypeNameHandling = TypeNameHandling.None;
                options.SerializerSettings.DateParseHandling = DateParseHandling.None;
                options.SerializerSettings.DateTimeZoneHandling = DateTimeZoneHandling.Utc;
                options.SerializerSettings.NullValueHandling = NullValueHandling.Ignore;
                options.SerializerSettings.MissingMemberHandling = MissingMemberHandling.Error;
                options.SerializerSettings.Formatting = Formatting.Indented;
                options.SerializerSettings.ContractResolver = new CamelCasePropertyNamesWithOverridesContractResolver();
                options.SerializerSettings.MetadataPropertyHandling = MetadataPropertyHandling.Ignore;
                options.SerializerSettings.Converters = new List<JsonConverter>
                    {
                        new LineInfoConverter(),
                        new TimeSpanConverter(),
                        new StringEnumConverter { NamingStrategy = new DefaultNamingStrategy() },
                        new AdjustToUniversalIsoDateTimeConverter()
                    };
            });
        }
        public void Configure(IApplicationBuilder app, IHostingEnvironment env)
        {
            app.Use(async (context, next) =>
            {
                context.Response.OnStarting(() =>
                {
                    RequestCorrelationHandler.PopulateResponseHeaders(context);
                    return Task.FromResult(0);
                });
                await next();
            }
            );
            app.UseMiddleware<RequestCorrelationHandler>();
            app.UseMiddleware<ExceptionMiddleware>();
            app.UseMiddleware<AuthorizationMiddleware>();
            app.UseMiddleware<RequestObserverMiddleware>();
            app.UseMvc();
            EventSource.ServiceStarted(serviceName: ServiceName, version: ServiceVersion);
            CreateActiveWorkspacesJob();

            var environment = CloudConfigurationManager.GetConfiguration("CloudEnvironment");
            var argEndpoint = CloudConfigurationManager.GetConfiguration("ARGEndpoint");

            if (string.Equals(environment, AzurePublicCloudEnvironment, StringComparison.OrdinalIgnoreCase) && !string.IsNullOrEmpty(argEndpoint))
            {
                CreateRecurringJob(ProviderConstants.ARG.AccessConnectorDependencyPublisher, ADBResourceType.AccessConnector,
                    TimeSpan.FromHours(ProviderConstants.ARG.AccessConnectorPublisherJobFrequencyInHours),
                    DateTime.UtcNow.AddMinutes(ProviderConstants.ARG.AccessConnectorPublisherJobStartDelayInMinutes));

                CreateRecurringJob(ProviderConstants.ARG.WorkspaceDependencyPublisher, ADBResourceType.Workspace,
                    TimeSpan.FromHours(ProviderConstants.ARG.WorkspacePublisherJobFrequencyInHours),
                    DateTime.UtcNow.AddMinutes(ProviderConstants.ARG.WorkspacePublisherJobStartDelayInMinutes));
            }
            else
            {
                EventSource.Debug("Configure", $"Skipping DependencyPublishing Job Creation for {environment}");
            }
        }

        private static void CreateActiveWorkspacesJob()
        {
            ApplicationProviderConfiguration.Instance.JobsDataProvider.CreateActiveWorkspacesJob(new ActiveWorkspacesJobMetadata
            {
                RequestCorrelationContext = new RequestCorrelationContext
                {
                    CorrelationId = Guid.NewGuid().ToString()
                },
                ProvidersLocation = CloudConfigurationManager.GetConfiguration("Microsoft.WindowsAzure.ResourceStack.Providers.RoleLocation")
            }).GetAwaiter().GetResult();
        }

        /// <summary>
        /// Creates a one time job for the given action name and resource type.
        /// </summary>
        /// <param name="actionName"> Name of the Action </param>
        /// <param name="resourceType"> Resource type </param>
        private static void CreateOneTimeJob(string actionName, ADBResourceType resourceType)
        {
            try
            {
                var existingJob = ApplicationProviderConfiguration.Instance.ScanJobsDataProvider.GetJob(actionName).GetAwaiter().GetResult();

                if (existingJob == null)
                {
                    var singleExecutionJobMetadata = new ScanJobMetadata
                    {
                        ExecutionMetric = new Dictionary<string, JobExecutionMetric>(),
                        RequestCorrelationContext = new RequestCorrelationContext
                        {
                            CorrelationId = Guid.NewGuid().ToString()
                        },
                        ProvidersLocation = CloudConfigurationManager.GetConfiguration("Microsoft.WindowsAzure.ResourceStack.Providers.RoleLocation"),
                        ActionName = actionName,
                        ResourceType = resourceType
                    };

                    ApplicationProviderConfiguration.Instance.ScanJobsDataProvider.CreateSingleExecutionJob(singleExecutionJobMetadata, DateTime.Now).GetAwaiter().GetResult();
                }
                else
                {
                    ProvidersLog.Current.Debug("CreateOneTimeJob", $"Job with job Id {actionName} already exists");
                }
            }
            catch (Exception)
            {
                // Ignore the exception so that it does not affect application startup
                // Error is already logged in ScanJobsDataProvider
            }
        }

        /// <summary>
        /// Creates a recurring job for the given action name and resource type.
        /// </summary>
        /// <param name="actionName"> Name of the Actio </param>
        /// <param name="resourceType"> Resource type </param>
        /// <param name="repeatAfterTime"> Recurring Job Frequency </param>
        /// <param name="jobStartTime"> Recurring Job Start Time </param>
        private static void CreateRecurringJob(string actionName, ADBResourceType resourceType, TimeSpan repeatAfterTime, DateTime jobStartTime)
        {
            try
            {
                var recurringExecutionJobMetadata = new ScanJobMetadata
                {
                    ExecutionMetric = new Dictionary<string, JobExecutionMetric>(),
                    RequestCorrelationContext = new RequestCorrelationContext
                    {
                        CorrelationId = Guid.NewGuid().ToString()
                    },
                    ProvidersLocation = CloudConfigurationManager.GetConfiguration("Microsoft.WindowsAzure.ResourceStack.Providers.RoleLocation"),
                    ActionName = actionName,
                    ResourceType = resourceType
                };

                ApplicationProviderConfiguration.Instance.ScanJobsDataProvider.CreateRecurringJob(recurringExecutionJobMetadata, repeatAfterTime, jobStartTime).GetAwaiter().GetResult();
            }
            catch (Exception)
            {
                // Ignore the exception so that it does not affect application startup
                // Error is already logged in ScanJobsDataProvider
            }
        }

        /// <summary>
        /// Configures role for optimal settings
        /// </summary>
        private static void ConfigureRole()
        {
            ProvidersResourceManagerInitialization.UseRequestCorrelationResourceManager();
            RoleInitialization.Default.ConfigureRole(logger: EventSource, configurationSettingsPrefix: "Microsoft.WindowsAzure.ResourceStack.Providers");
        }

        /// <summary>
        /// Called to log environment variables.
        /// </summary>
        private static void LogEnvironment()
        {
            RoleInitialization.Default.LogEnvironment(EventSource, ServiceName);

            ProvidersLog.Current.ServiceConfiguration(ServiceName, "Providers.RoleLocation", CloudConfigurationManager.GetConfiguration("Microsoft.WindowsAzure.ResourceStack.Providers.RoleLocation"));
            ProvidersLog.Current.ServiceConfiguration(ServiceName, "Providers.ArmMetadataEndpoint", CloudConfigurationManager.GetConfiguration("Microsoft.WindowsAzure.ResourceStack.Providers.ArmMetadataEndpoint"));
            ProvidersLog.Current.ServiceConfiguration(ServiceName, "Providers.AllowedReadOnlyClientCertificates", CloudConfigurationManager.GetConfiguration("Microsoft.WindowsAzure.ResourceStack.Providers.AllowedReadOnlyClientCertificates"));
        }
    }
}

