﻿//------------------------------------------------------------
// Copyright (c) Microsoft Corporation.  All rights reserved.
//------------------------------------------------------------

namespace Providers.ServiceFabric.FeatureRole
{
    using System;
    using System.Linq;
    using System.Runtime.Caching;
    using System.Security.Cryptography.X509Certificates;
    using System.Threading.Tasks;
    using Microsoft.WindowsAzure.ResourceStack.Common.EventSources;
    using Microsoft.WindowsAzure.ResourceStack.Common.Services;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Common.DataProviders;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Common.Engines;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Common.Logging;

    public class SSLCertificateProvider
    {
        private const string SSLCertKey = "SSLCertificate";

        public X509Certificate2 Certificate
        {
            get
            {
                return GetSSLCertificate();
            }
        }

        /// <summary>
        /// Caches and reads SSL certificate.
        /// </summary>
        private X509Certificate2 GetSSLCertificate()
        {
            try
            {
                var memoryCache = MemoryCache.Default;

                if (!memoryCache.Contains(SSLCertKey))
                {
                    ProvidersLog.Current.Debug(
                        operationName: "SSLCertificateProvider.GetSSLCertificate",
                        message: "SSL certificate not present in the cache. Fetching.");

                    var sslCertSubjectName = CloudConfigurationManager.GetConfiguration("Microsoft.WindowsAzure.ResourceStack.Providers.Appliances.SSLCertificateSubjectName");
                    var sslCertificate = SSLCertUtility.GetCertificateFromStore(sslCertSubjectName);
                    memoryCache.Set(SSLCertKey, sslCertificate, DateTime.Now.AddMinutes(10));
                }

                var cachedSSLCertificate = (X509Certificate2) memoryCache.Get(SSLCertKey);

                ProvidersLog.Current.Debug(
                    operationName: "SSLCertificateProvider.GetSSLCertificate",
                    message: $"Returning SSL certificate with thumbprint {cachedSSLCertificate.Thumbprint}.");

                return cachedSSLCertificate;
            }
            catch (Exception e)
            {
                ServiceEventSource.Current.Message($"Could not load SSL certificate from Store {e}");
                throw;
            }
        }
    }
}

