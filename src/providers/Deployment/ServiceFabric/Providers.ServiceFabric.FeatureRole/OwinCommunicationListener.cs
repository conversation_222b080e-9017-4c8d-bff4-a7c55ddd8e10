﻿//------------------------------------------------------------
// Copyright (c) Microsoft Corporation.  All rights reserved.
//------------------------------------------------------------

namespace Providers.ServiceFabric.FeatureRole
{
    using System;
    using System.Fabric;
    using System.Globalization;
    using System.Threading;
    using System.Threading.Tasks;
    using Microsoft.Owin.Hosting;
    using Microsoft.ServiceFabric.Services.Communication.Runtime;
    using Microsoft.WindowsAzure.ResourceStack.Common.EventSources;
    using Microsoft.WindowsAzure.ResourceStack.Common.Extensions;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Common.Logging;
    using Owin;

    /// <summary>
    /// Owin communication listener.
    /// </summary>
    public class OwinCommunicationListener : ICommunicationListener
    {
        private ICommonEventSource EventSource { get; }
        private readonly Action<IAppBuilder> startup;
        private readonly ServiceContext serviceContext;
        private readonly string endpointName;
        private readonly string appRoot;

        private IDisposable webApp;
        private string publishAddress;
        private string listeningAddress;

        /// <summary>
        /// Initializes a new instance of the <see cref="OwinCommunicationListener"/> class.
        /// </summary>
        /// <param name="startup">Startup.</param>
        /// <param name="serviceContext">Service context.</param>
        /// <param name="endpointName">Endpoint name.</param>
        public OwinCommunicationListener(Action<IAppBuilder> startup, ServiceContext serviceContext, IProvidersEventSource eventSource, string endpointName)
            : this(startup, serviceContext, eventSource, endpointName, null)
        {
        }

        /// <summary>
        /// Initializes a new instance of the <see cref="OwinCommunicationListener"/> class.
        /// </summary>
        /// <param name="startup">Startup.</param>
        /// <param name="serviceContext">Service context.</param>
        /// <param name="endpointName">Endpoint name.</param>
        /// <param name="appRoot">Application root.</param>
        public OwinCommunicationListener(Action<IAppBuilder> startup, ServiceContext serviceContext, IProvidersEventSource eventSource, string endpointName, string appRoot)
        {
            this.startup = startup ?? throw new ArgumentNullException(nameof(startup));
            this.serviceContext = serviceContext ?? throw new ArgumentNullException(nameof(serviceContext));
            this.EventSource = eventSource;
            this.endpointName = endpointName ?? throw new ArgumentNullException(nameof(endpointName));
            this.appRoot = appRoot;
        }

        /// <summary>
        /// Open Async.
        /// </summary>
        /// <param name="cancellationToken">Cancellation Token.</param>
        /// <returns>Task.</returns>
        public Task<string> OpenAsync(CancellationToken cancellationToken)
        {
            var serviceEndpoint = this.serviceContext.CodePackageActivationContext.GetEndpoint(this.endpointName);
            var protocol = serviceEndpoint.Protocol;
            int port = serviceEndpoint.Port;

            this.listeningAddress = string.Format(
                CultureInfo.InvariantCulture,
                "{0}://+:{1}/{2}",
                protocol,
                port,
                string.IsNullOrWhiteSpace(this.appRoot) ? string.Empty : this.appRoot.TrimEnd('/') + '/');

            this.publishAddress = this.listeningAddress.Replace("+", FabricRuntime.GetNodeContext().IPAddressOrFQDN);

            try
            {
                this.EventSource.Debug(
                    operationName: "OwinCommunicationListener.OpenAsync",
                    message: $"Starting web server on '{this.listeningAddress}'.");

                this.webApp = WebApp.Start(this.listeningAddress, appBuilder => this.startup.Invoke(appBuilder));

                this.EventSource.Debug(
                    operationName: "OwinCommunicationListener.OpenAsync",
                    message: $"Listening on '{this.publishAddress}'.");

                return Task.FromResult(this.publishAddress);
            }
            catch (Exception ex)
            {
                this.EventSource.Critical(
                    operationName: "OwinCommunicationListener.OpenAsync",
                    message: $"Unable to open endpoint '{this.endpointName}'.",
                    exception: ex);

                this.StopWebServer();

                if (ex.IsFatal())
                {
                    // When the static constructor, for ex: ApplicationProviderConfiguration throws an exception.
                    // The runtime doesn't invoke it a second time, and the type will remain uninitialized for the lifetime of the application domain.
                    // Following will immediately terminates a process after writing a message to the Windows Application event log, and then includes the message in error reporting to Microsoft.
                    // For more information see https://docs.microsoft.com/en-us/dotnet/csharp/programming-guide/classes-and-structs/static-constructors
                    Environment.FailFast(ex.Message);
                }

                throw;
            }
        }

        /// <summary>
        /// Close.
        /// </summary>
        /// <param name="cancellationToken">Cancellation token.</param>
        /// <returns>Task.</returns>
        public Task CloseAsync(CancellationToken cancellationToken)
        {
            this.EventSource.Debug(
                operationName: "OwinCommunicationListener.CloseAsync",
                message: $"Closing web server on endpoint '{this.endpointName}'.");

            this.StopWebServer();

            return Task.FromResult(true);
        }

        /// <summary>
        /// Abort.
        /// </summary>
        public void Abort()
        {
            this.EventSource.Debug(
                operationName: "OwinCommunicationListener.Abort",
                message: $"Aborting web server on endpoint '{this.endpointName}'.");

            this.StopWebServer();
        }

        /// <summary>
        /// Stops the Web Server.
        /// </summary>
        private void StopWebServer()
        {
            if (this.webApp != null)
            {
                try
                {
                    this.webApp.Dispose();
                }
                catch (ObjectDisposedException)
                {
                    // no-op
                }
            }
        }
    }
}
