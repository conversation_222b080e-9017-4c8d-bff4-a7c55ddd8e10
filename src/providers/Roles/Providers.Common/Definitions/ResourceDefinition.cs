﻿//------------------------------------------------------------
// Copyright (c) Microsoft Corporation.  All rights reserved.
//------------------------------------------------------------

namespace Microsoft.WindowsAzure.ResourceStack.Providers.Common.Data
{
    using System.Linq;
    using Microsoft.WindowsAzure.ResourceStack.Common.Collections;
    using Microsoft.WindowsAzure.ResourceStack.Common.Extensions;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Common.Extensions;
    using Microsoft.WindowsAzure.ResourceStack.Common.Utilities;
    using Newtonsoft.Json;
    using Newtonsoft.Json.Linq;

    /// <summary>
    /// The base resource definition.
    /// </summary>
    public class ResourceDefinition
    {
        /// <summary>
        /// Gets the id for the resource.
        /// </summary>
        [JsonProperty("id", Required = Required.Default)]
        public string Id
        {
            get
            {
                if (string.IsNullOrEmpty(this.SubscriptionId) ||
                    string.IsNullOrEmpty(this.Type) ||
                    string.IsNullOrEmpty(this.FullyQualifiedName))
                {
                    return null;
                }

                return ResourceDefinition.GetFullyQualifiedResourceId(
                    subscriptionId: this.SubscriptionId,
                    resourceGroup: this.ResourceGroup,
                    type: this.Type,
                    fullyQualifiedName: this.FullyQualifiedName);
            }
        }

        /// <summary>
        /// Gets the resource name.
        /// </summary>
        [JsonProperty("name", Required = Required.Default)]
        public virtual string Name
        {
            get
            {
                if (string.IsNullOrEmpty(this.FullyQualifiedName))
                {
                    return null;
                }

                return this.FullyQualifiedName.SplitRemoveEmpty('/').Last();
            }
        }

        /// <summary>
        /// Gets or sets the subscription.
        /// </summary>
        [JsonIgnore]
        public string SubscriptionId { get; set; }

        /// <summary>
        /// Gets or sets the name of the resource group.
        /// </summary>
        [JsonIgnore]
        public string ResourceGroup { get; set; }

        /// <summary>
        /// Gets or sets the type of the resource.
        /// </summary>
        [JsonProperty("type", Required = Required.Default)]
        public string Type { get; set; }

        /// <summary>
        /// Gets or sets the <c>sku</c> of the resource.
        /// </summary>
        [JsonProperty("sku", Required = Required.Default)]
        public JToken Sku { get; set; }

        /// <summary>
        /// Gets or sets the managed by property.
        /// </summary>
        [JsonProperty("managedBy", Required = Required.Default)]
        public string ManagedBy { get; set; }

        /// <summary>
        /// Gets or sets the resource fully qualified name.
        /// </summary>
        [JsonIgnore]
        public string FullyQualifiedName { get; set; }

        /// <summary>
        /// Gets or sets the storage account location.
        /// </summary>
        [JsonProperty("location", Required = Required.Default)]
        public string Location { get; set; }

        /// <summary>
        /// Gets or sets the availability zones.
        /// </summary>
        [JsonProperty("zones", Required = Required.Default)]
        public string[] Zones { get; set; }

        /// <summary>
        /// Gets or sets the <c>etag</c>.
        /// </summary>
        [JsonProperty("etag", Required = Required.Default)]
        public string ETag { get; set; }

        /// <summary>
        /// Gets or sets the tags.
        /// </summary>
        [JsonProperty("tags", Required = Required.Default)]
        public InsensitiveDictionary<string> Tags { get; set; }

        /// <summary>
        /// Gets or sets the scale.
        /// </summary>
        [JsonProperty("scale", Required = Required.Default)]
        public JToken Scale { get; set; }

        /// <summary>
        /// Gets the resource definition.
        /// </summary>
        public ResourceDefinition ToResourceDefinition()
        {
            return new ResourceDefinition
            {
                SubscriptionId = this.SubscriptionId,
                ResourceGroup = this.ResourceGroup,
                FullyQualifiedName = this.FullyQualifiedName,
                Type = this.Type,
                Location = this.Location,
                ETag = this.ETag,
                Tags = this.Tags,
                Scale = this.Scale
            };
        }

        /// <summary>
        /// Gets the fully qualified resource identifier.
        /// </summary>
        /// <param name="subscriptionId">The subscription identifier.</param>
        /// <param name="resourceGroup">The resource group.</param>
        /// <param name="type">The type.</param>
        /// <param name="fullyQualifiedName">Name of the fully qualified.</param>
        protected static string GetFullyQualifiedResourceId(string subscriptionId, string resourceGroup, string type, string fullyQualifiedName)
        {
            if (!string.IsNullOrEmpty(resourceGroup))
            {
                return string.Format(
                    format: "/subscriptions/{0}/resourceGroups/{1}/providers/{2}",
                    arg0: subscriptionId,
                    arg1: resourceGroup,
                    arg2: ResourceDefinition.GetResourceId(type, fullyQualifiedName));
            }
            else
            {
                return string.Format(
                    format: "/subscriptions/{0}/providers/{1}",
                    arg0: subscriptionId,
                    arg1: ResourceDefinition.GetResourceId(type, fullyQualifiedName));
            }
        }

        /// <summary>
        /// Gets the resource identifier.
        /// </summary>
        /// <param name="fullyQualifiedResourceType">Type of the fully qualified resource.</param>
        /// <param name="resourceName">Name of the resource.</param>
        private static string GetResourceId(string fullyQualifiedResourceType, string resourceName)
        {
            var resourceProviderNamespace = HttpUtility.GetPathSegments(fullyQualifiedResourceType).First();
            var nestedResourceTypes = HttpUtility.GetPathSegments(fullyQualifiedResourceType).Skip(1);
            var nestedResourceNames = HttpUtility.GetPathSegments(resourceName);
            var nestedResources = nestedResourceTypes.Zip(nestedResourceNames, (type, name) => string.Format("{0}/{1}", type, name));

            return string.Format("{0}/{1}", resourceProviderNamespace, nestedResources.ConcatStrings("/"));
        }
    }
}
