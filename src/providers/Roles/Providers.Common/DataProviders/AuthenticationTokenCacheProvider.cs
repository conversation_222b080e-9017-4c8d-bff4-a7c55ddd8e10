﻿//------------------------------------------------------------
// Copyright (c) Microsoft Corporation.  All rights reserved.
//------------------------------------------------------------

namespace Microsoft.WindowsAzure.ResourceStack.Providers.Common.DataProviders
{
    using System;
    using System.Collections.Generic;
    using System.Security.Cryptography.X509Certificates;
    using System.Threading.Tasks;
    using Microsoft.Identity.Client;
    using Microsoft.WindowsAzure.ResourceStack.Common.Algorithms;
    using Microsoft.WindowsAzure.ResourceStack.Common.EventSources;
    using Microsoft.WindowsAzure.ResourceStack.Common.Extensions;
    using Microsoft.WindowsAzure.ResourceStack.Common.Services;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Common.Logging;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Common.PerformanceCounters;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Common.Utilities;

    /// <summary>
    /// The authentication token cache provider.
    /// </summary>
    public class AuthenticationTokenCacheProvider
    {
        #region Properties

        /// <summary>
        /// Gets the token cache staleness threshold.
        /// </summary>
        private static TimeSpan TokenCacheStalenessThreshold
        {
            get
            {
                return CloudConfigurationManager.GetConfigurationTimeSpan(
                    settingName: "Microsoft.WindowsAzure.ResourceStack.Providers.AuthenticationTokenCacheProvider.StalenessThreshold",
                    defaultValue: TimeSpan.FromMinutes(30));
            }
        }

        /// <summary>
        /// Checks if this is a dev environment
        /// </summary>
        private static bool IsDevEnvironment =>
           CloudConfigurationManager.IsCloudEnvironmentEmulated
           && CloudConfigurationManager.GetConfiguration("Microsoft.WindowsAzure.ResourceStack.Providers.RoleLocation").StartsWithInsensitively("DevFabric");

        /// <summary>
        /// Gets the token cache recycle interval.
        /// </summary>
        private static TimeSpan TokenCacheRecycleInterval
        {
            get
            {
                return CloudConfigurationManager.GetConfigurationTimeSpan(
                    settingName: "Microsoft.WindowsAzure.ResourceStack.Providers.AuthenticationTokenCacheProvider.RecycleInterval",
                    defaultValue: TimeSpan.FromHours(4));
            }
        }

        /// <summary>
        /// Gets the retry count for getting token by token cache value factory.
        /// </summary>
        private static int TokenCacheRetryCount
        {
            get
            {
                return CloudConfigurationManager.GetConfigurationNumber(
                    settingName: "Microsoft.WindowsAzure.ResourceStack.Providers.AuthenticationTokenCacheProvider.RetryCount",
                    defaultValue: 3);
            }
        }

        /// <summary>
        /// Gets the retry interval for getting token by token cache value factory.
        /// </summary>
        private static TimeSpan TokenCacheRetryInterval
        {
            get
            {
                return CloudConfigurationManager.GetConfigurationTimeSpan(
                    settingName: "Microsoft.WindowsAzure.ResourceStack.Providers.AuthenticationTokenCacheProvider.RetryInterval",
                    defaultValue: TimeSpan.FromSeconds(1));
            }
        }

        /// <summary>
        /// Gets or sets the event source.
        /// </summary>
        private ICommonEventSource EventSource { get; set; }

        /// <summary>
        /// Gets or sets the cache of tokens. The key is the tenant Id.
        /// </summary>
        private AsyncPassThroughCache<string, AuthenticationResult> TokenCache { get; set; }

        /// <summary>
        /// Gets or sets the application Id.
        /// This is the identity for which this provider will cache tokens.
        /// </summary>
        private string FirstPartyApplicationId { get; set; }

        /// <summary>
        /// Gets or sets the service principal certificate.
        /// This certificate will be used to authenticate the application and get a token from Azure active directory.
        /// </summary>
        private X509Certificate2 ServicePrincipalCertificate { get; set; }

        /// <summary>
        /// Gets or sets the Azure active directory login address template.
        /// </summary>
        private Uri FirstPartyAadLoginTemplate { get; set; }

        /// <summary>
        /// Gets or sets the Azure active directory allowed audience.
        /// </summary>
        private string Audience { get; set; }

        /// <summary>
        /// Gets or sets the app Id for authentication to secrets resources.
        /// </summary>
        private string SecretsAppId { get; set; }

        /// <summary>
        /// Gets or sets the AAD login template for authentication to secrets resources.
        /// </summary>
        private Uri SecretsAadLoginTemplate { get; set; }

        /// <summary>
        /// Gets or sets the tenant Id for authentication to secrets resources.
        /// </summary>
        private string SecretsTenantId { get; set; }

        /// <summary>
        /// Gets or sets the Confidential App for authentication
        /// </summary>
        private IConfidentialClientApplication ConfidentialApp { get; set; }

        /// <summary>
        /// List of retryable transient error codes
        /// </summary>
        private readonly string[] TransientErrorCodes = { "AADSTS50076", "AADSTS700027" };
        #endregion

        /// <summary>
        /// Initializes a new instance of the <see cref="AuthenticationTokenCacheProvider" /> class.
        /// </summary>
        /// <param name="applicationId">The application Id for which tokens must be cached.</param>
        /// <param name="servicePrincipalCertificate">The service principal certificate to use to authenticate the application with Azure active directory.</param>
        /// <param name="aadLoginTemplate">The Azure active directory login address template.</param>
        /// <param name="aadAllowedAudience">The Azure active directory allowed audience.</param>
        /// <param name="eventSource">The event source.</param>
        /// <param name="secretsAppId">The app ID used for authenticating to secrets.</param>
        /// <param name="secretsLoginTemplate">The login template used for authenticating to secrets.</param>
        /// <param name="secretsTenantId">The tenant ID used for authenticating to secrets.</param>
        public AuthenticationTokenCacheProvider(string applicationId, X509Certificate2 servicePrincipalCertificate, Uri aadLoginTemplate, string aadAllowedAudience, ICommonEventSource eventSource, string secretsAppId, Uri secretsLoginTemplate, string secretsTenantId)
        {
            this.EventSource = eventSource;
            this.FirstPartyApplicationId = applicationId;
            this.ServicePrincipalCertificate = servicePrincipalCertificate;
            this.FirstPartyAadLoginTemplate = aadLoginTemplate;
            this.Audience = aadAllowedAudience;

            this.SecretsAppId = secretsAppId;
            this.SecretsAadLoginTemplate = secretsLoginTemplate;
            this.SecretsTenantId = secretsTenantId;

            this.EventSource.Debug(
                operationName: "AuthenticationTokenCacheProvider.AuthenticationTokenCacheProvider",
                format: "Instantiating AuthenticationTokenCacheProvider with applicationId '{0}', SecretsAppId '{1}', aadAllowedAudience '{2}' and aadLoginTemplate '{3}' and servicePrincipalCertificate Thumbprint '{4}' and SubjectName '{5}'.",
                arg0: applicationId,
                arg1: secretsAppId,
                arg2: aadAllowedAudience,
                arg3: aadLoginTemplate,
                arg4: servicePrincipalCertificate.Thumbprint,
                arg5: servicePrincipalCertificate.SubjectName.Name);

            this.TokenCache = new AsyncPassThroughCache<string, AuthenticationResult>(
                stalenessThreshold: AuthenticationTokenCacheProvider.TokenCacheStalenessThreshold,
                stalenessJitter: AuthenticationTokenCacheProvider.TokenCacheStalenessThreshold.ScaleBy(0.1),
                recycleInterval: AuthenticationTokenCacheProvider.TokenCacheRecycleInterval,
                operationInstrumentation: ProvidersPerformanceCountersExtensions.GetCacheOperationPerformanceCounter("AuthenticationTokenCacheProvider.TokenCache"),
                comparer: StringComparer.OrdinalIgnoreCase);
        }

        /// <summary>
        /// Gets the authentication token for the application Id under the scope of the given tenant.
        /// </summary>
        /// <param name="tenantId">The tenant to authenticate against.</param>
        /// <param name="forceRefresh">Optional. Indicates whether to force refresh the token.</param>
        public virtual Task<AuthenticationResult> GetAuthenticationToken(string tenantId, string audience = null, bool forceRefresh = false)
        {
            var effectiveAudience = audience ?? this.Audience;
            this.EventSource.Debug(
                operationName: "AuthenticationTokenCacheProvider.GetAuthenticationToken",
                format: "Gets the authentication token for the applicationId '{0}' under the scope of the given tenant. '{1}' with AadAllowedAudience: {2} with FirstPartyAadLoginTemplate '{3}' and servicePrincipalCertificate Thumbprint '{4}'.",
                arg0: this.FirstPartyApplicationId,
                arg1: tenantId,
                arg2: effectiveAudience,
                arg3: this.FirstPartyAadLoginTemplate,
                arg4: this.ServicePrincipalCertificate.Thumbprint);

            if (forceRefresh)
            {
                return this.RefreshAuthenticationToken(tenantId: tenantId, effectiveAudience);
            }

            return this.TokenCache.GetValue(
                cacheKey: tenantId + effectiveAudience,
                valueFactory: () => this.GetAuthenticationTokenFromAad(tenantId, effectiveAudience, this.FirstPartyApplicationId, this.FirstPartyAadLoginTemplate));
        }

        /// <summary>
        /// Gets the authentication token for the application Id under the scope of the given tenant.
        /// </summary>
        /// <param name="tenantId">The tenant to authenticate against.</param>
        /// <param name="resource">Audience for the authentication token.The audience to get an AAD token for</param>
        /// <param name="aadLoginTemplate">AAD Authority Uri to authenticate against.</param>
        public async Task<AuthenticationResult> GetAuthenticationTokenFromAad(string tenantId, string resource, Uri aadLoginTemplate)
        {
            this.EventSource.Debug(
                operationName: "AuthenticationTokenCacheProvider.GetAuthenticationToken",
                format: "Gets the authentication token for the applicationId '{0}' under the scope of the given tenant. '{1}' with resource: {2} with FirstPartyAadLoginTemplate '{3}' and servicePrincipalCertificate Thumbprint '{4}'.",
                arg0: this.FirstPartyApplicationId,
                arg1: tenantId,
                arg2: resource,
                arg3: aadLoginTemplate,
                arg4: this.ServicePrincipalCertificate.Thumbprint);

            var token = await this
                .GetAuthenticationTokenFromAad(tenantId, resource, this.FirstPartyApplicationId, aadLoginTemplate)
                .ConfigureAwait(continueOnCapturedContext: false);

            return token;
        }

        /// <summary>
        /// Gets the authentication token for the application Id under the scope of the given tenant.
        /// </summary>
        /// <param name="tenantId">The tenant to authenticate against.</param>
        /// <param name="resource">Audience for the authentication token.The audience to get an AAD token for</param>
        public async Task<AuthenticationResult> GetAuthenticationTokenFromAad(string tenantId, string resource)
        {
            return await this
                .GetAuthenticationTokenFromAad(tenantId, resource, this.FirstPartyApplicationId, this.FirstPartyAadLoginTemplate)
                .ConfigureAwait(continueOnCapturedContext: false);
        }

        /// <summary>
        /// Refreshes the authentication token for the application Id under the scope of the given tenant.
        /// </summary>
        /// <param name="tenantId">The tenant to authenticate against.</param>
        public async Task<AuthenticationResult> RefreshAuthenticationToken(string tenantId, string audience = null)
        {
            var effectiveAudience = audience ?? this.Audience;
            this.EventSource.Debug(
                operationName: "AuthenticationTokenCacheProvider.RefreshAuthenticationToken",
                format: "Refreshes the authentication token for the applicationId '{0}' under the scope of the given tenant. '{1}' with AadAllowedAudience: {2} with FirstPartyAadLoginTemplate '{3}' and servicePrincipalCertificate Thumbprint '{4}'.",
                arg0: this.FirstPartyApplicationId,
                arg1: tenantId,
                arg2: effectiveAudience,
                arg3: this.FirstPartyAadLoginTemplate,
                arg4: this.ServicePrincipalCertificate.Thumbprint);

            var token = await this
                .GetAuthenticationTokenFromAad(tenantId, effectiveAudience, this.FirstPartyApplicationId, this.FirstPartyAadLoginTemplate)
                .ConfigureAwait(continueOnCapturedContext: false);

            this.TokenCache.SetValue(cacheKey: tenantId + effectiveAudience, value: token);

            return token;
        }

        /// <summary>
        /// Gets an authentication token for the application Id under the scope of the given tenant, for a particular audience. Does not update the cache.
        /// </summary>
        /// <param name="resource">The audience to get a token for.</param>
        public async Task<AuthenticationResult> GetSecretsAuthenticationToken(string resource)
        {
            this.EventSource.Debug(
                operationName: "AuthenticationTokenCacheProvider.RefreshAuthenticationToken",
                format: "Gets the authentication token for the applicationId '{0}' under the scope of the given tenantId'{1}' for resource '{2}'. Does not update the cache",
                arg0: this.SecretsAppId,
                arg1: this.SecretsTenantId,
                arg2: resource);

            var token = await this
                .GetAuthenticationTokenFromAad(this.SecretsTenantId, resource, this.SecretsAppId, this.SecretsAadLoginTemplate)
                .ConfigureAwait(continueOnCapturedContext: false);

            return token;
        }

        /// <summary>
        /// Gets the authentication token from <c>AAD</c> for the application Id under the scope of the given tenant.
        /// </summary>
        /// <param name="tenantId">The tenant to authenticate against.</param>
        /// <param name="resource">Optional. The audience to get an AAD token for.</param>
        /// <param name="appId">Optional. The app Id to authenticate as.</param>
        /// <param name="aadLoginTemplate">Optional. The URL used for AAD.</param>
        private Task<AuthenticationResult> GetAuthenticationTokenFromAad(string tenantId, string resource, string appId, Uri aadLoginTemplate)
        {
            return AsyncRetry.Retry<AuthenticationResult>(
                retryCount: AuthenticationTokenCacheProvider.TokenCacheRetryCount,
                retryInterval: AuthenticationTokenCacheProvider.TokenCacheRetryInterval,
                action: () => this.GetAuthenticationTokenFromMsal(
                    aadLoginTemplate: aadLoginTemplate,
                    tenantId: tenantId,
                    resource: resource,
                    appId: appId,
                    certificate: this.ServicePrincipalCertificate),
                isRetryable: ex =>
                    IsRetryPossible(ex),
                errorAction: (Exception exception) =>
                {
                    this.EventSource.Error("AuthenticationTokenCacheProvider.GetAuthenticationTokenFromAad",
                        $"Failed to get MSAL Access token' failed. Framework will retry in {AuthenticationTokenCacheProvider.TokenCacheRetryInterval} seconds.Exception details: '{exception}'.");
                });
        }

        /// <summary>
        /// Gets the authentication Result from <c>AAD</c> for the application Id under the scope of the given tenant from MSAL library.
        /// </summary>
        /// <param name="tenantId">The tenant to authenticate against.</param>
        /// <param name="resource">Optional. The audience to get an AAD token for.</param>
        /// <param name="appId">Optional. The app Id to authenticate as.</param>
        /// <param name="aadLoginTemplate">Optional. The URL used for AAD.</param>
        /// <param name="certificate">Optional. The service principal certificate used to build the application.</param>
        private async Task<AuthenticationResult> GetAuthenticationTokenFromMsal(string tenantId, string resource, string appId, Uri aadLoginTemplate, X509Certificate2 certificate)
        {
            this.EventSource.Debug(
               operationName: "AuthenticationTokenCacheProvider.GetAuthenticationTokenFromMSAL",
               format: "Getting a new MSAL token with tenantId '{0}', resource '{1}', appId '{2}' and aadLoginTemplate '{3}' and servicePrincipalCertificate Thumbprint '{4}'.",
               arg0: tenantId,
               arg1: resource,
               arg2: appId,
               arg3: aadLoginTemplate,
               arg4: this.ServicePrincipalCertificate.Thumbprint);

            if (IsDevEnvironment)
            {
                ProvidersLog.Current.Critical(operationName: "AuthenticationTokenCacheProvider.GetAuthenticationTokenFromMsal", message: "Bypass authorization for development environment.");

                return new AuthenticationResult(
                    accessToken: "",
                    isExtendedLifeTimeToken: false,
                    uniqueId: Guid.NewGuid().ToString(),
                    expiresOn: DateTimeOffset.Now.AddMinutes(10),
                    extendedExpiresOn: DateTimeOffset.Now.AddMinutes(10),
                    tenantId: tenantId,
                    account: null,
                    idToken: "",
                    scopes: new List<string>(),
                    correlationId: Guid.NewGuid());
            }

            var aadRegion = string.IsNullOrEmpty(Utilities.Region) ? ConfidentialClientApplication.AttemptRegionDiscovery : Utilities.Region.ToLowerInvariant();

            this.EventSource.Debug(
              operationName: "AuthenticationTokenCacheProvider.GetAuthenticationTokenFromMSAL",
              format: "Getting a new MSAL token with Aad Region '{0}'",
              arg0: aadRegion);

            this.ConfidentialApp = ConfidentialClientApplicationBuilder.Create(appId)
                    .WithCertificate(certificate, sendX5C: true)
                    .WithAuthority($"{aadLoginTemplate}/{tenantId}")
                    .WithAzureRegion(aadRegion)
                    .Build() as ConfidentialClientApplication;

            var authResult = await this.ConfidentialApp.AcquireTokenForClient(
                new[] { $"{resource}/.default" })
                .WithTenantId(tenantId)
                .WithSendX5C(true)
                .ExecuteAsync()
                .ConfigureAwait(false);

            this.EventSource.Debug(
              operationName: "AuthenticationTokenCacheProvider.GetAuthenticationTokenFromMSAL",
              format: "Getting a new MSAL token with Aad TokenEndpoint '{0}', RegionUsed '{1}', AutoDetectionError '{2}'",
              arg0: authResult.AuthenticationResultMetadata.TokenEndpoint,
              arg1: authResult.AuthenticationResultMetadata.RegionDetails.RegionUsed,
              arg2: authResult.AuthenticationResultMetadata.RegionDetails.AutoDetectionError);

            return authResult;
        }

        /// <summary>
        /// Checks if the exception is retryable
        /// </summary>
        /// <param name="exception">The exception which occured during the function call.</param>
        private bool IsRetryPossible(Exception exception)
        {
            bool isRetryPossible = false;

            foreach (var errorCode in this.TransientErrorCodes)
            {
                if (!string.IsNullOrWhiteSpace(exception.Message) && exception.Message.ContainsInsensitively(errorCode))
                {
                    isRetryPossible = true;
                    break;
                }
            }

            return isRetryPossible;
        }
    }
}
