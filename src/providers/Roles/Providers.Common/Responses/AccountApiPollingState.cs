﻿﻿//-----------------------------------------------------------
// Copyright (c) Microsoft Corporation.  All rights reserved.
//-----------------------------------------------------------

namespace Microsoft.WindowsAzure.ResourceStack.Providers.Common.Responses
{
    /// <summary>
    /// Defines the possible states of an Account API polling operation.
    /// </summary>
    public enum AccountApiPollingState
    {
        /// <summary>
        /// Waiting for initialization operation to complete.
        /// </summary>
        WaitingForInitialization,

        /// <summary>
        /// Waiting for update operation to complete.
        /// </summary>
        WaitingForUpdate,

        /// <summary>
        /// Waiting for deletion operation to complete.
        /// </summary>
        WaitingForDeletion,

        /// <summary>
        /// Waiting for any operation to complete.
        /// </summary>
        WaitingForCompletion,

        /// <summary>
        /// Operation completed successfully.
        /// </summary>
        Completed,

        /// <summary>
        /// Operation failed.
        /// </summary>
        Failed
    }
}
