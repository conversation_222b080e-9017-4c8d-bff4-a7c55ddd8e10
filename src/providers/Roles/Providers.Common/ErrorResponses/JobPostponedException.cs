﻿﻿//-----------------------------------------------------------
// Copyright (c) Microsoft Corporation.  All rights reserved.
//-----------------------------------------------------------

namespace Microsoft.WindowsAzure.ResourceStack.Providers.Common.ErrorResponses
{
    using System;

    /// <summary>
    /// Exception thrown when a job needs to be postponed.
    /// This is used to signal that the job should be postponed and rescheduled for later execution.
    /// </summary>
    public class JobPostponedException : Exception
    {
        /// <summary>
        /// Gets the delay before the job should be rescheduled.
        /// </summary>
        public TimeSpan PostponeDelay { get; }

        /// <summary>
        /// Initializes a new instance of the <see cref="JobPostponedException"/> class.
        /// </summary>
        /// <param name="message">The error message.</param>
        /// <param name="postponeDelay">The delay before the job should be rescheduled.</param>
        public JobPostponedException(string message, TimeSpan postponeDelay)
            : base(message)
        {
            this.PostponeDelay = postponeDelay;
        }

        /// <summary>
        /// Initializes a new instance of the <see cref="JobPostponedException"/> class.
        /// </summary>
        /// <param name="message">The error message.</param>
        /// <param name="innerException">The inner exception.</param>
        /// <param name="postponeDelay">The delay before the job should be rescheduled.</param>
        public JobPostponedException(string message, Exception innerException, TimeSpan postponeDelay)
            : base(message, innerException)
        {
            this.PostponeDelay = postponeDelay;
        }
    }
}
