﻿using Newtonsoft.Json.Converters;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Microsoft.WindowsAzure.ResourceStack.Providers.Application.Data.Definitions.Enums
{
    /// <summary>
    /// Defines the possible states of an asynchronous operation on a Databricks workspace.
    /// This enum is used to track the progress and result of long-running operations.
    /// </summary>
    [JsonConverter(typeof(StringEnumConverter))]
    public enum DBOperationStatus
    {
        /// <summary>
        /// The operation is currently in progress.
        /// </summary>
        Running,

        /// <summary>
        /// The operation has completed successfully.
        /// </summary>
        Succeeded,

        /// <summary>
        /// The operation has failed.
        /// </summary>
        Failed
    }
}
