﻿using Newtonsoft.Json.Converters;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Microsoft.WindowsAzure.ResourceStack.Providers.Application.Data.Definitions.Enums
{
    /// <summary>
    /// Defines the possible states of a Databricks workspace within the Databricks control plane.
    /// This enum represents the operational status of the workspace in the Databricks service.
    /// </summary>
    [JsonConverter(typeof(StringEnumConverter))]
    public enum DBWorkspaceState
    {
        /// <summary>
        /// The workspace is actively running and available for users.
        /// </summary>
        Active,

        /// <summary>
        /// The workspace is not active, either because it's being provisioned, 
        /// deprovisioned, or is in a suspended state.
        /// </summary>
        Inactive
    }
}
