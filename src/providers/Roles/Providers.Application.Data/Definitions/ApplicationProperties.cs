﻿//------------------------------------------------------------
// Copyright (c) Microsoft Corporation.  All rights reserved.
//------------------------------------------------------------

namespace Microsoft.WindowsAzure.ResourceStack.Providers.Application.Data.Definitions
{
    using System.Collections.Generic;
    using Microsoft.WindowsAzure.ResourceStack.Common.Collections;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Application.Data.Definitions.Enums;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Application.Data.Entities;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Common.Data;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Common.Definitions;
    using Newtonsoft.Json;
    using Newtonsoft.Json.Converters;
    using Newtonsoft.Json.Linq;

    /// <summary>
    /// The appliance properties.
    /// </summary>
    public class ApplicationProperties
    {
        /// <summary>
        /// Gets or sets the Compute Mode.
        /// </summary>
        [JsonProperty(Required = Required.Default, NullValueHandling = NullValueHandling.Ignore)]
        public ComputeMode? ComputeMode { get; set; }

        /// <summary>
        /// Gets or sets the managed resource group Id.
        /// </summary>
        [JsonProperty(Required = Required.Default)]
        public string ManagedResourceGroupId { get; set; }

        /// <summary>
        /// Gets or sets the disk encryption set resource Id.
        /// </summary>
        [JsonProperty(Required = Required.Default)]
        public string DiskEncryptionSetId { get; set; }

        /// <summary>
        /// Gets or sets the publisher package Id.
        /// </summary>
        [JsonProperty(Required = Required.Default)]
        public string PublisherPackageId { get; set; }

        /// <summary>
        /// Gets or sets the application parameters.
        /// </summary>
        [JsonProperty(Required = Required.Default)]
        public InsensitiveDictionary<JToken> Parameters { get; set; }

        /// <summary>
        /// Gets or sets the application provisioning state.
        /// </summary>
        [JsonProperty(Required = Required.Default)]
        public ProvisioningState? ProvisioningState { get; set; }

        /// <summary>
        /// Gets or sets the read-only authorizations property that is retrieved from the application package.
        /// </summary>
        [JsonProperty(Required = Required.Default)]
        public ApplicationAuthorizationEntity[] Authorizations { get; set; }

        /// <summary>
        /// Gets or sets the client entity that created the application.
        /// </summary>
        [JsonProperty(Required = Required.Default)]
        public ApplicationClientDetailsEntity CreatedBy { get; set; }

        /// <summary>
        /// Gets or sets the client entity that last updated the application.
        /// </summary>
        [JsonProperty(Required = Required.Default)]
        public ApplicationClientDetailsEntity UpdatedBy { get; set; }

        /// <summary>
        /// Gets or sets the workspace id.
        /// </summary>
        [JsonProperty(Required = Required.Default)]
        public string WorkspaceId { get; set; }

        /// <summary>
        /// Gets or sets the workspace URL.
        /// </summary>
        [JsonProperty(Required = Required.Default)]
        public string WorkspaceUrl { get; set; }

        /// <summary>
        /// Gets or sets value
        /// </summary>
        [JsonProperty("privateLinkAssets", NullValueHandling = NullValueHandling.Ignore)]
        public List<string> PrivateLinkAssets { get; set; }

        /// <summary>
        /// Gets or sets the storage account identity property.
        /// </summary>
        [JsonProperty(Required = Required.Default)]
        public StorageAccountIdentityEntity StorageAccountIdentityEntity { get; set; }

        /// <summary>
        /// Gets or sets the managed disk identity property.
        /// </summary>
        [JsonProperty(Required = Required.Default)]
        public ManagedDiskIdentityEntity ManagedDiskIdentityEntity { get; set; }

        /// <summary>
        /// Gets or sets the Private Endpoint Connections proxies.
        /// </summary>
        [JsonProperty(Required = Required.Default)]
        public List<PrivateEndpointConnectionProxy> PrivateEndpointConnectionProxies { get; set; }

        /// <summary>
        /// Gets or sets the Public Network Access
        /// </summary>
        [JsonProperty(Required = Required.Default, NullValueHandling = NullValueHandling.Ignore)]
        [JsonConverter(typeof(StringEnumConverter))]
        public PublicNetworkAccessStatus? PublicNetworkAccess { get; set; }

        /// <summary>
        /// Gets or sets the Required Network Security Group Rules
        /// </summary>
        [JsonProperty(Required = Required.Default, NullValueHandling = NullValueHandling.Ignore)]
        [JsonConverter(typeof(StringEnumConverter))]
        public RequiredNetworkSecurityGroupType? RequiredNsgRules { get; set; }

        /// <summary>
        /// Gets or sets the encryption properties
        /// </summary>
        [JsonProperty(Required = Required.Default, NullValueHandling = NullValueHandling.Ignore)]
        public EncryptionPropertiesEntity EncryptionProperties { get; set; }

        /// <summary>
        /// Gets or sets the access Connector property.
        /// </summary>
        [JsonProperty("accessConnector", Required = Required.DisallowNull, NullValueHandling = NullValueHandling.Ignore)]
        public AccessConnectorIdEntity AccessConnector { get; set; }

        /// <summary>
        /// Gets or sets the default Storage Firewall configuration.
        /// </summary>
        [JsonProperty("defaultStorageFirewall", Required = Required.DisallowNull, NullValueHandling = NullValueHandling.Ignore)]
        public DefaultStorageFirewall? DefaultStorageFirewall { get; set; }

        /// <summary>
        /// Gets or Sets the workspace configuration for Enhanced Security & Compliance add-on
        /// </summary>
        [JsonProperty(Required = Required.Default, NullValueHandling = NullValueHandling.Ignore)]
        public EnhancedSecurityCompliance EnhancedSecurityCompliance { get; set; }

        /// <summary>
        /// Gets or sets the defaultCatalog configuration.
        /// </summary>
        [JsonProperty(Required = Required.DisallowNull, NullValueHandling = NullValueHandling.Ignore)]
        public DefaultCatalog DefaultCatalog { get; set; }

        /// <summary>
        /// Gets the whether Uc is Enabled or not.
        /// </summary>
        [JsonProperty(Required = Required.Default)]
        public bool? IsUcEnabled { get; set; }
    }
}