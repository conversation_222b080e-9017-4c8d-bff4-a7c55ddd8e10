﻿//------------------------------------------------------------
// Copyright (c) Microsoft Corporation.  All rights reserved.
//------------------------------------------------------------

namespace Microsoft.WindowsAzure.ResourceStack.Providers.Application.Data.Definitions.DBAsyncContract
{
    using Microsoft.WindowsAzure.ResourceStack.Providers.Application.Data.Definitions.Enums;
    using Newtonsoft.Json;

    /// <summary>
    /// Represents the status and details of an asynchronous operation performed on a Databricks workspace.
    /// This class encapsulates the current state of the operation and any error information if applicable.
    /// </summary>
    public class DBOperationStatusObject
    {
        /// <summary>
        /// Gets or sets the status of the operation.
        /// Represents the current state (Running, Succeeded, or Failed) of an asynchronous 
        /// operation being performed on the Databricks workspace.
        /// </summary>
        [JsonProperty("status", Required = Required.Default)]
        public DBOperationStatus OperationStatus { get; set; }

        /// <summary>
        /// Gets or sets the error message associated with a failed operation.
        /// Contains detailed error information when an operation fails, or null/empty when 
        /// the operation is successful or still in progress.
        /// </summary>
        [JsonProperty("errorMessage", Required = Required.Default)]
        public string ErrorMessage { get; set; }
    }
}
