﻿//------------------------------------------------------------
// Copyright (c) Microsoft Corporation.  All rights reserved.
//------------------------------------------------------------

namespace Microsoft.WindowsAzure.ResourceStack.Providers.Application.Data.Definitions.DBCSContract
{
    using Microsoft.WindowsAzure.ResourceStack.Providers.Application.Data.Definitions.Enums;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Common.Definitions;
    using Newtonsoft.Json;

    /// <summary>
    /// DBNotification workspace properties class.
    /// </summary>
    public class WorkspaceProperties
    {
        /// <summary>
        /// Gets or sets the application parameters.
        /// </summary>
        [JsonProperty("parameters", Required = Required.Default)]
        public WorkspaceParameters Parameters { get; set; }

        /// <summary>
        /// Gets or sets the encryption property
        /// </summary>
        [JsonProperty("encryption", Required = Required.Default)]
        public Encryption Encryption { get; set; }

        /// <summary>
        /// Gets or sets the disk encryption set ID property
        /// </summary>
        [JsonProperty("diskEncryptionSetId", Required = Required.Default)]
        public string DiskEncryptionSetId { get; set; }

        /// <summary>
        /// Gets or sets the Public Network Access
        /// </summary>
        [JsonProperty("publicNetworkAccess", Required = Required.Default)]
        public PublicNetworkAccessStatus? PublicNetworkAccess { get; set; }

        /// <summary>
        /// Gets or sets the Required Network Security Group Rules
        /// </summary>
        [JsonProperty("requiredNsgRules", Required = Required.Default)]
        public RequiredNetworkSecurityGroupType? RequiredNsgRules { get; set; }

        /// <summary>
        /// Gets or sets the access Connector property.
        /// </summary>
        [JsonProperty("accessConnector", Required = Required.Default, NullValueHandling = NullValueHandling.Ignore)]
        public AccessConnectorId AccessConnector { get; set; }

        /// <summary>
        /// Gets or sets default Storage Firewall configuration.
        /// </summary>
        [JsonProperty("defaultStorageFirewall", Required = Required.Default, NullValueHandling = NullValueHandling.Ignore)]
        public DefaultStorageFirewall? DefaultStorageFirewall { get; set; }

        /// <summary>
        /// Gets or sets the Enhanced Security Compliance configuration
        /// </summary>
        [JsonProperty("enhancedSecurityCompliance", Required = Required.Default, NullValueHandling = NullValueHandling.Ignore)]
        public EnhancedSecurityCompliance EnhancedSecurityCompliance { get; set; }
    }
}
