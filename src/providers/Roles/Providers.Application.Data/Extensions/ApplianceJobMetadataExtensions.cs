﻿﻿//------------------------------------------------------------
// Copyright (c) Microsoft Corporation.  All rights reserved.
//------------------------------------------------------------

namespace Microsoft.WindowsAzure.ResourceStack.Providers.Application.Data.Extensions
{
    using Microsoft.WindowsAzure.ResourceStack.Providers.Application.Data.Definitions.Enums;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Application.Data.Metadata;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Common.Responses;
    using System;

    /// <summary>
    /// Extension methods for ApplianceJobMetadata.
    /// </summary>
    public static class ApplianceJobMetadataExtensions
    {
        public const string AccountApiPollingStateKey = "AccountApiPollingState";
        public const string AccountApiOperationIdKey = "AccountApiOperationId";
        public const string AccountApiOperationStartTimeKey = "AccountApiOperationStartTime";
        public const string AccountApiBaseUriKey = "AccountApiBaseUri";
        public const string AccountApiAudienceKey = "AccountApiAudience";
        public const string WorkspaceDetailsKey = "WorkspaceDetails";

        /// <summary>
        /// Gets the Account API polling state.
        /// </summary>
        /// <param name="metadata">The metadata.</param>
        /// <returns>The Account API polling state.</returns>
        public static AccountApiPollingState GetAccountApiPollingState(this ApplianceJobMetadata metadata)
        {
            if (metadata is ApplianceProvisioningJobMetadata provisioningMetadata)
            {
                return provisioningMetadata.AccountApiPollingState;
            }

            if (metadata.Properties.TryGetValue(AccountApiPollingStateKey, out object value) && value is string stateStr)
            {
                if (Enum.TryParse<AccountApiPollingState>(stateStr, out var state))
                {
                    return state;
                }
            }

            return AccountApiPollingState.WaitingForCompletion;
        }

        /// <summary>
        /// Sets the Account API polling state.
        /// </summary>
        /// <param name="metadata">The metadata.</param>
        /// <param name="state">The state to set.</param>
        public static void SetAccountApiPollingState(this ApplianceJobMetadata metadata, AccountApiPollingState state)
        {
            if (metadata is ApplianceProvisioningJobMetadata provisioningMetadata)
            {
                provisioningMetadata.AccountApiPollingState = state;
                return;
            }

            metadata.Properties[AccountApiPollingStateKey] = state.ToString();
        }

        /// <summary>
        /// Gets the Account API operation ID.
        /// </summary>
        /// <param name="metadata">The metadata.</param>
        /// <returns>The Account API operation ID.</returns>
        public static string GetAccountApiOperationId(this ApplianceJobMetadata metadata)
        {
            if (metadata is ApplianceProvisioningJobMetadata provisioningMetadata)
            {
                return provisioningMetadata.AccountApiOperationId;
            }

            if (metadata.Properties.TryGetValue(AccountApiOperationIdKey, out object value) && value is string operationId)
            {
                return operationId;
            }

            return null;
        }

        /// <summary>
        /// Sets the Account API operation ID.
        /// </summary>
        /// <param name="metadata">The metadata.</param>
        /// <param name="operationId">The operation ID to set.</param>
        public static void SetAccountApiOperationId(this ApplianceJobMetadata metadata, string operationId)
        {
            if (metadata is ApplianceProvisioningJobMetadata provisioningMetadata)
            {
                provisioningMetadata.AccountApiOperationId = operationId;
                return;
            }

            metadata.Properties[AccountApiOperationIdKey] = operationId;
        }

        /// <summary>
        /// Gets the Account API operation start time.
        /// </summary>
        /// <param name="metadata">The metadata.</param>
        /// <returns>The Account API operation start time.</returns>
        public static DateTime? GetAccountApiOperationStartTime(this ApplianceJobMetadata metadata)
        {
            if (metadata is ApplianceProvisioningJobMetadata provisioningMetadata)
            {
                return provisioningMetadata.AccountApiOperationStartTime;
            }

            if (metadata.Properties.TryGetValue(AccountApiOperationStartTimeKey, out object value) && value is DateTime startTime)
            {
                return startTime;
            }

            return null;
        }

        /// <summary>
        /// Sets the Account API operation start time.
        /// </summary>
        /// <param name="metadata">The metadata.</param>
        /// <param name="startTime">The start time to set.</param>
        public static void SetAccountApiOperationStartTime(this ApplianceJobMetadata metadata, DateTime startTime)
        {
            if (metadata is ApplianceProvisioningJobMetadata provisioningMetadata)
            {
                provisioningMetadata.AccountApiOperationStartTime = startTime;
                return;
            }

            metadata.Properties[AccountApiOperationStartTimeKey] = startTime;
        }

        /// <summary>
        /// Gets the Account API base URI.
        /// </summary>
        /// <param name="metadata">The metadata.</param>
        /// <returns>The Account API base URI.</returns>
        public static Uri GetAccountApiBaseUri(this ApplianceJobMetadata metadata)
        {
            if (metadata is ApplianceProvisioningJobMetadata provisioningMetadata)
            {
                return provisioningMetadata.AccountApiBaseUri;
            }

            if (metadata.Properties.TryGetValue(AccountApiBaseUriKey, out object value) && value is string uriStr)
            {
                if (Uri.TryCreate(uriStr, UriKind.Absolute, out var uri))
                {
                    return uri;
                }
            }

            return null;
        }

        /// <summary>
        /// Sets the Account API base URI.
        /// </summary>
        /// <param name="metadata">The metadata.</param>
        /// <param name="baseUri">The base URI to set.</param>
        public static void SetAccountApiBaseUri(this ApplianceJobMetadata metadata, Uri baseUri)
        {
            if (metadata is ApplianceProvisioningJobMetadata provisioningMetadata)
            {
                provisioningMetadata.AccountApiBaseUri = baseUri;
                return;
            }

            metadata.Properties[AccountApiBaseUriKey] = baseUri.ToString();
        }

        /// <summary>
        /// Gets the Account API audience.
        /// </summary>
        /// <param name="metadata">The metadata.</param>
        /// <returns>The Account API audience.</returns>
        public static string GetAccountApiAudience(this ApplianceJobMetadata metadata)
        {
            if (metadata is ApplianceProvisioningJobMetadata provisioningMetadata)
            {
                return provisioningMetadata.AccountApiAudience;
            }

            if (metadata.Properties.TryGetValue(AccountApiAudienceKey, out object value) && value is string audience)
            {
                return audience;
            }

            return null;
        }

        /// <summary>
        /// Sets the Account API audience.
        /// </summary>
        /// <param name="metadata">The metadata.</param>
        /// <param name="audience">The audience to set.</param>
        public static void SetAccountApiAudience(this ApplianceJobMetadata metadata, string audience)
        {
            if (metadata is ApplianceProvisioningJobMetadata provisioningMetadata)
            {
                provisioningMetadata.AccountApiAudience = audience;
                return;
            }

            metadata.Properties[AccountApiAudienceKey] = audience;
        }

        /// <summary>
        /// Gets the workspace details.
        /// </summary>
        /// <param name="metadata">The metadata.</param>
        /// <returns>The workspace details.</returns>
        public static WorkspaceDetails GetWorkspaceDetails(this ApplianceJobMetadata metadata)
        {
            // First check if the property is set directly
            if (metadata.WorkspaceDetails != null)
            {
                return metadata.WorkspaceDetails;
            }

            // Then check if it's stored in the properties dictionary
            if (metadata.Properties.TryGetValue(WorkspaceDetailsKey, out object value) && value is WorkspaceDetails details)
            {
                return details;
            }

            return null;
        }

        /// <summary>
        /// Sets the workspace details.
        /// </summary>
        /// <param name="metadata">The metadata.</param>
        /// <param name="workspaceDetails">The workspace details to set.</param>
        public static void SetWorkspaceDetails(this ApplianceJobMetadata metadata, WorkspaceDetails workspaceDetails)
        {
            // Set the property directly
            metadata.WorkspaceDetails = workspaceDetails;

            // Also store in properties dictionary for backward compatibility
            metadata.Properties[WorkspaceDetailsKey] = workspaceDetails;
        }
    }
}
