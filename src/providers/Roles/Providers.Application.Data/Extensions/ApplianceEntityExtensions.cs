//-----------------------------------------------------------
// Copyright (c) Microsoft Corporation.  All rights reserved.
//-----------------------------------------------------------

namespace Microsoft.WindowsAzure.ResourceStack.Providers.Application.Data.Extensions
{
    using System;
    using System.Collections.Generic;
    using System.Net;
    using Microsoft.WindowsAzure.ResourceStack.Common.Collections;
    using Microsoft.WindowsAzure.ResourceStack.Common.EventSources;
    using Microsoft.WindowsAzure.ResourceStack.Common.Extensions;
    using Microsoft.WindowsAzure.ResourceStack.Common.Instrumentation;
    using Microsoft.WindowsAzure.ResourceStack.Common.Json;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Application.Data.Definitions;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Application.Data.Definitions.DBCSContract;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Application.Data.Definitions.Enums;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Application.Data.Entities;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Application.Data.Metadata;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Common.Constants;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Common.Definitions;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Common.Definitions.ManagedIdentity;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Common.Entities;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Common.ErrorResponses;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Common.Extensions;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Common.Logging;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Common.Utilities;
    using Newtonsoft.Json;
    using Newtonsoft.Json.Linq;

    /// <summary>
    /// The appliance extensions methods.
    /// </summary>
    public static class ApplianceEntityExtensions
    {
        /// <summary>
        /// Generates a DBNotification object given an ApplianceEntity object
        /// </summary>
        /// <param name="existingEntity">The ApplianceEntity object</param>
        /// <returns>The DBNotification object</returns>
        public static DBWorkspace ToDbWorkspace(this ApplianceEntity existingEntity)
        {
            var managedServices = existingEntity.Properties.EncryptionProperties?.EncryptionEntities?.ManagedServices;
            var enhancedSecurityCompliance = existingEntity.Properties.EnhancedSecurityCompliance;
            var computeMode = existingEntity.GetComputeMode();

            var dbWorkspace = new DBWorkspace()
            {
                Update = new WorkspaceSubset()
                {
                    Properties = new WorkspaceProperties()
                    {
                        Encryption = managedServices == null ? null : new Encryption()
                        {
                            Entities = new EncryptionEntities()
                            {
                                ManagedServices = new ManagedServices()
                                {
                                    KeySource = managedServices.KeySource,
                                    KeyVaultProperties = managedServices.KeySource == ManagedEncryptionKeySource.Default ?
                                        null : new ManagedServicesKeyVault()
                                        {
                                            KeyName = managedServices.KeyVaultProperties.KeyName,
                                            KeyVersion = managedServices.KeyVaultProperties.KeyVersion,
                                            KeyVaultUri = managedServices.KeyVaultProperties.KeyVaultUri
                                        }
                                }
                            }
                        },
                        EnhancedSecurityCompliance = enhancedSecurityCompliance == null ? null : new EnhancedSecurityCompliance()
                        {
                            AutomaticClusterUpdate = new AutomaticClusterUpdate()
                            {
                                Value = enhancedSecurityCompliance.AutomaticClusterUpdate.Value,
                            },
                            ComplianceSecurityProfile = new ComplianceSecurityProfile()
                            {
                                Value = enhancedSecurityCompliance.ComplianceSecurityProfile.Value,
                                ComplianceStandards = enhancedSecurityCompliance.ComplianceSecurityProfile.ComplianceStandards
                            },
                            EnhancedSecurityMonitoring = new EnhancedSecurityMonitoring()
                            {
                                Value = enhancedSecurityCompliance.EnhancedSecurityMonitoring.Value
                            }
                        },
                    },
                    Sku = existingEntity.Sku,
                    Tags = existingEntity.Tags,
                }
            };

            if (computeMode == ComputeMode.Hybrid)
            {
                dbWorkspace.Update.Properties.Parameters = new WorkspaceParameters()
                {
                    CustomVirtualNetworkId = existingEntity.Properties.Parameters.ContainsKey(ProviderConstants.Databricks.CustomVirtualNetworkIdProperty) ?
                                existingEntity.Properties.Parameters[ProviderConstants.Databricks.CustomVirtualNetworkIdProperty] : null,
                    CustomPublicSubnetName = existingEntity.Properties.Parameters.ContainsKey(ProviderConstants.Databricks.CustomPublicSubnetNameProperty) ?
                                existingEntity.Properties.Parameters[ProviderConstants.Databricks.CustomPublicSubnetNameProperty] : null,
                    CustomPrivateSubnetName = existingEntity.Properties.Parameters.ContainsKey(ProviderConstants.Databricks.CustomPrivateSubnetNameProperty) ?
                                existingEntity.Properties.Parameters[ProviderConstants.Databricks.CustomPrivateSubnetNameProperty] : null,
                    AmlWorkspaceId = existingEntity.Properties.Parameters.ContainsKey(ProviderConstants.Databricks.AmlWorkspaceIdProperty) ?
                                existingEntity.Properties.Parameters[ProviderConstants.Databricks.AmlWorkspaceIdProperty] : null,
                    EnableNoPublicIp = existingEntity.Properties.Parameters.ContainsKey(ProviderConstants.Databricks.EnableNoPublicIpProperty) ?
                                existingEntity.Properties.Parameters[ProviderConstants.Databricks.EnableNoPublicIpProperty] : null
                };
                dbWorkspace.Update.Properties.DiskEncryptionSetId = existingEntity.Properties.DiskEncryptionSetId;
                dbWorkspace.Update.Properties.PublicNetworkAccess = existingEntity.Properties.PublicNetworkAccess.GetPublicNetworkAccessDefaultValue();
                dbWorkspace.Update.Properties.RequiredNsgRules = existingEntity.Properties.RequiredNsgRules.GetRequiredNsgRulesMemberDefaultValue();
                dbWorkspace.Update.Properties.DefaultStorageFirewall = existingEntity.Properties.DefaultStorageFirewall;
                dbWorkspace.Update.Properties.AccessConnector = existingEntity.Properties.AccessConnector == null ? null
                    : new AccessConnectorId
                    {
                        Id = existingEntity.Properties.AccessConnector.Id,
                        IdentityType = existingEntity.Properties.AccessConnector.IdentityType,
                        UserAssignedIdentityId = existingEntity.Properties.AccessConnector.UserAssignedIdentityId
                    };
            }

            return dbWorkspace;
        }

        /// <summary>
        /// Update Access Connector Properties in Appliance Entity from Access Connector Entity based on access connector identityType
        /// </summary>
        /// <param name="applianceEntity">incoming entity</param>
        /// <param name="connectorEntity">access connector entity in DB</param>
        /// <returns></returns>
        public static void UpdateApplianceEntityWithAccessConnectorProperty(
            this ApplianceEntity applianceEntity,
            AccessConnectorIdEntity accessConnectorId,
            AccessConnectorEntity connectorEntity)
        {
            var operationName = Utilities.GetAsyncMethodName();

            ProvidersLog.Current.Debug(operationName, $"Populate AccessConnectorEntity for '{accessConnectorId.Id}', " +
                $"identityType: '{accessConnectorId.IdentityType}'" +
                $"UserAssignedIdentityId: '{accessConnectorId.UserAssignedIdentityId}'.");

            if (accessConnectorId.IdentityType == IdentityType.UserAssigned)
            {
                applianceEntity.Properties.AccessConnector.PrincipalId = connectorEntity.Identity.UserAssignedIdentities[accessConnectorId.UserAssignedIdentityId].PrincipalId;
                applianceEntity.Properties.AccessConnector.TenantId = connectorEntity.Identity.UserAssignedIdentities[accessConnectorId.UserAssignedIdentityId].TenantId ??
                    RequestCorrelationContext.Current.GetHomeTenantId();
            }
            else
            {
                applianceEntity.Properties.AccessConnector.PrincipalId = connectorEntity.Identity.PrincipalId;
                applianceEntity.Properties.AccessConnector.TenantId = connectorEntity.Identity.TenantId;
            }

            ProvidersLog.Current.Debug(operationName, $"Populated applianceEntity with " +
                $"principalId: '{applianceEntity.Properties.AccessConnector.PrincipalId}' " +
                $"tenantId: '{applianceEntity.Properties.AccessConnector.TenantId}'");

            return;
        }

        /// <summary>
        /// Method of populating storage account details.
        /// </summary>
        /// <param name="applianceEntity">Appliance Entity</param>
        /// <param name="storageDefinition">Storage Definition</param>
        /// <returns>appliance entity is being returned</returns>
        public static void PopulateStorageAccountIdentityEntity(this ApplianceEntity applianceEntity, StorageDefinition storageDefinition)
        {
            applianceEntity.Properties.StorageAccountIdentityEntity = new StorageAccountIdentityEntity()
            {
                PrincipalId = storageDefinition.Identity.TryGetProperty<string>(ProviderConstants.Storage.PrincipalId),
                TenantId = storageDefinition.Identity.TryGetProperty<string>(ProviderConstants.Storage.TenantId),
                Type = StorageAccountIdentityType.SystemAssigned
            };

            applianceEntity.Properties.Parameters[ProviderConstants.Storage.PrepareEncryption] = new JObject()
            {
                new JProperty(ProviderConstants.Storage.ParameterType, ProviderConstants.Storage.ParameterBoolType),
                new JProperty(ProviderConstants.Storage.ParameterValue, true)
            };

            return;
        }

        /// <summary>
        /// Method of populating managed disk encryption details.
        /// </summary>
        /// <param name="applianceEntity">Appliance Entity</param>
        /// <param name="diskEncryptionSetDefinition">Disk Encryption Set Definition</param>
        /// <returns>appliance entity is being returned</returns>
        public static void PopulateManagedDiskEncryptionDetails(this ApplianceEntity applianceEntity, DiskEncryptionSetDefinition diskEncryptionSetDefinition)
        {
            applianceEntity.Properties.ManagedDiskIdentityEntity = new ManagedDiskIdentityEntity()
            {
                PrincipalId = diskEncryptionSetDefinition.Identity.TryGetProperty<string>(ProviderConstants.Storage.PrincipalId),
                TenantId = diskEncryptionSetDefinition.Identity.TryGetProperty<string>(ProviderConstants.Storage.TenantId),
                Type = ManagedDiskIdentityType.SystemAssigned
            };

            var mrgId = applianceEntity.Properties.ManagedResourceGroupId;
            var diskEncryptionSetName = Utilities.GenerateUniqueDESName(mrgId, applianceEntity.SubscriptionId);

            applianceEntity.Properties.DiskEncryptionSetId = $"{mrgId}/providers/Microsoft.Compute/diskEncryptionSets/{diskEncryptionSetName}";

            return;
        }

        /// <summary>
        /// Method for setting AML workspace ID for Appliance Entity
        /// </summary>
        /// <param name="applianceEntity">Appliance Entity</param>
        /// <param name="amlWorkspaceId">AML workspace ID</param>
        /// <returns>appliance entity with AML workspace updated</returns>
        public static void SetAmlWorkspaceId(this ApplianceEntity applianceEntity, string amlWorkspaceId)
        {
            applianceEntity.Properties.Parameters[ProviderConstants.Databricks.AmlWorkspaceIdProperty] = new JObject()
            {
                new JProperty(ProviderConstants.Storage.ParameterType, ProviderConstants.Storage.ParameterStringType),
                new JProperty(ProviderConstants.Storage.ParameterValue, amlWorkspaceId)
            };

            return;
        }

        /// <summary>
        /// Set DBFS storage sku for Appliance Entity
        /// </summary>
        /// <param name="applianceEntity">Appliance Entity</param>
        /// <param name="skuValue">Storage Account Sku like Standard_GRS, Standard_LRS</param>
        /// <returns>Set Appliance entity with Storage Sku</returns>
        public static void SetStorageAccountSku(this ApplianceEntity applianceEntity, string skuValue)
        {
            applianceEntity.Properties.Parameters[ProviderConstants.Databricks.StorageAccountSkuNameParameter] = new JObject()
            {
                new JProperty(ProviderConstants.Storage.ParameterType, ProviderConstants.Storage.ParameterStringType),
                new JProperty(ProviderConstants.Storage.ParameterValue, skuValue)
            };

            return;
        }

        /// <summary>
        /// Returns the fully qualified Id for the appliance.
        /// </summary>
        /// <param name="applianceEntity">The appliance entity</param>
        /// <param name="resourceType">The resource type</param>
        public static string GetFullyQualifiedId(this ApplianceEntity applianceEntity, string resourceType)
        {
            var appliance = new Appliance(resourceType)
            {
                SubscriptionId = applianceEntity.SubscriptionId,
                ResourceGroup = applianceEntity.ResourceGroup,
                FullyQualifiedName = applianceEntity.Name,
            };

            return appliance.Id;
        }

        /// <summary>
        /// Checks whether workspace is FedRAMP Certified
        /// </summary>
        /// <param name="applicationProperties">The application properties</param>
        /// <param name="logger">The logger</param>
        public static bool IsApplicationPropertiesFedRamp(this ApplicationProperties applicationProperties, ICommonEventSource logger)
        {
            if (applicationProperties.Parameters == null)
            {
                return false;
            }

            applicationProperties.Parameters.TryParseDatabricksPropertiesParameter(ProviderConstants.Databricks.EnableFedRampCertificationProperty, logger, out bool enableFedRampCertification);

            return enableFedRampCertification;
        }

        /// <summary>
        /// Checks whether workspace is FedRAMP Certified
        /// </summary>
        /// <param name="applianceProperties">The appliance properties</param>
        /// <param name="logger">The logger</param>
        public static bool IsAppliancePropertiesFedRamp(this ApplianceProperties applianceProperties, ICommonEventSource logger)
        {
            if (applianceProperties.Parameters == null)
            {
                return false;
            }

            applianceProperties.Parameters.TryParseDatabricksPropertiesParameter(ProviderConstants.Databricks.EnableFedRampCertificationProperty, logger, out bool enableFedRampCertification);

            return enableFedRampCertification;
        }

        /// <summary>
        /// Checks whether workspace is FedRAMP Certified
        /// </summary>
        /// <param name="applianceEntity">The appliance entity</param>
        /// <param name="logger">The logger</param>
        public static bool IsApplianceEntityFedRamp(this ApplianceEntity applianceEntity, ICommonEventSource logger)
        {
            if (applianceEntity.Properties == null)
            {
                return false;
            }

            return applianceEntity.Properties.IsApplicationPropertiesFedRamp(logger);
        }

        /// <summary>
        /// Gets no public IP property of the workspace.
        /// </summary>
        /// <param name="parameters">The parameters</param>
        /// <param name="logger">The logger</param>
        public static bool GetNoPublicIpOrDefault(this InsensitiveDictionary<JToken> parameters, ICommonEventSource logger)
        {
            bool isNpipEnabled = false;

            if (RequestCorrelationContext.Current.ApiVersion.IsGreaterThanOrEqualTo(ProviderConstants.ApiVersion20240501))
            {
                isNpipEnabled = true;
            }

            if (parameters != null && parameters.ContainsKey(ProviderConstants.Databricks.EnableNoPublicIpProperty))
            {
                parameters.TryParseDatabricksPropertiesParameter(ProviderConstants.Databricks.EnableNoPublicIpProperty, logger, out isNpipEnabled);
            }

            return isNpipEnabled;
        }

        /// <summary>
        /// Method for setting no public IP property of the workspace.
        /// </summary>
        /// <param name="applianceEntity">Appliance Entity</param>
        /// <param name="enableNoPublicIp">no public IP property</param>
        public static void SetNoPublicIp(this ApplianceEntity applianceEntity, bool enableNoPublicIp)
        {
            if (applianceEntity.Properties?.Parameters == null)
            {
                applianceEntity.Properties.Parameters = new InsensitiveDictionary<JToken>();
            }

            applianceEntity.Properties.Parameters.SetNoPublicIp(enableNoPublicIp);
        }

        /// <summary>
        /// Method for setting no public IP property of the workspace.
        /// </summary>
        /// <param name="parameters">Appliance Parameters</param>
        /// <param name="enableNoPublicIp">no public IP property</param>
        public static void SetNoPublicIp(this InsensitiveDictionary<JToken> parameters, bool enableNoPublicIp)
        {
            parameters[ProviderConstants.Databricks.EnableNoPublicIpProperty] = new JObject()
            {
                new JProperty(ProviderConstants.Storage.ParameterType, ProviderConstants.Storage.ParameterBoolType),
                new JProperty(ProviderConstants.Storage.ParameterValue, enableNoPublicIp)
            };
        }

        /// <summary>
        /// Method for setting VNET & Subnet properties of the workspace.
        /// </summary>
        /// <param name="incomingApplianceEntity">Appliance metadata</param>
        /// <param name="customVirtualNetworkId">Custom Virtual Network ID</param>
        /// <param name="customPrivateSubnetName">Custom Private Subnet</param>
        /// <param name="customPublicSubnetName">Custom Public Subnet</param>
        /// <param name="includeParameterTypeInJToken">Indicate whether to include type field in JToken. The default value is false </param>
        public static void SetVirtualNetworkPropertiesToAppliance(this ApplianceEntity incomingApplianceEntity,
            string customVirtualNetworkId,
            string customPrivateSubnetName,
            string customPublicSubnetName,
            bool includeParameterTypeInJToken = false)
        {
            if (string.IsNullOrWhiteSpace(customVirtualNetworkId) ||
                string.IsNullOrWhiteSpace(customPrivateSubnetName) ||
                string.IsNullOrWhiteSpace(customPublicSubnetName))
            {
                return;
            }

            if (incomingApplianceEntity.Properties.Parameters == null)
            {
                incomingApplianceEntity.Properties.Parameters = new InsensitiveDictionary<JToken>();
            }

            var customVirtualNetworkIdJObject = new JObject();
            var customPrivateSubnetNameJObject = new JObject();
            var customPublicSubnetNameJObject = new JObject();

            if (includeParameterTypeInJToken)
            {
                var typeJProperty = new JProperty(ProviderConstants.Storage.ParameterType, ProviderConstants.Storage.ParameterStringType);
                customVirtualNetworkIdJObject.Add(typeJProperty);
                customPrivateSubnetNameJObject.Add(typeJProperty);
                customPublicSubnetNameJObject.Add(typeJProperty);
            }

            customVirtualNetworkIdJObject.Add(new JProperty(ProviderConstants.Storage.ParameterValue, customVirtualNetworkId));
            incomingApplianceEntity.Properties.Parameters[ProviderConstants.Databricks.CustomVirtualNetworkIdProperty] =
                customVirtualNetworkIdJObject;

            customPrivateSubnetNameJObject.Add(new JProperty(ProviderConstants.Storage.ParameterValue, customPrivateSubnetName));
            incomingApplianceEntity.Properties.Parameters[ProviderConstants.Databricks.CustomPrivateSubnetNameProperty] =
                customPrivateSubnetNameJObject;

            customPublicSubnetNameJObject.Add(new JProperty(ProviderConstants.Storage.ParameterValue, customPublicSubnetName));
            incomingApplianceEntity.Properties.Parameters[ProviderConstants.Databricks.CustomPublicSubnetNameProperty] =
                customPublicSubnetNameJObject;
        }

        /// <summary>
        /// Set Network Properties to workspace object
        /// </summary>
        /// <param name="existingEntity">The existing entity.</param>
        /// <param name="incomingEntity">The incoming entity.</param>
        /// <param name="logger">The logger</param>
        public static void SetNetworkPropertiesToAppliance(this ApplianceEntity existingEntity, ApplianceEntity incomingEntity, ICommonEventSource logger)
        {
            var operationName = Utilities.GetAsyncMethodName();

            logger.Debug(operationName, $"PublicNetwork Access: '{incomingEntity.Properties.PublicNetworkAccess}'" +
                $"RequiredNsgRules: '{incomingEntity.Properties.RequiredNsgRules}'");

            var incomingEnableNoPublicIp = incomingEntity.Properties.Parameters.GetNoPublicIpOrDefault(logger);
            var incomingPublicNetworkAccess = incomingEntity.Properties.PublicNetworkAccess.GetPublicNetworkAccessDefaultValue();
            var incomingRequiredNsgRules = incomingEntity.Properties.RequiredNsgRules.GetRequiredNsgRulesMemberDefaultValue();

            existingEntity.SetNoPublicIp(incomingEnableNoPublicIp);
            existingEntity.Properties.PublicNetworkAccess = incomingPublicNetworkAccess;
            existingEntity.Properties.RequiredNsgRules = incomingRequiredNsgRules;

            var customVirtualNetworkId = incomingEntity.Properties.Parameters.GetCanonicalizedCustomVirtualNetworkIdProperty(logger, string.Empty);
            var customPublicSubnetName = incomingEntity.Properties.Parameters.GetCustomPublicSubnetNameProperty(logger, string.Empty);
            var customPrivateSubnetName = incomingEntity.Properties.Parameters.GetCustomPrivateSubnetNameProperty(logger, string.Empty);

            existingEntity.SetVirtualNetworkPropertiesToAppliance(customVirtualNetworkId, customPrivateSubnetName, customPublicSubnetName);
        }

        /// <summary>
        /// Set Encryption Properties to workspace object
        /// </summary>
        /// <param name="existingEntity">The existing entity.</param>
        /// <param name="incomingEntity">The incoming entity.</param>
        public static void SetEncryptionPropertiesToAppliance(this ApplianceEntity existingEntity, ApplianceEntity incomingEntity)
        {
            if (incomingEntity.Properties.EncryptionProperties != null)
            {
                existingEntity.Properties.EncryptionProperties = incomingEntity.Properties.EncryptionProperties;
            }

            if (incomingEntity.Properties.ManagedDiskIdentityEntity != null)
            {
                existingEntity.Properties.ManagedDiskIdentityEntity = incomingEntity.Properties.ManagedDiskIdentityEntity;
            }

            if (incomingEntity.Properties.DiskEncryptionSetId != null)
            {
                existingEntity.Properties.DiskEncryptionSetId = incomingEntity.Properties.DiskEncryptionSetId;
            }
        }

        /// <summary>
        /// Set DBFS Parameters to existing workspace object
        /// </summary>
        /// <param name="existingEntity">The existing entity.</param>
        /// <param name="incomingEntity">The incoming entity.</param>
        /// <param name="logger">The logger</param>
        public static void SetDbfsEncryptionPropertiesToAppliance(this ApplianceEntity existingEntity,
            ApplianceEntity incomingEntity,
            ICommonEventSource logger)
        {
            //TODO: Need to look in better way to identify modified object & update exiting entity
            if (incomingEntity.Properties.Parameters.CoalesceDictionary()
                .TryGetValue(ProviderConstants.Storage.PrepareEncryption,
                    out var prepareEncryption))
            {
                existingEntity.Properties.Parameters[ProviderConstants.Storage.PrepareEncryption] = prepareEncryption;
            }

            if (incomingEntity.Properties.Parameters.CoalesceDictionary()
                .TryGetValue(ProviderConstants.Storage.Encryption,
                    out var storageEncryption))
            {
                existingEntity.Properties.Parameters[ProviderConstants.Storage.Encryption] = storageEncryption;
            }

            if (incomingEntity.Properties.StorageAccountIdentityEntity != null)
            {
                existingEntity.Properties.StorageAccountIdentityEntity =
                    incomingEntity.Properties.StorageAccountIdentityEntity;
            }
        }

        /// <summary>
        /// Checks whether workspace is no public IP
        /// </summary>
        /// <param name="applicationProperties">The application properties</param>
        /// <param name="logger">The logger</param>
        private static bool IsApplicationPropertiesNpipEnabled(this ApplicationProperties applicationProperties, ICommonEventSource logger)
        {
            return applicationProperties.Parameters.GetNoPublicIpOrDefault(logger);
        }

        /// <summary>
        /// Checks whether workspace is No Public IP
        /// </summary>
        /// <param name="applianceEntity">The appliance entity</param>
        /// <param name="logger">The logger</param>
        public static bool IsApplianceEntityNpip(this ApplianceEntity applianceEntity, ICommonEventSource logger)
        {
            if (applianceEntity.Properties == null)
            {
                return false;
            }

            return applianceEntity.Properties.IsApplicationPropertiesNpipEnabled(logger);
        }

        /// <summary>
        /// Gets the API version based on the publisher package id.
        /// </summary>
        /// <param name="applianceEntity">The appliance Entity.</param>
        /// <param name="logger">The logger.</param>
        /// <returns>Returns the API version.</returns>
        public static string GetDatabricksBackendApiVersion(this ApplianceEntity applianceEntity, ICommonEventSource logger)
        {
            var apiVersion = ProviderConstants.ApiVersion20180401;
            var publisherPackageId = applianceEntity?.Properties?.PublisherPackageId;

            if (applianceEntity.IsApplianceEntityFedRamp(logger))
            {
                apiVersion = ProviderConstants.ApiVersion20200215;
            }
            else if (publisherPackageId.ContainsInsensitively(Utilities.DevPackageIdIdentifier))
            {
                apiVersion = ProviderConstants.ApiVersion20180301;
            }
            else if (publisherPackageId.ContainsInsensitively(Utilities.StagingPackageIdIdentifier))
            {
                apiVersion = ProviderConstants.ApiVersion20180315;
            }

            return apiVersion;
        }

        /// <summary>
        /// Gets the default Value
        /// </summary>
        /// <param name="publicNetworkAccessStatus">the public network access status</param>
        /// <returns>Returns the default PNA value</returns>
        public static PublicNetworkAccessStatus GetPublicNetworkAccessDefaultValue(this PublicNetworkAccessStatus? publicNetworkAccessStatus)
        {
            return publicNetworkAccessStatus ?? PublicNetworkAccessStatus.Enabled;
        }

        /// <summary>
        /// Gets the default Value
        /// </summary>
        /// <param name="requiredNsgRules">the required Network Security Group Rules Member</param>
        /// <returns>Returns the default Network Security Group Rules Member value</returns>
        public static RequiredNetworkSecurityGroupType GetRequiredNsgRulesMemberDefaultValue(this RequiredNetworkSecurityGroupType? requiredNsgRules)
        {
            return requiredNsgRules ?? RequiredNetworkSecurityGroupType.AllRules;
        }

        /// <summary>
        /// Gets the Public Network Access or default (Enabled).
        /// </summary>
        /// <param name="applianceEntity">The appliance Entity.</param>
        /// <returns>return public network access.</returns>
        public static PublicNetworkAccessStatus GetPublicNetworkAccessOrDefault(this ApplianceEntity applianceEntity)
        {
            if (applianceEntity.Properties == null)
            {
                return PublicNetworkAccessStatus.Enabled;
            }

            return applianceEntity.Properties.PublicNetworkAccess.GetPublicNetworkAccessDefaultValue();
        }

        /// <summary>
        /// Gets the Required Network Security Group rules or default (AllRules).
        /// </summary>
        /// <param name="applianceEntity">The appliance Entity.</param>
        /// <returns>return Required Network Security Group rules.</returns>
        public static RequiredNetworkSecurityGroupType GetRequiredNetworkSecurityGroupOrDefault(this ApplianceEntity applianceEntity)
        {
            if (applianceEntity.Properties == null)
            {
                return RequiredNetworkSecurityGroupType.AllRules;
            }

            return applianceEntity.Properties.RequiredNsgRules.GetRequiredNsgRulesMemberDefaultValue();
        }

        /// <summary>
        /// Gets custom vnet id property of the workspace.
        /// </summary>
        /// <param name="parameters">The parameters</param>
        /// <param name="logger">The logger</param>
        /// <param name="defaultValue">Default value to be returned when parameters is null</param>
        public static string GetCanonicalizedCustomVirtualNetworkIdProperty(this InsensitiveDictionary<JToken> parameters, ICommonEventSource logger, string defaultValue)
        {
            if (parameters == null)
            {
                return defaultValue;
            }

            parameters.TryParseDatabricksPropertiesParameter(ProviderConstants.Databricks.CustomVirtualNetworkIdProperty, logger, out string customVirtualNetworkId);

            // canonical format for vnet ID does not contain trailing slash
            return customVirtualNetworkId.TrimEnd(new[] { '/' });
        }

        public static void CanonicalizeCustomVirtualNetworkIdProperty(this InsensitiveDictionary<JToken> parameters, ICommonEventSource logger)
        {
            if (parameters.TryParseDatabricksPropertiesParameter(ProviderConstants.Databricks.CustomVirtualNetworkIdProperty, logger, out string customVirtualNetworkId))
            {
                parameters[ProviderConstants.Databricks.CustomVirtualNetworkIdProperty] = new JObject()
                    {
                        new JProperty(ProviderConstants.Storage.ParameterType, ProviderConstants.Storage.ParameterStringType),
                        new JProperty(ProviderConstants.Storage.ParameterValue, customVirtualNetworkId.TrimEnd(new[] { '/' }))
                    };
            }
        }

        /// <summary>
        /// Gets custom private subnet name property of the workspace.
        /// </summary>
        /// <param name="parameters">The parameters</param>
        /// <param name="logger">The logger</param>
        /// <param name="defaultValue">Default value to be returned when parameters is null</param>
        public static string GetCustomPrivateSubnetNameProperty(this InsensitiveDictionary<JToken> parameters, ICommonEventSource logger, string defaultValue)
        {
            if (parameters == null)
            {
                return defaultValue;
            }

            parameters.TryParseDatabricksPropertiesParameter(ProviderConstants.Databricks.CustomPrivateSubnetNameProperty, logger, out string customPrivateSubnetName);

            return customPrivateSubnetName;
        }

        /// <summary>
        /// Gets custom public subnet name property of the workspace.
        /// </summary>
        /// <param name="parameters">The parameters</param>
        /// <param name="logger">The logger</param>
        /// <param name="defaultValue">Default value to be returned when parameters is null</param>
        public static string GetCustomPublicSubnetNameProperty(this InsensitiveDictionary<JToken> parameters, ICommonEventSource logger, string defaultValue)
        {
            if (parameters == null)
            {
                return defaultValue;
            }

            parameters.TryParseDatabricksPropertiesParameter(ProviderConstants.Databricks.CustomPublicSubnetNameProperty, logger, out string customPublicSubnetName);

            return customPublicSubnetName;
        }

        /// <summary>
        /// Determines if one or more network properties are changed.
        /// </summary>
        /// <param name="incomingEntity">The incoming entity.</param>
        /// <param name="existingEntity">The existing entity.</param>
        /// <param name="logger">The logger</param>
        /// <returns>Return if the network properties have changed.</returns>
        public static bool IsNetworkPropertyChanged(this ApplianceEntity existingEntity, ApplianceEntity incomingEntity, ICommonEventSource logger)
        {
            var operationName = Utilities.GetAsyncMethodName();

            var existingCustomervNetId = existingEntity.Properties.Parameters.GetCanonicalizedCustomVirtualNetworkIdProperty(logger, string.Empty);
            var existingPublicSubnetName = existingEntity.Properties.Parameters.GetCustomPublicSubnetNameProperty(logger, string.Empty);
            var existingPrivateSubnetName = existingEntity.Properties.Parameters.GetCustomPrivateSubnetNameProperty(logger, string.Empty);
            var existingEnableNoPublicIp = existingEntity.Properties.Parameters.GetNoPublicIpOrDefault(logger);
            var existingPublicNetworkAccess = existingEntity.Properties.PublicNetworkAccess.GetPublicNetworkAccessDefaultValue();
            var existingRequiredNsgRules = existingEntity.Properties.RequiredNsgRules.GetRequiredNsgRulesMemberDefaultValue();

            var incomingCustomervNetId = incomingEntity.Properties.Parameters.GetCanonicalizedCustomVirtualNetworkIdProperty(logger, existingCustomervNetId);
            var incomingPublicSubnetName = incomingEntity.Properties.Parameters.GetCustomPublicSubnetNameProperty(logger, existingPublicSubnetName);
            var incomingPrivateSubnetName = incomingEntity.Properties.Parameters.GetCustomPrivateSubnetNameProperty(logger, existingPrivateSubnetName);
            var incomingEnableNoPublicIp = incomingEntity.Properties.Parameters.GetNoPublicIpOrDefault(logger);
            var incomingPublicNetworkAccess = incomingEntity.Properties.PublicNetworkAccess.GetPublicNetworkAccessDefaultValue();
            var incomingRequiredNsgRules = incomingEntity.Properties.RequiredNsgRules.GetRequiredNsgRulesMemberDefaultValue();

            logger.Debug(operationName,
                $"Network Properties." +
                $" Custom Virtual Network Id : {existingCustomervNetId} -> {incomingCustomervNetId} " +
                $" Custom Public Subnet Name : {existingPublicSubnetName} -> {incomingPublicSubnetName} " +
                $" Custom Private Subnet Name : {existingPrivateSubnetName} -> {incomingPrivateSubnetName} " +
                $" Enable No Public IP : {existingEnableNoPublicIp} -> {incomingEnableNoPublicIp}," +
                $" Public Network Access : {existingPublicNetworkAccess} -> {incomingPublicNetworkAccess}," +
                $" RequiredNsgRules : {existingRequiredNsgRules} -> {incomingRequiredNsgRules}");

            return !existingCustomervNetId.Equals(incomingCustomervNetId, StringComparison.OrdinalIgnoreCase) ||
                !existingPublicSubnetName.Equals(incomingPublicSubnetName, StringComparison.OrdinalIgnoreCase) ||
                !existingPrivateSubnetName.Equals(incomingPrivateSubnetName, StringComparison.OrdinalIgnoreCase) ||
                existingEnableNoPublicIp != incomingEnableNoPublicIp ||
                    existingPublicNetworkAccess != incomingPublicNetworkAccess ||
                    existingRequiredNsgRules != incomingRequiredNsgRules;
        }

        /// <summary>
        /// Set Properties from Provisioning Job Metadata Parameters
        /// Including properties, sku and tags
        /// </summary>
        /// <param name="entity"></param>
        /// <param name="metadata"></param>
        public static void SetPropertiesFromMetadataParameters(this ApplianceEntity entity, ApplianceProvisioningJobMetadata metadata)
        {
            if (metadata.Parameters.TryGetValue(ProviderConstants.WorkspaceUpdateParameters.Properties, out JToken jTokenProperties))
            {
                entity.Properties = JsonConvert.DeserializeObject<ApplicationProperties>(jTokenProperties.ToJson());
            }

            if (metadata.Parameters.TryGetValue(ProviderConstants.WorkspaceUpdateParameters.Sku, out JToken jTokenSku))
            {
                entity.Sku = jTokenSku;
            }

            if (metadata.Parameters.TryGetValue(ProviderConstants.WorkspaceUpdateParameters.Tags, out JToken jTokenTags))
            {
                entity.Tags = JsonConvert.DeserializeObject<InsensitiveDictionary<string>>(jTokenTags.ToJson());
            }
        }

        /// <summary>
        /// Copy Properties from existing Entity to incoming Entity.
        /// Including location, metadata, subscriptionId, resourceGroup and name
        /// </summary>
        /// <param name="applianceEntity"></param>
        public static void CopyPropertiesFrom(this ApplianceEntity incomingEntity, ApplianceEntity existingEntity)
        {
            incomingEntity.Location = existingEntity.Location;
            incomingEntity.Metadata = existingEntity.Metadata;
            incomingEntity.SubscriptionId = existingEntity.SubscriptionId;
            incomingEntity.ResourceGroup = existingEntity.ResourceGroup;
            incomingEntity.Name = existingEntity.Name;
        }

        /// <summary>
        /// This method is used to copy from incoming entity to existing entity for Databricks notification
        /// Includes sku, tags and parameters, DbfsEncryptionProperties, EncryptionProperties and NetworkProperties
        /// </summary>
        /// <param name="existingEntity"></param>
        /// <param name="incomingEntity"></param>
        public static void CopyFromIncomingEntityForDBNotification(this ApplianceEntity existingEntity, ApplianceEntity incomingEntity, ICommonEventSource logger)
        {
            var operationName = Utilities.GetAsyncMethodName();

            logger.Debug(operationName, $"Copy from incoming entity to appliance for db notification. Including sku, tags and properties.parameters, DbfsEncryptionProperties, EncryptionProperties and NetworkProperties.");

            if (incomingEntity.Sku != null)
            {
                existingEntity.Sku = incomingEntity.Sku;
            }

            if (incomingEntity.Tags != null)
            {
                existingEntity.Tags = incomingEntity.Tags;
            }

            if (incomingEntity.Properties.EnhancedSecurityCompliance != null)
            {
                existingEntity.Properties.EnhancedSecurityCompliance = incomingEntity.Properties.EnhancedSecurityCompliance;
            }

            existingEntity.SetDbfsEncryptionPropertiesToAppliance(incomingEntity, logger);
            existingEntity.SetEncryptionPropertiesToAppliance(incomingEntity);
            existingEntity.SetNetworkPropertiesToAppliance(incomingEntity, logger);

            if (incomingEntity.Properties.Parameters != null)
            {
                InsensitiveDictionary<JToken> parameters = incomingEntity.Properties.Parameters;
                Utilities.RemoveDeploymentParameters(parameters, logger);

                HashSet<string> incomingParameterKeys = new HashSet<string>(parameters.Keys, StringComparer.OrdinalIgnoreCase);

                foreach (var key in incomingParameterKeys)
                {
                    if (key.Equals(ProviderConstants.Storage.PrepareEncryption, StringComparison.OrdinalIgnoreCase) ||
                        key.Equals(ProviderConstants.Storage.Encryption, StringComparison.OrdinalIgnoreCase))
                    {
                        continue; //the two parameters are handled in SetDbfsEncryptionPropertiesToAppliance method
                    }

                    existingEntity.Properties.Parameters[key] = incomingEntity.Properties.Parameters[key];

                    logger.Debug(operationName, $"Copy parameter {key} from incoming entity to appliance for db notification." +
                                                $" Incoming Value: {incomingEntity.Properties.Parameters[key]}" +
                                                $" Existing Value: {existingEntity.Properties.Parameters[key]}");
                }
            }
        }

        /// <summary>
        /// Populate workspace parameters from json string and save to appliance entity
        /// </summary>
        /// <param name="existingEntity">existing appliance entity</param>
        /// <param name="entityJson">json format entity metadata</param>
        /// <param name="logger"></param>
        /// <returns></returns>
        /// <exception cref="ErrorResponseMessageException"></exception>
        public static void PopulateWorkspaceParameterFromJsonString(this ApplianceEntity existingEntity, string entityJson, ICommonEventSource logger)
        {
            var operationName = Utilities.GetAsyncMethodName();

            var applianceParameters = existingEntity.Properties?.Parameters;

            if (applianceParameters == null)
            {
                applianceParameters = new InsensitiveDictionary<JToken>();
            }

            if (string.IsNullOrWhiteSpace(entityJson))
            {
                throw new ErrorResponseMessageException(
                    HttpStatusCode.BadRequest,
                    ErrorResponseCode.WorkspaceEntityJsonCannotBeNull,
                    ErrorResponseMessages.WorkspaceEntityJsonCannotBeNull);
            }

            var entityProperties = JToken.Parse(entityJson)?["properties"]?.ToString();
            if (entityProperties == null)
            {
                throw new ErrorResponseMessageException(
                    HttpStatusCode.BadRequest,
                    ErrorResponseCode.WorkspaceEntityPropertyParseFailure,
                    ErrorResponseMessages.WorkspaceEntityPropertyParseFailure);
            }

            var incomingParameters = JsonConvert.DeserializeObject<ApplicationProperties>(entityProperties)?.Parameters;

            logger.Debug(operationName, $"Populate workspace parameters from incoming: '{incomingParameters}' to appliance '{applianceParameters}'");

            foreach (var parameter in incomingParameters)
            {
                var typeString = parameter.Value.TryGetProperty<string>("type");
                var value = parameter.Value.TryGetProperty<object>("value");

                if (string.IsNullOrEmpty(typeString) || value == null)
                {
                    throw new ErrorResponseMessageException(
                        HttpStatusCode.BadRequest,
                        ErrorResponseCode.WorkspaceEntityParameterInvalid,
                        ErrorResponseMessages.WorkspaceEntityParameterInvalid);
                }
                else
                {
                    logger.Debug(operationName, $"Validate Parameter '{parameter.Key}': Type: '{typeString}', Value: '{value.ToJson()}'");

                    switch (typeString.ToLower())
                    {
                        case "string" when !(value is string) || (value.ToString() == null):
                        case "bool" when !(value is bool):
                        case "object" when !(value is JObject):
                        case null:
                            throw new ErrorResponseMessageException(
                                HttpStatusCode.BadRequest,
                                ErrorResponseCode.WorkspaceEntityParameterInvalid,
                                ErrorResponseMessages.WorkspaceEntityParameterInvalid);
                        default:
                            break;
                    }

                    applianceParameters[parameter.Key] = new JObject()
                    {
                        new JProperty(ProviderConstants.Storage.ParameterType, typeString),
                        new JProperty(ProviderConstants.Storage.ParameterValue, value)
                    };
                }
            }

            existingEntity.Properties.Parameters = applianceParameters;
        }

        /// <summary>
        /// Sets incomingEntity's initialName to existingEntity's initialName if null
        /// </summary>
        /// <param name="incomingEntity">incoming appliance entity</param>
        /// <param name="existingEntity">existing appliance entity to take initialName from</param>
        public static void UpdateInitialName(this ApplianceEntity incomingEntity, ApplianceEntity existingEntity)
        {
            if (existingEntity.Properties.DefaultCatalog != null && incomingEntity.Properties.DefaultCatalog != null
                && incomingEntity.Properties.DefaultCatalog.InitialName == null)
            {
                incomingEntity.Properties.DefaultCatalog.InitialName = existingEntity.Properties.DefaultCatalog.InitialName;
            }
        }

        /// <summary>
        /// Sets the workspace update operation property in the entity
        /// </summary>
        /// <param name="incomingEntity">incoming workspace entity</param>
        /// <param name="isUpdateOperation">true/false based on update or create operation</param>
        public static void SetWorkspaceUpdateOperation(this ApplianceEntity incomingEntity, bool isUpdateOperation)
        {
            if (incomingEntity.Properties.Parameters == null)
            {
                incomingEntity.Properties.Parameters = new InsensitiveDictionary<JToken>();
            }

            incomingEntity.Properties.Parameters[ProviderConstants.Databricks.IsWorkspaceUpdateOperationProperty] = new JObject()
            {
                new JProperty(ProviderConstants.Storage.ParameterType, ProviderConstants.Storage.ParameterBoolType),
                new JProperty(ProviderConstants.Storage.ParameterValue, isUpdateOperation)
            };
        }

        /// <summary>
        /// Gets the default value for the ComputeMode property from the appliance entity.
        /// </summary>
        /// <param name="applianceEntity">The appliance entity.</param>
        /// <returns></returns>
        public static ComputeMode GetComputeMode(this ApplianceEntity applianceEntity)
        {
            return applianceEntity.Properties?.ComputeMode == ComputeMode.Serverless ? ComputeMode.Serverless : ComputeMode.Hybrid;
        }

        /// <summary>
        /// Update the notebook encryption properties in the existing entity with the incoming entity's properties.
        /// </summary>
        /// <param name="existingEntity">existing entity</param>
        /// <param name="incomingEntity">incoming entity</param>
        public static void UpdateNotebookEncryptionProperties(this ApplianceEntity existingEntity, ApplianceEntity incomingEntity)
        {
            if (incomingEntity.Properties.EncryptionProperties?.EncryptionEntities?.ManagedServices != null)
            {
                if (existingEntity.Properties.EncryptionProperties == null)
                {
                    existingEntity.Properties.EncryptionProperties = incomingEntity.Properties.EncryptionProperties;
                }
                else if (existingEntity.Properties.EncryptionProperties.EncryptionEntities == null)
                {
                    existingEntity.Properties.EncryptionProperties.EncryptionEntities = incomingEntity.Properties.EncryptionProperties.EncryptionEntities;
                }
                else
                {
                    existingEntity.Properties.EncryptionProperties.EncryptionEntities.ManagedServices = incomingEntity.Properties.EncryptionProperties.EncryptionEntities.ManagedServices;
                }
            }
        }

        /// <summary>
        /// Update the enhanced security compliance properties in the existing entity with the incoming entity's properties.
        /// </summary>
        /// <param name="existingEntity">existing entity</param>
        /// <param name="incomingEntity">incoming entity</param>
        public static void UpdateEnhancedSecurityComplianceProperties(this ApplianceEntity existingEntity, ApplianceEntity incomingEntity)
        {
            if (incomingEntity.Properties.EnhancedSecurityCompliance != null)
            {
                existingEntity.Properties.EnhancedSecurityCompliance = incomingEntity.Properties.EnhancedSecurityCompliance;
            }
        }

        /// <summary>
        /// Set the incomingEntity.EnableNoPublicIp to existingEntity.EnableNoPublicIp
        /// during the UPDATE if it is not provided or is absent (since older workspaces before 2020 may not have this property).
        /// </summary>
        /// <param name="incomingEntity">incoming appliance entity</param>
        /// <param name="existingEntity">existing appliance entity to take enableNoPublicIp from</param>
        public static void SetWorkspaceNoPublicIp(this ApplianceEntity incomingEntity, ApplianceEntity existingEntity, ICommonEventSource logger)
        {
            var operationName = Utilities.GetAsyncMethodName();
            bool noPublicpIp;

            if (existingEntity == null)
            {
                noPublicpIp = incomingEntity.Properties.Parameters.GetNoPublicIpOrDefault(logger);
                logger.Debug(operationName, $"SetWorkspace Enable No Public IP : '{noPublicpIp}' from incoming entity for a new workspace");
            }
            else
            {
                if (incomingEntity.Properties.Parameters?.ContainsKey(ProviderConstants.Databricks.EnableNoPublicIpProperty) == true)
                {
                    noPublicpIp = incomingEntity.Properties.Parameters.GetNoPublicIpOrDefault(logger);
                    logger.Debug(operationName, $"SetWorkspace Enable No Public IP : '{noPublicpIp}' from incoming entity for an exsiting workspace");
                }
                else if (existingEntity.Properties.Parameters?.ContainsKey(ProviderConstants.Databricks.EnableNoPublicIpProperty) == true)
                {
                    noPublicpIp = existingEntity.Properties.Parameters.GetNoPublicIpOrDefault(logger);
                    logger.Debug(operationName, $"SetWorkspace Enable No Public IP : '{noPublicpIp}' from existing entity's Enable No Public IP value as the incoming entity missing this parameter");
                }
                else
                {
                    noPublicpIp = false;
                    logger.Debug(operationName, $"SetWorkspace Enable No Public IP : '{noPublicpIp}' since older workspaces before 2020 API Version may not have this property");
                }
            }

            incomingEntity.SetNoPublicIp(noPublicpIp);
        }
    }
}