﻿//------------------------------------------------------------
// Copyright (c) Microsoft Corporation.  All rights reserved.
//------------------------------------------------------------

namespace Microsoft.WindowsAzure.ResourceStack.Providers.Feature.Features.BaseFeature
{
    using System;
    using System.Linq;
    using System.Net;
    using Microsoft.WindowsAzure.ResourceStack.Common.EventSources;
    using Microsoft.WindowsAzure.ResourceStack.Common.EventSources.Tracing;
    using Microsoft.WindowsAzure.ResourceStack.Common.Extensions;
    using Microsoft.WindowsAzure.ResourceStack.Common.Instrumentation;
    using Microsoft.WindowsAzure.ResourceStack.Common.Json;
    using Microsoft.WindowsAzure.ResourceStack.Common.Utilities;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Application.Data.Definitions;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Application.Data.Entities;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Common.Constants;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Common.Data;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Common.ErrorResponses;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Common.Logging;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Common.Utilities;

    /// <summary>
    /// Methods to be added to BaseWorkspace class via extensions.
    /// </summary>
    public static class BaseWorkspaceExtensions
    {
        /// <summary>
        /// Gets the fully qualified resource identifier.
        /// </summary>
        /// <param name="baseAppliance">The base workspace object</param>
        /// <param name="subscriptionId">The subscription identifier.</param>
        /// <param name="resourceGroup">The resource group.</param>
        /// <param name="type">The type.</param>
        /// <param name="fullyQualifiedName">Name of the fully qualified.</param>
        public static string GetFullyQualifiedResourceId(
            this BaseWorkspace baseAppliance,
            string subscriptionId,
            string resourceGroup,
            string type,
            string fullyQualifiedName)
        {
            if (!string.IsNullOrEmpty(resourceGroup))
            {
                return string.Format(
                    format: "/subscriptions/{0}/resourceGroups/{1}/providers/{2}",
                    arg0: subscriptionId,
                    arg1: resourceGroup,
                    arg2: baseAppliance.GetResourceId(type, fullyQualifiedName));
            }
            else
            {
                return string.Format(
                    format: "/subscriptions/{0}/providers/{1}",
                    arg0: subscriptionId,
                    arg1: baseAppliance.GetResourceId(type, fullyQualifiedName));
            }
        }

        /// <summary>
        /// Gets the resource id
        /// </summary>
        /// <param name="baseWorkspace">The base workspace object</param>
        /// <param name="fullyQualifiedResourceType">The fully qualified resource type</param>
        /// <param name="resourceName">The resource name</param>
        /// <returns>The resource id</returns>
        public static string GetResourceId(this BaseWorkspace baseWorkspace, string fullyQualifiedResourceType, string resourceName)
        {
            var resourceProviderNamespace = HttpUtility.GetPathSegments(fullyQualifiedResourceType).First();
            var nestedResourceTypes = HttpUtility.GetPathSegments(fullyQualifiedResourceType).Skip(1);
            var nestedResourceNames = HttpUtility.GetPathSegments(resourceName);
            var nestedResources = nestedResourceTypes.Zip(nestedResourceNames, (type, name) => string.Format("{0}/{1}", type, name));

            return string.Format("{0}/{1}", resourceProviderNamespace, nestedResources.ConcatStrings("/"));
        }

        /// <summary>
        /// Validates the application SKU.
        /// </summary>
        /// <param name="baseWorkspace">The base workspace object.</param>
        public static void ValidateSku(this BaseWorkspace baseWorkspace)
        {
            var operationName = Utilities.GetAsyncMethodName();
            ProvidersLog.Current.Debug(operationName, $"Validating Application SKU Requirement.");

            var applicationSku = baseWorkspace.Sku;
            var apiVersion = RequestCorrelationContext.Current.ApiVersion;

            if (ProviderConstants.ApiVersion20250601Preview.IsGreaterThanOrEqualTo(
                apiVersion, StringComparison.OrdinalIgnoreCase) && applicationSku == null)
            {
                throw new ErrorResponseMessageException(
                    httpStatus: HttpStatusCode.BadRequest,
                    errorCode: ErrorResponseCode.InvalidApplicationSku,
                    errorMessage: ErrorResponseMessages.InvalidApplicationSku.ToLocalizedMessage());
            }

            if (applicationSku != null)
            {
                ProvidersLog.Current.Debug(operationName, $"Application SKU:{applicationSku}");

                var databricksSku = applicationSku.TryFromJToken<DatabricksSku>();
                if (databricksSku == null || databricksSku.Name == DatabricksSkuName.NotSpecified)
                {
                    var allowedDatabricksSkuNames = string.Join(
                        separator: ", ",
                        values: Enum
                            .GetValues(typeof(DatabricksSkuName))
                            .OfType<DatabricksSkuName>()
                            .Except(new[] { DatabricksSkuName.NotSpecified })
                            .Select(value => string.Format("'{0}'", value.ToString())));

                    throw new ErrorResponseMessageException(
                        httpStatus: HttpStatusCode.BadRequest,
                        errorCode: ErrorResponseCode.InvalidDatabricksSku,
                        errorMessage: ErrorResponseMessages.InvalidDatabricksSku.ToLocalizedMessage(allowedDatabricksSkuNames));
                }
            }
        }

        /// <summary>
        /// Constructs an appliance entity object from base workspace object's fields.
        /// </summary>
        /// <param name="baseWorkspace">The base workspace object.</param>
        /// <returns>An appliance entity object</returns>
        public static ApplianceEntity GetApplianceEntity(this BaseWorkspace baseWorkspace)
        {
            var applianceEntity = new ApplianceEntity()
            {
                SubscriptionId = baseWorkspace.SubscriptionId,
                ResourceGroup = baseWorkspace.ResourceGroup,
                Name = baseWorkspace.Name,
                Tags = baseWorkspace.Tags,
                Location = baseWorkspace.Location,
                Sku = baseWorkspace.Sku,
                Properties = new ApplicationProperties(),
                SystemData = baseWorkspace.SystemData
            };

            return applianceEntity;
        }
    }
}
