﻿//------------------------------------------------------------
// Copyright (c) Microsoft Corporation.  All rights reserved.
//------------------------------------------------------------

namespace Microsoft.WindowsAzure.ResourceStack.Providers.Feature.Features.ServerlessWorkspaceFeature
{
    using System.Net;
    using Microsoft.WindowsAzure.ResourceStack.Common.EventSources;
    using Microsoft.WindowsAzure.ResourceStack.Common.Extensions;
    using Microsoft.WindowsAzure.ResourceStack.Common.Instrumentation;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Application.Data.Entities;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Application.Data.Extensions;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Common.Constants;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Common.Definitions;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Common.ErrorResponses;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Common.Logging;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Common.Utilities;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Feature.Controllers.ControllerV11;

    /// <summary>
    /// Validation and other helper methods for compute mode.
    /// </summary>
    public static class ComputeModeExtensions
    {
        /// <summary>
        /// Validates that the Compute Mode is specified and valid for the workspace properties with correct MRG configuration.
        /// </summary>
        /// <param name="properties">The workspace properties v11.</param>
        /// <exception cref="ErrorResponseMessageException">The Error Response Message Exception.</exception>
        public static void ValidateMrgForComputeMode(this WorkspacePropertiesV11 properties)
        {
            var operationName = Utilities.GetAsyncMethodName();
            ProvidersLog.Current.Debug(operationName, $"Validating Compute Mode.");

            if (properties.ComputeMode == ComputeMode.Hybrid &&
                string.IsNullOrWhiteSpace(properties.ManagedResourceGroupId))
            {
                ProvidersLog.Current.Debug(operationName, $"Hybrid Workspace properties Must have managed resource group id.");

                throw new ErrorResponseMessageException(
                    httpStatus: HttpStatusCode.BadRequest,
                    errorCode: ErrorResponseCode.InvalidApplicationPropertiesForPut,
                    errorMessage: ErrorResponseMessages.ApplicationPropertiesNull.ToLocalizedMessage("ManagedResourceGroupId"));
            }

            if (properties.ComputeMode == ComputeMode.Serverless &&
                !string.IsNullOrWhiteSpace(properties.ManagedResourceGroupId))
            {
                ProvidersLog.Current.Debug(operationName, $"Serverless Workspace properties should NOT have managed resource group id: {properties.ManagedResourceGroupId}.");

                throw new ErrorResponseMessageException(
                    httpStatus: HttpStatusCode.BadRequest,
                    errorCode: ErrorResponseCode.FeatureNotSupportedForServerlessWorkspace,
                    errorMessage: ErrorResponseMessages.FeatureNotSupportedForServerlessWorkspace.ToLocalizedMessage("ManagedResourceGroupId"));
            }
        }

        /// <summary>
        /// Sets serverless workspace specific properties to the workspace's properties object
        /// </summary>
        /// <param name="computeModeProperties">The ComputeMode Properties</param>
        /// <param name="applianceEntity">The appliance Entity</param>
        public static void SetComputeModeFieldFromWorkspaceToEntity(
            this IComputeModeProperties computeModeProperties,
            ApplianceEntity applianceEntity)
        {
            var operationName = Utilities.GetAsyncMethodName();
            ProvidersLog.Current.Debug(operationName, $"Setting serverless workspace properties to workspace entity from workspace.");

            if (computeModeProperties == null)
            {
                return;
            }

            applianceEntity.Properties.ComputeMode = computeModeProperties.ComputeMode;
        }

        /// <summary>
        /// Sets serverless workspace specific properties to the workspace's properties object
        /// </summary>
        /// <param name="properties">The ComputeMode Properties.</param>
        /// <param name="applianceEntity">The appliance entity.</param>
        public static void SetComputeModeFieldFromEntityToWorkspace(
            this IComputeModeProperties properties,
            ApplianceEntity applianceEntity)
        {
            var operationName = Utilities.GetAsyncMethodName();
            ProvidersLog.Current.Debug(operationName, $"Setting Compute Mode properties to workspace from workspace entity.");

            if (applianceEntity.Properties == null)
            {
                return;
            }

            var apiVersion = RequestCorrelationContext.Current.ApiVersion;

            if (apiVersion.IsGreaterThanOrEqualTo(ProviderConstants.ApiVersion20250601Preview))
            {
                ProvidersLog.Current.Debug(operationName, $"Setting ComputeMode based on Api Version {apiVersion} as {applianceEntity.Properties.ComputeMode}.");

                properties.ComputeMode = applianceEntity.GetComputeMode();

                ProvidersLog.Current.Debug(operationName, $"ComputeMode is set to {properties.ComputeMode}.");
            }
        }
    }
}
