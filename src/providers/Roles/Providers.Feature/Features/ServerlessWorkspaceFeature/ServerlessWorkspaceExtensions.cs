﻿//------------------------------------------------------------
// Copyright (c) Microsoft Corporation.  All rights reserved.
//------------------------------------------------------------

namespace Microsoft.WindowsAzure.ResourceStack.Providers.Feature.Features.ServerlessWorkspaceFeature
{
    using System.Net;
    using Microsoft.WindowsAzure.ResourceStack.Common.EventSources;
    using Microsoft.WindowsAzure.ResourceStack.Common.Instrumentation;
    using Microsoft.WindowsAzure.ResourceStack.Common.Json;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Common.Data;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Common.ErrorResponses;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Common.Logging;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Common.Utilities;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Feature.Controllers.ControllerV11;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Feature.Features.AccessConnectorIdentity;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Feature.Features.BaseFeature;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Feature.Features.DefaultCatalog;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Feature.Features.DefaultStorageFirewall;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Feature.Features.ManagedDiskEncryptionFeature;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Feature.Features.PrivateLinkFeature;
    using Newtonsoft.Json.Linq;

    public static class ServerlessWorkspaceExtensions
    {
        public static void ValidateServerlessNotSupportedFeatures(this WorkspacePropertiesV11 properties, JToken sku)
        {
            var operationName = Utilities.GetAsyncMethodName();
            ProvidersLog.Current.Debug(operationName, $"Validating Serverless Workspace Not Supported Features.");

            if (sku.TryFromJToken<DatabricksSku>().Name != DatabricksSkuName.Premium)
            {
                throw new ErrorResponseMessageException(
                    HttpStatusCode.BadRequest,
                    ErrorResponseCode.InvalidSkuForServerlessWorkspace,
                    ErrorResponseMessages.InvalidSkuForServerlessWorkspace.ToLocalizedMessage());
            }

            properties.ValidateStorageAccountIdentity();
            properties.ValidateManagedDiskIdentity();
            properties.ValidateServerlessParameters();
            properties.ValidateServerlessManagedDiskEncruption();
            properties.ValidateServerlessAccessConnector();
            properties.ValidateServerlessDefaultCatalog();
            properties.ValidateServerlessPrivateLink();
            properties.ValidateServerlessDefaultStorageFirewall();
        }

        /// <summary>
        /// Validates that the Managed Disk Identity is not set for Serverless Workspaces.
        /// </summary>
        /// <param name="managedDiskEncryptionProperties">The managed disk encryption properties</param>
        /// <exception cref="ErrorResponseMessageException"></exception>
        public static void ValidateManagedDiskIdentity(this IManagedDiskEncryptionProperties managedDiskEncryptionProperties)
        {
            var operationName = Utilities.GetAsyncMethodName();
            ProvidersLog.Current.Debug(operationName, $"Validating NPI Properties of the serverless Request.");

            if (managedDiskEncryptionProperties == null)
            {
                return;
            }

            if (!string.IsNullOrEmpty(managedDiskEncryptionProperties.DiskEncryptionSetId))
            {
                throw new ErrorResponseMessageException(
                    HttpStatusCode.BadRequest,
                    ErrorResponseCode.FeatureNotSupportedForServerlessWorkspace,
                    ErrorResponseMessages.FeatureNotSupportedForServerlessWorkspace.ToLocalizedMessage("DiskEncryptionSetId"));
            }

            if (managedDiskEncryptionProperties.ManagedDiskIdentity != null)
            {
                throw new ErrorResponseMessageException(
                    HttpStatusCode.BadRequest,
                    ErrorResponseCode.FeatureNotSupportedForServerlessWorkspace,
                    ErrorResponseMessages.FeatureNotSupportedForServerlessWorkspace.ToLocalizedMessage("ManagedDiskIdentity"));
            }
        }

        /// <summary>
        /// Validates that the Storage Account Identity is not set for Serverless Workspaces.
        /// </summary>
        /// <param name="properties">The base workspace properties</param>
        /// <exception cref="ErrorResponseMessageException"></exception>
        private static void ValidateStorageAccountIdentity(this BaseWorkspaceProperties properties)
        {
            var operationName = Utilities.GetAsyncMethodName();
            ProvidersLog.Current.Debug(operationName, $"Validating NPI Properties of the serverless Request.");

            if (properties.StorageAccountIdentity != null)
            {
                throw new ErrorResponseMessageException(
                    HttpStatusCode.BadRequest,
                    ErrorResponseCode.FeatureNotSupportedForServerlessWorkspace,
                    ErrorResponseMessages.FeatureNotSupportedForServerlessWorkspace.ToLocalizedMessage("StorageAccountIdentity"));
            }
        }

        /// <summary>
        /// Validates that the workspace properties do not contain parameters for Serverless Workspaces.
        /// </summary>
        /// <param name="properties">the workspace properties v11</param>
        /// <exception cref="ErrorResponseMessageException"></exception>
        private static void ValidateServerlessParameters(this WorkspacePropertiesV11 properties)
        {
            var operationName = Utilities.GetAsyncMethodName();
            ProvidersLog.Current.Debug(operationName, $"Validating Parameters of the serverless Request.");

            if (properties.Parameters != null)
            {
                throw new ErrorResponseMessageException(
                    HttpStatusCode.BadRequest,
                    ErrorResponseCode.FeatureNotSupportedForServerlessWorkspace,
                    ErrorResponseMessages.FeatureNotSupportedForServerlessWorkspace.ToLocalizedMessage("Parameters"));
            }
        }

        /// <summary>
        /// Validates that the Managed Disk Encryption settings are not set for Serverless Workspaces.
        /// </summary>
        /// <param name="properties">The managed disk encryption peroperties</param>
        /// <exception cref="ErrorResponseMessageException"></exception>
        private static void ValidateServerlessManagedDiskEncruption(this IManagedDiskEncryptionProperties properties)
        {
            var operationName = Utilities.GetAsyncMethodName();
            ProvidersLog.Current.Debug(operationName, $"Validating Managed Disk Encryption Settings of the serverless Request.");

            var managedDiskEncryptionSettings = properties.Encryption?.Entities?.ManagedDisk;
            if (managedDiskEncryptionSettings != null)
            {
                throw new ErrorResponseMessageException(
                    HttpStatusCode.BadRequest,
                    ErrorResponseCode.FeatureNotSupportedForServerlessWorkspace,
                    ErrorResponseMessages.FeatureNotSupportedForServerlessWorkspace.ToLocalizedMessage("ManagedDiskEncryption"));
            }
        }

        /// <summary>
        /// Validates that the Access Connector is not set for Serverless Workspaces.
        /// </summary>
        /// <param name="properties">The Access Connector Properties</param>
        /// <exception cref="ErrorResponseMessageException"></exception>
        private static void ValidateServerlessAccessConnector(this IAccessConnectorProperties properties)
        {
            var operationName = Utilities.GetAsyncMethodName();
            ProvidersLog.Current.Debug(operationName, $"Validating Access Connector Settings of the serverless Request.");

            if (properties.AccessConnector != null)
            {
                throw new ErrorResponseMessageException(
                    HttpStatusCode.BadRequest,
                    ErrorResponseCode.FeatureNotSupportedForServerlessWorkspace,
                    ErrorResponseMessages.FeatureNotSupportedForServerlessWorkspace.ToLocalizedMessage($"accessConnector"));
            }
        }

        /// <summary>
        /// Validates that the Default Catalog is not set for Serverless Workspaces.
        /// </summary>
        /// <param name="properties">The Default Catalog Properties</param>
        /// <exception cref="ErrorResponseMessageException"></exception>
        private static void ValidateServerlessDefaultCatalog(this IDefaultCatalogProperties properties)
        {
            var operationName = Utilities.GetAsyncMethodName();
            ProvidersLog.Current.Debug(operationName, $"Validating Default Catalog Settings of the Request.");

            if (properties.DefaultCatalog != null || properties.IsUcEnabled != null)
            {
                throw new ErrorResponseMessageException(
                    HttpStatusCode.BadRequest,
                    ErrorResponseCode.FeatureNotSupportedForServerlessWorkspace,
                    ErrorResponseMessages.FeatureNotSupportedForServerlessWorkspace.ToLocalizedMessage("defaultCatalog"));
            }
        }

        /// <summary>
        /// Validates that the Private Link Settings are not set for Serverless Workspaces.
        /// </summary>
        /// <param name="properties">The private link properties</param>
        /// <exception cref="ErrorResponseMessageException"></exception>
        private static void ValidateServerlessPrivateLink(this IPrivateLinkProperties properties)
        {
            var operationName = Utilities.GetAsyncMethodName();
            ProvidersLog.Current.Debug(operationName, $"Validating Private Link Settings of the serverless Request.");

            if (properties.PrivateLinkAssets != null)
            {
                throw new ErrorResponseMessageException(
                    HttpStatusCode.BadRequest,
                    ErrorResponseCode.FeatureNotSupportedForServerlessWorkspace,
                    ErrorResponseMessages.FeatureNotSupportedForServerlessWorkspace.ToLocalizedMessage("PrivateLinkAssets"));
            }

            if (properties.PrivateEndpointConnections != null)
            {
                throw new ErrorResponseMessageException(
                    HttpStatusCode.BadRequest,
                    ErrorResponseCode.FeatureNotSupportedForServerlessWorkspace,
                    ErrorResponseMessages.FeatureNotSupportedForServerlessWorkspace.ToLocalizedMessage("PrivateEndpointConnections"));
            }

            if (properties.PublicNetworkAccess != null)
            {
                throw new ErrorResponseMessageException(
                    HttpStatusCode.BadRequest,
                    ErrorResponseCode.FeatureNotSupportedForServerlessWorkspace,
                    ErrorResponseMessages.FeatureNotSupportedForServerlessWorkspace.ToLocalizedMessage("PublicNetworkAccess"));
            }

            if (properties.RequiredNsgRules != null)
            {
                throw new ErrorResponseMessageException(
                    HttpStatusCode.BadRequest,
                    ErrorResponseCode.FeatureNotSupportedForServerlessWorkspace,
                    ErrorResponseMessages.FeatureNotSupportedForServerlessWorkspace.ToLocalizedMessage("RequiredNsgRules"));
            }
        }

        /// <summary>
        /// Validates that the Default Storage Firewall Settings are not set for Serverless Workspaces.
        /// </summary>
        /// <param name="properties">The default storage firewall properties</param>
        /// <exception cref="ErrorResponseMessageException"></exception>
        private static void ValidateServerlessDefaultStorageFirewall(this IDefaultStorageFirewallProperties properties)
        {
            var operationName = Utilities.GetAsyncMethodName();
            ProvidersLog.Current.Debug(operationName, $"Validating Default Storage Firewall Settings of the serverless Request.");

            if (properties.DefaultStorageFirewall != null)
            {
                throw new ErrorResponseMessageException(
                    HttpStatusCode.BadRequest,
                    ErrorResponseCode.FeatureNotSupportedForServerlessWorkspace,
                    ErrorResponseMessages.FeatureNotSupportedForServerlessWorkspace.ToLocalizedMessage("defaultStorageFirewall"));
            }
        }
    }
}
