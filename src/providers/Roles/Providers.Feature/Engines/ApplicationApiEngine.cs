//------------------------------------------------------------
// Copyright (c) Microsoft Corporation.  All rights reserved.
//------------------------------------------------------------

namespace Microsoft.WindowsAzure.ResourceStack.Providers.Feature.Engines
{
    using Microsoft.Azure.KeyVault.Utility;
    using Microsoft.WindowsAzure.ResourceStack.Common.Algorithms;
    using Microsoft.WindowsAzure.ResourceStack.Common.BackgroundJobs;
    using Microsoft.WindowsAzure.ResourceStack.Common.Collections;
    using Microsoft.WindowsAzure.ResourceStack.Common.EventSources;
    using Microsoft.WindowsAzure.ResourceStack.Common.Extensions;
    using Microsoft.WindowsAzure.ResourceStack.Common.Instrumentation;
    using Microsoft.WindowsAzure.ResourceStack.Common.Json;
    using Microsoft.WindowsAzure.ResourceStack.Common.Services;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Application.Data.Components;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Application.Data.DataProviders;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Application.Data.Definitions;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Application.Data.Definitions.DBAsyncContract;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Application.Data.Definitions.Enums;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Application.Data.Definitions.Extensions;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Application.Data.Entities;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Application.Data.Extensions;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Application.Data.Metadata;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Application.Services;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Common.Components;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Common.Constants;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Common.Data;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Common.Definitions;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Common.Definitions.ManagedIdentity;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Common.Engines;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Common.ErrorResponses;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Common.Extensions;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Common.Metadata;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Common.Responses;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Common.Utilities;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Feature.Components;
    using Microsoft.WindowsAzure.Storage;
    using Newtonsoft.Json.Linq;
    using System;
    using System.Collections.Generic;
    using System.Linq;
    using System.Net;
    using System.Net.Http;
    using System.Threading.Tasks;

    /// <summary>
    /// The application engine.
    /// </summary>
    public partial class ApplicationApiEngine
    {
        #region Properties and constructors

        /// <summary>
        /// Gets a value indicating whether validation for not actions should be disabled. By default it is enabled.
        /// </summary>
        private static bool DisableValidateNotActions => CloudConfigurationManager.GetConfigurationBoolean("DisableValidateNotActions", false);

        /// <summary>
        /// Gets a value indicating whether private link minimum API version.
        /// </summary>
        private static string PrivateLinkMinimumApiVersion =>
            CloudConfigurationManager.GetConfiguration(
                ProviderConstants.Databricks.PrivateLinkMinApiVersionKey,
                ProviderConstants.ApiVersion20200215);

        /// <summary>
        /// Gets or sets the provider configuration.
        /// </summary>
        private IApplicationProviderConfiguration ProviderConfiguration { get; set; }

        /// <summary>
        /// accessConnector provider configuration.
        /// </summary>
        private IAccessConnectorProviderConfiguration AccessConnectorProviderConfiguration { get; }

        /// <summary>
        /// Gets or sets the appliance resource type write operation name.
        /// </summary>
        private string ApplianceWriteOperationName { get; set; }

        /// <summary>
        /// Gets or sets the appliance resource type delete operation name.
        /// </summary>
        private string ApplianceDeleteOperationName { get; set; }

        /// <summary>
        /// Gets or sets the resource provider name space.
        /// </summary>
        private string ResourceProviderNamespace { get; set; }

        /// <summary>
        /// Gets or sets the resource type.
        /// </summary>
        private string ResourceType { get; set; }

        /// <summary>
        /// Gets the event service logger.
        /// </summary>
        private ICommonEventSource Logger => this.ProviderConfiguration.EventSource;

        /// <summary>
        /// Gets or sets front door client.
        /// </summary>
        private IFrontdoorEngine FrontdoorEngine { get; set; }

        /// <summary>
        /// Gets the market place appliance package data provider.
        /// </summary>
        private IApplicationPackageDataProvider GetMarketPlaceAppliancePackageDataProvider()
        {
            return this.ProviderConfiguration.AppliancePackageDataProvider;
        }

        /// <summary>
        /// Gets the appliance data provider.
        /// </summary>
        private IApplianceDataProvider GetApplianceDataProvider()
        {
            return this.ProviderConfiguration.ApplianceDataProvider;
        }

        /// <summary>
        /// Gets the accessConnector entity data provider.
        /// </summary>
        private IAccessConnectorDataProvider GetAccessConnectorDataProvider()
        {
            return this.AccessConnectorProviderConfiguration.AccessConnectorDataProvider;
        }

        /// <summary>
        /// Gets the jobs data provider.
        /// </summary>
        internal IJobsDataProvider GetJobsDataProvider()
        {
            return this.ProviderConfiguration.JobsDataProvider;
        }

        /// <summary>
        /// Gets the secondary jobs data provider.
        /// </summary>
        private IJobsDataProvider GetSecondaryJobsDataProvider()
        {
            return this.ProviderConfiguration.SecondaryJobsDataProvider;
        }

        private readonly IWorkspaceTagService workspaceTagService;

        /// <summary>
        /// Initializes a new instance of the <see cref="ApplicationApiEngine" /> class.
        /// </summary>
        /// <param name="providerConfiguration">The appliance provider configuration.</param>
        /// <param name="accessConnectorProviderConfiguration">The access connector provider configuration.</param>
        /// <param name="frontdoorEngine">An instance of FrontDoorEngine class.</param>
        public ApplicationApiEngine(
            IApplicationProviderConfiguration providerConfiguration,
            IAccessConnectorProviderConfiguration accessConnectorProviderConfiguration,
            IFrontdoorEngine frontdoorEngine)
        {
            this.ProviderConfiguration = providerConfiguration;
            this.AccessConnectorProviderConfiguration = accessConnectorProviderConfiguration;
            this.FrontdoorEngine = frontdoorEngine;

            this.ApplianceWriteOperationName = ProviderConstants.Databricks.DatabricksWriteOperationName;
            this.ApplianceDeleteOperationName = ProviderConstants.Databricks.DatabricksDeleteOperationName;
            this.ResourceProviderNamespace = ProviderConstants.Databricks.ResourceProviderNamespace;
            this.ResourceType = ProviderConstants.Databricks.WorkspacesResourceType;
            this.workspaceTagService = new WorkspaceTagService(frontdoorEngine, Logger);
        }

        #endregion

        #region PUT application

        /// <summary>
        /// Puts the application
        /// </summary>
        /// <param name="incomingEntity">The incoming appliance entity object</param>
        /// <param name="requestProcessingStartDateTime"> the processing start date time of the request</param>
        /// <param name="signedOboToken">The signed Obo Token.</param>
        /// <param name="isAsync">The Async Update is enabled.</param>
        /// <returns> Appliance entity</returns>
        public async Task<Tuple<ApplianceEntity, string, HttpStatusCode>> PutApplication(
            ApplianceEntity incomingEntity,
            DateTime requestProcessingStartDateTime,
            string signedOboToken,
            bool isAsync = false)
        {
            var operationName = this.GetOperationName(Utilities.GetAsyncMethodName());
            this.Logger.Debug(operationName, $"PutApplication called with incoming compute mode: {incomingEntity.GetComputeMode()}");

            await this.ValidateServerlessWorkspaceFeatureEnabled(incomingEntity).ConfigureAwait(false);

            if (incomingEntity.GetComputeMode() == ComputeMode.Serverless)
            {
                var returnValue = await this.PutServerlessApplication(
                    incomingEntity,
                    requestProcessingStartDateTime)
                    .ConfigureAwait(continueOnCapturedContext: false);

                return returnValue;
            }

            string customerTenantId = RequestCorrelationContext.Current.GetHomeTenantId();

            var resourceId = incomingEntity.GetFullyQualifiedResourceId();
            var resourceProviderNamespace = ResourceIdentifierExtensions.GetResourceProviderNamespace(resourceId);

            Validation.ApplicationDBFSEncryptionMinimumSkuRequirement(incomingEntity, this.Logger);
            Validation.ApplicationDBFSAllowedSkuName(incomingEntity, this.Logger);
            Validation.ApplicationNotebookEncryptionMinimumSkuRequirement(incomingEntity, this.Logger);
            Validation.ApplicationManagedDiskEncryptionMinimumSkuRequirement(incomingEntity, this.Logger);
            Validation.ApplicationDefaultStorageFirewallMinimumSkuRequirement(incomingEntity, this.Logger);
            Validation.ApplicationEnhancedSecurityComplianceMinimumSkuRequirement(incomingEntity, this.Logger);
            Validation.ApplicationDefaultCatalogMinimumSkuRequirement(incomingEntity, this.Logger);

            Logger.Debug(operationName,
                $"[Incoming] - Workspace NPIP : {incomingEntity.Properties.Parameters.GetNoPublicIpOrDefault(Logger)}, " +
                $"VNET Injected :{Utilities.IsVNetInjectedWorkspace(incomingEntity.Properties.Parameters, Logger)}, " +
                $"PublicNetworkAccess :  {incomingEntity.GetPublicNetworkAccessOrDefault()}, " +
                $"RequiredNsgRules : {incomingEntity.GetRequiredNetworkSecurityGroupOrDefault()}");

            await this.FrontdoorEngine.
                RegisterSubscriptionForResourceProviders(
                    customerTenantId,
                    resourceProviderNamespace,
                    incomingEntity.SubscriptionId,
                    new List<string>()
                    { ProviderConstants.ManagedIdentity.ManagedIdentityNamespace,
                      ProviderConstants.Storage.KeySourceStorage,
                      ProviderConstants.Compute.ComputeNamespace,
                      ProviderConstants.MicrosoftNetwork.NetworkNamespace
                    },
                    signedOboToken);

            var existingEntity = await this
                .GetApplianceDataProvider()
                .FindAppliance(
                    incomingEntity.SubscriptionId,
                    incomingEntity.ResourceGroup,
                    incomingEntity.Name)
                .ConfigureAwait(continueOnCapturedContext: false);

            Validation.ApplicationPropertiesExistOnUpdate(existingEntity);
            Validation.ApplicationParametersExistOnUpdate(existingEntity);

            incomingEntity.SetWorkspaceNoPublicIp(existingEntity, Logger);

            this.ValidatePublicNetworkAccessFeatureEnabled(incomingEntity);

            await this.ValidateRequiredNsgRuleEnabled(incomingEntity).ConfigureAwait(false);

            await this.ValidateAndPopulateAccessConnectorIdentity(incomingEntity).ConfigureAwait(false);

            #region Default Storage firewall and configuration Validation

            var isDefaultStorageFirewallFeatureEnabled = await Utilities
                        .IsDefaultStorageFirewallFeatureEnabled(
                            ProviderConstants.Databricks.ResourceProviderNamespace,
                            incomingEntity.SubscriptionId,
                            this.Logger,
                            this.FrontdoorEngine)
                        .ConfigureAwait(continueOnCapturedContext: false);

            if (!isDefaultStorageFirewallFeatureEnabled && incomingEntity.Properties.DefaultStorageFirewall != null)
            {
                this.Logger.Debug(operationName,
                    $"Subscription is not enabled with or Cloud doesn't support Default Storage Firewall Feature, but customer specified defaultStorageFirewall input.");

                throw new ErrorResponseMessageException(
                    HttpStatusCode.BadRequest,
                    ErrorResponseCode.DefaultStorageFirewallNotSupported,
                    ErrorResponseMessages.DefaultStorageFirewallNotSupported.ToLocalizedMessage());
            }

            this.ValidateDefaultStorageFirewallConfiguration(incomingEntity, existingEntity);

            #endregion

            await this.ValidateESCFeatureEnabled(incomingEntity);

            var isUcDefaultFeatureEnabled = await Utilities.
                IsUcDefaultFeatureEnabled(
                    ProviderConstants.Databricks.ResourceProviderNamespace,
                    incomingEntity.SubscriptionId,
                    this.Logger,
                    this.FrontdoorEngine)
                .ConfigureAwait(continueOnCapturedContext: false);

            if (!isUcDefaultFeatureEnabled && incomingEntity.Properties.DefaultCatalog != null)
            {
                this.Logger.Debug(operationName,
                $"Subscription is not enabled with UC Default Feature, but customer specified defaultCatalog input!!");

                throw new ErrorResponseMessageException(
                    HttpStatusCode.BadRequest,
                    ErrorResponseCode.UcDefaultNotEnabled,
                    ErrorResponseMessages.UcDefaultNotEnabled.ToLocalizedMessage());
            }

            ApplianceEntity savedApplianceEntity;
            HttpStatusCode responseStatusCode;
            string jobId = null;

            if (existingEntity == null)
            {
                this.Logger.Debug(
                    operationName,
                    "Creating workspace {0}  in resource group {1} in subscription {2}",
                    arg0: incomingEntity.Name,
                    arg1: incomingEntity.ResourceGroup,
                    arg2: incomingEntity.SubscriptionId);

                incomingEntity.SetWorkspaceUpdateOperation(isUpdateOperation: false);

                await this.FrontdoorEngine.CheckDbfsAccountNameAvailability(
                        customerTenantId,
                        incomingEntity.SubscriptionId,
                        incomingEntity.Properties.Parameters,
                        resourceProviderNamespace)
                    .ConfigureAwait(continueOnCapturedContext: false);

                savedApplianceEntity = await this
                    .CreateNewAppliance(incomingEntity, requestProcessingStartDateTime)
                    .ConfigureAwait(continueOnCapturedContext: false);

                responseStatusCode = HttpStatusCode.Created;
                jobId = ApplianceProvisioningJobMetadata.GetJobId(resourceGroupName: incomingEntity.ResourceGroup, applianceName: incomingEntity.Name);
            }
            else
            {
                Logger.Debug(operationName,
                    $"[Existing] - Workspace NPIP : {existingEntity.Properties.Parameters.GetNoPublicIpOrDefault(Logger)}, " +
                    $"VNET Injected :{Utilities.IsVNetInjectedWorkspace(existingEntity.Properties.Parameters, Logger)}, " +
                    $"PublicNetworkAccess :  {existingEntity.GetPublicNetworkAccessOrDefault()}, " +
                    $"RequiredNsgRules : {existingEntity.GetRequiredNetworkSecurityGroupOrDefault()}");

                Validation.ApplianceDeletionInProgress(existingEntity);
                Validation.ApplicanceCreationInProgress(existingEntity);
                Validation.ApplicanceUpdateInProgress(existingEntity);

                Validation.ValidateComputeModeUpdate(existingEntity, incomingEntity);
                Validation.ApplianceManagedResourceGroupIdMatch(
                    existingEntity.Properties.ManagedResourceGroupId,
                    incomingEntity.Properties.ManagedResourceGroupId);

                incomingEntity.UpdateInitialName(existingEntity);
                incomingEntity.SetWorkspaceUpdateOperation(isUpdateOperation: true);

                Validation.ApplicationSkuUpdate(existingEntity.Sku, incomingEntity.Sku);
                Validation.ValidateAnyPrivateEndpointConnectionProxyForPremiumSku(existingEntity, incomingEntity, this.Logger);
                Validation.ValidateSkuForEncryption(existingEntity, incomingEntity);
                Validation.ValidateSkuForEnhancedSecurityCompliance(existingEntity, incomingEntity);
                Validation.ValidateDefaultStorageFirewallConfigurationDuringSkuChange(existingEntity, incomingEntity, this.Logger);
                Validation.ValidateDefaultCatalogConfigurationUpdate(existingEntity, incomingEntity, this.Logger);

                // We want to recreate the MRG and redeploy the main template only if
                // the existing appliance is in a 'Deleted' or 'Failed' state
                var redeploy = (existingEntity.Properties.ProvisioningState == ProvisioningState.Deleted
                                   || existingEntity.Properties.ProvisioningState == ProvisioningState.Failed);

                var isVNetInjectedWorkspace = Utilities.IsVNetInjectedWorkspace(
                    existingEntity.Properties.Parameters,
                    out var existingWorkspaceVnetId,
                    out var existingWorkspacePrivateSubnetName,
                    out var existingWorkspacePublicSubnetName,
                    this.Logger);

                if (isVNetInjectedWorkspace)
                {
                    var incomingCustomVirtualNetworkId =
                        incomingEntity.Properties.Parameters.GetCanonicalizedCustomVirtualNetworkIdProperty(this.Logger, string.Empty);
                    var incomingCustomPrivateSubnet =
                        incomingEntity.Properties.Parameters.GetCustomPrivateSubnetNameProperty(this.Logger, string.Empty);
                    var incomingCustomPublicSubnet =
                        incomingEntity.Properties.Parameters.GetCustomPublicSubnetNameProperty(this.Logger, string.Empty);

                    if (string.IsNullOrWhiteSpace(incomingCustomVirtualNetworkId) ||
                        string.IsNullOrWhiteSpace(incomingCustomPrivateSubnet) ||
                        string.IsNullOrWhiteSpace(incomingCustomPublicSubnet))
                    {
                        this.Logger.Debug(
                            operationName,
                            "Set fallback values to virtual network properties for appliance");

                        incomingEntity.SetVirtualNetworkPropertiesToAppliance(existingWorkspaceVnetId,
                            existingWorkspacePrivateSubnetName,
                            existingWorkspacePublicSubnetName);
                    }
                }

                if (redeploy)
                {
                    this.Logger.Debug(
                        operationName,
                        "Redeploying workspace {0}  in resource group {1} in subscription {2}",
                        arg0: incomingEntity.Name,
                        arg1: incomingEntity.ResourceGroup,
                        arg2: incomingEntity.SubscriptionId);

                    await ValidateNetworkPropertiesUpdateForWorkspaceRedeploy(incomingEntity, existingEntity)
                        .ConfigureAwait(false);

                    savedApplianceEntity = await this.RedeployWorkspace(
                            existingEntity,
                            incomingEntity,
                            requestProcessingStartDateTime)
                        .ConfigureAwait(false);
                    responseStatusCode = HttpStatusCode.Created;
                    jobId = ApplianceProvisioningJobMetadata.GetJobId(incomingEntity.ResourceGroup, incomingEntity.Name);
                }
                else
                {
                    this.Logger.Debug(
                        operationName,
                        "Updating workspace {0}  in resource group {1} in subscription {2}",
                        arg0: incomingEntity.Name,
                        arg1: incomingEntity.ResourceGroup,
                        arg2: incomingEntity.SubscriptionId);

                    await ValidateAndPopulateWorkspaceUpdateRequest(incomingEntity, existingEntity, isAsync);

                    if (isAsync)
                    {
                        responseStatusCode = HttpStatusCode.Created;
                        savedApplianceEntity = await this.UpdateWorkspaceAsync(incomingEntity, requestProcessingStartDateTime).ConfigureAwait(false);
                        jobId = ApplianceProvisioningJobMetadata.GetJobId(incomingEntity.ResourceGroup, incomingEntity.Name);
                    }
                    else
                    {
                        responseStatusCode = HttpStatusCode.OK;
                        savedApplianceEntity = await this.UpdateWorkspace(existingEntity, incomingEntity).ConfigureAwait(false);
                    }
                }
            }

            return Tuple.Create<ApplianceEntity, string, HttpStatusCode>(savedApplianceEntity, jobId, responseStatusCode);
        }

        /// <summary>
        /// validates workspace update request details
        /// </summary>
        /// <param name="incomingEntity"></param>
        /// <param name="existingEntity"></param>
        /// <param name="isAsync">The Async Update is enabled.</param>
        private async Task ValidateAndPopulateWorkspaceUpdateRequest(ApplianceEntity incomingEntity, ApplianceEntity existingEntity, bool isAsync = false)
        {
            Validation.ValidateNotebookEncryptionForPut(existingEntity, incomingEntity);
            existingEntity.UpdateNotebookEncryptionProperties(incomingEntity);

            Validation.ValidateDiskEncryptionForPut(existingEntity, incomingEntity);

            Validation.ValidateEnhancedSecurityComplianceUpdate(existingEntity, incomingEntity);
            existingEntity.UpdateEnhancedSecurityComplianceProperties(incomingEntity);

            await ValidateNetworkUpdateProperties(incomingEntity, existingEntity, isAsync).ConfigureAwait(false);

            if (incomingEntity.Properties.Parameters != null)
            {
                Validation.ManagedStorageAccount(
                    existingEntity,
                    this.Logger);

                Validation.ApplicationRequireInfrastructureUpdate(
                    existingEntity,
                    incomingEntity,
                    this.Logger);
            }
        }

        /// <summary>
        /// Validates the Network Properties for workspace redeploy.
        /// </summary>
        /// <param name="incomingEntity">The incoming entity.</param>
        /// <param name="existingEntity">The existing entity.</param>
        /// <returns>Returns void or validation error.</returns>
        private async Task ValidateNetworkPropertiesUpdateForWorkspaceRedeploy(ApplianceEntity incomingEntity,
            ApplianceEntity existingEntity)
        {
            var operationName = this.GetOperationName(Utilities.GetAsyncMethodName());

            if (existingEntity.Properties.Parameters != null && incomingEntity.Properties.Parameters != null)
            {
                await ValidateNetworkUpdateProperties(incomingEntity, existingEntity, true).ConfigureAwait(false);
            }
            else
            {
                this.Logger.Debug(operationName, "Ignore Redeployment validation assuming creation of workspace failed in first attempt and user redeploying with correct parameters.");
            }
        }

        /// <summary>
        /// Validates the Network Properties.
        /// </summary>
        /// <param name="incomingEntity">The incoming entity.</param>
        /// <param name="existingEntity">The existing entity.</param>
        /// <param name="isAsync">Is async supported in the request.</param>
        /// <returns>Returns void or validation error.</returns>
        private async Task ValidateNetworkUpdateProperties(ApplianceEntity incomingEntity,
            ApplianceEntity existingEntity,
            bool isAsync)
        {
            if (!(Utilities.IsMultivaluedConfiguration(ProviderConstants.MicrosoftNetwork.VNetInjectionMandatoryApiVersions, RequestCorrelationContext.Current.ApiVersion)) ||
                existingEntity.Properties.Parameters.IsParamerterNotNullOrWhiteSpace(ProviderConstants.Databricks.LoadBalancerIdProperty, this.Logger) ||
                existingEntity.Properties.Parameters.IsParamerterNotNullOrWhiteSpace(ProviderConstants.Databricks.LoadBalancerBackendPoolNameProperty, this.Logger))
            {
                //Added new validation logic to ensure customer don't modify success created managed workspace into VNET Injected workspace
                Validation.ValidateCustomVirtualNetworkIdProperty(existingEntity, incomingEntity, this.Logger);
                Validation.ValidateCustomPublicSubnetNameProperty(existingEntity, incomingEntity, this.Logger);
                Validation.ValidateCustomPrivateSubnetNameProperty(existingEntity, incomingEntity, this.Logger);
            }

            if (isAsync)
            {
                Validation.ValidateNetworkUpdateInManagedVnetWorkspace(existingEntity, incomingEntity, this.Logger);
                Validation.ValidateNoAzureServiceRulesValue(existingEntity, incomingEntity);
                Validation.ValidateEnableNoPublicIpPropertyUpdate(existingEntity, incomingEntity, this.Logger);

                // Ensures the Virtual Machine check in the MRG is done only when atleast one network properties changes
                if (existingEntity.IsNetworkPropertyChanged(incomingEntity, this.Logger))
                {
                    var managedResourceGroupName = ResourceGroupRequestMatch.FromFullyQualifiedId(existingEntity.Properties.ManagedResourceGroupId.Trim()).ResourceGroup;
                    var publisherTenantId = this.GetPublisherTenantId(existingEntity);

                    var virtualMachinesExistInResourceGroup = await this.FrontdoorEngine
                                                                        .CheckIfVirtualMachineExistInResourceGroup(
                                                                          publisherTenantId,
                                                                          ProviderConstants.Databricks.ResourceProviderNamespace,
                                                                          existingEntity.SubscriptionId,
                                                                          managedResourceGroupName,
                                                                          ProviderConstants.Compute.VirtualMachinesApiVersion);

                    if (virtualMachinesExistInResourceGroup)
                    {
                        throw new ErrorResponseMessageException(
                            HttpStatusCode.BadRequest,
                            ErrorResponseCode.WorkspaceUpdateNotAllowed,
                            ErrorResponseMessages.WorkspaceUpdateNotAllowedForActiveCluster.ToLocalizedMessage());
                    }
                }
            }
            else
            {
                Validation.ValidatePublicNetworkAccessUpdate(existingEntity, incomingEntity);
                Validation.ValidateRequiredNsgRulesUpdate(existingEntity, incomingEntity);
                Validation.ValidateEnableNoPublicIpProperty(existingEntity, incomingEntity, this.Logger);
            }
        }

        /// <summary>
        /// Public Network access check will be performed only on non-fed workspaces.
        /// </summary>
        /// <param name="incomingEntity">The appliance entity object</param>
        /// <returns>Validates feature is being enabled or not</returns>
        private void ValidatePublicNetworkAccessFeatureEnabled(ApplianceEntity incomingEntity)
        {
            string operationName = this.GetOperationName(Utilities.GetAsyncMethodName());

            var subscriptionId = incomingEntity.SubscriptionId;
            bool isVNetInjectedWorkspace = Utilities.IsVNetInjectedWorkspace(incomingEntity.Properties.Parameters, this.Logger);

            bool isFedRampEnabled = incomingEntity.Properties.IsApplicationPropertiesFedRamp(this.Logger);

            PublicNetworkAccessStatus? networkAccessStatus = incomingEntity.Properties.PublicNetworkAccess.GetPublicNetworkAccessDefaultValue();
            RequiredNetworkSecurityGroupType? requiredNetworkSecurityGroupType = incomingEntity.Properties.RequiredNsgRules.GetRequiredNsgRulesMemberDefaultValue();

            bool disableFedRampCertificationCheckKey = CloudConfigurationManager.GetConfigurationBoolean(
                ProviderConstants.Databricks.DisableFedRampCertificationCheckKey, false);

            bool enableNoPublicIp = incomingEntity.Properties.Parameters.GetNoPublicIpOrDefault(this.Logger);

            if (disableFedRampCertificationCheckKey || isFedRampEnabled)
            {
                this.Logger.Debug(operationName, $"Ignore public network access check for FedRAMP workspaces.");
                return;
            }

            if (networkAccessStatus != PublicNetworkAccessStatus.Enabled)
            {
                if (!isVNetInjectedWorkspace)
                {
                    this.Logger.Debug(operationName, $"The subscription {subscriptionId} is not registered to create private workspace.");
                    throw new ErrorResponseMessageException(
                        HttpStatusCode.BadRequest,
                        ErrorResponseCode.InvalidPrivateWorkspaceForNonVNetInjectedWorkspace,
                        ErrorResponseMessages.PrivateWorkspaceNotSupportedForNonVNetInjectedWorkspace.ToLocalizedMessage(subscriptionId));
                }

                if (!enableNoPublicIp)
                {
                    this.Logger.Debug(operationName, $"Creation of workspace with PublicNetworkAccess Disable is only supported for Secure Cluster Connectivity (No Public IP).");
                    throw new ErrorResponseMessageException(
                        HttpStatusCode.BadRequest,
                        ErrorResponseCode.PrivateWorkspaceNotSupportedForPublicIP,
                        ErrorResponseMessages.PrivateWorkspaceNotSupportedForPublicIP.ToLocalizedMessage(subscriptionId));
                }

                if (requiredNetworkSecurityGroupType == RequiredNetworkSecurityGroupType.AllRules)
                {
                    this.Logger.Debug(operationName, $"Cannot use RequiredNsgRule = 'AllRules' for workspace with PublicNetworkAccess 'Disabled'.");
                    throw new ErrorResponseMessageException(
                        HttpStatusCode.BadRequest,
                        ErrorResponseCode.PrivateWorkspaceNotSupportedForSpecifiedRequiredNsgRules,
                        ErrorResponseMessages.PrivateWorkspaceNotSupportedForSpecifiedRequiredNsgRules.ToLocalizedMessage(requiredNetworkSecurityGroupType));
                }
            }
        }

        /// <summary>
        /// Required Network Security Group Rule check will be performed only on non-fed workspaces.
        /// </summary>
        /// <param name="incomingEntity">The appliance entity object</param>
        /// <returns>Validates semi-colon separated subs in CSCFG</returns>
        private async Task ValidateRequiredNsgRuleEnabled(ApplianceEntity incomingEntity)
        {
            string operationName = this.GetOperationName(Utilities.GetAsyncMethodName());

            string subscriptionId = incomingEntity.SubscriptionId;

            bool isVNetInjectedWorkspace = Utilities.IsVNetInjectedWorkspace(incomingEntity.Properties.Parameters, this.Logger);

            bool isFedRampEnabled = incomingEntity.Properties.IsApplicationPropertiesFedRamp(this.Logger);

            var requiredNsgRules = incomingEntity.Properties.RequiredNsgRules.GetRequiredNsgRulesMemberDefaultValue();

            bool enableNoPublicIp = incomingEntity.Properties.Parameters.GetNoPublicIpOrDefault(this.Logger);

            bool disableFedRampCertificationCheckKey = CloudConfigurationManager.GetConfigurationBoolean(ProviderConstants.Databricks.DisableFedRampCertificationCheckKey, false);

            if (disableFedRampCertificationCheckKey || isFedRampEnabled)
            {
                this.Logger.Debug(operationName, $"Ignore required Network Security Group rule check for FedRAMP workspaces.");
                return;
            }

            if (requiredNsgRules != RequiredNetworkSecurityGroupType.AllRules)
            {
                if (!isVNetInjectedWorkspace)
                {
                    this.Logger.Debug(operationName, $"Non VNet Injected workspace are not allowed to create with requiredNsgRule property.");
                    throw new ErrorResponseMessageException(
                        HttpStatusCode.BadRequest,
                        ErrorResponseCode.RequiredNsgRuleNotSupportedForNonVNetInjectedWorkspace,
                        ErrorResponseMessages.RequiredNsgRuleNotSupportedForNonVNetInjectedWorkspace.ToLocalizedMessage());
                }

                if (!enableNoPublicIp)
                {
                    this.Logger.Debug(operationName, $"Public IP workspace are not allowed to create with requiredNsgRule property.");
                    throw new ErrorResponseMessageException(
                        HttpStatusCode.BadRequest,
                        ErrorResponseCode.RequiredNsgRuleNotSupportedForPublicIPWorkspace,
                        ErrorResponseMessages.RequiredNsgRuleNotSupportedForPublicIPWorkspace.ToLocalizedMessage());
                }

                if (requiredNsgRules == RequiredNetworkSecurityGroupType.NoAzureServiceRules)
                {
                    await this.ValidateNoAzureServiceRules(incomingEntity, subscriptionId);
                }
            }
        }

        /// <summary>
        /// Validates that the subscription has the ESC feature enabled if properties are present in request
        /// </summary>
        /// <param name="incomingEntity"></param>
        /// <returns></returns>
        /// <exception cref="ErrorResponseMessageException"></exception>
        private async Task ValidateESCFeatureEnabled(ApplianceEntity incomingEntity)
        {
            string operationName = this.GetOperationName(Utilities.GetAsyncMethodName());

            if (incomingEntity.Properties.EnhancedSecurityCompliance != null)
            {
                var isEscEnabled = await Utilities
                        .IsESCFeatureEnabled(
                            ProviderConstants.Databricks.ResourceProviderNamespace,
                            incomingEntity.SubscriptionId,
                            this.Logger,
                            this.FrontdoorEngine)
                        .ConfigureAwait(continueOnCapturedContext: false);

                if (!isEscEnabled)
                {
                    this.Logger.Debug(operationName, $"ESC is NOT supported for this subscription");

                    throw new ErrorResponseMessageException(
                        HttpStatusCode.BadRequest,
                        ErrorResponseCode.EnhancedSecurityComplianceNotSupported,
                        ErrorResponseMessages.EnhancedSecurityComplianceNotSupported.ToLocalizedMessage());
                }

                this.Logger.Debug(operationName, $"ESC is supported for this subscription");
            }
        }

        /// <summary>
        /// Gets the list of outbound network endpoints.
        /// </summary>
        /// <param name="subscriptionId">The subscription ID</param>
        /// <param name="resourceGroup">The resource group name</param>
        /// <param name="applicationName">The workspace name</param>
        /// <returns>List of outbound network endpoints.</returns>
        public async Task<List<OutboundNetworkEndpoint>> GetOutboundNetworkDependencies(string subscriptionId, string resourceGroup, string applicationName)
        {
            string operationName = this.GetOperationName(Utilities.GetAsyncMethodName());

            try
            {
                this.Logger.Debug(operationName, $"Getting outbound network dependencies for workspace.");

                var applianceEntity = await this.GetApplication(
                    subscriptionId: subscriptionId,
                    resourceGroupName: resourceGroup,
                    applicationName: applicationName)
                .ConfigureAwait(continueOnCapturedContext: false);

                Validation.CustomProviderApiVersion(RequestCorrelationContext.Current.ApiVersion);

                bool isVNetInjectedWorkspace = Utilities.IsVNetInjectedWorkspace(
                   applianceEntity.Properties.Parameters, out _, out string _, out string _, this.Logger);

                if (!isVNetInjectedWorkspace)
                {
                    this.Logger.Debug(operationName, $"The workspace is not vnet injected workspace. Failing the request!!");
                    throw new ErrorResponseMessageException(
                        HttpStatusCode.BadRequest,
                        ErrorResponseCode.NonVNetInjectedWorkspaceNotSupported,
                        ErrorResponseMessages.NonVnetInjectedWorkspaceNotSupportedForOutboundNetworkEndpoints.ToLocalizedMessage(applicationName));
                }

                bool isNpipEnabled = applianceEntity.IsApplianceEntityNpip(this.Logger);
                var dbfsAccountName = applianceEntity.Properties?.Parameters?.ContainsKey(ProviderConstants.Databricks.StorageAccountNameParameter) == true ?
                    applianceEntity.Properties.Parameters[ProviderConstants.Databricks.StorageAccountNameParameter].Value<string>("value") : string.Empty;

                var outboundDependencies = await this.ProviderConfiguration.DatabricksBlobDataProvider.GetDatabricksOutboundNetworkEndpoints(applianceEntity.Location, isNpipEnabled).ConfigureAwait(false);

                // Add DBFS storage outbound endpoints dependency to the existing list
                this.Logger.Debug(operationName, $"Adding DBFS endpoints to outbound dependencies list for {dbfsAccountName}.");

                var outboundStorageDependenciesIndex = outboundDependencies.IndexOf(
                        endpoint => endpoint.Category == ProviderConstants.Storage.OutboundNetworkEndpointStorageCategory);

                var dbfsEndpoints = this.GetDbfsOutboundDependencies(dbfsAccountName);
                outboundDependencies[outboundStorageDependenciesIndex].Endpoints.AddRange(dbfsEndpoints);

                this.Logger.Debug(operationName, $"Successfully created outbound dependencies endpoint list.");
                return outboundDependencies;
            }
            catch (ErrorResponseMessageException exception)
            {
                this.Logger.Error(exception, operationName,
                    $"Error downloading list of outbound network endpoints for workspace {applicationName}. Error: {Utilities.FlattenException(exception)}");
                throw;
            }
            catch (Exception exception)
            {
                this.Logger.Error(exception, operationName,
                    $"Error downloading list of outbound network endpoints for workspace {applicationName}. Error: {Utilities.FlattenException(exception)}");
                throw new ServerErrorResponseMessageException(
                    HttpStatusCode.InternalServerError,
                    ErrorResponseCode.UnableToGetOutboundNetworkEndpoints.ToString(),
                    ErrorResponseMessages.UnableToGetOutboundNetworkEndpoints.ToLocalizedMessage(applicationName));
            }
        }

        /// <summary>
        /// Gets the DBFS storage account dependencies.
        /// </summary>
        /// <param name="dbfsAccountName">Dbfs Storage Account Name</param>
        /// <returns>Returns DBFS outbound network dependency</returns>
        private EndpointDependency[] GetDbfsOutboundDependencies(string dbfsAccountName)
        {
            var dbfsEndpoints = new EndpointDependency[]
            {
                new EndpointDependency
                {
                    DomainName = $"{dbfsAccountName}.{CloudConfigurationManager.GetConfiguration(ProviderConstants.Storage.BlobEndpointSuffix)}",
                    EndpointDetails = new List<EndpointDetail>() { new EndpointDetail() { Port = ProviderConstants.Storage.OutboundNetworkEndpointStoragePort }}
                },
                new EndpointDependency
                {
                    DomainName = $"{dbfsAccountName}.{CloudConfigurationManager.GetConfiguration(ProviderConstants.Storage.DfsEndpointSuffix)}",
                    EndpointDetails = new List<EndpointDetail>() { new EndpointDetail() { Port = ProviderConstants.Storage.OutboundNetworkEndpointStoragePort }}
                }
            };

            return dbfsEndpoints;
        }

        /// <summary>
        /// Creates a new appliance.
        /// </summary>
        /// <param name="incomingEntity">The appliance entity object representing the workspace to be created.</param>
        /// <param name="requestProcessingStartDateTime"> the processing start date time of the request</param>
        /// <returns>The newly created appliance entity object with provisioning metadata set.</returns>
        private async Task<ApplianceEntity> CreateNewAppliance(
                ApplianceEntity incomingEntity, DateTime requestProcessingStartDateTime)
        {
            var operationName = this.GetOperationName(Utilities.GetAsyncMethodName());

            this.Logger.Debug(
                operationName,
                format: "Create a new appliance with name '{0}', managed resource group '{1}'.",
                arg0: incomingEntity.Name,
                arg1: incomingEntity.Properties.ManagedResourceGroupId);

            Validation.ApplicationEncryptionAtCreate(incomingEntity.Properties.Parameters);

            var publisherPackageId = await this.GetPublisherPackageId(incomingEntity);

            incomingEntity.Properties.ProvisioningState = ProvisioningState.Accepted;
            incomingEntity.Properties.CreatedBy = ApplicationApiEngine.GetApplicationClientDetailsEntity();
            incomingEntity.Properties.UpdatedBy = ApplicationApiEngine.GetApplicationClientDetailsEntity();
            incomingEntity.Properties.PublisherPackageId = publisherPackageId;

            incomingEntity.Metadata = new ApplianceMetadata
            {
                ManagedResourceGroupLockName = incomingEntity.Name,
                DeploymentName = incomingEntity.Name,
                OperationStatuses = new InsensitiveDictionary<ApplicationOperationStatus>
                {
                    {
                        this.ApplianceWriteOperationName,
                        new ApplicationOperationStatus { Status = ProvisioningState.Accepted }
                    }
                }
            };

            // As per current design, when the creation of a new workspace is accepted, the parameters are
            // NOT saved in the data store. These are passed to the appliance provisioning job instead.
            // So saving the property values into a temporary variable and setting the entity's
            // ApplicationProperties.Parameters value to null.
            var deploymentParameters = incomingEntity.Properties.Parameters;
            incomingEntity.Properties.Parameters = null;

            await this
                .GetApplianceDataProvider()
                .SaveAppliance(incomingEntity)
                .ConfigureAwait(continueOnCapturedContext: false);

            await this
                .GetJobsDataProvider()
                .CreateApplianceProvisioningJob(
                    subscriptionId: incomingEntity.SubscriptionId,
                    resourceGroupName: incomingEntity.ResourceGroup,
                    resourceType: this.ResourceType,
                    resourceProviderNamespace: this.ResourceProviderNamespace,
                    applianceName: incomingEntity.Name,
                    location: incomingEntity.Location,
                    parameters: deploymentParameters,
                    requestProcessingStartDateTime: requestProcessingStartDateTime,
                    applianceWriteOperationName: this.ApplianceWriteOperationName)
                .ConfigureAwait(continueOnCapturedContext: false);

            var appliancePackage = this.GetValidatedMarketplaceAppliancePackage(publisherPackageId);

            this
               .PopulatePropertiesFromAppliancePackage(
                   applianceEntity: incomingEntity,
                   appliancePackage: appliancePackage);

            return incomingEntity;
        }

        /// <summary>
        /// Updates an existing appliance.
        /// </summary>
        /// <param name="existingEntity">The existing workspace object saved in the database</param>
        /// <param name="incomingEntity">The incoming workspace object serialized from customer request</param>
        /// <returns>The updated workspace object</returns>
        private async Task<ApplianceEntity> UpdateWorkspace(
            ApplianceEntity existingEntity,
            ApplianceEntity incomingEntity)
        {
            string operationName = this.GetOperationName(Utilities.GetAsyncMethodName());
            string workspaceId = existingEntity.GetFullyQualifiedId(this.ResourceType);
            var apiVersion = existingEntity.GetDatabricksBackendApiVersion(this.Logger);
            var publisherPackageId = await this.GetPublisherPackageId(existingEntity);
            var appliancePackage = this.GetValidatedMarketplaceAppliancePackage(publisherPackageId);

            this.Logger.Debug(
                operationName,
                $"Update existing appliance with Id '{workspaceId}', managed resource group '{existingEntity.Properties.ManagedResourceGroupId}' in ProvisioningState '{existingEntity.Properties.ProvisioningState}'");

            // Update disk encryption properties if provided and different from existing properties
            if (incomingEntity.Properties.EncryptionProperties?.EncryptionEntities?.ManagedDisk != null &&
                !incomingEntity.Properties.EncryptionProperties.EncryptionEntities.ManagedDisk.Equals(existingEntity.Properties.EncryptionProperties?.EncryptionEntities?.ManagedDisk))
            {
                var diskEncryptionTuple = await this.HandleManagedDiskEncryptionUpdate(existingEntity.SubscriptionId, appliancePackage.TenantId, existingEntity, incomingEntity);
                existingEntity.Properties.EncryptionProperties = diskEncryptionTuple.Item1;
                existingEntity.PopulateManagedDiskEncryptionDetails(diskEncryptionTuple.Item2);
            }

            var initialEntity = new ApplianceEntity(existingEntity);

            existingEntity.Tags = incomingEntity.Tags;

            var updatedSku = incomingEntity.Sku?.TryFromJToken<DatabricksSku>();
            if (updatedSku != null && updatedSku.Name != DatabricksSkuName.NotSpecified)
            {
                existingEntity.Sku = incomingEntity.Sku;
            }

            if (incomingEntity.Properties.Parameters != null
                && incomingEntity.Properties.Parameters.TryParseAmlWorkspaceId(this.Logger, out string amlWorkspaceId))
            {
                existingEntity.SetAmlWorkspaceId(amlWorkspaceId);
            }

            ApplicationClientDetailsEntity updatedBy = ApplicationApiEngine.GetApplicationClientDetailsEntity();
            existingEntity.Properties.UpdatedBy = updatedBy;

            if (incomingEntity.Properties.Parameters != null)
            {
                await this.EnableManagedIdentityOnStorage(
                    existingEntity.SubscriptionId,
                    appliancePackage.TenantId,
                    existingEntity,
                    incomingEntity)
                    .ConfigureAwait(false);

                await this.ConfigureEncryptionOnStorage(
                    existingEntity.SubscriptionId,
                    appliancePackage.TenantId,
                    existingEntity,
                    incomingEntity)
                    .ConfigureAwait(false);
            }

            try
            {
                var resourceGroupRequest = ResourceGroupRequestMatch.FromFullyQualifiedId(existingEntity.Properties.ManagedResourceGroupId.Trim());
                await workspaceTagService.UpdateWorkspaceTag(
                                                incomingEntity.Tags,
                                                appliancePackage.TenantId,
                                                resourceGroupRequest.ResourceGroup,
                                                this.ResourceProviderNamespace,
                                                existingEntity);

                //// Notifies DB for Workspace UPDATE
                var updatedNotification = DatabricksWorkspace.FromApplianceEntity(existingEntity);

                await this.DBWorkspaceUpdateNotificationWrapper(
                    existingEntity.SubscriptionId,
                    workspaceId,
                    this.ResourceProviderNamespace,
                    existingEntity,
                    apiVersion,
                    existingEntity.Properties?.WorkspaceUrl)
                    .ConfigureAwait(false);

                await this
                    .GetApplianceDataProvider()
                    .ReplaceAppliance(existingEntity)
                    .ConfigureAwait(false);
            }
            catch (Exception exception)
            {
                this.Logger.Error(
                    operationName,
                    $"ROLLBACK: DB Notification for Patch on the Workspace '{workspaceId}', Api-version: {RequestCorrelationContext.Current.ApiVersion}. Exception details: '{Utilities.FlattenException(exception)}");

                await this.DBWorkspaceUpdateNotificationWrapper(
                    existingEntity.SubscriptionId,
                    workspaceId,
                    this.ResourceProviderNamespace,
                    initialEntity,
                    apiVersion,
                    existingEntity.Properties?.WorkspaceUrl)
                    .ConfigureAwait(false);

                this.HandleResourceGroupOperationOrStorageException(existingEntity, exception);

                throw;
            }

            this.PopulatePropertiesFromAppliancePackage(existingEntity, appliancePackage);

            this.Logger.Debug(
                operationName,
                $"UPDATE on the Workspace '{workspaceId}', Api-version: {RequestCorrelationContext.Current.ApiVersion} completed");

            return existingEntity;
        }

        private void HandleResourceGroupOperationOrStorageException(ApplianceEntity existingEntity, Exception exception)
        {
            if (exception is ResourceGroupOperationException)
            {
                var resourceGroupOperationException = exception as ResourceGroupOperationException;

                var extendedErrorInfo = new ExtendedErrorInfo
                {
                    Code = resourceGroupOperationException.ErrorCode,
                    Message = exception.Message
                };

                throw new ErrorResponseMessageException(
                   resourceGroupOperationException.HttpStatus,
                   ErrorResponseCode.ApplicationUpdateFail,
                   ErrorResponseMessages.ApplicationUpdateFail.ToLocalizedMessage(existingEntity.Name),
                   details: extendedErrorInfo.AsArray());
            }

            if (exception is StorageException)
            {
                var storageException = exception as StorageException;

                if (storageException.RequestInformation?.HttpStatusCode == (int)HttpStatusCode.PreconditionFailed)
                {
                    throw new ErrorResponseMessageException(
                       HttpStatusCode.PreconditionFailed,
                       ErrorResponseCode.ConcurrentUpdateError,
                       ErrorResponseMessages.ConcurrentUpdateError.ToLocalizedMessage());
                }
            }
        }

        /// <summary>
        /// Updates an existing appliance asynchronously.
        /// </summary>
        /// <param name="incomingEntity">The incoming workspace object serialized from customer request</param>
        /// <param name="requestProcessingStartDateTime">The processing start time</param>
        /// <returns>The updated workspace object</returns>
        private async Task<ApplianceEntity> UpdateWorkspaceAsync(
            ApplianceEntity incomingEntity, DateTime requestProcessingStartDateTime)
        {
            string operationName = this.GetOperationName(Utilities.GetAsyncMethodName());

            this.Logger.Debug(
                            operationName: operationName,
                            format: "Update an appliance with name '{0}', managed resource group '{1}'.",
                            arg0: incomingEntity.Name,
                            arg1: incomingEntity.Properties.ManagedResourceGroupId);

            var publisherPackageId = await this.GetPublisherPackageId(incomingEntity);

            incomingEntity.Properties.ProvisioningState = ProvisioningState.Updating;
            incomingEntity.Properties.UpdatedBy = ApplicationApiEngine.GetApplicationClientDetailsEntity();
            incomingEntity.Properties.PublisherPackageId = publisherPackageId;

            incomingEntity.Metadata = new ApplianceMetadata
            {
                ManagedResourceGroupLockName = incomingEntity.Name,
                DeploymentName = incomingEntity.Name,
                OperationStatuses = new InsensitiveDictionary<ApplicationOperationStatus>
                {
                    {
                        this.ApplianceWriteOperationName,
                        new ApplicationOperationStatus { Status = ProvisioningState.Updating }
                    }
                }
            };

            await this
                .GetApplianceDataProvider()
                .SetApplianceProvisioningState(incomingEntity.SubscriptionId,
                                               incomingEntity.ResourceGroup,
                                               incomingEntity.Name,
                                               ProvisioningState.Updating)
                .ConfigureAwait(continueOnCapturedContext: false);

            var jobsMetadataParameters = new InsensitiveDictionary<JToken>
            {
                { ProviderConstants.WorkspaceUpdateParameters.Properties, incomingEntity.Properties.ToJToken() },
                { ProviderConstants.WorkspaceUpdateParameters.Sku, incomingEntity.Sku?.ToJToken() },
                { ProviderConstants.WorkspaceUpdateParameters.Tags, incomingEntity.Tags?.ToJToken() }
            };

            await this
                .GetJobsDataProvider()
                .CreateApplianceProvisioningJob(
                    subscriptionId: incomingEntity.SubscriptionId,
                    resourceGroupName: incomingEntity.ResourceGroup,
                    resourceType: this.ResourceType,
                    resourceProviderNamespace: this.ResourceProviderNamespace,
                    applianceName: incomingEntity.Name,
                    location: incomingEntity.Location,
                    parameters: jobsMetadataParameters,
                    requestProcessingStartDateTime: requestProcessingStartDateTime,
                    applianceWriteOperationName: this.ApplianceWriteOperationName,
                    provisioningOperation: ProvisioningOperation.Updating)
                .ConfigureAwait(continueOnCapturedContext: false);

            var appliancePackage = this.GetValidatedMarketplaceAppliancePackage(publisherPackageId);

            this
               .PopulatePropertiesFromAppliancePackage(
                   applianceEntity: incomingEntity,
                   appliancePackage: appliancePackage);

            return incomingEntity;
        }

        /// <summary>
        /// Redeploys an existing appliance.
        /// </summary>
        /// <param name="existingEntity">The existing appliance entity stored in the database</param>
        /// <param name="incomingEntity">The updated entity as requested by the customer</param>
        /// <param name="requestProcessingStartDateTime"> the processing start date time of the request</param>
        /// <returns>Updated entity with provisioning metadata</returns>
        private async Task<ApplianceEntity> RedeployWorkspace(
            ApplianceEntity existingEntity,
            ApplianceEntity incomingEntity,
            DateTime requestProcessingStartDateTime)
        {
            string operationName = this.GetOperationName(Utilities.GetAsyncMethodName());
            string workspaceId = existingEntity.GetFullyQualifiedId(this.ResourceType);

            this.Logger.Debug(operationName, $"Redeploying workspace '{workspaceId}'");

            ApplicationClientDetailsEntity updatedBy = ApplicationApiEngine.GetApplicationClientDetailsEntity();
            Validation.ApplicationEncryptionAtCreate(incomingEntity.Properties.Parameters);

            existingEntity.Tags = incomingEntity.Tags;

            var updatedSku = incomingEntity.Sku?.TryFromJToken<DatabricksSku>();
            if (updatedSku != null && updatedSku.Name != DatabricksSkuName.NotSpecified)
            {
                existingEntity.Sku = incomingEntity.Sku;
            }

            if (incomingEntity.Properties.EncryptionProperties != null)
            {
                existingEntity.Properties.EncryptionProperties = incomingEntity.Properties.EncryptionProperties;
            }

            if (incomingEntity.Properties.EnhancedSecurityCompliance != null)
            {
                existingEntity.Properties.EnhancedSecurityCompliance = incomingEntity.Properties.EnhancedSecurityCompliance;
            }

            if (incomingEntity.Properties.Parameters != null
                && incomingEntity.Properties.Parameters.TryParseAmlWorkspaceId(this.Logger, out string amlWorkspaceId))
            {
                existingEntity.SetAmlWorkspaceId(amlWorkspaceId);
            }

            // We should save existing connector/default storage firewall irrespective of incoming state of private dbfs
            existingEntity.Metadata.ExistingAccessConnector = existingEntity.Properties.AccessConnector;
            existingEntity.Metadata.ExistingDefaultStorageFirewall = existingEntity.Properties.DefaultStorageFirewall;

            // We should change existing default storage firewall with incoming default storage firewall if customer specified it in request
            if (incomingEntity.Properties.DefaultStorageFirewall != null)
            {
                existingEntity.Properties.DefaultStorageFirewall = incomingEntity.Properties.DefaultStorageFirewall;
            }

            // We should change existing access connector with incoming access connector if customer specified it in request
            if (incomingEntity.Properties.AccessConnector != null)
            {
                existingEntity.Properties.AccessConnector = incomingEntity.Properties.AccessConnector;
            }

            if (Utilities.IsVNetInjectedWorkspace(incomingEntity.Properties.Parameters, this.Logger) &&
                !Utilities.IsVNetInjectedWorkspace(existingEntity.Properties.Parameters, this.Logger))
            {
                existingEntity.Metadata.IsvNetInjectionMigrationInProgress = true;
            }

            existingEntity.SetNetworkPropertiesToAppliance(incomingEntity, this.Logger);
            existingEntity.Properties.UpdatedBy = updatedBy;
            existingEntity.Properties.ProvisioningState = ProvisioningState.Accepted;
            existingEntity.Metadata.OperationStatuses[this.ApplianceWriteOperationName] = new ApplicationOperationStatus { Status = ProvisioningState.Accepted };

            try
            {
                await this
                    .GetApplianceDataProvider()
                    .ReplaceAppliance(existingEntity)
                    .ConfigureAwait(false);
            }
            catch (Exception exception)
            {
                this.Logger.Error(
                    operationName,
                    $"Failed to save workspace to table storage '{workspaceId}', Api-version: {RequestCorrelationContext.Current.ApiVersion}. Exception details: '{Utilities.FlattenException(exception)}");

                throw;
            }

            var publisherPackageId = await this.GetPublisherPackageId(incomingEntity);
            var appliancePackage = this.GetValidatedMarketplaceAppliancePackage(publisherPackageId);
            this.PopulatePropertiesFromAppliancePackage(existingEntity, appliancePackage);

            this.Logger.Debug(operationName, $"Creating job to re-deploy the workspace '{workspaceId}'");

            await this
                .GetJobsDataProvider()
                .CreateApplianceProvisioningJob(
                    incomingEntity.SubscriptionId,
                    incomingEntity.ResourceGroup,
                    this.ResourceType,
                    this.ResourceProviderNamespace,
                    incomingEntity.Name,
                    existingEntity.Location,
                    incomingEntity.Properties.Parameters,
                    this.ApplianceWriteOperationName,
                    requestProcessingStartDateTime)
                .ConfigureAwait(false);

            return existingEntity;
        }

        /// <summary>
        /// Configuring Encryption Settings on Managed Storage account
        /// </summary>
        /// <param name="subscriptionId">subscription id</param>
        /// <param name="publisherTenantId">Publisher tenant id</param>
        /// <param name="existingEntity">appliance entity</param>
        /// <param name="incomingEntity">appliance input</param>
        /// <returns>returns a task</returns>
        private async Task ConfigureEncryptionOnStorage(string subscriptionId, string publisherTenantId, ApplianceEntity existingEntity, ApplianceEntity incomingEntity)
        {
            string operationName = this.GetOperationName(Utilities.GetAsyncMethodName());
            var managedResourceGroupName = ResourceGroupRequestMatch.FromFullyQualifiedId(incomingEntity.Properties.ManagedResourceGroupId.Trim()).ResourceGroup;
            existingEntity.Properties.Parameters.TryParseDatabricksPropertiesParameter(ProviderConstants.Databricks.StorageAccountNameParameter, this.Logger, out string storageAccountName);

            if (!incomingEntity.Properties.Parameters.ContainsKey(ProviderConstants.Storage.Encryption))
            {
                this.Logger.Debug(operationName, $"No need to Configure Encryption Settings for storage account {storageAccountName} under Managed Resource Group: '{managedResourceGroupName}'.");
                return;
            }

            Utilities.TryParseDatabricksPropertiesParameter(incomingEntity.Properties.Parameters, ProviderConstants.Storage.Encryption, this.Logger, out EncryptionDefinition encryptionDefinition);
            this.Logger.Debug(operationName, $"Entering Configure Encryption Settings for storage account {storageAccountName} under Managed Resource Group: '{managedResourceGroupName}'. KeySource {encryptionDefinition.KeySource}");

            JObject encryptionSettings;
            StorageDefinition storageUpdateRequest;
            if (encryptionDefinition.KeySource.EqualsInsensitively(ProviderConstants.Storage.KeySourceDefault))
            {
                this.Logger.Debug(operationName, $"Reverting Encryption Configuration Settings for Managed Resource Group: '{managedResourceGroupName}' to default.");
                storageUpdateRequest = new StorageDefinition()
                {
                    Properties = new JObject(
                    new JProperty(
                        ProviderConstants.Storage.Encryption,
                        new JObject
                        {
                                new JProperty(
                                    ProviderConstants.Storage.KeySource,
                                    ProviderConstants.Storage.KeySourceStorage)
                        }))
                };
                encryptionSettings = new JObject()
                {
                    new JProperty(ProviderConstants.Storage.ParameterType, ProviderConstants.Storage.ParameterObjectType),
                    new JProperty(
                        ProviderConstants.Storage.ParameterValue,
                        new JObject(
                            new JProperty(
                                ProviderConstants.Storage.KeySource,
                                ProviderConstants.Storage.KeySourceDefault)))
                };
            }
            else
            {
                KeyServiceUrlValidator.ValidateKeyServiceUrl(new Uri(encryptionDefinition.KeyVaultUri.ToString()));

                this.Logger.Debug(operationName, $"Setting Encryption Configuration for storage account {storageAccountName} under Managed Resource Group: '{managedResourceGroupName}' to Microsoft.KeyVault");
                storageUpdateRequest = new StorageDefinition()
                {
                    Properties = new JObject(
                        new JProperty(
                            ProviderConstants.Storage.Encryption,
                            new JObject
                            {
                                    new JProperty(
                                        ProviderConstants.Storage.KeySource,
                                        ProviderConstants.Storage.KeySourceKeyVault),
                                    new JProperty(
                                        ProviderConstants.Storage.KeyVaultProperties,
                                        new JObject(
                                            new JProperty(
                                                ProviderConstants.Storage.KeyVaultUri,
                                                encryptionDefinition.KeyVaultUri),
                                            new JProperty(
                                                ProviderConstants.Storage.KeyVaultKeyName,
                                                encryptionDefinition.KeyName),
                                            new JProperty(
                                                ProviderConstants.Storage.KeyVaultKeyVersion,
                                                encryptionDefinition.KeyVersion ?? string.Empty)))
                            }))
                };
                encryptionSettings = new JObject()
                {
                    new JProperty(ProviderConstants.Storage.ParameterType, ProviderConstants.Storage.ParameterObjectType),
                    new JProperty(
                        ProviderConstants.Storage.ParameterValue,
                        new JObject(
                            new JProperty(ProviderConstants.Storage.KeySource, ProviderConstants.Storage.KeySourceKeyVault),
                            new JProperty(
                                ProviderConstants.Storage.KeyVaultUri,
                                encryptionDefinition.KeyVaultUri),
                            new JProperty(ProviderConstants.Storage.KeyNameKey, encryptionDefinition.KeyName),
                            new JProperty(ProviderConstants.Storage.KeyVaultKeyVersion, encryptionDefinition.KeyVersion ?? string.Empty)))
                };
            }


            try
            {
                var storageEncryptionResponse = await this.FrontdoorEngine
                    .UpdateStorageEncryptionWithByok(
                        subscriptionId,
                        managedResourceGroupName,
                        storageAccountName,
                        publisherTenantId,
                        this.ResourceProviderNamespace,
                        storageUpdateRequest)
                    .ConfigureAwait(false);

                existingEntity.Properties.Parameters[ProviderConstants.Storage.Encryption] = encryptionSettings;
                this.Logger.Debug(operationName, $"Successfully updated storage account encryption for workspaceId - '{existingEntity.GetFullyQualifiedId(this.ResourceType)}'.");
            }
            catch (StorageOperationException exception)
            {
                this.Logger.Error(operationName, $"Failed to update storage account encryption setting in managed resource group - '{managedResourceGroupName}'.");

                await this
                    .GetApplianceDataProvider()
                    .ReplaceAppliance(existingEntity)
                    .ConfigureAwait(false);

                throw new ErrorResponseMessageException(
                    HttpStatusCode.Conflict,
                    ErrorResponseCode.StorageAccountOperationFailed,
                    ErrorResponseMessages.StorageAccountUpdateFailed.ToLocalizedMessage(storageAccountName, exception.Message));
            }
        }

        /// <summary>
        /// Enable Managed identity if not already enabled
        /// </summary>
        /// <param name="subscriptionId">subscription Id</param>
        /// <param name="publisherTenantId">publisher tenant Id</param>
        /// <param name="existingEntity">appliance entity</param>
        /// <param name="incomingEntity">input appliance</param>
        /// <returns>returns a task to enable MSI operation</returns>
        private async Task EnableManagedIdentityOnStorage(string subscriptionId, string publisherTenantId, ApplianceEntity existingEntity, ApplianceEntity incomingEntity)
        {
            var operationName = this.GetOperationName(Utilities.GetAsyncMethodName());

            var workspaceId = existingEntity.GetFullyQualifiedId(this.ResourceType);
            var managedResourceGroupName = ResourceGroupRequestMatch.FromFullyQualifiedId(incomingEntity.Properties.ManagedResourceGroupId.Trim()).ResourceGroup;
            existingEntity.Properties.Parameters.TryParseDatabricksPropertiesParameter(ProviderConstants.Databricks.StorageAccountNameParameter, this.Logger, out string storageAccountName);

            var storageUpdateResourceUri = UriTemplateEngine.GetStorageGetPropertiesRequestUri(
                this.FrontdoorEngine.FrontdoorEndpointUri,
                subscriptionId,
                managedResourceGroupName,
                storageAccountName,
                this.FrontdoorEngine.StorageApiVersion);

            var doesInputHasPrepareEncryption = incomingEntity.Properties.Parameters.ContainsKey(ProviderConstants.Storage.PrepareEncryption);
            var doesStorageIdentityNotExist = string.IsNullOrEmpty(existingEntity.Properties.StorageAccountIdentityEntity?.PrincipalId);
            this.Logger.Debug(
                operationName,
                $"prepareEncryption: {doesInputHasPrepareEncryption}, doesStorageIdentityNotExist: {doesStorageIdentityNotExist}");

            if (doesInputHasPrepareEncryption
                && incomingEntity.Properties.Parameters.TryParseDatabricksPropertiesParameter(ProviderConstants.Storage.PrepareEncryption, this.Logger, out bool prepareEncryption)
                && prepareEncryption
                && doesStorageIdentityNotExist)
            {
                try
                {
                    this.Logger.Debug(
                    operationName,
                    $"Enabling storage account MSI for the workspace '{workspaceId}' using Publisher TenantId: {publisherTenantId}");

                    var storageUpdateRequest = new StorageDefinition()
                    {
                        Identity = new JObject()
                        {
                            new JProperty(ProviderConstants.Storage.ParameterType, ProviderConstants.Storage.SystemAssigned)
                        }
                    };

                    var storageAccountDefinition = await this.FrontdoorEngine
                           .CallFrontdoor(
                               new HttpMethod("PATCH"),
                               storageUpdateResourceUri,
                               this.ResourceProviderNamespace,
                               publisherTenantId,
                               storageUpdateRequest)
                           .ConfigureAwait(false);

                    if (storageAccountDefinition == null)
                    {
                        this.Logger.Debug(
                            operationName,
                            $"The enable managed identity failed for workspace '{workspaceId}'");
                        throw new ErrorResponseMessageException(
                            HttpStatusCode.InternalServerError,
                            ErrorResponseCode.EnableMsiForStorageInternalError,
                            ErrorResponseMessages.EnableMsiForStorageInternalError.ToLocalizedMessage(workspaceId));
                    }

                    existingEntity.PopulateStorageAccountIdentityEntity(storageAccountDefinition);
                    this.Logger.Debug(operationName, $"Enable managed identity successful for dbfs storage of workspace '{workspaceId}'.");
                }
                catch (ServerErrorResponseMessageException exception)
                {
                    this.Logger.Error(
                        operationName,
                        $"Error occurred while enabling MSI for managed storage account of workspaceId '{workspaceId}'. Exception:{Utilities.FlattenException(exception)}");

                    throw new ErrorResponseMessageException(
                        HttpStatusCode.Conflict,
                        ErrorResponseCode.EnableMsiForStorageAccountError,
                        ErrorResponseMessages.InvalidRequestContent.ToLocalizedMessage(exception.Message));
                }
            }

            this.Logger.Debug(operationName, $"Finished enabling MSI on Storage account!!");
        }

        /// <summary>
        /// Wrapper method to notify databricks
        /// </summary>
        /// <param name="subscriptionId">Subscription Id</param>
        /// <param name="workspaceId">Workspace Id</param>
        /// <param name="resourceProviderNamespace"> Resource Provider Namespace</param>
        /// <param name="entity">Workspace Entity</param>
        /// <param name="apiVersion">Api Version</param>
        /// <param name="workspaceUrl">UTL to workspace</param>
        /// <returns></returns>
        /// <exception cref="ErrorResponseMessageException"></exception>
        private async Task DBWorkspaceUpdateNotificationWrapper(
           string subscriptionId,
           string workspaceId,
           string resourceProviderNamespace,
           ApplianceEntity entity,
           string apiVersion,
           string workspaceUrl)
        {
            string operationName = this.GetOperationName(Utilities.GetAsyncMethodName());
            string customerTenantId = RequestCorrelationContext.Current.GetHomeTenantId();

            var armNotification = entity.ToDbWorkspace();
            var accountApiNotification = DatabricksWorkspace.FromApplianceEntity(entity);

            // Skips the workspace PUT/ PATCH notification.
            if (!Utilities.IsWorkspaceNotificationEnabled(apiVersion))
            {
                this.Logger.Debug(
                    operationName,
                    $"SKIP: DB Workspace Notification (config/ AFEC) on WorkspaceId : '{workspaceId}'");

                return;
            }

            // Skips the workspace PUT/ PATCH notification if WorkspaceUrl is null or empty.
            if (string.IsNullOrEmpty(workspaceUrl))
            {
                this.Logger.Debug(
                    operationName,
                    $"SKIP: DB Workspace Notification (empty workspace url) on WorkspaceId : '{workspaceId}'");

                return;
            }

            try
            {
                (Uri baseUri, string audience, bool useArm) = await this.FrontdoorEngine
                    .GetDatabricksAccountApiEnpointAndAudience(customerTenantId, subscriptionId);
                string amlWorkspaceId = null;

                if (useArm)
                {
                    amlWorkspaceId = armNotification?.Update?.Properties?.Parameters?.AmlWorkspaceId?.Value<string>("value");
                }
                else
                {
                    amlWorkspaceId = accountApiNotification.Properties.Parameters?.AmlWorkspaceId?.Value<string>("value");
                }
                this.Logger.Debug(
                       operationName,
                       $"DB Workspace Notification - Linked AML Workspace Id : '{amlWorkspaceId}'");

                if (!string.IsNullOrEmpty(amlWorkspaceId))
                {
                    JToken amlWorkspace = await this.FrontdoorEngine
                                            .GetAmlWorkspace(customerTenantId, amlWorkspaceId, resourceProviderNamespace)
                                            .ConfigureAwait(false);

                    if (amlWorkspace == default(JToken))
                    {
                        this.Logger.Error(
                            operationName,
                            $"DB Workspace Notification - Linked AML Workspace Id : '{amlWorkspaceId}' does not exist.");

                        throw new ErrorResponseMessageException(
                            HttpStatusCode.Conflict,
                            ErrorResponseCode.LinkedAmlWorkspaceDoesNotExist,
                            ErrorResponseMessages.LinkedAmlWorkspaceDoesNotExist.ToLocalizedMessage(amlWorkspaceId));
                    }
                }
                var payload = useArm ? JToken.FromObject(armNotification) :
                    JToken.FromObject(accountApiNotification);
                this.Logger.Debug(
                    operationName,
                    $"[Feature Flag selection]START: DB Workspace {(useArm ? "PATCH" : "PUT")} Notification on WorkspaceId: '{workspaceId}'" +
                    $"Using {(useArm ? "ARM" : "AccountApi")}");
                await DatabricksAccountsManagerUtils.SendDBNotificationWithFlagControl(
                    this.FrontdoorEngine,
                    payload,
                    useArm ? new HttpMethod("PATCH") : new HttpMethod("PUT"),
                    customerTenantId,
                    useArm,
                    workspaceId,
                    baseUri,
                    apiVersion,
                    audience
                    ).ConfigureAwait(false);
                if (!useArm)
                {
                    // Instead of using AsyncRetry.Retry for in-process polling, we'll set up metadata for job-based polling
                    this.Logger.Debug(
                        operationName,
                        $"Setting up job-based polling for workspace update operation on WorkspaceId: '{workspaceId}'");

                    // Create a job metadata for the update operation
                    var resourceIdMatch = ResourceIdRequestMatch.FromFullyQualifiedId(workspaceId);
                    var jobMetadata = new ApplianceProvisioningJobMetadata
                    {
                        RequestCorrelationContext = RequestCorrelationContext.Current,
                        SubscriptionId = subscriptionId,
                        ResourceGroupName = resourceIdMatch.ResourceGroup,
                        ResourceType = this.ResourceType,
                        ResourceProviderNamespace = this.ResourceProviderNamespace,
                        ApplianceName = resourceIdMatch.ResourceName,
                        ProvidersLocation = entity.Location,
                        ApplianceProvisioningState = ApplianceProvisioningState.Initializing,
                        ApplianceWriteOperationName = this.ApplianceWriteOperationName,
                        CurrentRetryableErrorCount = 0,
                        CurrentDeploymentOperationExceptionRetryableErrorCount = 0,
                        ResourceOperation = ProvisioningOperation.AccountApiPolling,
                        ExecutionStartDateTime = DateTime.UtcNow,
                        ApplianceOperationType = ProvisioningOperation.Updating,
                        AccountApiPollingState = AccountApiPollingState.WaitingForUpdate,
                        AccountApiOperationId = workspaceId,
                        AccountApiOperationStartTime = DateTime.UtcNow,
                        AccountApiBaseUri = baseUri,
                        AccountApiAudience = audience
                    };

                    // Create and schedule a job for polling
                    await this.GetJobsDataProvider().CreateApplianceProvisioningJob(
                        subscriptionId: subscriptionId,
                        resourceGroupName: resourceIdMatch.ResourceGroup,
                        resourceType: this.ResourceType,
                        resourceProviderNamespace: this.ResourceProviderNamespace,
                        applianceName: resourceIdMatch.ResourceName,
                        location: entity.Location,
                        parameters: new InsensitiveDictionary<JToken>(),
                        applianceWriteOperationName: this.ApplianceWriteOperationName,
                        requestProcessingStartDateTime: DateTime.UtcNow,
                        delayJobStart: false,
                        provisioningOperation: ProvisioningOperation.AccountApiPolling).ConfigureAwait(false);

                    this.Logger.Debug(
                        operationName,
                        $"Created job for polling workspace update operation on WorkspaceId: '{workspaceId}'");
                }
            }
            catch (ErrorResponseMessageException errorResponseMessageException) when (errorResponseMessageException.ErrorCode == ErrorResponseCode.LinkedAmlWorkspaceDoesNotExist)
            {
                throw errorResponseMessageException;
            }
            catch (Exception)
            {
                throw new ErrorResponseMessageException(
                    HttpStatusCode.Conflict,
                    ErrorResponseCode.WorkspacePatchFailed,
                    ErrorResponseMessages.WorkspacePatchFailed.ToLocalizedMessage(workspaceId));
            }
        }

        /// <summary>
        /// Gets the appliance package.
        /// </summary>
        /// <param name="applianceEntity">The appliance entity.</param>
        private AppliancePackage GetAppliancePackage(ApplianceEntity applianceEntity)
        {
            // A corrupted workspace has no publisherPackageId property, so default to an empty string.
            string publisherPackageId = string.Empty;

            if (applianceEntity.Properties != null)
            {
                publisherPackageId = applianceEntity.Properties.PublisherPackageId;
            }

            // TODO(ilahat): Leave publisherPackageId in the older API version
            return this.GetValidatedMarketplaceAppliancePackage(publisherPackageId);
        }

        /// <summary>
        /// Gets the validated marketplace appliance package.
        /// </summary>
        /// <param name="publisherPackageId">The appliance publisher package Id.</param>
        private AppliancePackage GetValidatedMarketplaceAppliancePackage(string publisherPackageId)
        {
            var marketPlaceAppliancePackage = this
                .GetMarketPlaceAppliancePackageDataProvider()
                .FindMarketplaceAppliancePackage(publisherPackageId: publisherPackageId);

            Validation.MarketplaceAppliancePackage(
                appliancePackage: marketPlaceAppliancePackage,
                publisherPackageId: publisherPackageId);

            return marketPlaceAppliancePackage;
        }

        #endregion

        #region PATCH appliance

        /// <summary>
        /// Patches an appliance.
        /// </summary>
        /// <param name="incomingEntity">The incoming appliance entity.</param>
        /// <returns>The saved appliance entity</returns>
        public async Task<ApplianceEntity> PatchAppliance(ApplianceEntity incomingEntity)
        {
            string operationName = this.GetOperationName(Utilities.GetAsyncMethodName());

            string applianceId =
                Utilities.GetFullyQualifiedDatabricksResourceId(
                    incomingEntity.SubscriptionId,
                    incomingEntity.ResourceGroup,
                    incomingEntity.Name);

            var existingEntity = await this
                .GetApplianceDataProvider()
                .FindAppliance(
                    incomingEntity.SubscriptionId,
                    incomingEntity.ResourceGroup,
                    incomingEntity.Name)
                .ConfigureAwait(false);

            Validation.ApplicationExists(existingEntity, incomingEntity.Name);
            Validation.ApplicationPropertiesExistOnUpdate(existingEntity);
            Validation.ApplicationParametersExistOnUpdate(existingEntity);
            Validation.ApplicationSkuUpdatePatch(existingEntity.Sku, incomingEntity.Sku, this.Logger);


            if (incomingEntity.Properties.ManagedResourceGroupId != null)
            {
                Validation.ApplianceManagedResourceGroupIdMatch(
                    existingEntity.Properties.ManagedResourceGroupId,
                    incomingEntity.Properties.ManagedResourceGroupId);
            }

            var originalEntity = new ApplianceEntity(existingEntity);

            string workspaceId = Utilities.GetFullyQualifiedDatabricksResourceId(
                existingEntity.SubscriptionId,
                existingEntity.ResourceGroup,
                existingEntity.Name);

            var apiVersion = existingEntity.GetDatabricksBackendApiVersion(this.Logger);
            var publisherTenantId = this.GetPublisherTenantId(existingEntity);

            if (incomingEntity.SystemData != null)
            {
                existingEntity.SystemData = incomingEntity.SystemData;
            }

            try
            {

                if (existingEntity.GetComputeMode() == ComputeMode.Hybrid)
                {
                    var resourceGroupRequest =
                        ResourceGroupRequestMatch.FromFullyQualifiedId(
                            existingEntity.Properties.ManagedResourceGroupId.Trim());

                    await workspaceTagService.UpdateWorkspaceTag(
                        incomingEntity.Tags,
                        publisherTenantId,
                        resourceGroupRequest.ResourceGroup,
                        this.ResourceProviderNamespace,
                        existingEntity);
                }

                if (incomingEntity.Tags != null)
                {
                    this.Logger.Debug(operationName, $"Adding {incomingEntity.Tags.Count} tags for the workspace '{applianceId}'");

                    existingEntity.Tags = incomingEntity.Tags;

                    // Notifies DB for Workspace UPDATE

                    await this.DBWorkspaceUpdateNotificationWrapper(
                        existingEntity.SubscriptionId,
                        workspaceId,
                        this.ResourceProviderNamespace,
                        existingEntity,
                        apiVersion,
                        existingEntity.Properties?.WorkspaceUrl)
                        .ConfigureAwait(false);
                }

                await this
                    .GetApplianceDataProvider()
                    .ReplaceAppliance(existingEntity)
                    .ConfigureAwait(false);
            }
            catch (Exception exception)
            {
                this.Logger.Error(
                    operationName,
                    $"ROLLBACK: Failure to persist appliance entity object in table storage. DB Notification for Patch " +
                    $"on the Workspace '{workspaceId}', Api-version: {RequestCorrelationContext.Current.ApiVersion}. " +
                    $"Exception details: '{Utilities.FlattenException(exception)}");

                await this.DBWorkspaceUpdateNotificationWrapper(
                    existingEntity.SubscriptionId,
                    workspaceId,
                    this.ResourceProviderNamespace,
                    originalEntity,
                    apiVersion,
                    existingEntity.Properties?.WorkspaceUrl)
                    .ConfigureAwait(false);

                HandleResourceGroupOperationOrStorageException(existingEntity, exception);

                throw;
            }

            this.Logger.Debug(operationName, $"Patched the workspace '{applianceId}'");
            return existingEntity;
        }

        #endregion

        #region GET applications

        /// <summary>
        /// Gets the authentication tenant identifier.
        /// </summary>
        /// <param name="applianceEntity">The appliance entity.</param>
        private string GetPublisherTenantId(ApplianceEntity applianceEntity)
        {
            var appliancePackage = this
                .GetAppliancePackage(
                    applianceEntity: applianceEntity);
            return appliancePackage.TenantId;
        }

        /// <summary>
        /// Retrieves all applications within the subscription.
        /// </summary>
        /// <param name="subscriptionId">The subscription Id.</param>
        /// <param name="requestUri">The request uri.</param>
        public Task<SegmentedApplianceEntities> GetApplications(
            string subscriptionId,
            Uri requestUri)
        {
            Validation.Subscription(subscriptionId);

            return this.GetApplicationsInternal(
                subscriptionId: subscriptionId,
                resourceGroupName: string.Empty,
                requestUri: requestUri);
        }

        /// <summary>
        /// Retrieves all applications within the subscription.
        /// </summary>
        /// <param name="subscriptionId">The subscription Id.</param>
        /// <param name="resourceGroupName">Name of the resource group.</param>
        /// <param name="requestUri">The request uri.</param>
        public Task<SegmentedApplianceEntities> GetApplications(
            string subscriptionId,
            string resourceGroupName,
            Uri requestUri)
        {
            Validation.Subscription(subscriptionId);
            Validation.ResourceGroup(resourceGroupName);

            return this.GetApplicationsInternal(
                subscriptionId: subscriptionId,
                resourceGroupName: resourceGroupName,
                requestUri: requestUri);
        }

        /// <summary>
        /// Gets the applications.
        /// </summary>
        /// <param name="subscriptionId">The subscription identifier.</param>
        /// <param name="resourceGroupName">Name of the resource group.</param>
        /// <param name="requestUri">The request uri.</param>
        private async Task<SegmentedApplianceEntities> GetApplicationsInternal(
            string subscriptionId,
            string resourceGroupName,
            Uri requestUri)
        {
            var queryStringReader = new QueryStringReader(requestUri: requestUri);
            string operationName = this.GetOperationName(Utilities.GetAsyncMethodName());

            var applianceEntities = await this
                .GetApplianceDataProvider()
                .FindAppliancesSegmented(
                    subscriptionId: subscriptionId,
                    resourceGroupName: resourceGroupName,
                    top: queryStringReader.Top,
                    continuationToken: queryStringReader.ContinuationToken)
                .ConfigureAwait(continueOnCapturedContext: false);

            // Added logging to get the faulty workspace(if any) per subscription.
            applianceEntities.Entities.ForEach(entity =>
            {
                try
                {
                    this.PopulatePropertiesFromAppliancePackage(entity);
                }
                catch (Exception exception)
                {
                    this.Logger.Error(
                    operationName: operationName,
                    format: "Failed to populate properties for appliance {0}.",
                    arg0: entity.Name,
                    exception: exception);
                }
            });

            var segmentedResults = new SegmentedApplianceEntities()
            {
                ContinuationToken = applianceEntities.ContinuationToken,
                Entities = applianceEntities.Entities.Where(
                    applianceEntity =>
                        applianceEntity.Properties != null && applianceEntity.Properties.ProvisioningState != ProvisioningState.Deleted && applianceEntity.Location != null &&
                        applianceEntity.Location.TrimWhitespaces().Equals(Utilities.Region, StringComparison.CurrentCultureIgnoreCase)
                    )
            };

            return segmentedResults;
        }

        #endregion

        #region GET appliance

        /// <summary>
        /// Gets an application.
        /// </summary>
        /// <param name="subscriptionId">The subscription Id.</param>
        /// <param name="resourceGroupName">The resource group name.</param>
        /// <param name="applicationName">The application name.</param>
        public async Task<ApplianceEntity> GetApplication(
        string subscriptionId,
        string resourceGroupName,
        string applicationName)
        {
            var applianceEntity = await this.GetApplianceAsync(subscriptionId, resourceGroupName, applicationName);
            if (applianceEntity?.Properties?.Parameters != null)
            {
                applianceEntity.Properties.Parameters.CanonicalizeCustomVirtualNetworkIdProperty(this.Logger);
            }
            this.PopulatePropertiesFromAppliancePackage(applianceEntity: applianceEntity);
            return applianceEntity;
        }

        /// <summary>
        /// Get Appliance Object from data store
        /// </summary>
        /// <param name="subscriptionId">The subscription Id.</param>
        /// <param name="resourceGroupName">The resource group name.</param>
        /// <param name="workspaceName">The application name.</param>
        /// <returns></returns>
        private async Task<ApplianceEntity> GetApplianceAsync(
            string subscriptionId,
            string resourceGroupName,
            string workspaceName)
        {
            Validation.Subscription(subscriptionId);
            Validation.ResourceGroup(resourceGroupName);
            Validation.ResourceName(workspaceName);

            var existingEntity = await this
                .GetApplianceDataProvider()
                .FindAppliance(
                    subscriptionId: subscriptionId,
                    resourceGroupName: resourceGroupName,
                    applianceName: workspaceName)
                .ConfigureAwait(continueOnCapturedContext: false);

            Validation.ApplicationExists(existingEntity, workspaceName);

            if (existingEntity.Properties?.ProvisioningState == ProvisioningState.Deleted)
            {
                this.Logger.Debug(
                    operationName: "ApplianceEngine.GetAppliance",
                    format: "Appliance '{0}' exists with provisioning state '{1}'. No change required in storage.",
                    arg0: existingEntity.GetFullyQualifiedResourceId(),
                    arg1: existingEntity.Properties.ProvisioningState);

                throw new ErrorResponseMessageException(
                    httpStatus: HttpStatusCode.NotFound,
                    errorCode: ErrorResponseCode.ApplianceNotFound,
                    errorMessage: ErrorResponseMessages.ApplianceNotFound.ToLocalizedMessage(workspaceName));
            }

            return existingEntity;
        }

        #endregion

        #region GET application operation status

        /// <summary>
        /// Gets status of an application operation.
        /// </summary>
        /// <param name="subscriptionId">The subscription Id.</param>
        /// <param name="resourceGroupName">The resource group name.</param>
        /// <param name="operationId">The application operation Id.</param>
        /// <param name="applicationAsyncOperation">The decoded application async operation request.</param>
        public async Task<AsyncOperationJobMetadata> GetApplicationOperationStatus(
            string subscriptionId,
            string resourceGroupName,
            string operationId,
            ResourceAsyncOperation applicationAsyncOperation)
        {
            this.Logger.Debug(
                operationName: "AsyncOperationController.GetOperationResultById",
                message: $"Retrieving the operation status for the operation id {operationId}");

            Validation.Subscription(subscriptionId);
            Validation.ResourceGroup(resourceGroupName);

            Validation.ApplianceAsyncOperation(
                applianceAsyncOperation: applicationAsyncOperation,
                operationId: operationId,
                subscriptionId: subscriptionId,
                resourceGroupName: resourceGroupName);

            this.Logger.Debug(
                operationName: "AsyncOperationController.GetOperationResultById",
                message: $"Retrieving the job object for job id: {applicationAsyncOperation.JobId}");

            var job = await this
                .GetJobsDataProvider()
                .GetLocalJob(jobPartition: AsyncOperationJobMetadata.GetJobPartition(subscriptionId), jobId: applicationAsyncOperation.JobId)
                .ConfigureAwait(continueOnCapturedContext: false);

            #region Secondary Jobs Data
            /*
             * We leverage Resource Stack (Background Job Scheduler) as our worker framework for two purposes, one is the runtime of the ADB RP feature, and the other is the consistency job for the queueing feature.
             * To reduce the risk of conflict between the Cloud Service and Service Fabric instances, we have decided to have a new storage account provision for running BJS.
             * Since jobs data has transactional data, we have decided to decommission old jobs data storage after migration.
             * Following code-block is added for short-term to support read from both jobs data and should be removed once service is stabilized and running 100% in Service Fabric.
            */

            if (job == null)
            {
                try
                {
                    this.Logger.Debug(
                        operationName: "AsyncOperationController.GetOperationResultById",
                        message: $"Retrieving the job object for job id: {applicationAsyncOperation.JobId} from secondary jobs data.");

                    job = await this
                        .GetSecondaryJobsDataProvider()
                        .GetLocalJob(jobPartition: AsyncOperationJobMetadata.GetJobPartition(subscriptionId), jobId: applicationAsyncOperation.JobId)
                        .ConfigureAwait(continueOnCapturedContext: false);
                }
                catch (Exception exception)
                {
                    this.Logger.Error(
                        exception,
                        "AsyncOperationController.GetOperationResultById",
                        $"Unable to retrieve the job object for job id: {applicationAsyncOperation.JobId} from secondary jobs data.");
                }
            }
            #endregion

            Validation.AsyncOperationExists(job, operationId);

            this.Logger.Debug(
                operationName: "AsyncOperationController.GetOperationResultById",
                message: $"The job id: {applicationAsyncOperation.JobId} has the state {job.State.ToString()}");

            if (job.State != JobState.Enabled)
            {
                return job.Metadata.FromJson<AsyncOperationJobMetadata>();
            }

            return null;
        }

        /// <summary>
        /// Gets status of an application operation.
        /// </summary>
        /// <param name="subscriptionId">The subscription Id.</param>
        /// <param name="operationId"></param>
        /// <param name="applicationAsyncOperation"></param>
        /// <returns></returns>
        public async Task<AsyncOperationJobMetadata> GetApplicationOperationStatus(
            string subscriptionId,
            string operationId,
            ResourceAsyncOperation applicationAsyncOperation)
        {
            this.Logger.Debug(
                operationName: "AsyncOperationController.GetOperationResultById",
                message: $"Retrieving the operation status for the operation id {operationId}");

            Validation.Subscription(subscriptionId);

            if (string.IsNullOrEmpty(applicationAsyncOperation?.FullyQualifiedApplianceId))
            {
                throw new ErrorResponseMessageException(
                    httpStatus: HttpStatusCode.BadRequest,
                    errorCode: ErrorResponseCode.InvalidOperationId,
                    errorMessage: ErrorResponseMessages.InvalidOperationId.ToLocalizedMessage());
            }

            this.Logger.Debug(
                operationName: "AsyncOperationController.GetOperationResultById",
                message: $"Retrieving the job object for job id: {applicationAsyncOperation.JobId}");

            var job = await this
                .GetJobsDataProvider()
                .GetLocalJob(jobPartition: AsyncOperationJobMetadata.GetJobPartition(subscriptionId), jobId: applicationAsyncOperation.JobId)
                .ConfigureAwait(continueOnCapturedContext: false);

            Validation.AsyncOperationExists(job, operationId);

            this.Logger.Debug(
                operationName: "AsyncOperationController.GetOperationResultById",
                message: $"The job id: {applicationAsyncOperation.JobId} has the state {job.State.ToString()}");

            if (job.State != JobState.Enabled)
            {
                return job.Metadata.FromJson<AsyncOperationJobMetadata>();
            }

            return null;
        }
        #endregion

        #region DELETE application

        /// <summary>
        /// Deletes an application.
        /// </summary>
        /// <param name="subscriptionId">The subscription Id.</param>
        /// <param name="resourceGroupName">The resource group name.</param>
        /// <param name="workspaceName">The application name.</param>
        /// <param name="retainUcData">The customer input for whether to retainUcData or not.</param>
        /// <param name="requestProcessingStartDateTime"> the processing start date time of the request</param>
        public async Task<ApplianceEntity> DeleteApplication(
            string subscriptionId,
            string resourceGroupName,
            string workspaceName,
            bool retainUcData,
            DateTime requestProcessingStartDateTime)
        {
            Validation.Subscription(subscriptionId);
            Validation.ResourceGroup(resourceGroupName);
            Validation.ResourceName(workspaceName);

            string operationName = this.GetOperationName(Utilities.GetAsyncMethodName());

            var applianceEntity = await this
                .GetApplianceDataProvider()
                .FindAppliance(
                    subscriptionId: subscriptionId,
                    resourceGroupName: resourceGroupName,
                    applianceName: workspaceName)
                .ConfigureAwait(continueOnCapturedContext: false);

            if (applianceEntity == null || applianceEntity.Properties?.ProvisioningState == ProvisioningState.Deleted)
            {
                this.Logger.Debug(
                    operationName,
                    "Skip deletion of the workspace '{0}' in rg '{1}' and subscription '{2}'. ProvisioningState: '{3}'. Returning HttpStatusCode NoContent",
                    workspaceName,
                    resourceGroupName,
                    subscriptionId,
                    applianceEntity?.Properties?.ProvisioningState?.ToString());

                return null;
            }

            this.Logger.Debug(operationName, $"Workspace to delete: '{applianceEntity.GetFullyQualifiedId(this.ResourceType)}'." +
                $" ProvisioningState: '{applianceEntity.Properties?.ProvisioningState?.ToString()}'" +
                $" customer input for retainUcData: {retainUcData}");

            if (applianceEntity.Properties == null)
            {
                this.Logger.Debug(
                    operationName,
                    "Deleting the workspace '{0}' as workspace properties is null.",
                    workspaceName);

                // Corrupted workspaces should not use a delete job and just be deleted via Appliance Data Provider.
                await this
                    .GetApplianceDataProvider()
                    .DeleteAppliance(applianceEntity)
                    .ConfigureAwait(continueOnCapturedContext: false);

                return null;
            }

            Validation.ApplianceDeletionInProgress(applianceEntity);
            Validation.ApplicanceCreationInProgress(applianceEntity);
            Validation.ApplicanceUpdateInProgress(applianceEntity);

            var tenantId = this.GetTenantId(appliance: applianceEntity);
            Validation.TenantId(tenantId);

            this.Logger.Debug(
                operationName,
                $"Creating de-provisioning job for the workspace '{applianceEntity.GetFullyQualifiedResourceId()}'." +
                $"ProvisioningState: '{applianceEntity.Properties.ProvisioningState?.ToString()}'," +
                $" retainUcData: {retainUcData}");

            await this
                .PrepareApplicationForDelete(applianceEntity)
                .ConfigureAwait(continueOnCapturedContext: false);

            if (applianceEntity.GetComputeMode() == ComputeMode.Serverless)
            {
                var serverlessApplianceEntity = await this.DeleteServerlessApplication(
                    subscriptionId: subscriptionId,
                    resourceGroupName: resourceGroupName,
                    workspaceName: workspaceName,
                    requestProcessingStartDateTime: requestProcessingStartDateTime)
                    .ConfigureAwait(continueOnCapturedContext: false);

                return serverlessApplianceEntity;
            }

            await this
                .GetJobsDataProvider()
                .CreateApplianceDeprovisioningJob(
                    subscriptionId: subscriptionId,
                    resourceGroupName: resourceGroupName,
                    resourceType: this.ResourceType,
                    resourceProviderNamespace: this.ResourceProviderNamespace,
                    applianceName: workspaceName,
                    location: applianceEntity.Location,
                    retainUcData: retainUcData,
                    requestProcessingStartDateTime: requestProcessingStartDateTime)
                .ConfigureAwait(continueOnCapturedContext: false);

            return applianceEntity;
        }

        /// <summary>
        /// Prepares the application for delete operation.
        /// </summary>
        /// <param name="application">The application object.</param>
        private async Task PrepareApplicationForDelete(ApplianceEntity application)
        {
            application.Properties.ProvisioningState = ProvisioningState.Deleting;
            application.Metadata.OperationStatuses[this.ApplianceDeleteOperationName]
                = new ApplicationOperationStatus { Status = ProvisioningState.Deleting };

            await this
                .GetApplianceDataProvider()
                .SaveAppliance(application)
                .ConfigureAwait(continueOnCapturedContext: false);
        }

        #endregion

        #region Deny assignments

        /// <summary>
        /// Refresh Permissions for a workspace.
        /// </summary>
        /// <param name="subscriptionId">The subscription Id.</param>
        /// <param name="resourceGroupName">The resource group name.</param>
        /// <param name="applicationName">The application name.</param>
        public async Task<ApplianceEntity> RefreshPermissions(
            string subscriptionId,
            string resourceGroupName,
            string applicationName)
        {
            string operationName = this.GetOperationName(Utilities.GetAsyncMethodName());

            string applicationResourceId = Utilities.GetFullyQualifiedDatabricksResourceId(
                subscriptionId,
                resourceGroupName,
                applicationName);

            this.Logger.Debug(operationName, $"Creating a new refresh permission job for the workspace '{applicationResourceId}'");

            Validation.Subscription(subscriptionId);
            Validation.ResourceGroup(resourceGroupName);
            Validation.ResourceName(applicationName);

            var applianceEntity = await this
                .GetApplianceDataProvider()
                .FindAppliance(
                    subscriptionId,
                    resourceGroupName,
                    applicationName)
                .ConfigureAwait(false);

            Validation.ApplicationExists(applianceEntity, applicationName);
            Validation.ApplicationStateAllowsToRefreshPermissions(applianceEntity.Properties.ProvisioningState, applicationResourceId);

            await this
                .GetJobsDataProvider()
                .CreateRefreshPermissionJob(
                    subscriptionId,
                    resourceGroupName,
                    applianceEntity.Name,
                    this.ResourceType,
                    this.ResourceProviderNamespace)
                .ConfigureAwait(false);

            var jobId = ApplicationRefreshPermissionsJobMetadata.GetJobId(resourceGroupName, applicationName);

            this.Logger.Debug(operationName, $"Created a new refresh permission job '{jobId}' for the workspace '{applicationResourceId}' with managed rg '{applianceEntity.Properties.ManagedResourceGroupId}'");

            return applianceEntity;
        }

        /// <summary>
        /// Update deny assignment not actions for a workspace.
        /// </summary>
        /// <param name="subscriptionId">The subscription Id.</param>
        /// <param name="resourceGroupName">The resource group name.</param>
        /// <param name="applicationName">The application name.</param>
        /// <param name="denyAssignmentManagementDefinition">The deny assignment management action definition object created from the request content.</param>
        public async Task UpdateDenyAssignment(
            string subscriptionId,
            string resourceGroupName,
            string applicationName,
            DenyAssignmentManagementDefinition denyAssignmentManagementDefinition)
        {
            string operationName = this.GetOperationName(Utilities.GetAsyncMethodName());

            string applicationResourceId = Utilities.GetFullyQualifiedDatabricksResourceId(
                subscriptionId,
                resourceGroupName,
                applicationName);

            this.Logger.Debug(operationName, $"Update deny assignment not actions for a workspace '{applicationResourceId}' in tenant '{RequestCorrelationContext.Current.GetHomeTenantId()}'");

            Validation.Subscription(subscriptionId);
            Validation.ResourceGroup(resourceGroupName);
            Validation.ResourceName(applicationName);
            Validation.DenyAssignment(denyAssignmentManagementDefinition, ApplicationApiEngine.DisableValidateNotActions);

            string managedResourceGroupId = denyAssignmentManagementDefinition.ManagedResourceGroupId;

            if (string.IsNullOrWhiteSpace(managedResourceGroupId))
            {
                var applianceEntity = await this
                    .GetApplianceDataProvider()
                    .FindAppliance(
                        subscriptionId,
                        resourceGroupName,
                        applicationName)
                    .ConfigureAwait(false);

                Validation.ApplicationExists(applianceEntity, applicationName);
                Validation.ApplicationStateAllowsToUpdateDenyAssignment(applianceEntity.Properties.ProvisioningState, applicationResourceId);
                managedResourceGroupId = applianceEntity.Properties.ManagedResourceGroupId;
            }

            this.Logger.Debug(
                operationName,
                $"Getting existing deny assignment to update not actions for managed rg: '{managedResourceGroupId}' of the workspace '{applicationResourceId}' in tenant '{RequestCorrelationContext.Current.GetHomeTenantId()}' with not actions: {denyAssignmentManagementDefinition.NotActions}");

            var systemDenyAssignments = await this.FrontdoorEngine
                .GetSystemDenyAssignments(
                    RequestCorrelationContext.Current.GetHomeTenantId(),
                    managedResourceGroupId,
                    ProviderConstants.Authorizations.EveryonePrincipalId,
                    this.ResourceProviderNamespace)
                .ConfigureAwait(false);

            if (!systemDenyAssignments.CoalesceEnumerable().Any())
            {
                this.Logger.Error(
                    operationName,
                    $"Request to update the system deny assignment with the scope '{managedResourceGroupId}' failed. No system deny assignments exist.");

                throw new ErrorResponseMessageException(
                    HttpStatusCode.BadRequest,
                    ErrorResponseCode.DenyAssignmentMissing,
                    ErrorResponseMessages.DenyAssignmentMissing.ToLocalizedMessage(managedResourceGroupId));
            }

            if (systemDenyAssignments.Length > 1)
            {
                this.Logger.Error(
                    operationName,
                    $"Request to update the system deny assignment with the scope '{managedResourceGroupId}' failed. Unexpected count '{systemDenyAssignments.Length}'");

                throw new ErrorResponseMessageException(
                    HttpStatusCode.BadRequest,
                    ErrorResponseCode.DenyAssignmentsError,
                    ErrorResponseMessages.DenyAssignmentCreateOrUpdateError.ToLocalizedMessage(systemDenyAssignments.Length, managedResourceGroupId));
            }

            var systemDenyAssignmentDefinition = systemDenyAssignments.Single();
            var denyAssignmentResourceId = this.FrontdoorEngine.GetSystemDenyAssignmentResourceId(systemDenyAssignmentDefinition, managedResourceGroupId);

            var excludedActions = denyAssignmentManagementDefinition.NotActions.SplitAndRemoveWhitespaces(ProviderConstants.Databricks.ConfigValueSemicolonSeparator);

            if (systemDenyAssignmentDefinition.Properties.GetProperty(ProviderConstants.Authorizations.PermissionsProperty) == null
                || systemDenyAssignmentDefinition.Properties[ProviderConstants.Authorizations.PermissionsProperty].First() == null
                || systemDenyAssignmentDefinition.Properties[ProviderConstants.Authorizations.PermissionsProperty].First()[ProviderConstants.Authorizations.NotActionsProperty] == null)
            {
                Utilities.TrySerializeObject(systemDenyAssignmentDefinition.Properties, this.Logger, out string properties);
                this.Logger.Debug(operationName, $"Error updating deny assignment not actions with not actions. Missing properties: {properties}");

                throw new ErrorResponseMessageException(
                    HttpStatusCode.BadRequest,
                    ErrorResponseCode.DenyAssignmentsError,
                    ErrorResponseMessages.DenyAssignmentMissing.ToLocalizedMessage(managedResourceGroupId));
            }

            var previousNotActions = systemDenyAssignmentDefinition.Properties[ProviderConstants.Authorizations.PermissionsProperty].First()[ProviderConstants.Authorizations.NotActionsProperty].ToObject<string[]>();

            this.Logger.Debug(operationName, $"Updating deny assignment not actions from: '{string.Join(";", previousNotActions)}' to: '{denyAssignmentManagementDefinition.NotActions}'");

            systemDenyAssignmentDefinition.Properties[ProviderConstants.Authorizations.PermissionsProperty].First()[ProviderConstants.Authorizations.NotActionsProperty] = JArray.FromObject(excludedActions);

            await this.FrontdoorEngine
                .CreateOrUpdateDenyAssignmentsWithResourceId(
                    RequestCorrelationContext.Current.GetHomeTenantId(),
                    managedResourceGroupId,
                    denyAssignmentResourceId,
                    systemDenyAssignmentDefinition,
                    this.ResourceProviderNamespace)
                .ConfigureAwait(false);

            this.Logger.Debug(
                operationName,
                $"Updated deny assignment '{denyAssignmentResourceId}' not actions for managed rg: '{managedResourceGroupId}' of the workspace '{applicationResourceId}' with not actions: {denyAssignmentManagementDefinition.NotActions}");

            return;
        }

        #endregion

        #region Workspace refresh Operations

        /// <summary>
        /// Refresh workspaces with details like URL, workspace id from the <c>Databricks</c> control plane.
        /// </summary>
        /// <param name="subscriptionId">The subscription Id.</param>
        /// <param name="resourceGroupName">The resource group name.</param>
        /// <param name="applicationName">The application name.</param>
        /// <param name="refreshWorkspacesActionDefinition">The refresh workspace action definition.</param>
        public async Task<HttpStatusCode> RefreshWorkspaces(
            string subscriptionId,
            string resourceGroupName,
            string applicationName,
            RefreshWorkspacesActionDefinition refreshWorkspacesActionDefinition)
        {
            string operationName = this.GetOperationName(Utilities.GetAsyncMethodName());

            string applicationResourceId = Utilities.GetFullyQualifiedDatabricksResourceId(
                subscriptionId,
                resourceGroupName,
                applicationName);

            Validation.Subscription(subscriptionId);
            Validation.ResourceGroup(resourceGroupName);
            Validation.ResourceName(applicationName);

            var applianceEntity = await this
                .GetApplianceDataProvider()
                .FindAppliance(
                    subscriptionId,
                    resourceGroupName,
                    applicationName)
                .ConfigureAwait(false);

            Validation.ApplicationExists(applianceEntity, applianceEntity?.Name);

            Validation.ValidateProvisioningState(applianceEntity, refreshWorkspacesActionDefinition);

            if (refreshWorkspacesActionDefinition.ProvisioningState == ProvisioningState.Succeeded ||
                refreshWorkspacesActionDefinition.ProvisioningState == ProvisioningState.Failed)
            {
                applianceEntity.Properties.ProvisioningState = refreshWorkspacesActionDefinition.ProvisioningState;

                applianceEntity.Properties.WorkspaceId = !string.IsNullOrEmpty(refreshWorkspacesActionDefinition.WorkspaceId) ? refreshWorkspacesActionDefinition.WorkspaceId : applianceEntity.Properties.WorkspaceId;
                applianceEntity.Properties.WorkspaceUrl = !string.IsNullOrEmpty(refreshWorkspacesActionDefinition.WorkspaceUrl) ? refreshWorkspacesActionDefinition.WorkspaceUrl : applianceEntity.Properties.WorkspaceUrl;

                await this
                    .GetApplianceDataProvider()
                    .ReplaceAppliance(applianceEntity)
                    .ConfigureAwait(false);

                this.Logger.Debug(operationName, $"Workspace Updated  '{applicationResourceId}'. Provisioning State: '{applianceEntity.Properties?.ProvisioningState}', Workspace ID: '{applianceEntity.Properties?.WorkspaceId}', Workspace URL: '{applianceEntity.Properties?.WorkspaceUrl}'");
            }
            else if (refreshWorkspacesActionDefinition.ProvisioningState == ProvisioningState.Deleted)
            {
                await this
                    .GetApplianceDataProvider()
                    .DeleteAppliance(applianceEntity)
                    .ConfigureAwait(continueOnCapturedContext: false);

                this.Logger.Debug(operationName, $"Workspace Deleted '{applicationResourceId}'.");
            }
            else
            {
                return HttpStatusCode.NoContent;
            }

            return HttpStatusCode.OK;
        }

        #endregion

        #region Workspace Table Entity Update Operations

        /// <summary>
        /// Upate workspace table entity with json format workspace properties.
        /// </summary>
        /// <param name="subscriptionId">The subscription Id.</param>
        /// <param name="resourceGroupName">The resource group name.</param>
        /// <param name="applicationName">The application name.</param>
        /// <param name="entityJson">The workspace property json string.</param>
        public async Task<HttpStatusCode> UpdateWorkspaceParameters(
            string subscriptionId,
            string resourceGroupName,
            string applicationName,
            string entityJson)
        {
            string operationName = this.GetOperationName(Utilities.GetAsyncMethodName());

            string applicationResourceId = Utilities.GetFullyQualifiedDatabricksResourceId(
                subscriptionId,
                resourceGroupName,
                applicationName);

            Validation.Subscription(subscriptionId);
            Validation.ResourceGroup(resourceGroupName);
            Validation.ResourceName(applicationName);

            var applianceEntity = await this
                .GetApplianceDataProvider()
                .FindAppliance(
                    subscriptionId,
                    resourceGroupName,
                    applicationName)
                .ConfigureAwait(false);

            Validation.ApplicationExists(applianceEntity, applianceEntity?.Name);

            this.Logger.Debug(operationName, $"Updating workspace table entity parameter for '{applicationResourceId}' from '{applianceEntity.Properties?.Parameters?.ToJson()}'");

            applianceEntity.PopulateWorkspaceParameterFromJsonString(entityJson, this.Logger);

            await this
                    .GetApplianceDataProvider()
                    .ReplaceAppliance(applianceEntity)
                    .ConfigureAwait(false);

            this.Logger.Debug(operationName, $"Workspace table entity update completed for '{applicationResourceId}' to '{applianceEntity.Properties?.Parameters?.ToJson()}'");

            return HttpStatusCode.OK;
        }

        #endregion

        #region Private Endpoints Refresh Operations
        /// <summary>
        /// Refresh private endpoints.
        /// </summary>
        /// <param name="subscriptionId">The subscription Id.</param>
        /// <param name="resourceGroupName">The resource group name.</param>
        /// <param name="applicationName">The application name.</param>
        /// <param name="connectionName">The connection name.</param>
        /// <param name="refreshPrivateEndpointsActionDefinition">The refresh private endpoints action definition.</param>
        public async Task<HttpStatusCode> RefreshPrivateEndpoints(
            string subscriptionId,
            string resourceGroupName,
            string applicationName,
            string connectionName,
            RefreshPrivateEndpointsActionDefinition refreshPrivateEndpointsActionDefinition)
        {
            string operationName = this.GetOperationName(Utilities.GetAsyncMethodName());

            string applicationResourceId = Utilities.GetFullyQualifiedDatabricksResourceId(
                subscriptionId,
                resourceGroupName,
                applicationName);

            Validation.Subscription(subscriptionId);
            Validation.ResourceGroup(resourceGroupName);
            Validation.ResourceName(applicationName);

            var applianceEntity = await this
                .GetApplianceDataProvider()
                .FindAppliance(
                    subscriptionId,
                    resourceGroupName,
                    applicationName)
                .ConfigureAwait(false);

            Validation.ApplicationExists(applianceEntity, applianceEntity?.Name);

            var remotePrivateEndpointConnectionId = RemotePrivateEndpointConnection.GetRemotePrivateEndpointConnectionId(applicationResourceId, connectionName);

            if (applianceEntity.Properties?.PrivateEndpointConnectionProxies == null
                || !applianceEntity.Properties.PrivateEndpointConnectionProxies.Any(proxy =>
                        proxy.GetRemotePrivateEndpointConnectionId(applicationResourceId).EqualsInsensitively(remotePrivateEndpointConnectionId)))
            {
                this.Logger.Debug(operationName, $"The PEC '{remotePrivateEndpointConnectionId}' is not found");

                throw new ErrorResponseMessageException(
                    HttpStatusCode.NotFound,
                    ErrorResponseCode.PrivateEndpointConnectionsNotFound,
                    ErrorResponseMessages.PrivateEndpointConnectionsNotFound.ToLocalizedMessage(connectionName, applianceEntity.Name));
            }

            if (refreshPrivateEndpointsActionDefinition.ProvisioningState == PrivateEndpointConnectionProvisioningState.Failed ||
                refreshPrivateEndpointsActionDefinition.ProvisioningState == PrivateEndpointConnectionProvisioningState.Succeeded)
            {
                applianceEntity
                .Properties
                .PrivateEndpointConnectionProxies.First(proxy => proxy.GetRemotePrivateEndpointConnectionId(applicationResourceId).EqualsInsensitively(remotePrivateEndpointConnectionId))
                .ProvisioningState = refreshPrivateEndpointsActionDefinition.ProvisioningState;

                try
                {
                    await this
                        .GetApplianceDataProvider()
                        .ReplaceAppliance(applianceEntity)
                        .ConfigureAwait(false);

                    this.Logger.Debug(operationName, $"Private Endpoint Updated  '{remotePrivateEndpointConnectionId}'. Provisioning State: '{refreshPrivateEndpointsActionDefinition.ProvisioningState}'");
                }
                catch (Exception exception)
                {
                    this.Logger.Error(
                        operationName,
                        $"Failed to save workspace to table storage '{applicationResourceId}', Api-version: {RequestCorrelationContext.Current.ApiVersion}. Exception details: '{Utilities.FlattenException(exception)}");

                    throw;
                }
            }
            else
            {
                return HttpStatusCode.NoContent;
            }

            return HttpStatusCode.OK;
        }

        #endregion

        #region private helper methods

        /// <summary>
        /// Validates that the subscription has the ComputeMode feature enabled if properties are present in request
        /// </summary>
        /// <param name="incomingEntity"></param>
        /// <returns></returns>
        /// <exception cref="ErrorResponseMessageException"></exception>
        private async Task ValidateServerlessWorkspaceFeatureEnabled(ApplianceEntity incomingEntity)
        {
            string operationName = this.GetOperationName(Utilities.GetAsyncMethodName());
            this.Logger.Debug(operationName, $"Validating Serverless Workspace feature (compute mode property) enrollment for subscription: " +
                $"{incomingEntity.SubscriptionId}, with workspceType: '{incomingEntity.Properties.ComputeMode}'");

            if (incomingEntity.Properties.ComputeMode != null)
            {
                var isServerlessEnabled = await Utilities
                        .IsServerlessWorkspaceFeatureEnabled(
                            ProviderConstants.Databricks.ResourceProviderNamespace,
                            incomingEntity.SubscriptionId,
                            this.Logger,
                            this.FrontdoorEngine)
                        .ConfigureAwait(continueOnCapturedContext: false);

                if (!isServerlessEnabled)
                {
                    this.Logger.Debug(operationName, $"Serverless Workspace feature (compute mode property) is NOT supported for this subscription");
                    throw new ErrorResponseMessageException(
                        HttpStatusCode.BadRequest,
                        ErrorResponseCode.ServerlessWorkspaceFeatureNotEnabled,
                        ErrorResponseMessages.ServerlessWorkspaceFeatureNotEnabled.ToLocalizedMessage(incomingEntity.SubscriptionId));
                }

                this.Logger.Debug(operationName, $"Serverless workspace feature (compute mode property) is supported for this subscription");
            }
        }

        /// <summary>
        /// Populates appliance properties from an associated appliance package.
        /// </summary>
        /// <param name="applianceEntity">The appliance entity.</param>
        /// <param name="appliancePackage">The application package.</param>
        private void PopulatePropertiesFromAppliancePackage(ApplianceEntity applianceEntity, AppliancePackage appliancePackage = null)
        {
            if (appliancePackage == null)
            {
                appliancePackage = this
                    .GetAppliancePackage(applianceEntity: applianceEntity);
            }

            // Note(ilahat): Handle orphaned applications in case the backing application definition has been deleted
            if (appliancePackage != null && applianceEntity.Properties != null)
            {
                applianceEntity.Properties.Authorizations = appliancePackage.Authorizations?.Select(auth => new ApplicationAuthorizationEntity()
                {
                    PrincipalId = auth.PrincipalId,
                    RoleDefinitionId = auth.RoleDefinitionId
                }).ToArray();

                if (this.ResourceProviderNamespace.EqualsInsensitively(ProviderConstants.Databricks.ResourceProviderNamespace))
                {
                    applianceEntity.Properties.PublisherPackageId = null;
                }
            }
        }

        /// <summary>
        /// Gets the tenant ID for GET request.
        /// </summary>
        /// <param name="appliance">The appliance.</param>
        private string GetTenantId(ApplianceEntity appliance)
        {
            // TODO(ilahat): Leave publisherPackageId in the older API version
            var marketplaceAppliancePackage = this
                .GetMarketPlaceAppliancePackageDataProvider()
                .FindMarketplaceAppliancePackage(publisherPackageId: appliance.Properties.PublisherPackageId);

            return marketplaceAppliancePackage.TenantId;
        }

        /// <summary>
        /// Gets the application client details
        /// </summary>
        private static ApplicationClientDetailsEntity GetApplicationClientDetailsEntity()
        {
            return new ApplicationClientDetailsEntity
            {
                Oid = RequestCorrelationContext.Current.PrincipalOid,
                Puid = RequestCorrelationContext.Current.GetPrincipalLegacyPuid(),
                ApplicationId = RequestCorrelationContext.Current.ClientAppId
            };
        }

        /// <summary>
        /// Get Publisher Package Id.
        /// </summary>
        /// <param name="applianceEntity">The appliance entity.</param>
        /// <returns>Returns API version.</returns>
        private async Task<string> GetPublisherPackageId(ApplianceEntity applianceEntity)
        {
            var operationName = this.GetOperationName(Utilities.GetAsyncMethodName());

            this.Logger.Debug(operationName, "Getting the publisher package id for the workspace");

            Dictionary<string, string> existingMappings = this.ProviderConfiguration.GetValidatedPublisherPackageIdMappings();

            var apiVersion = ProviderConstants.ApiVersion20180401;

            try
            {
                var registeredFeatures = await this.FrontdoorEngine.GetRegisteredFeaturesInSubscription(
                        RequestCorrelationContext.Current.GetHomeTenantId(),
                        applianceEntity.SubscriptionId,
                        ProviderConstants.Databricks.ResourceProviderNamespace).ConfigureAwait(false);

                if (applianceEntity.IsApplianceEntityFedRamp(this.Logger))
                {
                    this.Logger.Debug(operationName, "Workspace is FedRamp enabled");
                    var publisherPackageId = existingMappings[ProviderConstants.ApiVersion20180401];
                    this.Logger.Debug(operationName, $"Returning publisher package id {publisherPackageId}");
                    return publisherPackageId;
                }
                else if (registeredFeatures.Contains(ProviderConstants.Databricks.MicrosoftDatabricksDevControlPlaneFeature))
                {
                    this.Logger.Debug(operationName, "The workspace is in a dev subscription.");
                    apiVersion = ProviderConstants.ApiVersion20180301;
                }
                else if (registeredFeatures.Contains(ProviderConstants.Databricks.MicrosoftDatabricksStagingControlPlaneFeature))
                {
                    this.Logger.Debug(operationName, "The workspace is in a staging subscription.");
                    apiVersion = ProviderConstants.ApiVersion20180315;
                }
            }
            catch (Exception exception)
            {
                this.Logger.Debug(operationName, $"Error getting Databricks feature  for subscription '{applianceEntity.SubscriptionId}'. Exception: {Utilities.FlattenException(exception)}");

                throw;
            }

            this.Logger.Debug(operationName, $"Api version for the purposes of identifying the publisher package id is {apiVersion}");

            this.Logger.Debug(
                operationName,
                $"Existing package id mappings are {string.Join(Environment.NewLine, existingMappings.Select(kvp => kvp.Key + ":" + kvp.Value))}");

            return existingMappings[apiVersion];
        }

        /// <summary>
        /// Validates the accessConnector is configured with managed identity or not and populate it from the connector entity.
        /// </summary>
        /// <param name="incomingEntity">incoming appliance entity</param>
        /// <returns>if not validation failure exists gracefully</returns>
        /// <exception cref="ErrorResponseMessageException">throws exception upon validation failures</exception>
        private async Task ValidateAndPopulateAccessConnectorIdentity(ApplianceEntity incomingEntity)
        {
            var operationName = this.GetOperationName(Utilities.GetAsyncMethodName());

            this.Logger.Debug(operationName,
                $"Validating MI configuration on Access Connector linked to workspace.");

            var accessConnectorId = incomingEntity.Properties.AccessConnector;

            if (accessConnectorId != null)
            {
                var resourceIdMatch = ResourceIdRequestMatch.FromFullyQualifiedId(accessConnectorId.Id);

                var connectorEntity = await this.GetAccessConnectorDataProvider()
                    .FindAccessConnector(
                        resourceIdMatch.SubscriptionId,
                        resourceIdMatch.ResourceGroup,
                        resourceIdMatch.ResourceName)
                    .ConfigureAwait(false);

                this.Logger.Debug(operationName,
                    $"Checking Access Connector '{accessConnectorId.Id}' exists or not.");

                if (connectorEntity == null)
                {
                    this.Logger.Debug(operationName,
                        $"Access Connector '{accessConnectorId.Id}' not found, it might have been deleted!.");

                    throw new ErrorResponseMessageException(
                        HttpStatusCode.BadRequest,
                        ErrorResponseCode.AccessConnectorNotFound,
                        ErrorResponseMessages.AccessConnectorNotFound.ToLocalizedMessage(accessConnectorId.Id));
                }

                this.Logger.Debug(operationName,
                    $"Checking Access Connector '{accessConnectorId.Id}' has identity being enabled on it.");

                if (connectorEntity.Identity == null ||
                    connectorEntity.Identity.Type == IdentityType.None)
                {
                    this.Logger.Debug(operationName,
                        $"Access Connector '{accessConnectorId.Id}' doesn't have MI configured.");

                    throw new ErrorResponseMessageException(
                        HttpStatusCode.BadRequest,
                        ErrorResponseCode.AccessConnnectorIdentityNotConfigured,
                        ErrorResponseMessages.AccessConnnectorIdentityNotConfigured.ToLocalizedMessage(accessConnectorId.Id));
                }

                if (!connectorEntity.Identity.Type.HasFlag(accessConnectorId.IdentityType))
                {
                    this.Logger.Debug(operationName,
                        $"Given identityType by customer doesn't existing accessConnector entity");

                    throw new ErrorResponseMessageException(
                            HttpStatusCode.BadRequest,
                            ErrorResponseCode.ConfiguredIdentityTypeDoesNotExistOnAccessConnector,
                            ErrorResponseMessages.ConfiguredIdentityTypeDoesNotExistOnAccessConnector.ToLocalizedMessage(accessConnectorId.IdentityType, connectorEntity.Identity.Type));
                }

                if (accessConnectorId.IdentityType == IdentityType.UserAssigned)
                {
                    this.Logger.Debug(operationName,
                            $"Checking Access Connector '{accessConnectorId.Id}' configured with UA MI and has UserAssignedIdentityId '{accessConnectorId.UserAssignedIdentityId}' assigned to it.");

                    if (connectorEntity.Identity.UserAssignedIdentities?.ContainsKey(accessConnectorId.UserAssignedIdentityId) != true)
                    {
                        this.Logger.Debug(operationName,
                            $"Access Connector '{accessConnectorId.Id}' configured with UA MI, but UserAssignedIdentityId '{accessConnectorId.UserAssignedIdentityId}' not configured in connectorEntity.");

                        throw new ErrorResponseMessageException(
                            HttpStatusCode.BadRequest,
                            ErrorResponseCode.UserAssignedIdentityIdMustBeAssignedToAccessConnector,
                            ErrorResponseMessages.UserAssignedIdentityIdMustBeAssignedToAccessConnector.ToLocalizedMessage(accessConnectorId.UserAssignedIdentityId, accessConnectorId.Id));
                    }
                }

                this.Logger.Debug(operationName,
                    $"Access Connector '{accessConnectorId.Id}' configured with {accessConnectorId.IdentityType} Managed Identity type.");

                incomingEntity.UpdateApplianceEntityWithAccessConnectorProperty(accessConnectorId, connectorEntity);
            }

            this.Logger.Debug(operationName, $"Completed Access Connector MI validations.");
        }

        /// <summary>
        /// Validate the defaultStorageFirewall configuration is set with proper flow.
        /// </summary>
        /// <param name="incomingEntity"></param>
        /// <param name="existingEntity"></param>
        /// <exception cref="ErrorResponseMessageException"></exception>
        private void ValidateDefaultStorageFirewallConfiguration(
            ApplianceEntity incomingEntity,
            ApplianceEntity existingEntity)
        {
            var operationName = this.GetOperationName(Utilities.GetAsyncMethodName());

            this.Logger.Debug(operationName,
                $"Validating the defaultStorageFirewall configuration is set with proper flow.");

            if (incomingEntity.Properties.DefaultStorageFirewall == DefaultStorageFirewall.Disabled)
            {
                if (existingEntity == null || existingEntity.Properties.DefaultStorageFirewall == null)
                {
                    this.Logger.Debug(operationName,
                        $"Existing workspace doesn't have Private DBFS enabled or user specified Disable during create workspace.");

                    throw new ErrorResponseMessageException(
                        HttpStatusCode.BadRequest,
                        ErrorResponseCode.DefaultStorageFirewallDisableOnlySupportedWhenEnabled,
                        ErrorResponseMessages.DefaultStorageFirewallDisableOnlySupportedWhenEnabled.ToLocalizedMessage());
                }
            }

            this.Logger.Debug(operationName, $"Completed defaultStorageFirewall properties validations.");
        }

        /// <summary>
        /// Validates the AllowNoAzureServiceRules feature flag on the subscription and the location based on the environment
        /// </summary>
        /// <param name="incomingEntity">The incoming appliance entity</param>
        /// <param name="subscriptionId">The subscription Id</param>
        private async Task ValidateNoAzureServiceRules(ApplianceEntity incomingEntity, string subscriptionId)
        {
            string operationName = this.GetOperationName(Utilities.GetAsyncMethodName());

            bool isNoAzureServiceRulesEnabled = await Utilities.IsFeatureRegistered(
                           ProviderConstants.Databricks.AllowNoAzureServiceRules,
                           this.ResourceProviderNamespace,
                           subscriptionId,
                           this.Logger,
                           this.FrontdoorEngine)
                       .ConfigureAwait(false);

            if (!isNoAzureServiceRulesEnabled)
            {
                this.Logger.Debug(operationName, $"The subscription {subscriptionId} is not registered to set NoAzureServiceRules for RequiredNsgRule property.");

                throw new ErrorResponseMessageException(
                HttpStatusCode.BadRequest,
                ErrorResponseCode.NoAzureServiceRuleNotSupportedForSubscription,
                ErrorResponseMessages.NoAzureServiceRuleNotSupportedForSubscription.ToLocalizedMessage(subscriptionId));
            }

            List<string> registeredFeatures = await this.FrontdoorEngine.GetRegisteredFeaturesInSubscription(
                RequestCorrelationContext.Current.GetHomeTenantId(),
                incomingEntity.SubscriptionId,
                ProviderConstants.Databricks.ResourceProviderNamespace)
            .ConfigureAwait(false);

            string allowedLocationConfigKey = ProviderConstants.MicrosoftNetwork.AllowedProdLocationsForNoAzureServiceRules;

            if (registeredFeatures.Contains(ProviderConstants.Databricks.MicrosoftDatabricksDevControlPlaneFeature))
            {
                allowedLocationConfigKey = ProviderConstants.MicrosoftNetwork.AllowedDevLocationsForNoAzureServiceRules;
            }
            else if (registeredFeatures.Contains(ProviderConstants.Databricks.MicrosoftDatabricksStagingControlPlaneFeature))
            {
                allowedLocationConfigKey = ProviderConstants.MicrosoftNetwork.AllowedStagingLocationsForNoAzureServiceRules;
            }

            this.Logger.Debug(operationName, $"Current location is {incomingEntity.Location}. Allowed Locations list in the environment: {string.Join(", ", CloudConfigurationManager.GetMultivaluedConfiguration(allowedLocationConfigKey))}.");

            if (!Utilities.IsMultivaluedConfiguration(allowedLocationConfigKey, incomingEntity.Location))
            {
                this.Logger.Debug(operationName, $"NoAzureServiceRules is not supported in {incomingEntity.Location}.");

                throw new ErrorResponseMessageException(
                HttpStatusCode.BadRequest,
                ErrorResponseCode.NoAzureServiceRuleNotSupportedInRegion,
                ErrorResponseMessages.NoAzureServiceRuleNotSupportedInRegion.ToLocalizedMessage(incomingEntity.Location, string.Join(", ", CloudConfigurationManager.GetMultivaluedConfiguration(allowedLocationConfigKey))));
            }
        }

        #endregion

        #region Managed Disk Encryption Update


        /// <summary>
        /// Handle workspace update for Managed Disk Encryption
        /// </summary>
        /// <param name="subscriptionId">subscription Id</param>
        /// <param name="publisherTenantId">publisher tenant Id</param>
        /// <param name="existingEntity">appliance entity</param>
        /// <param name="incomingEntity">input appliance</param>
        /// <returns>returns Encryption properties and DiskEncryptionSetDefinition</returns>
        private async Task<Tuple<EncryptionPropertiesEntity, DiskEncryptionSetDefinition>> HandleManagedDiskEncryptionUpdate(
            string subscriptionId,
            string publisherTenantId,
            ApplianceEntity existingEntity,
            ApplianceEntity incomingEntity)
        {
            string operationName = this.GetOperationName(Utilities.GetAsyncMethodName());

            // validate key details - throw error if not proper - update fails
            // non cmk to cmk
            // cmk exists, update key or update rotation to latest enabled bool
            // make PUT request with DES details

            try
            {
                var diskEncryptionResponse = await this.UpdateManagedDiskEncryption(subscriptionId, publisherTenantId, existingEntity, incomingEntity);

                if (diskEncryptionResponse == null)
                {
                    this.Logger.Debug(operationName, $"Failed to create disk encryption set in managed resource group - '{existingEntity.Properties.ManagedResourceGroupId}'.");

                    throw new ErrorResponseMessageException(
                        HttpStatusCode.InternalServerError,
                        ErrorResponseCode.DiskEncryptionSetCreateFailed,
                        ErrorResponseMessages.DiskEncryptionSetCreateFailed.ToLocalizedMessage(existingEntity.GetFullyQualifiedId(this.ResourceType)));
                }

                EncryptionPropertiesEntity encryptionSettings = existingEntity.Properties.EncryptionProperties;
                if (existingEntity.Properties.EncryptionProperties == null)
                {
                    encryptionSettings = incomingEntity.Properties.EncryptionProperties;
                }
                else if (existingEntity.Properties.EncryptionProperties.EncryptionEntities == null)
                {
                    encryptionSettings.EncryptionEntities = incomingEntity.Properties.EncryptionProperties.EncryptionEntities;
                }
                else
                {
                    encryptionSettings.EncryptionEntities.ManagedDisk = incomingEntity.Properties.EncryptionProperties.EncryptionEntities.ManagedDisk;
                }

                this.Logger.Debug(operationName, $"Successfully created disk encryption set for workspaceId - '{existingEntity.ToJson()}'.");

                // if request succeeds, update properties and notify databricks
                // if request fails, throw error
                // save existing details, if notification fails, roll back to existing

                return new Tuple<EncryptionPropertiesEntity, DiskEncryptionSetDefinition>(encryptionSettings, diskEncryptionResponse);
            }
            catch (DiskEncryptionSetOperationException exception)
            {
                this.Logger.Debug(operationName, $"Failed to create disk encryption set in managed resource group - '{existingEntity.Properties.ManagedResourceGroupId}'.");

                var diskEncryptionSetName = Utilities.GenerateUniqueDESName(existingEntity.Properties.ManagedResourceGroupId, existingEntity.SubscriptionId);

                throw new ErrorResponseMessageException(
                    HttpStatusCode.BadRequest,
                    ErrorResponseCode.DiskEncryptionSetCreateFailed,
                    ErrorResponseMessages.DiskEncryptionSetCreateFailed.ToLocalizedMessage(diskEncryptionSetName, exception.Message));
            }
        }


        /// <summary>
        /// Perform workspace update for Managed Disk Encryption
        /// </summary>
        /// <param name="subscriptionId">subscription Id</param>
        /// <param name="publisherTenantId">publisher tenant Id</param>
        /// <param name="existingEntity">appliance entity</param>
        /// <param name="incomingEntity">input appliance</param>
        /// <returns>returns a task to enable MSI operation</returns>
        private async Task<DiskEncryptionSetDefinition> UpdateManagedDiskEncryption(string subscriptionId, string publisherTenantId, ApplianceEntity existingEntity, ApplianceEntity incomingEntity)
        {
            var managedDiskEncryption = incomingEntity.Properties.EncryptionProperties.EncryptionEntities.ManagedDisk;
            var managedDiskKeyvaultProperties = managedDiskEncryption.KeyVaultProperties;

            var activeKey = UriTemplateEngine.GetDiskEncryptionSetActiveKeyUri(
                managedDiskKeyvaultProperties.KeyVaultUri,
                managedDiskKeyvaultProperties.KeyName,
                managedDiskKeyvaultProperties.KeyVersion);

            var diskEncryptionSetDefinition = new DiskEncryptionSetDefinition()
            {
                Location = incomingEntity.Location,
                Identity = new JObject(
                    new JProperty(
                        ProviderConstants.Authorizations.TypeProperty,
                        ProviderConstants.Storage.SystemAssigned)
                    ),
                Properties = new JObject(
                    new JProperty(
                        ProviderConstants.Compute.DiskEncryptionSetActiveKeyProperty,
                        new JObject(
                            new JProperty(
                                ProviderConstants.Compute.keyUrl,
                                activeKey)
                            )),
                    new JProperty(
                        ProviderConstants.Compute.DiskEncryptionSetEncryptionTypeProperty,
                        ProviderConstants.Compute.EncryptionAtRestWithCustomerKey
                        ),
                    new JProperty(
                        ProviderConstants.Compute.RotationToLatestKeyVersionEnabled,
                        managedDiskEncryption.RotationToLatestKeyVersionEnabled
                        )),
                Tags = incomingEntity.Tags
            };

            try
            {
                var initialDiskEncryptionResponse = await this.FrontdoorEngine
                    .CreateManagedDiskEncryptionSetWithCmk(
                        managedResourceGroupId: incomingEntity.Properties.ManagedResourceGroupId,
                        authenticationTenantId: publisherTenantId,
                        resourceProviderNamespace: this.ResourceProviderNamespace,
                        subscriptionId: incomingEntity.SubscriptionId,
                        diskEncryptionSetCreateRequest: diskEncryptionSetDefinition)
                    .ConfigureAwait(continueOnCapturedContext: false);

                if (initialDiskEncryptionResponse == null)
                {
                    this.Logger.Debug(
                        operationName: "ApplicationApiEngine.UpdateManagedDiskEncryption",
                        $"The creation of disk encryption set failed.");

                    throw new ErrorResponseMessageException(
                        HttpStatusCode.InternalServerError,
                        ErrorResponseCode.DiskEncryptionSetCreateFailed,
                        ErrorResponseMessages.DiskEncryptionSetCreateFailed.ToLocalizedMessage(this.ResourceType));
                }

                var diskEncryptionSetId = Utilities.GetDiskEncryptionSetId(incomingEntity.Properties.ManagedResourceGroupId, incomingEntity.SubscriptionId);

                // Get the async operation status using the azure asynchronous operation URL in the Azure-AsyncOperation header
                var diskEncryptionSetOperationStatusResponse = await this.GetDiskEncryptionSetOperationStatus(
                    DiskEncryptionSetOperation.CreateDiskEncryptionSet,
                    diskEncryptionSetId,
                    publisherTenantId,
                    this.ResourceProviderNamespace,
                    initialDiskEncryptionResponse.Headers.Location);

                this.Logger.Debug(
                    operationName: "ApplianceEngine.ConfigureManagedDiskEncryption",
                    $"Disk Encryption Set Operation Response - '{diskEncryptionSetOperationStatusResponse.ToJson()}'.");

                var diskEncryptionResponse = diskEncryptionSetOperationStatusResponse?.DiskEncryptionSetDefinition;

                this.Logger.Debug(
                    operationName: "ApplianceEngine.ConfigureManagedDiskEncryption",
                    $"Successfully created disk encryption set - Disk Encryption Set Definition - '{diskEncryptionResponse.ToJson()}'.");

                return diskEncryptionResponse;
            }
            catch (DiskEncryptionSetOperationException exception)
            {
                this.Logger.Error(
                    operationName: "ApplianceEngine.ConfigureManagedDiskEncryption",
                    $"Failed to create disk encryption set in managed resource group - '{incomingEntity.Properties.ManagedResourceGroupId}'.");

                var diskEncryptionSetName = Utilities.GenerateUniqueDESName(incomingEntity.Properties.ManagedResourceGroupId, subscriptionId);

                throw new ErrorResponseMessageException(
                    HttpStatusCode.BadRequest,
                    ErrorResponseCode.DiskEncryptionSetCreateFailed,
                    ErrorResponseMessages.DiskEncryptionSetCreateFailed.ToLocalizedMessage(diskEncryptionSetName, exception.Message));
            }
        }

        /// <summary>
        /// Get the operation status for disk encryption set operation.
        /// </summary>
        /// <param name="operation">The disk encryption set operation being performed.</param>
        /// <param name="diskEncryptionSetId">The disk encryption set ID.</param>
        /// <param name="customerTenantId">The customer tenant Id.</param>
        /// <param name="resourceProviderNamespace">The resource provider name space.</param>
        /// <param name="asyncTrackingUri">The async tracking URI</param>
        private async Task<DiskEncryptionSetOperationResponse> GetDiskEncryptionSetOperationStatus(
            DiskEncryptionSetOperation operation,
            string diskEncryptionSetId,
            string customerTenantId,
            string resourceProviderNamespace,
            Uri asyncTrackingUri)
        {
            string actionName = operation.ToString();
            string operationName = this.GetOperationName(Utilities.GetAsyncMethodName());

            var getOperationStatus = OperationStatus.Failed;
            ErrorResponseMessage getOperationErrorResponseMessage = null;
            Exception getOperationException = null;
            AsyncOperationResult operationStatus = null;
            DiskEncryptionSetOperationResponse response = null;

            try
            {
                response = await this.FrontdoorEngine.GetDiskEncryptionSetOperationStatus(
                        operation,
                        customerTenantId,
                        diskEncryptionSetId,
                        resourceProviderNamespace,
                        asyncTrackingUri)
                    .ConfigureAwait(false);

                getOperationStatus = response.Status;
            }
            catch (DiskEncryptionSetOperationException desoEx)
            {
                getOperationException = desoEx;
                getOperationErrorResponseMessage = new ErrorResponseMessage(desoEx.ErrorCode, desoEx.Message);
            }
            catch (ErrorResponseMessageException ermEx)
            {
                getOperationException = ermEx;
                getOperationErrorResponseMessage = new ErrorResponseMessage(ermEx.ErrorCode, ermEx.Message);
            }
            catch (ServerErrorResponseMessageException sermEx)
            {
                getOperationException = sermEx;
                getOperationErrorResponseMessage = new ErrorResponseMessage(sermEx.ErrorCode, sermEx.Message);
            }
            catch (Exception ex)
            {
                getOperationException = ex;
                getOperationErrorResponseMessage = new ErrorResponseMessage(
                    ErrorResponseCode.DiskEncryptionSetCreateFailed,
                    ErrorResponseMessages.DiskEncryptionSetCreateFailed.ToLocalizedMessage());
            }

            if (getOperationStatus == OperationStatus.Success)
            {
                this.Logger.Debug(operationName, $"The {actionName} operation succeeded for disk encryption set '{diskEncryptionSetId}.'");

                return new DiskEncryptionSetOperationResponse
                {
                    Status = OperationStatus.Success,
                    DiskEncryptionSetDefinition = response.DiskEncryptionSetDefinition
                };
            }

            Utilities.TrySerializeObject(operationStatus?.Error, this.Logger, out string errorJson);

            string errorMessage = operationStatus?.Error != null
                ? $"Error details: {errorJson}"
                : $"Error in {actionName} operation";
            this.Logger.Debug(
                operationName,
                $"{actionName} of the disk encryption set with ID: '{diskEncryptionSetId}' has failed. Error: '{errorMessage}', Exception: {Utilities.FlattenException(getOperationException)}");

            var errorResponseMessage = new ErrorResponseMessage(
                operationStatus?.Error?.Code ?? ErrorResponseCode.DiskEncryptionSetCreateFailed.ToString(),
                ErrorResponseMessages.DiskEncryptionSetCreateFailed.ToLocalizedMessage());

            return new DiskEncryptionSetOperationResponse
            {
                Status = OperationStatus.Failed,
                ErrorResponseMessage = getOperationErrorResponseMessage ?? errorResponseMessage
            };
        }

        #endregion
    }
}