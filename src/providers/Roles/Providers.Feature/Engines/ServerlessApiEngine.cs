﻿//------------------------------------------------------------
// Copyright (c) Microsoft Corporation.  All rights reserved.
//------------------------------------------------------------

namespace Microsoft.WindowsAzure.ResourceStack.Providers.Feature.Engines
{
    using System;
    using System.Net;
    using System.Threading.Tasks;
    using Microsoft.WindowsAzure.ResourceStack.Common.Collections;
    using Microsoft.WindowsAzure.ResourceStack.Common.EventSources;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Application.Data.Entities;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Application.Data.Extensions;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Application.Data.Metadata;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Common.Data;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Common.Utilities;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Feature.Components;

    /// <summary>
    /// The application engine.
    /// </summary>
    public partial class ApplicationApiEngine
    {
        #region PUT application

        /// <summary>
        /// Creates or updates a serverless application (workspace).
        /// </summary>
        /// <param name="incomingEntity"></param>
        /// <param name="requestProcessingStartDateTime"></param>
        /// <returns></returns>
        public async Task<Tuple<ApplianceEntity, string, HttpStatusCode>> PutServerlessApplication(
            ApplianceEntity incomingEntity,
            DateTime requestProcessingStartDateTime)
        {
            var operationName = this.GetOperationName(Utilities.GetAsyncMethodName());
            this.Logger.Debug(
                operationName, $"Create or Update Serverless Workspace {incomingEntity.Name}");

            await this.ValidateESCFeatureEnabled(incomingEntity);

            var existingEntity = await this
                .GetApplianceDataProvider()
                .FindAppliance(
                    incomingEntity.SubscriptionId,
                    incomingEntity.ResourceGroup,
                    incomingEntity.Name)
                .ConfigureAwait(continueOnCapturedContext: false);

            ApplianceEntity savedApplianceEntity;
            HttpStatusCode responseStatusCode;
            string jobId;

            if (existingEntity == null)
            {
                this.Logger.Debug(
                    operationName,
                    "Creating serverless workspace {0}  in resource group {1} in subscription {2}",
                    arg0: incomingEntity.Name,
                    arg1: incomingEntity.ResourceGroup,
                    arg2: incomingEntity.SubscriptionId);

                savedApplianceEntity = await this
                    .CreateServerlessWorkspace(incomingEntity, requestProcessingStartDateTime)
                    .ConfigureAwait(continueOnCapturedContext: false);

                responseStatusCode = HttpStatusCode.Created;
                jobId = ApplianceProvisioningJobMetadata.GetJobId(resourceGroupName: incomingEntity.ResourceGroup, applianceName: incomingEntity.Name);
            }
            else
            {
                Validation.ApplicationPropertiesExistOnUpdate(existingEntity);

                Validation.ApplianceDeletionInProgress(existingEntity);
                Validation.ApplicanceCreationInProgress(existingEntity);
                Validation.ApplicanceUpdateInProgress(existingEntity);

                Validation.ValidateComputeModeUpdate(existingEntity, incomingEntity);
                Validation.ValidSkuChangeForServerlessWorkspace(existingEntity, incomingEntity);

                this.Logger.Debug(
                        operationName,
                        "Updating serverless workspace {0}  in resource group {1} in subscription {2}",
                        arg0: incomingEntity.Name,
                        arg1: incomingEntity.ResourceGroup,
                        arg2: incomingEntity.SubscriptionId);

                ValidateAndPopulateServerlessWorkspaceUpdate(incomingEntity, existingEntity);

                responseStatusCode = HttpStatusCode.Created;
                savedApplianceEntity = await this.UpdateServerlessWorkspaceAsync(existingEntity, requestProcessingStartDateTime).ConfigureAwait(false);
                jobId = ApplianceProvisioningJobMetadata.GetJobId(incomingEntity.ResourceGroup, incomingEntity.Name);
            }

            return Tuple.Create<ApplianceEntity, string, HttpStatusCode>(savedApplianceEntity, jobId, responseStatusCode);
        }

        #endregion

        #region DELETE application

        /// <summary>
        /// Deletes a serverless application (workspace).
        /// </summary>
        /// <param name="subscriptionId"></param>
        /// <param name="resourceGroupName"></param>
        /// <param name="workspaceName"></param>
        /// <param name="requestProcessingStartDateTime"></param>
        /// <returns></returns>
        public async Task<ApplianceEntity> DeleteServerlessApplication(
            string subscriptionId,
            string resourceGroupName,
            string workspaceName,
            DateTime requestProcessingStartDateTime)
        {
            string operationName = this.GetOperationName(Utilities.GetAsyncMethodName());

            var applianceEntity = await this
                .GetApplianceDataProvider()
                .FindAppliance(
                    subscriptionId: subscriptionId,
                    resourceGroupName: resourceGroupName,
                    applianceName: workspaceName)
                .ConfigureAwait(continueOnCapturedContext: false);

            this.Logger.Debug(
                operationName,
                $"Creating de-provisioning job for the workspace serverless workspace");

            await this
                .GetJobsDataProvider()
                .CreateServerlessWorkspaceDeprovisioningJob(
                    subscriptionId: subscriptionId,
                    resourceGroupName: resourceGroupName,
                    resourceType: this.ResourceType,
                    resourceProviderNamespace: this.ResourceProviderNamespace,
                    workspaceName: workspaceName,
                    location: applianceEntity.Location,
                    requestProcessingStartDateTime: requestProcessingStartDateTime)
                .ConfigureAwait(continueOnCapturedContext: false);

            return applianceEntity;
        }

        #endregion

        #region Private methods

        /// <summary>
        /// Creates a new serverless workspace.
        /// </summary>
        /// <param name="incomingEntity"></param>
        /// <param name="requestProcessingStartDateTime"></param>
        /// <returns></returns>
        private async Task<ApplianceEntity> CreateServerlessWorkspace(
        ApplianceEntity incomingEntity, DateTime requestProcessingStartDateTime)
        {
            var operationName = this.GetOperationName(Utilities.GetAsyncMethodName());

            this.Logger.Debug(
                operationName,
                format: "Create a new serverless workspace with name '{0}'.",
                arg0: incomingEntity.Name);

            var publisherPackageId = await this.GetPublisherPackageId(incomingEntity);

            incomingEntity.Properties.ProvisioningState = ProvisioningState.Accepted;
            incomingEntity.Properties.CreatedBy = ApplicationApiEngine.GetApplicationClientDetailsEntity();
            incomingEntity.Properties.UpdatedBy = ApplicationApiEngine.GetApplicationClientDetailsEntity();
            incomingEntity.Properties.PublisherPackageId = publisherPackageId;

            incomingEntity.Metadata = new ApplianceMetadata
            {
                DeploymentName = incomingEntity.Name,
                OperationStatuses = new InsensitiveDictionary<ApplicationOperationStatus>
                    {
                        {
                            this.ApplianceWriteOperationName,
                            new ApplicationOperationStatus { Status = ProvisioningState.Accepted }
                        }
                    }
            };

            await this
                .GetApplianceDataProvider()
                .SaveAppliance(incomingEntity)
                .ConfigureAwait(continueOnCapturedContext: false);

            await this
                .GetJobsDataProvider()
                .CreateServerlessWorkspaceProvisioningJob(
                    subscriptionId: incomingEntity.SubscriptionId,
                    resourceGroupName: incomingEntity.ResourceGroup,
                    resourceType: this.ResourceType,
                    resourceProviderNamespace: this.ResourceProviderNamespace,
                    workspaceName: incomingEntity.Name,
                    location: incomingEntity.Location,
                    parameters: incomingEntity.Properties.Parameters,
                    requestProcessingStartDateTime: requestProcessingStartDateTime,
                    applianceWriteOperationName: this.ApplianceWriteOperationName,
                    provisioningOperation: ProvisioningOperation.Create)
                .ConfigureAwait(continueOnCapturedContext: false);

            var appliancePackage = this.GetValidatedMarketplaceAppliancePackage(publisherPackageId);

            this
               .PopulatePropertiesFromAppliancePackage(
                   applianceEntity: incomingEntity,
                   appliancePackage: appliancePackage);

            return incomingEntity;
        }

        /// <summary>
        /// Updates an existing serverless workspace.
        /// </summary>
        /// <param name="existingEntity"></param>
        /// <param name="requestProcessingStartDateTime"></param>
        /// <returns></returns>
        private async Task<ApplianceEntity> UpdateServerlessWorkspaceAsync(
            ApplianceEntity existingEntity, DateTime requestProcessingStartDateTime)
        {
            string operationName = this.GetOperationName(Utilities.GetAsyncMethodName());

            this.Logger.Debug(
                            operationName: operationName,
                            format: "Update a serverless workspace with name '{0}'.",
                            arg0: existingEntity.Name);

            var publisherPackageId = await this.GetPublisherPackageId(existingEntity);

            existingEntity.Properties.ProvisioningState = ProvisioningState.Updating;
            existingEntity.Properties.UpdatedBy = ApplicationApiEngine.GetApplicationClientDetailsEntity();
            existingEntity.Properties.PublisherPackageId = publisherPackageId;

            existingEntity.Metadata = new ApplianceMetadata
            {
                ManagedResourceGroupLockName = existingEntity.Name,
                DeploymentName = existingEntity.Name,
                OperationStatuses = new InsensitiveDictionary<ApplicationOperationStatus>
                {
                    {
                        this.ApplianceWriteOperationName,
                        new ApplicationOperationStatus { Status = ProvisioningState.Updating }
                    }
                }
            };

            await this
                .GetApplianceDataProvider()
                .SaveAppliance(existingEntity)
                .ConfigureAwait(continueOnCapturedContext: false);

            await this
                .GetJobsDataProvider()
                .CreateServerlessWorkspaceProvisioningJob(
                    subscriptionId: existingEntity.SubscriptionId,
                    resourceGroupName: existingEntity.ResourceGroup,
                    resourceType: this.ResourceType,
                    resourceProviderNamespace: this.ResourceProviderNamespace,
                    workspaceName: existingEntity.Name,
                    location: existingEntity.Location,
                    parameters: existingEntity.Properties.Parameters,
                    requestProcessingStartDateTime: requestProcessingStartDateTime,
                    applianceWriteOperationName: this.ApplianceWriteOperationName,
                    provisioningOperation: ProvisioningOperation.Updating)
                .ConfigureAwait(continueOnCapturedContext: false);

            var appliancePackage = this.GetValidatedMarketplaceAppliancePackage(publisherPackageId);

            this
               .PopulatePropertiesFromAppliancePackage(
                   applianceEntity: existingEntity,
                   appliancePackage: appliancePackage);

            return existingEntity;
        }

        /// <summary>
        /// validates workspace update request details
        /// </summary>
        /// <param name="incomingEntity"></param>
        /// <param name="existingEntity"></param>
        /// <param name="isAsync">The Async Update is enabled.</param>
        private void ValidateAndPopulateServerlessWorkspaceUpdate(ApplianceEntity incomingEntity, ApplianceEntity existingEntity)
        {
            Validation.ValidateNotebookEncryptionForPut(existingEntity, incomingEntity);
            existingEntity.UpdateNotebookEncryptionProperties(incomingEntity);

            Validation.ValidateEnhancedSecurityComplianceUpdate(existingEntity, incomingEntity);
            existingEntity.UpdateEnhancedSecurityComplianceProperties(incomingEntity);

            existingEntity.SystemData = incomingEntity.SystemData;
            existingEntity.Tags = incomingEntity.Tags ?? existingEntity.Tags;
        }

        #endregion
    }
}