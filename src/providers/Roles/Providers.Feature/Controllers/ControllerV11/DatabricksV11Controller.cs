﻿//------------------------------------------------------------
// Copyright (c) Microsoft Corporation.  All rights reserved.
//------------------------------------------------------------

namespace Microsoft.WindowsAzure.ResourceStack.Providers.Feature.Controllers.ControllerV11
{
    using System.Threading.Tasks;
    using Microsoft.AspNetCore.Mvc;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Application.Data.Entities;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Common.Constants;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Feature.Components;

    public class DatabricksV11Controller : WorkspaceControllerBase<WorkspaceV11>
    {
        /// <summary>
        /// Gets or sets the Async Support.
        /// </summary>
        protected override bool AsyncSupport
        {
            get
            {
                return true;
            }
        }

        /// <summary>
        /// Initializes a new instance of the <see cref="DatabricksV11Controller" /> class.
        /// </summary>
        /// <param name="providerConfiguration">The application provider configuration.</param>
        /// <param name="accessConnectorProviderConfiguration">The accessConnector provider configuration.</param>
        public DatabricksV11Controller(
            IApplicationProviderConfiguration providerConfiguration,
            IAccessConnectorProviderConfiguration accessConnectorProviderConfiguration)
            : base(
                  providerConfiguration: providerConfiguration,
                  accessConnectorProviderConfiguration: accessConnectorProviderConfiguration)
        {
        }

        /// <summary>
        /// Get Workspaces for a subscription and resource group
        /// </summary>
        /// <param name="subscriptionId"></param>
        /// <param name="resourceGroup"></param>
        /// <returns></returns>
        [ApiVersionSpecificRoute("/subscriptions/{subscriptionId}/resourcegroups/{resourceGroup}/providers/Microsoft.Databricks/workspaces", ProviderConstants.ApiVersion20250601Preview)]
        [HttpGet]
        public override async Task<IActionResult> GetApplications(string subscriptionId, string resourceGroup)
        {
            return await base.GetApplications(subscriptionId, resourceGroup);
        }

        /// <summary>
        /// Get Workspaces for a subscription
        /// </summary>
        /// <param name="subscriptionId"></param>
        /// <returns></returns>
        [ApiVersionSpecificRoute("/subscriptions/{subscriptionId}/providers/Microsoft.Databricks/workspaces", ProviderConstants.ApiVersion20250601Preview)]
        [HttpGet]
        public override async Task<IActionResult> GetApplications(string subscriptionId)
        {
            return await base.GetApplications(subscriptionId);
        }

        /// <summary>
        /// Create or Update Workspace for a subscription and resource group with workspace name
        /// </summary>
        /// <param name="subscriptionId"></param>
        /// <param name="resourceGroup"></param>
        /// <param name="workspaceName"></param>
        /// <param name="workspace"></param>
        /// <returns></returns>
        [ApiVersionSpecificRoute("subscriptions/{subscriptionId}/resourceGroups/{resourceGroup}/providers/Microsoft.Databricks/workspaces/{workspaceName}", ProviderConstants.ApiVersion20250601Preview)]
        [HttpPut]
        public override async Task<IActionResult> PutApplication(
            string subscriptionId,
            string resourceGroup,
            string workspaceName,
            [FromBody] WorkspaceV11 workspace)
        {
            return await base.PutApplication(subscriptionId, resourceGroup, workspaceName, workspace);
        }

        /// <summary>
        /// Patch Workspace for a subscription and resource group with workspace name
        /// </summary>
        /// <param name="subscriptionId"></param>
        /// <param name="resourceGroup"></param>
        /// <param name="workspaceName"></param>
        /// <param name="workspace"></param>
        /// <returns></returns>
        [ApiVersionSpecificRoute("subscriptions/{subscriptionId}/resourceGroups/{resourceGroup}/providers/Microsoft.Databricks/workspaces/{workspaceName}", ProviderConstants.ApiVersion20250601Preview)]
        [HttpPatch]
        public override async Task<IActionResult> PatchApplication(
            string subscriptionId,
            string resourceGroup,
            string workspaceName,
            [FromBody] WorkspaceV11 workspace)
        {
            return await base.PatchApplication(subscriptionId, resourceGroup, workspaceName, workspace);
        }

        /// <summary>
        /// Get Workspace for a subscription and resource group with workspace name
        /// </summary>
        /// <param name="subscriptionId"></param>
        /// <param name="resourceGroup"></param>
        /// <param name="workspaceName"></param>
        /// <returns></returns>
        [ApiVersionSpecificRoute("subscriptions/{subscriptionId}/resourceGroups/{resourceGroup}/providers/Microsoft.Databricks/workspaces/{workspaceName}", ProviderConstants.ApiVersion20250601Preview)]
        [HttpGet]
        public override async Task<IActionResult> GetApplication(
            string subscriptionId,
            string resourceGroup,
            string workspaceName)
        {
            return await base.GetApplication(subscriptionId, resourceGroup, workspaceName);
        }

        /// <summary>
        /// Delete workspace request with query string parameter for forceDeletion
        /// </summary>
        /// <param name="subscriptionId">subscriptionId of the workspace</param>
        /// <param name="resourceGroup">resourceGroup of the workspace</param>
        /// <param name="workspaceName">workspace name</param>
        /// <param name="forceDeletion">forceDeletion for the workspace</param>
        /// <returns></returns>
        [ApiVersionSpecificRoute("subscriptions/{subscriptionId}/resourceGroups/{resourceGroup}/providers/Microsoft.Databricks/workspaces/{workspaceName}", ProviderConstants.ApiVersion20250601Preview)]
        [HttpDelete]
        public override async Task<IActionResult> DeleteApplication(
            string subscriptionId,
            string resourceGroup,
            string workspaceName,
            [FromQuery] bool forceDeletion = false)
        {
            return await base.DeleteApplication(subscriptionId, resourceGroup, workspaceName, !forceDeletion);
        }

        /// <summary>
        /// Gets appliance entity from workspace object
        /// </summary>
        /// <param name="workspace">The workspace object</param>
        /// <returns>The appliance entity object</returns>
        protected override ApplianceEntity GetApplianceEntityFromWorkspace(WorkspaceV11 workspace)
        {
            return workspace.ToApplianceEntity();
        }

        /// <summary>
        /// Get a workspace object from appliance entity object
        /// </summary>
        /// <param name="applianceEntity">The appliance entity object</param>
        /// <returns>The workspace object</returns>
        protected override WorkspaceV11 GetWorkspaceFromApplianceEntity(ApplianceEntity applianceEntity)
        {
            return WorkspaceV11.FromApplianceEntity(applianceEntity);
        }

        /// <summary>
        /// Validates the workspace object for PUT requests
        /// </summary>
        /// <param name="workspace">The workspace object</param>
        protected override void ValidatePut(WorkspaceV11 workspace)
        {
            workspace.ValidatePut();
        }

        /// <summary>
        /// Validates the workspace object for PUT requests
        /// </summary>
        /// <param name="workspace">The workspace object</param>
        protected override void ValidatePatch(WorkspaceV11 workspace)
        {
            workspace.ValidatePatch();
        }
    }
}
