﻿//------------------------------------------------------------
// Copyright (c) Microsoft Corporation.  All rights reserved.
//------------------------------------------------------------

namespace Microsoft.WindowsAzure.ResourceStack.Providers.Feature.Controllers.ControllerV11
{
    using Microsoft.WindowsAzure.ResourceStack.Providers.Common.Definitions;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Feature.Features.ServerlessWorkspaceFeature;
    using Newtonsoft.Json;

    public class WorkspacePropertiesV11 :
            WorkspacePropertiesV7,
            IComputeModeProperties
    {
        /// <summary>
        /// Gets or sets the Compute Mode property.
        /// </summary>
        [JsonProperty("computeMode", Required = Required.Default, NullValueHandling = NullValueHandling.Ignore)]
        public ComputeMode ComputeMode { get; set; }
    }
}
