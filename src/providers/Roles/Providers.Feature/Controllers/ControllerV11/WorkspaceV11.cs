﻿//------------------------------------------------------------
// Copyright (c) Microsoft Corporation.  All rights reserved.
//------------------------------------------------------------

namespace Microsoft.WindowsAzure.ResourceStack.Providers.Feature.Controllers.ControllerV11
{
    using System.Net;
    using Microsoft.WindowsAzure.ResourceStack.Common.Instrumentation;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Application.Data.Entities;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Application.Data.Extensions;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Common.Definitions;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Common.ErrorResponses;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Common.Utilities;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Feature.Features.AccessConnectorIdentity;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Feature.Features.BaseFeature;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Feature.Features.DefaultCatalog;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Feature.Features.DefaultStorageFirewall;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Feature.Features.EnhancedSecurityComplianceFeature;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Feature.Features.ManagedDiskEncryptionFeature;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Feature.Features.NotebookEncryptionFeature;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Feature.Features.PrivateLinkFeature;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Feature.Features.ServerlessWorkspaceFeature;
    using Newtonsoft.Json;

    /// <summary>
    /// The Workspace object for V11 API
    /// Implement for 2025-06-01-preview and EnableServerlessWorkspaceFeature
    /// </summary>
    public class WorkspaceV11 : BaseWorkspace
    {
        #region Constructors
        /// <summary>
        /// Initializes a new instance of the <see cref="WorkspaceV11" /> class.
        /// </summary>
        public WorkspaceV11() : base()
        {
        }

        /// <summary>
        /// Initializes a new instance of the <see cref="WorkspaceV11" /> class and
        /// sets its fields from an appliance entity object.
        /// </summary>
        /// <param name="applianceEntity">The appliance entity object</param>
        public WorkspaceV11(ApplianceEntity applianceEntity) : base(applianceEntity)
        {
        }

        #endregion

        /// <summary>
        /// Gets or sets the appliance properties.
        /// </summary>
        [JsonProperty("properties", Required = Required.Default)]
        public WorkspacePropertiesV11 Properties { get; set; }

        /// <summary>
        /// Validates the workspace object for put operations.
        /// TODO: This method should be refactored to use a new validation helper.
        /// </summary>
        public void ValidatePut()
        {
            if (this.Properties == null)
            {
                throw new ErrorResponseMessageException(
                    httpStatus: HttpStatusCode.BadRequest,
                    errorCode: ErrorResponseCode.InvalidApplicationPropertiesForPut,
                    errorMessage: ErrorResponseMessages.ApplicationPropertiesNull.ToLocalizedMessage(this.Name));
            }

            this.ValidateSku();
            this.Properties.ValidateMrgForComputeMode();
            this.Properties.ValidateEnhancedSecurityCompliance(RequestCorrelationContext.Current.ApiVersion, this.Location.TrimWhitespaces());
            this.Properties.ValidateNotebookEncryption();

            if (this.Properties.ComputeMode == ComputeMode.Serverless)
            {
                this.Properties.ValidateServerlessNotSupportedFeatures(this.Sku);
            }
            else
            {
                this.Properties.ValidateMrg(this.SubscriptionId, this.ResourceGroup, this.Name);
                this.Properties.ValidateBaseWorkspaceParameters(this.SubscriptionId);
                this.Properties.ValidateManagedDiskEncryption();
                this.Properties.ValidateDefaultCatalog();
                this.Properties.ValidateAccessConnectorId(RequestCorrelationContext.Current.ApiVersion, this.Properties.DefaultStorageFirewall);
                this.Properties.ValidateDefaultStorageFirewall(this.Properties.AccessConnector, this.Properties.Parameters);
            }
        }

        /// <summary>
        /// Validates the workspace for patch operations.
        /// TODO: This method should be refactored to use a new validation helper.
        /// </summary>
        public void ValidatePatch()
        {
            if (this.Properties != null)
            {
                throw new ErrorResponseMessageException(
                    httpStatus: HttpStatusCode.BadRequest,
                    errorCode: ErrorResponseCode.InvalidAppliancePropertiesForPatch,
                    errorMessage: ErrorResponseMessages.AppliancePropertiesInvalidForPatch.ToLocalizedMessage(this.Name));
            }
        }

        /// <summary>
        /// Converts the workspace object to an appliance entity object
        /// </summary>
        /// <returns>The appliance entity object</returns>
        public ApplianceEntity ToApplianceEntity()
        {
            var applianceEntity = this.GetApplianceEntity();

            this.Properties.SetBasicPropertiesFromWorkspaceToEntity(applianceEntity);
            this.Properties.SetComputeModeFieldFromWorkspaceToEntity(applianceEntity);
            this.Properties.SetEnhancedSecurityComplianceFieldsFromWorkspaceToEntity(applianceEntity);
            this.Properties.SetNotebookEncryptionFieldsFromWorkspaceToEntity(applianceEntity);

            if (applianceEntity.GetComputeMode() == ComputeMode.Hybrid)
            {
                this.Properties.SetManagedDiskEncryptionFieldsFromWorkspaceToEntity(applianceEntity);
                this.Properties.SetDefaultCatalogFieldsFromWorkspaceToEntity(applianceEntity);
                this.Properties.SetAccessConnectorFieldsFromWorkspaceToEntity(applianceEntity);
                this.Properties.SetDefaultStorageFirewallFieldsFromWorkspaceToEntity(applianceEntity);
                this.Properties.SetPrivateLinkFieldsFromWorkspaceToEntity(applianceEntity);
            }

            return applianceEntity;
        }

        /// <summary>
        /// Converts an appliance entity object to an API workspace object.
        /// </summary>
        /// <param name="applianceEntity">The appliance entity object</param>
        /// <returns>The workspace object</returns>
        public static WorkspaceV11 FromApplianceEntity(ApplianceEntity applianceEntity)
        {
            var workspace = new WorkspaceV11(applianceEntity)
            {
                Properties = new WorkspacePropertiesV11()
            };

            workspace.Properties.SetBasicFieldsFromEntityToWorkspace(applianceEntity);
            workspace.Properties.SetComputeModeFieldFromEntityToWorkspace(applianceEntity);
            workspace.Properties.SetEnhancedSecurityComplianceFieldsFromEntityToWorkspace(applianceEntity);
            workspace.Properties.SetNotebookEncryptionFieldsFromEntityToWorkspace(applianceEntity);

            if (applianceEntity.GetComputeMode() == ComputeMode.Hybrid)
            {
                workspace.Properties.SetManagedDiskEncryptionFieldsFromEntityToWorkspace(applianceEntity);
                workspace.Properties.SetDefaultCatalogFieldsFromEntityToWorkspace(applianceEntity);
                workspace.Properties.SetConnectorFieldsFromEntityToWorkspace(applianceEntity);
                workspace.Properties.SetDefaultStorageFirewallFieldsFromEntityToWorkspace(applianceEntity);
                workspace.Properties.SetPrivateLinkFieldsFromEntityToWorkspace(applianceEntity);
            }

            return workspace;
        }
    }
}
