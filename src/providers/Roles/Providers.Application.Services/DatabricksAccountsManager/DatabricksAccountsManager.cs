﻿//------------------------------------------------------------
// Copyright (c) Microsoft Corporation.  All rights reserved.
//------------------------------------------------------------

namespace Microsoft.WindowsAzure.ResourceStack.Providers.Application.Services.DatabricksAccountsManager
{
    using Microsoft.WindowsAzure.ResourceStack.Common.Collections;
    using Microsoft.WindowsAzure.ResourceStack.Common.EventSources;
    using Microsoft.WindowsAzure.ResourceStack.Common.Services;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Application.Data.Components;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Application.Data.Extensions;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Common.Constants;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Common.Engines;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Common.ErrorResponses;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Common.Responses;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Common.Utilities;
    using Newtonsoft.Json.Linq;
    using System;
    using System.Collections.Generic;
    using System.Linq;
    using System.Net.Http;
    using System.Threading.Tasks;

    public class DatabricksAccountsManager : IDatabricksAccountsManager
    {
        /// <summary>
        /// Gets the event service logger.
        /// </summary>
        private ICommonEventSource Logger { get; }

        /// <summary>
        /// Gets front door client.
        /// </summary>
        private IFrontdoorEngine FrontdoorEngine { get; }

        public DatabricksAccountsManager(IFrontdoorEngine frontdoorEngine, ICommonEventSource Logger)
        {
            this.FrontdoorEngine = frontdoorEngine;
            this.Logger = Logger;
        }

        /// <summary>
        /// Notifies the databricks account manager service for workspace delete
        /// </summary>
        /// <param name="subscriptionId">The subscription id</param>
        /// <param name="workspaceResourceUri">The workspace resource uri</param>
        /// <param name="homeTenantId">The customer tenant id, If not supplied,it gets from RequestCorrelationContext </param>
        /// <param name="registeredFeatures"> List of registered features on subscription
        /// If not supplied,it calls ARM to get the list </param>
        /// <param name="additionalHeaders">Additional headers to be passed to the request</param>
        public async Task NotifyDatabricksForWorkspaceDelete(string subscriptionId,
            string workspaceResourceUri,
            string homeTenantId,
            List<string> registeredFeatures,
            InsensitiveDictionary<string> additionalHeaders,
            Application.Data.Metadata.ApplianceJobMetadata metadata = null
            )
        {
            var operationName = this.GetOperationName(Utilities.GetAsyncMethodName());

            this.Logger.Debug(operationName, $"Notifying databricks account manager for workspace delete");

            // This method is only used when the subscription is being deleted. We always use account api.
            // So the thir pramter bool useArm is skipped.
            (Uri baseUri, string aadAudience, _) = DatabricksAccountsManagerUtils.GetBaseUriAndAadAudience(registeredFeatures, this.Logger);

            var requestUri = UriTemplateEngine.DatabricksAccountApiWorkspaceNotificationUri(
                baseUri,
                homeTenantId,
                workspaceResourceUri.TrimStart('/'));

            try
            {
                await DatabricksAccountsManagerUtils.SendDBNotificationWithFlagControl(
                    this.FrontdoorEngine,
                    new JObject(),
                    HttpMethod.Delete,
                    homeTenantId,
                    false,
                    workspaceResourceUri,
                    baseUri,
                    "", // not using arm, this is redundant
                    aadAudience,
                    additionalHeaders.ToArray());

                // Metadata should always be available for job-based polling
                if (metadata == null)
                {
                    this.Logger.Debug(
                        operationName,
                        $"No metadata provided for job-based polling. This is unexpected and will cause issues.");

                    throw new InvalidOperationException("Metadata is required for job-based polling but was not provided.");
                }

                // Set up job-based polling for delete operation using extension methods
                metadata.SetAccountApiPollingState(AccountApiPollingState.WaitingForDeletion);
                metadata.SetAccountApiOperationId(workspaceResourceUri);
                metadata.SetAccountApiOperationStartTime(DateTime.UtcNow);
                metadata.SetAccountApiBaseUri(baseUri);
                metadata.SetAccountApiAudience(aadAudience);

                // Set the resource operation to AccountApiPolling
                metadata.ResourceOperation = Common.Data.ProvisioningOperation.AccountApiPolling;

                this.Logger.Debug(
                    operationName,
                    $"Setting up job-based polling for workspace delete operation: '{workspaceResourceUri}'");

                throw new JobPostponedException(
                    $"Waiting for Account API delete operation to complete for workspace '{workspaceResourceUri}'",
                    TimeSpan.FromSeconds(FrontdoorEngine.AccountApiPollingRetryInterval));
            }
            catch (AccessConnectorErrorResponse ex)
            {
                this.Logger.Error(operationName, $"Failed to notify databricks account manager for workspace delete", ex);

                if (ex.HttpStatus != System.Net.HttpStatusCode.NotFound)
                    throw;
            }
        }

        /// <summary>
        /// Get Databricks Account API endpoint and audience and account api feature enablement, based on feature flag
        /// </summary>
        /// <param name="tenantId"></param>
        /// <param name="subscriptionId"></param>
        /// <returns></returns>
        public async Task<(Uri, string, bool)> GetDatabricksAccountApiEnpointAndAudience(string tenantId, string subscriptionId)
        {
            string operationName = this.GetOperationName(Utilities.GetAsyncMethodName());
            this.Logger.Debug(operationName, $"START: Getting Databricks Account API endpoint and audience for tenantId: '{tenantId}', subscriptionId: '{subscriptionId}'");

            try
            {
                var registeredFeatures = await this.FrontdoorEngine.GetRegisteredFeaturesInSubscription(
                        tenantId,
                        subscriptionId,
                        ProviderConstants.Databricks.ResourceProviderNamespace)
                    .ConfigureAwait(false);

                (Uri baseUri, string aadAudience, bool useArm) = DatabricksAccountsManagerUtils.GetBaseUriAndAadAudience(registeredFeatures, this.Logger);
                if (useArm)
                {
                    aadAudience = CloudConfigurationManager.GetConfiguration("Microsoft.WindowsAzure.ResourceStack.Providers.Appliances.AadAllowedAudience");
                }
                this.Logger.Debug(operationName, $"SUCCESS: Got Databricks Account API endpoint: '{baseUri}' and audience: '{aadAudience}'");
                return (baseUri, aadAudience, useArm);
            }
            catch (Exception ex)
            {
                this.Logger.Error(operationName, $"ERROR: Failed to get Databricks Account API endpoint and audience. Error: {Utilities.FlattenException(ex)}");
                throw;
            }
        }
    }
}
