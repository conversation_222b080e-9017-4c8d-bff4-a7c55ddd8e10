﻿//-----------------------------------------------------------
// Copyright (c) Microsoft Corporation.  All rights reserved.
//-----------------------------------------------------------

namespace Microsoft.WindowsAzure.ResourceStack.Providers.Worker.StateHandlers
{
    using System;
    using System.Net;
    using System.Threading.Tasks;
    using Microsoft.WindowsAzure.ResourceStack.Common.BackgroundJobs;
    using Microsoft.WindowsAzure.ResourceStack.Common.Instrumentation;
    using Microsoft.WindowsAzure.ResourceStack.Common.Json;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Application.Data.DataProviders;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Application.Data.Definitions.Enums;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Application.Data.Entities;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Application.Data.Extensions;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Application.Data.Metadata;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Common.Constants;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Common.Data;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Common.Engines;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Common.ErrorResponses;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Common.Extensions;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Common.Logging;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Common.Metrics;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Common.Responses;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Common.Utilities;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Worker.Engines;
    using Newtonsoft.Json.Linq;

    public class WaitingHandler
    {
        private readonly IApplianceDataProvider ApplianceDataProvider;

        private readonly ApplianceProvisioningJobMetadata Metadata;

        private readonly IApplicationPackageDataProvider MarketPlacePackageDataProvider;

        private readonly ILogger Logger;

        private readonly IFrontdoorEngine FrontdoorEngine;

        private readonly IApplianceEngine ApplianceEngine;

        public WaitingHandler(IApplianceDataProvider applianceDataProvider,
                                   IApplicationPackageDataProvider marketPlacePackageDataProvider,
                                   IFrontdoorEngine frontdoorEngine,
                                   IApplianceEngine applianceEngine,
                                   ILogger logger,
                                   ApplianceProvisioningJobMetadata metadata)
        {
            this.ApplianceDataProvider = applianceDataProvider;
            this.Metadata = metadata;
            this.MarketPlacePackageDataProvider = marketPlacePackageDataProvider;
            this.Logger = logger;
            this.FrontdoorEngine = frontdoorEngine;
            this.ApplianceEngine = applianceEngine;
        }

        public async Task<JobExecutionResult> Execute()
        {
            try
            {
                string operationName = this.GetOperationName(Utilities.GetAsyncMethodName());
                ApplianceEntity existingEntity = await this
                        .ApplianceDataProvider
                        .FindAppliance(
                        subscriptionId: this.Metadata.SubscriptionId,
                        resourceGroupName: this.Metadata.ResourceGroupName,
                        applianceName: this.Metadata.ApplianceName)
                        .ConfigureAwait(continueOnCapturedContext: false);

                MarketplaceAppliancePackage appliancePackage = this.MarketPlacePackageDataProvider.FindMarketplaceAppliancePackage(existingEntity.Properties.PublisherPackageId);
                string publisherTenantId = appliancePackage.TenantId;
                string customerTenantId = RequestCorrelationContext.Current.GetHomeTenantId();
                string workspaceId = existingEntity.GetFullyQualifiedId(this.Metadata.ResourceType);

                try
                {
                    bool isVNetInjectedWorkspace = Utilities.IsVNetInjectedWorkspace(existingEntity.Properties.Parameters, out string vnetId, out string privateSubnetName, out string publicSubnetName, this.Logger.EventSource);

                    if (!isVNetInjectedWorkspace && await Utilities.IsFeatureRegistered(
                        ProviderConstants.Databricks.EnableServiceEndpointsOnManagedSubnets,
                        this.Metadata.ResourceProviderNamespace,
                        this.Metadata.SubscriptionId,
                        this.Logger.EventSource,
                        this.FrontdoorEngine)
                        .ConfigureAwait(false))
                    {
                        this.Logger.LogDebug(operationName, $"Enabling service endpoints on managed vnet subnets of workspace '{workspaceId}' due to (AFEC - {ProviderConstants.Databricks.EnableServiceEndpointsOnManagedSubnets}).");

                        await this.ApplianceEngine.EnableServiceEndpoints(
                            existingEntity,
                            publisherTenantId,
                            this.Metadata.ResourceType,
                            this.Metadata.ResourceProviderNamespace)
                            .ConfigureAwait(false);
                    }
                }
                catch (Exception exception)
                {
                    string errorMessage = $"Failed to enable service endpoints on managed vnet subnets of workspace '{workspaceId}'. Failure Reason: {exception.Message}";
                    ErrorResponseMessage enableServiceEndpointsErrorMessage = Utilities.GetEnableServiceEndpointsOnManagedSubnetsError(workspaceId, exception.Message);
                    this.Logger.LogError(operationName, errorMessage);
                    this.Metadata.OperationResponseStatusCode = HttpStatusCode.OK;
                    this.Metadata.OperationResponseContent = AsyncOperationResult.GetAsyncOperationResult(
                        ProvisioningState.Failed,
                        enableServiceEndpointsErrorMessage.Error.Code,
                        enableServiceEndpointsErrorMessage.Error.Message)
                        .ToJToken();

                    await this
                        .ApplianceDataProvider
                        .SetApplianceProvisioningState(
                            subscriptionId: this.Metadata.SubscriptionId,
                            resourceGroupName: this.Metadata.ResourceGroupName,
                            applianceName: this.Metadata.ApplianceName,
                            provisioningState: ProvisioningState.Failed)
                        .ConfigureAwait(continueOnCapturedContext: false);

                    return new JobExecutionResult
                    {
                        Status = JobExecutionStatus.Faulted,
                        Message = errorMessage,
                        Details = enableServiceEndpointsErrorMessage.Error.Message,
                        NextMetadata = this.Metadata.ToJson()
                    };
                }

                try
                {
                    await this.ApplianceEngine.EnableManagedIdentityForStorageAccount(
                            existingEntity,
                            publisherTenantId,
                            this.Metadata.ResourceType,
                            this.Metadata.ResourceProviderNamespace,
                            RequestCorrelationContext.Current.ApiVersion)
                        .ConfigureAwait(false);
                }
                catch (Exception exception)
                {
                    string errorMessage = $"Failed to enable managed idenity for storage account of workspace '{workspaceId}'. Failure Reason: {exception.Message}";
                    ErrorResponseMessage enableMsiErrorMessage = Utilities.GetEnableMsiForStorageAccountError(workspaceId, exception.Message);
                    this.Logger.LogDebug(operationName, errorMessage);
                    this.Metadata.OperationResponseStatusCode = HttpStatusCode.OK;
                    this.Metadata.OperationResponseContent = AsyncOperationResult.GetAsyncOperationResult(
                            ProvisioningState.Failed,
                            enableMsiErrorMessage.Error.Code,
                            enableMsiErrorMessage.Error.Message)
                        .ToJToken();

                    await this
                        .ApplianceDataProvider
                        .SetApplianceProvisioningState(
                            subscriptionId: this.Metadata.SubscriptionId,
                            resourceGroupName: this.Metadata.ResourceGroupName,
                            applianceName: this.Metadata.ApplianceName,
                            provisioningState: ProvisioningState.Failed)
                        .ConfigureAwait(continueOnCapturedContext: false);

                    return new JobExecutionResult
                    {
                        Status = JobExecutionStatus.Faulted,
                        Message = errorMessage,
                        Details = enableMsiErrorMessage.Error.Message,
                        NextMetadata = this.Metadata.ToJson()
                    };
                }

                // Get Disk Encryption Set if managed disk encryption is enabled
                if (existingEntity.Properties.EncryptionProperties?.EncryptionEntities?.ManagedDisk != null)
                {
                    var diskEncryptionSetName = Utilities.GenerateUniqueDESName(existingEntity.Properties.ManagedResourceGroupId, existingEntity.SubscriptionId);

                    try
                    {
                        this.Logger.LogDebug(
                        operationName: operationName,
                        format: "Managed Disk Encryption has been enabled for workspace: {0}, retrieving the disk encryption set in MRG",
                        arg0: existingEntity.GetFullyQualifiedId(this.Metadata.ResourceType));

                        Common.Definitions.DiskEncryptionSetDefinition diskEncryptionResponse = await this.ApplianceEngine
                                .GetDiskEncryptionSet(
                                    appliance: existingEntity,
                                    publisherTenantId: publisherTenantId,
                                    resourceType: this.Metadata.ResourceType,
                                    resourceProviderNamespace: this.Metadata.ResourceProviderNamespace)
                                .ConfigureAwait(continueOnCapturedContext: false);

                        if (diskEncryptionResponse == null)
                        {
                            this.Logger.LogDebug(operationName, $"Failed to retrieve disk encryption set in managed resource group - '{existingEntity.Properties.ManagedResourceGroupId}'.");

                            throw new ErrorResponseMessageException(
                                HttpStatusCode.InternalServerError,
                                ErrorResponseCode.DiskEncryptionSetReadFailure,
                                ErrorResponseMessages.DiskEncryptionSetReadFailure.ToLocalizedMessage(diskEncryptionSetName));
                        }

                        existingEntity.PopulateManagedDiskEncryptionDetails(diskEncryptionResponse);

                        await this
                            .ApplianceDataProvider
                            .SaveAppliance(existingEntity)
                            .ConfigureAwait(false);
                    }
                    catch (DiskEncryptionSetOperationException exception)
                    {
                        this.Logger.LogDebug(operationName, $"Failed to GET disk encryption set in managed resource group - '{existingEntity.Properties.ManagedResourceGroupId}'.");

                        throw new ErrorResponseMessageException(
                            HttpStatusCode.BadRequest,
                            ErrorResponseCode.DiskNotFound,
                            ErrorResponseMessages.DiskEncryptionSetGetFailed.ToLocalizedMessage(diskEncryptionSetName, exception.Message));
                    }
                }

                // Skip DB workspace init for subscriptions registered with related feature;
                // This should works only in staging environment
                bool skipControlPlane = await this.ApplianceEngine.ShouldSkipControlPlane(existingEntity);
                if (!skipControlPlane)
                {
                    WorkspaceInitializeResponse response = await this.ApplianceEngine
                        .InitializeWorkspace(
                            existingEntity,
                            customerTenantId,
                            this.Metadata.ResourceType,
                            this.Metadata.ResourceProviderNamespace,
                            existingEntity.GetDatabricksBackendApiVersion(this.Logger.EventSource))
                        .ConfigureAwait(false);

                    this.Logger.LogDebug($"Response is {JToken.FromObject(response)}");

                    // Check if polling is required
                    if (response.RequiresPolling)
                    {
                        this.Logger.LogDebug(
                            operationName,
                            $"Polling required for workspace operation. PollingState: {response.PollingState}, OperationId: {response.OperationId}");

                        // Update metadata with polling information
                        this.Metadata.SetAccountApiPollingState(response.PollingState);
                        this.Metadata.SetAccountApiOperationId(response.OperationId);
                        this.Metadata.SetAccountApiOperationStartTime(DateTime.UtcNow);
                        this.Metadata.SetAccountApiBaseUri(response.BaseUri);
                        this.Metadata.SetAccountApiAudience(response.Audience);
                        this.Metadata.ResourceOperation = response.ResourceOperation;

                        // Save the current state of the entity
                        await this
                            .ApplianceDataProvider
                            .ReplaceAppliance(existingEntity)
                            .ConfigureAwait(false);

                        return new JobExecutionResult
                        {
                            Status = JobExecutionStatus.Postponed,
                            NextExecutionTime = DateTime.UtcNow.Add(TimeSpan.FromSeconds(response.PollingIntervalSeconds)),
                            Message = $"Waiting for Account API operation to complete for workspace '{response.OperationId}'",
                            NextMetadata = this.Metadata.ToJson()
                        };
                    }

                    if (existingEntity.Properties.ProvisioningState == ProvisioningState.Accepted)
                    {
                        if (response.Status != OperationStatus.Success || response.WorkspaceDetails == null)
                        {
                            string errorMessage = $"Failed to initialize the workspace '{workspaceId}'. Job faulted";
                            this.Logger.LogDebug(operationName, errorMessage);

                            this.Metadata.OperationResponseStatusCode = HttpStatusCode.OK;
                            this.Metadata.OperationResponseContent = AsyncOperationResult.GetAsyncOperationResult(
                                    ProvisioningState.Failed,
                                    response.ErrorResponseMessage?.Error?.Code ?? ErrorResponseCode.ErrorInitializingWorkspace.ToString(),
                                    response.ErrorResponseMessage?.Error?.Message ?? ErrorResponseMessages.ErrorInitializingWorkspace.ToLocalizedMessage(ErrorResponseMessages.InternalServerError.ToLocalizedMessage(RequestCorrelationContext.Current.CorrelationId)))
                                .ToJToken();
                            var errorCode = response.ErrorResponseMessage?.Error?.Code;

                            if (!string.IsNullOrEmpty(errorCode) && errorCode.Equals(ErrorResponseCode.PermissionDenied.ToString(), StringComparison.InvariantCultureIgnoreCase))
                            {
                                this.Logger.LogDebug($"Adding ResponsiblePartyForFailure to metadata, error code is  {errorCode}");
                                this.Metadata.SLIErrorInfo = SLIErrorInfo.GetSLIErrorInfo(ResponsibleParty.Client, errorCode);
                            }

                            await this
                                .ApplianceDataProvider
                                .SetApplianceProvisioningState(
                                    subscriptionId: this.Metadata.SubscriptionId,
                                    resourceGroupName: this.Metadata.ResourceGroupName,
                                    applianceName: this.Metadata.ApplianceName,
                                    provisioningState: ProvisioningState.Failed)
                                .ConfigureAwait(continueOnCapturedContext: false);

                            return new JobExecutionResult
                            {
                                Status = JobExecutionStatus.Faulted,
                                Message = errorMessage,
                                Details = HttpStatusCode.InternalServerError.ToString(),
                                NextMetadata = this.Metadata.ToJson()
                            };
                        }

                        if (existingEntity.Metadata != null)
                        {
                            this.Logger.LogDebug(operationName, $"Workspace - '{workspaceId}' is allowed to create private link. Setting Appliance metadata");
                            existingEntity.Metadata.IsPrivateLinkAllowed = response.WorkspaceDetails?.IsPrivateLinkAllowed;
                        }

                        existingEntity.Properties.WorkspaceUrl = response.WorkspaceDetails.WorkspaceURL;
                        existingEntity.Properties.WorkspaceId = response.WorkspaceDetails.WorkspaceId;
                        existingEntity.Properties.IsUcEnabled = response.WorkspaceDetails.IsUcEnabled;

                        if (existingEntity.Properties.RequiredNsgRules.GetRequiredNsgRulesMemberDefaultValue() == RequiredNetworkSecurityGroupType.NoAzureServiceRules
                        && await Utilities.IsFeatureRegistered(
                               ProviderConstants.Databricks.AllowNoAzureServiceRules,
                               this.Metadata.ResourceProviderNamespace,
                               this.Metadata.SubscriptionId,
                               this.Logger.EventSource,
                               this.FrontdoorEngine)
                           .ConfigureAwait(false))
                        {
                            this.Logger.LogDebug(operationName, $"Workspace - '{workspaceId}' is allowed to store Private Link assets details");

                            existingEntity.Properties.PrivateLinkAssets = response.WorkspaceDetails.PrivateLinkAssets;
                        }
                    }
                }
                else
                {
                    this.Logger.LogDebug(operationName, $"Skip the step to Initialize DB control plane since the subscription registed to the feature '{ProviderConstants.Databricks.MicrosoftDatabricksSkipControlPlaneFeature}'.");
                }
                existingEntity.Properties.ProvisioningState = ProvisioningState.Succeeded;

                await this
                    .ApplianceDataProvider
                    .SaveAppliance(existingEntity)
                    .ConfigureAwait(false);

                this.Logger.LogDebug(operationName, $"The workspace '{workspaceId}' updated with DB workspace details");

                this.Metadata.OperationResponseStatusCode = HttpStatusCode.OK;
                this.Metadata.OperationResponseContent = AsyncOperationResult
                    .GetAsyncOperationResult(ProvisioningState.Succeeded)
                    .ToJToken();

                return new JobExecutionResult
                {
                    Status = JobExecutionStatus.Succeeded,
                    Message = $"Successfully provisioned and initialized the workspace '{workspaceId}'. Completing job",
                    Details = HttpStatusCode.OK.ToString(),
                    NextMetadata = this.Metadata.ToJson()
                };
            }
            catch (Exception ex)
            {
                await this
                    .ApplianceDataProvider
                    .SetApplianceProvisioningState(
                        subscriptionId: this.Metadata.SubscriptionId,
                        resourceGroupName: this.Metadata.ResourceGroupName,
                        applianceName: this.Metadata.ApplianceName,
                        provisioningState: ProvisioningState.Failed)
                    .ConfigureAwait(continueOnCapturedContext: false);

                this.Metadata.OperationResponseStatusCode = HttpStatusCode.OK;
                this.Metadata.OperationResponseContent = AsyncOperationResult
                    .GetAsyncOperationResult(ProvisioningState.Failed)
                    .ToJToken();

                return new JobExecutionResult
                {
                    Status = JobExecutionStatus.Faulted,
                    Message = $"Failed to create the workspace '{ex}'. Faulting job",
                    NextMetadata = this.Metadata.ToJson()
                };
            }
        }
    }
}
