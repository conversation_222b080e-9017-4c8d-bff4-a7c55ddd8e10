﻿//------------------------------------------------------------
// Copyright (c) Microsoft Corporation.  All rights reserved.
//------------------------------------------------------------

namespace Microsoft.WindowsAzure.ResourceStack.Providers.Worker.Engines
{
    using System;
    using System.Threading.Tasks;
    using Microsoft.WindowsAzure.ResourceStack.Common.BackgroundJobs;
    using Microsoft.WindowsAzure.ResourceStack.Common.Collections;
    using Microsoft.WindowsAzure.ResourceStack.Common.EventSources;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Application.Data.DataProviders;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Application.Data.Definitions;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Application.Data.Definitions.Enums;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Application.Data.Entities;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Common.Components;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Common.Data;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Common.Definitions;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Common.Definitions.Deployments;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Common.Responses;
    using Newtonsoft.Json.Linq;

    /// <summary>
    /// Interface that specifies the behaviors of the <c>ApplianceEngine</c> class.
    /// </summary>
    public interface IApplianceEngine
    {
        /// <summary>
        /// Create managed resource group if required.
        /// </summary>
        /// <param name="existingEntity">The appliance object.</param>
        /// <param name="publisherTenantId">The ISV's tenant Id.</param>
        /// <param name="customerTenantId">The customer tenant identifier.</param>
        /// <param name="resourceType">The resource type.</param>
        /// <param name="resourceProviderNamespace">The resource provider name space.</param>
        Task CreateManagedResourceGroupIfNotExists(
            ApplianceEntity existingEntity,
            string publisherTenantId,
            string customerTenantId,
            string resourceType,
            string resourceProviderNamespace);

        /// <summary>
        /// Get the operation status for disk encryption set operation.
        /// </summary>
        /// <param name="operation">The disk encryption set operation being performed.</param>
        /// <param name="diskEncryptionSetId">The disk encryption set ID.</param>
        /// <param name="customerTenantId">The customer tenant Id.</param>
        /// <param name="resourceProviderNamespace">The resource provider name space.</param>
        /// <param name="asyncTrackingUri">The async tracking URI</param>
        Task<DiskEncryptionSetOperationResponse> GetDiskEncryptionSetOperationStatus(
            DiskEncryptionSetOperation operation,
            string diskEncryptionSetId,
            string customerTenantId,
            string resourceProviderNamespace,
            Uri asyncTrackingUri);

        /// <summary>
        /// Get disk encryption set in MRG for managed disk encryption.
        /// </summary>
        /// <param name="appliance">The appliance object.</param>
        /// <param name="publisherTenantId">The ISV's tenant Id.</param>
        /// <param name="resourceType">The resource type.</param>
        /// <param name="resourceProviderNamespace">The resource provider name space.</param>
        Task<DiskEncryptionSetDefinition> GetDiskEncryptionSet(
            ApplianceEntity appliance,
            string publisherTenantId,
            string resourceType,
            string resourceProviderNamespace);

        /// <summary>
        /// Enable managed identity for storage account
        /// </summary>
        /// <param name="appliance">appliance parameter</param>
        /// <param name="customerTenantId">customer tenant id</param>
        /// <param name="resourceType">resource type</param>
        /// <param name="resourceProviderNamespace">resource provider namespace</param>
        /// <param name="apiVersion">The API version.</param>
        Task EnableManagedIdentityForStorageAccount(
            ApplianceEntity appliance,
            string customerTenantId,
            string resourceType,
            string resourceProviderNamespace,
            string apiVersion);

        /// <summary>
        /// Enable service endpoints on Managed VNET subnets
        /// </summary>
        /// <param name="appliance">appliance parameter</param>
        /// <param name="publisherTenantId">The application publisher's tenant Id.</param>
        /// <param name="resourceType">resource type</param>
        /// <param name="resourceProviderNamespace">resource provider namespace</param>
        Task EnableServiceEndpoints(
            ApplianceEntity appliance,
            string publisherTenantId,
            string resourceType,
            string resourceProviderNamespace);

        /// <summary>
        /// Create a role assignment at a given scope for a given principalId.
        /// </summary>
        /// <param name="principalId">The principal Id.</param>
        /// <param name="principalType">Type of service principal.</param>
        /// <param name="scope">The scope.</param>
        /// <param name="roleDefinitionId">The role definition Id.</param>
        /// <param name="subscriptionId">The subscription Id.</param>
        /// <param name="authenticationTenantId">The authentication tenant Id.</param>
        /// <param name="resourceProviderNamespace">The resource provider namespace.</param>
        Task CreateRoleAssignmentIfNotExists(
            string principalId,
            PrincipalType principalType,
            string scope,
            string roleDefinitionId,
            string subscriptionId,
            string authenticationTenantId,
            string resourceProviderNamespace);

        /// <summary>
        /// delete role assignments at a given scope for a given principalId.
        /// </summary>
        /// <param name="principalId">The principal Id.</param>
        /// <param name="scope">The scope.</param>
        /// <param name="roleDefinitionId">The role definition Id.</param>
        /// <param name="subscriptionId">The subscription Id.</param>
        /// <param name="authenticationTenantId">The authentication tenant Id.</param>
        /// <param name="resourceProviderNamespace">The resource provider namespace.</param>
        Task DeleteRoleAssignmentIfExists(
            string principalId,
            string scope,
            string roleDefinitionId,
            string subscriptionId,
            string authenticationTenantId,
            string resourceProviderNamespace);

        /// <summary>
        /// Create role assignments for the given authorization properties by the appliance package provider.
        /// </summary>
        /// <param name="appliance">The appliance object.</param>
        /// <param name="authorizations">The authorizations.</param>
        /// <param name="tenantId">The tenant Id.</param>
        /// <param name="resourceType">The resource type.</param>
        /// <param name="resourceProviderNamespace">The resource provider name space.</param>
        Task CreateProviderAuthorizationRoleAssignments(
            ApplianceEntity appliance,
            ApplicationAuthorization[] authorizations,
            string tenantId,
            string resourceType,
            string resourceProviderNamespace);

        /// <summary>
        /// Create policy assignment with the given policy definition by the appliance package provider.
        /// </summary>
        /// <param name="appliance">The appliance object.</param>
        /// <param name="applianceProviderTenantId">The appliance provider's tenant Id.</param>
        /// <param name="providerPolicy">The policy information.</param>
        /// <param name="resourceType">The resource type.</param>
        /// <param name="resourceProviderNamespace">The resource provider name space.</param>
        Task CreatePolicyAssignment(
            ApplianceEntity appliance,
            string applianceProviderTenantId,
            ApplicationPolicy providerPolicy,
            string resourceType,
            string resourceProviderNamespace);

        /// <summary>
        /// Create a deployment to provision resources for the appliance.
        /// </summary>
        /// <param name="appliance">The appliance object.</param>
        /// <param name="templateParameters">The template parameters.</param>
        /// <param name="appliancePackage">The appliance package.</param>
        /// <param name="publisherTenantId">The application publisher's tenant Id.</param>
        /// <param name="resourceType">The resource type.</param>
        /// <param name="resourceProviderNamespace">The resource provider name space.</param>
        /// <param name="deploymentMode">ARM Template Deployment Mode like Incremental, Complete</param>
        Task<DeploymentResponse> CreateApplianceDeployment(
            ApplianceEntity appliance,
            InsensitiveDictionary<JToken> templateParameters,
            AppliancePackage appliancePackage,
            string publisherTenantId,
            string resourceType,
            string resourceProviderNamespace,
            DeploymentMode deploymentMode = DeploymentMode.Complete);

        /// <summary>
        /// Creates authorization and deny assignment for the application.
        /// </summary>
        /// <param name="appliance">The appliance object.</param>
        /// <param name="appliancePackage">The appliance package.</param>
        /// <param name="customerTenantId">The customer tenant identifier.</param>
        /// <param name="publisherTenantId">The tenant identifier.</param>
        /// <param name="resourceType">The resource type.</param>
        /// <param name="resourceProviderNamespace">The resource provider namespace.</param>
        /// <param name="excludedPrincipalId">Excluded PrincipalId for deny assignment definition.</param>
        Task CreateAuthorizationForApplication(
            ApplianceEntity appliance,
            AppliancePackage appliancePackage,
            string customerTenantId,
            string publisherTenantId,
            string resourceType,
            string resourceProviderNamespace,
            string excludedPrincipalId);

        /// <summary>
        /// Initialize the workspace in the control plane
        /// </summary>
        /// <param name="existingEntity">The appliance object.</param>
        /// <param name="customerTenantId">The customer tenant identifier.</param>
        /// <param name="resourceType">The resource type.</param>
        /// <param name="resourceProviderNamespace">The resource provider namespace.</param>
        /// <param name="apiVersion">The <c>api</c> version</param>
        Task<WorkspaceInitializeResponse> InitializeWorkspace(
            ApplianceEntity existingEntity,
            string customerTenantId,
            string resourceType,
            string resourceProviderNamespace,
            string apiVersion);

        /// <summary>
        /// Calls DB for the workspace DELETE notification.
        /// </summary>
        /// <param name="subscriptionId">The subscription Id.</param>
        /// <param name="workspaceId">The workspace Id.</param>
        /// <param name="resourceProviderNamespace">The resource provider namespace.</param>
        /// <param name="appliance">The appliance.</param>
        /// <param name="retainUc">This is to let Databricks know whether to ratainUc or not.</param>
        /// <param name="apiVersion">The API version.</param>
        /// <returns>Returns a WorkspaceInitializeResponse with polling information if needed.</returns>
        Task<WorkspaceInitializeResponse> DBWorkspaceDeleteNotification(
            string subscriptionId,
            string workspaceId,
            string resourceProviderNamespace,
            ApplianceEntity appliance,
            bool retainUc,
            string apiVersion);

        /// <summary>
        /// Deletes the Managed Resource Group, using customer tenant Id/ publisher tenant Id.
        /// </summary>
        /// <param name="applianceEntity">The Appliance Entity.</param>
        /// <param name="managedResourceGroupRequest">The managed resource group request.</param>
        /// <param name="resourceProviderNamespace">The resource provider namespace.</param>
        /// <param name="workspaceId">The Workspace Id.</param>
        /// <param name="customerTenantId">The Customer Tenant Id.</param>
        /// <param name="publisherTenantId">The Publisher Tenant Id.</param>
        /// <param name="logger">The Logger.</param>
        /// <returns>Returns the long operation uri.</returns>
        Task<Uri> DeleteManagedResourceGroup(
            ApplianceEntity applianceEntity,
            ResourceGroupRequestMatch managedResourceGroupRequest,
            string resourceProviderNamespace,
            string workspaceId,
            string customerTenantId,
            string publisherTenantId,
            ICommonEventSource logger);

        /// <summary>
        /// Deletes dbfs containers in dbfs account leaving uc container.
        /// </summary>
        /// <param name="authenticationTenantId">The tenant Id to be used to get authentication token.</param>
        /// <param name="dbfsResourceId">Dbfs Account Resource Id.</param>
        /// <param name="resourceProviderNamespace">The resource provider name space.</param>
        Task DeleteDbfsContainers(
            string authenticationTenantId,
            string dbfsResourceId,
            string resourceProviderNamespace);

        /// <summary>
        /// Creates or updates the deny assignment on the resource group that contains the application's resources
        /// </summary>
        /// <param name="publisherTenantId">The publisher tenant identifier.</param>
        /// <param name="customerTenantId">The customer tenant identifier.</param>
        /// <param name="appliance">The appliance object.</param>
        /// <param name="resourceType">The resource type.</param>
        /// <param name="resourceProviderNamespace">The resource provider namespace.</param>
        Task CreateOrUpdateDenyAssignmentsForApplication(
            string publisherTenantId,
            string customerTenantId,
            ApplianceEntity appliance,
            string resourceType,
            string resourceProviderNamespace,
            string excludedPrincipalId);

        /// <summary>
        /// Gets the Databricks Blob data provider.
        /// </summary>
        IDatabricksBlobDataProvider GetDatabricksBlobDataProvider();

        /// <summary>
        /// Handle Private Dbfs Configuration for the workspace.
        /// </summary>
        /// <param name="incomingEntity">incoming workspace entity</param>
        /// <param name="existingConnector">existing access connector associated with workspace</param>
        /// <param name="existingDefaultStorageFirewall">existing default storage firewall associated with workspace</param>
        /// <param name="publisherTenantId">Publisher TenantId for workspace</param>
        /// <param name="dbfsResourceId">DBFS account resource Id</param>
        /// <returns></returns>
        Task<Tuple<JobExecutionResult, AccessConnectorIdEntity, DefaultStorageFirewall?>> ConfigureDefaultStorageFirewall(
            ApplianceEntity incomingEntity,
            AccessConnectorIdEntity existingConnector,
            DefaultStorageFirewall? existingDefaultStorageFirewall,
            string publisherTenantId,
            string dbfsResourceId);

        /// <summary>
        /// Perform Access connector Update with WorkspaceId verification
        /// </summary>
        /// <param name="accessConnectorResourceId">access connector resource id</param>
        /// <param name="workspaceResourceId">workspace resource id to be added to connector</param>
        /// <param name="mrgResourceId">managed resource group resource id</param>
        /// <returns></returns>
        Task AddWorkspaceIdToConnectorEntityReferByList(string accessConnectorResourceId, string workspaceResourceId, string mrgResourceId);

        /// <summary>
        /// Perform Access connector Update with WorkspaceId verification
        /// </summary>
        /// <param name="accessConnectorResourceId">access connector resource id</param>
        /// <param name="workspaceResourceId">workspace resource id to be removed from connector</param>
        /// <returns></returns>
        Task RemoveWorkspaceIdFromConnectorEntityReferByList(string accessConnectorResourceId, string workspaceResourceId);

        /// <summary>
        /// Deletes all the resources in Mrg Except Uc Resources ie, Storage Account, Access Connector
        /// </summary>
        /// <param name="authenticationTenantId">Authentication TenantId for ARM Calls</param>
        /// <param name="resourcesToBeDeleted">Array of resources to be deleted</param>
        /// <param name="resourceProviderNamespace">The resource provider namespace.</param>
        /// <returns>Returns Azure Async Operation Uri</returns>
        Task<Uri> DeleteResourcesInBulk(
            string authenticationTenantId,
            ARMResourceDefinition[] resourcesToBeDeleted, string resourceProviderNamespace);

        /// <summary>
        /// Checks if this is a staging environment
        /// </summary>
        /// <param name="applianceEntity">The appliance object</param>
        Task<bool> IsStagingEnvironment(ApplianceEntity applianceEntity);

        /// <summary>
        /// Check if needs to bypass all interactions with the RP backend, including notifications for workspace initialization, updates, and deletion
        /// </summary>
        /// <param name="applianceEntity">The appliance object</param>
        Task<bool> ShouldSkipControlPlane(ApplianceEntity applianceEntity);
    }
}
