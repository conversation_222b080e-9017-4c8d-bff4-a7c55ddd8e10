﻿//------------------------------------------------------------
// Copyright (c) Microsoft Corporation.  All rights reserved.
//------------------------------------------------------------

namespace Microsoft.WindowsAzure.ResourceStack.Providers.Worker.Engines
{
    using Microsoft.WindowsAzure.ResourceStack.Common.Algorithms;
    using Microsoft.WindowsAzure.ResourceStack.Common.BackgroundJobs;
    using Microsoft.WindowsAzure.ResourceStack.Common.Collections;
    using Microsoft.WindowsAzure.ResourceStack.Common.EventSources;
    using Microsoft.WindowsAzure.ResourceStack.Common.Extensions;
    using Microsoft.WindowsAzure.ResourceStack.Common.Instrumentation;
    using Microsoft.WindowsAzure.ResourceStack.Common.Json;
    using Microsoft.WindowsAzure.ResourceStack.Common.Services;
    using Microsoft.WindowsAzure.ResourceStack.Common.Storage;
    using Microsoft.WindowsAzure.ResourceStack.Common.Utilities;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Application.Data.Components;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Application.Data.DataProviders;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Application.Data.Definitions;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Application.Data.Definitions.DBAsyncContract;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Application.Data.Definitions.Enums;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Application.Data.Entities;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Application.Data.Extensions;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Application.Data.Metadata;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Application.Services;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Common.Components;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Common.Constants;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Common.Data;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Common.Definitions;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Common.Definitions.Deployments;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Common.Engines;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Common.ErrorResponses;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Common.Extensions;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Common.Responses;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Common.Utilities;
    using Newtonsoft.Json.Linq;
    using System;
    using System.Collections.Generic;
    using System.Diagnostics;
    using System.IO;
    using System.Linq;
    using System.Net;
    using System.Net.Http;
    using System.Reflection;
    using System.Threading.Tasks;

    /// <summary>
    /// The appliance engine.
    /// </summary>
    public class ApplianceEngine : IApplianceEngine
    {
        #region Properties and constructors
        /// <summary>
        /// The API version of the policy assignments resource type.
        /// </summary>
        private static readonly string PolicyAssignmentsApiVersion = "2017-06-01-preview";

        /// <summary>
        /// Gets the storage update API version.
        /// </summary>
        /// <value>
        /// The storage update API version.
        /// </value>
        private static string StorageApiVersion
        {
            get
            {
                return CloudConfigurationManager.GetConfiguration("StorageApiVersion");
            }
        }

        /// <summary>
        /// Gets the publisher app principal Type for authorization.
        /// </summary>
        /// <param name="packageId">The appliance package ID.</param>
        private static PrincipalType AuthPrincipalType(string packageId)
        {
            var principalType = (PrincipalType)Enum.Parse(typeof(PrincipalType), CloudConfigurationManager.GetConfiguration($"AuthPrincipalType_{packageId}"), true);
            return principalType;
        }

        /// <summary>
        /// Gets the service endpoint update API version.
        /// </summary>
        /// <value>
        /// The service endpoint update API version.
        /// </value>
        private static string ServiceEndpointsApiVersion
        {
            get
            {
                return CloudConfigurationManager.GetConfiguration("ServiceEndpointsApiVersion");
            }
        }

        /// <summary>
        /// Gets the deny assignment actions.
        /// </summary>
        private static string[] DenyAssignmentsActions
        {
            get
            {
                return CloudConfigurationManager
                    .GetMultivaluedConfiguration(ProviderConstants.Databricks.DenyAssignmentsActionsKey, ProviderConstants.Databricks.ConfigValueSemicolonSeparator)
                    .Where(action => !string.IsNullOrWhiteSpace(action))
                    .ToArray();
            }
        }

        /// <summary>
        /// Gets the deny assignment not actions.
        /// </summary>
        private static string[] DenyAssignmentsNotActions
        {
            get
            {
                return CloudConfigurationManager
                    .GetMultivaluedConfiguration(ProviderConstants.Databricks.DenyAssignmentsNotActionsKey, ProviderConstants.Databricks.ConfigValueSemicolonSeparator)
                    .Where(action => !string.IsNullOrWhiteSpace(action))
                    .ToArray();
            }
        }

        /// <summary>
        /// Gets the deny assignment Data Actions.
        /// </summary>
        private static string[] DenyAssignmentsDataActions
        {
            get
            {
                return CloudConfigurationManager
                    .GetMultivaluedConfiguration(ProviderConstants.Databricks.DenyAssignmentsDataActionsKey, ProviderConstants.Databricks.ConfigValueSemicolonSeparator)
                    .Where(action => !string.IsNullOrWhiteSpace(action))
                    .ToArray();
            }
        }

        /// <summary>
        /// Gets the deny assignment Not Data Actions.
        /// </summary>
        private static string[] DenyAssignmentsNotDataActions
        {
            get
            {
                return CloudConfigurationManager
                    .GetMultivaluedConfiguration(ProviderConstants.Databricks.DenyAssignmentsNotDataActionsKey, ProviderConstants.Databricks.ConfigValueSemicolonSeparator)
                    .Where(action => !string.IsNullOrWhiteSpace(action))
                    .ToArray();
            }
        }

        /// <summary>
        /// Gets or sets the event source.
        /// </summary>
        private ICommonEventSource EventSource { get; set; }

        /// <summary>
        /// Gets or sets the front door engine.
        /// </summary>
        private IFrontdoorEngine FrontdoorEngine { get; set; }

        /// <summary>
        /// Gets or the DataBricks Network Intent Policy Blob Data Provider
        /// </summary>
        private readonly IDatabricksBlobDataProvider DataBricksBlobDataProvider;

        private readonly IStorageAccountManager StorageAccountManager;

        private readonly IAccessConnectorDataProvider AccessConnectorDataProvider;

        private readonly IApplianceDataProvider ApplianceDataProvider;

        private readonly IVirtualNetworkManager VirtualNetworkManager;

        private readonly ApplianceJobMetadata Metadata;

        /// <summary>
        /// Initializes a new instance of the <see cref="ApplianceEngine" /> class.
        /// </summary>
        /// <param name="eventSource">The event source.</param>
        /// <param name="frontdoorEngine">The front door engine.</param>
        /// <param name="databricksBlobDataProvider">The DataBricks Network Intent Policy Blob Data Provider</param>
        public ApplianceEngine(
            ICommonEventSource eventSource,
            IFrontdoorEngine frontdoorEngine,
            IDatabricksBlobDataProvider databricksBlobDataProvider,
            IAccessConnectorDataProvider accessConnectorDataProvider,
            IApplianceDataProvider applianceDataProvider,
            ApplianceJobMetadata metadata)
        {
            this.EventSource = eventSource;
            this.VirtualNetworkManager = new VirtualNetworkManager(frontdoorEngine, eventSource, databricksBlobDataProvider);
            this.FrontdoorEngine = frontdoorEngine;
            this.DataBricksBlobDataProvider = databricksBlobDataProvider;
            this.StorageAccountManager = new StorageAccountManager(frontdoorEngine, eventSource);
            this.AccessConnectorDataProvider = accessConnectorDataProvider;
            this.ApplianceDataProvider = applianceDataProvider;
            this.Metadata = metadata;
        }

        #endregion

        /// <summary>
        /// Create managed resource group if required.
        /// </summary>
        /// <param name="existingEntity">The appliance object.</param>
        /// <param name="publisherTenantId">The ISV's tenant Id.</param>
        /// <param name="customerTenantId">The customer tenant identifier.</param>
        /// <param name="resourceType">The resource type.</param>
        /// <param name="resourceProviderNamespace">The resource provider name space.</param>
        public async Task CreateManagedResourceGroupIfNotExists(
            ApplianceEntity existingEntity,
            string publisherTenantId,
            string customerTenantId,
            string resourceType,
            string resourceProviderNamespace)
        {
            var operationName = this.GetOperationName(Utilities.GetAsyncMethodName());

            this.EventSource.Debug(operationName,
                "Create managed resource group '{0}' of the appliance '{1}' if it does not exist.",
                existingEntity.Properties.ManagedResourceGroupId,
                existingEntity.GetFullyQualifiedId(resourceType));

            var resourceGroupRequest = ResourceGroupRequestMatch.FromFullyQualifiedId(existingEntity.Properties.ManagedResourceGroupId.Trim());

            var managedResourceGroup = await this.FrontdoorEngine
                .GetResourceGroup(
                    publisherTenantId,
                    resourceGroupRequest.SubscriptionId,
                    resourceGroupRequest.ResourceGroup,
                    resourceProviderNamespace)
                .ConfigureAwait(false);

            if (managedResourceGroup != null && !NormalizationUtility.ScopeEquals(managedResourceGroup.ManagedBy, existingEntity.GetFullyQualifiedId(resourceType)))
            {
                this.EventSource.Debug(operationName,
                    "Managed resource group '{0}' of the appliance '{1}' has mismatched ManagedBy property '{2}'.",
                    existingEntity.Properties.ManagedResourceGroupId,
                    existingEntity.GetFullyQualifiedId(resourceType),
                    managedResourceGroup.ManagedBy);

                throw new ResourceGroupOperationException(
                    HttpStatusCode.Conflict,
                    ErrorResponseCode.ApplianceManagedResourceGroupManagedByMismatch.ToString(),
                    ErrorResponseMessages.ApplianceManagedResourceGroupManagedByMismatch.ToLocalizedMessage(managedResourceGroup.Id));
            }

            await this
                .CreateManagedResourceGroup(
                    existingEntity,
                    publisherTenantId,
                    customerTenantId,
                    resourceType,
                    resourceProviderNamespace)
                .ConfigureAwait(false);
        }

        /// <summary>
        /// Get the operation status for disk encryption set operation.
        /// </summary>
        /// <param name="operation">The disk encryption set operation being performed.</param>
        /// <param name="diskEncryptionSetId">The disk encryption set ID.</param>
        /// <param name="customerTenantId">The customer tenant Id.</param>
        /// <param name="resourceProviderNamespace">The resource provider name space.</param>
        /// <param name="asyncTrackingUri">The async tracking URI</param>
        public async Task<DiskEncryptionSetOperationResponse> GetDiskEncryptionSetOperationStatus(
            DiskEncryptionSetOperation operation,
            string diskEncryptionSetId,
            string customerTenantId,
            string resourceProviderNamespace,
            Uri asyncTrackingUri)
        {
            string actionName = operation.ToString();
            string operationName = this.GetOperationName(Utilities.GetAsyncMethodName());

            ErrorResponseMessage getOperationErrorResponseMessage = null;
            Exception getOperationException = null;
            AsyncOperationResult operationStatus = null;
            DiskEncryptionSetOperationResponse response = null;

            try
            {
                response = await this.FrontdoorEngine.GetDiskEncryptionSetOperationStatus(
                        operation,
                        customerTenantId,
                        diskEncryptionSetId,
                        resourceProviderNamespace,
                        asyncTrackingUri)
                    .ConfigureAwait(false);
            }
            catch (DiskEncryptionSetOperationException desoEx)
            {
                getOperationException = desoEx;
                getOperationErrorResponseMessage = new ErrorResponseMessage(desoEx.ErrorCode, desoEx.Message);
            }
            catch (ErrorResponseMessageException ermEx)
            {
                getOperationException = ermEx;
                getOperationErrorResponseMessage = new ErrorResponseMessage(ermEx.ErrorCode, ermEx.Message);
            }
            catch (ServerErrorResponseMessageException sermEx)
            {
                getOperationException = sermEx;
                getOperationErrorResponseMessage = new ErrorResponseMessage(sermEx.ErrorCode, sermEx.Message);
            }
            catch (Exception ex)
            {
                getOperationException = ex;
                getOperationErrorResponseMessage = new ErrorResponseMessage(
                    ErrorResponseCode.DiskEncryptionSetCreateFailed,
                    ErrorResponseMessages.DiskEncryptionSetCreateFailed.ToLocalizedMessage());
            }

            if (response.Status == OperationStatus.Success)
            {
                this.EventSource.Debug(operationName, $"The {actionName} operation succeeded for disk encryption set '{diskEncryptionSetId}.'");
                return response;
            }

            Utilities.TrySerializeObject(operationStatus?.Error, this.EventSource, out string errorJson);

            string errorMessage = operationStatus?.Error != null
                ? $"Error details: {errorJson}"
                : $"Error in {actionName} operation";
            this.EventSource.Debug(
                operationName,
                $"{actionName} of the disk encryption set with ID: '{diskEncryptionSetId}' has failed. Error: '{errorMessage}', Exception: {Utilities.FlattenException(getOperationException)}");

            var errorResponseMessage = new ErrorResponseMessage(
                operationStatus?.Error?.Code ?? ErrorResponseCode.DiskEncryptionSetCreateFailed.ToString(),
                ErrorResponseMessages.DiskEncryptionSetCreateFailed.ToLocalizedMessage());

            response.ErrorResponseMessage = getOperationErrorResponseMessage ?? errorResponseMessage;
            return response;
        }

        /// <summary>
        /// Get disk encryption set in MRG for managed disk encryption.
        /// </summary>
        /// <param name="appliance">The appliance object.</param>
        /// <param name="publisherTenantId">The ISV's tenant Id.</param>
        /// <param name="resourceType">The resource type.</param>
        /// <param name="resourceProviderNamespace">The resource provider name space.</param>
        public async Task<DiskEncryptionSetDefinition> GetDiskEncryptionSet(
            ApplianceEntity appliance,
            string publisherTenantId,
            string resourceType,
            string resourceProviderNamespace)
        {
            this.EventSource.Debug(
               "ApplianceEngine.GetDiskEncryptionSet",
               "Getting disk encryption set for managed disk encryption in MRG: '{0}' of the appliance '{1}'.",
               appliance.Properties.ManagedResourceGroupId,
               appliance.GetFullyQualifiedId(resourceType));

            var mrgId = appliance.Properties.ManagedResourceGroupId;
            var diskEncryptionSetName = Utilities.GenerateUniqueDESName(mrgId, appliance.SubscriptionId);

            try
            {
                var diskEncryptionResponse = await this.FrontdoorEngine
                    .GetManagedDiskEncryptionSet(
                        managedResourceGroupId: appliance.Properties.ManagedResourceGroupId,
                        authenticationTenantId: publisherTenantId,
                        resourceProviderNamespace: resourceProviderNamespace,
                        subscriptionId: appliance.SubscriptionId)
                    .ConfigureAwait(continueOnCapturedContext: false);

                if (diskEncryptionResponse == null)
                {
                    this.EventSource.Debug(
                        operationName: "ApplianceEngine.GetDiskEncryptionSet",
                        $"Unable to get disk encryption set.");
                    throw new ErrorResponseMessageException(
                        HttpStatusCode.InternalServerError,
                        ErrorResponseCode.DiskEncryptionSetReadFailure,
                        ErrorResponseMessages.DiskEncryptionSetReadFailure.ToLocalizedMessage(diskEncryptionSetName));
                }

                return diskEncryptionResponse.DiskEncryptionSetDefinition;
            }
            catch (DiskEncryptionSetOperationException exception)
            {
                this.EventSource.Error(
                    operationName: "ApplianceEngine.GetDiskEncryptionSet",
                    $"Failed to retrieve disk encryption set in managed resource group - '{appliance.Properties.ManagedResourceGroupId}'.");

                throw new ErrorResponseMessageException(
                    HttpStatusCode.BadRequest,
                    ErrorResponseCode.DiskEncryptionSetReadFailure,
                    ErrorResponseMessages.DiskEncryptionSetReadFailure.ToLocalizedMessage(diskEncryptionSetName, exception.Message));
            }
        }

        /// <summary>
        /// Enable managed identity for storage account
        /// </summary>
        /// <param name="appliance">appliance parameter</param>
        /// <param name="publisherTenantId">publisher tenant id</param>
        /// <param name="resourceType">resource type</param>
        /// <param name="resourceProviderNamespace">resource provider namespace</param>
        /// <param name="apiVersion">The API version.</param>
        public async Task EnableManagedIdentityForStorageAccount(
            ApplianceEntity appliance,
            string publisherTenantId,
            string resourceType,
            string resourceProviderNamespace,
            string apiVersion)
        {
            string operationName = this.GetOperationName(Utilities.GetAsyncMethodName());
            string workspaceId = appliance.GetFullyQualifiedId(resourceType);

            if (appliance.Properties.Parameters == null
                || !appliance.Properties.Parameters.TryParseDatabricksPropertiesParameter(ProviderConstants.Storage.PrepareEncryption, this.EventSource, out bool prepareEncryption)
                || prepareEncryption == false)
            {
                this.EventSource.Debug(
                    operationName,
                    $"Not enabling Storage account MSI for the workspace '{workspaceId}' in PublisherTenantId: {publisherTenantId}, Api-version: {apiVersion}.");
                return;
            }

            this.EventSource.Debug(
                operationName,
                $"Enabling storage account MSI for the workspace '{workspaceId}' in PublisherTenantId: {publisherTenantId}, Api-version: {apiVersion}.");

            try
            {
                var subscriptionId = appliance.SubscriptionId;
                var resourceGroupName = ResourceGroupRequestMatch.FromFullyQualifiedId(appliance.Properties.ManagedResourceGroupId.Trim()).ResourceGroup;
                appliance.Properties.Parameters.TryParseDatabricksPropertiesParameter(ProviderConstants.Databricks.StorageAccountNameParameter, this.EventSource, out string storageAccountName);
                var storageUpdateResourceUri = UriTemplateEngine.GetStorageGetPropertiesRequestUri(this.FrontdoorEngine.FrontdoorEndpointUri, subscriptionId, resourceGroupName, storageAccountName, StorageApiVersion);
                var storageUpdateRequest = new StorageDefinition()
                {
                    Identity = new JObject()
                    {
                        new JProperty(ProviderConstants.Storage.ParameterType, ProviderConstants.Storage.SystemAssigned)
                    }
                };

                var storageAccountDefinition = await this.FrontdoorEngine
                       .CallFrontdoor(
                           new HttpMethod("PATCH"),
                           storageUpdateResourceUri,
                           resourceProviderNamespace,
                           publisherTenantId,
                           storageUpdateRequest)
                       .ConfigureAwait(false);

                if (storageAccountDefinition == null)
                {
                    this.EventSource.Debug(
                        operationName,
                        $"The enable managed identity failed for workspace '{workspaceId}'");
                    throw new Exception(Utilities.GetEnableMsiInternalErrorResponseMessage(workspaceId).Error.Message);
                }

                this.EventSource.Debug(operationName, $"Enable managed identity successful for dbfs storage of workspace '{workspaceId}'.");
                appliance.PopulateStorageAccountIdentityEntity(storageAccountDefinition);
            }
            catch (Exception exception)
            {
                this.EventSource.Debug(
                    operationName,
                    $"exception occured during enable managed identity of the workspace '{workspaceId}' in PublisherTenantId: {publisherTenantId}. Error: {Utilities.FlattenException(exception)}");

                throw;
            }
            finally
            {
                this.EventSource.Debug(operationName, $"Completed enabled managed identity on storage account for the workspace '{workspaceId}'.");
            }
        }

        /// <summary>
        /// Enable service endpoints on Managed VNET subnets
        /// </summary>
        /// <param name="applianceEntity">appliance parameter</param>
        /// <param name="publisherTenantId">publisher tenant id</param>
        /// <param name="resourceType">resource type</param>
        /// <param name="resourceProviderNamespace">resource provider namespace</param>
        public async Task EnableServiceEndpoints(
            ApplianceEntity applianceEntity,
            string publisherTenantId,
            string resourceType,
            string resourceProviderNamespace)
        {
            string operationName = this.GetOperationName(Utilities.GetAsyncMethodName());
            string workspaceId = applianceEntity.GetFullyQualifiedId(resourceType);

            try
            {
                //Enabling service endpoints on Private subnet
                await this.EnableServiceEndpointsOnSubnet(
                    applianceEntity,
                    ProviderConstants.MicrosoftNetwork.PrivateSubnet,
                    publisherTenantId,
                    resourceType,
                    resourceProviderNamespace);

                //Enabling service endpoints on Public subnet
                await this.EnableServiceEndpointsOnSubnet(
                    applianceEntity,
                    ProviderConstants.MicrosoftNetwork.PublicSubnet,
                    publisherTenantId,
                    resourceType,
                    resourceProviderNamespace);

                this.EventSource.Debug(operationName, $"Enable service endpoints successful on Managed VNET subnets of workspace '{workspaceId}'.");
            }
            catch (Exception exception)
            {
                this.EventSource.Error(
                    operationName,
                    $"Exception occured during enabling service endpoints on Managed VNET subnets of the workspace '{workspaceId}' in PublisherTenantId: {publisherTenantId}. Error: {Utilities.FlattenException(exception)}");
                throw;
            }
            finally
            {
                this.EventSource.Debug(operationName, $"Completed enable service endpoints on Managed VNET subnets for the workspace '{workspaceId}'.");
            }
        }

        private async Task EnableServiceEndpointsOnSubnet(ApplianceEntity applianceEntity, string subnetName, string publisherTenantId, string resourceType, string resourceProviderNamespace)
        {
            string operationName = this.GetOperationName(Utilities.GetAsyncMethodName());
            string workspaceId = applianceEntity.GetFullyQualifiedId(resourceType);

            var managedResourceGroupId = applianceEntity.Properties.ManagedResourceGroupId;
            var vnetId = string.Format("{0}/{1}/{2}", managedResourceGroupId.TrimEnd('/'), ProviderConstants.MicrosoftNetwork.VNetIdPart, ProviderConstants.MicrosoftNetwork.WorkersVNet); ;
            var subnetResourceUri = UriTemplateEngine.GetSubnetGetPropertiesRequestUri(this.FrontdoorEngine.FrontdoorEndpointUri, vnetId.TrimStart('/'), subnetName, ServiceEndpointsApiVersion);

            try
            {
                //Get the subnet Request Payload by making a Frontdoor Call
                var subnetUpdateRequest = await
                    this.FrontdoorEngine
                    .GetResourcesFromFrontdoor<SubnetDefinition>(
                        publisherTenantId,
                        subnetResourceUri,
                        resourceProviderNamespace)
                    .ConfigureAwait(false);

                if (subnetUpdateRequest == null)
                {
                    throw new VirtualNetworkInjectionOperationException(
                        HttpStatusCode.BadRequest,
                        ErrorResponseCode.VirtualNetworkSubnetNotFound.ToString(),
                        ErrorResponseMessages.VirtualNetworkSubnetNotFound.ToLocalizedMessage(subnetName, vnetId));
                }

                //Transform the Request Payload by adding ServiceEndpoints property of type Microsoft.Storage, additional types will be added in config for future
                string[] supportedServices = CloudConfigurationManager.GetMultivaluedConfiguration(ProviderConstants.MicrosoftNetwork.SupportedServiceEndpointServicesOnManagedVNet);
                JArray supportedServicesJArray = new JArray();
                foreach (var serviceType in supportedServices)
                {
                    JObject serviceTypeJObject = new JObject(
                        new JProperty(
                            ProviderConstants.MicrosoftNetwork.Service,
                            serviceType));

                    supportedServicesJArray.Add(serviceTypeJObject);
                }

                subnetUpdateRequest.Properties.Add(
                        new JProperty(
                            ProviderConstants.MicrosoftNetwork.ServiceEndpoints,
                            supportedServicesJArray));

                //Add service endpoint to subnet
                await this.FrontdoorEngine.
                    PutServiceEndpointsOnSubnet(
                    subnetResourceUri,
                    resourceProviderNamespace,
                    publisherTenantId,
                    subnetUpdateRequest);
            }
            catch (Exception exception)
            {
                this.EventSource.Error(
                    operationName,
                    $"Exception occur while getting the subnet properties of '{subnetResourceUri}' of the workspace '{workspaceId}' in PublisherTenantId: {publisherTenantId}. Error: {Utilities.FlattenException(exception)}");
                throw;
            }
        }

        /// <summary>
        /// Create managed resource group.
        /// </summary>
        /// <param name="appliance">The appliance object.</param>
        /// <param name="publisherTenantId">The ISV's tenant Id.</param>
        /// <param name="customerTenantId">The customer tenant identifier.</param>
        /// <param name="resourceType">The resource type.</param>
        /// <param name="resourceProviderNamespace">The resource provider name space.</param>
        private async Task CreateManagedResourceGroup(
            ApplianceEntity appliance,
            string publisherTenantId,
            string customerTenantId,
            string resourceType,
            string resourceProviderNamespace)
        {
            var operationName = this.GetOperationName(Utilities.GetAsyncMethodName());

            this.EventSource.Debug(operationName,
                format: "Create Managed Resource Group '{0}' in PublisherTenantId '{1}' and CustomerTenantId '{2}'.",
                arg0: appliance.Metadata.ManagedResourceGroupLockName,
                arg1: publisherTenantId,
                arg2: customerTenantId);

            var resourceGroupRequest = ResourceGroupRequestMatch.FromFullyQualifiedId(
                resourceGroupId: appliance.Properties.ManagedResourceGroupId.Trim());

            var resourceGroupDefinition = new ResourceGroupDefinition
            {
                Location = appliance.Location,
                ManagedBy = appliance.GetFullyQualifiedId(resourceType),
                Tags = appliance.Tags
            };

            // Note(subraman): If we create resource group in customer tenant, ARM takes care of projecting in publisher tenant.
            await this.FrontdoorEngine
                .CreateResourceGroup(
                    authenticationTenantId: customerTenantId,
                    subscriptionId: resourceGroupRequest.SubscriptionId,
                    resourceGroupName: resourceGroupRequest.ResourceGroup,
                    resourceGroupDefinition: resourceGroupDefinition,
                    resourceProviderNamespace: resourceProviderNamespace)
                .ConfigureAwait(continueOnCapturedContext: false);
        }

        /// <summary>
        /// Create role assignments for the given authorization properties by the appliance package provider.
        /// </summary>
        /// <param name="appliance">The appliance object.</param>
        /// <param name="authorizations">The authorizations.</param>
        /// <param name="tenantId">The tenant Id.</param>
        /// <param name="resourceType">The resource type.</param>
        /// <param name="resourceProviderNamespace">The resource provider name space.</param>
        public async Task CreateProviderAuthorizationRoleAssignments(
            ApplianceEntity appliance,
            ApplicationAuthorization[] authorizations,
            string tenantId,
            string resourceType,
            string resourceProviderNamespace)
        {

            var operationName = this.GetOperationName(Utilities.GetAsyncMethodName());

            //Staging app doesn't have required authorization to do this; skip it temporarily
            bool isStagingEnv = await IsStagingEnvironment(appliance);
            if (isStagingEnv)
            {
                this.EventSource.Debug(operationName, "Skip 'deny assignments' step since the workspace is in a staging subscription.");
                return;
            }

            var authPrincipalsList = string.Join(",", authorizations.Select(authorization => authorization.PrincipalId));

            this.EventSource.Debug(
                operationName: operationName,
                $"Creating Provider Authorization Role Assignments for PrincipalIds {authPrincipalsList} in tenant: {tenantId}");

            // Note(ilahat): We need to force refresh a new frontdoor token to invalidate the role assignment cache
            await this.FrontdoorEngine
                .RefreshAuthenticationToken(
                    tenantId: tenantId,
                    resourceProviderNamespace: resourceProviderNamespace)
                .ConfigureAwait(continueOnCapturedContext: false);

            await authorizations
                .ExecuteOperationsConcurrently(
                    operation: providerAuthorization => this.CreateRoleAssignments(
                        appliance: appliance,
                        tenantId: tenantId,
                        providerAuthorization: providerAuthorization,
                        resourceType: resourceType,
                        resourceProviderNamespace: resourceProviderNamespace),
                    concurrencyLimit: 10)
                .ConfigureAwait(continueOnCapturedContext: false);
        }

        /// <summary>
        /// Create policy assignment with the given policy definition by the appliance package provider.
        /// </summary>
        /// <param name="appliance">The appliance object.</param>
        /// <param name="applianceProviderTenantId">The appliance provider's tenant Id.</param>
        /// <param name="providerPolicy">The policy information.</param>
        /// <param name="resourceType">The resource type.</param>
        /// <param name="resourceProviderNamespace">The resource provider name space.</param>
        public Task CreatePolicyAssignment(
            ApplianceEntity appliance,
            string applianceProviderTenantId,
            ApplicationPolicy providerPolicy,
            string resourceType,
            string resourceProviderNamespace)
        {
            this.EventSource.Debug(
                operationName: "ApplianceEngine.CreatePolicyAssignment",
                format: "Create policy on managed resource group '{0}' of appliance '{1}' for package provider policy '{2}'.",
                arg0: appliance.Properties.ManagedResourceGroupId,
                arg1: appliance.GetFullyQualifiedId(resourceType),
                arg2: providerPolicy.ToJson());

            // Note(wud): We only support builtin policy definition for now.
            var policyAssignmentDefinition = new PolicyAssignmentDefinition
            {
                Sku = string.IsNullOrWhiteSpace(providerPolicy.Sku)
                    ? PolicySku.FreePolicySku
                    : providerPolicy.Sku.TryFromJson<PolicySku>(),
                Name = providerPolicy.Name,
                Properties = new PolicyAssignmentProperties
                {
                    Parameters = string.IsNullOrWhiteSpace(providerPolicy.Parameters)
                        ? null
                        : providerPolicy.Parameters.TryFromJson<InsensitiveDictionary<PolicyParameter>>(),
                    Scope = appliance.Properties.ManagedResourceGroupId,
                    PolicyDefinitionId = string.Format(
                        format: "/providers/Microsoft.Authorization/policyDefinitions/{0}",
                        arg0: providerPolicy.PolicyDefinitionId)
                }
            };

            var policyAssignmentRequestUri = UriTemplateEngine.GetPolicyAssignmentRequestUri(
                endpoint: this.FrontdoorEngine.FrontdoorEndpointUri,
                scope: appliance.Properties.ManagedResourceGroupId,
                policyAssignmentName: providerPolicy.Name,
                apiVersion: ApplianceEngine.PolicyAssignmentsApiVersion);

            return this.FrontdoorEngine.CreateAuthorizationResource<PolicyAssignmentDefinition>(
                authenticationTenantId: applianceProviderTenantId,
                requestUri: policyAssignmentRequestUri,
                authorizationResourceDefinition: policyAssignmentDefinition,
                resourceProviderNamespace: resourceProviderNamespace);
        }

        /// <summary>
        /// Create a deployment to provision resources for the appliance.
        /// </summary>
        /// <param name="appliance">The appliance object.</param>
        /// <param name="templateParameters">The template parameters.</param>
        /// <param name="appliancePackage">The appliance package.</param>
        /// <param name="publisherTenantId">The application publisher's tenant Id.</param>
        /// <param name="resourceType">The resource type.</param>
        /// <param name="resourceProviderNamespace">The resource provider name space.</param>
        /// <param name="deploymentMode">ARM Template Deployment Mode like Incremental, Complete</param>
        public async Task<DeploymentResponse> CreateApplianceDeployment(
            ApplianceEntity appliance,
            InsensitiveDictionary<JToken> templateParameters,
            AppliancePackage appliancePackage,
            string publisherTenantId,
            string resourceType,
            string resourceProviderNamespace,
            DeploymentMode deploymentMode = DeploymentMode.Complete)
        {
            var operationName = this.GetOperationName(Utilities.GetAsyncMethodName());

            var mainTemplatePath = $"{Path.GetDirectoryName(Assembly.GetExecutingAssembly().Location)}\\{appliancePackage.ApplianceMainTemplateFilePath}";

            if (!File.Exists(mainTemplatePath))
            {
                throw new ErrorResponseMessageException(
                    HttpStatusCode.InternalServerError,
                    ErrorResponseCode.InternalServerError,
                    ErrorResponseMessages.TemplateFileDoesNotExist.ToLocalizedMessage(mainTemplatePath));
            }

            this.EventSource.Debug(operationName,
                $@"Create deployment with name '{appliance.Metadata.DeploymentName}'
                under managed resource group {appliance.Properties.ManagedResourceGroupId} of appliance {appliance.GetFullyQualifiedId(resourceType)} using template file '{mainTemplatePath}'");

            var deploymentDefinition = new DeploymentDefinition
            {
                Properties = new DeploymentProperties
                {
                    Mode = deploymentMode,
                    SecurityMode = DeploymentSecurityMode.Secured,
                    Parameters = templateParameters,
                    Template = JToken.Parse(File.ReadAllText(mainTemplatePath))
                }
            };

            var resourceGroupRequest = ResourceGroupRequestMatch.FromFullyQualifiedId(appliance.Properties.ManagedResourceGroupId.Trim());

            return await this.FrontdoorEngine
                .CreateDeployment(
                    publisherTenantId,
                    resourceGroupRequest.SubscriptionId,
                    resourceGroupRequest.ResourceGroup,
                    appliance.Metadata.DeploymentName,
                    deploymentDefinition,
                    resourceProviderNamespace,
                    true)
                .ConfigureAwait(false);
        }


        /// <summary>
        /// Create a role assignment for the given authorization properties by the appliance package provider and a role assignment on the managed application resource.
        /// </summary>
        /// <param name="appliance">The appliance object.</param>
        /// <param name="tenantId">The tenant Id.</param>
        /// <param name="providerAuthorization">The appliance package provider's authorization information.</param>
        /// <param name="resourceType">The resource type.</param>
        /// <param name="resourceProviderNamespace">The resource provider name space.</param>
        private Task CreateRoleAssignments(
            ApplianceEntity appliance,
            string tenantId,
            ApplicationAuthorization providerAuthorization,
            string resourceType,
            string resourceProviderNamespace)
        {
            var operationName = this.GetOperationName(Utilities.GetAsyncMethodName());
            var principalType = AuthPrincipalType(appliance.Properties.PublisherPackageId);

            this.EventSource.Debug(
                operationName: operationName,
                $"Create role assignments on managed resource group '{appliance.Properties.ManagedResourceGroupId}' " +
                $"and on managed application '{appliance.GetFullyQualifiedId(resourceType)}'. " +
                $"Package provider authorization '{providerAuthorization.ToJson()}'. principalType: {principalType}");

            var resourceGroupRoleAssignmentTask = this.CreateRoleAssignmentIfNotExists(
                principalId: providerAuthorization.PrincipalId,
                principalType: principalType,
                scope: appliance.Properties.ManagedResourceGroupId,
                roleDefinitionId: providerAuthorization.RoleDefinitionId,
                subscriptionId: appliance.SubscriptionId,
                authenticationTenantId: tenantId,
                resourceProviderNamespace: resourceProviderNamespace);

            return resourceGroupRoleAssignmentTask;
        }

        /// <summary>
        /// Create a role assignment at a given scope for a given principalId.
        /// </summary>
        /// <param name="principalId">The principal Id.</param>
        /// <param name="principalType">Type of service principal.</param>
        /// <param name="scope">The scope.</param>
        /// <param name="roleDefinitionId">The role definition Id.</param>
        /// <param name="subscriptionId">The subscription Id.</param>
        /// <param name="authenticationTenantId">The authentication tenant Id.</param>
        /// <param name="resourceProviderNamespace">The resource provider namespace.</param>
        public Task CreateRoleAssignmentIfNotExists(
            string principalId,
            PrincipalType principalType,
            string scope,
            string roleDefinitionId,
            string subscriptionId,
            string authenticationTenantId,
            string resourceProviderNamespace)
        {
            var operationName = this.GetOperationName(Utilities.GetAsyncMethodName());

            var assignmentDefinition = new RoleAssignmentDefinition
            {
                Properties = new RoleAssignmentProperties
                {
                    PrincipalId = principalId,
                    PrincipalType = principalType,
                    Scope = scope,
                    RoleDefinitionId = this.GetFullyQualifiedRoleDefinitionId(subscriptionId, roleDefinitionId)
                }
            };

            this.EventSource.Debug(
                operationName,
                $"Create role assignments if they don't exist for Principal: {principalId} in" +
                $" Subscription '{subscriptionId}' with Scope: {scope} and authenticationTenantId " +
                $"'{authenticationTenantId}'. RoleDefinitionId = {roleDefinitionId}");

            return this.FrontdoorEngine.CreateRoleAssignmentIfNotExists(
                authenticationTenantId: authenticationTenantId,
                scope: scope,
                principalId: principalId,
                roleDefinitionId: roleDefinitionId,
                roleAssignmentDefinition: assignmentDefinition,
                resourceProviderNamespace: resourceProviderNamespace);
        }

        /// <summary>
        /// Deletes role assignments at a given scope for a given principalId.
        /// </summary>
        /// <param name="principalId">The principal Id.</param>
        /// <param name="scope">The scope.</param>
        /// <param name="roleDefinitionId">The role definition Id.</param>
        /// <param name="subscriptionId">The subscription Id.</param>
        /// <param name="authenticationTenantId">The authentication tenant Id.</param>
        /// <param name="resourceProviderNamespace">The resource provider namespace.</param>
        public Task DeleteRoleAssignmentIfExists(
            string principalId,
            string scope,
            string roleDefinitionId,
            string subscriptionId,
            string authenticationTenantId,
            string resourceProviderNamespace)
        {
            var operationName = this.GetOperationName(Utilities.GetAsyncMethodName());

            this.EventSource.Debug(
                operationName,
                $"Delete role assignments if they exist for Principal: '{principalId}' in Subscription" +
                $" '{subscriptionId}' with Scope: '{scope}' RoleDefinitionId = '{roleDefinitionId}' in TenantId: {authenticationTenantId}");

            return this.FrontdoorEngine.DeleteRoleAssignmentIfExists(
                authenticationTenantId,
                scope,
                principalId,
                roleDefinitionId,
                resourceProviderNamespace);
        }

        /// <summary>
        /// Gets the fully qualified role definition identifier.
        /// </summary>
        /// <param name="subscriptionId">The subscription identifier.</param>
        /// <param name="roleDefinitionId">The role definition identifier.</param>
        private string GetFullyQualifiedRoleDefinitionId(string subscriptionId, string roleDefinitionId)
        {
            return string.Format(
                format: "/subscriptions/{0}/providers/Microsoft.Authorization/roleDefinitions/{1}",
                arg0: subscriptionId,
                arg1: roleDefinitionId);
        }

        /// <summary>
        /// Creates authorization and deny assignment for the application.
        /// </summary>
        /// <param name="appliance">The appliance object.</param>
        /// <param name="parameters">The appliance parameters.</param>
        /// <param name="appliancePackage">The appliance package.</param>
        /// <param name="customerTenantId">The customer tenant identifier.</param>
        /// <param name="publisherTenantId">The tenant identifier.</param>
        /// <param name="resourceType">The resource type.</param>
        /// <param name="resourceProviderNamespace">The resource provider namespace.</param>
        /// <param name="excludedPrincipalId">Excluded PrincipalId for deny assignment definition.</param>
        public async Task CreateAuthorizationForApplication(
            ApplianceEntity appliance,
            AppliancePackage appliancePackage,
            string customerTenantId,
            string publisherTenantId,
            string resourceType,
            string resourceProviderNamespace,
            string excludedPrincipalId)
        {
            await this
                .CreateProviderAuthorizationRoleAssignments(
                    appliance,
                    appliancePackage.Authorizations,
                    publisherTenantId,
                    resourceType,
                    resourceProviderNamespace)
                .ConfigureAwait(false);

            await this
                .CreateOrUpdateDenyAssignmentsForApplication(
                    publisherTenantId,
                    customerTenantId,
                    appliance,
                    resourceType,
                    resourceProviderNamespace,
                    excludedPrincipalId)
                .ConfigureAwait(false);
        }

        /// <summary>
        /// Creates or updates the deny assignment on the resource group that contains the application's resources
        /// </summary>
        /// <param name="publisherTenantId">The publisher tenant identifier.</param>
        /// <param name="customerTenantId">The customer tenant identifier.</param>
        /// <param name="appliance">The appliance object.</param>
        /// <param name="parameters">The appliance parameters.</param>
        /// <param name="resourceType">The resource type.</param>
        /// <param name="resourceProviderNamespace">The resource provider namespace.</param>
        public async Task CreateOrUpdateDenyAssignmentsForApplication(
            string publisherTenantId,
            string customerTenantId,
            ApplianceEntity appliance,
            string resourceType,
            string resourceProviderNamespace,
            string excludedPrincipalId)
        {
            var applicationId = appliance.GetFullyQualifiedId(resourceType);
            var operationName = this.GetOperationName(Utilities.GetAsyncMethodName());

            //Staging app doesn't have required authorization to do this; skip it temporarily
            bool isStagingEnv = await IsStagingEnvironment(appliance);
            if (isStagingEnv)
            {
                this.EventSource.Debug(operationName, "Skip 'deny assignments' steps since the workspace is in a staging subscription.");
                return;
            }

            this.EventSource.Debug(
                operationName,
                $"Creating or updating system deny assignment for the of workspace '{applicationId}'" +
                $" in CustomerTenantId: {customerTenantId}, PublisherTenantId: {publisherTenantId}" +
                $" excluded principalsId: '{excludedPrincipalId}'");

            JArray denyAssignmentsNotActions;

            denyAssignmentsNotActions = DenyAssignmentsNotActions.Any()
                ? JArray.FromObject(DenyAssignmentsNotActions)
                : new JArray { ProviderConstants.Databricks.ReadActions };

            var permissions = new JObject
            {
                {
                    ProviderConstants.Authorizations.ActionsProperty,
                    ApplianceEngine.DenyAssignmentsActions.Any() ?
                    JArray.FromObject(ApplianceEngine.DenyAssignmentsActions) :
                    new JArray { ProviderConstants.Databricks.AllActions }
                },
                {
                    ProviderConstants.Authorizations.NotActionsProperty,
                    denyAssignmentsNotActions
                },
                {
                    ProviderConstants.Authorizations.DataActionsProperty,
                    ApplianceEngine.DenyAssignmentsDataActions.Any() ?
                    JArray.FromObject(ApplianceEngine.DenyAssignmentsDataActions) :
                    new JArray { ProviderConstants.Databricks.AllActions }
                },
                {
                    ProviderConstants.Authorizations.NotDataActionsProperty,
                    ApplianceEngine.DenyAssignmentsNotDataActions.Any() ?
                    JArray.FromObject(ApplianceEngine.DenyAssignmentsNotDataActions) :
                    JArray.FromObject(EmptyArray<string>.Instance)
                }
            };

            var principals = new JObject
            {
                { ProviderConstants.Authorizations.IdProperty, ProviderConstants.Authorizations.EveryonePrincipalId },
                { ProviderConstants.Authorizations.TypeProperty, ProviderConstants.Authorizations.EveryonePrincipalType }
            };

            var denyAssignmentDefinition = new DenyAssignmentDefinition
            {
                Properties = new JObject
                {
                    { ProviderConstants.Authorizations.DenyAssignmentNameProperty, $"System deny assignment created by Azure Databricks {applicationId}" },
                    { ProviderConstants.Authorizations.DescriptionProperty, $"System deny assignment created by Azure Databricks '{applicationId}' in scope: '{appliance.Properties.ManagedResourceGroupId}'" },
                    { ProviderConstants.Authorizations.PermissionsProperty, new JArray { permissions } },
                    { ProviderConstants.Authorizations.PrincipalsProperty, new JArray { principals } },
                    { ProviderConstants.Authorizations.ConditionProperty, $"@Subject[ResourceId] StringNotStartsWithIgnoreCase '{appliance.Properties.ManagedResourceGroupId}' && @Subject[tid] StringNotEqualsAnyOfIgnoreCase {{'{publisherTenantId}'}}" },
                    { ProviderConstants.Authorizations.ConditionVersionProperty, "1.0" },
                    { ProviderConstants.Authorizations.IsSystemProtectedProperty, true }
                }
            };

            if (!string.IsNullOrWhiteSpace(excludedPrincipalId))
            {
                this.EventSource.Debug(
                    operationName,
                    $"Adding excluded principalId for deny assignment: '{excludedPrincipalId}'");

                var excludedPrincipals = new JObject
                {
                    { ProviderConstants.Authorizations.IdProperty,  excludedPrincipalId},
                    { ProviderConstants.Authorizations.TypeProperty, ProviderConstants.Authorizations.ServicePrincipalType }
                };
                var denyAssignmentProperties = denyAssignmentDefinition.Properties as JObject;

                denyAssignmentProperties.Add(
                    new JProperty
                            (
                                ProviderConstants.Authorizations.ExcludedPrincipalsProperty,
                                new JArray { excludedPrincipals }
                            )
                        );
            }

            // We need to force refresh a new frontdoor token to refresh the role assignment cache
            await this.FrontdoorEngine
                .RefreshAuthenticationToken(customerTenantId, resourceProviderNamespace)
                .ConfigureAwait(false);

            await this.FrontdoorEngine
                .CreateOrUpdateDenyAssignments(
                    customerTenantId,
                    appliance.Properties.ManagedResourceGroupId,
                    ProviderConstants.Authorizations.EveryonePrincipalId,
                    denyAssignmentDefinition,
                    resourceProviderNamespace)
                .ConfigureAwait(false);
        }

        /// <summary>
        /// Initialize the workspace in the control plane
        /// </summary>
        /// <param name="existingEntity">The appliance object.</param>
        /// <param name="customerTenantId">The customer tenant identifier.</param>
        /// <param name="resourceType">The resource type.</param>
        /// <param name="resourceProviderNamespace">The resource provider namespace.</param>
        /// <param name="apiVersion">The <c>api</c> version</param>
        public async Task<WorkspaceInitializeResponse> InitializeWorkspace(
            ApplianceEntity existingEntity,
            string customerTenantId,
            string resourceType,
            string resourceProviderNamespace,
            string apiVersion)
        {
            string operationName = this.GetOperationName(Utilities.GetAsyncMethodName());
            string resourceId = existingEntity.GetFullyQualifiedId(resourceType);
            string subscriptionId = existingEntity.SubscriptionId;

            this.EventSource.Debug(
                operationName,
                $"Initializing the workspace '{resourceId}' in CustomerTenantId: {customerTenantId}, Api-version: {apiVersion}.");

            var response = new WorkspaceInitializeResponse { Status = OperationStatus.Failed };

            var timer = new Stopwatch();
            timer.Start();

            try
            {

                (Uri baseUri, string audience, bool useArm) = await this.FrontdoorEngine
                   .GetDatabricksAccountApiEnpointAndAudience(customerTenantId, subscriptionId);
                WorkspaceDetails dbWorkspaceDetails;
                if (useArm)
                {
                    dbWorkspaceDetails = await DatabricksAccountsManagerUtils.GetDBWorkspace<WorkspaceDetails>(
                        FrontdoorEngine,
                        customerTenantId,
                        resourceId,
                        baseUri,
                        apiVersion,
                        audience,
                        useArm)
                        .ConfigureAwait(false);
                }
                else
                {
                    var asyncDbWorkspace = await DatabricksAccountsManagerUtils.GetDBWorkspace<DatabricksWorkspace>(
                        FrontdoorEngine,
                        customerTenantId,
                        resourceId,
                        baseUri,
                        apiVersion,
                        audience,
                        useArm)
                        .ConfigureAwait(false);
                    dbWorkspaceDetails = asyncDbWorkspace?.ToWorkspaceDetails();
                }


                if (dbWorkspaceDetails == null || string.IsNullOrWhiteSpace(dbWorkspaceDetails.WorkspaceId) || string.IsNullOrWhiteSpace(dbWorkspaceDetails.WorkspaceURL))
                {
                    WorkspaceDetails workspaceDetails;
                    if (useArm)
                    {
                        var workspaceInitializationRequestUri = UriTemplateEngine.WorkspaceInitializationOperationUri(
                            this.FrontdoorEngine.FrontdoorEndpointUri,
                            resourceId,
                            apiVersion);
                        // For ARM path, we use AsyncRetry for transient errors but don't need polling
                        // as the operation is synchronous
                        workspaceDetails = await AsyncRetry.Retry(
                             () => this
                                 .FrontdoorEngine
                                 .CallFrontdoor<WorkspaceDetails>(
                                     HttpMethod.Put,
                                     workspaceInitializationRequestUri,
                                     resourceProviderNamespace,
                                     customerTenantId,
                                     null),
                             this.FrontdoorEngine.WorkspaceInitializeRetryCount,
                             TimeSpan.FromSeconds(this.FrontdoorEngine.WorkspaceInitializeRetryInterval),
                             isRetryable: ex => ex.IsConnectivityException()
                                                || (ex is ServerErrorResponseMessageException && ((ServerErrorResponseMessageException)ex).HttpStatus.IsRetryableResponse()),
                             errorAction: (Exception exception) =>
                             {
                                 this.EventSource.Debug(
                                     operationName,
                                     $"Attempt to initialize workspace '{resourceId}' failed. " +
                                     $"Framework will retry in {this.FrontdoorEngine.WorkspaceInitializeRetryInterval} seconds." +
                                     $"Exception details: '{Utilities.FlattenException(exception)}'.");
                             })
                         .ConfigureAwait(false);

                        this.EventSource.Debug(
                            operationName,
                            $"ARM workspace initialization completed synchronously for '{resourceId}'");
                    }
                    else
                    {
                        // Send notification without retry - we'll handle errors and retries through job-based polling
                        this.EventSource.Debug(
                            operationName,
                            $"[Feature Flag selection]START: DB Workspace {(useArm ? "PATCH" : "PUT")} Notification on WorkspaceId: '{resourceId}'" +
                            $"Using AccountApi");

                        try
                        {
                            await DatabricksAccountsManagerUtils.SendDBNotificationWithFlagControl(
                                this.FrontdoorEngine,
                                useArm ? null : JToken.FromObject(DatabricksWorkspace.FromApplianceEntity(existingEntity)),
                                new HttpMethod("PUT"),
                                customerTenantId,
                                useArm,
                                resourceId,
                                baseUri,
                                apiVersion,
                                audience);
                        }
                        catch (Exception ex)
                        {
                            // Log the error but continue with polling setup
                            this.EventSource.Debug(
                                operationName,
                                $"Error initializing workspace '{resourceId}': {ex.Message}. Will continue with job-based polling.");
                        }

                        // Set up job-based polling for initialization operation
                        this.EventSource.Debug(
                            operationName,
                            $"Setting up job-based polling for workspace initialization operation: '{resourceId}'");

                        // Set up polling information in the response instead of throwing an exception
                        this.EventSource.Debug(
                            operationName,
                            $"Setting up polling information in response for workspace initialization operation: '{resourceId}'");

                        response.Status = OperationStatus.Postponed;
                        response.RequiresPolling = true;
                        response.PollingState = Common.Responses.AccountApiPollingState.WaitingForInitialization;
                        response.OperationId = resourceId;
                        response.BaseUri = baseUri;
                        response.Audience = audience;
                        response.PollingIntervalSeconds = FrontdoorEngine.AccountApiPollingRetryInterval;
                        response.ResourceOperation = ProvisioningOperation.AccountApiPolling;

                        return response;
                    }
                    if (workspaceDetails != null)
                    {
                        this.EventSource.Debug(
                            operationName,
                            $"The workspace initialization succeeded for '{resourceId}'. Response: {workspaceDetails}");

                        response.Status = OperationStatus.Success;
                        response.WorkspaceDetails = workspaceDetails;

                        return response;
                    }

                    this.EventSource.Debug(
                        operationName,
                        $"The workspace initialization failed for '{resourceId}'. Null or empty workspace details from DB CP.");

                    response.ErrorResponseMessage = Utilities.GetInitializeErrorResponseMessage(ErrorResponseMessages.NullOrEmptyWorkspaceDetails.ToLocalizedMessage());
                }
                else
                {
                    //// Notifies DB for Workspace UPDATE
                    var updatedNotification = useArm ?
                        JToken.FromObject(existingEntity.ToDbWorkspace()) :
                        JToken.FromObject(DatabricksWorkspace.FromApplianceEntity(existingEntity));

                    await DatabricksAccountsManagerUtils.SendDBNotificationWithFlagControl(
                        this.FrontdoorEngine,
                        updatedNotification,
                        useArm ? new HttpMethod("PATCH") : new HttpMethod("PUT"),
                        customerTenantId,
                        useArm,
                        resourceId,
                        baseUri,
                        apiVersion,
                        audience).ConfigureAwait(false);

                    if (useArm)
                    {
                        // For ARM path, we don't need polling - the operation is synchronous
                        this.EventSource.Debug(
                            operationName,
                            $"ARM workspace update operation completed synchronously for '{resourceId}'");

                        response.Status = OperationStatus.Success;
                        response.WorkspaceDetails = new WorkspaceDetails()
                        {
                            WorkspaceId = dbWorkspaceDetails.WorkspaceId,
                            WorkspaceURL = dbWorkspaceDetails.WorkspaceURL,
                            IsUcEnabled = dbWorkspaceDetails.IsUcEnabled,
                            IsPrivateLinkAllowed = dbWorkspaceDetails.IsPrivateLinkAllowed,
                            PrivateLinkAssets = dbWorkspaceDetails.PrivateLinkAssets
                        };
                    }
                    else
                    {
                        // Set up job-based polling for update operation
                        this.EventSource.Debug(
                            operationName,
                            $"Setting up job-based polling for workspace update operation: '{resourceId}'");

                        // Set up polling information in the response instead of throwing an exception
                        this.EventSource.Debug(
                            operationName,
                            $"Setting up polling information in response for workspace update operation: '{resourceId}'");

                        response.Status = OperationStatus.Postponed;
                        response.RequiresPolling = true;
                        response.PollingState = Common.Responses.AccountApiPollingState.WaitingForUpdate;
                        response.OperationId = resourceId;
                        response.BaseUri = baseUri;
                        response.Audience = audience;
                        response.PollingIntervalSeconds = FrontdoorEngine.AccountApiPollingRetryInterval;
                        response.ResourceOperation = ProvisioningOperation.AccountApiPolling;

                        return response;
                    }
                }
            }
            catch (ErrorResponseMessageException errorResponseMessageException)
            {
                this.EventSource.Debug(
                    operationName,
                    $"Error response exception initializing the workspace '{resourceId}' in CustomerTenantId: '{customerTenantId}'. Exception: {Utilities.FlattenException(errorResponseMessageException)}");

                response.ErrorResponseMessage = new ErrorResponseMessage(errorResponseMessageException.ErrorCode, errorResponseMessageException.Message);
            }
            catch (ServerErrorResponseMessageException serverErrorResponseMessageException)
            {
                this.EventSource.Debug(
                    operationName,
                    $"Server Error response exception while initializing the workspace '{resourceId}' in CustomerTenantId: '{customerTenantId}'. Exception: {Utilities.FlattenException(serverErrorResponseMessageException)}");

                response.ErrorResponseMessage = new ErrorResponseMessage(serverErrorResponseMessageException.ErrorCode, serverErrorResponseMessageException.Message);
            }
            catch (Exception exception)
            {
                this.EventSource.Debug(
                    operationName,
                    $"Error initializing the workspace '{resourceId}' in CustomerTenantId: {customerTenantId}. Error: {Utilities.FlattenException(exception)}");

                response.ErrorResponseMessage = Utilities.GetInitializeErrorResponseMessage(exception.Message);
            }
            finally
            {
                timer.Stop();

                this.EventSource.Debug(operationName, $"Completed initialization for the workspace '{resourceId}'. Status: {response.Status}. Time taken {timer.ElapsedMilliseconds} ms.");
            }

            return response;
        }

        /// <summary>
        /// Calls DB for the workspace DELETE notification.
        /// </summary>
        /// <param name="subscriptionId">The subscription Id.</param>
        /// <param name="workspaceId">The workspace Id.</param>
        /// <param name="resourceProviderNamespace">The resource provider namespace.</param>
        /// <param name="appliance">The appliance.</param>
        /// <param name="retainUc">This is to let Databricks know whether to ratainUc or not.</param>
        /// <param name="apiVersion">The API version.</param>
        /// <returns>Returns a WorkspaceInitializeResponse with polling information if needed.</returns>
        public async Task<WorkspaceInitializeResponse> DBWorkspaceDeleteNotification(
            string subscriptionId,
            string workspaceId,
            string resourceProviderNamespace,
            ApplianceEntity appliance,
            bool retainUc,
            string apiVersion)
        {
            string operationName = this.GetOperationName(Utilities.GetAsyncMethodName());
            string customerTenantId = RequestCorrelationContext.Current.GetHomeTenantId();
            (Uri baseUri, string audience, bool useArm) = await this.FrontdoorEngine
                .GetDatabricksAccountApiEnpointAndAudience(customerTenantId, subscriptionId);

            if (useArm && !Utilities.IsWorkspaceNotificationEnabled(apiVersion))
            {
                this.EventSource.Debug(
                    operationName,
                    $"SKIP: DB Workspace Notification (config/ AFEC) on WorkspaceId : '{workspaceId}'");

                // Return a success response with no polling required
                return new WorkspaceInitializeResponse
                {
                    Status = OperationStatus.Success,
                    RequiresPolling = false
                };
            }



            this.EventSource.Debug(
                operationName,
                $"[Feature Flag selection]START: DB Workspace DELETE Notification on WorkspaceId: '{workspaceId}'" +
                $"Using {(useArm ? "ARM" : "AccountApi")}");

            var additionalHeaders = new KeyValuePair<string, string>[]
            {
                new KeyValuePair<string, string>(
                    ProviderConstants.DBWorkspaceNotification.RetainUcHeader,
                    retainUc.ToString())
            };

            await DatabricksAccountsManagerUtils.SendDBNotificationWithFlagControl(
                this.FrontdoorEngine,
                useArm ? null : new JObject(),
                new HttpMethod("DELETE"),
                customerTenantId,
                useArm,
                workspaceId,
                baseUri,
                apiVersion,
                audience,
                additionalHeaders).ConfigureAwait(false);

            if (useArm)
            {
                // For ARM path, we don't need polling - the operation is synchronous
                this.EventSource.Debug(
                    operationName,
                    $"ARM workspace delete operation completed synchronously for '{workspaceId}'");

                // Return a success response with no polling required
                return new WorkspaceInitializeResponse
                {
                    Status = OperationStatus.Success,
                    RequiresPolling = false
                };
            }
            else
            {
                // Set up job-based polling for delete operation
                this.EventSource.Debug(
                    operationName,
                    $"Setting up job-based polling for workspace delete operation: '{workspaceId}'");

                // Instead of throwing an exception, return a WorkspaceInitializeResponse with polling information
                this.EventSource.Debug(
                    operationName,
                    $"Setting up polling information for workspace delete operation: '{workspaceId}'");

                // Create and return a response with polling information
                var response = new WorkspaceInitializeResponse
                {
                    Status = OperationStatus.Postponed,
                    RequiresPolling = true,
                    PollingState = Common.Responses.AccountApiPollingState.WaitingForDeletion,
                    OperationId = workspaceId,
                    BaseUri = baseUri,
                    Audience = audience,
                    PollingIntervalSeconds = FrontdoorEngine.AccountApiPollingRetryInterval,
                    ResourceOperation = ProvisioningOperation.AccountApiPolling
                };

                return response;
            }
        }

        /// <summary>
        /// Deletes the Managed Resource Group, using customer tenant Id/ publisher tenant Id.
        /// </summary>
        /// <param name="applianceEntity">The Appliance Entity.</param>
        /// <param name="managedResourceGroupRequest">The managed resource group request.</param>
        /// <param name="resourceProviderNamespace">The resource provider namespace.</param>
        /// <param name="workspaceId">The Workspace Id.</param>
        /// <param name="customerTenantId">The Customer Tenant Id.</param>
        /// <param name="publisherTenantId">The Publisher Tenant Id.</param>
        /// <param name="logger">The Logger.</param>
        /// <returns>Returns the long operation uri.</returns>
        public async Task<Uri> DeleteManagedResourceGroup(
            ApplianceEntity applianceEntity,
            ResourceGroupRequestMatch managedResourceGroupRequest,
            string resourceProviderNamespace,
            string workspaceId,
            string customerTenantId,
            string publisherTenantId,
            ICommonEventSource logger)
        {
            var operationName = this.GetOperationName(Utilities.GetAsyncMethodName());

            Uri longOperationTrackingUri;

            try
            {
                logger.Debug(
                    operationName,
                    $"Attempting to delete MRG using Customer Tenant Id '{customerTenantId}'," +
                    $" {applianceEntity.Properties.ManagedResourceGroupId} for the workspace '{workspaceId}'");

                longOperationTrackingUri = await this.FrontdoorEngine
                .DeleteResourceGroup(
                    authenticationTenantId: customerTenantId,
                    subscriptionId: managedResourceGroupRequest.SubscriptionId,
                    resourceGroupName: managedResourceGroupRequest.ResourceGroup,
                    resourceProviderNamespace: resourceProviderNamespace)
                .ConfigureAwait(continueOnCapturedContext: false);
            }
            catch (ResourceGroupOperationException resourceGroupOperationException)
            {
                logger.Debug(
                    operationName,
                    $"Deletion of MRG using Customer Tenant Id '{customerTenantId}'," +
                    $" {applianceEntity.Properties.ManagedResourceGroupId} for the workspace '{workspaceId}'," +
                    $" failed with exception '{Utilities.FlattenException(resourceGroupOperationException)}'." +
                    $" Retrying delete MRG using Publisher Tenant Id: '{publisherTenantId}'");

                longOperationTrackingUri = await this.FrontdoorEngine
                .DeleteResourceGroup(
                    authenticationTenantId: publisherTenantId,
                    subscriptionId: managedResourceGroupRequest.SubscriptionId,
                    resourceGroupName: managedResourceGroupRequest.ResourceGroup,
                    resourceProviderNamespace: resourceProviderNamespace)
                .ConfigureAwait(continueOnCapturedContext: false);
            }

            return longOperationTrackingUri;
        }


        /// <summary>
        /// Deletes dbfs containers in dbfs account leaving uc container.
        /// </summary>
        /// <param name="authenticationTenantId">The tenant Id to be used to get authentication token.</param>
        /// <param name="dbfsResourceId">Dbfs Account Resource Id.</param>
        /// <param name="resourceProviderNamespace">The resource provider name space.</param>
        public async Task DeleteDbfsContainers(
            string authenticationTenantId,
            string dbfsResourceId,
            string resourceProviderNamespace)
        {
            var operationName = this.GetOperationName(Utilities.GetAsyncMethodName());

            this.EventSource.Debug(
                operationName,
                $"Querying list of Dbfs containers in Dbfs Account: {dbfsResourceId}");

            var dbfsContainers = await this.FrontdoorEngine.GetDbfsContainers(
                    authenticationTenantId,
                    dbfsResourceId,
                    resourceProviderNamespace)
                .ConfigureAwait(false);

            if (dbfsContainers.CoalesceEnumerable().Any())
            {
                var containersList = string.Join(",", dbfsContainers.Select(container => container.Name));

                this.EventSource.Debug(
                    operationName,
                    $"Found Dbfs Containers that needs to be deleted: {containersList}");

                //Limiting concurrency to avoid concurrent operations at same scope causing race conditions.
                await dbfsContainers.ExecuteOperationsConcurrently(
                        async dbfsContainer =>
                        {
                            this.EventSource.Debug(
                                operationName,
                                $"Deleting Dbfs Container '{dbfsContainer.Name}' in DbfsAccount: {dbfsResourceId}");

                            await this.FrontdoorEngine.DeleteStorageAccountContainer(
                                authenticationTenantId,
                                dbfsResourceId,
                                dbfsContainer.Name,
                                resourceProviderNamespace).ConfigureAwait(false);
                        },
                        concurrencyLimit: 1)
                    .ConfigureAwait(false);
            }
        }

        /// <summary>
        /// Deletes all the resources in Mrg Except Uc Resources ie, Storage Account, Access Connector
        /// </summary>
        /// <param name="authenticationTenantId">Authentication TenantId for ARM Calls</param>
        /// <param name="resourcesToBeDeleted">Array of resources to be deleted</param>
        /// <param name="resourceProviderNamespace">The resource provider name space.</param>
        /// <returns>Returns Azure Async Operation Uri</returns>
        public async Task<Uri> DeleteResourcesInBulk(string authenticationTenantId,
            ARMResourceDefinition[] resourcesToBeDeleted,
            string resourceProviderNamespace)
        {
            var operationName = this.GetOperationName(Utilities.GetAsyncMethodName());

            this.EventSource.Debug(
                operationName,
                $"Deleting '{resourcesToBeDeleted.Length}' Resources in Mrg");

            var array = new JArray();

            foreach (var resource in resourcesToBeDeleted)
            {
                array.Add
                    (
                        new JObject()
                        {
                            { ProviderConstants.Databricks.Id, resource.Id }
                        }
                    );
            }

            var requestBody = new JObject()
                            {
                                { ProviderConstants.Databricks.Entities, array},
                                { ProviderConstants.Storage.ParameterType, ProviderConstants.Storage.KeySourceDefault }
                            };

            this.EventSource.Debug(
                operationName,
                $"Deleting '{resourcesToBeDeleted.Length}' Resources in Mrg, requestBody: {requestBody}");

            return await this.FrontdoorEngine.DeleteResourcesInBulk(authenticationTenantId, requestBody, resourceProviderNamespace);
        }

        /// <summary>
        /// Handle Private Dbfs Configuration for the workspace.
        /// </summary>
        /// <param name="incomingEntity">incoming workspace entity</param>
        /// <param name="existingConnector">existing access connector associated with workspace</param>
        /// <param name="publisherTenantId">Publisher TenantId for workspace</param>
        /// <param name="dbfsResourceId">DBFS account resource Id</param>
        /// <returns></returns>
        public async Task<Tuple<JobExecutionResult, AccessConnectorIdEntity, DefaultStorageFirewall?>> ConfigureDefaultStorageFirewall(
            ApplianceEntity incomingEntity,
            AccessConnectorIdEntity existingConnector,
            DefaultStorageFirewall? existingDefaultStorageFirewall,
            string publisherTenantId,
            string dbfsResourceId)
        {
            var operationName = this.GetOperationName(Utilities.GetAsyncMethodName());

            this.EventSource.Debug(operationName, $"Handle Default Storage Firewall Configuration Started.");

            if (incomingEntity.Properties.DefaultStorageFirewall == null)
            {
                this.EventSource.Debug(operationName, $"incoming default storage firewall is null, no need to handle so returning!!.");
                return Tuple.Create((JobExecutionResult)null, existingConnector, existingDefaultStorageFirewall);
            }

            var workspaceResourceId = incomingEntity.GetFullyQualifiedResourceId();

            try
            {
                this.EventSource.Debug(operationName, $"Existing Connector ID: '{existingConnector?.Id}' " +
                            $"Existing Principal ID: '{existingConnector?.PrincipalId}' " +
                            $"Existing Default Storage Firewall: '{existingDefaultStorageFirewall}' " +
                            $"Incoming Connector ID: '{incomingEntity.Properties.AccessConnector?.Id}' " +
                            $"Incoming Principal ID: '{incomingEntity.Properties.AccessConnector?.PrincipalId}' " +
                            $"incoming Default Storage Firewall: '{incomingEntity.Properties.DefaultStorageFirewall}'");

                if (incomingEntity.Properties.DefaultStorageFirewall == DefaultStorageFirewall.Enabled)
                {
                    if (!string.Equals(existingConnector?.Id, incomingEntity.Properties.AccessConnector.Id, StringComparison.OrdinalIgnoreCase))
                    {
                        //Update Storage Account Networking Acls given Access Connector Id changed
                        await this.StorageAccountManager
                            .ConfigureNetworkAclsOnDbfs(
                                incomingEntity.Properties.AccessConnector,
                                dbfsResourceId,
                                publisherTenantId,
                                incomingEntity.Properties.DefaultStorageFirewall)
                            .ConfigureAwait(false);

                        if (!string.IsNullOrEmpty(existingConnector?.Id))
                        {
                            if (Utilities.IsResourceInManagedResourceGroup(existingConnector.Id,
                                    incomingEntity.Properties.ManagedResourceGroupId))
                            {
                                this.EventSource.Debug(operationName,
                                        $"Existing Access Connector is in the Managed Resource Group: '{incomingEntity.Properties.ManagedResourceGroupId}'." +
                                        $" Given incomming Access connector Id : '{incomingEntity.Properties.AccessConnector.Id}' is provided," +
                                        $" Deleting the Access Connector Id: ' {existingConnector.Id}'");

                                await this.FrontdoorEngine.DeleteAccessConnector(
                                        publisherTenantId,
                                        existingConnector.Id,
                                        this.Metadata.ResourceProviderNamespace)
                                    .ConfigureAwait(false);
                            }
                            else
                            {
                                await this.RemoveWorkspaceIdFromConnectorEntityReferByList(existingConnector.Id, workspaceResourceId).ConfigureAwait(false);
                            }
                        }
                    }

                    existingConnector = await this.HandlePrincipalIdChange(
                                            incomingEntity,
                                            existingConnector,
                                            publisherTenantId,
                                            dbfsResourceId)
                                        .ConfigureAwait(false);

                    await this.AddWorkspaceIdToConnectorEntityReferByList(
                                existingConnector.Id,
                                workspaceResourceId,
                                incomingEntity.Properties.ManagedResourceGroupId)
                            .ConfigureAwait(false);
                }
                else
                {
                    // Don't update storage account networking acls if existing default storage firewall is already disabled
                    if (existingDefaultStorageFirewall != incomingEntity.Properties.DefaultStorageFirewall)
                    {
                        await this.StorageAccountManager
                                .ConfigureNetworkAclsOnDbfs(
                                    incomingEntity.Properties.AccessConnector,
                                    dbfsResourceId,
                                    publisherTenantId,
                                    incomingEntity.Properties.DefaultStorageFirewall)
                                .ConfigureAwait(false);
                    }

                    // Remove workspaceId from Access Connector entity referBy list and handle principalId
                    // change only when incoming connector is present in the request
                    if (!string.IsNullOrEmpty(incomingEntity.Properties.AccessConnector?.Id))
                    {
                        if (!string.IsNullOrEmpty(existingConnector?.Id) &&
                        !string.Equals(existingConnector.Id, incomingEntity.Properties.AccessConnector.Id, StringComparison.OrdinalIgnoreCase))
                        {
                            await this.RemoveWorkspaceIdFromConnectorEntityReferByList(existingConnector.Id, workspaceResourceId).ConfigureAwait(false);
                        }

                        existingConnector = await this.HandlePrincipalIdChange(
                            incomingEntity,
                            existingConnector,
                            publisherTenantId,
                            dbfsResourceId)
                        .ConfigureAwait(false);

                        await this.AddWorkspaceIdToConnectorEntityReferByList(
                            existingConnector.Id,
                            workspaceResourceId,
                            incomingEntity.Properties.ManagedResourceGroupId)
                        .ConfigureAwait(false);
                    }
                }
            }
            catch (Exception ex)
            {
                this.EventSource.Debug(operationName,
                        $"Encountered exception while configuring defaultStorageFirewall to workspace.");

                this.Metadata.OperationResponseStatusCode = HttpStatusCode.OK;
                this.Metadata.OperationResponseContent = AsyncOperationResult
                    .GetAsyncOperationResult(
                        provisioningState: ProvisioningState.Failed,
                        errorCode: ErrorResponseCode.ConfigureDefaultStorageFirewallFailed.ToString(),
                        message: ex.Message)
                    .ToJToken();

                await this.ApplianceDataProvider
                    .SetApplianceProvisioningState(
                        incomingEntity.SubscriptionId,
                        incomingEntity.ResourceGroup,
                        incomingEntity.Name,
                        ProvisioningState.Succeeded)
                    .ConfigureAwait(false);

                var jobResult = new JobExecutionResult
                {
                    Status = JobExecutionStatus.Faulted,
                    Message = $"Failed to configure private dbfs for the workspace '{ex}'. Faulting job",
                    NextMetadata = this.Metadata.ToJson(),
                    Details = ex.Message
                };

                return Tuple.Create(jobResult, existingConnector, incomingEntity.Properties.DefaultStorageFirewall);
            }

            this.EventSource.Debug(operationName,
                $"Updated connector on workspace: '{existingConnector}', updated default storage firewall status: '{incomingEntity.Properties.DefaultStorageFirewall}'");

            return Tuple.Create((JobExecutionResult)null, existingConnector, incomingEntity.Properties.DefaultStorageFirewall);
        }

        /// <summary>
        /// Perform Access connector Update with WorkspaceId verification
        /// </summary>
        /// <param name="accessConnectorResourceId">access connector resource id</param>
        /// <param name="workspaceResourceId">workspace resource id to be added to connector</param>
        /// <param name="mrgResourceId">managed resource group resource id</param>
        /// <returns></returns>
        public async Task AddWorkspaceIdToConnectorEntityReferByList(string accessConnectorResourceId, string workspaceResourceId, string mrgResourceId)
        {
            var operationName = this.GetOperationName(Utilities.GetAsyncMethodName());

            if (!string.IsNullOrWhiteSpace(accessConnectorResourceId) &&
                !(Utilities.IsResourceInManagedResourceGroup(accessConnectorResourceId, mrgResourceId)))
            {
                //Add this workspace Id to accessConnector references list.
                this.EventSource.Debug(
                    operationName,
                    $"Adding workspaceId: '{workspaceResourceId}' to accessConnector entity of " +
                    $"connectorId: {accessConnectorResourceId}.");

                var resourceIdMatch = ResourceIdRequestMatch.FromFullyQualifiedId(accessConnectorResourceId);
                var connectorEntity = await this.AccessConnectorDataProvider
                    .FindAccessConnector(
                        resourceIdMatch.SubscriptionId,
                        resourceIdMatch.ResourceGroup,
                        resourceIdMatch.ResourceName)
                    .ConfigureAwait(false);

                if (connectorEntity != null)
                {
                    if (connectorEntity.Properties.ReferedBy == null)
                    {
                        this.EventSource.Debug(
                            operationName,
                            $"Added workspaceId: '{workspaceResourceId}' to accessConnector referBy list " +
                            $": {accessConnectorResourceId}.");

                        connectorEntity.Properties.ReferedBy = new List<string>() { workspaceResourceId };
                    }
                    else if (!connectorEntity.Properties.ReferedBy.Contains(workspaceResourceId))
                    {
                        this.EventSource.Debug(
                            operationName,
                            $"Appened workspaceId: '{workspaceResourceId}' to accessConnector referBy existing list " +
                            $": {accessConnectorResourceId}.");

                        connectorEntity.Properties.ReferedBy.Add(workspaceResourceId);
                    }

                    this.EventSource.Debug(
                            operationName,
                            $"Saving accessConnector entity to backend storage!!.");

                    await this.AccessConnectorDataProvider.SaveAccessConnector(connectorEntity).ConfigureAwait(false);
                }
            }
        }

        /// <summary>
        /// Perform Access connector Update with WorkspaceId verification
        /// </summary>
        /// <param name="accessConnectorResourceId">access connector resource id</param>
        /// <param name="workspaceResourceId">workspace resource id to be removed from connector</param>
        /// <returns></returns>
        public async Task RemoveWorkspaceIdFromConnectorEntityReferByList(string accessConnectorResourceId, string workspaceResourceId)
        {
            var operationName = this.GetOperationName(Utilities.GetAsyncMethodName());

            if (!string.IsNullOrEmpty(accessConnectorResourceId))
            {
                this.EventSource.Debug(operationName,
                $"Removing workspaceId: '{workspaceResourceId}' from referedBy list of " +
                $"existing connectorId : '{accessConnectorResourceId}'");

                var resourceIdMatch = ResourceIdRequestMatch.FromFullyQualifiedId(accessConnectorResourceId);
                var connectorEntity = await this.AccessConnectorDataProvider
                    .FindAccessConnector(
                        resourceIdMatch.SubscriptionId,
                        resourceIdMatch.ResourceGroup,
                        resourceIdMatch.ResourceName)
                    .ConfigureAwait(false);

                if (connectorEntity?.Properties.ReferedBy?.Contains(workspaceResourceId) == true)
                {
                    this.EventSource.Debug(operationName,
                        $"Found workspaceId: '{workspaceResourceId}' in referedBy list of " +
                        $"connectorId : '{accessConnectorResourceId}, will remove it.'");

                    connectorEntity.Properties.ReferedBy.Remove(workspaceResourceId);
                    await this.AccessConnectorDataProvider.SaveAccessConnector(connectorEntity).ConfigureAwait(false);
                }
                else
                {
                    this.EventSource.Debug(operationName,
                        $"AccessConnector is null or empty in the appliance properties, skipping removal of " +
                        $"workspaceId: '{workspaceResourceId}' from referredBy list.");
                }
            }
        }

        /// <summary>
        /// Gets the Databricks Blob data provider.
        /// </summary>
        public IDatabricksBlobDataProvider GetDatabricksBlobDataProvider()
        {
            return this.DataBricksBlobDataProvider;
        }

        /// <summary>
        /// Checks if this is a staging environment
        /// </summary>
        public async Task<bool> IsStagingEnvironment(ApplianceEntity applianceEntity)
        {
            var operationName = this.GetOperationName(Utilities.GetAsyncMethodName());
            try
            {
                var registeredFeatures = await this.FrontdoorEngine.GetRegisteredFeaturesInSubscription(
                        RequestCorrelationContext.Current.GetHomeTenantId(),
                        applianceEntity.SubscriptionId,
                        ProviderConstants.Databricks.ResourceProviderNamespace).ConfigureAwait(false);

                if (registeredFeatures != null && registeredFeatures.Contains(ProviderConstants.Databricks.MicrosoftDatabricksStagingEnvironmentFeature))
                {
                    this.EventSource.Debug(operationName, "The workspace is in a staging subscription.");
                    return true;
                }
                return false;
            }
            catch (ServerErrorResponseMessageException exception)
            {
                this.EventSource.Debug(operationName, $"Error when getting Databricks features for subscription '{applianceEntity.SubscriptionId}'. Exception: {Utilities.FlattenException(exception)}");

                throw;
            }
        }

        /// <summary>
        /// Check if needs to bypass all interactions with the RP backend, including notifications for workspace initialization, updates, and deletion
        /// </summary>
        public async Task<bool> ShouldSkipControlPlane(ApplianceEntity applianceEntity)
        {
            var operationName = this.GetOperationName(Utilities.GetAsyncMethodName());
            try
            {
                var registeredFeatures = await this.FrontdoorEngine.GetRegisteredFeaturesInSubscription(
                        RequestCorrelationContext.Current.GetHomeTenantId(),
                        applianceEntity.SubscriptionId,
                        ProviderConstants.Databricks.ResourceProviderNamespace).ConfigureAwait(false);

                if (registeredFeatures != null && registeredFeatures.Contains(ProviderConstants.Databricks.MicrosoftDatabricksSkipControlPlaneFeature))
                {
                    return true;
                }
                return false;
            }
            catch (ServerErrorResponseMessageException exception)
            {
                this.EventSource.Debug(operationName, $"Error when getting Databricks features for subscription '{applianceEntity.SubscriptionId}'. Exception: {Utilities.FlattenException(exception)}");

                throw;
            }
        }

        /// <summary>
        /// Handles necessary actions when principalId of connector changes
        /// </summary>
        /// <param name="incomingEntity">incoming workspace entity</param>
        /// <param name="existingConnector">existing accessConnectorId entity on workspace</param>
        /// <param name="publisherTenantId">publisher tenant id for workspace</param>
        /// <param name="dbfsResourceId">dbfs resource id</param>
        /// <returns></returns>
        private async Task<AccessConnectorIdEntity> HandlePrincipalIdChange(
            ApplianceEntity incomingEntity,
            AccessConnectorIdEntity existingConnector,
            string publisherTenantId,
            string dbfsResourceId)
        {
            var operationName = this.GetOperationName(Utilities.GetAsyncMethodName());

            if (!string.Equals(existingConnector?.PrincipalId,
                incomingEntity.Properties.AccessConnector.PrincipalId, StringComparison.OrdinalIgnoreCase))
            {
                if (!string.IsNullOrEmpty(existingConnector?.PrincipalId))
                {
                    this.EventSource.Debug(operationName,
                            $"Removing existing Access Connector PrincipalId:'{existingConnector.PrincipalId}' from Dbfs RBAC.");

                    await this
                        .DeleteRoleAssignmentIfExists(
                            existingConnector.PrincipalId,
                            dbfsResourceId,
                            ProviderConstants.Storage.StorageBlobDataContributorRoleId,
                            incomingEntity.SubscriptionId,
                            RequestCorrelationContext.Current.GetHomeTenantId(),
                            this.Metadata.ResourceProviderNamespace)
                        .ConfigureAwait(false);
                }

                this.EventSource.Debug(operationName,
                        $"Creating Dbfs RBAC for incoming Access Connector PrincipalId: '{incomingEntity.Properties.AccessConnector.PrincipalId}'.");

                await this
                    .CreateRoleAssignmentIfNotExists(
                        incomingEntity.Properties.AccessConnector.PrincipalId,
                        PrincipalType.ServicePrincipal,
                        dbfsResourceId,
                        ProviderConstants.Storage.StorageBlobDataContributorRoleId,
                        incomingEntity.SubscriptionId,
                        RequestCorrelationContext.Current.GetHomeTenantId(),
                        this.Metadata.ResourceProviderNamespace)
                    .ConfigureAwait(false);

                this.EventSource.Debug(operationName,
                            $"Creating or Updating Deny Assignment for incoming Access Connector PrincipalId:" +
                            $" '{incomingEntity.Properties.AccessConnector.PrincipalId}'.");

                await this
                    .CreateOrUpdateDenyAssignmentsForApplication(
                        publisherTenantId,
                        RequestCorrelationContext.Current.GetHomeTenantId(),
                        incomingEntity,
                        this.Metadata.ResourceType,
                        this.Metadata.ResourceProviderNamespace,
                        incomingEntity.Properties.AccessConnector.PrincipalId)
                    .ConfigureAwait(false);
            }

            return incomingEntity.Properties.AccessConnector;
        }
    }
}