//-----------------------------------------------------------
// Copyright (c) Microsoft Corporation.  All rights reserved.
//-----------------------------------------------------------

namespace Microsoft.WindowsAzure.ResourceStack.Providers.Worker.JobHandlers
{
    using global::Providers.Application.Services.Arg.Models;
    using global::Providers.Application.Services.Arg.Service;
    using Microsoft.WindowsAzure.ResourceStack.Common.BackgroundJobs;
    using Microsoft.WindowsAzure.ResourceStack.Common.Instrumentation;
    using Microsoft.WindowsAzure.ResourceStack.Common.Json;
    using Microsoft.WindowsAzure.ResourceStack.Common.Services;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Application.Data.DataProviders;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Application.Data.Definitions.DBAsyncContract;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Application.Data.Definitions.Enums;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Application.Data.Entities;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Application.Data.Extensions;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Application.Data.Metadata;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Common.Constants;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Common.Data;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Common.Engines;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Common.ErrorResponses;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Common.Extensions;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Common.Logging;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Common.Metrics;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Common.Responses;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Common.Utilities;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Worker.Engines;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Worker.StateHandlers;
    using Newtonsoft.Json;
    using System;
    using System.Net;
    using System.Threading;
    using System.Threading.Tasks;

    /// <summary>
    /// The handler class for the <c>ApplianceProvisioningJob</c>
    /// </summary>
    public class ApplianceProvisioningJobHandler : ApplianceJobHandler<ApplianceProvisioningJobMetadata>
    {
        /// <summary>
        /// Gets the ARG Dependency Publisher request timeout.
        /// Since the following configuration doesn't exist, the value will be set to default.
        /// This approach is helpful during tests, as it can be overwritten to a shorter duration to save time.
        /// </summary>
        private static TimeSpan ARGDependencyPublisherRequestTimeout
        {
            get
            {
                return CloudConfigurationManager.GetConfigurationTimeSpan(
                    settingName: "Microsoft.WindowsAzure.ResourceStack.Providers.ARGDependencyPublisher.RequestTimeout",
                    defaultValue: TimeSpan.FromMinutes(1));
            }
        }

        /// <summary>
        /// Appliance engine object.
        /// </summary>
        private readonly IApplianceEngine applianceEngine;

        /// <summary>
        /// Dependency publisher object.
        /// </summary>
        private readonly IDependencyPublisher dependencyPublisher;

        /// <summary>
        /// Initializes a new instance of the <see cref="ApplianceProvisioningJobHandler" /> class.
        /// </summary>
        /// <param name="logger">A logger object</param>
        /// <param name="frontdoorEngine">A <c>IFrontdoorEngine</c> object</param>
        /// <param name="applianceEngine">An <c>ApplianceEngine</c> object</param>
        /// <param name="applianceDataProvider">An <c>IApplianceDataprovider</c> object for retrieving appliance data.</param>
        /// <param name="accessConnectorDataProvider">An <c>IAccessConnectorDataProvider</c> object for retrieving accessConnector data.</param>
        /// <param name="marketPlacePackageDataProvider">An <c>IApplianceProvider</c> object for retrieving marketplace appliance data.</param>
        /// <param name="metadata">The metadata for the job</param>
        /// <param name="unregisterManagedByTenantEnabled">Indicates whether unregistering of managed tenants is enabled.</param>
        /// <param name="registerManagedByTenantApiVersion"><c>Api</c> version for managed tenant registration.</param>
        /// <param name="totalExecutedCount">The total executed count</param>
        public ApplianceProvisioningJobHandler(
            ILogger logger,
            IFrontdoorEngine frontdoorEngine,
            IApplianceEngine applianceEngine,
            IApplianceDataProvider applianceDataProvider,
            IAccessConnectorDataProvider accessConnectorDataProvider,
            IApplicationPackageDataProvider marketPlacePackageDataProvider,
            ApplianceProvisioningJobMetadata metadata,
            bool unregisterManagedByTenantEnabled,
            string registerManagedByTenantApiVersion,
            int totalExecutedCount)
            : base(
                logger,
                frontdoorEngine,
                applianceDataProvider,
                accessConnectorDataProvider,
                marketPlacePackageDataProvider,
                metadata,
                unregisterManagedByTenantEnabled,
                registerManagedByTenantApiVersion)
        {
            this.applianceEngine = applianceEngine;

            this.dependencyPublisher = new ARGServicePublisher(frontdoorEngine, logger.EventSource);

            if (Metadata.ExecutionStartDateTime == DateTime.MinValue)
            {
                this.Metadata.ExecutionStartDateTime = DateTime.UtcNow;
            }

            this.Logger.LogDebug(
                operationName: "ApplianceProvisioningJob.Constructor",
                message: $"SLO:: Metadata updated with Job Start Date: {this.Metadata.ExecutionStartDateTime}");
        }

        /// <summary>
        /// Handle when job maximum life time has exceeded.
        /// </summary>
        public async Task OnJobMaxLifetimeExceeded()
        {
            var message = string.Format(
                format: "The '{0}' creation job did not complete within the allowed timeout period '{1}'.",
                arg0: this.Metadata.ResourceType,
                arg1: this.Metadata.ProvidersJobMaxLifetime);

            this.Logger.LogError(operationName: "ApplianceProvisioningJob.OnJobMaxLifetimeExceeded", message: message);

            await this
                .SetApplianceProvisioningState(
                    subscriptionId: this.Metadata.SubscriptionId,
                    resourceGroupName: this.Metadata.ResourceGroupName,
                    applianceName: this.Metadata.ApplianceName,
                    resourceType: this.Metadata.ResourceType,
                    provisioningState: ProvisioningState.Failed)
                .ConfigureAwait(continueOnCapturedContext: false);

            this.Metadata.OperationResponseStatusCode = HttpStatusCode.OK;
            this.Metadata.OperationResponseContent = AsyncOperationResult
                .GetAsyncOperationResult(
                    provisioningState: ProvisioningState.Failed,
                    errorCode: ErrorResponseCode.ApplianceProvisioningTimeout.ToString(),
                    message: message)
                .ToJToken();

            throw new JobExecutionResultException(status: JobExecutionStatus.Faulted, message: message, nextMetadata: this.Metadata.ToJson());
        }

        /// <summary>
        /// Handles the job execution.
        /// </summary>
        /// <returns>The job execution result</returns>
        public async Task<JobExecutionResult> OnJobExecute()
        {
            this.Logger.LogDebug(operationName: "ApplianceProvisioningJob.OnJobExecute", message: $"{DateTime.UtcNow}::OnJobExecute method entered");

            var metricsManager = MetricsManagerFactory.GetInstance(this.Logger.EventSource);

            var appliance = await this
                .GetApplianceDataProvider(resourceType: this.Metadata.ResourceType)
                .FindAppliance(
                    subscriptionId: this.Metadata.SubscriptionId,
                    resourceGroupName: this.Metadata.ResourceGroupName,
                    applianceName: this.Metadata.ApplianceName)
                .ConfigureAwait(continueOnCapturedContext: false);

            var debugMessage = string.Format(
                "ResourceType: '{0}', SubscriptionId: '{1}', ResourceGroup: '{2}', ApplianceName: '{3}', Appliance == null: '{4}'",
                this.Metadata.ResourceType,
                this.Metadata.SubscriptionId,
                this.Metadata.ResourceGroupName,
                this.Metadata.ApplianceName,
                appliance == null);

            this.Logger.LogDebug(operationName: "ApplianceProvisioningJob.OnJobExecute", message: debugMessage);

            if (appliance == null || !(appliance.Properties?.ProvisioningState == ProvisioningState.Accepted ||
                                       appliance.Properties?.ProvisioningState == ProvisioningState.Updating))
            {
                return MarkJobExecutionToFaulted(appliance);
            }

            var jobExecutionResult = await this
                .HandleApplianceProvisioning()
                .ConfigureAwait(false);

            this.Logger.LogDebug(operationName: "ApplianceProvisioningJob.OnJobExecute", message: $"Job Execution Status: '{jobExecutionResult.Status}'. Job Execution Results: {jobExecutionResult?.ToJson()}");

            ApplianceProvisioningJobMetadata metadata = JsonConvert.DeserializeObject<ApplianceProvisioningJobMetadata>(jobExecutionResult.NextMetadata);

            this.Logger.LogDebug(operationName: "ApplianceProvisioningJob.OnJobExecute", message: $"SLO:: Metadata Job Start Time: {metadata.ExecutionStartDateTime}");

            // Set appliance operation type used as input for MDM metrc
            var applianceOperationType = ProviderConstants.Databricks.CreateWorkspaceOperation;
            if (this.Metadata.ApplianceOperationType == ProvisioningOperation.Updating)
            {
                applianceOperationType = ProviderConstants.Databricks.UpdateWorkspaceOperation;
            }

            DateTime endDate;
            TimeSpan dateDiff;

            if (jobExecutionResult.Status == JobExecutionStatus.Succeeded)
            {
                appliance = await this
                .GetApplianceDataProvider(resourceType: this.Metadata.ResourceType)
                .FindAppliance(
                    subscriptionId: this.Metadata.SubscriptionId,
                    resourceGroupName: this.Metadata.ResourceGroupName,
                    applianceName: this.Metadata.ApplianceName)
                .ConfigureAwait(continueOnCapturedContext: false);

                if (appliance.Properties.ProvisioningState == ProvisioningState.Succeeded)
                {
                    endDate = DateTime.UtcNow;
                    dateDiff = endDate - metadata.ExecutionStartDateTime;

                    this.Logger.LogDebug(operationName: "ApplianceProvisioningJob.OnJobExecute",
                        message:
                        $"SLO:{applianceOperationType}:Job succeed for workspace: {this.Metadata.SubscriptionId}/{this.Metadata.ResourceGroupName}/{this.Metadata.ApplianceName}, " +
                        $"Start date: {metadata.ExecutionStartDateTime}, End Date: {endDate}, Diff:{dateDiff.TotalMilliseconds} ms, {dateDiff.TotalSeconds} secs, {dateDiff.TotalMinutes} mins, " +
                        $"apiversion:{metadata.RequestCorrelationContext.ApiVersion}");

                    metricsManager.MeasureLongRunningLatencyResponse((long)dateDiff.TotalMilliseconds,
                        this.Metadata.SubscriptionId,
                        this.Metadata.ResourceGroupName,
                        this.Metadata.ApplianceName,
                        ProviderConstants.Databricks.Success,
                        applianceOperationType,
                        this.Metadata.RequestCorrelationContext.ApiVersion);
                }

                await PublishWorkspaceToARG(appliance);
            }

            if (jobExecutionResult.Status == JobExecutionStatus.Completed || jobExecutionResult.Status == JobExecutionStatus.Failed || jobExecutionResult.Status == JobExecutionStatus.Faulted)
            {
                endDate = DateTime.UtcNow;
                dateDiff = endDate - metadata.ExecutionStartDateTime;

                this.Logger.LogDebug(operationName: "ApplianceProvisioningJob.OnJobExecute",
                    message:
                    $"SLO::{applianceOperationType}:Job failed for workspace: {this.Metadata.SubscriptionId}/{this.Metadata.ResourceGroupName}/{this.Metadata.ApplianceName}, " +
                    $"Start date: {metadata.ExecutionStartDateTime}, End Date {endDate}, Diff:{dateDiff.TotalMilliseconds} ms, {dateDiff.TotalSeconds} secs, {dateDiff.TotalMinutes} mins, " +
                    $"apiversion:{metadata.RequestCorrelationContext.ApiVersion}");

                var sliErrorDetails = SLIErrorInfo.ConvertToSLIErrorInfo(this.Metadata.SLIErrorInfo, this.Metadata);

                metricsManager.MeasureLongRunningLatencyResponse((long)dateDiff.TotalMilliseconds,
                    this.Metadata.SubscriptionId,
                    this.Metadata.ResourceGroupName,
                    this.Metadata.ApplianceName,
                    ProviderConstants.Databricks.Failed,
                    applianceOperationType,
                    this.Metadata.RequestCorrelationContext.ApiVersion,
                    sliErrorDetails.ResponsibleParty,
                    sliErrorDetails.FailureCategory);
            }

            this.Logger.LogDebug(operationName: "ApplianceProvisioningJob.OnJobExecute", message: $"{DateTime.UtcNow}::OnJobExecute method exited");

            return jobExecutionResult;
        }

        /// <summary>
        /// Marks Appliance as Faulted
        /// </summary>
        /// <param name="appliance"></param>
        /// <returns>Returns job execution result</returns>
        private JobExecutionResult MarkJobExecutionToFaulted(ApplianceEntity appliance)
        {
            var applianceState = appliance != null ? appliance.Properties.ProvisioningState : ProvisioningState.NotSpecified;
            var message = string.Format(
                format: "The resource of type '{0}' with name '{1}' is missing or the provisioning state is invalid '{2}'. Job faulted",
                arg0: this.Metadata.ResourceType,
                arg1: this.Metadata.ApplianceName,
                arg2: applianceState);

            this.Logger.LogError(operationName: "ApplianceProvisioningJob.OnJobExecute", message: message);

            this.Metadata.OperationResponseStatusCode = HttpStatusCode.OK;
            this.Metadata.OperationResponseContent = AsyncOperationResult
                .GetAsyncOperationResult(
                    provisioningState: ProvisioningState.Failed,
                    errorCode: ErrorResponseCode.InvalidApplianceState.ToString(),
                    message: ErrorResponseMessages.InvalidApplianceState.ToLocalizedMessage(this.Metadata.ResourceType, this.Metadata.ApplianceName, applianceState))
                .ToJToken();

            return new JobExecutionResult
            {
                Status = JobExecutionStatus.Faulted,
                NextMetadata = this.Metadata.ToJson(),
                Message = message
            };
        }

        /// <summary>
        /// Handles the appliance provisioning.
        /// </summary>

        private async Task<JobExecutionResult> HandleApplianceProvisioning()
        {
            switch (this.Metadata.ResourceOperation)
            {
                case ProvisioningOperation.Create:
                    switch (this.Metadata.ApplianceProvisioningState)
                    {
                        case ApplianceProvisioningState.Initializing:
                            var initializingHandler = new InitializingHandler(this.ApplianceDataProvider,
                                                                              this.MarketPlacePackageDataProvider,
                                                                              this.FrontdoorEngine,
                                                                              this.applianceEngine,
                                                                              this.Logger,
                                                                              this.Metadata);

                            return await initializingHandler.Execute().ConfigureAwait(continueOnCapturedContext: false);


                        case ApplianceProvisioningState.Initialized:
                            var initializedHandler = new InitializedHandler(this.ApplianceDataProvider,
                                                                              this.MarketPlacePackageDataProvider,
                                                                              this.FrontdoorEngine,
                                                                              this.applianceEngine,
                                                                              this.Logger,
                                                                              this.Metadata);

                            return await initializedHandler.Execute().ConfigureAwait(continueOnCapturedContext: false);

                        default:
                            await this
                                .SetApplianceProvisioningState(
                                    subscriptionId: this.Metadata.SubscriptionId,
                                    resourceGroupName: this.Metadata.ResourceGroupName,
                                    applianceName: this.Metadata.ApplianceName,
                                    resourceType: this.Metadata.ResourceType,
                                    provisioningState: ProvisioningState.Failed)
                                .ConfigureAwait(continueOnCapturedContext: false);

                            return new JobExecutionResult
                            {
                                Status = JobExecutionStatus.Faulted,
                                Message =
                                    $"The unknown appliance provisioning state '{this.Metadata.ResourceOperation}' is encountered. Current provisioning operation is failed.",
                                Details = this.Metadata.ResourceOperation.ToString(),
                            };
                    }

                case ProvisioningOperation.AzureAsyncOperationWaiting:
                    var azureAsyncOperationWaitingHandler = new AzureAsyncOperationWaitingHandler(this.ApplianceDataProvider,
                                                                      this.AccessConnectorDataProvider,
                                                                      this.MarketPlacePackageDataProvider,
                                                                      this.FrontdoorEngine,
                                                                      this.applianceEngine,
                                                                      this.Logger,
                                                                      this.Metadata);

                    return await azureAsyncOperationWaitingHandler.Execute().ConfigureAwait(continueOnCapturedContext: false);

                case ProvisioningOperation.Waiting:
                    var waitingHandler = new WaitingHandler(this.ApplianceDataProvider,
                                                                      this.MarketPlacePackageDataProvider,
                                                                      this.FrontdoorEngine,
                                                                      this.applianceEngine,
                                                                      this.Logger,
                                                                      this.Metadata);

                    return await waitingHandler.Execute().ConfigureAwait(continueOnCapturedContext: false);

                case ProvisioningOperation.PostDeploymentWaiting:
                    var postDeploymentWaitingHandler = new PostDeploymentWaitingHandler(this.ApplianceDataProvider,
                       this.Logger,
                        this.Metadata);

                    return await postDeploymentWaitingHandler.Execute().ConfigureAwait(continueOnCapturedContext: false);

                case ProvisioningOperation.AccountApiPolling:
                    return await HandleAccountApiPolling().ConfigureAwait(continueOnCapturedContext: false);

                case ProvisioningOperation.Updating:
                    var updatingHandler = new UpdatingHandler(this.ApplianceDataProvider,
                                                                    this.AccessConnectorDataProvider,
                                                                    this.MarketPlacePackageDataProvider,
                                                                    this.FrontdoorEngine,
                                                                    this.applianceEngine,
                                                                    this.Logger,
                                                                    this.Metadata);

                    return await updatingHandler.Execute().ConfigureAwait(continueOnCapturedContext: false);

                default:
                    await this
                        .SetApplianceProvisioningState(
                            subscriptionId: this.Metadata.SubscriptionId,
                            resourceGroupName: this.Metadata.ResourceGroupName,
                            applianceName: this.Metadata.ApplianceName,
                            resourceType: this.Metadata.ResourceType,
                            provisioningState: ProvisioningState.Failed)
                        .ConfigureAwait(continueOnCapturedContext: false);

                    return new JobExecutionResult
                    {
                        Status = JobExecutionStatus.Faulted,
                        Message = $"The unknown resource operation '{this.Metadata.ResourceOperation}' is encountered. Current provisioning operation is failed.",
                        Details = this.Metadata.ResourceOperation.ToString(),
                        NextMetadata = this.Metadata.ToJson(),
                    };
            }
        }

        /// <summary>
        /// Handles the Account API polling state.
        /// </summary>
        /// <returns>The job execution result.</returns>
        private async Task<JobExecutionResult> HandleAccountApiPolling()
        {
            string operationName = "ApplianceProvisioningJob.HandleAccountApiPolling";

            // Use extension methods to get polling state
            var pollingState = this.Metadata.GetAccountApiPollingState();
            string workspaceId = this.Metadata.GetAccountApiOperationId();

            // If workspaceId is null, try to get it from the appliance entity
            if (string.IsNullOrEmpty(workspaceId))
            {
                var appliance = await this.ApplianceDataProvider.FindAppliance(
                    this.Metadata.SubscriptionId,
                    this.Metadata.ResourceGroupName,
                    this.Metadata.ApplianceName).ConfigureAwait(false);

                if (appliance != null)
                {
                    workspaceId = appliance.GetFullyQualifiedId(this.Metadata.ResourceType);
                    this.Logger.LogDebug(operationName, $"WorkspaceId was null, using entity's fully qualified ID: '{workspaceId}'");
                    this.Metadata.SetAccountApiOperationId(workspaceId);
                }
            }

            if (string.IsNullOrEmpty(workspaceId))
            {
                this.Logger.LogError(operationName, "WorkspaceId is null and could not be determined from the entity");
                this.Metadata.SetAccountApiPollingState(AccountApiPollingState.Failed);

                this.Metadata.OperationResponseStatusCode = HttpStatusCode.OK;
                this.Metadata.OperationResponseContent = AsyncOperationResult
                    .GetAsyncOperationResult(
                        ProvisioningState.Failed,
                        ErrorResponseCode.WorkspaceUpdateFailed.ToString(),
                        "WorkspaceId is null and could not be determined from the entity")
                    .ToJToken();

                return new JobExecutionResult
                {
                    Status = JobExecutionStatus.Failed,
                    Message = "WorkspaceId is null and could not be determined from the entity",
                    NextMetadata = this.Metadata.ToJson()
                };
            }

            this.Logger.LogDebug(operationName, $"Handling Account API polling for workspace '{workspaceId}'");

            string customerTenantId = RequestCorrelationContext.Current.GetHomeTenantId();
            Uri baseUri = this.Metadata.GetAccountApiBaseUri();
            string audience = this.Metadata.GetAccountApiAudience();
            string apiVersion = RequestCorrelationContext.Current.ApiVersion;

            // If baseUri or audience is null, get them from the FrontdoorEngine
            if (baseUri == null || string.IsNullOrEmpty(audience))
            {
                this.Logger.LogDebug(
                    operationName,
                    $"Base URI or audience is null, getting them from FrontdoorEngine");

                (Uri newBaseUri, string newAudience, bool useArm) = await this.FrontdoorEngine
                    .GetDatabricksAccountApiEnpointAndAudience(customerTenantId, this.Metadata.SubscriptionId)
                    .ConfigureAwait(false);

                if (baseUri == null)
                {
                    baseUri = newBaseUri;
                    this.Metadata.SetAccountApiBaseUri(baseUri);
                    this.Logger.LogDebug(
                        operationName,
                        $"Set base URI to {baseUri}");
                }

                if (string.IsNullOrEmpty(audience))
                {
                    audience = newAudience;
                    this.Metadata.SetAccountApiAudience(audience);
                    this.Logger.LogDebug(
                        operationName,
                        $"Set audience to {audience}");
                }
            }

            try
            {
                // Handle delete operation polling
                if (pollingState == AccountApiPollingState.WaitingForDeletion || pollingState == AccountApiPollingState.WaitingForCompletion)
                {
                    this.Logger.LogDebug(
                        operationName,
                        $"Polling for delete operation completion for workspace '{workspaceId}'");


                        // Check if the workspace still exists
                        DatabricksWorkspace candidate = await DatabricksAccountsManagerUtils.GetDBWorkspace<DatabricksWorkspace>(
                            this.FrontdoorEngine,
                            customerTenantId,
                            workspaceId,
                            baseUri,
                            apiVersion,
                            audience,
                            false).ConfigureAwait(false);

                        if (candidate != null)
                        {
                            // Workspace still exists, continue polling
                            this.Logger.LogDebug(
                                operationName,
                                $"Workspace '{workspaceId}' delete operation is still in progress. Postponing job.");

                            return new JobExecutionResult
                            {
                                Status = JobExecutionStatus.Postponed,
                                NextExecutionTime = DateTime.UtcNow.Add(TimeSpan.FromSeconds(FrontdoorEngine.AccountApiPollingRetryInterval)),
                                Message = $"Waiting for Account API delete operation to complete for workspace '{workspaceId}'",
                                NextMetadata = this.Metadata.ToJson()
                            };
                        }

                    // If we reach here, the workspace no longer exists (delete completed successfully)
                    this.Logger.LogDebug(
                        operationName,
                        $"Workspace '{workspaceId}' delete operation completed successfully.");

                    this.Metadata.SetAccountApiPollingState(AccountApiPollingState.Completed);

                    // Update the appliance entity's provisioning state to Succeeded for deletion
                    var appliance = await this.ApplianceDataProvider.FindAppliance(
                        this.Metadata.SubscriptionId,
                        this.Metadata.ResourceGroupName,
                        this.Metadata.ApplianceName).ConfigureAwait(false);

                    if (appliance != null)
                    {
                        appliance.Properties.ProvisioningState = ProvisioningState.Succeeded;

                        // For deletion, we don't need to set workspace ID and URL as the workspace no longer exists

                        await this.ApplianceDataProvider.ReplaceAppliance(appliance).ConfigureAwait(false);
                        this.Logger.LogDebug(
                            operationName,
                            $"Updated appliance entity provisioning state to Succeeded for deleted workspace '{workspaceId}'");
                    }

                    // Set the operation response status code for successful completion
                    this.Metadata.OperationResponseStatusCode = HttpStatusCode.OK;
                    this.Metadata.OperationResponseContent = AsyncOperationResult
                        .GetAsyncOperationResult(ProvisioningState.Succeeded)
                        .ToJToken();

                    return new JobExecutionResult
                    {
                        Status = JobExecutionStatus.Succeeded,
                        Message = $"Successfully completed Account API delete operation for workspace '{workspaceId}'",
                        NextMetadata = this.Metadata.ToJson()
                    };
                }
                else if (pollingState == AccountApiPollingState.WaitingForInitialization)
                {
                    // Handle initialization polling
                    this.Logger.LogDebug(
                        operationName,
                        $"Polling for initialization operation completion for workspace '{workspaceId}'");

                    // Make a single call to check operation status
                    DatabricksWorkspace candidate = await DatabricksAccountsManagerUtils.GetDBWorkspace<DatabricksWorkspace>(
                        this.FrontdoorEngine,
                        customerTenantId,
                        workspaceId,
                        baseUri,
                        apiVersion,
                        audience,
                        false).ConfigureAwait(false);

                    if (candidate != null && candidate.Properties.OperationStatus.OperationStatus == DBOperationStatus.Running)
                    {
                        // Still initializing, postpone again
                        this.Logger.LogDebug(
                            operationName,
                            $"Workspace '{workspaceId}' initialization is still running. Postponing job.");

                        return new JobExecutionResult
                        {
                            Status = JobExecutionStatus.Postponed,
                            NextExecutionTime = DateTime.UtcNow.Add(TimeSpan.FromSeconds(FrontdoorEngine.AccountApiPollingRetryInterval)),
                            Message = $"Waiting for Account API initialization operation to complete for workspace '{workspaceId}'",
                            NextMetadata = this.Metadata.ToJson()
                        };
                    }
                    else if (candidate != null && candidate.Properties.OperationStatus.OperationStatus == DBOperationStatus.Succeeded)
                    {
                        // Initialization complete, store workspace details and return success
                        this.Logger.LogDebug(
                            operationName,
                            $"Workspace '{workspaceId}' initialization completed successfully. Response: {Newtonsoft.Json.Linq.JToken.FromObject(candidate)}");

                        this.Metadata.SetAccountApiPollingState(AccountApiPollingState.Completed);

                        // Store workspace details in metadata for retrieval by caller
                        this.Metadata.SetWorkspaceDetails(candidate.ToWorkspaceDetails());

                        // Update the appliance entity's provisioning state, workspace ID, and workspace URL
                        var appliance = await this.ApplianceDataProvider.FindAppliance(
                            this.Metadata.SubscriptionId,
                            this.Metadata.ResourceGroupName,
                            this.Metadata.ApplianceName).ConfigureAwait(false);

                        if (appliance != null)
                        {
                            // Get workspace details from metadata
                            var workspaceDetails = this.Metadata.WorkspaceDetails;

                            // Update appliance properties
                            appliance.Properties.ProvisioningState = ProvisioningState.Succeeded;
                            appliance.Properties.WorkspaceId = workspaceDetails.WorkspaceId;
                            appliance.Properties.WorkspaceUrl = workspaceDetails.WorkspaceURL;

                            await this.ApplianceDataProvider.ReplaceAppliance(appliance).ConfigureAwait(false);
                            this.Logger.LogDebug(
                                operationName,
                                $"Updated appliance entity with provisioning state Succeeded, workspace ID {workspaceDetails.WorkspaceId}, and workspace URL {workspaceDetails.WorkspaceURL} for workspace '{workspaceId}'");
                        }

                        // Set the operation response status code for successful completion
                        this.Metadata.OperationResponseStatusCode = HttpStatusCode.OK;
                        this.Metadata.OperationResponseContent = AsyncOperationResult
                            .GetAsyncOperationResult(ProvisioningState.Succeeded)
                            .ToJToken();

                        return new JobExecutionResult
                        {
                            Status = JobExecutionStatus.Succeeded,
                            Message = $"Successfully completed Account API initialization operation for workspace '{workspaceId}'",
                            NextMetadata = this.Metadata.ToJson()
                        };
                    }
                    else
                    {
                        // Initialization failed
                        this.Logger.LogError(
                            operationName,
                            $"Workspace '{workspaceId}' initialization failed with status: {candidate?.Properties?.OperationStatus?.OperationStatus}. Response: {Newtonsoft.Json.Linq.JToken.FromObject(candidate)}");

                        this.Metadata.SetAccountApiPollingState(AccountApiPollingState.Failed);

                        // Set the operation response status code for failed completion
                        this.Metadata.OperationResponseStatusCode = HttpStatusCode.OK;
                        this.Metadata.OperationResponseContent = AsyncOperationResult
                            .GetAsyncOperationResult(
                                ProvisioningState.Failed,
                                ErrorResponseCode.WorkspaceUpdateFailed.ToString(),
                                $"Account API initialization operation failed for workspace '{workspaceId}' with status: {candidate?.Properties?.OperationStatus?.OperationStatus}")
                            .ToJToken();

                        return new JobExecutionResult
                        {
                            Status = JobExecutionStatus.Failed,
                            Message = $"Account API initialization operation failed for workspace '{workspaceId}'",
                            NextMetadata = this.Metadata.ToJson()
                        };
                    }
                }
                else if (pollingState == AccountApiPollingState.WaitingForUpdate)
                {
                    // Handle update polling
                    this.Logger.LogDebug(
                        operationName,
                        $"Polling for update operation completion for workspace '{workspaceId}'");

                    // Make a single call to check operation status
                    DatabricksWorkspace candidate = await DatabricksAccountsManagerUtils.GetDBWorkspace<DatabricksWorkspace>(
                        this.FrontdoorEngine,
                        customerTenantId,
                        workspaceId,
                        baseUri,
                        apiVersion,
                        audience,
                        false).ConfigureAwait(false);

                    if (candidate != null && candidate.Properties.OperationStatus.OperationStatus == DBOperationStatus.Running)
                    {
                        // Still updating, postpone again
                        this.Logger.LogDebug(
                            operationName,
                            $"Workspace '{workspaceId}' update is still running. Postponing job.");

                        return new JobExecutionResult
                        {
                            Status = JobExecutionStatus.Postponed,
                            NextExecutionTime = DateTime.UtcNow.Add(TimeSpan.FromSeconds(FrontdoorEngine.AccountApiPollingRetryInterval)),
                            Message = $"Waiting for Account API update operation to complete for workspace '{workspaceId}'",
                            NextMetadata = this.Metadata.ToJson()
                        };
                    }
                    else if (candidate != null && candidate.Properties.OperationStatus.OperationStatus == DBOperationStatus.Succeeded)
                    {
                        // Update complete, store workspace details and return success
                        this.Logger.LogDebug(
                            operationName,
                            $"Workspace '{workspaceId}' update completed successfully. Response: {Newtonsoft.Json.Linq.JToken.FromObject(candidate)}");

                        this.Metadata.SetAccountApiPollingState(AccountApiPollingState.Completed);

                        // Store workspace details in metadata for retrieval by caller
                        this.Metadata.SetWorkspaceDetails(candidate.ToWorkspaceDetails());

                        // For update operations, continue with post-deployment waiting
                        this.Metadata.ResourceOperation = ProvisioningOperation.PostDeploymentWaiting;

                        var appliance = await this.ApplianceDataProvider.FindAppliance(
                            this.Metadata.SubscriptionId,
                            this.Metadata.ResourceGroupName,
                            this.Metadata.ApplianceName).ConfigureAwait(false);

                        return new JobExecutionResult
                        {
                            Status = JobExecutionStatus.Postponed,
                            NextExecutionTime = DateTime.UtcNow.Add(CloudConfigurationManager.GetConfigurationTimeSpan(
                                ProviderConstants.Databricks.DBNotificationPropagationPostponeIntervalKey,
                                TimeSpan.FromMinutes(10))),
                            Message = $"Successfully completed Account API update operation for the workspace '{this.Metadata.ApplianceName}'. Waiting for the post deployment operations. Postponing job",
                            Details = HttpStatusCode.OK.ToString(),
                            NextMetadata = this.Metadata.ToJson()
                        };
                    }
                    else
                    {
                        // Update failed
                        this.Logger.LogError(
                            operationName,
                            $"Workspace '{workspaceId}' update failed with status: {candidate?.Properties?.OperationStatus?.OperationStatus}. Response: {Newtonsoft.Json.Linq.JToken.FromObject(candidate)}");

                        this.Metadata.SetAccountApiPollingState(AccountApiPollingState.Failed);

                        var appliance = await this.ApplianceDataProvider.FindAppliance(
                            this.Metadata.SubscriptionId,
                            this.Metadata.ResourceGroupName,
                            this.Metadata.ApplianceName).ConfigureAwait(false);

                        appliance.Properties.ProvisioningState = ProvisioningState.Failed;

                        await this.ApplianceDataProvider.ReplaceAppliance(appliance).ConfigureAwait(false);

                        this.Metadata.OperationResponseStatusCode = HttpStatusCode.OK;
                        this.Metadata.OperationResponseContent = AsyncOperationResult
                            .GetAsyncOperationResult(
                                ProvisioningState.Failed,
                                ErrorResponseCode.WorkspaceUpdateFailed.ToString(),
                                $"Account API update operation failed for workspace '{workspaceId}' with status: {candidate?.Properties?.OperationStatus?.OperationStatus}")
                            .ToJToken();

                        return new JobExecutionResult
                        {
                            Status = JobExecutionStatus.Failed,
                            Message = $"Account API update operation failed for workspace '{workspaceId}' with status: {candidate?.Properties?.OperationStatus?.OperationStatus}",
                            NextMetadata = this.Metadata.ToJson()
                        };
                    }
                }
            }
            catch (Exception ex)
            {
                // Handle exceptions during polling
                this.Logger.LogError(
                    operationName,
                    $"Error occurred while polling for workspace '{workspaceId}' operation: {ex.Message}");

                // If it's a transient error, postpone and retry
                if (ex is ServerErrorResponseMessageException serverEx)
                {
                    return new JobExecutionResult
                    {
                        Status = JobExecutionStatus.Postponed,
                        NextExecutionTime = DateTime.UtcNow.Add(TimeSpan.FromSeconds(FrontdoorEngine.AccountApiPollingRetryInterval)),
                        Message = $"Transient error while polling for Account API operation for workspace '{workspaceId}'. Retrying.",
                        NextMetadata = this.Metadata.ToJson()
                    };
                }

                // For non-transient errors, mark as failed
                this.Metadata.SetAccountApiPollingState(AccountApiPollingState.Failed);

                var appliance = await this.ApplianceDataProvider.FindAppliance(
                    this.Metadata.SubscriptionId,
                    this.Metadata.ResourceGroupName,
                    this.Metadata.ApplianceName).ConfigureAwait(false);

                if (appliance != null)
                {
                    appliance.Properties.ProvisioningState = ProvisioningState.Failed;
                    await this.ApplianceDataProvider.ReplaceAppliance(appliance).ConfigureAwait(false);
                }

                this.Metadata.OperationResponseStatusCode = HttpStatusCode.OK;
                this.Metadata.OperationResponseContent = AsyncOperationResult
                    .GetAsyncOperationResult(
                        ProvisioningState.Failed,
                        ErrorResponseCode.WorkspaceUpdateFailed.ToString(),
                        $"Error occurred while polling for Account API operation for workspace '{workspaceId}': {ex.Message}")
                    .ToJToken();

                return new JobExecutionResult
                {
                    Status = JobExecutionStatus.Failed,
                    Message = $"Error occurred while polling for Account API operation for workspace '{workspaceId}': {ex.Message}",
                    NextMetadata = this.Metadata.ToJson()
                };
            }

            // Default return for unknown polling states
            this.Logger.LogWarning(
                operationName,
                $"Unknown Account API polling state: {pollingState}. Returning failed status.");

            this.Metadata.SetAccountApiPollingState(AccountApiPollingState.Failed);

            // Set the operation response status code for unknown polling state
            this.Metadata.OperationResponseStatusCode = HttpStatusCode.OK;
            this.Metadata.OperationResponseContent = AsyncOperationResult
                .GetAsyncOperationResult(
                    ProvisioningState.Failed,
                    ErrorResponseCode.WorkspaceUpdateFailed.ToString(),
                    $"Unknown Account API polling state: {pollingState}")
                .ToJToken();

            return new JobExecutionResult
            {
                Status = JobExecutionStatus.Failed,
                Message = $"Unknown Account API polling state: {pollingState}",
                NextMetadata = this.Metadata.ToJson()
            };
        }

        /// <summary>
        /// Publishes the workspace to Azure Resource Graph (ARG).
        /// </summary>
        /// <param name="appliance">The appliance entity containing workspace details.</param>
        /// <returns></returns>
        private async Task PublishWorkspaceToARG(ApplianceEntity appliance)
        {
            string operationName = "ApplianceProvisioningJob.PublishToARG";

            try
            {
                this.Logger.LogDebug(operationName, $"Publishing Workspace '{appliance.Name}' to ARG.");

                DependencyData dependencyData = dependencyPublisher.GetWorkspaceDependencyData(appliance);

                using (var cts = new CancellationTokenSource(ARGDependencyPublisherRequestTimeout))
                {
                    try
                    {
                        await dependencyPublisher.PushRelationshipNotification(dependencyData, cts.Token);
                    }
                    catch (OperationCanceledException ex)
                    {
                        // Log the error and suppress exceptions to maintain existing code paths
                        this.Logger.LogError(ex, operationName, $"A timeout error occurred while publishing Workspace '{appliance.Name}' to ARG. Details: {ex.Message}");
                    }
                }

                this.Logger.LogDebug(operationName, $"Publishing of Workspace to ARG '{appliance.Name}' has been successfully completed");
            }
            catch (Exception ex)
            {
                // Log the error and suppress exceptions to maintain existing code paths
                this.Logger.LogError(ex, operationName, $"An error occurred while publishing Workspace '{appliance?.Name}' to ARG. Details: {ex.Message}");
            }
        }
    }
}
