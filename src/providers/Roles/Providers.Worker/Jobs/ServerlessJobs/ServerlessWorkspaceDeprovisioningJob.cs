﻿//-----------------------------------------------------------
// Copyright (c) Microsoft Corporation.  All rights reserved.
//-----------------------------------------------------------

namespace Microsoft.WindowsAzure.ResourceStack.Providers.Worker.Jobs
{
    using System;
    using System.Net;
    using System.Threading.Tasks;
    using Microsoft.AspNetCore.JsonPatch.Operations;
    using Microsoft.WindowsAzure.ResourceStack.Common.BackgroundJobs;
    using Microsoft.WindowsAzure.ResourceStack.Common.EventSources;
    using Microsoft.WindowsAzure.ResourceStack.Common.Extensions;
    using Microsoft.WindowsAzure.ResourceStack.Common.Instrumentation;
    using Microsoft.WindowsAzure.ResourceStack.Common.Json;
    using Microsoft.WindowsAzure.ResourceStack.Common.Services;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Application.Data.Definitions.DBAsyncContract;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Application.Data.Entities;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Application.Data.Extensions;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Application.Data.Metadata;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Common.Constants;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Common.Data;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Common.ErrorResponses;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Common.Extensions;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Common.Metrics;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Common.Responses;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Common.Utilities;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Worker.Configuration;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Worker.Jobs.ServerlessJobs;

    /// <summary>
    /// The serverless workspace deletion job.
    /// </summary>
    [JobCallback(Name = ProviderConstants.Databricks.ServerlessWorkspaceDeprovisioningJob)]
    public class ServerlessWorkspaceDeprovisioningJob : ApplianceJob<ApplianceDeprovisioningJobMetadata>
    {
        #region Constructor

        /// <summary>
        /// Initializes a new instance of the <see cref="ServerlessWorkspaceDeprovisioningJob" /> class.
        /// </summary>
        /// <param name="jobConfiguration">The job configuration.</param>
        public ServerlessWorkspaceDeprovisioningJob(ProvidersJobConfiguration jobConfiguration)
            : base(jobConfiguration)
        {
        }

        #endregion

        #region Properties

        /// <summary>
        /// Gets the resource deletion job postpone interval.
        /// </summary>
        protected override TimeSpan PostponeInterval
        {
            get
            {
                return CloudConfigurationManager.GetConfigurationTimeSpan(
                    settingName: "Microsoft.WindowsAzure.ResourceStack.Providers.ApplianceDeprovisioningJob.PostponeInterval",
                    defaultValue: TimeSpan.FromSeconds(10));
            }
        }

        /// <summary>
        /// Gets the Max retries for serverless workspace deprovisioning.
        /// </summary>
        private int MaxRetries
        {
            get
            {
                return CloudConfigurationManager.GetConfigurationNumber(
                    "Microsoft.WindowsAzure.ResourceStack.Providers.ApplianceDeprovisioningJob.MaxRetries",
                    3);
            }
        }

        /// <summary>
        /// Gets a value indicating whether the environment is development.
        /// </summary>
        private bool IsDevEnvironment
        {
            get
            {
                return
                    CloudConfigurationManager.IsCloudEnvironmentEmulated &&
                    CloudConfigurationManager.GetConfiguration("Microsoft.WindowsAzure.ResourceStack.Providers.RoleLocation", string.Empty)
                                                .StartsWithInsensitively("DevFabric");
            }
        }

        #endregion

        /// <summary>
        /// Handle when job maximum life time has exceeded.
        /// </summary>
        protected override async Task OnJobMaxLifetimeExceeded()
        {
            var operationName = this.GetOperationName(Utilities.GetAsyncMethodName());
            var message = string.Format(
                format: "The serverless workspace deletion job did not complete within the allowed timeout period '{0}'.",
                arg0: this.Metadata.ProvidersJobMaxLifetime);

            this.Logger.LogError(operationName, message);

            await this
                .SetApplianceProvisioningState(
                    subscriptionId: this.Metadata.SubscriptionId,
                    resourceGroupName: this.Metadata.ResourceGroupName,
                    applianceName: this.Metadata.ApplianceName,
                    resourceType: this.Metadata.ResourceType,
                    provisioningState: ProvisioningState.Failed)
                .ConfigureAwait(continueOnCapturedContext: false);

            this.Metadata.OperationResponseStatusCode = HttpStatusCode.Conflict;
            this.Metadata.OperationResponseContent = AsyncOperationResult
                .GetAsyncOperationResult(
                    provisioningState: ProvisioningState.Failed,
                    errorCode: ErrorResponseCode.ApplianceDeprovisioningTimeout.ToString(),
                    message: message)
                .ToJToken();

            throw new JobExecutionResultException(status: JobExecutionStatus.Faulted, message: message, nextMetadata: this.Metadata.ToJson());
        }

        /// <summary>
        /// Executes the job asynchronously.
        /// </summary>
        protected override async Task<JobExecutionResult> OnJobExecute()
        {
            var metricsManager = MetricsManagerFactory.GetInstance(this.Logger.EventSource as ICommonEventSource);
            var operationName = this.GetOperationName(Utilities.GetAsyncMethodName());

            try
            {
                this.Logger.LogDebug(operationName, "Starting serverless de-provisioning job execution");

                if (this.Metadata.ExecutionStartDateTime == DateTime.MinValue)
                {
                    this.Metadata.ExecutionStartDateTime = DateTime.UtcNow;
                }

                this.Logger.LogDebug(
                    operationName,
                    $"SLO:: MetadataExecutionStartDate: {this.Metadata.ExecutionStartDateTime}");

                var workspaceEntity = await this
                    .GetApplianceDataProvider(resourceType: this.Metadata.ResourceType)
                    .FindAppliance(
                        subscriptionId: this.Metadata.SubscriptionId,
                        resourceGroupName: this.Metadata.ResourceGroupName,
                        applianceName: this.Metadata.ApplianceName)
                    .ConfigureAwait(continueOnCapturedContext: false);

                if (workspaceEntity == null)
                {
                    var message = string.Format(
                        format: "The resource type '{0}' with name '{1}' is not found. Job Succeeded",
                        arg0: this.Metadata.ResourceType,
                        arg1: this.Metadata.ApplianceName);

                    this.Metadata.OperationResponseStatusCode = HttpStatusCode.OK;

                    ServerlessJobHelper.LogSuccessMetrics(
                        logger: this.Logger,
                        metricsManager: metricsManager,
                        operationName: operationName,
                        operationType: ProviderConstants.Databricks.DeleteWorkspaceOperation,
                        subscriptionId: this.Metadata.SubscriptionId,
                        resourceGroupName: this.Metadata.ResourceGroupName,
                        workspaceName: this.Metadata.ApplianceName,
                        startDateTime: Metadata.ExecutionStartDateTime,
                        apiVersion: this.Metadata.RequestCorrelationContext.ApiVersion,
                        customMessage: "and workspaceEntity is null");

                    return new JobExecutionResult
                    {
                        Status = JobExecutionStatus.Succeeded,
                        NextMetadata = this.Metadata.ToJson(),
                        Message = message
                    };
                }

                var appliancePackage = this.GetAppliancePackage(workspaceEntity.Properties.PublisherPackageId);

                var jobExecutionResult = await this.HandleServerlessWorkspaceDeprovision(
                    existingEntity: workspaceEntity,
                    applicationPackage: appliancePackage)
                    .ConfigureAwait(false);

                this.Logger.LogDebug(operationName, $"Serverless Application deprovisioning status : {jobExecutionResult.ToJson()}");

                if (jobExecutionResult.Status == JobExecutionStatus.Faulted)
                {
                    var sliErrors = SLIErrorInfo.ConvertToSLIErrorInfo(this.Metadata.SLIErrorInfo, this.Metadata);

                    ServerlessJobHelper.LogFailedMetrics(
                        logger: this.Logger,
                        metricsManager: metricsManager,
                        operationName: operationName,
                        operationType: ProviderConstants.Databricks.DeleteWorkspaceOperation,
                        subscriptionId: this.Metadata.SubscriptionId,
                        resourceGroupName: this.Metadata.ResourceGroupName,
                        workspaceName: this.Metadata.ApplianceName,
                        startDateTime: Metadata.ExecutionStartDateTime,
                        apiVersion: this.Metadata.RequestCorrelationContext.ApiVersion,
                        sliErrors.ResponsibleParty,
                        sliErrors.FailureCategory);

                    await this
                        .SetApplianceProvisioningState(
                            subscriptionId: this.Metadata.SubscriptionId,
                            resourceGroupName: this.Metadata.ResourceGroupName,
                            applianceName: this.Metadata.ApplianceName,
                            resourceType: this.Metadata.ResourceType,
                            provisioningState: ProvisioningState.Failed)
                        .ConfigureAwait(continueOnCapturedContext: false);

                    return jobExecutionResult;
                }

                ServerlessJobHelper.LogSuccessMetrics(
                    logger: this.Logger,
                    metricsManager: metricsManager,
                    operationName: operationName,
                    operationType: ProviderConstants.Databricks.DeleteWorkspaceOperation,
                    subscriptionId: this.Metadata.SubscriptionId,
                    resourceGroupName: this.Metadata.ResourceGroupName,
                    workspaceName: this.Metadata.ApplianceName,
                    startDateTime: Metadata.ExecutionStartDateTime,
                    apiVersion: this.Metadata.RequestCorrelationContext.ApiVersion,
                    customMessage: "and workspaceEntity is not verified as null");

                return jobExecutionResult;
            }
            catch (Exception exception)
            {
                return await this.HandleDeprovisionUnexpectedException(
                    exception: exception,
                    metricsManager: metricsManager)
                    .ConfigureAwait(continueOnCapturedContext: false);
            }
        }

        /// <summary>
        /// Handles the serverless workspace deprovisioning logic.
        /// </summary>
        /// <param name="existingEntity">The existing entity</param>
        /// <param name="applicationPackage">The application package</param>
        /// <returns></returns>
        private async Task<JobExecutionResult> HandleServerlessWorkspaceDeprovision(
            ApplianceEntity existingEntity,
            AppliancePackage applicationPackage)
        {
            string operationName = this.GetOperationName(Utilities.GetAsyncMethodName());
            string workspaceId = existingEntity.GetFullyQualifiedId(this.Metadata.ResourceType);

            this.Logger.LogDebug(
                operationName,
                "Handle de-provisioning for operation. ResourceOperation: {0}",
                this.Metadata.ResourceOperation);

            // Check if we're in the polling state
            if (this.Metadata.ResourceOperation == ProvisioningOperation.AccountApiPolling)
            {
                return await this
                    .HandleAccountApiPolling(
                        existingEntity: existingEntity,
                        applicationPackage: applicationPackage)
                    .ConfigureAwait(continueOnCapturedContext: false);
            }

            try
            {
                bool skipControlPlane = this.IsDevEnvironment ? false : await this.ApplianceEngine.ShouldSkipControlPlane(existingEntity);
                if (!skipControlPlane)
                {
                    string apiVersion = existingEntity.GetDatabricksBackendApiVersion(this.Logger.EventSource as ICommonEventSource);

                    var response = await this.ApplianceEngine
                        .DBWorkspaceDeleteNotification(
                            this.Metadata.SubscriptionId,
                            workspaceId,
                            this.Metadata.ResourceProviderNamespace,
                            existingEntity,
                            false,
                            apiVersion)
                        .ConfigureAwait(false);

                    // Check if polling is required
                    if (response.RequiresPolling)
                    {
                        this.Logger.LogDebug(
                            operationName,
                            $"Polling required for workspace delete operation. PollingState: {response.PollingState}, OperationId: {response.OperationId}");

                        // Update metadata with polling information
                        this.Metadata.SetAccountApiPollingState(response.PollingState);
                        this.Metadata.SetAccountApiOperationId(response.OperationId);
                        this.Metadata.SetAccountApiOperationStartTime(DateTime.UtcNow);
                        this.Metadata.SetAccountApiBaseUri(response.BaseUri);
                        this.Metadata.SetAccountApiAudience(response.Audience);
                        this.Metadata.ResourceOperation = response.ResourceOperation;

                        return new JobExecutionResult
                        {
                            Status = JobExecutionStatus.Postponed,
                            NextExecutionTime = DateTime.UtcNow.Add(TimeSpan.FromSeconds(response.PollingIntervalSeconds)),
                            Message = $"Waiting for Account API delete operation to complete for workspace '{response.OperationId}'",
                            NextMetadata = this.Metadata.ToJson()
                        };
                    }
                }
                else
                {
                    this.Logger.LogDebug(operationName, $"Skip the step to notify DB for serverless Workspace DELETE since the subscription registed to the feature '{ProviderConstants.Databricks.MicrosoftDatabricksSkipControlPlaneFeature}'.");
                }
            }
            catch (ServerErrorResponseMessageException exception)
            {
                this.Logger.LogError(
                    operationName,
                    format: "Deleting resource type '{0}' with id '{1}' failed with exception '{2}', Retry Attempt '{3}'.",
                    arg0: this.Metadata.ResourceType,
                    arg1: existingEntity.GetFullyQualifiedId(this.Metadata.ResourceType),
                    arg2: exception.ToString(),
                    arg3: this.Metadata.CurrentDeploymentOperationExceptionRetryableErrorCount);

                if (exception.IsOperationExceptionRetryable(this.Metadata.CurrentDeploymentOperationExceptionRetryableErrorCount, this.MaxRetries))
                {
                    this.Metadata.CurrentDeploymentOperationExceptionRetryableErrorCount++;
                    return this.PostponeJob(
                        message: exception.Message,
                        delay: this.PostponeInterval);
                }
                else
                {
                    return await this.HandleWorkspaceDeprovisionFailure(
                        exception: exception,
                        statusCode: HttpStatusCode.Conflict,
                        customMessage: "Serverless workspace deprovisioning job completed with deployment failures.")
                        .ConfigureAwait(false);
                }
            }
            catch (Exception exception)
            {
                this.Logger.LogError(exception, operationName, $"Unexpected Exception Occurred: {exception?.Message}," +
                    $" Inner Exception:{exception?.InnerException?.Message}, Exception StackTrace:{exception?.StackTrace}");

                return await this.HandleWorkspaceDeprovisionFailure(
                    exception: exception,
                    statusCode: HttpStatusCode.InternalServerError)
                    .ConfigureAwait(false);
            }

            try
            {
                if (this.UnregisterManagedByTenantEnabled ||
                    await Utilities.IsManagedTenantFeatureEnabled(
                            existingEntity.SubscriptionId,
                            Logger.EventSource as ICommonEventSource,
                            this.GetFrontdoorEngine())
                        .ConfigureAwait(false))
                {
                    string applianceResourceId = existingEntity.GetFullyQualifiedId(this.Metadata.ResourceType);
                    string apiversion = await this.GetManagedByTenantApiVersion(existingEntity.SubscriptionId)
                        .ConfigureAwait(continueOnCapturedContext: false);

                    this.Logger.LogDebug(
                        operationName,
                        $"Calling UnregisterSubscriptionWithManagedTenant Endpoint with api-version :{apiversion} to un-register ManagedByTenant for the resource : {applianceResourceId ?? string.Empty}");

                    await this.GetFrontdoorEngine()
                        .UnregisterSubscriptionWithManagedTenant(
                            authenticationTenantId: RequestCorrelationContext.Current.GetHomeTenantId(),
                            subscriptionId: existingEntity.SubscriptionId,
                            publisherTenantId: applicationPackage.TenantId,
                            resourceProviderNamespace: this.Metadata.ResourceProviderNamespace,
                            apiVersion: apiversion,
                            resourceId: applianceResourceId)
                        .ConfigureAwait(continueOnCapturedContext: false);
                }
                else
                {
                    this.Logger.LogDebug(
                        operationName,
                        "UnRegistering of ManagedTenant for a subscription has been disabled.");
                }

                return await this.HandleWorkspaceDeprovisionSucceeded(existingEntity).ConfigureAwait(false);
            }
            catch (ServerErrorResponseMessageException exception)
            {
                this.Logger.LogError(
                    operationName,
                    format: "Deleting resource type '{0}' with id '{1}' failed with exception '{2}'.",
                    arg0: this.Metadata.ResourceType,
                    arg1: existingEntity.GetFullyQualifiedId(this.Metadata.ResourceType),
                    arg2: exception.ToString());

                return await this.HandleWorkspaceDeprovisionFailure(
                    exception: exception,
                    statusCode: exception.HttpStatus)
                    .ConfigureAwait(false);
            }
            catch (Exception exception)
            {
                this.Logger.LogError(exception, operationName, $"Unexpected Exception Occurred: {exception?.Message}," +
                    $" Inner Exception:{exception?.InnerException?.Message}, Exception StackTrace:{exception?.StackTrace}");

                return await this.HandleWorkspaceDeprovisionFailure(
                    exception: exception,
                    statusCode: HttpStatusCode.InternalServerError)
                    .ConfigureAwait(false);
            }
        }

        /// <summary>
        /// Handles the unexpected exception during workspace deprovisioning.
        /// </summary>
        /// <param name="exception">the exception.</param>
        /// <param name="metricsManager">the metrics manager.</param>
        /// <returns></returns>
        private async Task<JobExecutionResult> HandleDeprovisionUnexpectedException(
            Exception exception,
            IMetricsManager metricsManager)
        {
            var operationName = this.GetOperationName(Utilities.GetAsyncMethodName());
            this.Logger.LogError(exception, operationName, $"Unexpected Exception Occurred: {exception?.Message}, Inner Exception:{exception?.InnerException?.Message}, Exception StackTrace:{exception?.StackTrace}");

            ServerlessJobHelper.LogFailedMetrics(
                logger: this.Logger,
                metricsManager: metricsManager,
                operationName: operationName,
                operationType: ProviderConstants.Databricks.DeleteWorkspaceOperation,
                subscriptionId: this.Metadata.SubscriptionId,
                resourceGroupName: this.Metadata.ResourceGroupName,
                workspaceName: this.Metadata.ApplianceName,
                startDateTime: Metadata.ExecutionStartDateTime,
                apiVersion: this.Metadata.RequestCorrelationContext.ApiVersion);

            return await this.HandleWorkspaceDeprovisionFailure(
                    exception: exception,
                    statusCode: HttpStatusCode.InternalServerError,
                    customMessage: $"Unexpected Exception encountered:{exception?.Message}")
                    .ConfigureAwait(false);
        }

        /// <summary>
        /// Handles the workspace deprovisioning succeeded scenario.
        /// </summary>
        /// <param name="existingEntity">The existing eneity.</param>
        /// <returns></returns>
        private async Task<JobExecutionResult> HandleWorkspaceDeprovisionSucceeded(
            ApplianceEntity existingEntity)
        {
            var operationName = this.GetOperationName(Utilities.GetAsyncMethodName());

            await this.SetApplianceProvisioningState(
                this.Metadata.SubscriptionId,
                this.Metadata.ResourceGroupName,
                this.Metadata.ApplianceName,
                this.Metadata.ResourceType,
                ProvisioningState.Deleted)
            .ConfigureAwait(false);

            this.Logger.LogDebug(
                operationName,
                $"Deleting serverless workspace {existingEntity.GetFullyQualifiedId(this.Metadata.ResourceType)}");

            existingEntity.Properties.ProvisioningState = ProvisioningState.Deleted;
            await this
                .GetApplianceDataProvider(resourceType: this.Metadata.ResourceType)
                .DeleteAppliance(existingEntity)
                .ConfigureAwait(continueOnCapturedContext: false);

            this.Metadata.OperationResponseStatusCode = HttpStatusCode.OK;
            this.Metadata.OperationResponseContent = AsyncOperationResult
                .GetAsyncOperationResult(provisioningState: ProvisioningState.Succeeded)
                .ToJToken();

            return new JobExecutionResult
            {
                Status = JobExecutionStatus.Succeeded,
                Message = "Successfully deleted the serverless workspace.",
                Details = HttpStatusCode.OK.ToString(),
                NextMetadata = this.Metadata.ToJson()
            };
        }

        /// <summary>
        /// Handles the workspace deprovision failure scenario.
        /// </summary>
        /// <param name="exception">The exception.</param>
        /// <param name="statusCode">The status code</param>
        /// <param name="customMessage">The customm message for jobExecutionResult.</param>
        /// <returns></returns>
        private async Task<JobExecutionResult> HandleWorkspaceDeprovisionFailure(
            Exception exception,
            HttpStatusCode statusCode,
            string customMessage = null)
        {
            this.Metadata.OperationResponseStatusCode = statusCode;
            this.Metadata.OperationResponseContent = AsyncOperationResult
                .GetAsyncOperationResult(
                    provisioningState: ProvisioningState.Failed,
                    errorCode: ErrorResponseCode.ApplianceDeprovisioningFailed.ToString(),
                    message: exception.Message)
                .ToJToken();

            await this
                .SetApplianceProvisioningState(
                subscriptionId: this.Metadata.SubscriptionId,
                resourceGroupName: this.Metadata.ResourceGroupName,
                applianceName: this.Metadata.ApplianceName,
                resourceType: this.Metadata.ResourceType,
                provisioningState: ProvisioningState.Failed)
                .ConfigureAwait(false);

            return new JobExecutionResult
            {
                Status = JobExecutionStatus.Faulted,
                Message = customMessage ?? "Failed to delete serverless workspace",
                Details = exception.Message,
                NextMetadata = this.Metadata.ToJson()
            };
        }

        /// <summary>
        /// Handles the Account API polling for delete operations.
        /// </summary>
        /// <param name="existingEntity">The appliance entity.</param>
        /// <param name="applicationPackage">The application package.</param>
        /// <returns>The job execution result.</returns>
        private async Task<JobExecutionResult> HandleAccountApiPolling(
            ApplianceEntity existingEntity,
            AppliancePackage applicationPackage)
        {
            string operationName = "ServerlessWorkspaceDeprovisioningJob.HandleAccountApiPolling";

            // Use extension methods to get polling state
            var pollingState = this.Metadata.GetAccountApiPollingState();
            string workspaceId = this.Metadata.GetAccountApiOperationId();

            // If workspaceId is null, use the fully qualified ID from the existing entity
            if (string.IsNullOrEmpty(workspaceId) && existingEntity != null)
            {
                workspaceId = existingEntity.GetFullyQualifiedId(this.Metadata.ResourceType);
                this.Logger.LogDebug(operationName, $"WorkspaceId was null, using entity's fully qualified ID: '{workspaceId}'");
                this.Metadata.SetAccountApiOperationId(workspaceId);
            }

            if (string.IsNullOrEmpty(workspaceId))
            {
                this.Logger.LogError(operationName, "WorkspaceId is null and could not be determined from the entity");
                this.Metadata.SetAccountApiPollingState(AccountApiPollingState.Failed);

                return new JobExecutionResult
                {
                    Status = JobExecutionStatus.Failed,
                    Message = "WorkspaceId is null and could not be determined from the entity",
                    NextMetadata = this.Metadata.ToJson()
                };
            }

            this.Logger.LogDebug(operationName, $"Handling Account API polling for workspace '{workspaceId}'");

            string customerTenantId = RequestCorrelationContext.Current.GetHomeTenantId();
            Uri baseUri = this.Metadata.GetAccountApiBaseUri();
            string audience = this.Metadata.GetAccountApiAudience();
            string apiVersion = existingEntity.GetDatabricksBackendApiVersion(this.Logger.EventSource as ICommonEventSource);

            // If baseUri or audience is null, get them from the FrontdoorEngine
            if (baseUri == null || string.IsNullOrEmpty(audience))
            {
                this.Logger.LogDebug(
                    operationName,
                    $"Base URI or audience is null, getting them from FrontdoorEngine");

                (Uri newBaseUri, string newAudience, bool useArm) = await this.GetFrontdoorEngine()
                    .GetDatabricksAccountApiEnpointAndAudience(customerTenantId, this.Metadata.SubscriptionId)
                    .ConfigureAwait(false);

                if (baseUri == null)
                {
                    baseUri = newBaseUri;
                    this.Metadata.SetAccountApiBaseUri(baseUri);
                    this.Logger.LogDebug(
                        operationName,
                        $"Set base URI to {baseUri}");
                }

                if (string.IsNullOrEmpty(audience))
                {
                    audience = newAudience;
                    this.Metadata.SetAccountApiAudience(audience);
                    this.Logger.LogDebug(
                        operationName,
                        $"Set audience to {audience}");
                }
            }

            try
            {
                // Handle delete operation polling
                if (pollingState == AccountApiPollingState.WaitingForDeletion || pollingState == AccountApiPollingState.WaitingForCompletion)
                {
                    this.Logger.LogDebug(
                        operationName,
                        $"Polling for delete operation completion for workspace '{workspaceId}'");

                    // Check if the workspace still exists
                    DatabricksWorkspace candidate = await DatabricksAccountsManagerUtils.GetDBWorkspace<DatabricksWorkspace>(
                        this.GetFrontdoorEngine(),
                        customerTenantId,
                        workspaceId,
                        baseUri,
                        apiVersion,
                        audience,
                        false).ConfigureAwait(false);

                    if (candidate != null)
                    {
                        // Workspace still exists, continue polling
                        this.Logger.LogDebug(
                            operationName,
                            $"Workspace '{workspaceId}' delete operation is still in progress. Postponing job.");

                        return new JobExecutionResult
                        {
                            Status = JobExecutionStatus.Postponed,
                            NextExecutionTime = DateTime.UtcNow.Add(TimeSpan.FromSeconds(this.GetFrontdoorEngine().AccountApiPollingRetryInterval)),
                            Message = $"Waiting for Account API delete operation to complete for workspace '{workspaceId}'",
                            NextMetadata = this.Metadata.ToJson()
                        };
                    }

                    // If we reach here, the workspace no longer exists (delete completed successfully)
                    this.Logger.LogDebug(
                        operationName,
                        $"Workspace '{workspaceId}' delete operation completed successfully.");

                    this.Metadata.SetAccountApiPollingState(AccountApiPollingState.Completed);

                    // Continue with the rest of the deprovisioning process
                    return await this.HandleWorkspaceDeprovisionSucceeded(existingEntity).ConfigureAwait(false);
                }

                // If we reach here, it means we have an unexpected polling state
                this.Logger.LogWarning(
                    operationName,
                    $"Unexpected Account API polling state: {pollingState}. Returning failed status.");

                this.Metadata.SetAccountApiPollingState(AccountApiPollingState.Failed);

                return new JobExecutionResult
                {
                    Status = JobExecutionStatus.Failed,
                    Message = $"Unexpected Account API polling state: {pollingState}",
                    NextMetadata = this.Metadata.ToJson()
                };
            }
            catch (Exception ex)
            {
                // For non-transient errors, mark as failed
                this.Metadata.SetAccountApiPollingState(AccountApiPollingState.Failed);

                this.Metadata.OperationResponseStatusCode = HttpStatusCode.OK;
                this.Metadata.OperationResponseContent = AsyncOperationResult
                    .GetAsyncOperationResult(
                        ProvisioningState.Failed,
                        ErrorResponseCode.WorkspaceDeleteFailed.ToString(),
                        $"Error occurred while polling for Account API operation for workspace '{workspaceId}': {ex.Message}")
                    .ToJToken();

                return new JobExecutionResult
                {
                    Status = JobExecutionStatus.Failed,
                    Message = $"Error occurred while polling for Account API operation for workspace '{workspaceId}': {ex.Message}",
                    NextMetadata = this.Metadata.ToJson()
                };
            }
        }
    }
}