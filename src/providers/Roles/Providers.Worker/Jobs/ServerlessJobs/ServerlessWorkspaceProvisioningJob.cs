//-----------------------------------------------------------
// Copyright (c) Microsoft Corporation.  All rights reserved.
//-----------------------------------------------------------

namespace Microsoft.WindowsAzure.ResourceStack.Providers.Worker.Jobs
{
    using System;
    using System.Linq;
    using System.Net;
    using System.Net.Http;
    using System.Threading.Tasks;
    using Microsoft.WindowsAzure.ResourceStack.Common.BackgroundJobs;
    using Microsoft.WindowsAzure.ResourceStack.Common.EventSources;
    using Microsoft.WindowsAzure.ResourceStack.Common.Extensions;
    using Microsoft.WindowsAzure.ResourceStack.Common.Instrumentation;
    using Microsoft.WindowsAzure.ResourceStack.Common.Json;
    using Microsoft.WindowsAzure.ResourceStack.Common.Services;
    using Microsoft.WindowsAzure.ResourceStack.Common.Storage;
    using Microsoft.WindowsAzure.ResourceStack.Common.Utilities;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Application.Data.Components;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Application.Data.DataProviders;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Application.Data.Definitions.DBAsyncContract;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Application.Data.Definitions.DBCSContract;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Application.Data.Definitions.Enums;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Application.Data.Entities;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Application.Data.Extensions;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Application.Data.Metadata;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Common.Constants;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Common.Data;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Common.Engines;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Common.ErrorResponses;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Common.Extensions;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Common.Metrics;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Common.Responses;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Common.Utilities;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Worker.Configuration;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Worker.Jobs.ServerlessJobs;
    using Newtonsoft.Json;
    using Newtonsoft.Json.Linq;

    /// <summary>
    /// The serverless workspace provisioning job.
    /// </summary>
    [JobCallback(Name = ProviderConstants.Databricks.ServerlessWorkspaceProvisioningJob)]
    public class ServerlessWorkspaceProvisioningJob : ApplianceJob<ApplianceProvisioningJobMetadata>
    {
        #region Fields and Properties
        /// <summary>
        /// The frontdoor engine.
        /// </summary>
        private readonly IFrontdoorEngine FrontdoorEngine;

        /// <summary>
        /// The appliance data provider.
        /// </summary>
        protected readonly IApplianceDataProvider ApplianceDataProvider;

        /// <summary>
        /// The marketplace package data provider
        /// </summary>
        protected readonly IApplicationPackageDataProvider MarketPlacePackageDataProvider;

        /// <summary>
        /// Maximum number of retries allowed.
        /// </summary>
        public int MaxRetries =>
            CloudConfigurationManager.GetConfigurationNumber(
                "Microsoft.WindowsAzure.ResourceStack.Providers.ApplianceProvisioningJob.MaxRetries",
                3);

        /// <summary>
        /// Gets the resource deletion job postpone interval.
        /// </summary>
        protected override TimeSpan PostponeInterval
        {
            get
            {
                return CloudConfigurationManager.GetConfigurationTimeSpan(
                    settingName: "Microsoft.WindowsAzure.ResourceStack.Providers.ApplianceProvisioningJob.PostponeInterval",
                    defaultValue: TimeSpan.FromSeconds(20));
            }
        }
        #endregion

        #region Constructor

        /// <summary>
        /// Initializes a new instance of the <see cref="ServerlessWorkspaceProvisioningJob" /> class.
        /// </summary>
        /// <param name="jobConfiguration">The job configuration.</param>
        public ServerlessWorkspaceProvisioningJob(ProvidersJobConfiguration jobConfiguration)
            : base(jobConfiguration)
        {
            this.FrontdoorEngine = this.ProvidersJobConfiguration.FrontdoorEngine;
            this.ApplianceDataProvider = this.ProvidersJobConfiguration.ApplicationDataProvidersContainer.ApplianceDataProvider;
            this.MarketPlacePackageDataProvider = this.ProvidersJobConfiguration.ApplicationDataProvidersContainer.MarketPlacePackageDataProvider;
        }

        #endregion

        /// <summary>
        /// Handle when job maximum life time has exceeded.
        /// </summary>
        protected override async Task OnJobMaxLifetimeExceeded()
        {
            var operationName = this.GetOperationName(Utilities.GetAsyncMethodName());
            var message = string.Format(
                format: "The '{0}' creation job did not complete within the allowed timeout period '{1}'.",
                arg0: this.Metadata.ResourceType,
                arg1: this.Metadata.ProvidersJobMaxLifetime);

            this.Logger.LogError(operationName, message);

            this.SetFailedOperationResponse(
                errorCode: ErrorResponseCode.ApplianceProvisioningTimeout.ToString(),
                errorMessage: message);

            await this.SetProvisioningStateToFailed();

            throw new JobExecutionResultException(status: JobExecutionStatus.Faulted, message: message, nextMetadata: this.Metadata.ToJson());
        }

        /// <summary>
        /// Executes the job asynchronously.
        /// </summary>
        protected override async Task<JobExecutionResult> OnJobExecute()
        {
            var metricsManager = MetricsManagerFactory.GetInstance(this.Logger.EventSource as ICommonEventSource);
            var operationName = this.GetOperationName(Utilities.GetAsyncMethodName());

            var applianceOperationType = ProviderConstants.Databricks.CreateWorkspaceOperation;
            if (this.Metadata.ApplianceOperationType == ProvisioningOperation.Updating)
            {
                applianceOperationType = ProviderConstants.Databricks.UpdateWorkspaceOperation;
            }

            try
            {
                this.Logger.LogDebug(operationName, $"{DateTime.UtcNow}::OnJobExecute method entered");

                // Check if we're in account API polling state
                if (this.Metadata.ResourceOperation == ProvisioningOperation.AccountApiPolling)
                {
                    this.Logger.LogDebug(
                        operationName,
                        $"Job is in Account API polling state. Handling polling.");

                    return await HandleAccountApiPolling().ConfigureAwait(false);
                }

                var existingEntity = await GetWorkspaceEntity()
                    .ConfigureAwait(continueOnCapturedContext: false);

                if (existingEntity.Properties.ProvisioningState != ProvisioningState.Accepted &&
                    existingEntity.Properties.ProvisioningState != ProvisioningState.Updating)
                {
                    return await this.HandleInvalidStateError(
                        resourceType: this.Metadata.ResourceType,
                        resourceName: this.Metadata.ApplianceName,
                        currentState: existingEntity.Properties.ProvisioningState);
                }

                JobExecutionResult jobExecutionResult;

                #region Shared Variables

                MarketplaceAppliancePackage appliancePackage = this.MarketPlacePackageDataProvider
                    .FindMarketplaceAppliancePackage(existingEntity.Properties.PublisherPackageId);
                string customerTenantId = RequestCorrelationContext.Current.GetHomeTenantId();
                string publisherTenantId = appliancePackage.TenantId;
                string workspaceResourceId = existingEntity.GetFullyQualifiedResourceId();

                #endregion

                if (this.Metadata.ResourceOperation == ProvisioningOperation.Create)
                {
                    jobExecutionResult = await ProcessCreateOperation(
                        existingEntity,
                        customerTenantId,
                        workspaceResourceId,
                        publisherTenantId);
                }
                else if (this.Metadata.ResourceOperation == ProvisioningOperation.Updating)
                {
                    jobExecutionResult = await ProcessUpdateOperation(
                        existingEntity,
                        appliancePackage,
                        workspaceResourceId,
                        customerTenantId);
                }
                else
                {
                    jobExecutionResult = await this.HandleUnknownOperationError();
                }

                this.Logger.LogDebug(operationName, $"Job Execution Status: '{jobExecutionResult.Status}'. Job Execution Results: {jobExecutionResult.ToJson()}");

                ApplianceProvisioningJobMetadata metadata = JsonConvert.DeserializeObject<ApplianceProvisioningJobMetadata>(jobExecutionResult.NextMetadata);

                this.Logger.LogDebug(operationName, $"SLO:: Metadata Job Start Time: {metadata.ExecutionStartDateTime}");

                if (jobExecutionResult.Status == JobExecutionStatus.Faulted)
                {
                    var sliErrorDetails = SLIErrorInfo.ConvertToSLIErrorInfo(this.Metadata.SLIErrorInfo, this.Metadata);

                    ServerlessJobHelper.LogFailedMetrics(
                        logger: this.Logger,
                        metricsManager: metricsManager,
                        operationName: operationName,
                        operationType: applianceOperationType,
                        subscriptionId: this.Metadata.SubscriptionId,
                        resourceGroupName: this.Metadata.ResourceGroupName,
                        workspaceName: this.Metadata.ApplianceName,
                        startDateTime: this.Metadata.ExecutionStartDateTime,
                        apiVersion: metadata.RequestCorrelationContext.ApiVersion,
                        responsibleParty: sliErrorDetails.ResponsibleParty,
                        failureCategory: sliErrorDetails.FailureCategory);

                    return jobExecutionResult;
                }

                ServerlessJobHelper.LogSuccessMetrics(
                            logger: this.Logger,
                            metricsManager: metricsManager,
                            operationName: operationName,
                            operationType: applianceOperationType,
                            subscriptionId: this.Metadata.SubscriptionId,
                            resourceGroupName: this.Metadata.ResourceGroupName,
                            workspaceName: this.Metadata.ApplianceName,
                            startDateTime: this.Metadata.ExecutionStartDateTime,
                            apiVersion: metadata.RequestCorrelationContext.ApiVersion);

                this.Logger.LogDebug(operationName, $"{DateTime.UtcNow}::OnJobExecute method exited");

                return jobExecutionResult;
            }
            catch (Exception exception)
            {
                this.Logger.LogError(exception, operationName, $"Unexpected Exception Occurred: {exception?.Message}, Inner Exception:{exception?.InnerException?.Message}, Exception StackTrace:{exception?.StackTrace}");

                ServerlessJobHelper.LogFailedMetrics(
                    logger: this.Logger,
                    metricsManager: metricsManager,
                    operationName: operationName,
                    operationType: applianceOperationType,
                    subscriptionId: this.Metadata.SubscriptionId,
                    resourceGroupName: this.Metadata.ResourceGroupName,
                    workspaceName: this.Metadata.ApplianceName,
                    startDateTime: this.Metadata.ExecutionStartDateTime,
                    apiVersion: this.Metadata.RequestCorrelationContext.ApiVersion);

                await this
                    .SetApplianceProvisioningState(
                        subscriptionId: this.Metadata.SubscriptionId,
                        resourceGroupName: this.Metadata.ResourceGroupName,
                        applianceName: this.Metadata.ApplianceName,
                        resourceType: this.Metadata.ResourceType,
                        provisioningState: ProvisioningState.Failed)
                    .ConfigureAwait(continueOnCapturedContext: false);

                return new JobExecutionResult
                {
                    Status = JobExecutionStatus.Faulted,
                    NextMetadata = this.Metadata.ToJson(),
                    Message = $"Unexpected Exception encountered:{exception?.Message}"
                };
            }
        }

        #region Operation Processing

        /// <summary>
        /// Processes the create operation for the serverless workspace provisioning job.
        /// </summary>
        /// <param name="existingEntity"></param>
        /// <param name="customerTenantId"></param>
        /// <param name="workspaceResourceId"></param>
        /// <param name="publisherTenantId"></param>
        /// <returns></returns>
        private async Task<JobExecutionResult> ProcessCreateOperation(
            ApplianceEntity existingEntity,
            string customerTenantId,
            string workspaceResourceId,
            string publisherTenantId)
        {
            var operationName = this.GetOperationName(Utilities.GetAsyncMethodName());

            try
            {
                var registerManagedTenantApiVersion = await this.GetManagedByTenantApiVersion(existingEntity.SubscriptionId).ConfigureAwait(false);

                this.Logger.LogDebug(
                    operationName,
                    $"Calling RegisterSubscriptionWithManagedTenant API version - {registerManagedTenantApiVersion} to register ManagedByTenant " +
                    $"for the resource : '{workspaceResourceId}'");

                bool isStagingEnv = await this.ApplianceEngine.IsStagingEnvironment(existingEntity);
                string applianceResourceId = existingEntity.GetFullyQualifiedId(this.Metadata.ResourceType);

                if (!isStagingEnv)
                {
                    await this.FrontdoorEngine
                    .RegisterSubscriptionWithManagedTenant(
                        customerTenantId,
                        existingEntity.SubscriptionId,
                        publisherTenantId,
                        this.Metadata.ResourceProviderNamespace,
                        registerManagedTenantApiVersion,
                        applianceResourceId)
                    .ConfigureAwait(continueOnCapturedContext: false);
                }
            }
            catch (ServerErrorResponseMessageException exception)
            {
                if ((exception.HttpStatus.IsRetryableResponse() ||
                     exception.HttpStatus == HttpStatusCode.Forbidden ||
                     exception.HttpStatus == HttpStatusCode.Unauthorized) &&
                    (exception is ResourceGroupOperationException ||
                     exception is AuthorizationOperationException) &&
                     this.Metadata.CurrentRetryableErrorCount < this.MaxRetries)
                {
                    this.Logger.LogDebug(
                        operationName,
                        $"Postponing provisioning job for the workspace '{existingEntity.GetFullyQualifiedId(this.Metadata.ResourceType)}' because of a retry-able. Current retry count: {this.Metadata.CurrentRetryableErrorCount}, Max retries: {this.MaxRetries}");

                    this.Metadata.CurrentRetryableErrorCount++;

                    return this.PostponeJob(
                        ErrorResponseMessages.InternalServerError.ToLocalizedMessage(RequestCorrelationContext.Current
                            .CorrelationId),
                        TimeSpan.FromTicks(this.PostponeInterval.Ticks * this.Metadata.CurrentRetryableErrorCount));
                }

                this.Logger.LogDebug(
                    operationName,
                    $"Failed to provision the workspace '{existingEntity.GetFullyQualifiedId(this.Metadata.ResourceType)}'. Current retry count: {this.Metadata.CurrentRetryableErrorCount}, Max retries: {this.MaxRetries}");

                return await this.HandleWorkspaceProvisionFailure(
                    errorCode: ErrorResponseCode.ApplianceProvisioningFailed.ToString(),
                    errorMessage: exception.Message,
                    customMessage: $"Failed to provision the workspace '{existingEntity.GetFullyQualifiedId(this.Metadata.ResourceType)}'. Faulting job",
                    details: exception.Message)
                    .ConfigureAwait(false);
            }
            catch (Exception exception)
            {
                if (exception.IsTimeoutException() && this.Metadata.CurrentRetryableErrorCount < this.MaxRetries)
                {
                    this.Metadata.CurrentRetryableErrorCount++;
                    return this.PostponeJob(
                        $"workspace with id '{existingEntity.GetFullyQualifiedId(this.Metadata.ResourceType)}' failed with timeout exception. Retrying.",
                        TimeSpan.FromTicks(this.PostponeInterval.Ticks * this.Metadata.CurrentRetryableErrorCount));
                }

                this.Logger.LogError(operationName, $"Failed to provision the workspace. Faulted with exception: '{exception}'");

                throw;
            }

            try
            {
                this.Logger.LogDebug(
                    operationName,
                    $"Call Databricks to provision serverless workspace with Id '{workspaceResourceId}', in ProvisioningState '{existingEntity.Properties.ProvisioningState}'");

                var response = await this.ApplianceEngine
                    .InitializeWorkspace(
                        existingEntity,
                        customerTenantId,
                        this.Metadata.ResourceType,
                        this.Metadata.ResourceProviderNamespace,
                        existingEntity.GetDatabricksBackendApiVersion(this.Logger.EventSource as ICommonEventSource))
                    .ConfigureAwait(false);

                // Check if the response indicates we should use job-based polling
                if (response.RequiresPolling)
                {
                    this.Logger.LogDebug(
                        operationName,
                        $"Workspace initialization requires polling. Setting up job-based polling.");

                    // Get the base URI and audience for account API
                    string apiVersion = existingEntity.GetDatabricksBackendApiVersion(this.Logger.EventSource as ICommonEventSource);
                    (Uri baseUri, string audience, bool useArm) = await this.FrontdoorEngine
                        .GetDatabricksAccountApiEnpointAndAudience(customerTenantId, this.Metadata.SubscriptionId);

                    if (!useArm)
                    {
                        // Set up metadata for job-based polling
                        this.Logger.LogDebug(
                            operationName,
                            $"Setting up job-based polling for Account API initialization operation on workspace '{workspaceResourceId}'");

                        // Set the polling state based on the operation
                        this.Metadata.SetAccountApiPollingState(AccountApiPollingState.WaitingForInitialization);
                        this.Metadata.SetAccountApiOperationId(workspaceResourceId);
                        this.Metadata.SetAccountApiOperationStartTime(DateTime.UtcNow);
                        this.Metadata.SetAccountApiBaseUri(baseUri);
                        this.Metadata.SetAccountApiAudience(audience);
                        this.Metadata.ResourceOperation = ProvisioningOperation.AccountApiPolling;

                        return new JobExecutionResult
                        {
                            Status = JobExecutionStatus.Postponed,
                            NextExecutionTime = DateTime.UtcNow.Add(TimeSpan.FromSeconds(this.FrontdoorEngine.AccountApiPollingRetryInterval)),
                            Message = $"Waiting for Account API initialization operation to complete for workspace '{workspaceResourceId}'",
                            NextMetadata = this.Metadata.ToJson()
                        };
                    }
                }

                if (response.Status != OperationStatus.Success || response.WorkspaceDetails == null)
                {
                    return await HandleWorkspaceInitFailure(response, workspaceResourceId);
                }

                existingEntity.Properties.WorkspaceUrl = response.WorkspaceDetails.WorkspaceURL;
                existingEntity.Properties.WorkspaceId = response.WorkspaceDetails.WorkspaceId;
                existingEntity.Properties.IsUcEnabled = response.WorkspaceDetails.IsUcEnabled;

                this.Logger.LogDebug(
                    operationName,
                    $"Databricks returned serverless workspace with WorkspaceUrl '{response.WorkspaceDetails.WorkspaceURL}', in ProvisioningState '{existingEntity.Properties.ProvisioningState}'");

                existingEntity.Properties.ProvisioningState = ProvisioningState.Succeeded;

                this.Metadata.OperationResponseStatusCode = HttpStatusCode.OK;
                this.Metadata.OperationResponseContent = AsyncOperationResult
                    .GetAsyncOperationResult(ProvisioningState.Succeeded)
                    .ToJToken();

                await this.ApplianceDataProvider.
                    SaveAppliance(existingEntity)
                    .ConfigureAwait(false);

                var message = string.Format("Successfully provisioned and initialized the workspace appliance resource '{0}'. Completing job", workspaceResourceId);
                this.Logger.LogDebug(operationName, message);

                return new JobExecutionResult
                {
                    Status = JobExecutionStatus.Succeeded,
                    Message = message,
                    Details = HttpStatusCode.OK.ToString(),
                    NextMetadata = this.Metadata.ToJson()
                };
            }
            catch (Exception ex)
            {
                return await this.HandleWorkspaceProvisionFailure(
                    errorCode: ErrorResponseCode.ApplianceProvisioningFailed.ToString(),
                    errorMessage: ex.Message,
                    customMessage: $"Failed to provision the serverless workspace '{workspaceResourceId}'. Faulting job",
                    details: ex.Message)
                    .ConfigureAwait(false);
            }
        }

        /// <summary>
        /// Processes the update operation for the serverless workspace provisioning job.
        /// </summary>
        /// <param name="existingEntity"></param>
        /// <param name="appliancePackage"></param>
        /// <param name="workspaceResourceId"></param>
        /// <param name="customerTenantId"></param>
        /// <returns></returns>
        private async Task<JobExecutionResult> ProcessUpdateOperation(
            ApplianceEntity existingEntity,
            MarketplaceAppliancePackage appliancePackage,
            string workspaceResourceId,
            string customerTenantId)
        {
            var operationName = this.GetOperationName(Utilities.GetAsyncMethodName());

            this.Logger.LogDebug(
                operationName,
                $"Update existing serverless workspace with Id '{workspaceResourceId}' in ProvisioningState '{existingEntity.Properties.ProvisioningState}'");

            try
            {
                var databricksBackendApiVersion = existingEntity.GetDatabricksBackendApiVersion(this.Logger.EventSource as ICommonEventSource);

                await this.SendDBWorkspaceNotification(
                                existingEntity: existingEntity,
                                workspaceResourceId: workspaceResourceId,
                                apiVersion: databricksBackendApiVersion,
                                publisherTenantId: customerTenantId);

                // Check if we've been set up for account API polling
                if (this.Metadata.ResourceOperation == ProvisioningOperation.AccountApiPolling)
                {
                    this.Logger.LogDebug(
                        operationName,
                        $"Job has been set up for Account API polling. Postponing job execution.");

                    return new JobExecutionResult
                    {
                        Status = JobExecutionStatus.Postponed,
                        NextExecutionTime = DateTime.UtcNow.Add(TimeSpan.FromSeconds(this.FrontdoorEngine.AccountApiPollingRetryInterval)),
                        Message = $"Waiting for Account API update operation to complete for workspace '{workspaceResourceId}'",
                        NextMetadata = this.Metadata.ToJson()
                    };
                }

                this.PopulatePropertiesFromAppliancePackage(existingEntity, appliancePackage);

                this.Logger.LogDebug(
                    operationName,
                    $"Completed Notification of serverless workspace update for '{workspaceResourceId}' to downstream, Api-version: {RequestCorrelationContext.Current.ApiVersion}");

                this.Logger.LogDebug(operationName, $"The serverless workspace '{existingEntity.GetFullyQualifiedResourceId()}' update completed.");

                this.Metadata.OperationResponseStatusCode = HttpStatusCode.OK;
                this.Metadata.OperationResponseContent = AsyncOperationResult
                    .GetAsyncOperationResult(ProvisioningState.Succeeded)
                    .ToJToken();

                await this.GetApplianceDataProvider(this.Metadata.ResourceType)
                    .SetApplianceProvisioningState(
                        existingEntity.SubscriptionId,
                        existingEntity.ResourceGroup,
                        existingEntity.Name,
                        ProvisioningState.Succeeded)
                    .ConfigureAwait(continueOnCapturedContext: false);

                return new JobExecutionResult
                {
                    Status = JobExecutionStatus.Succeeded,
                    Message = $"Successfully provisioned and updated the serverless workspace '{existingEntity.GetFullyQualifiedResourceId()}'. Completing job",
                    Details = HttpStatusCode.OK.ToString(),
                    NextMetadata = this.Metadata.ToJson()
                };
            }
            catch (Exception ex)
            {
                var defaultErrorMessage = ErrorResponseMessages.WorkspaceUpdateFailed.ToLocalizedMessage(
                    existingEntity.GetFullyQualifiedResourceId(),
                    ex.Message);

                if (ex is ServerErrorResponseMessageException exception)
                {
                    if (exception.ErrorCode.Equals(ErrorResponseCode.PermissionDenied.ToString(),
                            StringComparison.InvariantCultureIgnoreCase))
                    {
                        this.Logger.LogDebug(
                            operationName,
                            $"Failed to Notify serverless workspace update for '{workspaceResourceId}' to RPBackend. Failure : {ex}");

                        // Set provisioning state back to succeeded. Since any of the above Update failure will not lead to workspace failed provisioning state.
                        // Update workflow only occurs if the workspace is in success state
                        await this.GetApplianceDataProvider(this.Metadata.ResourceType)
                            .SetApplianceProvisioningState(existingEntity.SubscriptionId,
                                existingEntity.ResourceGroup,
                                existingEntity.Name,
                                ProvisioningState.Succeeded)
                            .ConfigureAwait(continueOnCapturedContext: false);

                        return this.HandleFailedJobExecutionResult(
                            existingEntity.GetFullyQualifiedResourceId(),
                            exception.ErrorCode,
                            ex);
                    }
                }

                var failedJobExecutionResult = this.HandleFailedJobExecutionResult(
                    existingEntity.GetFullyQualifiedResourceId(),
                    ErrorResponseCode.WorkspaceUpdateFailed.ToString(),
                    ex);

                if (failedJobExecutionResult.Status == JobExecutionStatus.Faulted && existingEntity.Properties != null)
                {
                    await this.ApplianceDataProvider.SetApplianceProvisioningState(
                            existingEntity.SubscriptionId,
                            existingEntity.ResourceGroup,
                            existingEntity.Name,
                            ProvisioningState.Failed)
                        .ConfigureAwait(continueOnCapturedContext: false);
                }

                this.Logger.LogError(
                    operationName,
                    format: "Updating resource type '{0}' with id '{1}' failed with deployment operation.",
                    arg0: this.Metadata.ResourceType,
                    arg1: existingEntity.GetFullyQualifiedId(this.Metadata.ResourceType),
                    exception: ex);

                return failedJobExecutionResult;
            }
        }

        #endregion

        #region Databricks Notification Helper Methods
        private async Task SendDBWorkspaceNotification(
            ApplianceEntity existingEntity,
            string workspaceResourceId,
            string apiVersion,
            string publisherTenantId)
        {
            var operationName = this.GetOperationName(Utilities.GetAsyncMethodName());

            var serverlessDBNotification = existingEntity.ToDbWorkspace();

            if (serverlessDBNotification.Update.Tags != null ||
                serverlessDBNotification.Update.Properties.Encryption != null ||
                serverlessDBNotification.Update.Properties.EnhancedSecurityCompliance != null)
            {
                this.Logger.LogDebug(
                    operationName,
                    $"Call Databricks to PATCH serverless workspace with Id '{workspaceResourceId}', in ProvisioningState '{existingEntity.Properties.ProvisioningState}'");

                // Get the base URI and audience for account API
                (Uri baseUri, string audience, bool useArm) = await this.FrontdoorEngine
                    .GetDatabricksAccountApiEnpointAndAudience(publisherTenantId, this.Metadata.SubscriptionId);

                var payload = useArm ?
                    JToken.FromObject(serverlessDBNotification) :
                    JToken.FromObject(DatabricksWorkspace.FromApplianceEntity(existingEntity));

                this.Logger.LogDebug(
                    operationName,
                    $"[Feature Flag selection]START: DB Workspace {(useArm ? "PATCH" : "PUT")} Notification on WorkspaceId: '{workspaceResourceId}'" +
                    $"Using {(useArm ? "ARM" : "AccountApi")}");

                await DatabricksAccountsManagerUtils.SendDBNotificationWithFlagControl(
                    this.FrontdoorEngine,
                    payload,
                    useArm ? new HttpMethod("PATCH") : new HttpMethod("PUT"),
                    publisherTenantId,
                    useArm,
                    workspaceResourceId,
                    baseUri,
                    apiVersion,
                    audience).ConfigureAwait(false);

                if (!useArm)
                {
                    // Set up metadata for job-based polling
                    this.Logger.LogDebug(
                        operationName,
                        $"Setting up job-based polling for Account API operation on workspace '{workspaceResourceId}'");

                    // Set the polling state based on the operation
                    this.Metadata.SetAccountApiPollingState(AccountApiPollingState.WaitingForUpdate);
                    this.Metadata.SetAccountApiOperationId(workspaceResourceId);
                    this.Metadata.SetAccountApiOperationStartTime(DateTime.UtcNow);
                    this.Metadata.SetAccountApiBaseUri(baseUri);
                    this.Metadata.SetAccountApiAudience(audience);
                    this.Metadata.ResourceOperation = ProvisioningOperation.AccountApiPolling;

                    // Return a postponed result to allow polling
                    return;
                }
            }
        }



        /// <summary>
        /// Handles the Account API polling for workspace operations.
        /// </summary>
        /// <returns>The job execution result.</returns>
        private async Task<JobExecutionResult> HandleAccountApiPolling()
        {
            string operationName = "ServerlessWorkspaceProvisioningJob.HandleAccountApiPolling";

            // Use extension methods to get polling state
            var pollingState = this.Metadata.GetAccountApiPollingState();
            string workspaceId = this.Metadata.GetAccountApiOperationId();
            DateTime? operationStartTime = this.Metadata.GetAccountApiOperationStartTime();
            Uri baseUri = this.Metadata.GetAccountApiBaseUri();
            string audience = this.Metadata.GetAccountApiAudience();
            string customerTenantId = RequestCorrelationContext.Current.GetHomeTenantId();
            string apiVersion = this.Metadata.RequestCorrelationContext.ApiVersion;

            this.Logger.LogDebug(
                operationName,
                $"Handling Account API polling for workspace '{workspaceId}' in state '{pollingState}'");

            try
            {
                // Handle update operation polling
                if (pollingState == AccountApiPollingState.WaitingForUpdate || pollingState == AccountApiPollingState.WaitingForCompletion)
                {
                    this.Logger.LogDebug(
                        operationName,
                        $"Polling for update operation completion for workspace '{workspaceId}'");

                    // Make a single call to check operation status
                    DatabricksWorkspace candidate = await DatabricksAccountsManagerUtils.GetDBWorkspace<DatabricksWorkspace>(
                        this.FrontdoorEngine,
                        customerTenantId,
                        workspaceId,
                        baseUri,
                        apiVersion,
                        audience,
                        false).ConfigureAwait(false);

                    // Check if the operation is still running
                    if (candidate.Properties.OperationStatus.OperationStatus == DBOperationStatus.Running)
                    {
                        // Still updating, postpone again
                        this.Logger.LogDebug(
                            operationName,
                            $"Workspace '{workspaceId}' update is still running. Postponing job.");

                        return new JobExecutionResult
                        {
                            Status = JobExecutionStatus.Postponed,
                            NextExecutionTime = DateTime.UtcNow.Add(TimeSpan.FromSeconds(this.FrontdoorEngine.AccountApiPollingRetryInterval)),
                            Message = $"Waiting for Account API update operation to complete for workspace '{workspaceId}'",
                            NextMetadata = this.Metadata.ToJson()
                        };
                    }
                    else if (candidate.Properties.OperationStatus.OperationStatus == DBOperationStatus.Succeeded)
                    {
                        this.Logger.LogDebug(
                            operationName,
                            $"Workspace '{workspaceId}' update operation completed successfully.");

                        // Update the entity with the latest information
                        var existingEntity = await GetWorkspaceEntity()
                            .ConfigureAwait(continueOnCapturedContext: false);

                        existingEntity.Properties.ProvisioningState = ProvisioningState.Succeeded;

                        // Save the updated entity
                        await this.ApplianceDataProvider.
                            SaveAppliance(existingEntity)
                            .ConfigureAwait(false);

                        this.Metadata.OperationResponseStatusCode = HttpStatusCode.OK;
                        this.Metadata.OperationResponseContent = AsyncOperationResult
                            .GetAsyncOperationResult(ProvisioningState.Succeeded)
                            .ToJToken();

                        // Set the polling state to completed
                        this.Metadata.SetAccountApiPollingState(AccountApiPollingState.Completed);

                        return new JobExecutionResult
                        {
                            Status = JobExecutionStatus.Succeeded,
                            Message = $"Successfully completed Account API update operation for workspace '{workspaceId}'.",
                            Details = HttpStatusCode.OK.ToString(),
                            NextMetadata = this.Metadata.ToJson()
                        };
                    }
                    else if (candidate.Properties.OperationStatus.OperationStatus == DBOperationStatus.Failed)
                    {
                        this.Logger.LogError(
                            operationName,
                            $"Workspace '{workspaceId}' update operation failed.");

                        // Set the polling state to failed
                        this.Metadata.SetAccountApiPollingState(AccountApiPollingState.Failed);

                        return await this.HandleWorkspaceProvisionFailure(
                            errorCode: ErrorResponseCode.WorkspaceUpdateFailed.ToString(),
                            errorMessage: $"Workspace '{workspaceId}' update operation failed.",
                            customMessage: $"Failed to update workspace '{workspaceId}'.",
                            details: null)
                            .ConfigureAwait(false);
                    }
                    else
                    {
                        // Unknown operation status
                        this.Logger.LogError(
                            operationName,
                            $"Workspace '{workspaceId}' has unknown operation status: {candidate.Properties.OperationStatus.OperationStatus}.");

                        // Set the polling state to failed
                        this.Metadata.SetAccountApiPollingState(AccountApiPollingState.Failed);

                        return await this.HandleWorkspaceProvisionFailure(
                            errorCode: ErrorResponseCode.WorkspaceUpdateFailed.ToString(),
                            errorMessage: $"Workspace '{workspaceId}' has unknown operation status: {candidate.Properties.OperationStatus.OperationStatus}.",
                            customMessage: $"Failed to update workspace '{workspaceId}' due to unknown operation status.",
                            details: null)
                            .ConfigureAwait(false);
                    }
                }
                else if (pollingState == AccountApiPollingState.WaitingForInitialization)
                {
                    // Similar logic for initialization polling
                    this.Logger.LogDebug(
                        operationName,
                        $"Polling for initialization operation completion for workspace '{workspaceId}'");

                    // Make a single call to check operation status
                    DatabricksWorkspace candidate = await DatabricksAccountsManagerUtils.GetDBWorkspace<DatabricksWorkspace>(
                        this.FrontdoorEngine,
                        customerTenantId,
                        workspaceId,
                        baseUri,
                        apiVersion,
                        audience,
                        false).ConfigureAwait(false);

                    // Check if the operation is still running
                    if (candidate.Properties.OperationStatus.OperationStatus == DBOperationStatus.Running)
                    {
                        // Still initializing, postpone again
                        this.Logger.LogDebug(
                            operationName,
                            $"Workspace '{workspaceId}' initialization is still running. Postponing job.");

                        return new JobExecutionResult
                        {
                            Status = JobExecutionStatus.Postponed,
                            NextExecutionTime = DateTime.UtcNow.Add(TimeSpan.FromSeconds(this.FrontdoorEngine.AccountApiPollingRetryInterval)),
                            Message = $"Waiting for Account API initialization operation to complete for workspace '{workspaceId}'",
                            NextMetadata = this.Metadata.ToJson()
                        };
                    }
                    else if (candidate.Properties.OperationStatus.OperationStatus == DBOperationStatus.Succeeded)
                    {
                        this.Logger.LogDebug(
                            operationName,
                            $"Workspace '{workspaceId}' initialization operation completed successfully.");

                        // Update the entity with the latest information
                        var existingEntity = await GetWorkspaceEntity()
                            .ConfigureAwait(continueOnCapturedContext: false);

                        existingEntity.Properties.ProvisioningState = ProvisioningState.Succeeded;
                        existingEntity.Properties.WorkspaceUrl = candidate.Properties.WorkspaceUrl;
                        existingEntity.Properties.WorkspaceId = candidate.Properties.WorkspaceId;

                        // Save the updated entity
                        await this.ApplianceDataProvider.
                            SaveAppliance(existingEntity)
                            .ConfigureAwait(false);

                        this.Metadata.OperationResponseStatusCode = HttpStatusCode.OK;
                        this.Metadata.OperationResponseContent = AsyncOperationResult
                            .GetAsyncOperationResult(ProvisioningState.Succeeded)
                            .ToJToken();

                        // Set the polling state to completed
                        this.Metadata.SetAccountApiPollingState(AccountApiPollingState.Completed);

                        return new JobExecutionResult
                        {
                            Status = JobExecutionStatus.Succeeded,
                            Message = $"Successfully completed Account API initialization operation for workspace '{workspaceId}'.",
                            Details = HttpStatusCode.OK.ToString(),
                            NextMetadata = this.Metadata.ToJson()
                        };
                    }
                    else if (candidate.Properties.OperationStatus.OperationStatus == DBOperationStatus.Failed)
                    {
                        this.Logger.LogError(
                            operationName,
                            $"Workspace '{workspaceId}' initialization operation failed.");

                        // Set the polling state to failed
                        this.Metadata.SetAccountApiPollingState(AccountApiPollingState.Failed);

                        return await this.HandleWorkspaceProvisionFailure(
                            errorCode: ErrorResponseCode.ErrorInitializingWorkspace.ToString(),
                            errorMessage: $"Workspace '{workspaceId}' initialization operation failed.",
                            customMessage: $"Failed to initialize workspace '{workspaceId}'.",
                            details: null)
                            .ConfigureAwait(false);
                    }
                    else
                    {
                        // Unknown operation status
                        this.Logger.LogError(
                            operationName,
                            $"Workspace '{workspaceId}' has unknown operation status: {candidate.Properties.OperationStatus.OperationStatus}.");

                        // Set the polling state to failed
                        this.Metadata.SetAccountApiPollingState(AccountApiPollingState.Failed);

                        return await this.HandleWorkspaceProvisionFailure(
                            errorCode: ErrorResponseCode.ErrorInitializingWorkspace.ToString(),
                            errorMessage: $"Workspace '{workspaceId}' has unknown operation status: {candidate.Properties.OperationStatus.OperationStatus}.",
                            customMessage: $"Failed to initialize workspace '{workspaceId}' due to unknown operation status.",
                            details: null)
                            .ConfigureAwait(false);
                    }
                }
                else
                {
                    // Handle unexpected polling state
                    this.Logger.LogError(
                        operationName,
                        $"Unexpected polling state '{pollingState}' for workspace '{workspaceId}'.");

                    return await this.HandleWorkspaceProvisionFailure(
                        errorCode: ErrorResponseCode.ApplianceProvisioningFailed.ToString(),
                        errorMessage: $"Unexpected polling state '{pollingState}' for workspace '{workspaceId}'.",
                        customMessage: $"Failed to poll for workspace operation due to unexpected polling state.",
                        details: null)
                        .ConfigureAwait(false);
                }
            }
            catch (Exception ex)
            {
                // Handle exceptions during polling
                this.Logger.LogError(
                    operationName,
                    $"Error occurred while polling for workspace '{workspaceId}' operation: {ex.Message}");

                return await this.HandleWorkspaceProvisionFailure(
                    errorCode: ErrorResponseCode.ApplianceProvisioningFailed.ToString(),
                    errorMessage: ex.Message,
                    customMessage: $"Failed to poll for workspace '{workspaceId}' operation due to an error.",
                    details: ex.Message)
                    .ConfigureAwait(false);
            }
        }

        #endregion

        #region Error Handling Helper Methods
        /// <summary>
        /// Handles the failure of workspace initialization and sets the appropriate error response.
        /// </summary>
        /// <param name="response"></param>
        /// <param name="workspaceResourceId"></param>
        /// <returns></returns>
        private async Task<JobExecutionResult> HandleWorkspaceInitFailure(
            WorkspaceInitializeResponse response,
            string workspaceResourceId)
        {
            var operationName = this.GetOperationName(Utilities.GetAsyncMethodName());
            string errorMessage = $"Failed to initialize the serverless workspace '{workspaceResourceId}' with status '{response.Status}'";
            string detailedMessage = response.ErrorResponseMessage?.Error?.Message ??
                ErrorResponseMessages.ErrorInitializingWorkspace.ToLocalizedMessage(
                    ErrorResponseMessages.InternalServerError.ToLocalizedMessage(
                        RequestCorrelationContext.Current.CorrelationId));

            this.Logger.LogDebug(operationName, errorMessage);

            return await this.HandleWorkspaceProvisionFailure(
                errorCode: ErrorResponseCode.ErrorInitializingWorkspace.ToString(),
                errorMessage: detailedMessage,
                customMessage: errorMessage,
                details: null)
                .ConfigureAwait(false); ;
        }

        /// <summary>
        /// Handles the error when the resource is in an invalid state.
        /// </summary>
        /// <param name="resourceType"></param>
        /// <param name="resourceName"></param>
        /// <param name="currentState"></param>
        /// <returns></returns>
        private async Task<JobExecutionResult> HandleInvalidStateError(
            string resourceType,
            string resourceName,
            ProvisioningState? currentState)
        {
            var operationName = this.GetOperationName(Utilities.GetAsyncMethodName());
            var errorMessage = $"Resource of type '{resourceType}' with name '{resourceName}' has invalid state '{currentState}'";
            this.Logger.LogError(operationName, errorMessage);

            return await this.HandleWorkspaceProvisionFailure(
                errorCode: ErrorResponseCode.InvalidApplianceState.ToString(),
                errorMessage: ErrorResponseMessages.InvalidApplianceState.ToLocalizedMessage(resourceType, resourceName, currentState),
                customMessage: errorMessage,
                details: null)
                .ConfigureAwait(false); ;
        }

        /// <summary>
        /// Handles the error when an unknown operation is encountered.
        /// </summary>
        /// <returns></returns>
        private async Task<JobExecutionResult> HandleUnknownOperationError()
        {
            var operationName = this.GetOperationName(Utilities.GetAsyncMethodName());
            var errorMessage = $"The unknown resource operation '{this.Metadata.ResourceOperation}' is encountered. Current provisioning operation is failed.";
            this.Logger.LogError(operationName, errorMessage);

            await this.SetProvisioningStateToFailed();

            return this.CreateFaultedJobExecutionResult(errorMessage);
        }

        /// <summary>
        /// Handles the workspace provision failure scenario.
        /// </summary>
        /// <param name="errorCode">the error code of the async operation result</param>
        /// <param name="errorMessage">the error message of the async operation result</param>
        /// <param name="customMessage">the job execution result message</param>
        /// <param name="details">the job execution result details</param>
        /// <returns></returns>
        private async Task<JobExecutionResult> HandleWorkspaceProvisionFailure(
            string errorCode,
            string errorMessage,
            string customMessage = null,
            string details = null)
        {
            this.SetFailedOperationResponse(
                errorCode: errorCode,
                errorMessage: errorMessage);

            await this.SetProvisioningStateToFailed();

            return this.CreateFaultedJobExecutionResult(customMessage, details);
        }

        /// <summary>
        /// Sets the failed operation response with the given error code and message.
        /// </summary>
        /// <param name="errorCode">the error code</param>
        /// <param name="errorMessage">the error message</param>
        /// <param name="statusCode">the status code, default to HttpStatusCode.OK</param>
        private void SetFailedOperationResponse(
            string errorCode,
            string errorMessage,
            HttpStatusCode statusCode = HttpStatusCode.OK)
        {
            this.Metadata.OperationResponseStatusCode = statusCode;
            this.Metadata.OperationResponseContent = AsyncOperationResult
                .GetAsyncOperationResult(
                    ProvisioningState.Failed,
                    errorCode,
                    errorMessage)
                .ToJToken();
        }

        /// <summary>
        /// Set Provisioning State to Failed
        /// </summary>
        /// <returns></returns>
        private async Task SetProvisioningStateToFailed()
        {
            await this.SetApplianceProvisioningState(
                subscriptionId: this.Metadata.SubscriptionId,
                resourceGroupName: this.Metadata.ResourceGroupName,
                applianceName: this.Metadata.ApplianceName,
                resourceType: this.Metadata.ResourceType,
                provisioningState: ProvisioningState.Failed)
                .ConfigureAwait(false);
        }

        /// <summary>
        /// Creates a faulted job execution result.
        /// </summary>
        /// <returns></returns>
        private JobExecutionResult CreateFaultedJobExecutionResult(
            string message,
            string details = null)
        {
            return new JobExecutionResult
            {
                Status = JobExecutionStatus.Faulted,
                Message = message,
                Details = details ?? this.Metadata.ResourceOperation.ToString(),
                NextMetadata = this.Metadata.ToJson(),
            };
        }

        /// <summary>
        /// Get failed job execution result
        /// </summary>
        /// <param name="workspaceResourceId">Workspace Azure Resource Id</param>
        /// <param name="metadata">Job Metadata</param>
        /// <param name="errorResponseCode">The error code</param>
        /// <param name="serverException">Exception object</param>
        protected JobExecutionResult HandleFailedJobExecutionResult(
            string workspaceResourceId,
            string errorResponseCode,
            Exception serverException)
        {
            var defaultErrorMessage = ErrorResponseMessages.WorkspaceUpdateFailed.ToLocalizedMessage(
                workspaceResourceId,
                serverException.Message);

            this.SetFailedOperationResponse(
                errorResponseCode,
                defaultErrorMessage);

            if (serverException is ServerErrorResponseMessageException exception)
            {
                if (exception.IsExceptionRetryable(this.Metadata.CurrentDeploymentOperationExceptionRetryableErrorCount,
                        this.MaxRetries))
                {
                    this.Metadata.CurrentDeploymentOperationExceptionRetryableErrorCount++;

                    return this.PostponeJob(
                        message: exception.Message,
                        delay: this.PostponeInterval);
                }

                var ex = serverException as ServerErrorResponseMessageException;
                this.Metadata.SLIErrorInfo = SLIErrorInfo.GetSLIErrorInfo(ex.HttpStatus, ex.ErrorCode);
            }

            if (serverException is ErrorResponseMessageException)
            {
                var ex = serverException as ErrorResponseMessageException;
                this.Metadata.SLIErrorInfo = SLIErrorInfo.GetSLIErrorInfo(ex.HttpStatus, ex.ErrorCode.ToString());
            }

            return CreateFaultedJobExecutionResult(
                message: serverException.Message,
                details: serverException.InnerException?.Message ?? serverException.Message);
        }

        #endregion

        /// <summary>
        /// Populates appliance properties from an associated appliance package.
        /// </summary>
        /// <param name="applianceEntity">The appliance entity.</param>
        /// <param name="appliancePackage">The application package.</param>
        private void PopulatePropertiesFromAppliancePackage(ApplianceEntity applianceEntity, AppliancePackage appliancePackage)
        {
            applianceEntity.Properties.Authorizations = appliancePackage.Authorizations?.Select(auth => new ApplicationAuthorizationEntity()
            {
                PrincipalId = auth.PrincipalId,
                RoleDefinitionId = auth.RoleDefinitionId
            }).ToArray();
        }

        /// <summary>
        /// Get Workspace Entity
        /// </summary>
        /// <returns></returns>
        private async Task<ApplianceEntity> GetWorkspaceEntity()
        {
            var operationName = this.GetOperationName(Utilities.GetAsyncMethodName());

            var workspaceEntity = await this.ApplianceDataProvider
                .FindAppliance(
                    subscriptionId: this.Metadata.SubscriptionId,
                    resourceGroupName: this.Metadata.ResourceGroupName,
                    applianceName: this.Metadata.ApplianceName)
                .ConfigureAwait(continueOnCapturedContext: false);

            this.Logger.LogDebug(
                operationName,
                string.Format(
                    "ResourceType: '{0}', SubscriptionId: '{1}', ResourceGroup: '{2}', WorkspaceName: '{3}', WorkspaceEntity == null: '{4}'",
                    this.Metadata.ResourceType,
                    this.Metadata.SubscriptionId,
                    this.Metadata.ResourceGroupName,
                    this.Metadata.ApplianceName,
            workspaceEntity == null));

            return workspaceEntity;
        }
    }
}