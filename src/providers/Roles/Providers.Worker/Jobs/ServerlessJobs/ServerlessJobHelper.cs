﻿//-----------------------------------------------------------
// Copyright (c) Microsoft Corporation.  All rights reserved.
//-----------------------------------------------------------

namespace Microsoft.WindowsAzure.ResourceStack.Providers.Worker.Jobs.ServerlessJobs
{
    using System;
    using Microsoft.WindowsAzure.ResourceStack.Common.BackgroundJobs;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Common.Constants;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Common.Metrics;

    /// <summary>
    /// Helper class for provisioning and deprovisioning related to workspace jobs
    /// </summary>
    public static class ServerlessJobHelper
    {
        #region Logging and Metrics
        /// <summary>
        /// Logs SLO metrics for a successful operation and records them
        /// </summary>
        /// <param name="logger">The logger.</param>
        /// <param name="metricsManager">The metrics manager.</param>
        /// <param name="operationName">The operation name</param>
        /// <param name="operationType">The operation type.</param>
        /// <param name="subscriptionId">The subscription id.</param>
        /// <param name="resourceGroupName">The resource group name.</param>
        /// <param name="workspaceName">The workspace name.</param>
        /// <param name="startDateTime">The start time.</param>
        /// <param name="apiVersion">The api version</param>
        /// <param name="customMessage">The custom message used in log.</param>
        public static void LogSuccessMetrics(
            JobLogger logger,
            IMetricsManager metricsManager,
            string operationName,
            string operationType,
            string subscriptionId,
            string resourceGroupName,
            string workspaceName,
            DateTime startDateTime,
            string apiVersion,
            string customMessage = null)
        {
            var endDate = DateTime.UtcNow;
            var dateDiff = endDate - startDateTime;

            logger.LogDebug(
                operationName,
                $"SLO:{operationType}:Job succeed for workspace {customMessage}: {subscriptionId}/{resourceGroupName}/{workspaceName}, " +
                $"Start date: {startDateTime}, End Date: {endDate}, Diff:{dateDiff.TotalMilliseconds} ms, " +
                $"{dateDiff.TotalSeconds} secs, {dateDiff.TotalMinutes} mins, apiversion:{apiVersion}");

            metricsManager.MeasureLongRunningLatencyResponse(
                (long)dateDiff.TotalMilliseconds,
                subscriptionId,
                resourceGroupName,
                workspaceName,
                ProviderConstants.Databricks.Success,
                operationType,
                apiVersion);
        }

        /// <summary>
        /// Logs SLO metrics for a failed operation and records them
        /// </summary>
        /// <param name="logger">The logger.</param>
        /// <param name="metricsManager">The metric manager.</param>
        /// <param name="operationName">The operation name.</param>
        /// <param name="operationType">The operation type.</param>
        /// <param name="subscriptionId">The subscription id.</param>
        /// <param name="resourceGroupName">The resource group name.</param>
        /// <param name="workspaceName">The workspace name.</param>
        /// <param name="startDateTime">The start time.</param>
        /// <param name="apiVersion">The api version.</param>
        /// <param name="responsibleParty">The responsible party.</param>
        /// <param name="failureCategory">The failure category.</param>
        public static void LogFailedMetrics(
            JobLogger logger,
            IMetricsManager metricsManager,
            string operationName,
            string operationType,
            string subscriptionId,
            string resourceGroupName,
            string workspaceName,
            DateTime startDateTime,
            string apiVersion,
            string responsibleParty = null,
            string failureCategory = null)
        {
            var endDate = DateTime.UtcNow;
            var dateDiff = endDate - startDateTime;

            logger.LogDebug(
                operationName,
                $"SLO::{operationType}:Job failed for workspace: {subscriptionId}/{resourceGroupName}/{workspaceName}, " +
                $"Start date: {startDateTime}, End Date {endDate}, Diff:{dateDiff.TotalMilliseconds} ms, " +
                $"{dateDiff.TotalSeconds} secs, {dateDiff.TotalMinutes} mins, apiversion:{apiVersion}");

            metricsManager.MeasureLongRunningLatencyResponse(
                (long)dateDiff.TotalMilliseconds,
                subscriptionId,
                resourceGroupName,
                workspaceName,
                ProviderConstants.Databricks.Failed,
                operationType,
                apiVersion,
                responsibleParty,
                failureCategory);
        }

        #endregion
    }
}
