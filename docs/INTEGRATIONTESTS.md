# Integration Tests and Runners

The integration tests are a C# project in the `ARMIntegrationTests` folder. They create and delete workspaces in all regions in order to validate the RP is working properly.<br>

SanityTestsAllRegions is the most important test. It tests most aspects of workspace creation and deletion, in parallel for all 25 regions. <br>

These tests run every hour <NAME_EMAIL> the results. <br>

TODO: we need to setup these integration tests to use Geneva runners, and setup automatic ICMs to come from them.

## Notes

1. These tests should build on any machine with Visual Studio installed. We used 2015 to develop them but future versions should work.

2. Important: Some of these tests expect the certificate to be installed on the LocalMachine. To install this cert, assuming you have access to the Azure Databricks Development subscription, go to [adbrptest-global-kv](https://ms.portal.azure.com/#@microsoft.onmicrosoft.com/resource/subscriptions/0140911e-1040-48da-8bc9-b99fb3dd88a6/resourceGroups/adbrptest_acis_ev2_rg/providers/Microsoft.KeyVault/vaults/adbrptest-global-kv/overview). Download and install the PFX file (private key) for the target certificate. Make sure to install to your LocalMachine and delete the pfx file afterwords.

The code is messy but should be self explanatory- we use the the rest APIs to perform Databricks workspace operations in various azure regions.

One thing to note, there is an existing ARM bug that prevents simultaneous workspace creations for the same subscription in the same region. So we use some sleeps in the parallel Sanity tests.
