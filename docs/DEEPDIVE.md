# Databricks RP Deep Dive

A quick note: this RP codebase is forked from the Managed Application RP. The general idea behind the Managed Apps RP is that 3rd parties can define an application/appliance. Based on that application/appliance definition, the Managed RP code:
1. Creates a managed resource group in the customer's subscription
2. Gives a particular app ID provided by the 3rd party accesss to that managed resource group
3. Deploys some initial resources defined in an ARM template provided by the 3rd party
4. 3rd party can deploy resources into that managed resource group to provide its service to the customer

Databricks used to be just an example of an appliance/application customer can deploy (though with some specific differences because it is a first party service). Now we have forked to have an independent RP.

The Databricks RP is a web service that provides CRUD operations for Databricks Workspaces: 
* create
* update
* delete
* get

When a customer creates a ADB workspace in the Azure portal (or using ARM templates), they call into ARM. ARM calls into the Databricks RP service to do the actual create operation.

Below we talk about the execution for CREATE. Once we understand CREATE, it is useful to step through the code of the other operations. 

### Create

The URI of a create request for a Databricks workspace in eastus2 looks like:
PUT http://eastus2.adb-spearfish-rp.net/subscriptions/mysubid/resourceGroups/myresourcegroup/providers/Microsoft.Databricks/workspaces/myworkspace?api-version=2018-04-01

This URI is routed to the `DatabricksController` in `HttpConfigurationInitializer`.

Take a look at the `DatabricksController` class. It's base class `ApplicationApiController` actually implements the PUT, GET, DELETE methods. When ARM calls into the Databricks RP to create a workspace, it calls the `ApplicationApiController::PutApplication` method. Follow this code path to understand what's happening. (Set a breakpoint in the method and run the DatabricksWorkspaceTests_SynchronousCRUD_TrackWithGetWorkspace test). At a high level, the tasks performed are:

#### Web Role
* `ApplicationApiEngine::PutApplication`: gets and validates the subscriptionId, resourceGroupName, and workspaceName (applicationName) from the request. Check if the workspace exists. If the workspace doesn't exist, this is a create request. If not, this is an update request.
* `ApplicationApiEnginer::CreateNewAppliance and ApplicationDataProvider::SaveAppliance`: An `ApplianceEntity` object is created (just a property bag representing the Databricks workspace). That `ApplianceEntity` is saved in Azure Storage.
* `ApplicationApiEngine::PutApplication`: creates an `ApplianceProvisioningJob`. That job is picked up by the Worker Role through the per-region jobs storage account.

#### Worker Role
* The worker picks up the `ApplianceProvisioningJob`.
* `ApplianceProvisioningJob::OnJobExecute`: gets the `ApplianceEntity` created earlier from Azure Storage.
* `ApplianceProvisioningJob::InitializeApplianceComponents`:
    * Calls `FrontdoorEngine::RegisterSubscriptionWithManagedTenant`: Registers a subscription with the Databricks Tenant. This allows cross-tenant authorization to work.
    * Calls `ApplianceEngine::CreateManagedResourceGroupIfNotExists`: Talks to ARM to create the managed resource group for the ADB workspace.
    * Calls `ApplianceEngine::CreateProviderAuthorizationRoleAssignements`: This code gives authorization to an application in the Databricks control plane to access the managed resource group.
    * Calls `ApplianceEngine::CreateManagedResourceGroupReadOnlyLockForMarketPlaceAppliance`: creates the lock on the managed resource group 
* `ApplianceProvisioningJob::ProvisionApplianceTemplate`: We provision an ARM template into the managed resource group. The template we provision can be found in `./DatabricksAppliances/prodMainTemplate.json`.

Notes: 
* RP web role sends async responses to CRUD requests. Those responses contain a URI in the Header. A GET on that URI calls into `ApplicationApiController::GetApplicationOperationStatus`. This queries the associated `ApplianceProvisioningJob` object to see its status.

### More on managed resource group creation

### Data Storage
