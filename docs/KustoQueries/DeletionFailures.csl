EventServiceEntries 
| where operation<PERSON>ame contains "Microsoft.Databricks/workspaces/delete"
| where subscriptionId != "0140911e-1040-48da-8bc9-b99fb3dd88a6"
| where status == "Succeeded" or status == "Failed"
| where properties !contains "MissingSubscriptionRegistration" and properties !contains "ApplianceBeingDeleted" and properties !contains "BadRequest" and properties !contains "policy" and properties !contains "Patch" 
| where properties !contains "in an invalid state 'Deleting'" and properties !contains "previous deployment from" and properties !contains "SubscriptionExceeded"
| summarize success_count = dcountif(resourceUri, status == "Succeeded"), failure_count = dcountif(resourceUri, status == "Failed") by bin (TIMESTAMP, 1d)
| extend rate = (success_count / toreal(success_count + failure_count))
| extend total = success_count + failure_count
| project TIMESTAMP, rate, total
| order by TIMESTAMP desc   
| render timechart
