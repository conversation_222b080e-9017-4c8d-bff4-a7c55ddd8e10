HttpIncomingRequests
| where targetUri contains "/subscriptions/edb336ca-b85f-4204-8057-7fdb7d65322c/resourcegroups/ODL-ml-50832/providers/Microsoft.Databricks/workspaces/50832"
| where httpStatusCode > 0
| where httpMethod == "PUT"
| project TIMESTAMP, correlationId
| join (HttpOutgoingRequests
    | where httpStatusCode >= 300
    | project outgoingMethod=httpMethod, outgoingStatus=httpStatusCode, outgoingUri=targetUri, correlationId) on correlationId