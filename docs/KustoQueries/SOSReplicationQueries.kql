// SOS has concept of dividing total data into 512 buckets and process them.
// When processing each bucket (compaction operation), it does all the operations configured for the group which is mutation, replication and deletion.
// So for last 24 hours if we see the percentage of completion is 100% it means SOS has processed all the buckets and performed all the operations.
// The following ARMProd queries will help you to dertermine, the completion of Mutation process. For further help refer SOS Wiki and work with ARM On call

//Check for percentage progress for replication groups
let BucketsPerShard = 512;  
JobTraces  
| where TIMESTAMP > ago(1d)  
| where Role contains "operator"  
| where jobPartition contains "replicationgroup-appliances" // our replication group name
| where message contains "Succeeded"
| extend slice = toint(split(jobId, "-", 1)[0])  
| summarize by slice, jobPartition  
| summarize count() by jobPartition
| where jobPartition contains "-"  
| extend ReplicationGroup = tostring(split(jobPartition, "-")[0])  
| summarize TotalBuckets = count() * BucketsPerShard, FinishedBuckets = sum(count_), RemainingBuckets = (count() * BucketsPerShard) - sum(count_) by ReplicationGroup  
| extend PercentComplete = round(todouble(FinishedBuckets) / todouble(TotalBuckets), 5) * 100  
| sort by PercentComplete desc nulls last 

// Check for no failures for last 36 hours to ensure 100% safe to disable mutation.
Compactions  
| where TIMESTAMP > ago(3d)
| where executionStatus != "Succeeded"  
// our replication group names list
| where replicationGroup in ("auscentral2replicationgroup", "auscentralreplicationgroup", "auseastreplicationgroup", "ausseastreplicationgroup", "brazsouthreplicationgroup", "cancentralreplicationgroup",
"caneastreplicationgroup", "centralusreplicationgroup", "eastasiareplicationgroup", "eastus2euapreplicationgroup", "eastus2replicationgroup", "eastusreplicationgroup", "francentralreplicationgroup", "indcentralreplicationgroup", "indsouthreplicationgroup", "indwestreplicationgroup", "japaneastreplicationgroup", "japanwestreplicationgroup",
"korcentralreplicationgroup", "ncentralusreplicationgroup", "northeuropereplicationgroup", "safrnorthreplicationgroup", "scentralusreplicationgroup", "seastasiareplicationgroup",
"uaenorthreplicationgroup", "uksouthreplicationgroup", "ukwestreplicationgroup", "westeuropereplicationgroup", "westus2replicationgroup", "westus3replicationgroup", "westusreplicationgroup", "switznorthreplicationgroup", "norwayeastreplicationgroup", "gerwcentralreplicationgroup",
"switzwestreplicationgroup", "swedcentralreplicationgroup", "wcentralusreplicationgroup")
| extend errorMessage = substring(exception, 0, indexof(exception, '\n'))
| project replicationGroup, errorMessage
| summarize count() by replicationGroup, errorMessage

//Check Job traces to see if the number records in compaction operation is nearly zero.
//There can be few operations but they are related to new records that are created, which are anyway created using new certs.  
JobTraces
| where TIMESTAMP >= ago(1d)
| where Role == "StorageOperations.Operator.razzle"
| where message contains "Succeeded" 
| where jobPartition contains "replicationgroup-appliances"
| extend ReplicationGroup = tostring(split(jobPartition, "-")[0])  
| where jobId != "COMPACTIONSETUPJOB"
| where message !contains "Background job operation \'CompactionJob\' completed with status \'Succeeded\', message \'Executed 0 compaction operations, 0 hydration operations, 0 hydration tombstone operations"
| project TIMESTAMP, ReplicationGroup, jobId, message
| order by TIMESTAMP desc

//Query to run for checking mutation configuration is being picked or not.
Traces
| where TIMESTAMP >= ago(1d)
| where Role == "StorageOperations.Operator.razzle"
| where message contains "PropertyBagReEncryptSecrets"
| where message contains "B2669F50D2DC7ACBACC6C09F10ACADDECB04BFF5" // One of the certificate we gave in mutation config
| project TIMESTAMP, Role, RoleInstance, operationName, message, exception

//Find the jobPartition that is incomplete by running the below query.  
JobTraces 
| where TIMESTAMP >= ago(2d) 
| where Role == "StorageOperations.Operator.razzle" 
| where message contains "Succeeded"  
//Change "replicationgroup-appliances" to your replication group name  
| where jobPartition contains "replicationgroup-appliances" 
| extend slice = toint(split(jobId, "-", 1)[0]) 
| summarize by slice, jobPartition 
| summarize count() by jobPartition 
| where count_ != 512

//You can also search for any errors in the JobTraces table for the specific jobPartition and jobId 
JobTraces 
| where TIMESTAMP >= ago(2d) 
| where Role == "StorageOperations.Operator.razzle" 
| where message !contains "Succeeded"  
//Change "replicationgroup-appliances" to your replication group name  
| where jobPartition contains "replicationgroup-appliances" 
| project jobPartition, jobId, message 
| limit 10

//Look at the history of job for this jobPartition and jobId. If you see consistent errors, this is related to incorrect data or incorrect config ARM SOS 
JobDefinitions 
| where TIMESTAMP > ago(2d) 
| where Role contains "operator" 
| where jobPartition contains "replicationgroup-appliances" and jobId == "COMPACTIONJOB-00297" 
| project jobPartition, jobId , lastExecutionStatus   
| limit 5

//Verify the compaction logs to find the errors if any. 
//Update the replication group and startBoundary as appropriate for the job partition. 
Compactions 
| where TIMESTAMP >= ago(1d) 
| where Role == "StorageOperations.Operator.razzle" 
| where replicationGroup == "westus2replicationgroup" or replicationGroup == "westusreplicationgroup" or replicationGroup == "westeuropereplicationgroup" or replicationGroup == "westus3replicationgroup" 
| where executionStatus == "Failed" 
| where startBoundary == "F8000" 
