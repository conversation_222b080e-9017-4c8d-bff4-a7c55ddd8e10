HttpIncomingRequests
| where (TIMESTAMP >= datetime(1/24/2019 10:44:25 PM) and TIMESTAMP < datetime(1/24/2019 11:14:25 PM)) 
| where operationName == "GET/SUBSCRIPTIONS/RESOURCEGROUPS/PROVIDERS/MICROSOFT.DATABRICKS/LOCATIONS/OPERATIONSTATUSES/" and Deployment == "c38385ffabcc4f92a9a59e90ed06f78e" and Role == "Frontdoor.Web.razzle" and TaskName == "HttpIncomingRequestEndWithServerFailure" and SourceNamespace == "csmAustraliaSERPF"
| project correlationId 
| join (HttpOutgoingRequests
| where (TIMESTAMP >= datetime(1/24/2019 10:44:25 PM) and TIMESTAMP < datetime(1/24/2019 11:14:25 PM)) 
| where hostName != "management.azure.com"
) on correlationId 
| where httpStatusCode > 299