EventServiceEntries 
| where TIMESTAMP >= ago(1d)
| where operation<PERSON><PERSON> contains "Microsoft.Databricks/workspaces/write"
| where subscriptionId != "0140911e-1040-48da-8bc9-b99fb3dd88a6"
| where status == "Failed"
| where properties !contains "MissingSubscriptionRegistration" and properties !contains "ApplianceBeingDeleted" and properties !contains "BadRequest" and properties !contains "policy" and properties !contains "Patch" 
| where properties !contains "in an invalid state 'Deleting'" and properties !contains "previous deployment from" and properties !contains "SubscriptionExceeded"
| project correlationId, subscriptionId, status, properties
| join (
        HttpOutgoingRequests           
        | where targetUri contains "adb-spearfish-rp.net"
        | project correlationId, targetUri) 
        on correlationId
| extend region = substring(substring(targetUri, 0, indexof(targetUri, ".adb-spearfish")), strlen("https://"))
//| distinct status, subscriptionId, region
//| project subscriptionId, status, region
//| summarize success_count = countif(status == "Succeeded"), failure_count = countif(status == "Failed") by region

        