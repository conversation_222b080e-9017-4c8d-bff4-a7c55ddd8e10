# RP Kusto Logs

You'll want to install the Kusto client. Go to aka.ms/kusto to do this.

## Kusto clusters

There are two useful Kusto clusters for understanding Databricks workspace creations/deletions/gets/etc: ARMProd and AzureDatabricksRP. (TODO: instructions on how to get access)

* ARMProd cluster: https://csm.kusto.windows.net:443 
* AzureDatabricksRP cluster: https://azuredatabricksrp.kusto.windows.net:443 

Add these clusters to your Kusto client (right click on Connections and hit add). You can also use some Kusto WebExplorer links below.

## Workspace creation/deletion rates

Make sure you have access to the ARMProd Kusto tables to run the below queries. TODO: instructions on getting ARMProd access.

[Creation Rates](https://dataexplorer.azure.com/clusters/armprod/databases/ARMProd?query=H4sIAAAAAAAAA42SQU8bMRCF70j8h2EvJGpSEjVCySEHEKnEIRUi6bmatWc3Lrv2duwNSdUf3/GSwFLB0pvl8Tx/8+YttmTDinhrFC1sYEMeTk/+wOOGmMBVxBiMs9+wJFDOBjTWQ7I0ip13Wfh8gwFTNurBXzw6fvAVKpIjm0DJi46vU6/YVFHqVsPZHJLReDKajcc0HI8mo+FkqnE4TdVsmM5mWfpF6+kUL9sKAUPtYS6dq1opIk06Acftwlc0hdy+NFUcBwhxprM2vPfG5qsW0z3lxoenURNAq99pvaqqwqBVdE2icEMFhYjxfsM16nv6VZMPXa8qVxi173pxh0FtEvhoNGNFA4zdYmF0Yw3BeYMpuOedCExb48RITVXh9qWkAjJ2ZVdP28HF7rCTjxCxYEK9B9qJ475Rfy4yeVezBKjr1/WGoESLOWlI98dHe3AZBCkdNSBnV1eg0FoXIJX0btDmB0BflyWy+R2TKWHy/odytUw8B90cTNY76nxnM3g7ff0BZJK4mun/uw8R7UfyVLbVW98uF6v11fJuAGPdj2y0CyTDc9zdHHqvAS8gOPGv+Of602uSfiMkzvwkFaD1RRSNJceaODI812TvXgE0CWP5X8rBlCSmcTg9+Qs0hvQwJgQAAA==)

[Deletion Rates](https://dataexplorer.azure.com/clusters/armprod/databases/ARMProd?query=H4sIAAAAAAAAA42QTUvDQBCG74L/YcypwcYmWCQ59CBYoYeK0HqW/ZjQtclumJ1tVfzx7opgFBFvw8y8zzzM8oCWN0gHo3BpmQx6OD15g+MOCcENSIKNs3eiR1DOsjDWQ7Y2ipx3LV/cCBaSjNr72dHR3g9CoZ9p7JAx+wL5IL0iMyTWSsPZArKympdNVWFRlfOymNdaFLVUTSGbppWXWte1uBoTWHDwsIjJTVAKUaPOwNF4cCtMF7sp5EPfCzKv6XTc9v5RuWAZFqA/CtNOCL0LpPCBzPR3fD6FNiID4f/Tnw45yBeQxsJku1ovN9vr9f0UKp0nN3xmtBriZzESJ98FZ8COUHQ/2uffTfIxiB2LLpL+TKT9gdwTKoaRUpJII4ogJGDTo9oJ4ndjthYfGAIAAA==)

## Useful Kusto queries

There are some useful ad-hoc queries in the ./KustoQueries directory. Some other queries are listed below. 

### Workspace Creations and Deletions

Against ARMProd,

```sql
EventServiceEntries 
| where PreciseTimeStamp >= ago(1d) 
| where operationName contains "Microsoft.Databricks/workspaces/write" or operationName contains "Microsoft.Databricks/workspaces/delete"
| where status == "Failed" or where status == "Succeeded"
```

The properties field is good for seeing the error that customers see from the Portal, ARM templates, etc.

### Incoming Requests to the RP

Against AzureDatabricksRP,

```sql
HttpIncomingRequests 
| where TIMESTAMP >= ago(1h) 
| where httpMethod == "PUT" 
| where httpStatusCode > 0 
| where targetUri contains "/subscriptions/0140911e-1040-48da-8bc9-b99fb3dd88a6/resourceGroups/IntTestsuksouthSQFTPP/providers/Microsoft.Databricks/workspaces/workspaceSZT2SX"
```

You can change the httpMethod depending on what you're looking for.

### Looking up workspace creation job operations for associated with a PUT request

```sql
HttpIncomingRequests 
| where targetUri contains "/subscriptions/596df088-441c-4a6f-881e-5511128a3f1c/resourcegroups/qa_rg_27cf023d/providers/Microsoft.Databricks/workspaces/qa_workspace_premium_27cf023d" 
| where httpMethod == "PUT" 
| where httpStatusCode > 0 
| project TIMESTAMP, httpMethod, httpStatusCode, correlationId 
| join (JobTraces             
        | project message, exception, correlationId)        
        on correlationId
```

### Looking up exceptions and errors in the RP in the RP

A generally good idea is to search the Errors, Traces, and JobTraces tables based on the correlationId of the request. You can find the correlationId in the HttpIncomingRequests table.

```sql
Errors
| where correlationId == ""
```
