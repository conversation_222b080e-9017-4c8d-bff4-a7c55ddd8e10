# Deploying the Azure Databricks RP

The basic steps for a deployment are as follows:

Note: in all the below steps, replace MM_dd_yyyy with the current date (ex. 11_05_2018)

1. From the commit in master you wish to deploy from, run `git checkout -b release_MM_dd_yyyy` to create a new release branch (release_canary_MM_dd_yyyy if it is only for Canary).
2. Push this branch `git push origin release_MM_dd_yyyy`
3. Generate a build. For now, this is done manually on SAW machines, we are moving to use CDPX. See the README file in the root for steps on how to generate a build.
4. Generate latest Ev2 Specs by running `.\GenerateEv2Configs.ps1 -BuildVersion 1.YY.MMDD.HHMM -BuildLocalPath D:\Repos\DatabricksRP\deployment\ExpressV2` in ExpressV2 folder.
5. Create a cspkg. From an admin CMD prompt (not powershell), run `.\deployment\Package\createcspackage.cmd` on your SAW. We are moving to also do this in CDPX. (Add "C:\Program Files\Microsoft SDKs\Azure\\.NET SDK\v2.9\bin;C:\Program Files\Microsoft SDKs\Azure\Emulator" to environment Path variable and restart the command prompt if you get command not found error).
6. Create new folder with name `release_MM_dd_yyyy` under `\\scratch2\scratch\shnimm\AzureDatabricksRP_BuildDrop`
7. Copy the ServiceGroupRoot folder to `\\scratch2\scratch\shnimm\AzureDatabricksRP_BuildDrop\release_MM_dd_yyyy`
8. Generate build by queuing new build from [ADB RP _ Official Pipeline _STATIC temporary](https://msdata.visualstudio.com/Spearfish/_build?definitionId=7314) and set DropPath to `\\scratch2\scratch\shnimm\AzureDatabricksRP_BuildDrop\release_MM_dd_yyyy`

Now that you have a build, you can actually do a deployment. Follow the steps mentioned below for deployment

## Region wise deployment

| Usage Type | Region Names |
| --- | --- |
| Low Region | Korea Central, East Asia, UK South, Canada Central, Canada East, Japan West, West India, South Africa North, Brazil South, France Central, UAE North, Switzerland North, Norway East, Germany West Central , Switzerland West
| Medium Region | WEST US, South East Asia, EAST US, South Central US, Australia South EAST, Japan East, UK WEST, Central India, Australia East, South India, North Central US |
| High Region | West US2, West Europe, EAST US2 North Europe, Australia Central, Central US |

*Note : Australia Central 2 is not enabled for customers*

## Deploy to eastus2euap (canary)

Create a new release using [this](https://msdata.visualstudio.com/Spearfish/_release?view=mine&definitionId=9) release definition.

## Deploy to low-usage regions

Create a new release using [this](https://msdata.visualstudio.com/Spearfish/_release?view=mine&definitionId=5) release definition.

## Deploy to Non customer regions

Create a new release using [this](https://msdata.visualstudio.com/Spearfish/_release?view=mine&definitionId=14) release definition.

## Deploy to medium-usage regions

Create a new release using [this](https://msdata.visualstudio.com/Spearfish/_release?view=mine&definitionId=6) release definition.

## Deploy to high-usage regions

Create a new release using [this](https://msdata.visualstudio.com/Spearfish/_release?view=mine&definitionId=7) release definition.

## Rollback

Deploy the last deployed build to respective region using the above release definitions. 

## Deploying mainTemplate.json files

1. Make sure the mainTemplate files are updated on the box you are deploying from.
2. Get JIT access to the RP subscription
3. Use the scripts in `deployment/AppliancePackage/`. The canary script will update the mainTemplate.json file for the eastus2euap region, the other script updates the mainTemplate.json file for other regions. 
