{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {"scrolled": true}, "outputs": [{"data": {"text/plain": ["'2018-12-05 16:25:57.041639'"]}, "execution_count": 1, "metadata": {}, "output_type": "execute_result"}], "source": ["from datetime import datetime\n", "str(datetime.now())"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Workspace Creations by Region\n", "\n", "Below we look at workspace creations to the new RP by region. We use the EventServiceEntriesAnalyzer.get_error_groups (in the kustoutils.py file in this repo) to break the errors into groups. <br>\n", "\n", "The empty regions have not been switched over yet."]}, {"cell_type": "code", "execution_count": 1, "metadata": {"scrolled": true}, "outputs": [], "source": ["from kustoutils import KustoCaller, EventServiceEntriesAnalyzer\n", "import pandas as pd\n", "import warnings\n", "import json\n", "warnings.filterwarnings('ignore')\n", "\n", "pd.set_option('display.max_colwidth', -1)\n", "\n", "kc = KustoCaller()\n", "query = \"\"\"\n", "HttpIncomingRequests\n", "| where httpStatusCode > 0\n", "| where subscriptionId != \"0140911e-1040-48da-8bc9-b99fb3dd88a6\"\n", "| where targetUri contains \"Microsoft.Databricks\"\n", "| where httpMethod == \"PUT\"\n", "| project TIMESTAMP, subscriptionId, httpMethod, httpStatusCode, targetUri\n", "| order by subscriptionId, TIMESTAMP asc\n", "\"\"\"\n", "df_puts = kc.query(query, kc.RP_DB, kc.RP_CLUSTER)\n", "\n", "query = \"\"\"\n", "EventServiceEntries \n", "| where subscriptionId != \"0140911e-1040-48da-8bc9-b99fb3dd88a6\"\n", "| where operationName contains \"Microsoft.Databricks/workspaces/write\"\n", "| where status == \"Failed\" or status == \"Succeeded\"\n", "| project TIMESTAMP, subscriptionId, resourceUri, properties, status\n", "\"\"\"\n", "\n", "df_creations = kc.query(query, kc.ARM_DB, kc.ARM_CLUSTER)\n", "\n", "def get_region(resourceUri: str):\n", "    matchingputs= df_puts[df_puts.targetUri.str.contains(resourceUri)]\n", "    if(len(matchingputs) == 0):\n", "        return \"unknown\"\n", "    targetUri = matchingputs.iloc[0].targetUri\n", "    return targetUri[8:targetUri.index(\".adb-spearfish\")]\n", "    \n", "df_creations[\"region\"] = df_creations.resourceUri.apply(get_region)\n", "\n", "def transform(value):\n", "    if(value == \"\"):\n", "        return {}\n", "    jsonval = json.loads(value)\n", "    return json.loads(jsonval[\"statusMessage\"]) if \"statusMessage\" in jsonval else {}\n", "\n", "df_creations.properties = df_creations.properties.map(transform)\n", "df_creations = EventServiceEntriesAnalyzer.get_error_groups(df_creations)\n", "\n", "df_creations = df_creations[df_creations.region != \"unknown\"]"]}, {"cell_type": "code", "execution_count": 5, "metadata": {"scrolled": false}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["eastus2euap\n", "{'Succeeded': 5}\n", "{}\n", "\n", "west<PERSON>\n", "{'Succeeded': 2}\n", "{}\n", "\n", "eastus2\n", "{}\n", "{}\n", "\n", "westeurope\n", "{}\n", "{}\n", "\n", "northeurope\n", "{}\n", "{}\n", "\n", "eastus\n", "{'Succeeded': 1}\n", "{}\n", "\n", "seastasia\n", "{'Succeeded': 17, 'Failed': 1}\n", "{'ResourceDeploymentFailure:ApplianceProvisioningFailed': 1}\n", "/subscriptions/596df088-441c-4a6f-881e-5511128a3f1c/resourcegroups/qa_rg_27cf023d/providers/Microsoft.Databricks/workspaces/qa_workspace_premium_27cf023d\n", "\n", "eastasia\n", "{'Succeeded': 2}\n", "{}\n", "\n", "<PERSON><PERSON><PERSON>\n", "{'Succeeded': 3, 'Failed': 2}\n", "{'InternalServerError:NA': 2}\n", "/subscriptions/619f1ab9-764e-4308-8360-da885b1c9eaa/resourcegroups/paasstandard-s19us-20180404/providers/Microsoft.Databricks/workspaces/discovery-nomrg-ussc-dev\n", "/subscriptions/619f1ab9-764e-4308-8360-da885b1c9eaa/resourcegroups/paasstandard-s19us-20180404/providers/Microsoft.Databricks/workspaces/discovery-armtest-nomrg-ussc-dev\n", "\n", "ncentralus\n", "{}\n", "{}\n", "\n", "westus2\n", "{}\n", "{}\n", "\n", "centralus\n", "{}\n", "{}\n", "\n", "ukwest\n", "{}\n", "{}\n", "\n", "uksouth\n", "{}\n", "{}\n", "\n", "auseast\n", "{}\n", "{}\n", "\n", "ausseast\n", "{}\n", "{}\n", "\n", "auscentral\n", "{}\n", "{}\n", "\n", "auscentral2\n", "{}\n", "{}\n", "\n", "japaneast\n", "{'Succeeded': 29, 'Failed': 6}\n", "{'ResourceDeploymentFailure:LinkedAuthorizationFailed': 6}\n", "/subscriptions/ee2bb161-08ec-4c51-bbe7-69772f43c3bc/resourcegroups/rg-p-moana-handson/providers/Microsoft.Databricks/workspaces/testwsdbb\n", "/subscriptions/5b865324-839b-4350-999c-227a3f3357e0/resourcegroups/GBB-20181128-databricks/providers/Microsoft.Databricks/workspaces/databricks-20181128-001\n", "/subscriptions/5b865324-839b-4350-999c-227a3f3357e0/resourcegroups/GBB-20181128-databricks/providers/Microsoft.Databricks/workspaces/gbbdatabricks20181128-001\n", "/subscriptions/5483e85c-abe7-42ca-a743-a765da050bc7/resourcegroups/20181128HandsOn/providers/Microsoft.Databricks/workspaces/20181128databricks\n", "/subscriptions/5483e85c-abe7-42ca-a743-a765da050bc7/resourcegroups/20181128HandsOn/providers/Microsoft.Databricks/workspaces/DatabricksService\n", "/subscriptions/78916214-b0cf-4053-9d53-82849c4f9ea8/resourcegroups/20181128-Databricks/providers/Microsoft.Databricks/workspaces/databrickstest\n", "\n", "japanwest\n", "{'Succeeded': 9, 'Failed': 1}\n", "{'ResourceDeploymentFailure:LinkedAuthorizationFailed': 1}\n", "/subscriptions/e286d118-46a2-41c3-9948-e7af2a6b231c/resourcegroups/TurnGreenVisualization/providers/Microsoft.Databricks/workspaces/TestMatsuBricks\n", "\n", "cancentral\n", "{'Succeeded': 1}\n", "{}\n", "\n", "caneast\n", "{'Succeeded': 1}\n", "{}\n", "\n", "indcentral\n", "{'Succeeded': 8}\n", "{}\n", "\n", "indsouth\n", "{'Succeeded': 15, 'Failed': 4}\n", "{'ResourceDeploymentFailure:LinkedAuthorizationFailed': 3, 'InvalidDatabricksSku:NA': 1}\n", "/subscriptions/e3866c3e-ec80-4f10-a068-ceb132672f14/resourcegroups/nagendraDataFacory/providers/Microsoft.Databricks/workspaces/HelloDataBricks\n", "/subscriptions/ed0b2672-8412-463f-a0c1-1867730d7e27/resourcegroups/ansirg2/providers/Microsoft.Databricks/workspaces/abDatabricks\n", "/subscriptions/f3329e6b-ef13-48b3-839b-45863ceee21e/resourcegroups/maya-dev/providers/Microsoft.Databricks/workspaces/maya-dev-db\n", "/subscriptions/8fac0370-b046-4b2a-b2f4-fd6fc7df2776/resourcegroups/Raja/providers/Microsoft.Databricks/workspaces/eswar123\n", "\n", "indwest\n", "{'Succeeded': 6}\n", "{}\n", "\n"]}], "source": ["regions = [\"eastus2euap\", \"westus\", \"eastus2\", \"westeurope\", \"northeurope\", \"eastus\", \"seastasia\", \n", "           \"eastasia\", \"scentralus\", \"ncentralus\", \"westus2\", \"centralus\", \"ukwest\", \"uksouth\", \n", "           \"auseast\", \"ausseast\", \"auscentral\", \"auscentral2\", \"japaneast\", \"japanwest\", \"cancentral\", \"caneast\",\n", "           \"indcentral\", \"indsouth\", \"indwest\"]\n", "\n", "unique_creations = df_creations.drop_duplicates(\"resourceUri\")\n", "\n", "for current_region in regions:\n", "    print(current_region)\n", "    df_region = unique_creations[unique_creations.region == current_region]\n", "    print(dict(df_region.status.value_counts()))\n", "    print(dict(df_region[df_region.status == \"Failed\"].error_group.value_counts()))\n", "    for resourceUri in list(df_region[df_region.status == \"Failed\"].resourceUri):\n", "        print(resourceUri)\n", "    print()"]}, {"cell_type": "code", "execution_count": null, "metadata": {"scrolled": true}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.6.4"}}, "nbformat": 4, "nbformat_minor": 2}