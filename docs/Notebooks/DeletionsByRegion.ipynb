{"cells": [{"cell_type": "code", "execution_count": 14, "metadata": {"scrolled": true}, "outputs": [{"data": {"text/plain": ["'2018-12-04 22:29:02.617453'"]}, "execution_count": 14, "metadata": {}, "output_type": "execute_result"}], "source": ["from datetime import datetime\n", "str(datetime.now())"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Workspace Deletions by Region\n", "\n", "Below we look at workspace creations to the new RP by region. We use the EventServiceEntriesAnalyzer.get_error_groups (in the kustoutils.py file in this repo) to break the errors into groups. <br>\n", "\n", "The empty regions have not been switched over yet."]}, {"cell_type": "code", "execution_count": 12, "metadata": {"scrolled": true}, "outputs": [], "source": ["from kustoutils import KustoCaller, EventServiceEntriesAnalyzer\n", "import pandas as pd\n", "import warnings\n", "import json\n", "warnings.filterwarnings('ignore')\n", "\n", "pd.set_option('display.max_colwidth', -1)\n", "\n", "kc = KustoCaller()\n", "query = \"\"\"\n", "HttpIncomingRequests\n", "| where httpStatusCode > 0\n", "| where subscriptionId != \"0140911e-1040-48da-8bc9-b99fb3dd88a6\"\n", "| where targetUri contains \"Microsoft.Databricks\"\n", "| where httpMethod == \"DELETE\"\n", "| project TIMESTAMP, subscriptionId, httpMethod, httpStatusCode, targetUri\n", "| order by subscriptionId, TIMESTAMP asc\n", "\"\"\"\n", "df_puts = kc.query(query, kc.RP_DB, kc.RP_CLUSTER)\n", "\n", "query = \"\"\"\n", "EventServiceEntries \n", "| where subscriptionId != \"0140911e-1040-48da-8bc9-b99fb3dd88a6\"\n", "| where operationName contains \"Microsoft.Databricks/workspaces/delete\"\n", "| where status == \"Failed\" or status == \"Succeeded\"\n", "| project TIMESTAMP, subscriptionId, resourceUri, properties, status\n", "\"\"\"\n", "\n", "df_deletions = kc.query(query, kc.ARM_DB, kc.ARM_CLUSTER)\n", "\n", "def get_region(resourceUri: str):\n", "    matchingputs= df_puts[df_puts.targetUri.str.contains(resourceUri)]\n", "    if(len(matchingputs) == 0):\n", "        return \"unknown\"\n", "    targetUri = matchingputs.iloc[0].targetUri\n", "    return targetUri[8:targetUri.index(\".adb-spearfish\")]\n", "    \n", "df_deletions[\"region\"] = df_deletions.resourceUri.apply(get_region)\n", "\n", "def transform(value):\n", "    if(value == \"\"):\n", "        return {}\n", "    jsonval = json.loads(value)\n", "    return json.loads(jsonval[\"statusMessage\"]) if \"statusMessage\" in jsonval else {}\n", "\n", "df_deletions.properties = df_deletions.properties.map(transform)\n", "df_deletions = EventServiceEntriesAnalyzer.get_error_groups(df_deletions)\n", "\n", "df_deletions = df_deletions[df_deletions.region != \"unknown\"]"]}, {"cell_type": "code", "execution_count": 13, "metadata": {"scrolled": false}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["eastus2euap\n", "{'Succeeded': 1}\n", "{}\n", "\n", "west<PERSON>\n", "{}\n", "{}\n", "\n", "eastus2\n", "{}\n", "{}\n", "\n", "westeurope\n", "{}\n", "{}\n", "\n", "northeurope\n", "{}\n", "{}\n", "\n", "eastus\n", "{'Succeeded': 1}\n", "{}\n", "\n", "seastasia\n", "{'Succeeded': 4}\n", "{}\n", "\n", "eastasia\n", "{}\n", "{}\n", "\n", "<PERSON><PERSON><PERSON>\n", "{'Succeeded': 7}\n", "{}\n", "\n", "ncentralus\n", "{}\n", "{}\n", "\n", "westus2\n", "{}\n", "{}\n", "\n", "centralus\n", "{}\n", "{}\n", "\n", "ukwest\n", "{}\n", "{}\n", "\n", "uksouth\n", "{}\n", "{}\n", "\n", "auseast\n", "{}\n", "{}\n", "\n", "ausseast\n", "{}\n", "{}\n", "\n", "auscentral\n", "{}\n", "{}\n", "\n", "auscentral2\n", "{}\n", "{}\n", "\n", "japaneast\n", "{'Succeeded': 11, 'Failed': 2}\n", "{'ScopeLocked:NA': 2}\n", "/subscriptions/5b399a8c-17ee-4296-81b0-fa304793ef7b/resourceGroups/adb-cost-test-premium2/providers/Microsoft.Databricks/workspaces/b1-dna-adb-ws-pre-cost-test2\n", "/subscriptions/5b399a8c-17ee-4296-81b0-fa304793ef7b/resourceGroups/adb-cost-test-standard2/providers/Microsoft.Databricks/workspaces/b1-dna-adb-ws-cost-test2\n", "\n", "japanwest\n", "{'Succeeded': 3}\n", "{}\n", "\n", "cancentral\n", "{}\n", "{}\n", "\n", "caneast\n", "{'Succeeded': 1}\n", "{}\n", "\n", "indcentral\n", "{'Succeeded': 5}\n", "{}\n", "\n", "indsouth\n", "{'Succeeded': 7}\n", "{}\n", "\n", "indwest\n", "{'Succeeded': 3}\n", "{}\n", "\n"]}], "source": ["regions = [\"eastus2euap\", \"westus\", \"eastus2\", \"westeurope\", \"northeurope\", \"eastus\", \"seastasia\", \n", "           \"eastasia\", \"scentralus\", \"ncentralus\", \"westus2\", \"centralus\", \"ukwest\", \"uksouth\", \n", "           \"auseast\", \"ausseast\", \"auscentral\", \"auscentral2\", \"japaneast\", \"japanwest\", \"cancentral\", \"caneast\",\n", "           \"indcentral\", \"indsouth\", \"indwest\"]\n", "\n", "unique_creations = df_deletions.drop_duplicates(\"resourceUri\")\n", "\n", "for current_region in regions:\n", "    print(current_region)\n", "    df_region = unique_creations[unique_creations.region == current_region]\n", "    print(dict(df_region.status.value_counts()))\n", "    print(dict(df_region[df_region.status == \"Failed\"].error_group.value_counts()))\n", "    for resourceUri in list(df_region[df_region.status == \"Failed\"].resourceUri):\n", "        print(resourceUri)\n", "    print()"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.6.4"}}, "nbformat": 4, "nbformat_minor": 2}