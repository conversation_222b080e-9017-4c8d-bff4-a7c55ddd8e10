{"cells": [{"cell_type": "code", "execution_count": 20, "metadata": {"scrolled": false}, "outputs": [{"data": {"text/plain": ["'2019-02-01 08:51:24.450507'"]}, "execution_count": 20, "metadata": {}, "output_type": "execute_result"}], "source": ["from datetime import datetime\n", "str(datetime.now())"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Workspace Creations by Region\n", "\n", "Below we look at workspace creations to the new RP by region. We use the EventServiceEntriesAnalyzer.get_error_groups (in the kustoutils.py file in this repo) to break the errors into groups. <br>\n", "\n", "The empty regions have not been switched over yet."]}, {"cell_type": "code", "execution_count": 1, "metadata": {"scrolled": false}, "outputs": [], "source": ["from kustoutils import KustoCaller, EventServiceEntriesAnalyzer\n", "import pandas as pd\n", "import warnings\n", "import json\n", "warnings.filterwarnings('ignore')\n", "\n", "pd.set_option('display.max_colwidth', -1)\n", "\n", "kc = KustoCaller()\n", "query = \"\"\"\n", "HttpIncomingRequests\n", "| where TIMESTAMP >= ago(1d)\n", "| where httpStatusCode > 0\n", "| where subscriptionId != \"0140911e-1040-48da-8bc9-b99fb3dd88a6\"\n", "| where targetUri contains \"Microsoft.Databricks\"\n", "| where httpMethod == \"PUT\"\n", "| project TIMESTAMP, subscriptionId, httpMethod, httpStatusCode, targetUri\n", "| order by subscriptionId, TIMESTAMP asc\n", "\"\"\"\n", "df_puts = kc.query(query, kc.RP_DB, kc.RP_CLUSTER)\n", "\n", "query = \"\"\"\n", "EventServiceEntries \n", "| where TIMESTAMP >= ago(1d)\n", "| where subscriptionId != \"0140911e-1040-48da-8bc9-b99fb3dd88a6\"\n", "| where operationName contains \"Microsoft.Databricks/workspaces/write\"\n", "| where status == \"Failed\" or status == \"Succeeded\"\n", "| project TIMESTAMP, subscriptionId, resourceUri, properties, status\n", "\"\"\"\n", "\n", "df_creations = kc.query(query, kc.ARM_DB, kc.ARM_CLUSTER)\n", "\n", "def get_region(resourceUri: str):\n", "    matchingputs= df_puts[df_puts.targetUri.str.contains(resourceUri)]\n", "    if(len(matchingputs) == 0):\n", "        return \"unknown\"\n", "    targetUri = matchingputs.iloc[0].targetUri\n", "    return targetUri[8:targetUri.index(\".adb-spearfish\")]\n", "    \n", "df_creations[\"region\"] = df_creations.resourceUri.apply(get_region)\n", "\n", "def transform(value):\n", "    if(value == \"\"):\n", "        return {}\n", "    jsonval = json.loads(value)\n", "    return json.loads(jsonval[\"statusMessage\"]) if \"statusMessage\" in jsonval else {}\n", "\n", "df_creations.properties = df_creations.properties.map(transform)\n", "df_creations = EventServiceEntriesAnalyzer.get_error_groups(df_creations)"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>TIMESTAMP</th>\n", "      <th>subscriptionId</th>\n", "      <th>resourceUri</th>\n", "      <th>properties</th>\n", "      <th>status</th>\n", "      <th>region</th>\n", "      <th>outer_error</th>\n", "      <th>inner_error</th>\n", "      <th>error_group</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>1183</th>\n", "      <td>2019-02-01 21:24:08.302401400</td>\n", "      <td>5ed53004-0643-4a84-896d-d9089624e6cf</td>\n", "      <td>/subscriptions/5ed53004-0643-4a84-896d-d9089624e6cf/resourcegroups/davivienda/providers/microsoft.databricks/workspaces/davibricks</td>\n", "      <td>{}</td>\n", "      <td>Succeeded</td>\n", "      <td>eastus2</td>\n", "      <td>None</td>\n", "      <td>NA</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>15</th>\n", "      <td>2019-02-01 21:23:04.090057500</td>\n", "      <td>0c8ee94f-e9a1-4b8a-8bf5-12845b6c165b</td>\n", "      <td>/subscriptions/0c8ee94f-e9a1-4b8a-8bf5-12845b6c165b/resourcegroups/rg-digital-dataeg/providers/microsoft.databricks/workspaces/dde-dev-databricks-vi-001</td>\n", "      <td>{'error': {'code': 'ApplicationUpdateFail', 'message': 'Failed to update application: 'dde-dev-databricks-vi-001', because patch resource group failure.', 'details': [{'code': 'RequestDisallowedByPolicy', 'message': 'Resource \\'workers-vnet\\' was disallowed by policy. Policy identifiers: \\'[{\"policyAssignment\":{\"name\":\"Not allowed resource types\",\"id\":\"/subscriptions/0c8ee94f-e9a1-4b8a-8bf5-12845b6c165b/providers/Microsoft.Authorization/policyAssignments/02902ccc2d094a458ef2e9f7\"},\"policyDefinition\":{\"name\":\"Not allowed resource types\",\"id\":\"/providers/Microsoft.Authorization/policyDefinitions/6c112d4e-5bc7-47ae-a041-ea2d9dccd749\"}}]\\'.'}]}}</td>\n", "      <td>Failed</td>\n", "      <td>unknown</td>\n", "      <td>ApplicationUpdateFail</td>\n", "      <td>PolicyError</td>\n", "      <td>ApplicationUpdateFail:PolicyError</td>\n", "    </tr>\n", "    <tr>\n", "      <th>16</th>\n", "      <td>2019-02-01 21:21:17.838191600</td>\n", "      <td>a6845992-d9d0-4a3d-b9cd-377452e8cc03</td>\n", "      <td>/subscriptions/a6845992-d9d0-4a3d-b9cd-377452e8cc03/resourcegroups/jubilant/providers/microsoft.databricks/workspaces/jubilant</td>\n", "      <td>{}</td>\n", "      <td>Succeeded</td>\n", "      <td>eastus2</td>\n", "      <td>None</td>\n", "      <td>NA</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>2019-02-01 21:20:08.539659500</td>\n", "      <td>f5e09b0c-0b06-4e03-8b77-29893f7faed2</td>\n", "      <td>/subscriptions/f5e09b0c-0b06-4e03-8b77-29893f7faed2/resourcegroups/pv-test-resource-group/providers/microsoft.databricks/workspaces/pv-test</td>\n", "      <td>{}</td>\n", "      <td>Succeeded</td>\n", "      <td>westus</td>\n", "      <td>None</td>\n", "      <td>NA</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>496</th>\n", "      <td>2019-02-01 21:17:49.648053100</td>\n", "      <td>ab0fe328-55c9-43d7-997d-e8a310b6d70c</td>\n", "      <td>/subscriptions/ab0fe328-55c9-43d7-997d-e8a310b6d70c/resourcegroups/testdeployr/providers/microsoft.databricks/workspaces/dpadatabricksspace</td>\n", "      <td>{}</td>\n", "      <td>Succeeded</td>\n", "      <td>westus</td>\n", "      <td>None</td>\n", "      <td>NA</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>497</th>\n", "      <td>2019-02-01 21:16:36.221362600</td>\n", "      <td>ff643ad9-ac83-4a4d-beef-1f4414d96a0f</td>\n", "      <td>/subscriptions/ff643ad9-ac83-4a4d-beef-1f4414d96a0f/resourcegroups/wpaappsrg-20190201130350/providers/microsoft.databricks/workspaces/wpaappsdb20190201130350</td>\n", "      <td>{}</td>\n", "      <td>Succeeded</td>\n", "      <td>eastus2</td>\n", "      <td>None</td>\n", "      <td>NA</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>870</th>\n", "      <td>2019-02-01 21:14:56.824315800</td>\n", "      <td>a6845992-d9d0-4a3d-b9cd-377452e8cc03</td>\n", "      <td>/subscriptions/a6845992-d9d0-4a3d-b9cd-377452e8cc03/resourcegroups/jubilant_poc/providers/microsoft.databricks/workspaces/julilant</td>\n", "      <td>{}</td>\n", "      <td>Succeeded</td>\n", "      <td>eastus</td>\n", "      <td>None</td>\n", "      <td>NA</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>720</th>\n", "      <td>2019-02-01 21:12:52.356024700</td>\n", "      <td>8fc7c80a-4850-4f1a-b8df-77cf727b1f30</td>\n", "      <td>/subscriptions/8fc7c80a-4850-4f1a-b8df-77cf727b1f30/resourcegroups/databricks-demo/providers/microsoft.databricks/workspaces/workspace</td>\n", "      <td>{}</td>\n", "      <td>Succeeded</td>\n", "      <td>westeurope</td>\n", "      <td>None</td>\n", "      <td>NA</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>721</th>\n", "      <td>2019-02-01 21:10:15.037901200</td>\n", "      <td>dd4c49b8-8c99-4c52-8cdf-59a04e7ab308</td>\n", "      <td>/subscriptions/dd4c49b8-8c99-4c52-8cdf-59a04e7ab308/resourcegroups/incrementaldataload/providers/microsoft.databricks/workspaces/databricks0201</td>\n", "      <td>{}</td>\n", "      <td>Succeeded</td>\n", "      <td>westus</td>\n", "      <td>None</td>\n", "      <td>NA</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1024</th>\n", "      <td>2019-02-01 21:09:07.448884900</td>\n", "      <td>ab0fe328-55c9-43d7-997d-e8a310b6d70c</td>\n", "      <td>/subscriptions/ab0fe328-55c9-43d7-997d-e8a310b6d70c/resourcegroups/testdeployq/providers/microsoft.databricks/workspaces/dpadatabricksspace</td>\n", "      <td>{'error': {'code': 'InvalidApplicationManagedResourceGroupId', 'message': 'The managed resource group identifier is invalid. Please provide fully qualified identifier of the resource group for the application's resources.'}}</td>\n", "      <td>Failed</td>\n", "      <td>westus</td>\n", "      <td>InvalidApplicationManagedResourceGroupId</td>\n", "      <td>NA</td>\n", "      <td>InvalidApplicationManagedResourceGroupId:NA</td>\n", "    </tr>\n", "    <tr>\n", "      <th>296</th>\n", "      <td>2019-02-01 21:04:32.802839800</td>\n", "      <td>b45fc77d-d8a1-4661-9747-05e03f484b63</td>\n", "      <td>/subscriptions/b45fc77d-d8a1-4661-9747-05e03f484b63/resourcegroups/learning-acc72162/providers/microsoft.databricks/workspaces/azuredatabricks-72162</td>\n", "      <td>{}</td>\n", "      <td>Succeeded</td>\n", "      <td>eastus</td>\n", "      <td>None</td>\n", "      <td>NA</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>303</th>\n", "      <td>2019-02-01 21:04:32.143787400</td>\n", "      <td>b45fc77d-d8a1-4661-9747-05e03f484b63</td>\n", "      <td>/subscriptions/b45fc77d-d8a1-4661-9747-05e03f484b63/resourcegroups/learning-acc72156/providers/microsoft.databricks/workspaces/azuredatabricks-72156</td>\n", "      <td>{}</td>\n", "      <td>Succeeded</td>\n", "      <td>eastus</td>\n", "      <td>None</td>\n", "      <td>NA</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1003</th>\n", "      <td>2019-02-01 21:04:27.100557600</td>\n", "      <td>58a32ebe-b50f-4ecd-a4ee-355e4459de4b</td>\n", "      <td>/subscriptions/58a32ebe-b50f-4ecd-a4ee-355e4459de4b/resourcegroups/learning-acc72157/providers/microsoft.databricks/workspaces/azuredatabricks-72157</td>\n", "      <td>{}</td>\n", "      <td>Succeeded</td>\n", "      <td>eastus</td>\n", "      <td>None</td>\n", "      <td>NA</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1004</th>\n", "      <td>2019-02-01 21:04:12.503961400</td>\n", "      <td>bc588d10-be88-4b91-bc23-6921cf5de60f</td>\n", "      <td>/subscriptions/bc588d10-be88-4b91-bc23-6921cf5de60f/resourcegroups/learning-acc72161/providers/microsoft.databricks/workspaces/azuredatabricks-72161</td>\n", "      <td>{}</td>\n", "      <td>Succeeded</td>\n", "      <td>eastus</td>\n", "      <td>None</td>\n", "      <td>NA</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1006</th>\n", "      <td>2019-02-01 21:04:09.280807300</td>\n", "      <td>b45fc77d-d8a1-4661-9747-05e03f484b63</td>\n", "      <td>/subscriptions/b45fc77d-d8a1-4661-9747-05e03f484b63/resourcegroups/learning-acc72153/providers/microsoft.databricks/workspaces/azuredatabricks-72153</td>\n", "      <td>{}</td>\n", "      <td>Succeeded</td>\n", "      <td>eastus</td>\n", "      <td>None</td>\n", "      <td>NA</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1007</th>\n", "      <td>2019-02-01 21:04:08.125118800</td>\n", "      <td>bc588d10-be88-4b91-bc23-6921cf5de60f</td>\n", "      <td>/subscriptions/bc588d10-be88-4b91-bc23-6921cf5de60f/resourcegroups/learning-acc72158/providers/microsoft.databricks/workspaces/azuredatabricks-72158</td>\n", "      <td>{}</td>\n", "      <td>Succeeded</td>\n", "      <td>eastus</td>\n", "      <td>None</td>\n", "      <td>NA</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>550</th>\n", "      <td>2019-02-01 21:04:07.714962900</td>\n", "      <td>bc588d10-be88-4b91-bc23-6921cf5de60f</td>\n", "      <td>/subscriptions/bc588d10-be88-4b91-bc23-6921cf5de60f/resourcegroups/learning-acc72155/providers/microsoft.databricks/workspaces/azuredatabricks-72155</td>\n", "      <td>{}</td>\n", "      <td>Succeeded</td>\n", "      <td>eastus</td>\n", "      <td>None</td>\n", "      <td>NA</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>295</th>\n", "      <td>2019-02-01 21:02:21.757598200</td>\n", "      <td>b45fc77d-d8a1-4661-9747-05e03f484b63</td>\n", "      <td>/subscriptions/b45fc77d-d8a1-4661-9747-05e03f484b63/resourcegroups/learning-acc72159/providers/microsoft.databricks/workspaces/azuredatabricks-72159</td>\n", "      <td>{}</td>\n", "      <td>Succeeded</td>\n", "      <td>eastus</td>\n", "      <td>None</td>\n", "      <td>NA</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>304</th>\n", "      <td>2019-02-01 21:02:21.157662500</td>\n", "      <td>58a32ebe-b50f-4ecd-a4ee-355e4459de4b</td>\n", "      <td>/subscriptions/58a32ebe-b50f-4ecd-a4ee-355e4459de4b/resourcegroups/learning-acc72160/providers/microsoft.databricks/workspaces/azuredatabricks-72160</td>\n", "      <td>{}</td>\n", "      <td>Succeeded</td>\n", "      <td>eastus</td>\n", "      <td>None</td>\n", "      <td>NA</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>298</th>\n", "      <td>2019-02-01 21:02:16.835334000</td>\n", "      <td>58a32ebe-b50f-4ecd-a4ee-355e4459de4b</td>\n", "      <td>/subscriptions/58a32ebe-b50f-4ecd-a4ee-355e4459de4b/resourcegroups/learning-acc72154/providers/microsoft.databricks/workspaces/azuredatabricks-72154</td>\n", "      <td>{}</td>\n", "      <td>Succeeded</td>\n", "      <td>eastus</td>\n", "      <td>None</td>\n", "      <td>NA</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>547</th>\n", "      <td>2019-02-01 21:00:31.341284000</td>\n", "      <td>839ef081-3052-441a-b1ed-6ed09e1153ba</td>\n", "      <td>/subscriptions/839ef081-3052-441a-b1ed-6ed09e1153ba/resourcegroups/trp_x_001/providers/microsoft.databricks/workspaces/4tpxzdbs001</td>\n", "      <td>{}</td>\n", "      <td>Succeeded</td>\n", "      <td>unknown</td>\n", "      <td>None</td>\n", "      <td>NA</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>546</th>\n", "      <td>2019-02-01 20:57:10.313523000</td>\n", "      <td>839ef081-3052-441a-b1ed-6ed09e1153ba</td>\n", "      <td>/subscriptions/839ef081-3052-441a-b1ed-6ed09e1153ba/resourcegroups/jlr_x_001/providers/microsoft.databricks/workspaces/jlrx001databricks001</td>\n", "      <td>{}</td>\n", "      <td>Succeeded</td>\n", "      <td>unknown</td>\n", "      <td>None</td>\n", "      <td>NA</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1043</th>\n", "      <td>2019-02-01 20:57:09.363026600</td>\n", "      <td>839ef081-3052-441a-b1ed-6ed09e1153ba</td>\n", "      <td>/subscriptions/839ef081-3052-441a-b1ed-6ed09e1153ba/resourcegroups/ejd_x_001/providers/microsoft.databricks/workspaces/smartspacedbks</td>\n", "      <td>{}</td>\n", "      <td>Succeeded</td>\n", "      <td>unknown</td>\n", "      <td>None</td>\n", "      <td>NA</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>834</th>\n", "      <td>2019-02-01 20:56:33.342241700</td>\n", "      <td>8fc7c80a-4850-4f1a-b8df-77cf727b1f30</td>\n", "      <td>/subscriptions/8fc7c80a-4850-4f1a-b8df-77cf727b1f30/resourcegroups/databricks-test/providers/microsoft.databricks/workspaces/databricks</td>\n", "      <td>{}</td>\n", "      <td>Succeeded</td>\n", "      <td>westeurope</td>\n", "      <td>None</td>\n", "      <td>NA</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1044</th>\n", "      <td>2019-02-01 20:56:14.540279600</td>\n", "      <td>839ef081-3052-441a-b1ed-6ed09e1153ba</td>\n", "      <td>/subscriptions/839ef081-3052-441a-b1ed-6ed09e1153ba/resourcegroups/chc_x_001/providers/microsoft.databricks/workspaces/db1</td>\n", "      <td>{}</td>\n", "      <td>Succeeded</td>\n", "      <td>unknown</td>\n", "      <td>None</td>\n", "      <td>NA</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>28</th>\n", "      <td>2019-02-01 20:54:56.925433600</td>\n", "      <td>839ef081-3052-441a-b1ed-6ed09e1153ba</td>\n", "      <td>/subscriptions/839ef081-3052-441a-b1ed-6ed09e1153ba/resourcegroups/3psa_t_001/providers/microsoft.databricks/workspaces/3psatzdbs001</td>\n", "      <td>{}</td>\n", "      <td>Succeeded</td>\n", "      <td>unknown</td>\n", "      <td>None</td>\n", "      <td>NA</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>166</th>\n", "      <td>2019-02-01 20:54:37.475556600</td>\n", "      <td>839ef081-3052-441a-b1ed-6ed09e1153ba</td>\n", "      <td>/subscriptions/839ef081-3052-441a-b1ed-6ed09e1153ba/resourcegroups/3psa_ddbs_001/providers/microsoft.databricks/workspaces/3psadzdbs001</td>\n", "      <td>{}</td>\n", "      <td>Succeeded</td>\n", "      <td>unknown</td>\n", "      <td>None</td>\n", "      <td>NA</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1041</th>\n", "      <td>2019-02-01 20:54:05.821526400</td>\n", "      <td>df236553-2478-44d3-a074-dcf28bc24ae3</td>\n", "      <td>/subscriptions/df236553-2478-44d3-a074-dcf28bc24ae3/resourcegroups/acuacar/providers/microsoft.databricks/workspaces/acuacardbricks</td>\n", "      <td>{}</td>\n", "      <td>Succeeded</td>\n", "      <td>westus</td>\n", "      <td>None</td>\n", "      <td>NA</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>164</th>\n", "      <td>2019-02-01 20:53:43.746810100</td>\n", "      <td>ab0fe328-55c9-43d7-997d-e8a310b6d70c</td>\n", "      <td>/subscriptions/ab0fe328-55c9-43d7-997d-e8a310b6d70c/resourcegroups/testdeployp/providers/microsoft.databricks/workspaces/dpadatabricksspace</td>\n", "      <td>{'error': {'code': 'InvalidApplicationManagedResourceGroupId', 'message': 'The managed resource group identifier is invalid. Please provide fully qualified identifier of the resource group for the application's resources.'}}</td>\n", "      <td>Failed</td>\n", "      <td>westus</td>\n", "      <td>InvalidApplicationManagedResourceGroupId</td>\n", "      <td>NA</td>\n", "      <td>InvalidApplicationManagedResourceGroupId:NA</td>\n", "    </tr>\n", "    <tr>\n", "      <th>56</th>\n", "      <td>2019-02-01 20:51:37.703215400</td>\n", "      <td>536c87a1-3ba3-4a16-8995-79b573336a15</td>\n", "      <td>/subscriptions/536c87a1-3ba3-4a16-8995-79b573336a15/resourcegroups/dpt_poc_use-1/providers/microsoft.databricks/workspaces/bg-databricks-premium-tier</td>\n", "      <td>{}</td>\n", "      <td>Succeeded</td>\n", "      <td>eastus</td>\n", "      <td>None</td>\n", "      <td>NA</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>968</th>\n", "      <td>2019-01-31 23:16:08.671685600</td>\n", "      <td>ccb3ad09-cba9-44c2-b6c7-884753fcd58c</td>\n", "      <td>/subscriptions/ccb3ad09-cba9-44c2-b6c7-884753fcd58c/resourcegroups/abhi-ibp-vnetinj-pubpreview-rg/providers/microsoft.databricks/workspaces/abhi-ibp-vnetinj-pubpreview-ws</td>\n", "      <td>{}</td>\n", "      <td>Succeeded</td>\n", "      <td>eastus2</td>\n", "      <td>None</td>\n", "      <td>NA</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>967</th>\n", "      <td>2019-01-31 23:14:11.599520500</td>\n", "      <td>31f3fdf6-27b7-4cfb-904d-bf1c5b8c1371</td>\n", "      <td>/subscriptions/31f3fdf6-27b7-4cfb-904d-bf1c5b8c1371/resourcegroups/edap-prod-rg-test4/providers/microsoft.databricks/workspaces/edap-prod-rg-test4-workspace</td>\n", "      <td>{}</td>\n", "      <td>Succeeded</td>\n", "      <td>centralus</td>\n", "      <td>None</td>\n", "      <td>NA</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>331</th>\n", "      <td>2019-01-31 23:08:02.270002100</td>\n", "      <td>89d52744-be0e-4971-9a6d-bfa378d99341</td>\n", "      <td>/subscriptions/89d52744-be0e-4971-9a6d-bfa378d99341/resourcegroups/casmelo-ml/providers/microsoft.databricks/workspaces/casmelo-ads</td>\n", "      <td>{}</td>\n", "      <td>Succeeded</td>\n", "      <td>westus</td>\n", "      <td>None</td>\n", "      <td>NA</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>756</th>\n", "      <td>2019-01-31 22:52:27.275892400</td>\n", "      <td>3a89d508-f992-4729-9058-ba4fae9a35ca</td>\n", "      <td>/subscriptions/3a89d508-f992-4729-9058-ba4fae9a35ca/resourcegroups/sandbox-335544-002-rg/providers/microsoft.databricks/workspaces/ffr-databricks3</td>\n", "      <td>{}</td>\n", "      <td>Succeeded</td>\n", "      <td>unknown</td>\n", "      <td>None</td>\n", "      <td>NA</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>476</th>\n", "      <td>2019-01-31 22:45:39.629790200</td>\n", "      <td>2db6db0c-9da5-4ec7-a9b3-1affc620bf3e</td>\n", "      <td>/subscriptions/2db6db0c-9da5-4ec7-a9b3-1affc620bf3e/resourcegroups/datalakeetl-prod-group/providers/microsoft.databricks/workspaces/eajobs-prod-adbwspace-cus</td>\n", "      <td>{'error': {'code': 'MissingSubscriptionRegistration', 'message': 'The subscription is not registered to use namespace 'Microsoft.Databricks'. See https://aka.ms/rps-not-found for how to register subscriptions.', 'details': [{'code': 'MissingSubscriptionRegistration', 'target': 'Microsoft.Databricks', 'message': \"The subscription is not registered to use namespace 'Microsoft.Databricks'. See https://aka.ms/rps-not-found for how to register subscriptions.\"}]}}</td>\n", "      <td>Failed</td>\n", "      <td>unknown</td>\n", "      <td>MissingSubscriptionRegistration</td>\n", "      <td>MissingSubscriptionRegistration</td>\n", "      <td>MissingSubscriptionRegistration:MissingSubscriptionRegistration</td>\n", "    </tr>\n", "    <tr>\n", "      <th>477</th>\n", "      <td>2019-01-31 22:44:18.863732900</td>\n", "      <td>d935d65e-c63a-4e87-b51d-1ca3d1498d2d</td>\n", "      <td>/subscriptions/d935d65e-c63a-4e87-b51d-1ca3d1498d2d/resourcegroups/wpaappsrg-20190131143044/providers/microsoft.databricks/workspaces/wpaappsdb20190131143044</td>\n", "      <td>{}</td>\n", "      <td>Succeeded</td>\n", "      <td>westus2</td>\n", "      <td>None</td>\n", "      <td>NA</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>86</th>\n", "      <td>2019-01-31 22:40:13.596635900</td>\n", "      <td>8ae3a5ac-469f-4cb3-92a3-d4541716c4cb</td>\n", "      <td>/subscriptions/8ae3a5ac-469f-4cb3-92a3-d4541716c4cb/resourcegroups/delfin-dev-resourcegroup/providers/microsoft.databricks/workspaces/a360devspark</td>\n", "      <td>{}</td>\n", "      <td>Succeeded</td>\n", "      <td>westus</td>\n", "      <td>None</td>\n", "      <td>NA</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1401</th>\n", "      <td>2019-01-31 22:34:48.628410800</td>\n", "      <td>721f1036-94cb-48e5-8d11-eb7b8e082b19</td>\n", "      <td>/subscriptions/721f1036-94cb-48e5-8d11-eb7b8e082b19/resourcegroups/ryu-spsanalytics/providers/microsoft.databricks/workspaces/ryudatabricks</td>\n", "      <td>{}</td>\n", "      <td>Succeeded</td>\n", "      <td>westus2</td>\n", "      <td>None</td>\n", "      <td>NA</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>183</th>\n", "      <td>2019-01-31 22:32:58.997164500</td>\n", "      <td>36f75872-9ace-4c20-911c-aea8eba2945c</td>\n", "      <td>/subscriptions/36f75872-9ace-4c20-911c-aea8eba2945c/resourcegroups/mike-test-rg/providers/microsoft.databricks/workspaces/mike-test-wksp</td>\n", "      <td>{}</td>\n", "      <td>Succeeded</td>\n", "      <td>westus</td>\n", "      <td>None</td>\n", "      <td>NA</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>182</th>\n", "      <td>2019-01-31 22:30:47.532719300</td>\n", "      <td>7e33d792-86ef-4706-b2c9-a5c6a903ba27</td>\n", "      <td>/subscriptions/7e33d792-86ef-4706-b2c9-a5c6a903ba27/resourcegroups/datalake/providers/microsoft.databricks/workspaces/apposita-bricks</td>\n", "      <td>{}</td>\n", "      <td>Succeeded</td>\n", "      <td>northeurope</td>\n", "      <td>None</td>\n", "      <td>NA</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>107</th>\n", "      <td>2019-01-31 22:27:27.492669500</td>\n", "      <td>9a7a1d1b-21ee-417e-8096-70fd52730e24</td>\n", "      <td>/subscriptions/9a7a1d1b-21ee-417e-8096-70fd52730e24/resourcegroups/spark/providers/microsoft.databricks/workspaces/gxdb</td>\n", "      <td>{}</td>\n", "      <td>Succeeded</td>\n", "      <td>auseast</td>\n", "      <td>None</td>\n", "      <td>NA</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>85</th>\n", "      <td>2019-01-31 22:27:05.417860800</td>\n", "      <td>15862453-f8cb-4f50-ba1f-f2547d153a22</td>\n", "      <td>/subscriptions/15862453-f8cb-4f50-ba1f-f2547d153a22/resourcegroups/data_science_pilot/providers/microsoft.databricks/workspaces/db_ws_load_rec</td>\n", "      <td>{}</td>\n", "      <td>Succeeded</td>\n", "      <td><PERSON><PERSON>us</td>\n", "      <td>None</td>\n", "      <td>NA</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1001</th>\n", "      <td>2019-01-31 22:22:22.559937800</td>\n", "      <td>b3813a92-d36f-494c-b94a-6007ebad207f</td>\n", "      <td>/subscriptions/b3813a92-d36f-494c-b94a-6007ebad207f/resourcegroups/gm_rsd_dev-cdw-rg/providers/microsoft.databricks/workspaces/ak-1</td>\n", "      <td>{}</td>\n", "      <td>Succeeded</td>\n", "      <td>eastus2</td>\n", "      <td>None</td>\n", "      <td>NA</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>106</th>\n", "      <td>2019-01-31 22:16:54.098978600</td>\n", "      <td>99048da9-23a9-4a39-95c0-ad006ba02d88</td>\n", "      <td>/subscriptions/99048da9-23a9-4a39-95c0-ad006ba02d88/resourcegroups/rg1/providers/microsoft.databricks/workspaces/xian-databricks_test</td>\n", "      <td>{}</td>\n", "      <td>Succeeded</td>\n", "      <td>eastus</td>\n", "      <td>None</td>\n", "      <td>NA</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>2019-01-31 22:15:27.435750300</td>\n", "      <td>d935d65e-c63a-4e87-b51d-1ca3d1498d2d</td>\n", "      <td>/subscriptions/d935d65e-c63a-4e87-b51d-1ca3d1498d2d/resourcegroups/wpaappsrg-20190131140129/providers/microsoft.databricks/workspaces/wpaappsdb20190131140129</td>\n", "      <td>{}</td>\n", "      <td>Succeeded</td>\n", "      <td>westus2</td>\n", "      <td>None</td>\n", "      <td>NA</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>879</th>\n", "      <td>2019-01-31 22:14:58.552970800</td>\n", "      <td>b45fc77d-d8a1-4661-9747-05e03f484b63</td>\n", "      <td>/subscriptions/b45fc77d-d8a1-4661-9747-05e03f484b63/resourcegroups/learning-acc72144/providers/microsoft.databricks/workspaces/azuredatabricks-72144</td>\n", "      <td>{}</td>\n", "      <td>Succeeded</td>\n", "      <td>eastus</td>\n", "      <td>None</td>\n", "      <td>NA</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>290</th>\n", "      <td>2019-01-31 22:13:34.912955500</td>\n", "      <td>2a852612-0bb6-4a90-bc05-77d9eb821369</td>\n", "      <td>/subscriptions/2a852612-0bb6-4a90-bc05-77d9eb821369/resourcegroups/test/providers/microsoft.databricks/workspaces/test_service</td>\n", "      <td>{}</td>\n", "      <td>Succeeded</td>\n", "      <td>westus</td>\n", "      <td>None</td>\n", "      <td>NA</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>681</th>\n", "      <td>2019-01-31 22:13:26.901471200</td>\n", "      <td>667bd743-cbc2-4f89-b30e-5548f4c58d53</td>\n", "      <td>/subscriptions/667bd743-cbc2-4f89-b30e-5548f4c58d53/resourcegroups/databricks-resource-group/providers/microsoft.databricks/workspaces/anadbwsp01nc05-test</td>\n", "      <td>{}</td>\n", "      <td>Succeeded</td>\n", "      <td>centralus</td>\n", "      <td>None</td>\n", "      <td>NA</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>878</th>\n", "      <td>2019-01-31 22:13:15.961015300</td>\n", "      <td>8dc39156-b35f-4805-8a35-be868fb796ba</td>\n", "      <td>/subscriptions/8dc39156-b35f-4805-8a35-be868fb796ba/resourcegroups/test/providers/microsoft.databricks/workspaces/test</td>\n", "      <td>{}</td>\n", "      <td>Succeeded</td>\n", "      <td>westus</td>\n", "      <td>None</td>\n", "      <td>NA</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>654</th>\n", "      <td>2019-01-31 22:01:50.179803200</td>\n", "      <td>33d475ff-18e3-4e93-838d-77b07598541e</td>\n", "      <td>/subscriptions/33d475ff-18e3-4e93-838d-77b07598541e/resourcegroups/bhac-int/providers/microsoft.databricks/workspaces/databricksprototype</td>\n", "      <td>{}</td>\n", "      <td>Succeeded</td>\n", "      <td>unknown</td>\n", "      <td>None</td>\n", "      <td>NA</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1002</th>\n", "      <td>2019-01-31 21:59:15.990406900</td>\n", "      <td>af037760-5e1f-4676-b2db-7d5a38063d4f</td>\n", "      <td>/subscriptions/af037760-5e1f-4676-b2db-7d5a38063d4f/resourcegroups/rg-fit-d01/providers/microsoft.databricks/workspaces/adb-fit-tsi-d02</td>\n", "      <td>{}</td>\n", "      <td>Succeeded</td>\n", "      <td>westus2</td>\n", "      <td>None</td>\n", "      <td>NA</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>289</th>\n", "      <td>2019-01-31 21:58:02.749881200</td>\n", "      <td>58a32ebe-b50f-4ecd-a4ee-355e4459de4b</td>\n", "      <td>/subscriptions/58a32ebe-b50f-4ecd-a4ee-355e4459de4b/resourcegroups/learning-acc72143/providers/microsoft.databricks/workspaces/azuredatabricks-72143</td>\n", "      <td>{}</td>\n", "      <td>Succeeded</td>\n", "      <td>eastus</td>\n", "      <td>None</td>\n", "      <td>NA</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>460</th>\n", "      <td>2019-01-31 21:53:19.898092300</td>\n", "      <td>77692cf6-f0b1-4b80-953d-5d6cc589d04f</td>\n", "      <td>/subscriptions/77692cf6-f0b1-4b80-953d-5d6cc589d04f/resourcegroups/development/providers/microsoft.databricks/workspaces/adcpadbs</td>\n", "      <td>{}</td>\n", "      <td>Succeeded</td>\n", "      <td>eastus</td>\n", "      <td>None</td>\n", "      <td>NA</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>122</th>\n", "      <td>2019-01-31 21:48:08.987249300</td>\n", "      <td>90b22448-277d-4150-9cea-0e0637477af6</td>\n", "      <td>/subscriptions/90b22448-277d-4150-9cea-0e0637477af6/resourcegroups/databricksrg2/providers/microsoft.databricks/workspaces/dataricks2</td>\n", "      <td>{}</td>\n", "      <td>Succeeded</td>\n", "      <td><PERSON><PERSON>us</td>\n", "      <td>None</td>\n", "      <td>NA</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>10</th>\n", "      <td>2019-01-31 21:47:59.344368300</td>\n", "      <td>ea40fe7e-16ad-4db8-8d2f-ac9cb8d79712</td>\n", "      <td>/subscriptions/ea40fe7e-16ad-4db8-8d2f-ac9cb8d79712/resourcegroups/desa-comtrade-test-westeurope-datafactory-rg/providers/microsoft.databricks/workspaces/preproctest</td>\n", "      <td>{}</td>\n", "      <td>Succeeded</td>\n", "      <td>westeurope</td>\n", "      <td>None</td>\n", "      <td>NA</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>188</th>\n", "      <td>2019-01-31 21:42:52.217412500</td>\n", "      <td>9a9b2a3c-c51a-43e4-82ee-63b4618d9b36</td>\n", "      <td>/subscriptions/9a9b2a3c-c51a-43e4-82ee-63b4618d9b36/resourcegroups/rg-rajesh-test/providers/microsoft.databricks/workspaces/rajesh-db-test</td>\n", "      <td>{}</td>\n", "      <td>Succeeded</td>\n", "      <td>unknown</td>\n", "      <td>None</td>\n", "      <td>NA</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>458</th>\n", "      <td>2019-01-31 21:42:18.782952900</td>\n", "      <td>b45fc77d-d8a1-4661-9747-05e03f484b63</td>\n", "      <td>/subscriptions/b45fc77d-d8a1-4661-9747-05e03f484b63/resourcegroups/learning-acc72142/providers/microsoft.databricks/workspaces/azuredatabricks-72142</td>\n", "      <td>{}</td>\n", "      <td>Succeeded</td>\n", "      <td>eastus</td>\n", "      <td>None</td>\n", "      <td>NA</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>187</th>\n", "      <td>2019-01-31 21:39:32.140539000</td>\n", "      <td>2869a299-917b-429d-8176-af010f03e04e</td>\n", "      <td>/subscriptions/2869a299-917b-429d-8176-af010f03e04e/resourcegroups/hthdev/providers/microsoft.databricks/workspaces/hthadsdev</td>\n", "      <td>{}</td>\n", "      <td>Succeeded</td>\n", "      <td>unknown</td>\n", "      <td>None</td>\n", "      <td>NA</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>185</th>\n", "      <td>2019-01-31 21:39:27.763868100</td>\n", "      <td>e2b6707b-b235-4a3e-bb73-ab39fb081345</td>\n", "      <td>/subscriptions/e2b6707b-b235-4a3e-bb73-ab39fb081345/resourcegroups/rgadvancedtechnologydev/providers/microsoft.databricks/workspaces/advancedtechnologydev</td>\n", "      <td>{}</td>\n", "      <td>Succeeded</td>\n", "      <td>unknown</td>\n", "      <td>None</td>\n", "      <td>NA</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>12</th>\n", "      <td>2019-01-31 21:35:23.091956600</td>\n", "      <td>139ca47d-ffee-4bea-ba11-19c2adf5a186</td>\n", "      <td>/subscriptions/139ca47d-ffee-4bea-ba11-19c2adf5a186/resourcegroups/application-insights-eastus/providers/microsoft.databricks/workspaces/yingjitrial</td>\n", "      <td>{}</td>\n", "      <td>Succeeded</td>\n", "      <td>unknown</td>\n", "      <td>None</td>\n", "      <td>NA</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>570 rows × 9 columns</p>\n", "</div>"], "text/plain": ["                         TIMESTAMP                        subscriptionId  \\\n", "1183 2019-02-01 21:24:08.302401400  5ed53004-0643-4a84-896d-d9089624e6cf   \n", "15   2019-02-01 21:23:04.090057500  0c8ee94f-e9a1-4b8a-8bf5-12845b6c165b   \n", "16   2019-02-01 21:21:17.838191600  a6845992-d9d0-4a3d-b9cd-377452e8cc03   \n", "3    2019-02-01 21:20:08.539659500  f5e09b0c-0b06-4e03-8b77-29893f7faed2   \n", "496  2019-02-01 21:17:49.648053100  ab0fe328-55c9-43d7-997d-e8a310b6d70c   \n", "497  2019-02-01 21:16:36.221362600  ff643ad9-ac83-4a4d-beef-1f4414d96a0f   \n", "870  2019-02-01 21:14:56.824315800  a6845992-d9d0-4a3d-b9cd-377452e8cc03   \n", "720  2019-02-01 21:12:52.356024700  8fc7c80a-4850-4f1a-b8df-77cf727b1f30   \n", "721  2019-02-01 21:10:15.037901200  dd4c49b8-8c99-4c52-8cdf-59a04e7ab308   \n", "1024 2019-02-01 21:09:07.448884900  ab0fe328-55c9-43d7-997d-e8a310b6d70c   \n", "296  2019-02-01 21:04:32.802839800  b45fc77d-d8a1-4661-9747-05e03f484b63   \n", "303  2019-02-01 21:04:32.143787400  b45fc77d-d8a1-4661-9747-05e03f484b63   \n", "1003 2019-02-01 21:04:27.100557600  58a32ebe-b50f-4ecd-a4ee-355e4459de4b   \n", "1004 2019-02-01 21:04:12.503961400  bc588d10-be88-4b91-bc23-6921cf5de60f   \n", "1006 2019-02-01 21:04:09.280807300  b45fc77d-d8a1-4661-9747-05e03f484b63   \n", "1007 2019-02-01 21:04:08.125118800  bc588d10-be88-4b91-bc23-6921cf5de60f   \n", "550  2019-02-01 21:04:07.714962900  bc588d10-be88-4b91-bc23-6921cf5de60f   \n", "295  2019-02-01 21:02:21.757598200  b45fc77d-d8a1-4661-9747-05e03f484b63   \n", "304  2019-02-01 21:02:21.157662500  58a32ebe-b50f-4ecd-a4ee-355e4459de4b   \n", "298  2019-02-01 21:02:16.835334000  58a32ebe-b50f-4ecd-a4ee-355e4459de4b   \n", "547  2019-02-01 21:00:31.341284000  839ef081-3052-441a-b1ed-6ed09e1153ba   \n", "546  2019-02-01 20:57:10.313523000  839ef081-3052-441a-b1ed-6ed09e1153ba   \n", "1043 2019-02-01 20:57:09.363026600  839ef081-3052-441a-b1ed-6ed09e1153ba   \n", "834  2019-02-01 20:56:33.342241700  8fc7c80a-4850-4f1a-b8df-77cf727b1f30   \n", "1044 2019-02-01 20:56:14.540279600  839ef081-3052-441a-b1ed-6ed09e1153ba   \n", "28   2019-02-01 20:54:56.925433600  839ef081-3052-441a-b1ed-6ed09e1153ba   \n", "166  2019-02-01 20:54:37.475556600  839ef081-3052-441a-b1ed-6ed09e1153ba   \n", "1041 2019-02-01 20:54:05.821526400  df236553-2478-44d3-a074-dcf28bc24ae3   \n", "164  2019-02-01 20:53:43.746810100  ab0fe328-55c9-43d7-997d-e8a310b6d70c   \n", "56   2019-02-01 20:51:37.703215400  536c87a1-3ba3-4a16-8995-79b573336a15   \n", "...                            ...                                   ...   \n", "968  2019-01-31 23:16:08.671685600  ccb3ad09-cba9-44c2-b6c7-884753fcd58c   \n", "967  2019-01-31 23:14:11.599520500  31f3fdf6-27b7-4cfb-904d-bf1c5b8c1371   \n", "331  2019-01-31 23:08:02.270002100  89d52744-be0e-4971-9a6d-bfa378d99341   \n", "756  2019-01-31 22:52:27.275892400  3a89d508-f992-4729-9058-ba4fae9a35ca   \n", "476  2019-01-31 22:45:39.629790200  2db6db0c-9da5-4ec7-a9b3-1affc620bf3e   \n", "477  2019-01-31 22:44:18.863732900  d935d65e-c63a-4e87-b51d-1ca3d1498d2d   \n", "86   2019-01-31 22:40:13.596635900  8ae3a5ac-469f-4cb3-92a3-d4541716c4cb   \n", "1401 2019-01-31 22:34:48.628410800  721f1036-94cb-48e5-8d11-eb7b8e082b19   \n", "183  2019-01-31 22:32:58.997164500  36f75872-9ace-4c20-911c-aea8eba2945c   \n", "182  2019-01-31 22:30:47.532719300  7e33d792-86ef-4706-b2c9-a5c6a903ba27   \n", "107  2019-01-31 22:27:27.492669500  9a7a1d1b-21ee-417e-8096-70fd52730e24   \n", "85   2019-01-31 22:27:05.417860800  15862453-f8cb-4f50-ba1f-f2547d153a22   \n", "1001 2019-01-31 22:22:22.559937800  b3813a92-d36f-494c-b94a-6007ebad207f   \n", "106  2019-01-31 22:16:54.098978600  99048da9-23a9-4a39-95c0-ad006ba02d88   \n", "9    2019-01-31 22:15:27.435750300  d935d65e-c63a-4e87-b51d-1ca3d1498d2d   \n", "879  2019-01-31 22:14:58.552970800  b45fc77d-d8a1-4661-9747-05e03f484b63   \n", "290  2019-01-31 22:13:34.912955500  2a852612-0bb6-4a90-bc05-77d9eb821369   \n", "681  2019-01-31 22:13:26.901471200  667bd743-cbc2-4f89-b30e-5548f4c58d53   \n", "878  2019-01-31 22:13:15.961015300  8dc39156-b35f-4805-8a35-be868fb796ba   \n", "654  2019-01-31 22:01:50.179803200  33d475ff-18e3-4e93-838d-77b07598541e   \n", "1002 2019-01-31 21:59:15.990406900  af037760-5e1f-4676-b2db-7d5a38063d4f   \n", "289  2019-01-31 21:58:02.749881200  58a32ebe-b50f-4ecd-a4ee-355e4459de4b   \n", "460  2019-01-31 21:53:19.898092300  77692cf6-f0b1-4b80-953d-5d6cc589d04f   \n", "122  2019-01-31 21:48:08.987249300  90b22448-277d-4150-9cea-0e0637477af6   \n", "10   2019-01-31 21:47:59.344368300  ea40fe7e-16ad-4db8-8d2f-ac9cb8d79712   \n", "188  2019-01-31 21:42:52.217412500  9a9b2a3c-c51a-43e4-82ee-63b4618d9b36   \n", "458  2019-01-31 21:42:18.782952900  b45fc77d-d8a1-4661-9747-05e03f484b63   \n", "187  2019-01-31 21:39:32.140539000  2869a299-917b-429d-8176-af010f03e04e   \n", "185  2019-01-31 21:39:27.763868100  e2b6707b-b235-4a3e-bb73-ab39fb081345   \n", "12   2019-01-31 21:35:23.091956600  139ca47d-ffee-4bea-ba11-19c2adf5a186   \n", "\n", "                                                                                                                                                                     resourceUri  \\\n", "1183  /subscriptions/5ed53004-0643-4a84-896d-d9089624e6cf/resourcegroups/davivienda/providers/microsoft.databricks/workspaces/davibricks                                           \n", "15    /subscriptions/0c8ee94f-e9a1-4b8a-8bf5-12845b6c165b/resourcegroups/rg-digital-dataeg/providers/microsoft.databricks/workspaces/dde-dev-databricks-vi-001                     \n", "16    /subscriptions/a6845992-d9d0-4a3d-b9cd-377452e8cc03/resourcegroups/jubilant/providers/microsoft.databricks/workspaces/jubilant                                               \n", "3     /subscriptions/f5e09b0c-0b06-4e03-8b77-29893f7faed2/resourcegroups/pv-test-resource-group/providers/microsoft.databricks/workspaces/pv-test                                  \n", "496   /subscriptions/ab0fe328-55c9-43d7-997d-e8a310b6d70c/resourcegroups/testdeployr/providers/microsoft.databricks/workspaces/dpadatabricksspace                                  \n", "497   /subscriptions/ff643ad9-ac83-4a4d-beef-1f4414d96a0f/resourcegroups/wpaappsrg-20190201130350/providers/microsoft.databricks/workspaces/wpaappsdb20190201130350                \n", "870   /subscriptions/a6845992-d9d0-4a3d-b9cd-377452e8cc03/resourcegroups/jubilant_poc/providers/microsoft.databricks/workspaces/julilant                                           \n", "720   /subscriptions/8fc7c80a-4850-4f1a-b8df-77cf727b1f30/resourcegroups/databricks-demo/providers/microsoft.databricks/workspaces/workspace                                       \n", "721   /subscriptions/dd4c49b8-8c99-4c52-8cdf-59a04e7ab308/resourcegroups/incrementaldataload/providers/microsoft.databricks/workspaces/databricks0201                              \n", "1024  /subscriptions/ab0fe328-55c9-43d7-997d-e8a310b6d70c/resourcegroups/testdeployq/providers/microsoft.databricks/workspaces/dpadatabricksspace                                  \n", "296   /subscriptions/b45fc77d-d8a1-4661-9747-05e03f484b63/resourcegroups/learning-acc72162/providers/microsoft.databricks/workspaces/azuredatabricks-72162                         \n", "303   /subscriptions/b45fc77d-d8a1-4661-9747-05e03f484b63/resourcegroups/learning-acc72156/providers/microsoft.databricks/workspaces/azuredatabricks-72156                         \n", "1003  /subscriptions/58a32ebe-b50f-4ecd-a4ee-355e4459de4b/resourcegroups/learning-acc72157/providers/microsoft.databricks/workspaces/azuredatabricks-72157                         \n", "1004  /subscriptions/bc588d10-be88-4b91-bc23-6921cf5de60f/resourcegroups/learning-acc72161/providers/microsoft.databricks/workspaces/azuredatabricks-72161                         \n", "1006  /subscriptions/b45fc77d-d8a1-4661-9747-05e03f484b63/resourcegroups/learning-acc72153/providers/microsoft.databricks/workspaces/azuredatabricks-72153                         \n", "1007  /subscriptions/bc588d10-be88-4b91-bc23-6921cf5de60f/resourcegroups/learning-acc72158/providers/microsoft.databricks/workspaces/azuredatabricks-72158                         \n", "550   /subscriptions/bc588d10-be88-4b91-bc23-6921cf5de60f/resourcegroups/learning-acc72155/providers/microsoft.databricks/workspaces/azuredatabricks-72155                         \n", "295   /subscriptions/b45fc77d-d8a1-4661-9747-05e03f484b63/resourcegroups/learning-acc72159/providers/microsoft.databricks/workspaces/azuredatabricks-72159                         \n", "304   /subscriptions/58a32ebe-b50f-4ecd-a4ee-355e4459de4b/resourcegroups/learning-acc72160/providers/microsoft.databricks/workspaces/azuredatabricks-72160                         \n", "298   /subscriptions/58a32ebe-b50f-4ecd-a4ee-355e4459de4b/resourcegroups/learning-acc72154/providers/microsoft.databricks/workspaces/azuredatabricks-72154                         \n", "547   /subscriptions/839ef081-3052-441a-b1ed-6ed09e1153ba/resourcegroups/trp_x_001/providers/microsoft.databricks/workspaces/4tpxzdbs001                                           \n", "546   /subscriptions/839ef081-3052-441a-b1ed-6ed09e1153ba/resourcegroups/jlr_x_001/providers/microsoft.databricks/workspaces/jlrx001databricks001                                  \n", "1043  /subscriptions/839ef081-3052-441a-b1ed-6ed09e1153ba/resourcegroups/ejd_x_001/providers/microsoft.databricks/workspaces/smartspacedbks                                        \n", "834   /subscriptions/8fc7c80a-4850-4f1a-b8df-77cf727b1f30/resourcegroups/databricks-test/providers/microsoft.databricks/workspaces/databricks                                      \n", "1044  /subscriptions/839ef081-3052-441a-b1ed-6ed09e1153ba/resourcegroups/chc_x_001/providers/microsoft.databricks/workspaces/db1                                                   \n", "28    /subscriptions/839ef081-3052-441a-b1ed-6ed09e1153ba/resourcegroups/3psa_t_001/providers/microsoft.databricks/workspaces/3psatzdbs001                                         \n", "166   /subscriptions/839ef081-3052-441a-b1ed-6ed09e1153ba/resourcegroups/3psa_ddbs_001/providers/microsoft.databricks/workspaces/3psadzdbs001                                      \n", "1041  /subscriptions/df236553-2478-44d3-a074-dcf28bc24ae3/resourcegroups/acuacar/providers/microsoft.databricks/workspaces/acuacardbricks                                          \n", "164   /subscriptions/ab0fe328-55c9-43d7-997d-e8a310b6d70c/resourcegroups/testdeployp/providers/microsoft.databricks/workspaces/dpadatabricksspace                                  \n", "56    /subscriptions/536c87a1-3ba3-4a16-8995-79b573336a15/resourcegroups/dpt_poc_use-1/providers/microsoft.databricks/workspaces/bg-databricks-premium-tier                        \n", "...                                                                                                                                                     ...                        \n", "968   /subscriptions/ccb3ad09-cba9-44c2-b6c7-884753fcd58c/resourcegroups/abhi-ibp-vnetinj-pubpreview-rg/providers/microsoft.databricks/workspaces/abhi-ibp-vnetinj-pubpreview-ws   \n", "967   /subscriptions/31f3fdf6-27b7-4cfb-904d-bf1c5b8c1371/resourcegroups/edap-prod-rg-test4/providers/microsoft.databricks/workspaces/edap-prod-rg-test4-workspace                 \n", "331   /subscriptions/89d52744-be0e-4971-9a6d-bfa378d99341/resourcegroups/casmelo-ml/providers/microsoft.databricks/workspaces/casmelo-ads                                          \n", "756   /subscriptions/3a89d508-f992-4729-9058-ba4fae9a35ca/resourcegroups/sandbox-335544-002-rg/providers/microsoft.databricks/workspaces/ffr-databricks3                           \n", "476   /subscriptions/2db6db0c-9da5-4ec7-a9b3-1affc620bf3e/resourcegroups/datalakeetl-prod-group/providers/microsoft.databricks/workspaces/eajobs-prod-adbwspace-cus                \n", "477   /subscriptions/d935d65e-c63a-4e87-b51d-1ca3d1498d2d/resourcegroups/wpaappsrg-20190131143044/providers/microsoft.databricks/workspaces/wpaappsdb20190131143044                \n", "86    /subscriptions/8ae3a5ac-469f-4cb3-92a3-d4541716c4cb/resourcegroups/delfin-dev-resourcegroup/providers/microsoft.databricks/workspaces/a360devspark                           \n", "1401  /subscriptions/721f1036-94cb-48e5-8d11-eb7b8e082b19/resourcegroups/ryu-spsanalytics/providers/microsoft.databricks/workspaces/ryudatabricks                                  \n", "183   /subscriptions/36f75872-9ace-4c20-911c-aea8eba2945c/resourcegroups/mike-test-rg/providers/microsoft.databricks/workspaces/mike-test-wksp                                     \n", "182   /subscriptions/7e33d792-86ef-4706-b2c9-a5c6a903ba27/resourcegroups/datalake/providers/microsoft.databricks/workspaces/apposita-bricks                                        \n", "107   /subscriptions/9a7a1d1b-21ee-417e-8096-70fd52730e24/resourcegroups/spark/providers/microsoft.databricks/workspaces/gxdb                                                      \n", "85    /subscriptions/15862453-f8cb-4f50-ba1f-f2547d153a22/resourcegroups/data_science_pilot/providers/microsoft.databricks/workspaces/db_ws_load_rec                               \n", "1001  /subscriptions/b3813a92-d36f-494c-b94a-6007ebad207f/resourcegroups/gm_rsd_dev-cdw-rg/providers/microsoft.databricks/workspaces/ak-1                                          \n", "106   /subscriptions/99048da9-23a9-4a39-95c0-ad006ba02d88/resourcegroups/rg1/providers/microsoft.databricks/workspaces/xian-databricks_test                                        \n", "9     /subscriptions/d935d65e-c63a-4e87-b51d-1ca3d1498d2d/resourcegroups/wpaappsrg-20190131140129/providers/microsoft.databricks/workspaces/wpaappsdb20190131140129                \n", "879   /subscriptions/b45fc77d-d8a1-4661-9747-05e03f484b63/resourcegroups/learning-acc72144/providers/microsoft.databricks/workspaces/azuredatabricks-72144                         \n", "290   /subscriptions/2a852612-0bb6-4a90-bc05-77d9eb821369/resourcegroups/test/providers/microsoft.databricks/workspaces/test_service                                               \n", "681   /subscriptions/667bd743-cbc2-4f89-b30e-5548f4c58d53/resourcegroups/databricks-resource-group/providers/microsoft.databricks/workspaces/anadbwsp01nc05-test                   \n", "878   /subscriptions/8dc39156-b35f-4805-8a35-be868fb796ba/resourcegroups/test/providers/microsoft.databricks/workspaces/test                                                       \n", "654   /subscriptions/33d475ff-18e3-4e93-838d-77b07598541e/resourcegroups/bhac-int/providers/microsoft.databricks/workspaces/databricksprototype                                    \n", "1002  /subscriptions/af037760-5e1f-4676-b2db-7d5a38063d4f/resourcegroups/rg-fit-d01/providers/microsoft.databricks/workspaces/adb-fit-tsi-d02                                      \n", "289   /subscriptions/58a32ebe-b50f-4ecd-a4ee-355e4459de4b/resourcegroups/learning-acc72143/providers/microsoft.databricks/workspaces/azuredatabricks-72143                         \n", "460   /subscriptions/77692cf6-f0b1-4b80-953d-5d6cc589d04f/resourcegroups/development/providers/microsoft.databricks/workspaces/adcpadbs                                            \n", "122   /subscriptions/90b22448-277d-4150-9cea-0e0637477af6/resourcegroups/databricksrg2/providers/microsoft.databricks/workspaces/dataricks2                                        \n", "10    /subscriptions/ea40fe7e-16ad-4db8-8d2f-ac9cb8d79712/resourcegroups/desa-comtrade-test-westeurope-datafactory-rg/providers/microsoft.databricks/workspaces/preproctest        \n", "188   /subscriptions/9a9b2a3c-c51a-43e4-82ee-63b4618d9b36/resourcegroups/rg-rajesh-test/providers/microsoft.databricks/workspaces/rajesh-db-test                                   \n", "458   /subscriptions/b45fc77d-d8a1-4661-9747-05e03f484b63/resourcegroups/learning-acc72142/providers/microsoft.databricks/workspaces/azuredatabricks-72142                         \n", "187   /subscriptions/2869a299-917b-429d-8176-af010f03e04e/resourcegroups/hthdev/providers/microsoft.databricks/workspaces/hthadsdev                                                \n", "185   /subscriptions/e2b6707b-b235-4a3e-bb73-ab39fb081345/resourcegroups/rgadvancedtechnologydev/providers/microsoft.databricks/workspaces/advancedtechnologydev                   \n", "12    /subscriptions/139ca47d-ffee-4bea-ba11-19c2adf5a186/resourcegroups/application-insights-eastus/providers/microsoft.databricks/workspaces/yingjitrial                         \n", "\n", "                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                     properties  \\\n", "1183  {}                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                          \n", "15    {'error': {'code': 'ApplicationUpdateFail', 'message': 'Failed to update application: 'dde-dev-databricks-vi-001', because patch resource group failure.', 'details': [{'code': 'RequestDisallowedByPolicy', 'message': 'Resource \\'workers-vnet\\' was disallowed by policy. Policy identifiers: \\'[{\"policyAssignment\":{\"name\":\"Not allowed resource types\",\"id\":\"/subscriptions/0c8ee94f-e9a1-4b8a-8bf5-12845b6c165b/providers/Microsoft.Authorization/policyAssignments/02902ccc2d094a458ef2e9f7\"},\"policyDefinition\":{\"name\":\"Not allowed resource types\",\"id\":\"/providers/Microsoft.Authorization/policyDefinitions/6c112d4e-5bc7-47ae-a041-ea2d9dccd749\"}}]\\'.'}]}}   \n", "16    {}                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                          \n", "3     {}                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                          \n", "496   {}                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                          \n", "497   {}                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                          \n", "870   {}                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                          \n", "720   {}                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                          \n", "721   {}                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                          \n", "1024  {'error': {'code': 'InvalidApplicationManagedResourceGroupId', 'message': 'The managed resource group identifier is invalid. Please provide fully qualified identifier of the resource group for the application's resources.'}}                                                                                                                                                                                                                                                                                                                                                                                                                                            \n", "296   {}                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                          \n", "303   {}                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                          \n", "1003  {}                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                          \n", "1004  {}                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                          \n", "1006  {}                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                          \n", "1007  {}                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                          \n", "550   {}                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                          \n", "295   {}                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                          \n", "304   {}                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                          \n", "298   {}                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                          \n", "547   {}                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                          \n", "546   {}                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                          \n", "1043  {}                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                          \n", "834   {}                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                          \n", "1044  {}                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                          \n", "28    {}                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                          \n", "166   {}                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                          \n", "1041  {}                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                          \n", "164   {'error': {'code': 'InvalidApplicationManagedResourceGroupId', 'message': 'The managed resource group identifier is invalid. Please provide fully qualified identifier of the resource group for the application's resources.'}}                                                                                                                                                                                                                                                                                                                                                                                                                                            \n", "56    {}                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                          \n", "...   ..                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                          \n", "968   {}                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                          \n", "967   {}                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                          \n", "331   {}                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                          \n", "756   {}                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                          \n", "476   {'error': {'code': 'MissingSubscriptionRegistration', 'message': 'The subscription is not registered to use namespace 'Microsoft.Databricks'. See https://aka.ms/rps-not-found for how to register subscriptions.', 'details': [{'code': 'MissingSubscriptionRegistration', 'target': 'Microsoft.Databricks', 'message': \"The subscription is not registered to use namespace 'Microsoft.Databricks'. See https://aka.ms/rps-not-found for how to register subscriptions.\"}]}}                                                                                                                                                                                              \n", "477   {}                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                          \n", "86    {}                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                          \n", "1401  {}                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                          \n", "183   {}                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                          \n", "182   {}                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                          \n", "107   {}                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                          \n", "85    {}                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                          \n", "1001  {}                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                          \n", "106   {}                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                          \n", "9     {}                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                          \n", "879   {}                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                          \n", "290   {}                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                          \n", "681   {}                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                          \n", "878   {}                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                          \n", "654   {}                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                          \n", "1002  {}                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                          \n", "289   {}                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                          \n", "460   {}                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                          \n", "122   {}                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                          \n", "10    {}                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                          \n", "188   {}                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                          \n", "458   {}                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                          \n", "187   {}                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                          \n", "185   {}                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                          \n", "12    {}                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                          \n", "\n", "         status       region                               outer_error  \\\n", "1183  Succeeded  eastus2      None                                       \n", "15    Failed     unknown      ApplicationUpdateFail                      \n", "16    Succeeded  eastus2      None                                       \n", "3     Succeeded  west<PERSON>       None                                       \n", "496   Succeeded  west<PERSON>       None                                       \n", "497   Succeeded  eastus2      None                                       \n", "870   Succeeded  eastus       None                                       \n", "720   Succeeded  westeurope   None                                       \n", "721   Succeeded  west<PERSON>       None                                       \n", "1024  Failed     westus       InvalidApplicationManagedResourceGroupId   \n", "296   Succeeded  eastus       None                                       \n", "303   Succeeded  eastus       None                                       \n", "1003  Succeeded  eastus       None                                       \n", "1004  Succeeded  eastus       None                                       \n", "1006  Succeeded  eastus       None                                       \n", "1007  Succeeded  eastus       None                                       \n", "550   Succeeded  eastus       None                                       \n", "295   Succeeded  eastus       None                                       \n", "304   Succeeded  eastus       None                                       \n", "298   Succeeded  eastus       None                                       \n", "547   Succeeded  unknown      None                                       \n", "546   Succeeded  unknown      None                                       \n", "1043  Succeeded  unknown      None                                       \n", "834   Succeeded  westeur<PERSON>   None                                       \n", "1044  Succeeded  unknown      None                                       \n", "28    Succeeded  unknown      None                                       \n", "166   Succeeded  unknown      None                                       \n", "1041  Succeeded  west<PERSON>       None                                       \n", "164   Failed     westus       InvalidApplicationManagedResourceGroupId   \n", "56    Succeeded  eastus       None                                       \n", "...         ...     ...        ...                                       \n", "968   Succeeded  eastus2      None                                       \n", "967   Succeeded  centralus    None                                       \n", "331   Succeeded  west<PERSON>       None                                       \n", "756   Succeeded  unknown      None                                       \n", "476   Failed     unknown      MissingSubscriptionRegistration            \n", "477   Succeeded  westus2      None                                       \n", "86    Succeeded  west<PERSON>       None                                       \n", "1401  Succeeded  westus2      None                                       \n", "183   Succeeded  west<PERSON>       None                                       \n", "182   Succeeded  northeurope  None                                       \n", "107   Succeeded  auseast      None                                       \n", "85    Succeeded  scentralus   None                                       \n", "1001  Succeeded  eastus2      None                                       \n", "106   Succeeded  eastus       None                                       \n", "9     Succeeded  westus2      None                                       \n", "879   Succeeded  eastus       None                                       \n", "290   Succeeded  west<PERSON>       None                                       \n", "681   Succeeded  centralus    None                                       \n", "878   Succeeded  west<PERSON>       None                                       \n", "654   Succeeded  unknown      None                                       \n", "1002  Succeeded  westus2      None                                       \n", "289   Succeeded  eastus       None                                       \n", "460   Succeeded  eastus       None                                       \n", "122   Succeeded  scentralus   None                                       \n", "10    Succeeded  west<PERSON><PERSON>   None                                       \n", "188   Succeeded  unknown      None                                       \n", "458   Succeeded  eastus       None                                       \n", "187   Succeeded  unknown      None                                       \n", "185   Succeeded  unknown      None                                       \n", "12    Succeeded  unknown      None                                       \n", "\n", "                          inner_error  \\\n", "1183  NA                                \n", "15    PolicyError                       \n", "16    NA                                \n", "3     NA                                \n", "496   NA                                \n", "497   NA                                \n", "870   NA                                \n", "720   NA                                \n", "721   NA                                \n", "1024  NA                                \n", "296   NA                                \n", "303   NA                                \n", "1003  NA                                \n", "1004  NA                                \n", "1006  NA                                \n", "1007  NA                                \n", "550   NA                                \n", "295   NA                                \n", "304   NA                                \n", "298   NA                                \n", "547   NA                                \n", "546   NA                                \n", "1043  NA                                \n", "834   NA                                \n", "1044  NA                                \n", "28    NA                                \n", "166   NA                                \n", "1041  NA                                \n", "164   NA                                \n", "56    NA                                \n", "...   ..                                \n", "968   NA                                \n", "967   NA                                \n", "331   NA                                \n", "756   NA                                \n", "476   MissingSubscriptionRegistration   \n", "477   NA                                \n", "86    NA                                \n", "1401  NA                                \n", "183   NA                                \n", "182   NA                                \n", "107   NA                                \n", "85    NA                                \n", "1001  NA                                \n", "106   NA                                \n", "9     NA                                \n", "879   NA                                \n", "290   NA                                \n", "681   NA                                \n", "878   NA                                \n", "654   NA                                \n", "1002  NA                                \n", "289   NA                                \n", "460   NA                                \n", "122   NA                                \n", "10    NA                                \n", "188   NA                                \n", "458   NA                                \n", "187   NA                                \n", "185   NA                                \n", "12    NA                                \n", "\n", "                                                          error_group  \n", "1183  NaN                                                              \n", "15    ApplicationUpdateFail:PolicyError                                \n", "16    NaN                                                              \n", "3     NaN                                                              \n", "496   NaN                                                              \n", "497   NaN                                                              \n", "870   NaN                                                              \n", "720   NaN                                                              \n", "721   NaN                                                              \n", "1024  InvalidApplicationManagedResourceGroupId:NA                      \n", "296   NaN                                                              \n", "303   NaN                                                              \n", "1003  NaN                                                              \n", "1004  NaN                                                              \n", "1006  NaN                                                              \n", "1007  NaN                                                              \n", "550   NaN                                                              \n", "295   NaN                                                              \n", "304   NaN                                                              \n", "298   NaN                                                              \n", "547   NaN                                                              \n", "546   NaN                                                              \n", "1043  NaN                                                              \n", "834   NaN                                                              \n", "1044  NaN                                                              \n", "28    NaN                                                              \n", "166   NaN                                                              \n", "1041  NaN                                                              \n", "164   InvalidApplicationManagedResourceGroupId:NA                      \n", "56    NaN                                                              \n", "...   ...                                                              \n", "968   NaN                                                              \n", "967   NaN                                                              \n", "331   NaN                                                              \n", "756   NaN                                                              \n", "476   MissingSubscriptionRegistration:MissingSubscriptionRegistration  \n", "477   NaN                                                              \n", "86    NaN                                                              \n", "1401  NaN                                                              \n", "183   NaN                                                              \n", "182   NaN                                                              \n", "107   NaN                                                              \n", "85    NaN                                                              \n", "1001  NaN                                                              \n", "106   NaN                                                              \n", "9     NaN                                                              \n", "879   NaN                                                              \n", "290   NaN                                                              \n", "681   NaN                                                              \n", "878   NaN                                                              \n", "654   NaN                                                              \n", "1002  NaN                                                              \n", "289   NaN                                                              \n", "460   NaN                                                              \n", "122   NaN                                                              \n", "10    NaN                                                              \n", "188   NaN                                                              \n", "458   NaN                                                              \n", "187   NaN                                                              \n", "185   NaN                                                              \n", "12    NaN                                                              \n", "\n", "[570 rows x 9 columns]"]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["df_creations.resourceUri = df_creations.resourceUri.str.lower()\n", "df_creations.drop_duplicates(subset=['resourceUri'])"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["df_green = df_creations[df_creations.status == \"Succeeded\"]\n", "df_failures = df_creations[df_creations.status == \"Failed\"]\n", "\n", "yellow_errors = ['InvalidApplicationManagedResourceGroupId:NA',\n", "       'ResourceDeploymentFailure:MissingSubscriptionRegistration',\n", "       'ResourceDeploymentFailure:ARMDocumentDBIssue',\n", "       'ResourceDeploymentFailure:PolicyError',\n", "       'ApplianceBeingDeleted:NA',\n", "       'ResourceDeploymentFailure:InvalidApplianceState',\n", "       'ApplianceManagedResourceGroupMismatch:NA',\n", "       'ResourceDeploymentFailure:MaxStorageAccountsCountPerSubscriptionExceeded',\n", "       'InvalidDatabricksSkuUpdate:NA',\n", "       'MissingSubscriptionRegistration:MissingSubscriptionRegistration',\n", "       'ResourceDeploymentFailure:SubscriptionNotRegistered',\n", "       'InvalidDatabricksSku:NA',\n", "        'ApplianceManagedResourceGroupMismatch:NA',\n", "       'ResourceDeploymentFailure:InternalServerError']\n", "\n", "df_yellow = df_failures[df_failures.error_group.isin(yellow_errors)]\n", "df_red = df_failures[~df_failures.error_group.isin(yellow_errors)]"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["1321\n", "28\n", "116\n"]}], "source": ["print(len(df_green))\n", "print(len(df_yellow))\n", "print(len(df_red))"]}, {"cell_type": "code", "execution_count": 24, "metadata": {"scrolled": false}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["EASTUS2EUAP\n", "{}\n", "\n", "\n", "WESTUS\n", "{'Succeeded': 253, 'Failed': 6}\n", "\n", "ResourceDeploymentFailure:AuthorizationFailed : 6\n", "/subscriptions/12100e91-7179-48ca-b02a-19f502f60d35/resourcegroups/DataScienceLab/providers/Microsoft.Databricks/workspaces/DataScienceLab-bricks\n", "/subscriptions/12100e91-7179-48ca-b02a-19f502f60d35/resourcegroups/DataScienceLab/providers/Microsoft.Databricks/workspaces/DataScienceLab-ws\n", "/subscriptions/596df088-441c-4a6f-881e-5511128a3f1c/resourcegroups/qa_rg_westus_standard_5bf6db39/providers/Microsoft.Databricks/workspaces/qa_workspace_westus_standard_5bf6db39\n", "/subscriptions/5bbcae9d-d03c-4184-af23-4313e099dd6d/resourcegroups/testminevn/providers/Microsoft.Databricks/workspaces/testmine\n", "/subscriptions/0827c45d-52f6-48fe-8ed1-a4a113ac31e1/resourcegroups/demo/providers/Microsoft.Databricks/workspaces/test3\n", "/subscriptions/eb481d23-7e79-476f-a31d-aba81951ec98/resourcegroups/RG-MyInsights-Service-Prod/providers/Microsoft.Databricks/workspaces/mi-dbricks-prod-poc\n", "\n", "\n", "EASTUS2\n", "{'Succeeded': 353, 'Failed': 22}\n", "\n", "ResourceDeploymentFailure:AuthorizationFailed : 20\n", "/subscriptions/89bc2539-531f-401d-9e0d-b1eaf905a6c6/resourcegroups/Jeff-<PERSON>/providers/Microsoft.Databricks/workspaces/gaia-jb\n", "/subscriptions/bffe4cc6-9376-49c6-83e1-705eb8b823c3/resourcegroups/FII-PRD-BLOOMBERGANALYSIS/providers/Microsoft.Databricks/workspaces/PRDDTBK01-WS\n", "/subscriptions/79103142-f838-4690-ab90-7f1c64fafe75/resourcegroups/ODL-DiscoveryDay-49590/providers/Microsoft.Databricks/workspaces/azurediscdayws2\n", "/subscriptions/596df088-441c-4a6f-881e-5511128a3f1c/resourcegroups/qa_rg_eastus2_premium_174a1b51/providers/Microsoft.Databricks/workspaces/qa_workspace_eastus2_premium_174a1b51\n", "/subscriptions/82bcff9b-c4ff-42c6-aa18-34da5cc72fbb/resourcegroups/marketing-etl-eus2-rg/providers/Microsoft.Databricks/workspaces/popcore-etl\n", "/subscriptions/89bc2539-531f-401d-9e0d-b1eaf905a6c6/resourcegroups/db-lab-vjc/providers/Microsoft.Databricks/workspaces/db-lab-vjc-databricks\n", "/subscriptions/06dd00f7-b728-411f-aca7-5f8a32872d56/resourcegroups/AzureMLWorkshop/providers/Microsoft.Databricks/workspaces/JoeMLWS\n", "/subscriptions/1f913984-b1c8-4187-b297-415b71d6d278/resourcegroups/corp-datatogo-application-dev/providers/Microsoft.Databricks/workspaces/github-databrick\n", "/subscriptions/1f913984-b1c8-4187-b297-415b71d6d278/resourcegroups/gsk-Corp-Platforms-Dataecosys-application-resources/providers/Microsoft.Databricks/workspaces/josh-github-databrick\n", "/subscriptions/a35ebf47-daf2-4bf2-ad69-9e3b908eac04/resourcegroups/AZ-RG-STCDatWarehouse40-Prod-01/providers/Microsoft.Databricks/workspaces/adbenttestadw01\n", "/subscriptions/cfdea0dc-e97b-463e-8dc3-36aca6c6270d/resourcegroups/demo2/providers/Microsoft.Databricks/workspaces/demo2\n", "/subscriptions/872fa0dd-4729-4b7f-8c74-bcce12be61f1/resourcegroups/CurriculumExtractor/providers/Microsoft.Databricks/workspaces/WorkspaceDataBricks\n", "/subscriptions/52674a79-ec0d-4895-94d2-ff05ce6ed6ea/resourcegroups/Enterprise/providers/Microsoft.Databricks/workspaces/EnterpriseSpace\n", "/subscriptions/8FFD00A2-3731-44A0-B51E-4B9260C92A39/resourcegroups/ODL-bigdata-vis-49495/providers/Microsoft.Databricks/workspaces/49495\n", "/subscriptions/8dee33b7-0acc-4214-9268-be41406d49cd/resourcegroups/ODL_Databricksdev-9781/providers/Microsoft.Databricks/workspaces/9781\n", "/subscriptions/8dee33b7-0acc-4214-9268-be41406d49cd/resourcegroups/ODL_Databricksdev-9782/providers/Microsoft.Databricks/workspaces/9782\n", "/subscriptions/41ca5ed7-3e4f-4a30-b857-9cdad18ad91e/resourcegroups/ODL-mlTest-49485/providers/Microsoft.Databricks/workspaces/49485\n", "/subscriptions/aa8eeb81-5148-4123-a642-4f68f6019093/resourcegroups/aeu2-poc-clientdelivery-databricks-rsg/providers/Microsoft.Databricks/workspaces/ClientDelivery_WS\n", "/subscriptions/71276155-2563-4744-b3b8-512e5bd29c14/resourcegroups/TEST3/providers/Microsoft.Databricks/workspaces/TEST3\n", "/subscriptions/11502dbb-1f54-441e-9eb5-66602bd6c3b7/resourcegroups/databricks/providers/Microsoft.Databricks/workspaces/TEST2\n", "\n", "ApplicationNotFound:NA : 1\n", "/subscriptions/387cc842-835f-4ccd-a789-54531d36904e/resourcegroups/RG-AmerisourceBergen/providers/Microsoft.Databricks/workspaces/DB-AmerisourceBergen\n", "\n", "ResourceDeploymentFailure:ApplianceProvisioningFailed : 1\n", "/subscriptions/EB2AC4C2-141F-43CB-96D9-FF4B3BC9760A/resourcegroups/ODL-bigdata-vis-49499/providers/Microsoft.Databricks/workspaces/49499\n", "\n", "\n", "WESTEUROPE\n", "{'Succeeded': 374, 'Failed': 13}\n", "\n", "ResourceDeploymentFailure:AuthorizationFailed : 9\n", "/subscriptions/b4a5eaff-15ac-4575-9891-0e4e8417450e/resourcegroups/vap-dev-ba-cs-bu-b2c-nl-we-rg/providers/Microsoft.Databricks/workspaces/db-fede\n", "/subscriptions/1cce7f69-c5f7-4391-8354-a3180e0862f0/resourcegroups/skills-dev-we/providers/Microsoft.Databricks/workspaces/analytics\n", "/subscriptions/625adb3e-2fec-4162-8790-36dbb10cad43/resourcegroups/DatabricksCD/providers/Microsoft.Databricks/workspaces/databricks20190129\n", "/subscriptions/59a878ab-342f-4641-bfa6-419c20edde4c/resourcegroups/fe-POV-databricks/providers/Microsoft.Databricks/workspaces/fe-databricks-new\n", "/subscriptions/041715c3-b9ce-43d1-8818-8c844119c248/resourcegroups/rg0129adb/providers/Microsoft.Databricks/workspaces/adb0129andre\n", "/subscriptions/137767c8-35be-4fb0-8707-9061853fe445/resourcegroups/ucx-datascience-rg/providers/Microsoft.Databricks/workspaces/ucx-datascience-databricks\n", "/subscriptions/ae187d5f-639d-4c0e-a02a-cd277fa0998a/resourcegroups/funwithbricks/providers/Microsoft.Databricks/workspaces/mydatabrickslife\n", "/subscriptions/fc143c42-6a8c-45fb-b9ad-f13da63dae69/resourcegroups/rgdatabricksspark/providers/Microsoft.Databricks/workspaces/databricks\n", "/subscriptions/ae187d5f-639d-4c0e-a02a-cd277fa0998a/resourcegroups/unilever/providers/Microsoft.Databricks/workspaces/databrickstesting\n", "\n", "ResourceDeploymentFailure:ApplianceProvisioningFailed : 2\n", "/subscriptions/562f0104-22cc-4ddd-a384-b5dd900cdc17/resourcegroups/emsa-hpims-test-long-term-storage/providers/Microsoft.Databricks/workspaces/emsa-hpims-test-we-databricks\n", "/subscriptions/dc9839c9-7145-41fb-a3fb-d0569bd89fad/resourcegroups/NHCM-POC-krisn-RG/providers/Microsoft.Databricks/workspaces/krisnDataBricks2\n", "\n", "InternalServerError:NA : 1\n", "/subscriptions/b238961f-5881-4618-a860-ebff05f53733/resourcegroups/zure-dataguild-int-test/providers/Microsoft.Databricks/workspaces/o32zizcoyfcva\n", "\n", "ResourceGroupNotFound:NA : 1\n", "/subscriptions/b146ae31-d42f-4c88-889b-318f2cc23f98/resourceGroups/dataThirstDatabricksDBFSExplorer-RG/providers/Microsoft.Databricks/workspaces/dataThirstDatabricksDBFSExplorer\n", "\n", "\n", "NORTHEUROPE\n", "{'Succeeded': 199, 'Failed': 5}\n", "\n", "ResourceDeploymentFailure:AuthorizationFailed : 4\n", "/subscriptions/9656b513-da1f-4f44-8b7c-98d832183256/resourcegroups/Cofina3/providers/Microsoft.Databricks/workspaces/Cofina3\n", "/subscriptions/2df15e9c-49e4-44bd-b170-3e143db5bb83/resourcegroups/giubranabc/providers/Microsoft.Databricks/workspaces/giubrandef\n", "/subscriptions/9698c522-26cc-43f2-8197-dfa9225e856f/resourcegroups/grt_drp_test/providers/Microsoft.Databricks/workspaces/drpGrtDataBricks\n", "/subscriptions/ae187d5f-639d-4c0e-a02a-cd277fa0998a/resourcegroups/unileverprocess/providers/Microsoft.Databricks/workspaces/msftlearnbricks\n", "\n", "ResourceDeploymentFailure:ApplianceProvisioningFailed : 1\n", "/subscriptions/204671af-5130-4ef5-819c-e314b65f9d06/resourcegroups/bieno-da-p-60072-engine-rg/providers/Microsoft.Databricks/workspaces/ohub2-databricks-prod\n", "\n", "\n", "EASTUS\n", "{'Succeeded': 484, 'Failed': 17}\n", "\n", "ResourceDeploymentFailure:AuthorizationFailed : 17\n", "/subscriptions/54006eea-3986-4fa8-90ff-7606059d2f61/resourcegroups/RG-BoC-MohsinYasin1/providers/Microsoft.Databricks/workspaces/mohsinDB\n", "/subscriptions/1ff4d762-64da-4eae-9cec-77477370fa40/resourcegroups/ssm_test_rg/providers/Microsoft.Databricks/workspaces/ssmtestdatabricks\n", "/subscriptions/db0d54ba-b405-4b1f-a4df-012b1f34a905/resourcegroups/AI_ML_rg/providers/Microsoft.Databricks/workspaces/Databricksworkshop\n", "/subscriptions/26317ae8-9d60-48dd-81c3-cc484ed4c90e/resourcegroups/UBAP-DevTest-RG/providers/Microsoft.Databricks/workspaces/UBAP-DataBricks-ETL\n", "/subscriptions/4722946a-31b0-4bf0-b300-791332aa4605/resourcegroups/MSWorkshop/providers/Microsoft.Databricks/workspaces/msds\n", "/subscriptions/8dee33b7-0acc-4214-9268-be41406d49cd/resourcegroups/ODL_Databricksdev-9778/providers/Microsoft.Databricks/workspaces/9778\n", "/subscriptions/8dee33b7-0acc-4214-9268-be41406d49cd/resourcegroups/ODL_Databricksdev-9779/providers/Microsoft.Databricks/workspaces/9779\n", "/subscriptions/8dee33b7-0acc-4214-9268-be41406d49cd/resourcegroups/ODL_Databricksdev-9777/providers/Microsoft.Databricks/workspaces/9777\n", "/subscriptions/53e77d8e-c18b-4040-846b-282ed557ee9a/resourcegroups/iONSit/providers/Microsoft.Databricks/workspaces/ds-jobs\n", "/subscriptions/41ca5ed7-3e4f-4a30-b857-9cdad18ad91e/resourcegroups/ODL-mlTest-49491/providers/Microsoft.Databricks/workspaces/49491\n", "/subscriptions/41ca5ed7-3e4f-4a30-b857-9cdad18ad91e/resourcegroups/ODL-mlTest-49487/providers/Microsoft.Databricks/workspaces/49487\n", "/subscriptions/53e77d8e-c18b-4040-846b-282ed557ee9a/resourcegroups/iONSit/providers/Microsoft.Databricks/workspaces/ds-data-acqre-n-proces-jobs\n", "/subscriptions/41ca5ed7-3e4f-4a30-b857-9cdad18ad91e/resourcegroups/ODL-mlTest-49478/providers/Microsoft.Databricks/workspaces/49478\n", "/subscriptions/53e77d8e-c18b-4040-846b-282ed557ee9a/resourcegroups/iONSit/providers/Microsoft.Databricks/workspaces/ds-data-acquire-n-process-jobs\n", "/subscriptions/5928cf55-bdcb-41eb-8211-ffa65eed3728/resourcegroups/demoData/providers/Microsoft.Databricks/workspaces/demodatabricks\n", "/subscriptions/41ca5ed7-3e4f-4a30-b857-9cdad18ad91e/resourcegroups/ODL-mlTest-49473/providers/Microsoft.Databricks/workspaces/49473\n", "/subscriptions/8950e1b9-4c32-438e-a5f8-1ebc318dabb4/resourcegroups/SanjayPOC/providers/Microsoft.Databricks/workspaces/MyTestCluster\n", "\n", "\n", "SEASTASIA\n", "{'Succeeded': 224, 'Failed': 14}\n", "\n", "ResourceDeploymentFailure:AuthorizationFailed : 13\n", "/subscriptions/e3675a5d-9205-4ab3-9bb5-773259bbbde8/resourcegroups/ODL-ml-49906/providers/Microsoft.Databricks/workspaces/49906\n", "/subscriptions/21ebe4ed-eb21-4791-bde5-33fca7f1142a/resourcegroups/ODL-ml-49901/providers/Microsoft.Databricks/workspaces/49901\n", "/subscriptions/e8bfb459-cc60-4e18-bfe3-cca6a9c05b06/resourcegroups/ODL-ml-49898/providers/Microsoft.Databricks/workspaces/49898\n", "/subscriptions/df085af0-1d07-4890-83c6-c1e6519aa212/resourcegroups/ODL-ml-49889/providers/Microsoft.Databricks/workspaces/49889\n", "/subscriptions/9a0055fb-0c2f-47ee-8141-bc00115a3299/resourcegroups/ODL-ml-49886/providers/Microsoft.Databricks/workspaces/49886\n", "/subscriptions/4e09b5ee-747c-4bc6-b0d6-37550536c1a6/resourcegroups/ODL-databricks-dw-49879/providers/Microsoft.Databricks/workspaces/49879\n", "/subscriptions/03df3539-4556-46c1-87d1-b5383cd0bb27/resourcegroups/ODL-databricks-dw-49877/providers/Microsoft.Databricks/workspaces/49877\n", "/subscriptions/ea8323fb-dd33-48d1-8ffb-3d771bc5f05c/resourcegroups/ODL-databricks-dw-49867/providers/Microsoft.Databricks/workspaces/49867\n", "/subscriptions/07a3b836-0813-4c05-afd4-3a7ab00358d9/resourcegroups/ODL-databricks-dw-49865/providers/Microsoft.Databricks/workspaces/49865\n", "/subscriptions/2903defe-821c-415b-98f0-18cd2465d7a4/resourcegroups/ODL-databricks-dw-49856/providers/Microsoft.Databricks/workspaces/49856\n", "/subscriptions/edb336ca-b85f-4204-8057-7fdb7d65322c/resourcegroups/ODL-databricks-dw-49852/providers/Microsoft.Databricks/workspaces/49852\n", "/subscriptions/f33373bf-4282-4926-a9a4-fcc8c3c6ff62/resourcegroups/ODL-databricks-dw-49817/providers/Microsoft.Databricks/workspaces/49817\n", "/subscriptions/41ca5ed7-3e4f-4a30-b857-9cdad18ad91e/resourcegroups/ODL-mlTest-49549/providers/Microsoft.Databricks/workspaces/49549\n", "\n", "ResourceNotFound:NA : 1\n", "/subscriptions/b9cec7a1-c948-4cd3-a08e-aac87ab0de4a/resourcegroups/PUC-RG/providers/Microsoft.Databricks/workspaces/Demo\n", "\n", "\n", "EASTASIA\n", "{'Succeeded': 6}\n", "\n", "\n", "SCENTRALUS\n", "{'Succeeded': 96}\n", "\n", "\n", "NCENTRALUS\n", "{'Succeeded': 13}\n", "\n", "\n", "WESTUS2\n", "{'Succeeded': 158, 'Failed': 4}\n", "\n", "ResourceDeploymentFailure:AuthorizationFailed : 3\n", "/subscriptions/1722f1c2-5b86-41e2-b9d5-5626314fb7db/resourcegroups/s1services/providers/Microsoft.Databricks/workspaces/s1tlogtest\n", "/subscriptions/dc7df375-fcdf-4881-a73c-f5ae0411021d/resourcegroups/yashdatabric/providers/Microsoft.Databricks/workspaces/yashdatabrickkk\n", "/subscriptions/ae71ef11-a03f-4b4f-a0e6-ef144727c711/resourcegroups/AML_IU_Demo/providers/Microsoft.Databricks/workspaces/Databricks_IU_Demo_\n", "\n", "ResourceDeploymentFailure:ApplianceProvisioningFailed : 1\n", "/subscriptions/7559c239-0954-495e-82ee-518829957e2d/resourcegroups/PowerBITests/providers/Microsoft.Databricks/workspaces/PowerBIRestAPI\n", "\n", "\n", "CENTRALUS\n", "{'Succeeded': 110}\n", "\n", "\n", "UKWEST\n", "{'Succeeded': 16, 'Failed': 1}\n", "\n", "ResourceDeploymentFailure:AuthorizationFailed : 1\n", "/subscriptions/1dc70a97-2716-4eee-9a3a-197120597727/resourcegroups/azure-ai-hack-uk/providers/Microsoft.Databricks/workspaces/kallidus-course-recommendation-101\n", "\n", "\n", "UKSOUTH\n", "{'Succeeded': 34}\n", "\n", "\n", "AUSEAST\n", "{'Succeeded': 78, 'Failed': 1}\n", "\n", "ResourceDeploymentFailure:ApplianceProvisioningFailed : 1\n", "/subscriptions/8fc73de0-0a3f-42b5-924e-cf51a9bba92e/resourcegroups/databricks_ws/providers/Microsoft.Databricks/workspaces/wip_db_cluster\n", "\n", "\n", "AUSSEAST\n", "{'Succeeded': 193, 'Failed': 2}\n", "\n", "ResourceDeploymentFailure:ApplianceProvisioningFailed : 2\n", "/subscriptions/7a2f51a1-ab51-42a4-b672-8fa0c2e9ff9b/resourcegroups/ODL-databricks-dw-49699/providers/Microsoft.Databricks/workspaces/49699\n", "/subscriptions/41ca5ed7-3e4f-4a30-b857-9cdad18ad91e/resourcegroups/ODL-mlTest-49472/providers/Microsoft.Databricks/workspaces/49472\n", "\n", "\n", "AUSCENTRAL\n", "{'Succeeded': 1}\n", "\n", "\n", "AUSCENTRAL2\n", "{}\n", "\n", "\n", "JAPANEAST\n", "{'Succeeded': 21}\n", "\n", "\n", "JAPANWEST\n", "{'Succeeded': 8, 'Failed': 1}\n", "\n", "ResourceDeploymentFailure:ApplianceProvisioningFailed : 1\n", "/subscriptions/0ab9ad2e-f316-4da5-b243-e7f65da24755/resourcegroups/table3akdatabricksrg/providers/Microsoft.Databricks/workspaces/table3akdatabricks\n", "\n", "\n", "CANCENTRAL\n", "{'Succeeded': 37, 'Failed': 3}\n", "\n", "ResourceDeploymentFailure:AuthorizationFailed : 3\n", "/subscriptions/54006eea-3986-4fa8-90ff-7606059d2f61/resourcegroups/RG-BoC-GabrielleForget1/providers/Microsoft.Databricks/workspaces/coffee\n", "/subscriptions/6c5f4698-361b-4d1d-a796-c906900f5065/resourcegroups/psp_demo_rc2/providers/Microsoft.Databricks/workspaces/psp_demo_rc2\n", "/subscriptions/5208dadf-4d5b-4d21-891c-0f1fffeb2521/resourcegroups/dafortin-rg/providers/Microsoft.Databricks/workspaces/dafortin-w\n", "\n", "\n", "CANEAST\n", "{'Succeeded': 10}\n", "\n", "\n", "INDCENTRAL\n", "{'Succeeded': 16}\n", "\n", "\n", "INDSOUTH\n", "{'Succeeded': 19}\n", "\n", "\n", "INDWEST\n", "{'Succeeded': 3}\n", "\n", "\n"]}], "source": ["regions = [\"eastus2euap\", \"westus\", \"eastus2\", \"westeurope\", \"northeurope\", \"eastus\", \"seastasia\", \n", "           \"eastasia\", \"scentralus\", \"ncentralus\", \"westus2\", \"centralus\", \"ukwest\", \"uksouth\", \n", "           \"auseast\", \"ausseast\", \"auscentral\", \"auscentral2\", \"japaneast\", \"japanwest\", \"cancentral\", \"caneast\",\n", "           \"indcentral\", \"indsouth\", \"indwest\"]\n", "\n", "unique_creations = real_errors.drop_duplicates(\"resourceUri\")\n", "\n", "for current_region in regions:\n", "    print(current_region.upper())\n", "    df_region = unique_creations[unique_creations.region == current_region]\n", "    print(dict(df_region.status.value_counts()))\n", "    error_counts = dict(df_region[df_region.status == \"Failed\"].error_group.value_counts())\n", "    print()\n", "    for key in error_counts:\n", "        print(key + \" : \" + str(error_counts[key]))\n", "        for uri in list(df_region[(df_region.status == \"Failed\") & (df_region.error_group == key)].resourceUri):\n", "            print(uri)\n", "        print()\n", "    print()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.6.4"}}, "nbformat": 4, "nbformat_minor": 2}