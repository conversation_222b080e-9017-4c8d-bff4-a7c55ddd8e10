{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {"scrolled": false}, "outputs": [{"data": {"text/plain": ["'2018-12-07 14:15:00.785855'"]}, "execution_count": 1, "metadata": {}, "output_type": "execute_result"}], "source": ["from datetime import datetime\n", "str(datetime.now())"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Workspace Creations by Region\n", "\n", "Below we look at workspace creations to the new RP by region. We use the EventServiceEntriesAnalyzer.get_error_groups (in the kustoutils.py file in this repo) to break the errors into groups. <br>\n", "\n", "The empty regions have not been switched over yet."]}, {"cell_type": "code", "execution_count": 2, "metadata": {"scrolled": false}, "outputs": [], "source": ["from kustoutils import KustoCaller, EventServiceEntriesAnalyzer\n", "import pandas as pd\n", "import warnings\n", "import json\n", "warnings.filterwarnings('ignore')\n", "\n", "pd.set_option('display.max_colwidth', -1)\n", "\n", "kc = KustoCaller()\n", "query = \"\"\"\n", "HttpIncomingRequests\n", "| where httpStatusCode > 0\n", "| where subscriptionId != \"0140911e-1040-48da-8bc9-b99fb3dd88a6\"\n", "| where targetUri contains \"Microsoft.Databricks\"\n", "| where httpMethod == \"PUT\"\n", "| project TIMESTAMP, subscriptionId, httpMethod, httpStatusCode, targetUri\n", "| order by subscriptionId, TIMESTAMP asc\n", "\"\"\"\n", "df_puts = kc.query(query, kc.RP_DB, kc.RP_CLUSTER)\n", "\n", "query = \"\"\"\n", "EventServiceEntries \n", "| where subscriptionId != \"0140911e-1040-48da-8bc9-b99fb3dd88a6\"\n", "| where operationName contains \"Microsoft.Databricks/workspaces/write\"\n", "| where status == \"Failed\" or status == \"Succeeded\"\n", "| project TIMESTAMP, subscriptionId, resourceUri, properties, status\n", "\"\"\"\n", "\n", "df_creations = kc.query(query, kc.ARM_DB, kc.ARM_CLUSTER)\n", "\n", "def get_region(resourceUri: str):\n", "    matchingputs= df_puts[df_puts.targetUri.str.contains(resourceUri)]\n", "    if(len(matchingputs) == 0):\n", "        return \"unknown\"\n", "    targetUri = matchingputs.iloc[0].targetUri\n", "    return targetUri[8:targetUri.index(\".adb-spearfish\")]\n", "    \n", "df_creations[\"region\"] = df_creations.resourceUri.apply(get_region)\n", "\n", "def transform(value):\n", "    if(value == \"\"):\n", "        return {}\n", "    jsonval = json.loads(value)\n", "    return json.loads(jsonval[\"statusMessage\"]) if \"statusMessage\" in jsonval else {}\n", "\n", "df_creations.properties = df_creations.properties.map(transform)\n", "df_creations = EventServiceEntriesAnalyzer.get_error_groups(df_creations)\n", "\n", "df_creations = df_creations[df_creations.region != \"unknown\"]"]}, {"cell_type": "code", "execution_count": 8, "metadata": {"scrolled": false}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["EASTUS2EUAP\n", "{'Succeeded': 12, 'Failed': 2}\n", "\n", "ResourceDeploymentFailure:ARMDocumentDBIssue : 2\n", "/subscriptions/596df088-441c-4a6f-881e-5511128a3f1c/resourcegroups/qa_rg_2a840ca0/providers/Microsoft.Databricks/workspaces/qa_workspace_trial_2a840ca0\n", "/subscriptions/596df088-441c-4a6f-881e-5511128a3f1c/resourcegroups/qa_rg_6a7a0ec9/providers/Microsoft.Databricks/workspaces/qa_workspace_premium_6a7a0ec9\n", "\n", "\n", "WESTUS\n", "{'Succeeded': 84, 'Failed': 6}\n", "\n", "ResourceDeploymentFailure:ARMDocumentDBIssue : 3\n", "/subscriptions/596df088-441c-4a6f-881e-5511128a3f1c/resourcegroups/qa_rg_0953d525/providers/Microsoft.Databricks/workspaces/qa_workspace_trial_0953d525\n", "/subscriptions/596df088-441c-4a6f-881e-5511128a3f1c/resourcegroups/qa_rg_0fb03659/providers/Microsoft.Databricks/workspaces/qa_workspace_trial_0fb03659\n", "/subscriptions/596df088-441c-4a6f-881e-5511128a3f1c/resourcegroups/qa_rg_5c8d29c9/providers/Microsoft.Databricks/workspaces/qa_workspace_standard_5c8d29c9\n", "\n", "ApplianceBeingDeleted:NA : 1\n", "/subscriptions/1be38912-a8f5-44e8-90ad-d38f992eafe9/resourcegroups/Suvamtestrg/providers/Microsoft.Databricks/workspaces/Databricks_Test\n", "\n", "ResourceDeploymentFailure:ApplianceProvisioningFailed : 1\n", "/subscriptions/e5323706-4762-423c-ac39-3a27e66a1d35/resourcegroups/westus-dust-staging-npip/providers/Microsoft.Databricks/workspaces/westus-dust-staging-npip\n", "\n", "ApplianceManagedResourceGroupMismatch:NA : 1\n", "/subscriptions/4aaf5510-3cdc-43c1-a291-5f74114265bd/resourcegroups/MTC_WRKSHP/providers/Microsoft.Databricks/workspaces/MTC_WORKSHOP_SSAHA\n", "\n", "\n", "EASTUS2\n", "{'Succeeded': 128}\n", "\n", "\n", "WESTEUROPE\n", "{'Succeeded': 93, 'Failed': 8}\n", "\n", "ResourceDeploymentFailure:LinkedAuthorizationFailed : 6\n", "/subscriptions/a6a625d1-7d41-491e-8a36-2d2c3fa45315/resourcegroups/ETL-DEV/providers/Microsoft.Databricks/workspaces/etl-databricks-dev\n", "/subscriptions/9fc52825-34f4-4845-91ea-d26f3f95e46e/resourcegroups/databricks/providers/Microsoft.Databricks/workspaces/soulaimabricks\n", "/subscriptions/2bc36c3d-0f68-4674-8823-46a991bfe54b/resourcegroups/databricks/providers/Microsoft.Databricks/workspaces/databricks\n", "/subscriptions/420fc6f7-92c1-4e71-bb56-847894de984e/resourcegroups/vibaiworkshop/providers/Microsoft.Databricks/workspaces/aiworkshop\n", "/subscriptions/ff01794f-f8f8-4dad-9ec9-798842e5d175/resourcegroups/TEST/providers/Microsoft.Databricks/workspaces/bricksws\n", "/subscriptions/07b98312-e57a-4ec5-b053-b3533aec1933/resourcegroups/datafactorybrickswesteurope/providers/Microsoft.Databricks/workspaces/datafactorybricks\n", "\n", "ApplianceBeingDeleted:NA : 1\n", "/subscriptions/86e48b56-abcb-4622-82fe-147e1b0ef872/resourcegroups/<PERSON>-<PERSON>/providers/Microsoft.Databricks/workspaces/bw-intern-jsc-test-adb\n", "\n", "ResourceDeploymentFailure:ApplianceProvisioningFailed : 1\n", "/subscriptions/b47e2ab2-a123-4442-97b2-116aa74eb4a6/resourcegroups/eva-dev-rsg/providers/Microsoft.Databricks/workspaces/eva-dev-dbs\n", "\n", "\n", "NORTHEUROPE\n", "{'Succeeded': 51, 'Failed': 8}\n", "\n", "InternalServerError:NA : 4\n", "/subscriptions/204671af-5130-4ef5-819c-e314b65f9d06/resourcegroups/bieno-da-d-60072-engine-rg/providers/Microsoft.Databricks/workspaces/ohub2-databricks-dev\n", "/subscriptions/204671af-5130-4ef5-819c-e314b65f9d06/resourcegroups/bieno-da-q-60072-engine-rg/providers/Microsoft.Databricks/workspaces/ohub2-databricks-qa\n", "/subscriptions/204671af-5130-4ef5-819c-e314b65f9d06/resourcegroups/bieno-da-u-60072-engine-rg/providers/Microsoft.Databricks/workspaces/ohub2-databricks-uat\n", "/subscriptions/204671af-5130-4ef5-819c-e314b65f9d06/resourcegroups/bieno-da-p-60072-engine-rg/providers/Microsoft.Databricks/workspaces/ohub2-databricks-prod\n", "\n", "ResourceDeploymentFailure:ARMDocumentDBIssue : 2\n", "/subscriptions/596df088-441c-4a6f-881e-5511128a3f1c/resourcegroups/qa_rg_b9dc341e/providers/Microsoft.Databricks/workspaces/qa_workspace_premium_b9dc341e\n", "/subscriptions/596df088-441c-4a6f-881e-5511128a3f1c/resourcegroups/qa_rg_a9528975/providers/Microsoft.Databricks/workspaces/qa_workspace_premium_a9528975\n", "\n", "ResourceDeploymentFailure:LinkedAuthorizationFailed : 1\n", "/subscriptions/fdf55827-2c67-4ef1-ac56-99746c586ec9/resourcegroups/DataBricksDemo/providers/Microsoft.Databricks/workspaces/demobricksnunon\n", "\n", "ResourceDeploymentFailure:ApplianceProvisioningFailed : 1\n", "/subscriptions/b460dc79-9170-46d8-96e3-1cf46f0dae6b/resourcegroups/da-development/providers/Microsoft.Databricks/workspaces/cae-dev-databricks\n", "\n", "\n", "EASTUS\n", "{'Succeeded': 93, 'Failed': 2}\n", "\n", "ResourceDeploymentFailure:ARMDocumentDBIssue : 1\n", "/subscriptions/596df088-441c-4a6f-881e-5511128a3f1c/resourcegroups/qa_rg_d2da3b1f/providers/Microsoft.Databricks/workspaces/qa_workspace_standard_d2da3b1f\n", "\n", "ResourceDeploymentFailure:InvalidApplianceState : 1\n", "/subscriptions/df67e625-9e97-4272-aea2-684377d878d7/resourcegroups/tata_power_solar_rg/providers/Microsoft.Databricks/workspaces/cntkpower\n", "\n", "\n", "SEASTASIA\n", "{'Succeeded': 45, 'Failed': 5}\n", "\n", "ResourceDeploymentFailure:ARMDocumentDBIssue : 4\n", "/subscriptions/596df088-441c-4a6f-881e-5511128a3f1c/resourcegroups/qa_rg_0858b072/providers/Microsoft.Databricks/workspaces/qa_workspace_trial_0858b072\n", "/subscriptions/596df088-441c-4a6f-881e-5511128a3f1c/resourcegroups/qa_rg_3fbcd3c9/providers/Microsoft.Databricks/workspaces/qa_workspace_standard_3fbcd3c9\n", "/subscriptions/596df088-441c-4a6f-881e-5511128a3f1c/resourcegroups/qa_rg_d8f6bde9/providers/Microsoft.Databricks/workspaces/qa_workspace_standard_d8f6bde9\n", "/subscriptions/596df088-441c-4a6f-881e-5511128a3f1c/resourcegroups/qa_rg_27cf023d/providers/Microsoft.Databricks/workspaces/qa_workspace_premium_27cf023d\n", "\n", "NoRegisteredProviderFound:NA : 1\n", "/subscriptions/e5323706-4762-423c-ac39-3a27e66a1d35/resourcegroups/seasia-dust-staging-npip/providers/Microsoft.Databricks/workspaces/seasia-dust-staging-npip\n", "\n", "\n", "EASTASIA\n", "{'Succeeded': 3}\n", "\n", "\n", "SCENTRALUS\n", "{'Succeeded': 47, 'Failed': 4}\n", "\n", "ResourceDeploymentFailure:LinkedAuthorizationFailed : 2\n", "/subscriptions/6605961d-177e-49ce-868d-289d6d1986de/resourcegroups/jit-rg-mllearning/providers/Microsoft.Databricks/workspaces/jit-databricks\n", "/subscriptions/de5115f6-b24f-45d9-897d-e26c480dce25/resourcegroups/AZD/providers/Microsoft.Databricks/workspaces/AZD\n", "\n", "InternalServerError:NA : 2\n", "/subscriptions/619f1ab9-764e-4308-8360-da885b1c9eaa/resourcegroups/paasstandard-s19us-20180404/providers/Microsoft.Databricks/workspaces/discovery-nomrgtest-ussc-dev\n", "/subscriptions/619f1ab9-764e-4308-8360-da885b1c9eaa/resourcegroups/paasstandard-s19us-20180404/providers/Microsoft.Databricks/workspaces/discovery-armtest-nomrg-ussc-dev\n", "\n", "\n", "NCENTRALUS\n", "{'Succeeded': 6}\n", "\n", "\n", "WESTUS2\n", "{'Succeeded': 42, 'Failed': 6}\n", "\n", "ResourceDeploymentFailure:LinkedAuthorizationFailed : 6\n", "/subscriptions/2b8031d9-b685-4037-982e-d827ef894008/resourcegroups/OneLake/providers/Microsoft.Databricks/workspaces/OneBrick\n", "/subscriptions/5bee875f-5823-47d8-91a2-26234b4b62d0/resourcegroups/familyvpnproto-rg/providers/Microsoft.Databricks/workspaces/famvpnproto-databricks\n", "/subscriptions/cbdc7928-5afd-4a27-9bc9-2b536b51b200/resourcegroups/azlab-629-rg/providers/Microsoft.Databricks/workspaces/azlab629_db_pa\n", "/subscriptions/cbdc7928-5afd-4a27-9bc9-2b536b51b200/resourcegroups/azlab-629-rg/providers/Microsoft.Databricks/workspaces/azlab629\n", "/subscriptions/b3fd76a6-caa7-447d-a901-0696990f6505/resourcegroups/azlab-239-rg/providers/Microsoft.Databricks/workspaces/azlab239databricks\n", "/subscriptions/14b25d2e-dc84-4dff-9aa3-9cc604047e64/resourcegroups/mydatabricksrp/providers/Microsoft.Databricks/workspaces/mydatabricksws\n", "\n", "\n", "CENTRALUS\n", "{'Succeeded': 26, 'Failed': 3}\n", "\n", "ResourceDeploymentFailure:LinkedAuthorizationFailed : 3\n", "/subscriptions/2a508aff-7f67-420e-a4f2-6dadf6faa8c7/resourcegroups/rg-IncedoAssetMark/providers/Microsoft.Databricks/workspaces/dataBricks-AssertMark\n", "/subscriptions/8cdef41c-f74b-4596-8b71-ed7e4c48be45/resourcegroups/DataAnalytics/providers/Microsoft.Databricks/workspaces/Test\n", "/subscriptions/06c203a1-bd58-4092-b5a4-ced8e97d9e2e/resourcegroups/wisecat-test/providers/Microsoft.Databricks/workspaces/wisecat-spark\n", "\n", "\n", "UKWEST\n", "{'Succeeded': 2}\n", "\n", "\n", "UKSOUTH\n", "{'Succeeded': 8, 'Failed': 1}\n", "\n", "ResourceDeploymentFailure:LinkedAuthorizationFailed : 1\n", "/subscriptions/f42a917a-0ca2-4656-8c64-9501bd4c5f26/resourcegroups/example-rg/providers/Microsoft.Databricks/workspaces/andy-ws\n", "\n", "\n", "AUSEAST\n", "{'Succeeded': 13}\n", "\n", "\n", "AUSSEAST\n", "{'Succeeded': 5, 'Failed': 1}\n", "\n", "ResourceDeploymentFailure:LinkedAuthorizationFailed : 1\n", "/subscriptions/b08669ad-e28e-4165-9e60-ad10fe4955fa/resourcegroups/poc_res_group/providers/Microsoft.Databricks/workspaces/poc-databricks\n", "\n", "\n", "AUSCENTRAL\n", "{}\n", "\n", "\n", "AUSCENTRAL2\n", "{}\n", "\n", "\n", "JAPANEAST\n", "{'Succeeded': 32, 'Failed': 8}\n", "\n", "ResourceDeploymentFailure:LinkedAuthorizationFailed : 7\n", "/subscriptions/3bd5fc22-3db1-4f08-9ee3-5557bea7f583/resourcegroups/EMTResourceGroupB1/providers/Microsoft.Databricks/workspaces/EMTDBRCB1\n", "/subscriptions/ee2bb161-08ec-4c51-bbe7-69772f43c3bc/resourcegroups/rg-p-moana-handson/providers/Microsoft.Databricks/workspaces/testwsdbb\n", "/subscriptions/5b865324-839b-4350-999c-227a3f3357e0/resourcegroups/GBB-20181128-databricks/providers/Microsoft.Databricks/workspaces/databricks-20181128-001\n", "/subscriptions/5b865324-839b-4350-999c-227a3f3357e0/resourcegroups/GBB-20181128-databricks/providers/Microsoft.Databricks/workspaces/gbbdatabricks20181128-001\n", "/subscriptions/5483e85c-abe7-42ca-a743-a765da050bc7/resourcegroups/20181128HandsOn/providers/Microsoft.Databricks/workspaces/20181128databricks\n", "/subscriptions/5483e85c-abe7-42ca-a743-a765da050bc7/resourcegroups/20181128HandsOn/providers/Microsoft.Databricks/workspaces/DatabricksService\n", "/subscriptions/78916214-b0cf-4053-9d53-82849c4f9ea8/resourcegroups/20181128-Databricks/providers/Microsoft.Databricks/workspaces/databrickstest\n", "\n", "ApplianceManagedResourceGroupMismatch:NA : 1\n", "/subscriptions/3bd5fc22-3db1-4f08-9ee3-5557bea7f583/resourcegroups/EMTResourceGroupB1/providers/Microsoft.Databricks/workspaces/emtdbrcb1\n", "\n", "\n", "JAPANWEST\n", "{'Succeeded': 10, 'Failed': 1}\n", "\n", "ResourceDeploymentFailure:LinkedAuthorizationFailed : 1\n", "/subscriptions/e286d118-46a2-41c3-9948-e7af2a6b231c/resourcegroups/TurnGreenVisualization/providers/Microsoft.Databricks/workspaces/TestMatsuBricks\n", "\n", "\n", "CANCENTRAL\n", "{'Succeeded': 10, 'Failed': 1}\n", "\n", "ApplianceManagedResourceGroupMismatch:NA : 1\n", "/subscriptions/f5c25c0b-39d1-48b7-a644-c1f6b4d83b49/resourcegroups/rganalytiquetmplab01/providers/Microsoft.Databricks/workspaces/hqdatabricksanalytiquetmplab0101\n", "\n", "\n", "CANEAST\n", "{'Succeeded': 4}\n", "\n", "\n", "INDCENTRAL\n", "{'Succeeded': 10}\n", "\n", "\n", "INDSOUTH\n", "{'Succeeded': 24, 'Failed': 6}\n", "\n", "ResourceDeploymentFailure:LinkedAuthorizationFailed : 5\n", "/subscriptions/68a2bb52-d095-44b1-88d7-da209b3733fd/resourcegroups/C4GResourceGroupFreeTrial/providers/Microsoft.Databricks/workspaces/C4GDataBrickFreeTrial\n", "/subscriptions/db8d3854-c79d-4980-a35a-73da607b7418/resourcegroups/safetypoc/providers/Microsoft.Databricks/workspaces/sampledata\n", "/subscriptions/e3866c3e-ec80-4f10-a068-ceb132672f14/resourcegroups/nagendraDataFacory/providers/Microsoft.Databricks/workspaces/HelloDataBricks\n", "/subscriptions/ed0b2672-8412-463f-a0c1-1867730d7e27/resourcegroups/ansirg2/providers/Microsoft.Databricks/workspaces/abDatabricks\n", "/subscriptions/f3329e6b-ef13-48b3-839b-45863ceee21e/resourcegroups/maya-dev/providers/Microsoft.Databricks/workspaces/maya-dev-db\n", "\n", "InvalidDatabricksSku:NA : 1\n", "/subscriptions/8fac0370-b046-4b2a-b2f4-fd6fc7df2776/resourcegroups/Raja/providers/Microsoft.Databricks/workspaces/eswar123\n", "\n", "\n", "INDWEST\n", "{'Succeeded': 10}\n", "\n", "\n"]}], "source": ["regions = [\"eastus2euap\", \"westus\", \"eastus2\", \"westeurope\", \"northeurope\", \"eastus\", \"seastasia\", \n", "           \"eastasia\", \"scentralus\", \"ncentralus\", \"westus2\", \"centralus\", \"ukwest\", \"uksouth\", \n", "           \"auseast\", \"ausseast\", \"auscentral\", \"auscentral2\", \"japaneast\", \"japanwest\", \"cancentral\", \"caneast\",\n", "           \"indcentral\", \"indsouth\", \"indwest\"]\n", "\n", "unique_creations = df_creations.drop_duplicates(\"resourceUri\")\n", "\n", "for current_region in regions:\n", "    print(current_region.upper())\n", "    df_region = unique_creations[unique_creations.region == current_region]\n", "    print(dict(df_region.status.value_counts()))\n", "    error_counts = dict(df_region[df_region.status == \"Failed\"].error_group.value_counts())\n", "    print()\n", "    for key in error_counts:\n", "        print(key + \" : \" + str(error_counts[key]))\n", "        for uri in list(df_region[(df_region.status == \"Failed\") & (df_region.error_group == key)].resourceUri):\n", "            print(uri)\n", "        print()\n", "    print()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.6.4"}}, "nbformat": 4, "nbformat_minor": 2}