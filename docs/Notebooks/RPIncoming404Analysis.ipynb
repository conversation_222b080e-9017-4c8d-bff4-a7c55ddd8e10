{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"data": {"text/plain": ["'2019-01-31 12:00:39.129270'"]}, "execution_count": 1, "metadata": {}, "output_type": "execute_result"}], "source": ["from datetime import datetime\n", "str(datetime.now())"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Incoming HTTP Request Error Analysis\n", "\n", "Below we look at all the non-successful (error code >= 300) incoming GET requests to the new RP. We filter out 404s where a DELETE request previously came in. We also filter out our test subscription."]}, {"cell_type": "code", "execution_count": 2, "metadata": {"scrolled": false}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["1\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>TIMESTAMP</th>\n", "      <th>subscriptionId</th>\n", "      <th>httpMethod</th>\n", "      <th>httpStatusCode</th>\n", "      <th>targetUri</th>\n", "      <th>region</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>2353</th>\n", "      <td>2019-01-31 18:33:39.573145200</td>\n", "      <td>d6c4783f-da66-431b-9bb7-ce719b946537</td>\n", "      <td>GET</td>\n", "      <td>404</td>\n", "      <td>https://eastus.adb-spearfish-rp.net:11707/subscriptions/d6c4783f-da66-431b-9bb7-ce719b946537/resourcegroups/test-databrick/providers/microsoft.databricks/workspaces/test-databrick?api-version=2018-04-01</td>\n", "      <td>eastus</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                         TIMESTAMP                        subscriptionId  \\\n", "2353 2019-01-31 18:33:39.573145200  d6c4783f-da66-431b-9bb7-ce719b946537   \n", "\n", "     httpMethod  httpStatusCode  \\\n", "2353  GET        404              \n", "\n", "                                                                                                                                                                                                       targetUri  \\\n", "2353  https://eastus.adb-spearfish-rp.net:11707/subscriptions/d6c4783f-da66-431b-9bb7-ce719b946537/resourcegroups/test-databrick/providers/microsoft.databricks/workspaces/test-databrick?api-version=2018-04-01   \n", "\n", "      region  \n", "2353  eastus  "]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["%load_ext autoreload\n", "%autoreload 2\n", "%reload_ext autoreload\n", "\n", "from kustoutils import KustoCaller\n", "import pandas as pd\n", "\n", "pd.set_option('display.max_colwidth', -1)\n", "\n", "query = \"\"\"\n", "HttpIncomingRequests \n", "| where httpStatusCode >= 300 or httpMethod == \"DELETE\"\n", "| where httpStatusCode > 0\n", "| where subscriptionId != \"0140911e-1040-48da-8bc9-b99fb3dd88a6\"\n", "| where subscriptionId != \"596df088-441c-4a6f-881e-5511128a3f1c\"\n", "| where targetUri contains \"Microsoft.Databricks\"\n", "| where targetUri !contains \"healthcheck\"\n", "| where TIMESTAMP >= datetime(2019-01-31 18:33:00) or httpMethod == \"DELETE\"\n", "| project TIMESTAMP, subscriptionId, httpMethod, httpStatusCode, targetUri\n", "| order by subscriptionId, TIMESTAMP asc\n", "\"\"\"\n", "\n", "kc = KustoCaller()\n", "df = kc.query(query, KustoCaller.RP_DB, KustoCaller.RP_CLUSTER)\n", "\n", "df.targetUri = df.targetUri.str.lower()\n", "\n", "deleted_uris = list(df[df.httpMethod == \"DELETE\"].targetUri.unique())\n", "bad_put_uris = list(df[(df.httpMethod == \"PUT\") & (df.httpStatusCode >= 400)].targetUri.unique())\n", "\n", "errors_df = df[df.httpMethod != \"DELETE\"]\n", "errors_df = errors_df[~((errors_df.httpMethod == \"GET\")\n", "                                  & (errors_df.httpStatusCode == 404)\n", "                                  & (errors_df.targetUri.isin(deleted_uris)))]\n", "errors_df = errors_df[~((errors_df.httpMethod == \"GET\")\n", "                                  & (errors_df.httpStatusCode == 404)\n", "                                  & (errors_df.targetUri.isin(bad_put_uris)))]\n", "\n", "errors_df = errors_df[errors_df.httpMethod == \"GET\"]\n", "errors_df = errors_df[errors_df.httpStatusCode == 404]\n", "\n", "errors404 = errors_df.drop_duplicates(\"targetUri\")\n", "\n", "def get_region(targetUri: str):\n", "    return targetUri[8:targetUri.index(\".adb-spearfish-rp.net\")]\n", "\n", "errors404[\"region\"] = errors404.targetUri.apply(get_region)\n", "\n", "print(len(errors404))\n", "errors404"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.6.4"}}, "nbformat": 4, "nbformat_minor": 2}