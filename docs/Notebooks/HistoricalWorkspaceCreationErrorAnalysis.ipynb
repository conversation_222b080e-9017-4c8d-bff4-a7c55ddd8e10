{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["## Databricks Workspace Creation Error Rate Analysis\n", "\n", "In this notebook we analyze Databricks workspace creation rates (as told by the EventServiceEntries table in ARMProd Kusto logs.)\n", "\n", "The kustoutils python module (kustoutils.py in this repo) does most of the analysis under the hood."]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"data": {"text/plain": ["'2018-12-04 22:29:23.095792'"]}, "execution_count": 1, "metadata": {}, "output_type": "execute_result"}], "source": ["from datetime import datetime\n", "str(datetime.now())"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["The autoreload extension is already loaded. To reload it, use:\n", "  %reload_ext autoreload\n"]}], "source": ["%load_ext autoreload\n", "%autoreload 2\n", "%reload_ext autoreload\n", "\n", "# Kusto utils is all in the kustoutils.py library\n", "from kustoutils import EventServiceEntriesAnalyzer\n", "import warnings\n", "warnings.filterwarnings('ignore')\n", "import pandas as pd\n", "\n", "pd.set_option('display.max_colwidth', -1)\n", "\n", "ks = EventServiceEntriesAnalyzer()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## The below queries the EventServiceEntries table in ARMProd.\n", "\n", "```sql\n", "EventServiceEntries \n", "| where operationName contains \"Microsoft.Databricks/workspaces/write\"\n", "| where subscriptionId != \"0140911e-1040-48da-8bc9-b99fb3dd88a6\"\n", "| where status == \"Failed\" or status == \"Succeeded\"\n", "| project TIMESTAMP, subscriptionId, status, resourceUri, properties\n", "```\n", "\n", "## Naieve success rates (not filtering out customer errors, duplicates, etc.)"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Success: 33514\n", "Failures: 5370\n", "Success rate: 0.8618969241847546\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>TIMESTAMP</th>\n", "      <th>subscriptionId</th>\n", "      <th>status</th>\n", "      <th>resourceUri</th>\n", "      <th>properties</th>\n", "      <th>outer_error</th>\n", "      <th>inner_error</th>\n", "      <th>error_group</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>1436</th>\n", "      <td>2018-11-30 22:18:06.786046500</td>\n", "      <td>ff16fbfa-64be-4db5-b836-94404c56877a</td>\n", "      <td>Failed</td>\n", "      <td>/subscriptions/ff16fbfa-64be-4db5-b836-94404c56877a/resourcegroups/newres/providers/Microsoft.Databricks/workspaces/databr</td>\n", "      <td>{'status': 'Failed', 'error': {'code': 'ResourceOperationFailure', 'message': 'The resource operation completed with terminal provisioning state 'Failed'.', 'details': [{'code': 'ApplianceDeploymentFailed', 'message': \"The operation to create appliance failed. Please check operations of deployment 'databr' under resource group '/subscriptions/ff16fbfa-64be-4db5-b836-94404c56877a/resourceGroups/databricks-rg-databr-jew4mdvwnrdek'. Error message: 'At least one resource deployment operation failed. Please list deployment operations for details. Please see https://aka.ms/arm-debug for usage details.'\", 'details': [{'code': 'Forbidden', 'message': '{\\r\\n  \"error\": {\\r\\n    \"code\": \"LinkedAuthorizationFailed\",\\r\\n    \"message\": \"The client has permission to perform action \\'Microsoft.Network/networkSecurityGroups/join/action\\' on scope \\'/subscriptions/ff16fbfa-64be-4db5-b836-94404c56877a/resourcegroups/databricks-rg-databr-jew4mdvwnrdek/providers/Microsoft.Network/virtualNetworks/workers-vnet\\', however the current tenant \\'2f4a9838-26b7-47ee-be60-ccc1fdec5953\\' is not authorized to access linked subscription \\'ff16fbfa-64be-4db5-b836-94404c56877a\\'.\"\\r\\n  }\\r\\n}'}]}]}}</td>\n", "      <td>ResourceDeploymentFailure</td>\n", "      <td>LinkedAuthorizationFailed</td>\n", "      <td>ResourceDeploymentFailure:LinkedAuthorizationFailed</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9037</th>\n", "      <td>2018-11-30 22:18:02.053744400</td>\n", "      <td>ff16fbfa-64be-4db5-b836-94404c56877a</td>\n", "      <td>Failed</td>\n", "      <td>/subscriptions/ff16fbfa-64be-4db5-b836-94404c56877a/resourcegroups/newres/providers/Microsoft.Databricks/workspaces/databr</td>\n", "      <td>{'status': 'Failed', 'error': {'code': 'ResourceDeploymentFailure', 'message': 'The resource operation completed with terminal provisioning state 'Failed'.', 'details': [{'code': 'ApplianceDeploymentFailed', 'message': \"The operation to create appliance failed. Please check operations of deployment 'databr' under resource group '/subscriptions/ff16fbfa-64be-4db5-b836-94404c56877a/resourceGroups/databricks-rg-databr-jew4mdvwnrdek'. Error message: 'At least one resource deployment operation failed. Please list deployment operations for details. Please see https://aka.ms/arm-debug for usage details.'\", 'details': [{'code': 'Forbidden', 'message': '{\\r\\n  \"error\": {\\r\\n    \"code\": \"LinkedAuthorizationFailed\",\\r\\n    \"message\": \"The client has permission to perform action \\'Microsoft.Network/networkSecurityGroups/join/action\\' on scope \\'/subscriptions/ff16fbfa-64be-4db5-b836-94404c56877a/resourcegroups/databricks-rg-databr-jew4mdvwnrdek/providers/Microsoft.Network/virtualNetworks/workers-vnet\\', however the current tenant \\'2f4a9838-26b7-47ee-be60-ccc1fdec5953\\' is not authorized to access linked subscription \\'ff16fbfa-64be-4db5-b836-94404c56877a\\'.\"\\r\\n  }\\r\\n}'}]}]}}</td>\n", "      <td>ResourceDeploymentFailure</td>\n", "      <td>LinkedAuthorizationFailed</td>\n", "      <td>ResourceDeploymentFailure:LinkedAuthorizationFailed</td>\n", "    </tr>\n", "    <tr>\n", "      <th>423</th>\n", "      <td>2018-11-30 22:14:03.347011200</td>\n", "      <td>ff16fbfa-64be-4db5-b836-94404c56877a</td>\n", "      <td>Failed</td>\n", "      <td>/subscriptions/ff16fbfa-64be-4db5-b836-94404c56877a/resourcegroups/mmtest/providers/Microsoft.Databricks/workspaces/databr</td>\n", "      <td>{'status': 'Failed', 'error': {'code': 'ResourceOperationFailure', 'message': 'The resource operation completed with terminal provisioning state 'Failed'.', 'details': [{'code': 'ApplianceDeploymentFailed', 'message': \"The operation to create appliance failed. Please check operations of deployment 'databr' under resource group '/subscriptions/ff16fbfa-64be-4db5-b836-94404c56877a/resourceGroups/databricks-rg-databr-ptvx5ogzjtgtg'. Error message: 'At least one resource deployment operation failed. Please list deployment operations for details. Please see https://aka.ms/arm-debug for usage details.'\", 'details': [{'code': 'Forbidden', 'message': '{\\r\\n  \"error\": {\\r\\n    \"code\": \"LinkedAuthorizationFailed\",\\r\\n    \"message\": \"The client has permission to perform action \\'Microsoft.Network/networkSecurityGroups/join/action\\' on scope \\'/subscriptions/ff16fbfa-64be-4db5-b836-94404c56877a/resourcegroups/databricks-rg-databr-ptvx5ogzjtgtg/providers/Microsoft.Network/virtualNetworks/workers-vnet\\', however the current tenant \\'2f4a9838-26b7-47ee-be60-ccc1fdec5953\\' is not authorized to access linked subscription \\'ff16fbfa-64be-4db5-b836-94404c56877a\\'.\"\\r\\n  }\\r\\n}'}]}]}}</td>\n", "      <td>ResourceDeploymentFailure</td>\n", "      <td>LinkedAuthorizationFailed</td>\n", "      <td>ResourceDeploymentFailure:LinkedAuthorizationFailed</td>\n", "    </tr>\n", "    <tr>\n", "      <th>421</th>\n", "      <td>2018-11-30 22:14:03.251037400</td>\n", "      <td>ff16fbfa-64be-4db5-b836-94404c56877a</td>\n", "      <td>Failed</td>\n", "      <td>/subscriptions/ff16fbfa-64be-4db5-b836-94404c56877a/resourcegroups/mmtest/providers/Microsoft.Databricks/workspaces/databr</td>\n", "      <td>{'status': 'Failed', 'error': {'code': 'ResourceDeploymentFailure', 'message': 'The resource operation completed with terminal provisioning state 'Failed'.', 'details': [{'code': 'ApplianceDeploymentFailed', 'message': \"The operation to create appliance failed. Please check operations of deployment 'databr' under resource group '/subscriptions/ff16fbfa-64be-4db5-b836-94404c56877a/resourceGroups/databricks-rg-databr-ptvx5ogzjtgtg'. Error message: 'At least one resource deployment operation failed. Please list deployment operations for details. Please see https://aka.ms/arm-debug for usage details.'\", 'details': [{'code': 'Forbidden', 'message': '{\\r\\n  \"error\": {\\r\\n    \"code\": \"LinkedAuthorizationFailed\",\\r\\n    \"message\": \"The client has permission to perform action \\'Microsoft.Network/networkSecurityGroups/join/action\\' on scope \\'/subscriptions/ff16fbfa-64be-4db5-b836-94404c56877a/resourcegroups/databricks-rg-databr-ptvx5ogzjtgtg/providers/Microsoft.Network/virtualNetworks/workers-vnet\\', however the current tenant \\'2f4a9838-26b7-47ee-be60-ccc1fdec5953\\' is not authorized to access linked subscription \\'ff16fbfa-64be-4db5-b836-94404c56877a\\'.\"\\r\\n  }\\r\\n}'}]}]}}</td>\n", "      <td>ResourceDeploymentFailure</td>\n", "      <td>LinkedAuthorizationFailed</td>\n", "      <td>ResourceDeploymentFailure:LinkedAuthorizationFailed</td>\n", "    </tr>\n", "    <tr>\n", "      <th>21891</th>\n", "      <td>2018-11-30 22:07:57.217984100</td>\n", "      <td>ff16fbfa-64be-4db5-b836-94404c56877a</td>\n", "      <td>Failed</td>\n", "      <td>/subscriptions/ff16fbfa-64be-4db5-b836-94404c56877a/resourcegroups/rg-aml-lab/providers/Microsoft.Databricks/workspaces/ws-databricks</td>\n", "      <td>{'status': 'Failed', 'error': {'code': 'ResourceDeploymentFailure', 'message': 'The resource operation completed with terminal provisioning state 'Failed'.', 'details': [{'code': 'ApplianceDeploymentFailed', 'message': \"The operation to create appliance failed. Please check operations of deployment 'ws-databricks' under resource group '/subscriptions/ff16fbfa-64be-4db5-b836-94404c56877a/resourceGroups/databricks-rg-ws-databricks-e3kwihsvsyv2u'. Error message: 'At least one resource deployment operation failed. Please list deployment operations for details. Please see https://aka.ms/arm-debug for usage details.'\", 'details': [{'code': 'Forbidden', 'message': '{\\r\\n  \"error\": {\\r\\n    \"code\": \"LinkedAuthorizationFailed\",\\r\\n    \"message\": \"The client has permission to perform action \\'Microsoft.Network/networkSecurityGroups/join/action\\' on scope \\'/subscriptions/ff16fbfa-64be-4db5-b836-94404c56877a/resourcegroups/databricks-rg-ws-databricks-e3kwihsvsyv2u/providers/Microsoft.Network/virtualNetworks/workers-vnet\\', however the current tenant \\'2f4a9838-26b7-47ee-be60-ccc1fdec5953\\' is not authorized to access linked subscription \\'ff16fbfa-64be-4db5-b836-94404c56877a\\'.\"\\r\\n  }\\r\\n}'}]}]}}</td>\n", "      <td>ResourceDeploymentFailure</td>\n", "      <td>LinkedAuthorizationFailed</td>\n", "      <td>ResourceDeploymentFailure:LinkedAuthorizationFailed</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                          TIMESTAMP                        subscriptionId  \\\n", "1436  2018-11-30 22:18:06.786046500  ff16fbfa-64be-4db5-b836-94404c56877a   \n", "9037  2018-11-30 22:18:02.053744400  ff16fbfa-64be-4db5-b836-94404c56877a   \n", "423   2018-11-30 22:14:03.347011200  ff16fbfa-64be-4db5-b836-94404c56877a   \n", "421   2018-11-30 22:14:03.251037400  ff16fbfa-64be-4db5-b836-94404c56877a   \n", "21891 2018-11-30 22:07:57.217984100  ff16fbfa-64be-4db5-b836-94404c56877a   \n", "\n", "       status  \\\n", "1436   Failed   \n", "9037   Failed   \n", "423    Failed   \n", "421    Failed   \n", "21891  Failed   \n", "\n", "                                                                                                                                 resourceUri  \\\n", "1436   /subscriptions/ff16fbfa-64be-4db5-b836-94404c56877a/resourcegroups/newres/providers/Microsoft.Databricks/workspaces/databr              \n", "9037   /subscriptions/ff16fbfa-64be-4db5-b836-94404c56877a/resourcegroups/newres/providers/Microsoft.Databricks/workspaces/databr              \n", "423    /subscriptions/ff16fbfa-64be-4db5-b836-94404c56877a/resourcegroups/mmtest/providers/Microsoft.Databricks/workspaces/databr              \n", "421    /subscriptions/ff16fbfa-64be-4db5-b836-94404c56877a/resourcegroups/mmtest/providers/Microsoft.Databricks/workspaces/databr              \n", "21891  /subscriptions/ff16fbfa-64be-4db5-b836-94404c56877a/resourcegroups/rg-aml-lab/providers/Microsoft.Databricks/workspaces/ws-databricks   \n", "\n", "                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                  properties  \\\n", "1436   {'status': 'Failed', 'error': {'code': 'ResourceOperationFailure', 'message': 'The resource operation completed with terminal provisioning state 'Failed'.', 'details': [{'code': 'ApplianceDeploymentFailed', 'message': \"The operation to create appliance failed. Please check operations of deployment 'databr' under resource group '/subscriptions/ff16fbfa-64be-4db5-b836-94404c56877a/resourceGroups/databricks-rg-databr-jew4mdvwnrdek'. Error message: 'At least one resource deployment operation failed. Please list deployment operations for details. Please see https://aka.ms/arm-debug for usage details.'\", 'details': [{'code': 'Forbidden', 'message': '{\\r\\n  \"error\": {\\r\\n    \"code\": \"LinkedAuthorizationFailed\",\\r\\n    \"message\": \"The client has permission to perform action \\'Microsoft.Network/networkSecurityGroups/join/action\\' on scope \\'/subscriptions/ff16fbfa-64be-4db5-b836-94404c56877a/resourcegroups/databricks-rg-databr-jew4mdvwnrdek/providers/Microsoft.Network/virtualNetworks/workers-vnet\\', however the current tenant \\'2f4a9838-26b7-47ee-be60-ccc1fdec5953\\' is not authorized to access linked subscription \\'ff16fbfa-64be-4db5-b836-94404c56877a\\'.\"\\r\\n  }\\r\\n}'}]}]}}                         \n", "9037   {'status': 'Failed', 'error': {'code': 'ResourceDeploymentFailure', 'message': 'The resource operation completed with terminal provisioning state 'Failed'.', 'details': [{'code': 'ApplianceDeploymentFailed', 'message': \"The operation to create appliance failed. Please check operations of deployment 'databr' under resource group '/subscriptions/ff16fbfa-64be-4db5-b836-94404c56877a/resourceGroups/databricks-rg-databr-jew4mdvwnrdek'. Error message: 'At least one resource deployment operation failed. Please list deployment operations for details. Please see https://aka.ms/arm-debug for usage details.'\", 'details': [{'code': 'Forbidden', 'message': '{\\r\\n  \"error\": {\\r\\n    \"code\": \"LinkedAuthorizationFailed\",\\r\\n    \"message\": \"The client has permission to perform action \\'Microsoft.Network/networkSecurityGroups/join/action\\' on scope \\'/subscriptions/ff16fbfa-64be-4db5-b836-94404c56877a/resourcegroups/databricks-rg-databr-jew4mdvwnrdek/providers/Microsoft.Network/virtualNetworks/workers-vnet\\', however the current tenant \\'2f4a9838-26b7-47ee-be60-ccc1fdec5953\\' is not authorized to access linked subscription \\'ff16fbfa-64be-4db5-b836-94404c56877a\\'.\"\\r\\n  }\\r\\n}'}]}]}}                        \n", "423    {'status': 'Failed', 'error': {'code': 'ResourceOperationFailure', 'message': 'The resource operation completed with terminal provisioning state 'Failed'.', 'details': [{'code': 'ApplianceDeploymentFailed', 'message': \"The operation to create appliance failed. Please check operations of deployment 'databr' under resource group '/subscriptions/ff16fbfa-64be-4db5-b836-94404c56877a/resourceGroups/databricks-rg-databr-ptvx5ogzjtgtg'. Error message: 'At least one resource deployment operation failed. Please list deployment operations for details. Please see https://aka.ms/arm-debug for usage details.'\", 'details': [{'code': 'Forbidden', 'message': '{\\r\\n  \"error\": {\\r\\n    \"code\": \"LinkedAuthorizationFailed\",\\r\\n    \"message\": \"The client has permission to perform action \\'Microsoft.Network/networkSecurityGroups/join/action\\' on scope \\'/subscriptions/ff16fbfa-64be-4db5-b836-94404c56877a/resourcegroups/databricks-rg-databr-ptvx5ogzjtgtg/providers/Microsoft.Network/virtualNetworks/workers-vnet\\', however the current tenant \\'2f4a9838-26b7-47ee-be60-ccc1fdec5953\\' is not authorized to access linked subscription \\'ff16fbfa-64be-4db5-b836-94404c56877a\\'.\"\\r\\n  }\\r\\n}'}]}]}}                         \n", "421    {'status': 'Failed', 'error': {'code': 'ResourceDeploymentFailure', 'message': 'The resource operation completed with terminal provisioning state 'Failed'.', 'details': [{'code': 'ApplianceDeploymentFailed', 'message': \"The operation to create appliance failed. Please check operations of deployment 'databr' under resource group '/subscriptions/ff16fbfa-64be-4db5-b836-94404c56877a/resourceGroups/databricks-rg-databr-ptvx5ogzjtgtg'. Error message: 'At least one resource deployment operation failed. Please list deployment operations for details. Please see https://aka.ms/arm-debug for usage details.'\", 'details': [{'code': 'Forbidden', 'message': '{\\r\\n  \"error\": {\\r\\n    \"code\": \"LinkedAuthorizationFailed\",\\r\\n    \"message\": \"The client has permission to perform action \\'Microsoft.Network/networkSecurityGroups/join/action\\' on scope \\'/subscriptions/ff16fbfa-64be-4db5-b836-94404c56877a/resourcegroups/databricks-rg-databr-ptvx5ogzjtgtg/providers/Microsoft.Network/virtualNetworks/workers-vnet\\', however the current tenant \\'2f4a9838-26b7-47ee-be60-ccc1fdec5953\\' is not authorized to access linked subscription \\'ff16fbfa-64be-4db5-b836-94404c56877a\\'.\"\\r\\n  }\\r\\n}'}]}]}}                        \n", "21891  {'status': 'Failed', 'error': {'code': 'ResourceDeploymentFailure', 'message': 'The resource operation completed with terminal provisioning state 'Failed'.', 'details': [{'code': 'ApplianceDeploymentFailed', 'message': \"The operation to create appliance failed. Please check operations of deployment 'ws-databricks' under resource group '/subscriptions/ff16fbfa-64be-4db5-b836-94404c56877a/resourceGroups/databricks-rg-ws-databricks-e3kwihsvsyv2u'. Error message: 'At least one resource deployment operation failed. Please list deployment operations for details. Please see https://aka.ms/arm-debug for usage details.'\", 'details': [{'code': 'Forbidden', 'message': '{\\r\\n  \"error\": {\\r\\n    \"code\": \"LinkedAuthorizationFailed\",\\r\\n    \"message\": \"The client has permission to perform action \\'Microsoft.Network/networkSecurityGroups/join/action\\' on scope \\'/subscriptions/ff16fbfa-64be-4db5-b836-94404c56877a/resourcegroups/databricks-rg-ws-databricks-e3kwihsvsyv2u/providers/Microsoft.Network/virtualNetworks/workers-vnet\\', however the current tenant \\'2f4a9838-26b7-47ee-be60-ccc1fdec5953\\' is not authorized to access linked subscription \\'ff16fbfa-64be-4db5-b836-94404c56877a\\'.\"\\r\\n  }\\r\\n}'}]}]}}   \n", "\n", "                     outer_error                inner_error  \\\n", "1436   ResourceDeploymentFailure  LinkedAuthorizationFailed   \n", "9037   ResourceDeploymentFailure  LinkedAuthorizationFailed   \n", "423    ResourceDeploymentFailure  LinkedAuthorizationFailed   \n", "421    ResourceDeploymentFailure  LinkedAuthorizationFailed   \n", "21891  ResourceDeploymentFailure  LinkedAuthorizationFailed   \n", "\n", "                                               error_group  \n", "1436   ResourceDeploymentFailure:LinkedAuthorizationFailed  \n", "9037   ResourceDeploymentFailure:LinkedAuthorizationFailed  \n", "423    ResourceDeploymentFailure:LinkedAuthorizationFailed  \n", "421    ResourceDeploymentFailure:LinkedAuthorizationFailed  \n", "21891  ResourceDeploymentFailure:LinkedAuthorizationFailed  "]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["errors, good = ks.get_tables()\n", "\n", "success_rate = len(good) / (len(errors) + len(good))\n", "\n", "print(\"Success: \" + str(len(good)))\n", "print(\"Failures: \" + str(len(errors)))\n", "print(\"Success rate: \" + str(success_rate))\n", "\n", "errors.head(5)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## To make the analysis easier, lets drop duplicates resourceUris. This will allow us to see \"first\" errors."]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Success: 33514\n", "Failures: 1285\n", "Modified success rate: 0.963073651541711\n"]}], "source": ["first_errors = errors.drop_duplicates(\"resourceUri\")\n", "\n", "success_rate = len(good) / (len(first_errors) + len(good))\n", "\n", "print(\"Success: \" + str(len(good)))\n", "print(\"Failures: \" + str(len(first_errors)))\n", "print(\"Modified success rate: \" + str(success_rate))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Lets group the failures by error type."]}, {"cell_type": "code", "execution_count": 5, "metadata": {"scrolled": false}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["First errors:\n"]}, {"data": {"text/plain": ["ResourceDeploymentFailure:LinkedAuthorizationFailed                637\n", "ResourceDeploymentFailure:ApplianceProvisioningFailed              172\n", "ResourceDeploymentFailure:InvalidApplianceState                    93 \n", "ApplianceBeingDeleted:NA                                           93 \n", "ResourceDeploymentFailure:PolicyError                              66 \n", "ApplianceManagedResourceGroupMismatch:NA                           34 \n", "NaN                                                                22 \n", "InvalidApplicationManagedResourceGroupId:NA                        21 \n", "ResourceDeploymentFailure:NA                                       19 \n", "InvalidApplicationName:NA                                          11 \n", "ApplicationNotFound:NA                                             10 \n", "ServerClosedRequest:NA                                             8  \n", "NoRegisteredProviderFound:NA                                       6  \n", "ApplicationUpdateFail:ResourceGroupNotFound                        6  \n", "ApplicationUpdateFail:PatchResourceNotFound                        6  \n", "InternalServerError:NA                                             6  \n", "ResourceGroupNotFound:NA                                           6  \n", "ResourceDeploymentFailure:SubscriptionNotRegistered                5  \n", "ResourceDeploymentFailure:ResourceDeploymentFailure                5  \n", "InvalidRequestContent:NA                                           5  \n", "MissingSubscriptionRegistration:MissingSubscriptionRegistration    5  \n", "ApplicationUpdateFail:PolicyError                                  4  \n", "ResourceDeploymentFailure:AuthorizationFailed                      4  \n", "PatchResourceNotFound:NA                                           4  \n", "InvalidDatabricksSkuUpdate:NA                                      3  \n", "ResourceNotFound:NA                                                2  \n", "ResourceDeploymentFailure:ServerClosedRequest                      2  \n", "LocationRequired:NA                                                2  \n", "InvalidDatabricksSku:NA                                            2  \n", "ReadOnlyDisabledSubscription:NA                                    2  \n", "ResourceDeploymentFailure:ApplianceDeploymentFailed                2  \n", "InvalidResourceId:NA                                               2  \n", "ResourceDeploymentFailure:SubscriptionOperationInProgress          2  \n", "AuthorizationFailed:NA                                             2  \n", "InvalidTemplate:NA                                                 2  \n", "ResourcesBeingMoved:NA                                             1  \n", "ResourceGroupBeingDeleted:NA                                       1  \n", "RequestDisallowedByPolicy:PolicyError                              1  \n", "ScopeLocked:NA                                                     1  \n", "InvalidResourceLocation:NA                                         1  \n", "MultipleErrorsOccurred:ServerTimeout                               1  \n", "AuthenticationFailed:NA                                            1  \n", "ResourceDeploymentFailure:SubscriptionNotFound                     1  \n", "GatewayTimeout:NA                                                  1  \n", "ApplicationUpdateFail:ResourceGroupBeingDeleted                    1  \n", "ResourceDeploymentFailure:StorageAccountAlreadyTaken               1  \n", "ResourceDeploymentFailure:AccountNameInvalid                       1  \n", "ResourceDeploymentFailure:StorageAccountAlreadyExists              1  \n", "PatchResourceReadFailed:NA                                         1  \n", "Name: error_group, dtype: int64"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["print(\"First errors:\")\n", "first_errors.error_group.value_counts(dropna=False)"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["All errors:\n"]}, {"data": {"text/plain": ["InternalServerError:NA                                             2291\n", "ResourceDeploymentFailure:LinkedAuthorizationFailed                1379\n", "ResourceDeploymentFailure:ApplianceProvisioningFailed              449 \n", "ApplianceBeingDeleted:NA                                           215 \n", "ResourceDeploymentFailure:InvalidApplianceState                    162 \n", "ResourceDeploymentFailure:PolicyError                              148 \n", "NaN                                                                115 \n", "ApplicationUpdateFail:PolicyError                                  115 \n", "ApplianceManagedResourceGroupMismatch:NA                           106 \n", "InvalidApplicationManagedResourceGroupId:NA                        89  \n", "ResourceDeploymentFailure:NA                                       35  \n", "ApplicationUpdateFail:PatchResourceNotFound                        25  \n", "InvalidApplicationName:NA                                          24  \n", "MissingSubscriptionRegistration:MissingSubscriptionRegistration    23  \n", "NoRegisteredProviderFound:NA                                       18  \n", "ApplicationUpdateFail:ResourceGroupNotFound                        18  \n", "InvalidRequestContent:NA                                           14  \n", "ResourceDeploymentFailure:ResourceDeploymentFailure                11  \n", "ApplicationNotFound:NA                                             11  \n", "ResourceDeploymentFailure:SubscriptionNotRegistered                10  \n", "ScopeLocked:NA                                                     9   \n", "ServerClosedRequest:NA                                             9   \n", "ResourceNotFound:NA                                                8   \n", "InvalidDatabricksSkuUpdate:NA                                      8   \n", "InvalidTemplate:NA                                                 6   \n", "ResourceGroupNotFound:NA                                           6   \n", "LocationRequired:NA                                                6   \n", "ResourceDeploymentFailure:AuthorizationFailed                      6   \n", "InvalidResourceId:NA                                               4   \n", "PatchResourceNotFound:NA                                           4   \n", "ResourceDeploymentFailure:ServerClosedRequest                      4   \n", "InvalidDatabricksSku:NA                                            4   \n", "ResourceDeploymentFailure:SubscriptionOperationInProgress          4   \n", "GatewayTimeout:NA                                                  3   \n", "ResourceDeploymentFailure:ApplianceDeploymentFailed                3   \n", "PatchResourceReadFailed:NA                                         3   \n", "ResourceDeploymentFailure:AccountNameInvalid                       3   \n", "InvalidResourceLocation:NA                                         2   \n", "ReadOnlyDisabledSubscription:NA                                    2   \n", "ResourceDeploymentFailure:StorageAccountAlreadyTaken               2   \n", "ResourceDeploymentFailure:StorageAccountAlreadyExists              2   \n", "AuthenticationFailed:NA                                            2   \n", "AuthorizationFailed:NA                                             2   \n", "ResourcesBeingMoved:NA                                             2   \n", "LocationNotAvailableForResourceType:NA                             1   \n", "ResourceDeploymentFailure:SubscriptionNotFound                     1   \n", "ServerTimeout:NA                                                   1   \n", "RequestDisallowedByPolicy:PolicyError                              1   \n", "ApplicationUpdateFail:ResourceGroupBeingDeleted                    1   \n", "ResourceGroupBeingDeleted:NA                                       1   \n", "MultipleErrorsOccurred:ServerTimeout                               1   \n", "InvalidResource:NA                                                 1   \n", "Name: error_group, dtype: int64"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["print(\"All errors:\")\n", "errors.error_group.value_counts(dropna=False)"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.6.4"}}, "nbformat": 4, "nbformat_minor": 2}