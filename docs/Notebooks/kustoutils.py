import pandas as pd
import json

from azure.kusto.data.request import KustoC<PERSON>, KustoConnectionStringBuilder
from azure.kusto.data.helpers import dataframe_from_result_table
import os


class KustoCaller:

    RP_CLUSTER = "https://azuredatabricksrp.kusto.windows.net"
    RP_DB = "AzureDatabricksRP"

    ARM_CLUSTER = "https://armprod.kusto.windows.net:443"
    ARM_DB = "ARMProd"

    client_id = os.environ["DATABRICKSRPTESTER_APPID"]
    authority_id = os.environ["DATABRICKSRPTESTER_TENANTID"]
    client_key = os.environ["DATABRICKSRPTESTER_KEY"]

    def rp_get_putdelete_requests(self):
        return self.query("""
HttpIncomingRequests 
| where httpMethod == "DELETE" or httpMethod == "PUT"
| where httpStatusCode > 0
| where subscriptionId != "0140911e-1040-48da-8bc9-b99fb3dd88a6"
| where target<PERSON><PERSON> contains "Microsoft.Databricks"
| order by TIMESTAMP asc
""", self.RP_DB, self.RP_CLUSTER)

    def rp_get_error_requests(self):
        return self.query("""
HttpIncomingRequests 
| where httpStatusCode > 300 or httpMethod == "DELETE"
| where httpStatusCode > 0
| where subscriptionId != "0140911e-1040-48da-8bc9-b99fb3dd88a6"
| where targetUri contains "Microsoft.Databricks"
| where targetUri !contains "healthcheck"
| where TIMESTAMP >= ago(7d)
| project TIMESTAMP, subscriptionId, httpMethod, httpStatusCode, targetUri
| order by subscriptionId, TIMESTAMP asc
""", self.RP_DB, self.RP_CLUSTER)

    def arm_get_writedelete_events(self):
        return self.query("""
EventServiceEntries 
| where operationName contains "Microsoft.Databricks/workspaces/write"
| where subscriptionId != "0140911e-1040-48da-8bc9-b99fb3dd88a6"
| where status == "Failed" or status == "Succeeded"
| project TIMESTAMP, subscriptionId, status, resourceUri, properties
        """, self.ARM_DB, self.ARM_CLUSTER)

    def query(self, querystr, db, cluster):
        kcsb = KustoConnectionStringBuilder.with_aad_application_key_authentication(
            cluster, self.client_id, self.client_key, self.authority_id
        )
        client = KustoClient(kcsb)
        response = client.execute(db, querystr)
        df = dataframe_from_result_table(response.primary_results[0])
        df.TIMESTAMP = pd.to_datetime(df.TIMESTAMP, infer_datetime_format=True)
        df = df.sort_values("TIMESTAMP", ascending=False)
        return df


class HttpIncomingRequestsAnalyzer:

    def __init__(self):
        kc = KustoCaller()
        self.df = kc.rp_get_error_requests()

        self.df.targetUri = self.df.targetUri.str.lower()

        deleted_uris = list(self.df[self.df.httpMethod == "DELETE"].targetUri.unique())
        bad_put_uris = list(self.df[(self.df.httpMethod == "PUT") & (self.df.httpStatusCode == 400)].targetUri.unique())

        self.errors_df = self.df[self.df.httpMethod != "DELETE"]
        self.errors_df = self.errors_df[~((self.errors_df.httpMethod == "GET")
                                          & (self.errors_df.httpStatusCode == 404)
                                          & (self.errors_df.targetUri.isin(deleted_uris)))]
        self.errors_df = self.errors_df[~((self.errors_df.httpMethod == "GET")
                                          & (self.errors_df.httpStatusCode == 404)
                                          & (self.errors_df.targetUri.isin(bad_put_uris)))]

    @property
    def true_errors(self):
        return self.errors_df


class EventServiceEntriesAnalyzer:

    NON_ERRORS = ['ApplianceBeingDeleted', 'NoRegisteredProviderFound', 'MissingSubscriptionRegistration',
                  'InvalidApplicationName', 'InvalidResourceId', 'LocationRequired', 'ResourceGroupBeingDeleted',
                  'RequestDisallowedByPolicy', 'InvalidDatabricksSku', 'InvalidDatabricksSkuUpdate',
                  'ResourcesBeingMoved', 'PatchResourceNotFound', 'AuthorizationFailed', 'InvalidResource',
                  'InvalidRequestContent', 'InvalidApplicationManagedResourceGroupId',
                  'ApplianceManagedResourceGroupMismatch', 'RequestDisallowedByPolicy']

    NON_ERROR_MESSAGES = ["is in an invalid state 'Deleting'", "is a trademarked or reserved word",
                          "because of policy violation", "was disallowed by policy", "RequestDisallowedByPolicy"]

    NON_ERROR_TOKENS = ["RequestDisallowedByPolicy", "A resource with the same name cannot be created in location"]

    events_df: pd.DataFrame = None

    def __init__(self):
        kc = KustoCaller()

        self.events_df = kc.arm_get_writedelete_events()
        self.bad: pd.DataFrame = self.events_df[self.events_df.status == "Failed"]

        def transform(value):
            jsonval = json.loads(value)
            return json.loads(jsonval["statusMessage"]) if "statusMessage" in jsonval else {}

        self.bad.properties = self.bad.properties.map(transform)
        self.good = self.events_df[self.events_df.status != "Failed"]
        self.bad = self.get_error_groups(self.bad)

    @property
    def first_time(self) -> pd.Timestamp:
        return self.events_df.iloc[-1].TIMESTAMP

    @property
    def last_time(self) -> pd.Timestamp:
        return self.events_df.iloc[0].TIMESTAMP

    def get_tables(self):
        return self.bad, self.good

    # def get_counts_per_hour(self) -> pd.DataFrame:
    #     first = self.first_time
    #     first = first.round('H')
    #     last = self.last_time
    #     current_time = first
    #
    #     times = []
    #     reds = []
    #     yellows = []
    #     greens = []
    #
    #     while current_time <= last:
    #         red, yellow, green = self.get_tables(start=current_time, hours=1)
    #         times.append(current_time)
    #         reds.append(len(red))
    #         yellows.append(len(yellow))
    #         greens.append(len(green))
    #         current_time += pd.DateOffset(hours=1)
    #
    #     counts_df = pd.DataFrame()
    #
    #     counts_df["greens"] = greens
    #     counts_df["yellows"] = yellows
    #     counts_df["reds"] = reds
    #     # counts_df = counts_df.sort_values(ascending=False)
    #
    #     countssum = (counts_df.reds + counts_df.greens + counts_df.yellows)
    #
    #     counts_df["gc"] = (counts_df.greens / countssum)
    #     counts_df["yc"] = (counts_df.yellows / countssum)
    #     counts_df["rc"] = (counts_df.reds / countssum)
    #
    #     counts_df['error_zscore'] = (counts_df.rc - counts_df.rc.mean()) / counts_df.rc.std(ddof=0)
    #
    #     counts_df.index = times # counts_df['TIMESTAMP']
    #
    #     return counts_df

    @staticmethod
    def _time_filter(df: pd.DataFrame, start: pd.Timestamp, end: pd.Timestamp):
        return df[(df.TIMESTAMP >= start) & (df.TIMESTAMP <= end)]

    @staticmethod
    def get_error_groups(bad: pd.DataFrame):
        bad["outer_error"] = bad.properties.apply(lambda row: row["error"]["code"] if "error" in row else None)
        bad["outer_error"] = bad.outer_error.apply(lambda error: "ResourceDeploymentFailure" if
        error == "ResourceOperationFailure" else error)

        def get_error_details(properties):
            if "error" not in properties or "details" not in properties["error"]:
                return {}
            return properties["error"]["details"][0]

        def get_inner_details(error_details):
            if "details" not in error_details:
                return {}
            return error_details["details"][0]

        def get_inner_error_message(inner_details):
            if "message" not in inner_details:
                return {}
            try:
                return json.loads(inner_details["message"])
            except Exception as e:
                return {}

        policy_tokens = ["was disallowed by policy", "policy violation"]

        def get_inner_error_group(row):
            if any([token in str(row.properties) for token in policy_tokens]):
                return "PolicyError"

            if 'error' in row.inner_message:
                return row.inner_message['error']['code']

            inner_code = row.error_details.get('code', "NA")

            if inner_code == "ApplianceProvisioningFailed" and "server error" in str(row.error_details) and "request correlation id" in str(row.error_details):
                return "ARMDocumentDBIssue"

            return inner_code

        bad["error_details"] = bad.properties.apply(get_error_details)
        bad["inner_details"] = bad.error_details.apply(get_inner_details)
        bad["inner_message"] = bad.inner_details.apply(get_inner_error_message)
        bad["inner_error"] = bad.apply(get_inner_error_group, axis=1)

        bad_with_error_groups = bad.drop(["inner_details", "error_details", "inner_message"], axis=1)
        bad_with_error_groups["error_group"] = bad_with_error_groups.outer_error + ":" + bad_with_error_groups.inner_error

        return bad_with_error_groups
