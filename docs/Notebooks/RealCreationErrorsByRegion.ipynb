{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {"scrolled": false}, "outputs": [{"data": {"text/plain": ["'2018-12-07 14:15:00.785855'"]}, "execution_count": 1, "metadata": {}, "output_type": "execute_result"}], "source": ["from datetime import datetime\n", "str(datetime.now())"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Workspace Creations by Region\n", "\n", "Below we look at workspace creations to the new RP by region. We use the EventServiceEntriesAnalyzer.get_error_groups (in the kustoutils.py file in this repo) to break the errors into groups. <br>\n", "\n", "The empty regions have not been switched over yet."]}, {"cell_type": "code", "execution_count": 2, "metadata": {"scrolled": false}, "outputs": [], "source": ["from kustoutils import KustoCaller, EventServiceEntriesAnalyzer\n", "import pandas as pd\n", "import warnings\n", "import json\n", "warnings.filterwarnings('ignore')\n", "\n", "pd.set_option('display.max_colwidth', -1)\n", "\n", "kc = KustoCaller()\n", "query = \"\"\"\n", "HttpIncomingRequests\n", "| where TIMESTAMP >= ago(7d)\n", "| where httpStatusCode > 0\n", "| where subscriptionId != \"0140911e-1040-48da-8bc9-b99fb3dd88a6\"\n", "| where targetUri contains \"Microsoft.Databricks\"\n", "| where httpMethod == \"PUT\"\n", "| project TIMESTAMP, subscriptionId, httpMethod, httpStatusCode, targetUri\n", "| order by subscriptionId, TIMESTAMP asc\n", "\"\"\"\n", "df_puts = kc.query(query, kc.RP_DB, kc.RP_CLUSTER)\n", "\n", "query = \"\"\"\n", "EventServiceEntries \n", "| where TIMESTAMP >= ago(7d)\n", "| where subscriptionId != \"0140911e-1040-48da-8bc9-b99fb3dd88a6\"\n", "| where operationName contains \"Microsoft.Databricks/workspaces/write\"\n", "| where status == \"Failed\" or status == \"Succeeded\"\n", "| project TIMESTAMP, subscriptionId, resourceUri, properties, status\n", "\"\"\"\n", "\n", "df_creations = kc.query(query, kc.ARM_DB, kc.ARM_CLUSTER)\n", "\n", "def get_region(resourceUri: str):\n", "    matchingputs= df_puts[df_puts.targetUri.str.contains(resourceUri)]\n", "    if(len(matchingputs) == 0):\n", "        return \"unknown\"\n", "    targetUri = matchingputs.iloc[0].targetUri\n", "    return targetUri[8:targetUri.index(\".adb-spearfish\")]\n", "    \n", "df_creations[\"region\"] = df_creations.resourceUri.apply(get_region)\n", "\n", "def transform(value):\n", "    if(value == \"\"):\n", "        return {}\n", "    jsonval = json.loads(value)\n", "    return json.loads(jsonval[\"statusMessage\"]) if \"statusMessage\" in jsonval else {}\n", "\n", "df_creations.properties = df_creations.properties.map(transform)\n", "df_creations = EventServiceEntriesAnalyzer.get_error_groups(df_creations)\n", "\n", "df_creations = df_creations[df_creations.region != \"unknown\"]"]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [], "source": ["fake_error_groups = ['InvalidApplicationManagedResourceGroupId:NA',\n", "       'ResourceDeploymentFailure:MissingSubscriptionRegistration',\n", "       'ResourceDeploymentFailure:ARMDocumentDBIssue',\n", "       'ResourceDeploymentFailure:PolicyError',\n", "       'ApplianceBeingDeleted:NA',\n", "       'ResourceDeploymentFailure:InvalidApplianceState',\n", "       'ApplianceManagedResourceGroupMismatch:NA',\n", "       'ResourceDeploymentFailure:MaxStorageAccountsCountPerSubscriptionExceeded',\n", "       'InvalidDatabricksSkuUpdate:NA',\n", "       'MissingSubscriptionRegistration:MissingSubscriptionRegistration',\n", "       'ResourceDeploymentFailure:SubscriptionNotRegistered',\n", "       'InvalidDatabricksSku:NA',\n", "        'ApplianceManagedResourceGroupMismatch:NA',\n", "       'ResourceDeploymentFailure:InternalServerError']\n", "\n", "fake_errors = df_creations[df_creations.error_group.isin(fake_error_groups)]\n", "real_errors = df_creations[~df_creations.error_group.isin(fake_error_groups)]"]}, {"cell_type": "code", "execution_count": 24, "metadata": {"scrolled": false}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["EASTUS2EUAP\n", "{}\n", "\n", "\n", "WESTUS\n", "{'Succeeded': 253, 'Failed': 6}\n", "\n", "ResourceDeploymentFailure:AuthorizationFailed : 6\n", "/subscriptions/12100e91-7179-48ca-b02a-19f502f60d35/resourcegroups/DataScienceLab/providers/Microsoft.Databricks/workspaces/DataScienceLab-bricks\n", "/subscriptions/12100e91-7179-48ca-b02a-19f502f60d35/resourcegroups/DataScienceLab/providers/Microsoft.Databricks/workspaces/DataScienceLab-ws\n", "/subscriptions/596df088-441c-4a6f-881e-5511128a3f1c/resourcegroups/qa_rg_westus_standard_5bf6db39/providers/Microsoft.Databricks/workspaces/qa_workspace_westus_standard_5bf6db39\n", "/subscriptions/5bbcae9d-d03c-4184-af23-4313e099dd6d/resourcegroups/testminevn/providers/Microsoft.Databricks/workspaces/testmine\n", "/subscriptions/0827c45d-52f6-48fe-8ed1-a4a113ac31e1/resourcegroups/demo/providers/Microsoft.Databricks/workspaces/test3\n", "/subscriptions/eb481d23-7e79-476f-a31d-aba81951ec98/resourcegroups/RG-MyInsights-Service-Prod/providers/Microsoft.Databricks/workspaces/mi-dbricks-prod-poc\n", "\n", "\n", "EASTUS2\n", "{'Succeeded': 353, 'Failed': 22}\n", "\n", "ResourceDeploymentFailure:AuthorizationFailed : 20\n", "/subscriptions/89bc2539-531f-401d-9e0d-b1eaf905a6c6/resourcegroups/Jeff-<PERSON>/providers/Microsoft.Databricks/workspaces/gaia-jb\n", "/subscriptions/bffe4cc6-9376-49c6-83e1-705eb8b823c3/resourcegroups/FII-PRD-BLOOMBERGANALYSIS/providers/Microsoft.Databricks/workspaces/PRDDTBK01-WS\n", "/subscriptions/79103142-f838-4690-ab90-7f1c64fafe75/resourcegroups/ODL-DiscoveryDay-49590/providers/Microsoft.Databricks/workspaces/azurediscdayws2\n", "/subscriptions/596df088-441c-4a6f-881e-5511128a3f1c/resourcegroups/qa_rg_eastus2_premium_174a1b51/providers/Microsoft.Databricks/workspaces/qa_workspace_eastus2_premium_174a1b51\n", "/subscriptions/82bcff9b-c4ff-42c6-aa18-34da5cc72fbb/resourcegroups/marketing-etl-eus2-rg/providers/Microsoft.Databricks/workspaces/popcore-etl\n", "/subscriptions/89bc2539-531f-401d-9e0d-b1eaf905a6c6/resourcegroups/db-lab-vjc/providers/Microsoft.Databricks/workspaces/db-lab-vjc-databricks\n", "/subscriptions/06dd00f7-b728-411f-aca7-5f8a32872d56/resourcegroups/AzureMLWorkshop/providers/Microsoft.Databricks/workspaces/JoeMLWS\n", "/subscriptions/1f913984-b1c8-4187-b297-415b71d6d278/resourcegroups/corp-datatogo-application-dev/providers/Microsoft.Databricks/workspaces/github-databrick\n", "/subscriptions/1f913984-b1c8-4187-b297-415b71d6d278/resourcegroups/gsk-Corp-Platforms-Dataecosys-application-resources/providers/Microsoft.Databricks/workspaces/josh-github-databrick\n", "/subscriptions/a35ebf47-daf2-4bf2-ad69-9e3b908eac04/resourcegroups/AZ-RG-STCDatWarehouse40-Prod-01/providers/Microsoft.Databricks/workspaces/adbenttestadw01\n", "/subscriptions/cfdea0dc-e97b-463e-8dc3-36aca6c6270d/resourcegroups/demo2/providers/Microsoft.Databricks/workspaces/demo2\n", "/subscriptions/872fa0dd-4729-4b7f-8c74-bcce12be61f1/resourcegroups/CurriculumExtractor/providers/Microsoft.Databricks/workspaces/WorkspaceDataBricks\n", "/subscriptions/52674a79-ec0d-4895-94d2-ff05ce6ed6ea/resourcegroups/Enterprise/providers/Microsoft.Databricks/workspaces/EnterpriseSpace\n", "/subscriptions/8FFD00A2-3731-44A0-B51E-4B9260C92A39/resourcegroups/ODL-bigdata-vis-49495/providers/Microsoft.Databricks/workspaces/49495\n", "/subscriptions/8dee33b7-0acc-4214-9268-be41406d49cd/resourcegroups/ODL_Databricksdev-9781/providers/Microsoft.Databricks/workspaces/9781\n", "/subscriptions/8dee33b7-0acc-4214-9268-be41406d49cd/resourcegroups/ODL_Databricksdev-9782/providers/Microsoft.Databricks/workspaces/9782\n", "/subscriptions/41ca5ed7-3e4f-4a30-b857-9cdad18ad91e/resourcegroups/ODL-mlTest-49485/providers/Microsoft.Databricks/workspaces/49485\n", "/subscriptions/aa8eeb81-5148-4123-a642-4f68f6019093/resourcegroups/aeu2-poc-clientdelivery-databricks-rsg/providers/Microsoft.Databricks/workspaces/ClientDelivery_WS\n", "/subscriptions/71276155-2563-4744-b3b8-512e5bd29c14/resourcegroups/TEST3/providers/Microsoft.Databricks/workspaces/TEST3\n", "/subscriptions/11502dbb-1f54-441e-9eb5-66602bd6c3b7/resourcegroups/databricks/providers/Microsoft.Databricks/workspaces/TEST2\n", "\n", "ApplicationNotFound:NA : 1\n", "/subscriptions/387cc842-835f-4ccd-a789-54531d36904e/resourcegroups/RG-AmerisourceBergen/providers/Microsoft.Databricks/workspaces/DB-AmerisourceBergen\n", "\n", "ResourceDeploymentFailure:ApplianceProvisioningFailed : 1\n", "/subscriptions/EB2AC4C2-141F-43CB-96D9-FF4B3BC9760A/resourcegroups/ODL-bigdata-vis-49499/providers/Microsoft.Databricks/workspaces/49499\n", "\n", "\n", "WESTEUROPE\n", "{'Succeeded': 374, 'Failed': 13}\n", "\n", "ResourceDeploymentFailure:AuthorizationFailed : 9\n", "/subscriptions/b4a5eaff-15ac-4575-9891-0e4e8417450e/resourcegroups/vap-dev-ba-cs-bu-b2c-nl-we-rg/providers/Microsoft.Databricks/workspaces/db-fede\n", "/subscriptions/1cce7f69-c5f7-4391-8354-a3180e0862f0/resourcegroups/skills-dev-we/providers/Microsoft.Databricks/workspaces/analytics\n", "/subscriptions/625adb3e-2fec-4162-8790-36dbb10cad43/resourcegroups/DatabricksCD/providers/Microsoft.Databricks/workspaces/databricks20190129\n", "/subscriptions/59a878ab-342f-4641-bfa6-419c20edde4c/resourcegroups/fe-POV-databricks/providers/Microsoft.Databricks/workspaces/fe-databricks-new\n", "/subscriptions/041715c3-b9ce-43d1-8818-8c844119c248/resourcegroups/rg0129adb/providers/Microsoft.Databricks/workspaces/adb0129andre\n", "/subscriptions/137767c8-35be-4fb0-8707-9061853fe445/resourcegroups/ucx-datascience-rg/providers/Microsoft.Databricks/workspaces/ucx-datascience-databricks\n", "/subscriptions/ae187d5f-639d-4c0e-a02a-cd277fa0998a/resourcegroups/funwithbricks/providers/Microsoft.Databricks/workspaces/mydatabrickslife\n", "/subscriptions/fc143c42-6a8c-45fb-b9ad-f13da63dae69/resourcegroups/rgdatabricksspark/providers/Microsoft.Databricks/workspaces/databricks\n", "/subscriptions/ae187d5f-639d-4c0e-a02a-cd277fa0998a/resourcegroups/unilever/providers/Microsoft.Databricks/workspaces/databrickstesting\n", "\n", "ResourceDeploymentFailure:ApplianceProvisioningFailed : 2\n", "/subscriptions/562f0104-22cc-4ddd-a384-b5dd900cdc17/resourcegroups/emsa-hpims-test-long-term-storage/providers/Microsoft.Databricks/workspaces/emsa-hpims-test-we-databricks\n", "/subscriptions/dc9839c9-7145-41fb-a3fb-d0569bd89fad/resourcegroups/NHCM-POC-krisn-RG/providers/Microsoft.Databricks/workspaces/krisnDataBricks2\n", "\n", "InternalServerError:NA : 1\n", "/subscriptions/b238961f-5881-4618-a860-ebff05f53733/resourcegroups/zure-dataguild-int-test/providers/Microsoft.Databricks/workspaces/o32zizcoyfcva\n", "\n", "ResourceGroupNotFound:NA : 1\n", "/subscriptions/b146ae31-d42f-4c88-889b-318f2cc23f98/resourceGroups/dataThirstDatabricksDBFSExplorer-RG/providers/Microsoft.Databricks/workspaces/dataThirstDatabricksDBFSExplorer\n", "\n", "\n", "NORTHEUROPE\n", "{'Succeeded': 199, 'Failed': 5}\n", "\n", "ResourceDeploymentFailure:AuthorizationFailed : 4\n", "/subscriptions/9656b513-da1f-4f44-8b7c-98d832183256/resourcegroups/Cofina3/providers/Microsoft.Databricks/workspaces/Cofina3\n", "/subscriptions/2df15e9c-49e4-44bd-b170-3e143db5bb83/resourcegroups/giubranabc/providers/Microsoft.Databricks/workspaces/giubrandef\n", "/subscriptions/9698c522-26cc-43f2-8197-dfa9225e856f/resourcegroups/grt_drp_test/providers/Microsoft.Databricks/workspaces/drpGrtDataBricks\n", "/subscriptions/ae187d5f-639d-4c0e-a02a-cd277fa0998a/resourcegroups/unileverprocess/providers/Microsoft.Databricks/workspaces/msftlearnbricks\n", "\n", "ResourceDeploymentFailure:ApplianceProvisioningFailed : 1\n", "/subscriptions/204671af-5130-4ef5-819c-e314b65f9d06/resourcegroups/bieno-da-p-60072-engine-rg/providers/Microsoft.Databricks/workspaces/ohub2-databricks-prod\n", "\n", "\n", "EASTUS\n", "{'Succeeded': 484, 'Failed': 17}\n", "\n", "ResourceDeploymentFailure:AuthorizationFailed : 17\n", "/subscriptions/54006eea-3986-4fa8-90ff-7606059d2f61/resourcegroups/RG-BoC-MohsinYasin1/providers/Microsoft.Databricks/workspaces/mohsinDB\n", "/subscriptions/1ff4d762-64da-4eae-9cec-77477370fa40/resourcegroups/ssm_test_rg/providers/Microsoft.Databricks/workspaces/ssmtestdatabricks\n", "/subscriptions/db0d54ba-b405-4b1f-a4df-012b1f34a905/resourcegroups/AI_ML_rg/providers/Microsoft.Databricks/workspaces/Databricksworkshop\n", "/subscriptions/26317ae8-9d60-48dd-81c3-cc484ed4c90e/resourcegroups/UBAP-DevTest-RG/providers/Microsoft.Databricks/workspaces/UBAP-DataBricks-ETL\n", "/subscriptions/4722946a-31b0-4bf0-b300-791332aa4605/resourcegroups/MSWorkshop/providers/Microsoft.Databricks/workspaces/msds\n", "/subscriptions/8dee33b7-0acc-4214-9268-be41406d49cd/resourcegroups/ODL_Databricksdev-9778/providers/Microsoft.Databricks/workspaces/9778\n", "/subscriptions/8dee33b7-0acc-4214-9268-be41406d49cd/resourcegroups/ODL_Databricksdev-9779/providers/Microsoft.Databricks/workspaces/9779\n", "/subscriptions/8dee33b7-0acc-4214-9268-be41406d49cd/resourcegroups/ODL_Databricksdev-9777/providers/Microsoft.Databricks/workspaces/9777\n", "/subscriptions/53e77d8e-c18b-4040-846b-282ed557ee9a/resourcegroups/iONSit/providers/Microsoft.Databricks/workspaces/ds-jobs\n", "/subscriptions/41ca5ed7-3e4f-4a30-b857-9cdad18ad91e/resourcegroups/ODL-mlTest-49491/providers/Microsoft.Databricks/workspaces/49491\n", "/subscriptions/41ca5ed7-3e4f-4a30-b857-9cdad18ad91e/resourcegroups/ODL-mlTest-49487/providers/Microsoft.Databricks/workspaces/49487\n", "/subscriptions/53e77d8e-c18b-4040-846b-282ed557ee9a/resourcegroups/iONSit/providers/Microsoft.Databricks/workspaces/ds-data-acqre-n-proces-jobs\n", "/subscriptions/41ca5ed7-3e4f-4a30-b857-9cdad18ad91e/resourcegroups/ODL-mlTest-49478/providers/Microsoft.Databricks/workspaces/49478\n", "/subscriptions/53e77d8e-c18b-4040-846b-282ed557ee9a/resourcegroups/iONSit/providers/Microsoft.Databricks/workspaces/ds-data-acquire-n-process-jobs\n", "/subscriptions/5928cf55-bdcb-41eb-8211-ffa65eed3728/resourcegroups/demoData/providers/Microsoft.Databricks/workspaces/demodatabricks\n", "/subscriptions/41ca5ed7-3e4f-4a30-b857-9cdad18ad91e/resourcegroups/ODL-mlTest-49473/providers/Microsoft.Databricks/workspaces/49473\n", "/subscriptions/8950e1b9-4c32-438e-a5f8-1ebc318dabb4/resourcegroups/SanjayPOC/providers/Microsoft.Databricks/workspaces/MyTestCluster\n", "\n", "\n", "SEASTASIA\n", "{'Succeeded': 224, 'Failed': 14}\n", "\n", "ResourceDeploymentFailure:AuthorizationFailed : 13\n", "/subscriptions/e3675a5d-9205-4ab3-9bb5-773259bbbde8/resourcegroups/ODL-ml-49906/providers/Microsoft.Databricks/workspaces/49906\n", "/subscriptions/21ebe4ed-eb21-4791-bde5-33fca7f1142a/resourcegroups/ODL-ml-49901/providers/Microsoft.Databricks/workspaces/49901\n", "/subscriptions/e8bfb459-cc60-4e18-bfe3-cca6a9c05b06/resourcegroups/ODL-ml-49898/providers/Microsoft.Databricks/workspaces/49898\n", "/subscriptions/df085af0-1d07-4890-83c6-c1e6519aa212/resourcegroups/ODL-ml-49889/providers/Microsoft.Databricks/workspaces/49889\n", "/subscriptions/9a0055fb-0c2f-47ee-8141-bc00115a3299/resourcegroups/ODL-ml-49886/providers/Microsoft.Databricks/workspaces/49886\n", "/subscriptions/4e09b5ee-747c-4bc6-b0d6-37550536c1a6/resourcegroups/ODL-databricks-dw-49879/providers/Microsoft.Databricks/workspaces/49879\n", "/subscriptions/03df3539-4556-46c1-87d1-b5383cd0bb27/resourcegroups/ODL-databricks-dw-49877/providers/Microsoft.Databricks/workspaces/49877\n", "/subscriptions/ea8323fb-dd33-48d1-8ffb-3d771bc5f05c/resourcegroups/ODL-databricks-dw-49867/providers/Microsoft.Databricks/workspaces/49867\n", "/subscriptions/07a3b836-0813-4c05-afd4-3a7ab00358d9/resourcegroups/ODL-databricks-dw-49865/providers/Microsoft.Databricks/workspaces/49865\n", "/subscriptions/2903defe-821c-415b-98f0-18cd2465d7a4/resourcegroups/ODL-databricks-dw-49856/providers/Microsoft.Databricks/workspaces/49856\n", "/subscriptions/edb336ca-b85f-4204-8057-7fdb7d65322c/resourcegroups/ODL-databricks-dw-49852/providers/Microsoft.Databricks/workspaces/49852\n", "/subscriptions/f33373bf-4282-4926-a9a4-fcc8c3c6ff62/resourcegroups/ODL-databricks-dw-49817/providers/Microsoft.Databricks/workspaces/49817\n", "/subscriptions/41ca5ed7-3e4f-4a30-b857-9cdad18ad91e/resourcegroups/ODL-mlTest-49549/providers/Microsoft.Databricks/workspaces/49549\n", "\n", "ResourceNotFound:NA : 1\n", "/subscriptions/b9cec7a1-c948-4cd3-a08e-aac87ab0de4a/resourcegroups/PUC-RG/providers/Microsoft.Databricks/workspaces/Demo\n", "\n", "\n", "EASTASIA\n", "{'Succeeded': 6}\n", "\n", "\n", "SCENTRALUS\n", "{'Succeeded': 96}\n", "\n", "\n", "NCENTRALUS\n", "{'Succeeded': 13}\n", "\n", "\n", "WESTUS2\n", "{'Succeeded': 158, 'Failed': 4}\n", "\n", "ResourceDeploymentFailure:AuthorizationFailed : 3\n", "/subscriptions/1722f1c2-5b86-41e2-b9d5-5626314fb7db/resourcegroups/s1services/providers/Microsoft.Databricks/workspaces/s1tlogtest\n", "/subscriptions/dc7df375-fcdf-4881-a73c-f5ae0411021d/resourcegroups/yashdatabric/providers/Microsoft.Databricks/workspaces/yashdatabrickkk\n", "/subscriptions/ae71ef11-a03f-4b4f-a0e6-ef144727c711/resourcegroups/AML_IU_Demo/providers/Microsoft.Databricks/workspaces/Databricks_IU_Demo_\n", "\n", "ResourceDeploymentFailure:ApplianceProvisioningFailed : 1\n", "/subscriptions/7559c239-0954-495e-82ee-518829957e2d/resourcegroups/PowerBITests/providers/Microsoft.Databricks/workspaces/PowerBIRestAPI\n", "\n", "\n", "CENTRALUS\n", "{'Succeeded': 110}\n", "\n", "\n", "UKWEST\n", "{'Succeeded': 16, 'Failed': 1}\n", "\n", "ResourceDeploymentFailure:AuthorizationFailed : 1\n", "/subscriptions/1dc70a97-2716-4eee-9a3a-197120597727/resourcegroups/azure-ai-hack-uk/providers/Microsoft.Databricks/workspaces/kallidus-course-recommendation-101\n", "\n", "\n", "UKSOUTH\n", "{'Succeeded': 34}\n", "\n", "\n", "AUSEAST\n", "{'Succeeded': 78, 'Failed': 1}\n", "\n", "ResourceDeploymentFailure:ApplianceProvisioningFailed : 1\n", "/subscriptions/8fc73de0-0a3f-42b5-924e-cf51a9bba92e/resourcegroups/databricks_ws/providers/Microsoft.Databricks/workspaces/wip_db_cluster\n", "\n", "\n", "AUSSEAST\n", "{'Succeeded': 193, 'Failed': 2}\n", "\n", "ResourceDeploymentFailure:ApplianceProvisioningFailed : 2\n", "/subscriptions/7a2f51a1-ab51-42a4-b672-8fa0c2e9ff9b/resourcegroups/ODL-databricks-dw-49699/providers/Microsoft.Databricks/workspaces/49699\n", "/subscriptions/41ca5ed7-3e4f-4a30-b857-9cdad18ad91e/resourcegroups/ODL-mlTest-49472/providers/Microsoft.Databricks/workspaces/49472\n", "\n", "\n", "AUSCENTRAL\n", "{'Succeeded': 1}\n", "\n", "\n", "AUSCENTRAL2\n", "{}\n", "\n", "\n", "JAPANEAST\n", "{'Succeeded': 21}\n", "\n", "\n", "JAPANWEST\n", "{'Succeeded': 8, 'Failed': 1}\n", "\n", "ResourceDeploymentFailure:ApplianceProvisioningFailed : 1\n", "/subscriptions/0ab9ad2e-f316-4da5-b243-e7f65da24755/resourcegroups/table3akdatabricksrg/providers/Microsoft.Databricks/workspaces/table3akdatabricks\n", "\n", "\n", "CANCENTRAL\n", "{'Succeeded': 37, 'Failed': 3}\n", "\n", "ResourceDeploymentFailure:AuthorizationFailed : 3\n", "/subscriptions/54006eea-3986-4fa8-90ff-7606059d2f61/resourcegroups/RG-BoC-GabrielleForget1/providers/Microsoft.Databricks/workspaces/coffee\n", "/subscriptions/6c5f4698-361b-4d1d-a796-c906900f5065/resourcegroups/psp_demo_rc2/providers/Microsoft.Databricks/workspaces/psp_demo_rc2\n", "/subscriptions/5208dadf-4d5b-4d21-891c-0f1fffeb2521/resourcegroups/dafortin-rg/providers/Microsoft.Databricks/workspaces/dafortin-w\n", "\n", "\n", "CANEAST\n", "{'Succeeded': 10}\n", "\n", "\n", "INDCENTRAL\n", "{'Succeeded': 16}\n", "\n", "\n", "INDSOUTH\n", "{'Succeeded': 19}\n", "\n", "\n", "INDWEST\n", "{'Succeeded': 3}\n", "\n", "\n"]}], "source": ["regions = [\"eastus2euap\", \"westus\", \"eastus2\", \"westeurope\", \"northeurope\", \"eastus\", \"seastasia\", \n", "           \"eastasia\", \"scentralus\", \"ncentralus\", \"westus2\", \"centralus\", \"ukwest\", \"uksouth\", \n", "           \"auseast\", \"ausseast\", \"auscentral\", \"auscentral2\", \"japaneast\", \"japanwest\", \"cancentral\", \"caneast\",\n", "           \"indcentral\", \"indsouth\", \"indwest\"]\n", "\n", "unique_creations = real_errors.drop_duplicates(\"resourceUri\")\n", "\n", "for current_region in regions:\n", "    print(current_region.upper())\n", "    df_region = unique_creations[unique_creations.region == current_region]\n", "    print(dict(df_region.status.value_counts()))\n", "    error_counts = dict(df_region[df_region.status == \"Failed\"].error_group.value_counts())\n", "    print()\n", "    for key in error_counts:\n", "        print(key + \" : \" + str(error_counts[key]))\n", "        for uri in list(df_region[(df_region.status == \"Failed\") & (df_region.error_group == key)].resourceUri):\n", "            print(uri)\n", "        print()\n", "    print()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.6.4"}}, "nbformat": 4, "nbformat_minor": 2}