# First two things to do in the RP

## VNet Injection (IBP v2 support)

Virtual network injection is a popular feature currently in public preview. It allows customers to specify their own existing virtual network to deploy Databricks clusters into, instead of the managed workers-vnet we create. For more information on this feature, checkout [these docs](https://docs.azuredatabricks.net/administration-guide/cloud-configurations/azure/vnet-inject.html).<br>

For these feature, we use a Networking feature called "Intent Based Policies" (IBP) or "Network Intent Policies". This allows us to require a set of rules on the customer's virtual network. More specifically, it enforces a set of rules on the Network Security Group that the subnets are assocaited with. These rules allow communication between cluster resources in the customer subnets and the Databricks control plane. Once the IBP/Network Intent Policies policy is applied, the customer cannot change or break the rules we specify. We need these rules in order for the Databricks Control Plane to connect with cluster resources deploy into the customers VNet. <br>

Note: in IBPv1, the customer still has to setup their NSG to have the right inbound/outbound rules. The Network Intent Policy just prevents them from changing them.<br>

Right now, the IBPv1 code runs in the Databricks control plane. When a customer creates a VNet injected workspace, the Databricks control plane does a "PUT Network Intent Policy" request on the vnet/subnets the customer specifies. As mentioned, this prevents the customer from messing with the rules we need. <br>

We are moving to IBPv2, which has a slightly different flow. In short, it will look like the following:

1. Customer "delegates" their subnets to Microsoft.Databricks.
2. Customer creates a vnet injected Databricks workspace.
3. Our RP validates the subnets (their CIDR range, they are associated with an NSG, etc.)
4. Our RP calls the PREPARE API to add the proper rules to the subnets.

In this repo, you can find the docs from the networking team in `./docs/vnetinjection`.

In order to get this flow to work, we need to do the following:

1. Standup an API that provides the rules Microsoft.Databricks needs to work. I have started this work, you can see the PR [here](https://msdata.visualstudio.com/Spearfish/_git/DatabricksRP/pullrequest/187703?_a=files).
2. Add code in Workspace creation flow for vnet injected workspaces
3. Add validation code in the vnet injection flow
4. Add call to the PREPARE API in that flow

## ARM SOS replication groups

In short ARM Storage Operations Service (SOS) is a service that enables replication of data across multiple Azure storage accounts. Data is replicated between storage accounts in an "SOS ring". SOS can also do automatic data backups as well as encryption certificate rollovers. For more information on SOS, check out: [SOS FAQ](https://armwiki.azurewebsites.net/rp_onboarding/SOSFAQ.html) and [SOS Onboarding](https://armwiki.azurewebsites.net/rp_onboarding/SOSRPOnboarding.html). <br>

Currently, our workspace data storage accounts sit in an ARM SOS replication group with some other storage accounts belonging to the ARM team. This was how we did the data migration while moving from the Managed RP to our own independent RP. This is not the source of truth, but you can see the SOS ring config in this repo at `./ApplianceReplicateGroups`. For example, consider the `./ApplianceReplicateGroups/WestUS2.json` file. The storage accounts list in this file are:

1. "/subscriptions/f31cf11d-46a3-4edf-87a3-163d7cf00b42/resourceGroups/prdsapplianceprodmwh01-migrated/providers/Microsoft.Storage/storageAccounts/prdsapplianceprodmwh01"
2. "/subscriptions/f31cf11d-46a3-4edf-87a3-163d7cf00b42/resourceGroups/Default-Storage-WestUS2/providers/Microsoft.Storage/storageAccounts/appliancereplicatemwh01"
3. "/subscriptions/9ae8e263-8df7-4b7e-b27b-d59f0bfb685b/resourceGroups/adbrp_westus2/providers/Microsoft.Storage/storageAccounts/westus2workspacedata"

The last account is the one in our subscription. 

Here's what we need to do:

1. We need to take our storage accounts out of their replication ring and setup our own. [Work item](https://msdata.visualstudio.com/Spearfish/_workitems/edit/369163). I have already started writing the config files in the `./IndependentSOSGroups` folder, and have created the replication and backup storage accounts in our subscriptions. It should be as simple as removing our storage accounts from the SOS rings above and doing a PUT to get the new indepdent rings setup with the config files in `./IndependentSOSGroups`.
2. We also need to figure out how to rollover our Data Encryption cert.

## "The role assignment already exists" bug

There is an old bug that should be fixed, one customer has complained about it. This bug actual existed in the Managed Apps RP codebase and was fixed by Ilshat Ahatov. Here is his PR: [Link](https://msazure.visualstudio.com/One/_git/AzureUX-ARM/pullrequest/1327930?_a=overview). We should use that PR as a reference and port it into our codebase.

<br>

# List of other important or time-critical work items

### Portal work

1. We need to update the portal SDK by end of February.

### Dev fundamentals

1. Setup gated checkins that run local tests. [Work item](https://msdata.visualstudio.com/Spearfish/_workitems/edit/369156)
2. Utilize CloudBuild for official signed builds. [Work item](https://msdata.visualstudio.com/Spearfish/_workitems/edit/333355)
3. Use ExpressV2 for Deployment. [Work item](https://msdata.visualstudio.com/Spearfish/_workitems/edit/369157)
4. Migrate integration tests runners to use Geneva runners. [Work item](https://msdata.visualstudio.com/Spearfish/_workitems/edit/338479)
5. Setup alterting on workspace creation and deletion errors rates. [Work item](https://msdata.visualstudio.com/Spearfish/_workitems/edit/369161)

### Features and bug fixes

2. "The role assignment already exists" bug. [Work item](https://msdata.visualstudio.com/Spearfish/_workitems/edit/332845)
3. Better portal validation for CIDR range. [Work item](https://msdata.visualstudio.com/Spearfish/_workitems/edit/369145)

### Maintenance tasks

1. Certificate rotation. SSL and AAD cert expire 10/14/2019 and 10/15/2019.
    * Currently we use OneCert our AAD cert and ssladmin for our SSL cert. The certificates are stored in the Key Vault in adbrp_global in our RP subscription.
    * After refreshing these certs and uploading them to all the Cloud Services, you can update the cert thumbprints in the Certificates section of the Cloud Service config files.
3. Data encryption certificate rollover. We can use ARM SOS to do this, but I'm not sure how. The current certificate we are using expires 11/29/2019.
4. Update ARM certificate value when ARM updates their cert.

