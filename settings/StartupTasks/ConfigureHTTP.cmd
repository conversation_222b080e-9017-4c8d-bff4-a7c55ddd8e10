@ECHO OFF
setlocal
setlocal EnableDelayedExpansion
REM This file updates the registry value used by HTTP service.

ECHO Arguments: %* > ConfigureHTTP.log
ECHO. >> ConfigureHTTP.log

ECHO Environment variables: >> ConfigureHTTP.log
SET >> ConfigureHTTP.log
ECHO. >> ConfigureHTTP.log

ECHO Begin configuring HTTP service >> ConfigureHTTP.log
ECHO. >> ConfigureHTTP.log

REM Show current HTTP configuration
REG QUERY HKEY_LOCAL_MACHINE\System\CurrentControlSet\Services\HTTP\Parameters /s >> ConfigureHTTP.log 2>> ConfigureHTTP.err

ECHO Increasing the max header value size >> ConfigureHTTP.log
REG ADD HKEY_LOCAL_MACHINE\System\CurrentControlSet\Services\HTTP\Parameters /f /v MaxFieldLength /t REG_DWORD /d 64000 >> ConfigureHTTP.log 2>> ConfigureHTTP.err

ECHO Increasing the the upper limit for the total size of the Request line and the headers >> ConfigureHTTP.log
REG ADD HKEY_LOCAL_MACHINE\System\CurrentControlSet\Services\HTTP\Parameters /f /v MaxRequestBytes /t REG_DWORD /d 131072 >> ConfigureHTTP.log 2>> ConfigureHTTP.err

REM Cache disabling is needed as a workaround for HTTP.sys security vulnerability (MSRC case 21164). This should be removed after the case is resolved.
ECHO Disabling uri cache as a workaround for HTTP.sys security vulnerability >> ConfigureHTTP.log
REG ADD HKEY_LOCAL_MACHINE\System\CurrentControlSet\Services\HTTP\Parameters /f /v UriEnableCache /t REG_DWORD /d 0 >> ConfigureHTTP.log 2>> ConfigureHTTP.err

ECHO Set maximum number of characters in a URL path segment to 16K. >> ConfigureHTTP.log
reg add HKEY_LOCAL_MACHINE\System\CurrentControlSet\Services\HTTP\Parameters /f /v UrlSegmentMaxLength /t REG_DWORD /d 16384 >> ConfigureHTTP.log 2>> ConfigureHTTP.err

REM Security Advisory 2868725: recommendation to disable RC4.
ECHO Disabling RC4 security vulnerability >> ConfigureHTTP.log
REG ADD "HKEY_LOCAL_MACHINE\SYSTEM\CurrentControlSet\Control\SecurityProviders\SCHANNEL\Ciphers\RC4 128/128" /f /v "Enabled" /t REG_DWORD /d 0 >> ConfigureHTTP.log 2>> ConfigureHTTP.err
REG ADD "HKEY_LOCAL_MACHINE\SYSTEM\CurrentControlSet\Control\SecurityProviders\SCHANNEL\Ciphers\RC4 64/128" /f /v "Enabled" /t REG_DWORD /d 0 >> ConfigureHTTP.log 2>> ConfigureHTTP.err
REG ADD "HKEY_LOCAL_MACHINE\SYSTEM\CurrentControlSet\Control\SecurityProviders\SCHANNEL\Ciphers\RC4 56/128" /f /v "Enabled" /t REG_DWORD /d 0 >> ConfigureHTTP.log 2>> ConfigureHTTP.err
REG ADD "HKEY_LOCAL_MACHINE\SYSTEM\CurrentControlSet\Control\SecurityProviders\SCHANNEL\Ciphers\RC4 40/128" /f /v "Enabled" /t REG_DWORD /d 0 >> ConfigureHTTP.log 2>> ConfigureHTTP.err

call :DisableHTTP2WithRetry >> ConfigureHTTP.log  2>> ConfigureHTTP.err

ECHO. >> ConfigureHTTP.log
ECHO End configuring HTTP service >> ConfigureHTTP.log

goto :EOF

:DisableHTTP2WithRetry
ECHO Disable HTTP2 --- %DISABLE_HTTP2%
REM A reboot will be done if there is any change in the registry.
set REBOOT=N
call :DisableHTTP2
if "%REBOOT%"=="Y" (
    echo Rebooting to apply changes...
    shutdown.exe /r /c "Rebooting to apply changes from DisableHTTP2" /f /d p:2:4
) else (
    echo No reboot required.
)

goto :EOF

:DisableHTTP2
SET HTTP2REGKEY=HKEY_LOCAL_MACHINE\SYSTEM\CurrentControlSet\Services\HTTP\Parameters
if "%DISABLE_HTTP2%"=="true" (
    reg query %HTTP2REGKEY% /v EnableHttp2Tls 2> nul
    if !ERRORLEVEL! == 1 (
        ECHO Disabling HTTP2, was enabled.
        reg add %HTTP2REGKEY% /v EnableHttp2Tls /t REG_DWORD /d 0x00000 /f
        SET REBOOT=Y
    ) else (
        ECHO HTTP2 is already disabled
    )
) else (
    reg query %HTTP2REGKEY% /v EnableHttp2Tls 2> nul
    if !ERRORLEVEL! == 0 (
        ECHO Enabling HTTP2, was disabled.
        reg delete %HTTP2REGKEY% /v EnableHttp2Tls /f
        SET REBOOT=Y
    ) else (
        ECHO HTTP2 is already enabled
    )
)

goto :EOF
