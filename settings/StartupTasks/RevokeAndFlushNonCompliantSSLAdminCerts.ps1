<#
  .Synopsis
    Adds the non-compliant SSLAdmin V1 certificates to the LocalMachine Disallowed Store.
    A rollback operation is also supported to remove these certs from the Disallowed store.

  .Description
    As part of the Magisterium effort, all non compliant SSL-Admin V1 certifcates will be distrusted around Feb-2021.
    This script is to: 
       - Help teams exercise the revocation behavior prior to global PKI revocation in Feb-2021.
       - Teams can also use this script to rollback the operation of adding the SSLAdmin V1 certs to the DisAllowed store.
    
    The default behavior of the script is to also flush the following caches:
       - Flush CRLs and OCSP caches on the machine (memory and disk). These caches are used by Crypt32.dll on the machine.
       - Flush LSASS.exe cache on the machine.
     
    NOTE: Flusing these cases in most cases is sufficient for client to stop using SSLAdminV1 certs without a machine reboot.

  .EXAMPLE
    PS> .\RevokeAndFlushNonCompliantSSLAdminCerts.ps1  
    
    Adds the non-compliant certs to untrusted (disallowed) store and performs a cache flush.

   .EXAMPLE
     PS> .\RevokeAndFlushNonCompliantSSLAdminCerts.ps1 -DoNotFlushCaches
    
     Adds the non-compliant certs to untrusted (disallowed) store but does not flush the caches.    
   
   .EXAMPLE
      PS> .\RevokeAndFlushNonCompliantSSLAdminCerts.ps1 -Rollback
    
      Removes the non-compliant certs from the unstrusted (disallowed) store and then flushes the caches.
   
   .EXAMPLE
      PS> .\RevokeAndFlushNonCompliantSSLAdminCerts.ps1 -Rollback -DoNotFlushCaches

      Removes the non-compliant certs from the unstrusted (disallowed) store but does not flushe the caches.
#>

Param (
[Parameter(Mandatory=$false)]
[switch]$Rollback,

[Parameter(Mandatory=$false)]
[switch]$DoNotFlushCaches
)

# Base-64 encoded CER files of SSLAdmin IT TLS CA1
$SSLAdminCA1_Base64 =
"
MIIFtDCCBJygAwIBAgIQCLh6UBu+nNotFk0+OVG/VTANBgkqhkiG9w0BAQsFADBa
MQswCQYDVQQGEwJJRTESMBAGA1UEChMJQmFsdGltb3JlMRMwEQYDVQQLEwpDeWJl
clRydXN0MSIwIAYDVQQDExlCYWx0aW1vcmUgQ3liZXJUcnVzdCBSb290MB4XDTE2
MDUyMDEyNTEyOFoXDTI0MDUyMDEyNTEyOFowgYsxCzAJBgNVBAYTAlVTMRMwEQYD
VQQIEwpXYXNoaW5ndG9uMRAwDgYDVQQHEwdSZWRtb25kMR4wHAYDVQQKExVNaWNy
b3NvZnQgQ29ycG9yYXRpb24xFTATBgNVBAsTDE1pY3Jvc29mdCBJVDEeMBwGA1UE
AxMVTWljcm9zb2Z0IElUIFRMUyBDQSAxMIICIjANBgkqhkiG9w0BAQEFAAOCAg8A
MIICCgKCAgEAjvPxhHV3vL7JpPUWpVMrUGCZ3Nh92SS14XJJN0j+2oaTo30dmksQ
TXd5fmWpfG424kfUNknQzCQCJxTirnHN2Vd0PBBWGZJKh2L545CNXt7RQRsaph9A
oiwejlXXJthoQqvsDd7dXmGVs6xsgc6o4K2vX8qm5FFoLif9VCpxpMy7fpLx9lNR
BTHQGYKwymPQ8koAC830aUv0WpZWOSbJnUsKYzQygKUE5eoot8EAwG0a8CjUSo+A
rHMZ2PUWL62uCJdiBiz+56XwrUFTf40rMcMUcyHd43hjnFGGtaJIScB5CBVDACuZ
uEvgx1cHbMS5plQtAVPyo/KkzNnBVPOIzeRME9OKMyFYrRi/vjmBcDlpN/hbpGPv
CQffhxpix5oIxdEdXmJ2Ad1p5ji7RLAtTTrGLoDgYHJb8szmjlw6IR5dsDkrveoT
y5bLtmp0jI68DhCfG6VAQY+RXHanDvGqOoe3DHffcWovKGFCLZAPcgWrZ+DBe8uc
QJrECghEjHw9uqkOHrHZIr0fX0Fqc1T2ZuKg+aY53tJ382kEv7e7PMST/3IEHLU2
nWh/3/o5T7L2j7kc/63tDhUI44Z88khJd5cW9v0A9k+mXm/nOcBRZT3rsZcw7Oqe
c/weLKDfi89zX7UOBkIXJpXs2Kkn0NBllFziP8ooKaUg9MjdXbT/5t0CAwEAAaOC
AUIwggE+MB0GA1UdDgQWBBRYiJ/W3JxIIrcUPv+EiOjmhf/6fTAfBgNVHSMEGDAW
gBTlnVkwgkdYzKz6CFQ2hns6tQRN8DASBgNVHRMBAf8ECDAGAQH/AgEAMA4GA1Ud
DwEB/wQEAwIBhjAnBgNVHSUEIDAeBggrBgEFBQcDAQYIKwYBBQUHAwIGCCsGAQUF
BwMJMDQGCCsGAQUFBwEBBCgwJjAkBggrBgEFBQcwAYYYaHR0cDovL29jc3AuZGln
aWNlcnQuY29tMDoGA1UdHwQzMDEwL6AtoCuGKWh0dHA6Ly9jcmwzLmRpZ2ljZXJ0
LmNvbS9PbW5pcm9vdDIwMjUuY3JsMD0GA1UdIAQ2MDQwMgYEVR0gADAqMCgGCCsG
AQUFBwIBFhxodHRwczovL3d3dy5kaWdpY2VydC5jb20vQ1BTMA0GCSqGSIb3DQEB
CwUAA4IBAQAwmsadav3vkwgMvoJ3+XagbZ57MCN7qCla9Go+xwsMlt+4S1LkDZw4
7XhjtXPAHB874Kf/f0lRlTK40Jup5c+WA4GA1UphGP7Easbff0FGIpyAZusPQqDk
86Qho5jQenT2jOjD0iuqK84RWRlE51wHCULr1/0VTblvbEQ1Joe6oztosIHnIMl/
EwLzzKufHJVQy65kgLuHCl3OpmuyfeM9NuIpUbcl/NAJ47CtxGIuPn6FJrL2r/dt
MXPGGZipcpMCzsoLPTzs2XDogPUWq3hqh03GgTeoCnaBBqjvF2B8cBATPDjXM0zk
N2UI+5Gz6BZ2YSpl9ViUs0UB78BPA3u4
"

# Base-64 encoded CER files of SSLAdmin IT TLS CA2
$SSLAdminCA2_Base64 =
"
MIIFtDCCBJygAwIBAgIQDywQyVsGwJN/uNRJ+D6FaTANBgkqhkiG9w0BAQsFADBa
MQswCQYDVQQGEwJJRTESMBAGA1UEChMJQmFsdGltb3JlMRMwEQYDVQQLEwpDeWJl
clRydXN0MSIwIAYDVQQDExlCYWx0aW1vcmUgQ3liZXJUcnVzdCBSb290MB4XDTE2
MDUyMDEyNTE1N1oXDTI0MDUyMDEyNTE1N1owgYsxCzAJBgNVBAYTAlVTMRMwEQYD
VQQIEwpXYXNoaW5ndG9uMRAwDgYDVQQHEwdSZWRtb25kMR4wHAYDVQQKExVNaWNy
b3NvZnQgQ29ycG9yYXRpb24xFTATBgNVBAsTDE1pY3Jvc29mdCBJVDEeMBwGA1UE
AxMVTWljcm9zb2Z0IElUIFRMUyBDQSAyMIICIjANBgkqhkiG9w0BAQEFAAOCAg8A
MIICCgKCAgEAnqoVwRuhY1/mURjFFrsR3AtNm5EKukBJK9zWBgvFd1ksNEJFC06o
yRbwKPMflpW/HtOfzIeBliGk57MwZq18bgASr70sPUWuoD917HUgBfxBYoF8zA7Z
Ie5zAHODFboJL7Fg/apgbQs/GiZZNCi0QkQUWzw0nTUmVSNQ0mz6pCu95Dv1WMsL
GyPGfdN9zD3Q/QEDyJ695QgjRIxYA1DUE+54ti2k6r0ycKFQYkyWwZ25HD1h2kYt
3ovW85vF6y7tjTqUEcLbgKUCB81/955hdLLsbFd6f9o2PkU8xuOc3U+bUedvv6Sb
tvGjBEZeFyH8/CaQhzlsKMH0+OPOFv/bMqcLarPw1V1sOV1bl4W9vi2278niblzI
bEHt7nN888p4KNIwqCcXaGhbtS4tjn3NKI6v1d2XRyxIvCJDjgoZ09zF39Pyoe92
sSRikZh7xns4tQEQ8BCs4o5NBSx8UxEsgyzNSskWGEWqsIjt+7+A1skDDZv6k2o8
VCHNbTLFKS7d72wMI4ErpzVsBIicxaG2ezuMBBuqThxIiJ+G9zfoP9lxim/9rvJA
xbh3nujA1VJfkOYTJIojEAYCxR3QjEoGdapJmBle97AfqEBnwoJsu2wav8h9v+po
DL4h6dRzRUxY1DHypcFlXGoHu/REQgFLq2IN30/AhQLN90Pj9TT2RQECAwEAAaOC
AUIwggE+MB0GA1UdDgQWBBSRnjtEbD1XnEJ3KjTXT9HMSpcs2jAfBgNVHSMEGDAW
gBTlnVkwgkdYzKz6CFQ2hns6tQRN8DASBgNVHRMBAf8ECDAGAQH/AgEAMA4GA1Ud
DwEB/wQEAwIBhjAnBgNVHSUEIDAeBggrBgEFBQcDAQYIKwYBBQUHAwIGCCsGAQUF
BwMJMDQGCCsGAQUFBwEBBCgwJjAkBggrBgEFBQcwAYYYaHR0cDovL29jc3AuZGln
aWNlcnQuY29tMDoGA1UdHwQzMDEwL6AtoCuGKWh0dHA6Ly9jcmwzLmRpZ2ljZXJ0
LmNvbS9PbW5pcm9vdDIwMjUuY3JsMD0GA1UdIAQ2MDQwMgYEVR0gADAqMCgGCCsG
AQUFBwIBFhxodHRwczovL3d3dy5kaWdpY2VydC5jb20vQ1BTMA0GCSqGSIb3DQEB
CwUAA4IBAQBsf+pqb89rW8E0rP/cDuB9ixMX4C9OWQ7EA7n0BSllR64ZmuhU9mTV
2L0G4HEiGXvOmt15i99wJ0ho2/dvMxm1ZeufkAfMuEc5fQ9RE5ENgNR2UCuFB2Bt
bVmaKUAWxscN4GpXS4AJv+/HS0VXs5Su19J0DA8Bg+lo8ekCl4dq2G1m1WsCvFBI
oLIjd4neCLlGoxT2jA43lj2JpQ/SMkLkLy9DXj/JHdsqJDR5ogcij4VIX8V+bVD0
NCw7kQa6Ulq9Zo0jDEq1at4zSeH4mV2PMM3LwIXBA2xo5sda1cnUWJo3Pq4uMgcL
e0t+fCut38NMkTl8F0arflspaqUVVUov
"

# Base-64 encoded CER files of SSLAdmin IT TLS CA4
$SSLAdminCA4_Base64 =
"
MIIFtDCCBJygAwIBAgIQC2qzsD6xqfbEYJJqqM3+szANBgkqhkiG9w0BAQsFADBa
MQswCQYDVQQGEwJJRTESMBAGA1UEChMJQmFsdGltb3JlMRMwEQYDVQQLEwpDeWJl
clRydXN0MSIwIAYDVQQDExlCYWx0aW1vcmUgQ3liZXJUcnVzdCBSb290MB4XDTE2
MDUyMDEyNTIzOFoXDTI0MDUyMDEyNTIzOFowgYsxCzAJBgNVBAYTAlVTMRMwEQYD
VQQIEwpXYXNoaW5ndG9uMRAwDgYDVQQHEwdSZWRtb25kMR4wHAYDVQQKExVNaWNy
b3NvZnQgQ29ycG9yYXRpb24xFTATBgNVBAsTDE1pY3Jvc29mdCBJVDEeMBwGA1UE
AxMVTWljcm9zb2Z0IElUIFRMUyBDQSA0MIICIjANBgkqhkiG9w0BAQEFAAOCAg8A
MIICCgKCAgEAq+XrXaNrOZ71NIgSux1SJl19CQvGeY6rtw7fGbLd7g/27vRW5Ebi
kg/iZwvjHHGk1EFztMuZFo6/d32wrx5s7XEuwwh3Sl6Sruxa0EiB0MXpoPV6jx6N
XtOtksDaxpE1MSC5OQTNECo8lx0AnpkYGAnPS5fkyfwA8AxanTboskDBSqyEKKo9
Rhgrp4qs9K9LqH5JQsdiIMDmpztd65Afu4rYnJDjOrFswpTOPjJry3GzQS65xeFd
2FkngvvhSA1+6ATx+QEnQfqUWn3FMLu2utcRm4j6AcxuS5K5+Hg8y5xomhZmiNCT
sCqDLpcRHX6BIGHksLmbnG5TlZUixtm9dRC62XWMPD8d0Jb4M0V7ex9UM+VIl6cF
JKLb0dyVriAqfZaJSHuSetAksd5IEfdnPLTf+Fhg9U97NGjm/awmCLbzLEPbT8QW
0JsMcYexB2uG3Y+gsftm2tjL6fLwZeWO2BzqL7otZPFe0BtQsgyFSs87yC4qanWM
wK5c2enAfH182pzjvUqwYAeCK31dyBCvLmKM3Jr94dm5WUiXQhrDUIELH4Mia+Sb
vCkigv2AUVx1Xw41wt1/L3pnnz2OW4y7r530zAz7qB+dIcHz51IaXc4UV21QuEnu
sQsn0uJpJxJuxsAmPuekKxuLUzgG+hqHOuBLf5kWTlk9WWnxcadlZRsCAwEAAaOC
AUIwggE+MB0GA1UdDgQWBBR6e4zBz+egyhzUa/r74TPDDxqinTAfBgNVHSMEGDAW
gBTlnVkwgkdYzKz6CFQ2hns6tQRN8DASBgNVHRMBAf8ECDAGAQH/AgEAMA4GA1Ud
DwEB/wQEAwIBhjAnBgNVHSUEIDAeBggrBgEFBQcDAQYIKwYBBQUHAwIGCCsGAQUF
BwMJMDQGCCsGAQUFBwEBBCgwJjAkBggrBgEFBQcwAYYYaHR0cDovL29jc3AuZGln
aWNlcnQuY29tMDoGA1UdHwQzMDEwL6AtoCuGKWh0dHA6Ly9jcmwzLmRpZ2ljZXJ0
LmNvbS9PbW5pcm9vdDIwMjUuY3JsMD0GA1UdIAQ2MDQwMgYEVR0gADAqMCgGCCsG
AQUFBwIBFhxodHRwczovL3d3dy5kaWdpY2VydC5jb20vQ1BTMA0GCSqGSIb3DQEB
CwUAA4IBAQAR/nIGOiEKN27I9SkiAmKeRQ7t+gaf77+eJDUX/jmIsrsB4Xjf0YuX
/bd38YpyT0k66LMp13SH5LnzF2CHiJJVgr3ZfRNIfwaQOolm552W95XNYA/X4cr2
du76mzVIoZh90pMqT4EWx6iWu9El86ZvUNoAmyqo9DUA4/0sO+3lFZt/Fg/Hjsk2
IJTwHQG5ElBQmYHgKEIsjnj/7cae1eTK6aCqs0hPpF/kixj/EwItkBE2GGYoOiKa
3pXxWe6fbSoXdZNQwwUS1d5ktLa829d2Wf6l1uVW4f5GXDuK+OwO++8SkJHOIBKB
ujxS43/jQPQMQSBmhxjaMmng9tyPKPK9
"

# Base-64 encoded CER files of SSLAdmin IT TLS CA5
$SSLAdminCA5_Base64 =
"
MIIFtDCCBJygAwIBAgIQCIjNUl8ZJERNFKWCkd65UjANBgkqhkiG9w0BAQsFADBa
MQswCQYDVQQGEwJJRTESMBAGA1UEChMJQmFsdGltb3JlMRMwEQYDVQQLEwpDeWJl
clRydXN0MSIwIAYDVQQDExlCYWx0aW1vcmUgQ3liZXJUcnVzdCBSb290MB4XDTE2
MDUyMDEyNTMwM1oXDTI0MDUyMDEyNTMwM1owgYsxCzAJBgNVBAYTAlVTMRMwEQYD
VQQIEwpXYXNoaW5ndG9uMRAwDgYDVQQHEwdSZWRtb25kMR4wHAYDVQQKExVNaWNy
b3NvZnQgQ29ycG9yYXRpb24xFTATBgNVBAsTDE1pY3Jvc29mdCBJVDEeMBwGA1UE
AxMVTWljcm9zb2Z0IElUIFRMUyBDQSA1MIICIjANBgkqhkiG9w0BAQEFAAOCAg8A
MIICCgKCAgEAmt+BXDuPrul5lrmaeaeB8jnaVShGIwLFgmYHthXe/Zw6GpuRCdJm
jwuJF/hxhyF/ONM/miUAtzXQq/gIejbzUFvfKykn3qTdJJL69MZwjTYqmvCA3jX6
HkKdCYGq1QcKFqXFWkJtQr4eQoK0VzCZW0Ur1I/TCgbGc5Ok4YPvxb8FJ6d4zbML
4J4iFvOY3KYU6MyU1yP50FCZu7ULEJXx3wLpj46dVpk82I/TWPtckn49e/hQSVr3
EHt3+OZKkEpVUt6UrXQJoGRXLM0HkJ8WrZXD0Qa68e9sBbUErKncGzGbDi0ZlQRP
3mbLrTVyrxmCCLIUOhZfsDyb240MsALWJh/oFXHE7/ljOUOM6cKSLqHCoDAlDpYn
X56jK4LWEL08GR6mh/5VITpcQfwBmMwvkv9mOLS4ZpwPEmhLSqyGu16Y/56mnFNs
MxGk0K5SR9eLj/GWrLkpmo8s8a1kGMMmuwBk3lBwwLvsxmuu06DvwPFcDfLMelna
GDMvWRCtZxQsXyJDSkTh6N3g51UWTgnvA0wMSFBa8APfju9jyltnh0NALAa2Hw8+
U8BmP9cUFeYIYphIfoPlp7VdUS1ULWH9NF3Ut4DN0n3OsSQ785dsbBPeihfJivVI
lUL3EpDjEBf2oQDFNiplkZ4F7EIuWriZG//UTrX6ZlXZg46/CCmN+gsCAwEAAaOC
AUIwggE+MB0GA1UdDgQWBBQI/iWfdOqHBMK8u46oOF8zxtFsZTAfBgNVHSMEGDAW
gBTlnVkwgkdYzKz6CFQ2hns6tQRN8DASBgNVHRMBAf8ECDAGAQH/AgEAMA4GA1Ud
DwEB/wQEAwIBhjAnBgNVHSUEIDAeBggrBgEFBQcDAQYIKwYBBQUHAwIGCCsGAQUF
BwMJMDQGCCsGAQUFBwEBBCgwJjAkBggrBgEFBQcwAYYYaHR0cDovL29jc3AuZGln
aWNlcnQuY29tMDoGA1UdHwQzMDEwL6AtoCuGKWh0dHA6Ly9jcmwzLmRpZ2ljZXJ0
LmNvbS9PbW5pcm9vdDIwMjUuY3JsMD0GA1UdIAQ2MDQwMgYEVR0gADAqMCgGCCsG
AQUFBwIBFhxodHRwczovL3d3dy5kaWdpY2VydC5jb20vQ1BTMA0GCSqGSIb3DQEB
CwUAA4IBAQA+8s8wpXq/HmrfOVgYKDzDne7ngcVL/Gf2vx9ON9re8K/uivkDe2Bn
dMc72v8rSuv9VHUTi+XCgRK6UhIguimKOs1DJMzVFwX+nBY/c+BtQcB2PfKrSMVZ
YmS6RE8KGII/Qeo/GDpY56AwV3X10WoxFLaUmWXatugB3uSr+7Xz5RkKGF+kAlfe
tlwmb3P+Lgn1CEPED8ckf50oZ2Wh3FvwOv34cIXnpU8k3kI/HUQ7XYUGhR0eHNTZ
TlHk/R4RFsyeANmXGpfjZceGNRtTdr4y0SxBSUujPpMMW3dXBzA8NYuM0WmiJ/pV
6KudEB7RF9+6bInTyVvXC5SIqdi0ldeO
"


# Checks if the user is in the administrator group. Warns and stops if the user is not.
if (!([Security.Principal.WindowsPrincipal] [Security.Principal.WindowsIdentity]::GetCurrent()).IsInRole([Security.Principal.WindowsBuiltInRole] "Administrator"))
{
    Write-Warning "You are not running this as local administrator. Run it again in an elevated prompt."; return
}

function AddSslAdminV1IntermediatesToDisallowedStore()
{
    $store = new-object System.Security.Cryptography.X509Certificates.X509Store(
                [System.Security.Cryptography.X509Certificates.StoreName]::Disallowed,
                [System.Security.Cryptography.X509Certificates.StoreLocation]::LocalMachine)

    $store.Open("MaxAllowed")
    $store.Add($SSLAdminCA1_X509)
    $store.Add($SSLAdminCA2_X509)
    $store.Add($SSLAdminCA4_X509)
    $store.Add($SSLAdminCA5_X509)
    $store.close()
    Write-Host("Successfully ADDED SSLAdmin V1 certificates TO LocalMachine Disallowed store") -ForegroundColor Green
}

function RemoveSslAdminV1IntermediatesFromDisallowedStore()
{
    $store = new-object System.Security.Cryptography.X509Certificates.X509Store(
                [System.Security.Cryptography.X509Certificates.StoreName]::Disallowed,
                [System.Security.Cryptography.X509Certificates.StoreLocation]::LocalMachine)

    $store.Open("MaxAllowed")
    $store.Remove($SSLAdminCA1_X509)
    $store.Remove($SSLAdminCA2_X509)
    $store.Remove($SSLAdminCA4_X509)
    $store.Remove($SSLAdminCA5_X509)
    $store.close()

    Write-Host("Successfully REMOVED SSLAdmin V1 certificates FROM the LocalMachine Disallowed store") -ForegroundColor Green
}

function FlushCaches()
{
    if ($null -eq (Get-Command "certutil.exe" -ErrorAction SilentlyContinue))
    {
        Write-Warning ("Powershell was unable to find certutil.exe on this machine. Please copy a signed version of CertUtil.exe and binplace it with this script.")
    }
    else
    {
        # Flush Crypt32.dll cache
        cmd.exe /c "certutil.exe -setreg chain\ChainCacheResyncFiletime @now"

        # Do not exit, just log and continue
        if ($LASTEXITCODE -ne 0)
        {
           Write-Warning("Failed flushing Crypt32 cache, errorcode $LASTEXITCODE")
        }
        else
        {
           Write-Host("Successfully flushed Crypt32 caches") -ForegroundColor Green
        }

        # Flush LSASS cache
        cmd.exe /c "certutil -flushCache 0 0xFF"

        # Do not exit, just log and continue
        if ($LASTEXITCODE -ne 0)
        {
           Write-Warning("Failed flushing LSASS cache, errorcode $LASTEXITCODE")
        }
        else
        {
           Write-Host("Successfully flushed LSASS caches") -ForegroundColor Green
        }
    }
}

# Import Base-64 representations of SSLAdmin V1 certs into cert objects
$SSLAdminCA1_X509 = [System.Security.Cryptography.X509Certificates.X509Certificate2]::new(([System.Convert]::FromBase64String($SSLAdminCA1_Base64)))
$SSLAdminCA2_X509 = [System.Security.Cryptography.X509Certificates.X509Certificate2]::new(([System.Convert]::FromBase64String($SSLAdminCA2_Base64)))
$SSLAdminCA4_X509 = [System.Security.Cryptography.X509Certificates.X509Certificate2]::new(([System.Convert]::FromBase64String($SSLAdminCA4_Base64)))
$SSLAdminCA5_X509 = [System.Security.Cryptography.X509Certificates.X509Certificate2]::new(([System.Convert]::FromBase64String($SSLAdminCA5_Base64)))

if (!$Rollback.IsPresent)
{
    AddSslAdminV1IntermediatesToDisallowedStore
}
else
{
    RemoveSslAdminV1IntermediatesFromDisallowedStore
}

if (!$DoNotFlushCaches.IsPresent)
{
    FlushCaches
}