<#
    .SYNOPSIS
        Installs event source to windows event store.

    .DESCRIPTION
        Installs event source to windows event store.

    .EXAMPLE
        .\Install-EventSource.ps1
#>
[CmdletBinding(DefaultParameterSetName="Standard")]
param($manifestFile, $manifestAssembly)

if (($manifestFile -eq $null) -or ($manifestAssembly -eq $null))
{
   write-host "manifestFile or manifestAssembly is empty."
   exit 1
}

Write-Host "Reading manifest file $manifestFile"
[xml]$man = Get-Content $manifestFile

$manifestAssemblyFullPath = (Get-Item -Path ".\$manifestAssembly").FullName

Write-Host "Updating manifest file path to $manifestAssemblyFullPath"
$man.instrumentationManifest.instrumentation.events.provider.resourceFileName = $manifestAssemblyFullPath
$man.instrumentationManifest.instrumentation.events.provider.messageFileName = $manifestAssemblyFullPath

Write-Host "Saving manifest file."
$man.Save($manifestFile)

Write-Host "Uninstalling any existing manifest"
& wevtutil uninstall-manifest $manifestFile

Write-Host "Installing manifest"
& wevtutil install-manifest $manifestFile

Write-Host "Install manifest completed."