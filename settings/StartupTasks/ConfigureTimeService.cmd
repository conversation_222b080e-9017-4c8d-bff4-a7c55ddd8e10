@ECHO OFF
ECHO Arguments: %* > ConfigureTimeService.log
ECHO. >> ConfigureTimeService.log

ECHO Environment variables: >> ConfigureTimeService.log
SET >> ConfigureTimeService.log
ECHO. >> ConfigureTimeService.log

ECHO Begin configuring Windows time service >> ConfigureTimeService.log
ECHO. >> ConfigureTimeService.log

IF "%EMULATED%"=="true" (
    ECHO Running emulated, not need to configure Windows time service >> ConfigureTimeService.log
) ELSE (
    ECHO Stopping Windows time service: net stop w32time >> ConfigureTimeService.log
    net stop w32time >> ConfigureTimeService.log 2>> ConfigureTimeService.err
    ECHO. >> ConfigureTimeService.log

    ECHO Unregistering Windows time service: w32tm /unregister >> ConfigureTimeService.log
    w32tm /unregister >> ConfigureTimeService.log 2>> ConfigureTimeService.err
    ECHO. >> ConfigureTimeService.log    

    ECHO Registering Windows time service: w32tm /register >> ConfigureTimeService.log
    w32tm /register >> ConfigureTimeService.log 2>> ConfigureTimeService.err
    ECHO. >> ConfigureTimeService.log    

    ECHO Updating Windows time service pool interval: REG ADD HKLM\SYSTEM\CurrentControlSet\Services\w32time\TimeProviders\NtpClient /v SpecialPollInterval /t REG_DWORD /d 0xe10 /f >> ConfigureTimeService.log
    REG ADD HKLM\SYSTEM\CurrentControlSet\Services\w32time\TimeProviders\NtpClient /v SpecialPollInterval /t REG_DWORD /d 0xe10 /f >> ConfigureTimeService.log 2>> ConfigureTimeService.err
    ECHO. >> ConfigureTimeService.log    

    ECHO Updating Windows time service configuration: sc config w32time start= auto >> ConfigureTimeService.log
    sc config w32time start= auto >> ConfigureTimeService.log 2>> ConfigureTimeService.err
    ECHO. >> ConfigureTimeService.log    

    ECHO Updating Windows time service configuration: sc failure w32time actions= restart/60000/restart/60000/restart/60000 reset= 86400 >> ConfigureTimeService.log
    sc failure w32time actions= restart/60000/restart/60000/restart/60000 reset= 86400 >> ConfigureTimeService.log 2>> ConfigureTimeService.err
    ECHO. >> ConfigureTimeService.log    

    ECHO Starting Windows time service: net start w32time >> ConfigureTimeService.log
    net start w32time >> ConfigureTimeService.log 2>> ConfigureTimeService.err
)

ECHO. >> ConfigureTimeService.log
ECHO Finished configuring Windows time service >> ConfigureTimeService.log
