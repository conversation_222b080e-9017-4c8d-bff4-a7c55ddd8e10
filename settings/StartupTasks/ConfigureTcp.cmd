@ECHO OFF
REM This file updates the registry value related to TCP.

ECHO Arguments: %* > ConfigureTCP.log
ECHO. >> ConfigureTCP.log

ECHO Environment variables: >> ConfigureTCP.log
SET >> ConfigureTCP.log
ECHO. >> ConfigureTCP.log

ECHO Begin configuring TCP >> ConfigureTCP.log
ECHO. >> ConfigureTCP.log

REM Show current TCP configuration
REG QUERY HKEY_LOCAL_MACHINE\System\CurrentControlSet\Services\Tcpip\Parameters /s >> ConfigureTCP.log 2>> ConfigureTCP.err

ECHO Updating the max user port configuration to 65534 (0xfffe = 65534) >> ConfigureTCP.log
REG ADD HKEY_LOCAL_MACHINE\System\CurrentControlSet\Services\Tcpip\Parameters /f /v MaxUserPort /t REG_DWORD /d 0xfffe >> ConfigureTCP.log 2>> ConfigureTCP.err

ECHO Updating the TCP timed wait delay to 60 seconds (0x3c = 60) >> ConfigureTCP.log
REG ADD HKEY_LOCAL_MACHINE\System\CurrentControlSet\Services\Tcpip\Parameters /f /v TcpTimedWaitDelay /t REG_DWORD /d 0x3c >> ConfigureTCP.log 2>> ConfigureTCP.err

REM according to KB https://support.microsoft.com/en-us/kb/929851
ECHO Updating the UDP dynamic port range to [1025, 65534]
netsh int ipv4 set dynamicport udp start=1025 num=64510

ECHO Updating the TCP dynamic port range to [1025, 65534]
netsh int ipv4 set dynamicport tcp start=1025 num=64510

ECHO. >> ConfigureTCP.log
ECHO End configuring TCP service >> ConfigureTCP.log
