@ECHO OFF
ECHO Arguments: %* > ConfigureWaWorkerHost.log
ECHO. >> ConfigureWaWorkerHost.log

ECHO Environment variables: >> ConfigureWaWorkerHost.log
SET >> ConfigureWaWorkerHost.log
ECHO. >> ConfigureWaWorkerHost.log

ECHO Begin configuring WaWorkerHost >> ConfigureWaWorkerHost.log
ECHO. >> ConfigureWaWorkerHost.log

ECHO Updating WaWorkerHost.exe.config to enable Server GC >> ConfigureWaWorkerHost.log
COPY WaWorkerHost.exe.config %RoleRoot%\base\x64\. /Y >> ConfigureWaWorkerHost.log

ECHO. >> ConfigureWaWorkerHost.log
ECHO End configuring WaWorkerHost >> ConfigureWaWorkerHost.log
