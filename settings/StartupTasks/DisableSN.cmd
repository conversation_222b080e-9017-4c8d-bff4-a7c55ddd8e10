@ECHO OFF
ECHO Arguments: %* > DisableSN.log
ECHO. >> DisableSN.log

ECHO Environment variables: >> DisableSN.log
SET >> DisableSN.log
ECHO. >> DisableSN.log

ECHO Begin strong name verification configuration >> DisableSN.log
ECHO. >> DisableSN.log

ECHO Strong name verification is disabled >> DisableSN.log
REG ADD HKEY_LOCAL_MACHINE\SOFTWARE\Microsoft\StrongName\Verification\*,31bf3856ad364e35 /f >> DisableSN.log 2>> DisableSN.err
REG ADD HKEY_LOCAL_MACHINE\SOFTWARE\Wow6432Node\Microsoft\StrongName\Verification\*,31bf3856ad364e35 /f >> DisableSN.log 2>> DisableSN.err

ECHO. >> DisableSN.log
ECHO End strong name verification configuration >> DisableSN.log
