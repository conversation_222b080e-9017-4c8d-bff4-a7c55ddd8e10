@ECHO OFF
ECHO Arguments: %* > InstallMEP.log
ECHO. >> InstallMEP.log

ECHO Environment variables: >> InstallMEP.log
SET >> InstallMEP.log
ECHO. >> InstallMEP.log

ECHO Begin installing Microsoft endpoint protection >> InstallMEP.log
ECHO. >> InstallMEP.log

CALL :doit

ECHO. >> InstallMEP.log
ECHO End installing Microsoft endpoint protection >> InstallMEP.log

GOTO :EOF

:doit

IF "%EMULATED%"=="true" (
   ECHO Running in emulator, no need to install Microsoft endpoint protection >> InstallMEP.log
) ELSE (
   ECHO Installing Microsoft endpoint protection >> InstallMEP.log
   CD MEP
   START /b InstallMEP.cmd
)
