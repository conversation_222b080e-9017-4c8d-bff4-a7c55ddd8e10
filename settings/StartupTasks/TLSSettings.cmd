FOR /f %%a IN ('WMIC OS GET LocalDateTime ^| FIND "."') DO SET DTS=%%a
SET RunDateTime=%DTS:~0,4%-%DTS:~4,2%-%DTS:~6,2%_%DTS:~8,2%-%DTS:~10,2%-%DTS:~12,2%
SET LogFileDir=%DiagnosticStore%LogFiles\StartupTask\
md %LogFileDir%
SET LogFilePath=%LogFileDir%StartupLog_TLS_%RunDateTime%.txt
echo StartingRun: %RunDateTime%  >> "%LogFilePath%" 2>>&1

echo "Running TLSSettings.ps1 to restrict TLS1.0 and TLS1.1." >> "%LogFilePath%" 2>>&1
PowerShell -NoProfile -ExecutionPolicy Unrestricted %~dp0TLSSettings.ps1 -SetCipherOrder B >> "%LogFilePath%" 2>>&1

REM   If an error occurred, return the errorlevel.
EXIT /B %errorlevel%