@ECHO OFF
ECHO Arguments: %* > StartVulnScanBootstrapper.log
ECHO. >> StartVulnScanBootstrapper.log

ECHO Environment variables: >> StartVulnScanBootstrapper.log
SET >> StartVulnScanBootstrapper.log
ECHO. >> StartVulnScanBootstrapper.log

ECHO Begin starting vulnerability scan bootstrapper >> StartVulnScanBootstrapper.log
ECHO. >> StartVulnScanBootstrapper.log

IF "%EMULATED%"=="true" (
   ECHO Running in emulator, no need to start vulnerability scan bootstrapper >> StartVulnScanBootstrapper.log
) ELSE (
   VulnScan\ApSecClientBootstrapper.exe >> StartVulnScanBootstrapper.log 2>> StartVulnScanBootstrapper.err
)

ECHO. >> StartVulnScanBootstrapper.log
ECHO End starting vulnerability scan bootstrapper >> StartVulnScanBootstrapper.log
