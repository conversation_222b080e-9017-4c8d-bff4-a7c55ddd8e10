@ECHO OFF
ECHO Arguments: %* > InstallEventSource.log
ECHO. >> InstallEventSource.log

ECHO Environment variables: >> InstallEventSource.log
SET >> InstallEventSource.log
ECHO. >> InstallEventSource.log

ECHO Begin installing event source manifest >> InstallEventSource.log
ECHO. >> InstallEventSource.log

ECHO powershell -ExecutionPolicy Unrestricted -File ./Install-EventSource.ps1 %1 %2
powershell -ExecutionPolicy Unrestricted -File ./Install-EventSource.ps1 %1 %2 >> InstallEventSource.log 2>> InstallEventSource.err

ECHO. >> InstallEventSource.log
ECHO End installing event source manifest >> InstallEventSource.log