@ECHO OFF
REM Inspired by https://msdn.microsoft.com/en-us/library/azure/jj129544.aspx
REM This file reboots the system after all configuration changes. 
REM This file makes sure no multiple restarts are done.

ECHO Arguments: %* >> RebootOnce.log
ECHO. >> RebootOnce.log

ECHO Environment variables: >> RebootOnce.log
SET >> RebootOnce.log
ECHO. >> RebootOnce.log

ECHO Begin reboot once >> RebootOnce.log
ECHO. >> RebootOnce.log

IF "%EMULATED%"=="true" (
    ECHO Running emulated, no need to restart role >> RebootOnce.log
) ELSE (
    ECHO Check if RoleRebootedOnce.txt exists. If file aready exists, instance has been rebooted once >> RebootOnce.log
    IF NOT EXIST "%RoleRoot%\RoleRebootedOnce.txt" (
        ECHO RoleRebootedOnce.txt does not exist. Restarting the role instance in 5 seconds... >> RebootOnce.log
        ECHO This file was created to mark the system restart after configuration. This prevents further restarts on role recycle >> "%RoleRoot%\RoleRebootedOnce.txt"
        SHUTDOWN /r /t 5 /c "Rebooting system first time after completing configuration." /f /d p:2:4 >> RebootOnce.log 2>> RebootOnce.err
    ) ELSE (
        ECHO File RoleRebootedOnce.txt exists. The system does not need restart >> RebootOnce.log
    )
)

ECHO. >> RebootOnce.log
ECHO End reboot once >> RebootOnce.log