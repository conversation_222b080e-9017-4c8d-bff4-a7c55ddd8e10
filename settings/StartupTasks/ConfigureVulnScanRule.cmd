@ECHO OFF
ECHO Arguments: %* > ConfigureVulnScanRule.log
ECHO. >> ConfigureVulnScanRule.log

ECHO Environment variables: >> ConfigureVulnScanRule.log
SET >> ConfigureVulnScanRule.log
ECHO. >> ConfigureVulnScanRule.log

ECHO Begin vulnerability scan rule configuration >> ConfigureVulnScanRule.log
ECHO. >> ConfigureVulnScanRule.log

IF "%EMULATED%"=="true" (
   ECHO Running in emulator, no need to configure vulnerability scan rule >> ConfigureVulnScanRule.log
) ELSE (
   IF "%CONFIGUREVULNSCANRULE%"=="true" (
      ECHO Configuring vulnerability scan rule >> ConfigureVulnScanRule.log
      netsh advfirewall firewall add rule name="VulnScan" action=allow protocol=TCP dir=in remoteip=%VULNSCANRULEIPS% localport=%VULNSCANRULEPORTS% >> ConfigureVulnScanRule.log 2>> ConfigureVulnScanRule.err
   ) ELSE (
   ECHO no need to configure vulnerability scan rule for the environment >> ConfigureVulnScanRule.log
   )
)

ECHO. >> ConfigureVulnScanRule.log
ECHO End vulnerability scan rule configuration >> ConfigureVulnScanRule.log
