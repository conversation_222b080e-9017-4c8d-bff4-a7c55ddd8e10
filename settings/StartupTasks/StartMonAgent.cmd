@ECHO OFF
ECHO Arguments: %* > StartMonAgent.log
ECHO. >> StartMonAgent.log

ECHO Environment variables: >> StartMonAgent.log
SET >> StartMonAgent.log
ECHO. >> StartMonAgent.log

CALL :doit >> StartMonAgent.log 2>> StartMonAgent.err

GOTO :EOF

:doit

IF "%EMULATED%"=="true" (
   ECHO Running emulated, not need to start Monitoring Agent
) ELSE (
   IF DEFINED MDSCONFIG_FILE (
       REM Extract mds config version and table namespace and set to env variable
       REM They are needed by MonAgent when building the queue message for the event-service events
       REM since <PERSON><PERSON><PERSON> changes the target table's name and version according to these values in the config. 

       ECHO Parsing %MDSCONFIG_FILE% for namespace value	
       for /f "usebackq" %%x in (`powershell -ExecutionPolicy Unrestricted -file .\ExtractXmlValue.ps1 -file Monitoring\%MDSCONFIG_FILE% -xpath /MonitoringManagement/@namespace`) do set MDSCONFIG_NAMESPACE=%%x
 
       REM Dump the value of MDSCONFIG_NAMESPACE 
       SET MDSCONFIG_NAMESPACE

       ECHO Parsing %MDSCONFIG_FILE% for eventVersion value	
       for /f "usebackq" %%x in (`powershell -ExecutionPolicy Unrestricted -file .\ExtractXmlValue.ps1 -file Monitoring\%MDSCONFIG_FILE% -xpath /MonitoringManagement/@eventVersion`) do set MDSCONFIG_EVENTVERSION=%%x

       REM Dump the value of MDSCONFIG_EVENTVERSION
       SET MDSCONFIG_EVENTVERSION
   ) ELSE (
       ECHO MDSCONFIG_FILE not defined. 
   )

   REM Change BrazilUS to a GSM supported region 
   IF "%MONITORING_GCS_REGION%"=="Brazil US" (
       SET  MONITORING_GCS_REGION=SouthCentralUS
       ECHO Changed original MONITORING_GCS_REGION from Brazil US to SouthCentralUS
   )
   
   ECHO Starting Monitoring agent 
   START /b %MonAgentClientLocation%\MonAgentClient.exe -useenv
   ECHO Monitoring agent launcher started.
)