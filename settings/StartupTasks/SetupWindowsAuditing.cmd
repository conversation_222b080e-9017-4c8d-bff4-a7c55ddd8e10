@ECHO OFF
ECHO Arguments: %* > SetupWindowsAuditing.log
ECHO. >> SetupWindowsAuditing.log

ECHO Environment variables: >> SetupWindowsAuditing.log
SET >> SetupWindowsAuditing.log
ECHO. >> SetupWindowsAuditing.log

ECHO Begin configuring windows auditing >> SetupWindowsAuditing.log
ECHO. >> SetupWindowsAuditing.log

CALL :doit

ECHO. >> SetupWindowsAuditing.log
ECHO End configuring windows auditing >> SetupWindowsAuditing.log

GOTO :EOF

:doit

IF "%EMULATED%"=="true" (
   ECHO Running in emulator, no need to configure windows auditing >> SetupWindowsAuditing.log
) ELSE (
   ECHO Configuring windows auditing >> SetupWindowsAuditing.log
   CD Monitoring
   START /b AuditInstaller.cmd
)
