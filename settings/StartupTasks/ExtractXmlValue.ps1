#-----------------------------------------------------
# This script extracts the value from an xml file 
# by the xpath.
#-----------------------------------------------------
Param($file, $xpath)

$logFile = "ExtractXmlValue.log"
function Log
{
   Param ([string]$logstring)   
   add-content $logFile -value $logstring   
}

if (($file -eq $null) -or ($xpath -eq $null))
{
   write-host "-file [xml file name]"
   write-host "-xpath [xpath, e.g. /MonitoringManagement/@namespace"
   exit 1
}

Log "Parsing file '$file'..."

if (-not [System.IO.File]::Exists($file))
{
   Log "File '$file' doesn't exist."
   exit 1 
}

[xml]$xml = get-content $file
$xmlNodes = $xml.SelectNodes($xpath);

$count = $xmlNodes.Count 
if ($count -ne 1)
{
   Log "There isn't exactly one value. $count values found."    
   exit 1
}

# Just get the first value and exit.
foreach ($xmlNode in $xmlNodes)
{
    write-host $xmlNode.Value
    exit 0
}
