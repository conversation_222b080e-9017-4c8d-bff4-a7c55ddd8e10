@ECHO OFF
ECHO Arguments: %* > ConfigureDNS.log
ECHO. >> ConfigureDNS.log

ECHO Environment variables: >> ConfigureDNS.log
SET >> ConfigureDNS.log
ECHO. >> ConfigureDNS.log

ECHO Begin configuring DNS cache service >> ConfigureDNS.log
ECHO. >> ConfigureDNS.log

ECHO Disabling negative DNS cache in registry >> ConfigureDNS.log
REG ADD HKEY_LOCAL_MACHINE\SYSTEM\CurrentControlSet\Services\Dnscache\Parameters /f /v MaxNegativeCacheTtl /t REG_DWORD /d 0 >> ConfigureDNS.log 2>> ConfigureDNS.err

ECHO. >> ConfigureDNS.log
ECHO End configuring DNS cache service >> ConfigureDNS.log
