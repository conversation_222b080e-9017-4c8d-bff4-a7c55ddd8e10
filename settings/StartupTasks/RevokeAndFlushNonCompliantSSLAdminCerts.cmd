FOR /f %%a IN ('WMIC OS GET LocalDateTime ^| FIND "."') DO SET DTS=%%a
SET RunDateTime=%DTS:~0,4%-%DTS:~4,2%-%DTS:~6,2%_%DTS:~8,2%-%DTS:~10,2%-%DTS:~12,2%
SET LogFileDir=%DiagnosticStore%LogFiles\StartupTask\
md %LogFileDir%
SET LogFilePath=%LogFileDir%StartupLog_SSL_%RunDateTime%.txt
echo StartingRun: %RunDateTime%  >> "%LogFilePath%" 2>>&1

echo "Running RevokeAndFlushNonCompliantSSLAdminCerts.ps1" >> "%LogFilePath%" 2>>&1
PowerShell -NoProfile -ExecutionPolicy Unrestricted %~dp0RevokeAndFlushNonCompliantSSLAdminCerts.ps1>> "%LogFilePath%" 2>>&1

REM   If an error occurred, return the errorlevel.
EXIT /B %errorlevel%