@ECHO OFF
ECHO Arguments: %* > ConfigureIIS.log
ECHO. >> ConfigureIIS.log

ECHO Environment variables: >> ConfigureIIS.log

REM Getting OffsetMinutes from 10 .. 59 to avoid cases where we get a single digit number with a preceding space.
SET /a OffsetMinutes=%RANDOM%%%49+10

SET >> ConfigureIIS.log
ECHO. >> ConfigureIIS.log

ECHO Begin configuring IIS >> ConfigureIIS.log
ECHO. >> ConfigureIIS.log

SET AppCmd="%windir%\system32\inetsrv\AppCmd.exe"
ECHO AppCmd: %AppCmd% >> ConfigureIIS.log

ECHO Installing Application Initialization module
call Dism.exe /Online /Enable-Feature /FeatureName:IIS-ApplicationInit /NoRestart /Quiet /LogPath:ConfigureIIS-Enable-ApplicationInit.log /LogLevel:4

ECHO "%AppCmd%" unlock config /section:system.webServer/security/access >> ConfigureIIS.log
call "%AppCmd%" unlock config /section:system.webServer/security/access >> ConfigureIIS.log 2>> ConfigureIIS.err

ECHO "%AppCmd%" unlock config /section:httpErrors >> ConfigureIIS.log
call "%AppCmd%" unlock config /section:httpErrors >> ConfigureIIS.log 2>> ConfigureIIS.err

ECHO "%AppCmd%" set config /section:system.applicationHost/applicationPools /applicationPoolDefaults.failure.rapidFailProtection:"False" >> ConfigureIIS.log
call "%AppCmd%" set config /section:system.applicationHost/applicationPools /applicationPoolDefaults.failure.rapidFailProtection:"False" >> ConfigureIIS.log 2>> ConfigureIIS.err

ECHO "%AppCmd%" set config /section:system.applicationHost/applicationPools /applicationPoolDefaults.recycling.periodicRestart.time:08:%OffsetMinutes%:00 >> ConfigureIIS.log
call "%AppCmd%" set config /section:system.applicationHost/applicationPools /applicationPoolDefaults.recycling.periodicRestart.time:08:%OffsetMinutes%:00 >> ConfigureIIS.log 2>> ConfigureIIS.err

ECHO "%AppCmd%" set config -section:system.applicationHost/applicationPools /applicationPoolDefaults.recycling.periodicRestart.privateMemory:"48000000" /commit:apphost >> ConfigureIIS.log
call "%AppCmd%" set config -section:system.applicationHost/applicationPools /applicationPoolDefaults.recycling.periodicRestart.privateMemory:"48000000" /commit:apphost >> ConfigureIIS.log 2>> ConfigureIIS.err

ECHO "%AppCmd%" set config /section:system.applicationHost/applicationPools /applicationPoolDefaults.processModel.idleTimeout:00:00:00 >> ConfigureIIS.log
call "%AppCmd%" set config /section:system.applicationHost/applicationPools /applicationPoolDefaults.processModel.idleTimeout:00:00:00 >> ConfigureIIS.log 2>> ConfigureIIS.err

ECHO "%AppCmd%" set config /section:system.applicationHost/applicationPools /applicationPoolDefaults.startMode:OnDemand >> ConfigureIIS.log
call "%AppCmd%" set config /section:system.applicationHost/applicationPools /applicationPoolDefaults.startMode:OnDemand >> ConfigureIIS.log 2>> ConfigureIIS.err

ECHO "%AppCmd%" set config /section:system.applicationHost/applicationPools /applicationPoolDefaults.queueLength:7000 >> ConfigureIIS.log
call "%AppCmd%" set config /section:system.applicationHost/applicationPools /applicationPoolDefaults.queueLength:7000 >> ConfigureIIS.log 2>> ConfigureIIS.err

ECHO "%AppCmd%" set config /section:system.webServer/serverRuntime /uploadReadAheadSize:"8388608" >> ConfigureIIS.log
call "%AppCmd%" set config /section:system.webServer/serverRuntime /uploadReadAheadSize:"8388608" >> ConfigureIIS.log 2>> ConfigureIIS.err

ECHO "%AppCmd%" set config /section:system.webServer/urlCompression /doDynamicCompression:"True" >> ConfigureIIS.log
call "%AppCmd%" set config /section:system.webServer/urlCompression /doDynamicCompression:"True" >> ConfigureIIS.log 2>> ConfigureIIS.err

ECHO "%AppCmd%" set config /section:system.webServer/httpCompression /-"dynamicTypes.[mimeType='application/json']" >> ConfigureIIS.log
call "%AppCmd%" set config /section:system.webServer/httpCompression /-"dynamicTypes.[mimeType='application/json']" >> ConfigureIIS.log 2>> ConfigureIIS.err

ECHO "%AppCmd%" set config /section:system.webServer/httpCompression /+"dynamicTypes.[mimeType='application/json',enabled='True']" >> ConfigureIIS.log
call "%AppCmd%" set config /section:system.webServer/httpCompression /+"dynamicTypes.[mimeType='application/json',enabled='True']" >> ConfigureIIS.log 2>> ConfigureIIS.err

ECHO "%AppCmd%" set config /section:system.webServer/httpCompression /-"dynamicTypes.[mimeType='application/json; charset=utf-8']" >> ConfigureIIS.log
call "%AppCmd%" set config /section:system.webServer/httpCompression /-"dynamicTypes.[mimeType='application/json; charset=utf-8']" >> ConfigureIIS.log 2>> ConfigureIIS.err

ECHO "%AppCmd%" set config /section:system.webServer/httpCompression /+"dynamicTypes.[mimeType='application/json; charset=utf-8',enabled='True']" >> ConfigureIIS.log
call "%AppCmd%" set config /section:system.webServer/httpCompression /+"dynamicTypes.[mimeType='application/json; charset=utf-8',enabled='True']" >> ConfigureIIS.log 2>> ConfigureIIS.err

ECHO "%AppCmd%" set config /section:system.webServer/httpCompression /-"dynamicTypes.[mimeType='*/*']" >> ConfigureIIS.log
call "%AppCmd%" set config /section:system.webServer/httpCompression /-"dynamicTypes.[mimeType='*/*']" >> ConfigureIIS.log 2>> ConfigureIIS.err

ECHO "%AppCmd%" set config /section:system.webServer/httpCompression /+"dynamicTypes.[mimeType='*/*',enabled='False']" >> ConfigureIIS.log
call "%AppCmd%" set config /section:system.webServer/httpCompression /+"dynamicTypes.[mimeType='*/*',enabled='False']" >> ConfigureIIS.log 2>> ConfigureIIS.err

set extensionsToAllow=resources
for %%a in ("%extensionsToAllow:,=" "%") do (
    ECHO "%AppCmd%" set config /section:system.webServer/security/requestFiltering /-"fileExtensions.[fileExtension='.%%a',allowed='false']" >> ConfigureIIS.log
    call "%AppCmd%" set config /section:system.webServer/security/requestFiltering /-"fileExtensions.[fileExtension='.%%a',allowed='false']" >> ConfigureIIS.log 2>> ConfigureIIS.err

    ECHO "%AppCmd%" set config /section:system.webServer/security/requestFiltering /+"fileExtensions.[fileExtension='.%%a',allowed='true']" >> ConfigureIIS.log
    call "%AppCmd%" set config /section:system.webServer/security/requestFiltering /+"fileExtensions.[fileExtension='.%%a',allowed='true']" >> ConfigureIIS.log 2>> ConfigureIIS.err
)

set extensionsToBlock=bat,cer,cmd,config,cscfg,csdef,dll,exe,InstallLog,InstallState,man
for %%a in ("%extensionsToBlock:,=" "%") do (
    ECHO "%AppCmd%" set config /section:system.webServer/security/requestFiltering /-"fileExtensions.[fileExtension='.%%a',allowed='true']" >> ConfigureIIS.log
    call "%AppCmd%" set config /section:system.webServer/security/requestFiltering /-"fileExtensions.[fileExtension='.%%a',allowed='true']" >> ConfigureIIS.log 2>> ConfigureIIS.err

    ECHO "%AppCmd%" set config /section:system.webServer/security/requestFiltering /+"fileExtensions.[fileExtension='.%%a',allowed='false']" >> ConfigureIIS.log
    call "%AppCmd%" set config /section:system.webServer/security/requestFiltering /+"fileExtensions.[fileExtension='.%%a',allowed='false']" >> ConfigureIIS.log 2>> ConfigureIIS.err
)

ECHO "%AppCmd%" set config /section:system.webServer/security/requestFiltering /-"hiddensegments.[segment='bin']" >> ConfigureIIS.log
call "%AppCmd%" set config /section:system.webServer/security/requestFiltering /-"hiddensegments.[segment='bin']" >> ConfigureIIS.log 2>> ConfigureIIS.err

call "%~dp0\IisBrotli\tools\SetupBrotli.cmd" "%AppCmd%"

ECHO. >> ConfigureIIS.log
ECHO End configuring IIS >> ConfigureIIS.log
