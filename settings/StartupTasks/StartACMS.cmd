@ECHO OFF

ECHO Arguments: %* > StartACMS.log
ECHO. >> StartACMS.log

ECHO Environment variables: >> StartACMS.log
SET >> StartACMS.log
ECHO. >> StartACMS.log

ECHO Installing Azure Certificate Management Service ... >> StartACMS.log
ECHO. >> StartACMS.log

CALL :doit  >> StartACMS.log 2>&1

ECHO. >> StartACMS.log
ECHO End installing Azure Certificate Management Service >> StartACMS.log

GOTO :EOF

:doit

IF "%EMULATED%"=="true" (
   ECHO Running in emulator, no need to install Azure Certificate Management Service
) ELSE (
   ACMS\AzureCredentialsManagementService.exe -checkandinstall
)