<?xml version="1.0" encoding="utf-8"?>
<AzureSecurityPack xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" service="1988c97e-373f-4894-b5af-770dab39e8fe" timestamp="2016-09-09T00:01:16.5480874Z" version="********">
  <Features>
    <Feature name="Audit" enabled="true">
      <ConfigurationImport>AzSecMdsAuditCMA.xml</ConfigurationImport>
      <StartupCommand isScript="true">AuditInstaller.cmd</StartupCommand>
    </Feature>
    <Feature name="LegacyAudit" enabled="false">
      <ConfigurationImport>AzSecMdsAuditCMA.xml</ConfigurationImport>
      <StartupCommand isScript="true">LegacyAuditInstaller.cmd</StartupCommand>
    </Feature>
    <Feature name="Antimalware" enabled="true">
      <ConfigurationImport>AzSecMdsAntimalware.xml</ConfigurationImport>
      <StartupCommand>AntimalwareInstall.cmd</StartupCommand>
    </Feature>
    <Feature name="AsmScan" enabled="true">
      <ConfigurationImport>AzSecMdsAsmScan.xml</ConfigurationImport>
      <StartupCommand>AsmScan.bat</StartupCommand>
    </Feature>
    <Feature name="SystemSecurityLog" enabled="true">
      <ConfigurationImport>AzSecMdsSystemEvents.xml</ConfigurationImport>
      <StartupCommand isScript="true">SlamAuditPolicy.cmd</StartupCommand>
    </Feature>
    <Feature name="AppLocker" enabled="false">
      <ConfigurationImport>AzSecMdsAppLocker.xml</ConfigurationImport>
      <StartupCommand isScript="true">EnableAppLocker.cmd</StartupCommand>
    </Feature>
    <Feature name="CodeIntegrity" enabled="false">
      <ConfigurationImport>AzSecMdsCodeIntegrity.xml</ConfigurationImport>
      <StartupCommand isScript="true">EnableCodeIntegrity.cmd</StartupCommand>
    </Feature>
  </Features>
</AzureSecurityPack>