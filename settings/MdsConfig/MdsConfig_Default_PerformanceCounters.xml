﻿<?xml version="1.0" encoding="utf-8"?>
<MonitoringManagement version="1.0" namespace="AzureDatabricksRPWarm" timestamp="2011-08-22T20:08:38.0823856Z" eventVersion="1">
  <Events>
    <CounterSets>
      <CounterSet eventName="SysPerfCounters" sampleRateInSeconds="60" storeType="Central">

        <Counter>\Memory\Available MBytes</Counter>
        <Counter>\Memory\Committed Bytes</Counter>
        <Counter>\Memory\Page Faults/sec</Counter>
        <Counter>\Memory\Pages/sec</Counter>

        <Counter>\TCPv4\Connections Established</Counter>
        <Counter>\TCPv4\Connection Failures</Counter>
        <Counter>\TCPv4\Connections Reset</Counter>
        <Counter>\TCPv4\Segments Sent/sec</Counter>
        <Counter>\TCPv4\Segments Received/sec</Counter>
        <Counter>\TCPv4\Segments Retransmitted/sec</Counter>

        <Counter>\HTTP Service Request Queues(*)\CurrentQueueSize</Counter>
        <Counter>\HTTP Service Request Queues(*)\MaxQueueItemAge</Counter>
        <Counter>\HTTP Service Request Queues(*)\RejectedRequests</Counter>
        <Counter>\HTTP Service Request Queues(*)\RejectionRate</Counter>
        <Counter>\HTTP Service Request Queues(*)\ArrivalRate</Counter>

        <Counter>\Network Interface(Microsoft Hyper-V Network Adapter)\Bytes Received/sec</Counter>
        <Counter>\Network Interface(Microsoft Hyper-V Network Adapter)\Bytes Sent/sec</Counter>
        <Counter>\Network Interface(Microsoft Hyper-V Network Adapter)\Bytes Total/sec</Counter>
        <Counter>\Network Interface(Microsoft Hyper-V Network Adapter)\Output Queue Length</Counter>

        <Counter>\Thread(_Total)\Context Switches/sec</Counter>

        <Counter>\Processor(_Total)\% Privileged Time</Counter>
        <Counter>\Processor(_Total)\% Processor Time</Counter>
        <Counter>\Processor(_Total)\% User Time</Counter>
        <Counter>\LogicalDisk(_Total)\% Disk Time</Counter>
        <Counter>\LogicalDisk(_Total)\% Disk Write Time</Counter>
        <Counter>\LogicalDisk(_Total)\Avg. Disk Queue Length</Counter>
        <Counter>\LogicalDisk(_Total)\% Free Space</Counter>

        <Counter>\Web Service(_Total)\Connection Attempts/sec</Counter>
        <Counter>\Web Service(_Total)\Current Connections</Counter>
        <Counter>\Web Service(_Total)\Get Requests/sec</Counter>
        <Counter>\Web Service(_Total)\Head Requests/sec</Counter>
        <Counter>\Web Service(_Total)\Post Requests/sec</Counter>
        <Counter>\Web Service(_Total)\Put Requests/sec</Counter>
        <Counter>\Web Service(_Total)\Delete Requests/sec</Counter>
        <Counter>\Web Service(_Total)\Options Requests/sec</Counter>
        <Counter>\Web Service(_Total)\Other Request Methods/sec</Counter>
        
        <Counter>\W3SVC_W3WP(_Total)\Active Requests</Counter>
        <Counter>\W3SVC_W3WP(_Total)\Active Threads Count</Counter>
        <Counter>\W3SVC_W3WP(_Total)\Requests / Sec</Counter>
        <Counter>\W3SVC_W3WP(_Total)\Total HTTP Requests Served</Counter>
        <Counter>\W3SVC_W3WP(_Total)\URI Cache Misses</Counter>
        <Counter>\W3SVC_W3WP(_Total)\Uri Cache Misses / sec</Counter>
        <Counter>\W3SVC_W3WP(_Total)\URI Cache Hits</Counter>
        <Counter>\W3SVC_W3WP(_Total)\Uri Cache Hits / sec</Counter>

        <Counter>\ASP.NET\Request Wait Time</Counter>
        <Counter>\ASP.NET\Request Execution Time</Counter>
        <Counter>\ASP.NET\Requests Queued</Counter>
        <Counter>\ASP.NET\Requests Current</Counter>

        <Counter>\ASP.NET v4.0.30319\Application Restarts</Counter>
        <Counter>\ASP.NET v4.0.30319\Error Events Raised</Counter>
        <Counter>\ASP.NET v4.0.30319\Worker Process Restarts</Counter>
        <Counter>\ASP.NET v4.0.30319\Request Execution Time</Counter>
        <Counter>\ASP.NET v4.0.30319\Request Wait Time</Counter>
        <Counter>\ASP.NET v4.0.30319\Requests Queued</Counter>

        <Counter>\ASP.NET Applications(__Total__)\Requests In Application Queue</Counter>
        <Counter>\ASP.NET Applications(__Total__)\Pipeline Instance Count</Counter>
        <Counter>\ASP.NET Applications(__Total__)\Requests Executing</Counter>
        <Counter>\ASP.NET Applications(__Total__)\Requests/Sec</Counter>

        <Counter>\ASP.NET Apps v4.0.30319(__Total__)\Error Events Raised</Counter>
        <Counter>\ASP.NET Apps v4.0.30319(__Total__)\Error Events Raised/Sec</Counter>
        <Counter>\ASP.NET Apps v4.0.30319(__Total__)\Errors During Execution</Counter>
        <Counter>\ASP.NET Apps v4.0.30319(__Total__)\Errors Total/Sec</Counter>
        <Counter>\ASP.NET Apps v4.0.30319(__Total__)\Request Execution Time</Counter>
        <Counter>\ASP.NET Apps v4.0.30319(__Total__)\Request Wait Time</Counter>
        <Counter>\ASP.NET Apps v4.0.30319(__Total__)\Requests Executing</Counter>
        <Counter>\ASP.NET Apps v4.0.30319(__Total__)\Requests Failed</Counter>
        <Counter>\ASP.NET Apps v4.0.30319(__Total__)\Requests In Application Queue</Counter>
        <Counter>\ASP.NET Apps v4.0.30319(__Total__)\Requests Total</Counter>
        <Counter>\ASP.NET Apps v4.0.30319(__Total__)\Requests/Sec</Counter>
        <Counter>\ASP.NET Apps v4.0.30319(__Total__)\Request Bytes In Total</Counter>
        <Counter>\ASP.NET Apps v4.0.30319(__Total__)\Request Bytes Out Total</Counter>

        <Counter>\.NET CLR Jit(_Global_)\% Time in Jit</Counter>
        <Counter>\.NET CLR Memory(_Global_)\% Time in GC</Counter>
        <Counter>\.NET CLR Memory(_Global_)\Finalization Survivors</Counter>
        <Counter>\.NET CLR Memory(_Global_)\# GC Handles</Counter>
        <Counter>\.NET CLR Memory(_Global_)\# Bytes in all Heaps</Counter>
        <Counter>\.NET CLR Memory(_Global_)\Allocated Bytes/sec</Counter>
        <Counter>\.NET CLR Memory(_Global_)\Gen 0 heap size</Counter>
        <Counter>\.NET CLR Memory(_Global_)\Gen 1 heap size</Counter>
        <Counter>\.NET CLR Memory(_Global_)\Gen 2 heap size</Counter>
        <Counter>\.NET CLR Memory(_Global_)\Large Object Heap size</Counter>
        <Counter>\.NET CLR Exceptions(_Global_)\# of Exceps Thrown / sec</Counter>
        <Counter>\.NET CLR Exceptions(_Global_)\# of Exceps Thrown</Counter>
        <Counter>\.NET CLR LocksAndThreads(_Global_)\# of current logical Threads</Counter>
        <Counter>\.NET CLR LocksAndThreads(_Global_)\# of current physical Threads</Counter>
        <Counter>\.NET CLR LocksAndThreads(_Global_)\Total # of Contentions</Counter>
        <Counter>\.NET CLR LocksAndThreads(_Global_)\Current Queue Length</Counter>
        <Counter>\.NET CLR LocksAndThreads(_Global_)\Contention Rate / Sec</Counter>
        <Counter>\Process(_Total)\% Processor Time</Counter>
        <Counter>\Process(_Total)\Page Faults/sec</Counter>
        <Counter>\Process(_Total)\Thread Count</Counter>
        <Counter>\Process(_Total)\Handle Count</Counter>
        <Counter>\Process(_Total)\Private Bytes</Counter>
        <Counter>\Process(_Total)\Working Set</Counter>
        <Counter>\Process(_Total)\Working Set - Private</Counter>

        <Counter>\.NET CLR Jit(WaWorkerHost)\% Time in Jit</Counter>
        <Counter>\.NET CLR Memory(WaWorkerHost)\% Time in GC</Counter>
        <Counter>\.NET CLR Memory(WaWorkerHost)\Finalization Survivors</Counter>
        <Counter>\.NET CLR Memory(WaWorkerHost)\# GC Handles</Counter>
        <Counter>\.NET CLR Memory(WaWorkerHost)\# Bytes in all Heaps</Counter>
        <Counter>\.NET CLR Memory(WaWorkerHost)\Allocated Bytes/sec</Counter>
        <Counter>\.NET CLR Memory(WaWorkerHost)\Gen 0 heap size</Counter>
        <Counter>\.NET CLR Memory(WaWorkerHost)\Gen 1 heap size</Counter>
        <Counter>\.NET CLR Memory(WaWorkerHost)\Gen 2 heap size</Counter>
        <Counter>\.NET CLR Memory(WaWorkerHost)\Large Object Heap size</Counter>
        <Counter>\.NET CLR Exceptions(WaWorkerHost)\# of Exceps Thrown / sec</Counter>
        <Counter>\.NET CLR Exceptions(WaWorkerHost)\# of Exceps Thrown</Counter>
        <Counter>\.NET CLR LocksAndThreads(WaWorkerHost)\# of current logical Threads</Counter>
        <Counter>\.NET CLR LocksAndThreads(WaWorkerHost)\# of current physical Threads</Counter>
        <Counter>\.NET CLR LocksAndThreads(WaWorkerHost)\Total # of Contentions</Counter>
        <Counter>\.NET CLR LocksAndThreads(WaWorkerHost)\Current Queue Length</Counter>
        <Counter>\.NET CLR LocksAndThreads(WaWorkerHost)\Contention Rate / Sec</Counter>
        <Counter>\Process(WaWorkerHost)\ID Process</Counter>
        <Counter>\Process(WaWorkerHost)\% Processor Time</Counter>
        <Counter>\Process(WaWorkerHost)\Page Faults/sec</Counter>
        <Counter>\Process(WaWorkerHost)\Thread Count</Counter>
        <Counter>\Process(WaWorkerHost)\Handle Count</Counter>
        <Counter>\Process(WaWorkerHost)\Private Bytes</Counter>
        <Counter>\Process(WaWorkerHost)\Working Set</Counter>
        <Counter>\Process(WaWorkerHost)\Working Set - Private</Counter>

        <Counter>\.NET CLR Jit(WaIISHost)\% Time in Jit</Counter>
        <Counter>\.NET CLR Memory(WaIISHost)\% Time in GC</Counter>
        <Counter>\.NET CLR Memory(WaIISHost)\Finalization Survivors</Counter>
        <Counter>\.NET CLR Memory(WaIISHost)\# GC Handles</Counter>
        <Counter>\.NET CLR Memory(WaIISHost)\# Bytes in all Heaps</Counter>
        <Counter>\.NET CLR Memory(WaIISHost)\Allocated Bytes/sec</Counter>
        <Counter>\.NET CLR Memory(WaIISHost)\Gen 0 heap size</Counter>
        <Counter>\.NET CLR Memory(WaIISHost)\Gen 1 heap size</Counter>
        <Counter>\.NET CLR Memory(WaIISHost)\Gen 2 heap size</Counter>
        <Counter>\.NET CLR Memory(WaIISHost)\Large Object Heap size</Counter>
        <Counter>\.NET CLR Exceptions(WaIISHost)\# of Exceps Thrown / sec</Counter>
        <Counter>\.NET CLR Exceptions(WaIISHost)\# of Exceps Thrown</Counter>
        <Counter>\.NET CLR LocksAndThreads(WaIISHost)\# of current logical Threads</Counter>
        <Counter>\.NET CLR LocksAndThreads(WaIISHost)\# of current physical Threads</Counter>
        <Counter>\.NET CLR LocksAndThreads(WaIISHost)\Total # of Contentions</Counter>
        <Counter>\.NET CLR LocksAndThreads(WaIISHost)\Current Queue Length</Counter>
        <Counter>\.NET CLR LocksAndThreads(WaIISHost)\Contention Rate / Sec</Counter>
        <Counter>\Process(WaIISHost)\ID Process</Counter>
        <Counter>\Process(WaIISHost)\% Processor Time</Counter>
        <Counter>\Process(WaIISHost)\Page Faults/sec</Counter>
        <Counter>\Process(WaIISHost)\Thread Count</Counter>
        <Counter>\Process(WaIISHost)\Handle Count</Counter>
        <Counter>\Process(WaIISHost)\Private Bytes</Counter>
        <Counter>\Process(WaIISHost)\Working Set</Counter>
        <Counter>\Process(WaIISHost)\Working Set - Private</Counter>

        <Counter>\.NET CLR Jit(w3wp)\% Time in Jit</Counter>
        <Counter>\.NET CLR Memory(w3wp)\% Time in GC</Counter>
        <Counter>\.NET CLR Memory(w3wp)\Finalization Survivors</Counter>
        <Counter>\.NET CLR Memory(w3wp)\# GC Handles</Counter>
        <Counter>\.NET CLR Memory(w3wp)\# Bytes in all Heaps</Counter>
        <Counter>\.NET CLR Memory(w3wp)\Allocated Bytes/sec</Counter>
        <Counter>\.NET CLR Memory(w3wp)\Gen 0 heap size</Counter>
        <Counter>\.NET CLR Memory(w3wp)\Gen 1 heap size</Counter>
        <Counter>\.NET CLR Memory(w3wp)\Gen 2 heap size</Counter>
        <Counter>\.NET CLR Memory(w3wp)\Large Object Heap size</Counter>
        <Counter>\.NET CLR Exceptions(w3wp)\# of Exceps Thrown / sec</Counter>
        <Counter>\.NET CLR Exceptions(w3wp)\# of Exceps Thrown</Counter>
        <Counter>\.NET CLR LocksAndThreads(w3wp)\# of current logical Threads</Counter>
        <Counter>\.NET CLR LocksAndThreads(w3wp)\# of current physical Threads</Counter>
        <Counter>\.NET CLR LocksAndThreads(w3wp)\Current Queue Length</Counter>
        <Counter>\.NET CLR LocksAndThreads(w3wp)\Contention Rate / Sec</Counter>
        <Counter>\.NET CLR LocksAndThreads(w3wp)\Total # of Contentions</Counter>
        <Counter>\Process(w3wp)\ID Process</Counter>
        <Counter>\Process(w3wp)\% Processor Time</Counter>
        <Counter>\Process(w3wp)\Page Faults/sec</Counter>
        <Counter>\Process(w3wp)\Thread Count</Counter>
        <Counter>\Process(w3wp)\Handle Count</Counter>
        <Counter>\Process(w3wp)\Private Bytes</Counter>
        <Counter>\Process(w3wp)\Working Set</Counter>
        <Counter>\Process(w3wp)\Working Set - Private</Counter>

        <Counter>\Process(MonAgentHost)\ID Process</Counter>
        <Counter>\Process(MonAgentHost)\% Processor Time</Counter>
        <Counter>\Process(MonAgentHost)\Page Faults/sec</Counter>
        <Counter>\Process(MonAgentHost)\Thread Count</Counter>
        <Counter>\Process(MonAgentHost)\Handle Count</Counter>
        <Counter>\Process(MonAgentHost)\Private Bytes</Counter>
        <Counter>\Process(MonAgentHost)\Working Set</Counter>
        <Counter>\Process(MonAgentHost)\Working Set - Private</Counter>

        <Counter>\Process(AgentCore)\ID Process</Counter>
        <Counter>\Process(AgentCore)\% Processor Time</Counter>
        <Counter>\Process(AgentCore)\Page Faults/sec</Counter>
        <Counter>\Process(AgentCore)\Thread Count</Counter>
        <Counter>\Process(AgentCore)\Handle Count</Counter>
        <Counter>\Process(AgentCore)\Private Bytes</Counter>
        <Counter>\Process(AgentCore)\Working Set</Counter>
        <Counter>\Process(AgentCore)\Working Set - Private</Counter>

        <Counter>\Process(MetricsExtension.Native)\ID Process</Counter>
        <Counter>\Process(MetricsExtension.Native)\% Processor Time</Counter>
        <Counter>\Process(MetricsExtension.Native)\Page Faults/sec</Counter>
        <Counter>\Process(MetricsExtension.Native)\Thread Count</Counter>
        <Counter>\Process(MetricsExtension.Native)\Handle Count</Counter>
        <Counter>\Process(MetricsExtension.Native)\Private Bytes</Counter>
        <Counter>\Process(MetricsExtension.Native)\Working Set</Counter>
        <Counter>\Process(MetricsExtension.Native)\Working Set - Private</Counter>

        <Counter>\Process(lsass)\ID Process</Counter>
        <Counter>\Process(lsass)\% Processor Time</Counter>
        <Counter>\Process(lsass)\Page Faults/sec</Counter>
        <Counter>\Process(lsass)\Thread Count</Counter>
        <Counter>\Process(lsass)\Handle Count</Counter>
        <Counter>\Process(lsass)\Private Bytes</Counter>
        <Counter>\Process(lsass)\Working Set</Counter>
        <Counter>\Process(lsass)\Working Set - Private</Counter>
      </CounterSet>
    </CounterSets>

    <CrashDumpMonitor>
      <CrashDumpItem eventName="CrashDumpEvent" storeType="CentralBond" useAzureWatson="true">
        <ProcessList>
          <Process>[All]</Process>
        </ProcessList>
      </CrashDumpItem>
    </CrashDumpMonitor>

    <DerivedEvents>
      <DerivedEvent eventName="SysPerfCountersDerived" storeType="Local" duration="PT60S" source="SysPerfCounters">
        <!-- following code will move process name of system perf counter to a dimension of MDM metric -->
        <!-- e.g. "\.NET CLR Jit(WaWorkerHost)\% Time in Jit" ==> "\.NET CLR Jit(*)\% Time in Jit", CounterInstance == WaWorkerHost -->
        <!-- e.g. "\Processor(_Total)\% Privileged Time" ==> "\Processor(*)\% Privileged Time", CounterInstance == _Total -->
        <Query>
          <![CDATA[
          let MdmAccount = GetEnvironmentVariable("MDM_ACCOUNT")
          let MdmNamespace = "SysPerfCounters"
          let RoleLocation = GetEnvironmentVariable("MONITORING_ROLE_LOCATION")
          let Tenant = GetEnvironmentVariable("MONITORING_TENANT")
          let Role = GetEnvironmentVariable("MONITORING_ROLE")
          let RoleInstance = GetEnvironmentVariable("MONITORING_ROLE_INSTANCE")
          let SourceNamespace = GetEnvironmentVariable("MDSCONFIG_NAMESPACE")
          let JarvisEndpoint = GetEnvironmentVariable("JARVIS_ENDPOINT")

          let CounterCategory = RegexMatch(CounterName, "^\\([^\(\)\\]+)", 1)
          let CounterInstance = RegexMatch(CounterName, "^\\([^\(\)\\]+)(?:\(([^\(\)]+)\))?", 2)
          let CounterObject   = RegexMatch(CounterName, "^\\([^\(\)\\]+)(?:\(([^\(\)]+)\))?\\([^\\]+)$", 3)
          let CounterCategoryName = (CounterInstance != "") ? Concat("", CounterCategory, "(*)") : CounterCategory
          let CounterId = Concat("\\", "", CounterCategoryName, CounterObject)
          
          let IsMetricSet = false
          if ((MdmAccount != "") && (MdmNamespace != "") && (CounterObject != ""))
          {
            IsMetricSet = SetMdmMeasureMetric(MdmAccount, MdmNamespace, CounterId, CounterValue, "CounterInstance", CounterInstance, "RoleLocation", RoleLocation, "SourceNamespace", SourceNamespace, "JarvisEndpoint", JarvisEndpoint, "__Tenant", Tenant, "__Role", Role, "__RoleInstance", RoleInstance)
          }
          ]]>
        </Query>
      </DerivedEvent>
    </DerivedEvents>
  </Events>
  
  <EventStreamingAnnotations>
    <EventStreamingAnnotation name="^SysPerfCounters$">
      <LogAnalytics>
        <Content>LogAnalyticsContent</Content>
      </LogAnalytics>
    </EventStreamingAnnotation>
  </EventStreamingAnnotations>    
</MonitoringManagement>
