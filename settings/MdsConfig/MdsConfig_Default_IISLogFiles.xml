﻿<?xml version="1.0" encoding="utf-8"?>
<MonitoringManagement version="1.0" namespace="AzureDatabricksRPWarm" timestamp="2011-08-22T20:08:38.0823856Z" eventVersion="1">
  <Events>
    <IisLogSubscriptions>
      <Subscription eventName="IISLogs" storeType="CentralBond" directoryQuotaInMB="3000" filter=".*" retentionInDays="90" />
      <Subscription eventName="IISHttpErrors" storeType="CentralBond" directoryQuotaInMB="3000" filter="HTTPERR" retentionInDays="90" />
    </IisLogSubscriptions>
  </Events>

  <EventStreamingAnnotations>
    <EventStreamingAnnotation name="^.*IISLogs$">
      <Indexing>
        <Content><![CDATA[ <Config /> ]]></Content>
      </Indexing>
      <LogAnalytics>
        <Content>LogAnalyticsContent</Content>
      </LogAnalytics>
    </EventStreamingAnnotation>

    <EventStreamingAnnotation name="^.*IISHttpErrors$">
      <Indexing>
        <Content><![CDATA[ <Config /> ]]></Content>
      </Indexing>
      <LogAnalytics>
        <Content>LogAnalyticsContent</Content>
      </LogAnalytics>
    </EventStreamingAnnotation>
  </EventStreamingAnnotations>
</MonitoringManagement>
