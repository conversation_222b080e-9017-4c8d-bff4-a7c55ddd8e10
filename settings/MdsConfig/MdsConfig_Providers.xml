﻿<MonitoringManagement version="1.0" namespace="AzureDatabricksRPWarm" timestamp="2015-03-02T23:45:00.0000000Z" eventVersion="21">
  <Imports>
    <Import file="AgentStandardEvents.xml" />
    <Import file="MdsConfig_Default_IISLogFiles.xml" />
    <Import file="MdsConfig_Default_PerformanceCounters.xml" />
    <Import file="MdsConfig_Default_WindowsEvents.xml" />
    <Import file="AzureSecurityPackMds.xml" />
    <Import file="ServiceDependencyCore.xml">
      <Condition>GetEnvironmentVariable("MONITORING_GCS_REGION").Contains("EUAP")</Condition>
    </Import>
  </Imports>
  <Accounts>
    <Account moniker="azuredatabricksrpwarmdiag" isDefault="true" />
    <Account moniker="azuredatabricksrpwarmsecurity" alias="AzSecurityStore" />
    <Account moniker="azuredatabricksrpwarmaudit" alias="AuditStore" />
  </Accounts>
  <Management eventVolume="Medium" defaultRetentionInDays="180" e2eDomain="ARM">
    <Identity tenantNameAlias="Deployment">
      <IdentityComponent name="Deployment">GetEnvironmentVariable("MONITORING_TENANT")</IdentityComponent>
      <IdentityComponent name="Role">GetEnvironmentVariable("MONITORING_ROLE")</IdentityComponent>
      <IdentityComponent name="RoleInstance">GetEnvironmentVariable("MONITORING_ROLE_INSTANCE")</IdentityComponent>
    </Identity>
    <AgentResourceUsage diskQuotaInMB="20000" />
    <AgentMetrics mdmMonitoringAccount="%MDM_ACCOUNT%" />
  </Management>
  <Events>
    <CounterSets>
      <CounterSet eventName="AppPerfCounters" sampleRateInSeconds="60" storeType="CentralBond">
        <Counter>\Providers - operations(*)\Total calls</Counter>
        <Counter>\Providers - operations(*)\Active calls</Counter>
        <Counter>\Providers - operations(*)\Call rate</Counter>
        <Counter>\Providers - operations(*)\Average time</Counter>
        <Counter>\Providers - cache(*)\Hit total</Counter>
        <Counter>\Providers - cache(*)\Hit rate</Counter>
        <Counter>\Providers - cache(*)\Hit ratio</Counter>
        <Counter>\Providers - cache(*)\Miss total</Counter>
        <Counter>\Providers - cache(*)\Miss rate</Counter>
        <Counter>\Providers - cache(*)\Miss ratio</Counter>
      </CounterSet>
    </CounterSets>
    <EtwProviders>
      <!-- Microsoft-Azure-CSM-Providers -->
      <EtwProvider guid="********-43E4-49C4-84B1-A9BFB06FEB1E" format="Manifest" storeType="CentralBond" duration="PT1M">
        <AdditionalHeaderFields>
          <Field>ActivityId</Field>
        </AdditionalHeaderFields>
        <RemoveHeaderFields>
          <Field>EventMessage</Field>
          <Field>ChannelName</Field>
          <Field>KeywordName</Field>
          <Field>OpcodeName</Field>
        </RemoveHeaderFields>
        <DefaultEvent eventName="Events" />
        <Event id="10" eventName="Service" logToDefault="false" />
        <Event id="11" eventName="Service" logToDefault="false" />
        <Event id="12" eventName="Service" logToDefault="false" />
        <Event id="13" eventName="Service" logToDefault="false" />
        <Event id="14" eventName="Service" logToDefault="false" />
        <Event id="20" eventName="Traces" logToDefault="false" />
        <Event id="21" eventName="Traces" logToDefault="false" />
        <Event id="22" eventName="Errors" logToDefault="false" />
        <Event id="23" eventName="Errors" logToDefault="false" />
        <Event id="30" eventName="HttpIncomingRequests" logToDefault="false" />
        <Event id="31" eventName="HttpIncomingRequests" logToDefault="false" />
        <Event id="32" eventName="HttpIncomingRequests" logToDefault="false" />
        <Event id="33" eventName="HttpIncomingRequests" logToDefault="false" />
        <Event id="34" eventName="HttpOutgoingRequests" logToDefault="false" />
        <Event id="35" eventName="HttpOutgoingRequests" logToDefault="false" />
        <Event id="36" eventName="HttpOutgoingRequests" logToDefault="false" />
        <Event id="37" eventName="HttpOutgoingRequests" logToDefault="false" />
        <Event id="38" eventName="StorageRequests" retentionInDays="7" />
        <Event id="39" eventName="StorageRequests" logToDefault="false" retentionInDays="7" />
        <Event id="40" eventName="StorageRequests" logToDefault="false" retentionInDays="7" />
        <Event id="41" eventName="StorageRequests" logToDefault="false" retentionInDays="7" />
        <Event id="50" eventName="JobTraces" logToDefault="false" />
        <Event id="51" eventName="JobTraces" logToDefault="false" />
        <Event id="52" eventName="JobErrors" logToDefault="false" />
        <Event id="53" eventName="JobErrors" logToDefault="false" />
        <Event id="54" eventName="JobOperations" logToDefault="false" />
        <Event id="55" eventName="JobHistory" logToDefault="false" />
        <Event id="56" eventName="JobDefinitions" logToDefault="false" />
        <Event id="57" eventName="JobDispatchingErrors" logToDefault="false" />
        <Event id="60" eventName="DispatcherTraces" logToDefault="false" />
        <Event id="61" eventName="DispatcherTraces" logToDefault="false" />
        <Event id="62" eventName="DispatcherErrors" logToDefault="false" />
        <Event id="63" eventName="DispatcherErrors" logToDefault="false" />
        <Event id="64" eventName="DispatcherEvents" logToDefault="false" />
        <Event id="65" eventName="DispatcherEvents" logToDefault="false" />
        <Event id="100" eventName="ClientErrors" logToDefault="false" />
        <Event id="101" eventName="ClientTraces" logToDefault="false" />
        <Event id="102" eventName="ClientTelemetry" logToDefault="false" />
        <Event id="103" eventName="ClientRequests" logToDefault="false" />
      </EtwProvider>
    </EtwProviders>
    <DerivedEvents>
      <DerivedEvent eventName="AppPerfCountersDerived" storeType="Local" duration="PT60S" source="AppPerfCounters">
        <Query><![CDATA[
          let MdmAccount = GetEnvironmentVariable("MDM_ACCOUNT")
          let MdmNamespace = "ProvidersPerfCounters"
          let Tenant = GetEnvironmentVariable("MONITORING_TENANT")
          let Role = GetEnvironmentVariable("MONITORING_ROLE")
          let RoleLocation = GetEnvironmentVariable("MONITORING_ROLE_LOCATION")
          let RoleInstance = GetEnvironmentVariable("MONITORING_ROLE_INSTANCE")
          let SourceNamespace = GetEnvironmentVariable("MDSCONFIG_NAMESPACE")
          let JarvisEndpoint = GetEnvironmentVariable("JARVIS_ENDPOINT")

          let CounterCategory = RegexMatch(CounterName, "^\\([^\(\)\\]+)", 1)
          let CounterInstance = RegexMatch(CounterName, "^\\([^\(\)\\]+)(?:\(([^\(\)]+)\))?", 2)
          let CounterObject   = RegexMatch(CounterName, "^\\([^\(\)\\]+)(?:\(([^\(\)]+)\))?\\([^\\]+)$", 3)
          let CounterCategoryName = (CounterInstance != "") ? Concat("", CounterCategory, "(*)") : CounterCategory
          let CounterId = Concat("\\", "", CounterCategoryName, CounterObject)
          
          let IsMetricSet = false
          if ((MdmAccount != "") && (MdmNamespace != "") && (CounterObject != ""))
          {
            IsMetricSet = SetMdmMeasureMetric(MdmAccount, MdmNamespace, CounterId, CounterValue, "CounterInstance", CounterInstance, "RoleLocation", RoleLocation, "SourceNamespace", SourceNamespace, "JarvisEndpoint", JarvisEndpoint, "__Tenant", Tenant, "__Role", Role, "__RoleInstance", RoleInstance)
          }
          ]]></Query>
      </DerivedEvent>
      <DerivedEvent eventName="HttpIncomingRequestsDerived" storeType="Local" duration="PT60S" source="HttpIncomingRequests">
        <Query><![CDATA[
          let MdmAccount = GetEnvironmentVariable("MDM_ACCOUNT")
          let MdmNamespace = "Requests"
          where 
            (MdmAccount != "" AND MdmNamespace != "" AND (EventId == 31 OR EventId == 32 OR EventId == 33))
          let Tenant = GetEnvironmentVariable("MONITORING_TENANT")
          let Role = GetEnvironmentVariable("MONITORING_ROLE")
          let RoleLocation = GetEnvironmentVariable("MONITORING_ROLE_LOCATION")
          let RoleInstance = GetEnvironmentVariable("MONITORING_ROLE_INSTANCE")
          let SourceNamespace = GetEnvironmentVariable("MDSCONFIG_NAMESPACE")
          let JarvisEndpoint = GetEnvironmentVariable("JARVIS_ENDPOINT")

          let CounterId = "HttpIncomingRequestsLatency"
          let ServerFailureCountId = "IncomingProviderFailureLatency"
          let Latency = durationInMilliseconds * 0.001
          let HttpMethod = httpMethod
          let HttpStatusCode = httpStatusCode
          let OperationName = operationName
          let AuthorizationAction = ""
          let ProviderNamespace = ""
          let Result = (EventId == 31) ? "Success" : (EventId == 33) ? "ClientFailure" : "ProviderServerFailure"

          let IsMetricSet = SetMdmMeasureMetric(MdmAccount, MdmNamespace, CounterId, Latency, "RoleLocation", RoleLocation, "SourceNamespace", SourceNamespace, "JarvisEndpoint", JarvisEndpoint, "__Tenant", Tenant, "__Role", Role, "__RoleInstance", RoleInstance, "HttpMethod", HttpMethod, "HttpStatusCode", ToString(HttpStatusCode), "OperationName", OperationName, "Result", Result, "AuthorizationAction", AuthorizationAction, "ProviderNamespace", ProviderNamespace)
          let IsServerFailureMetricSet = (Result == "ProviderServerFailure") ? SetMdmMeasureMetric(MdmAccount, MdmNamespace, ServerFailureCountId, Latency, "RoleLocation", RoleLocation, "SourceNamespace", SourceNamespace, "JarvisEndpoint", JarvisEndpoint, "__Tenant", Tenant, "__Role", Role, "__RoleInstance", RoleInstance, "HttpMethod", HttpMethod, "HttpStatusCode", ToString(HttpStatusCode), "OperationName", OperationName, "AuthorizationAction", AuthorizationAction, "ProviderNamespace", ProviderNamespace) : false
          ]]></Query>
      </DerivedEvent>
      <DerivedEvent eventName="HttpOutgoingRequestsDerived" storeType="Local" duration="PT60S" source="HttpOutgoingRequests">
        <Query><![CDATA[
          let MdmAccount = GetEnvironmentVariable("MDM_ACCOUNT")
          let MdmNamespace = "Requests"
          where 
            (MdmAccount != "" AND MdmNamespace != "" AND (EventId == 35 OR EventId == 36 OR EventId == 37))
          let Tenant = GetEnvironmentVariable("MONITORING_TENANT")
          let Role = GetEnvironmentVariable("MONITORING_ROLE")
          let RoleLocation = GetEnvironmentVariable("MONITORING_ROLE_LOCATION")
          let RoleInstance = GetEnvironmentVariable("MONITORING_ROLE_INSTANCE")
          let SourceNamespace = GetEnvironmentVariable("MDSCONFIG_NAMESPACE")
          let JarvisEndpoint = GetEnvironmentVariable("JARVIS_ENDPOINT")

          let CounterId = "HttpOutgoingRequestsLatency"
          let ServerFailureCountId = "OutgoingServerFailureLatency"
          let Latency = durationInMilliseconds * 0.001
          let HttpMethod = httpMethod
          let HttpStatusCode = httpStatusCode
          let OperationName = operationName
          let Result = (EventId == 35) ? "Success" : (EventId == 37) ? "ClientFailure" : "ServerFailure"
        
          let IsMetricSet = SetMdmMeasureMetric(MdmAccount, MdmNamespace, CounterId, Latency, "RoleLocation", RoleLocation, "SourceNamespace", SourceNamespace, "JarvisEndpoint", JarvisEndpoint, "__Tenant", Tenant, "__Role", Role, "__RoleInstance", RoleInstance, "HttpMethod", HttpMethod, "HttpStatusCode", ToString(HttpStatusCode), "OperationName", OperationName, "Result", Result)
          let IsServerFailureMetricSet = (Result == "ServerFailure") ? SetMdmMeasureMetric(MdmAccount, MdmNamespace, ServerFailureCountId, Latency, "RoleLocation", RoleLocation, "SourceNamespace", SourceNamespace, "JarvisEndpoint", JarvisEndpoint, "__Tenant", Tenant, "__Role", Role, "__RoleInstance", RoleInstance, "HttpMethod", HttpMethod, "HttpStatusCode", ToString(HttpStatusCode), "OperationName", OperationName) : false
          ]]></Query>
      </DerivedEvent>
      <DerivedEvent eventName="StorageRequestsDerived" storeType="Local" duration="PT60S" source="StorageRequests">
        <Query><![CDATA[
          let MdmAccount = GetEnvironmentVariable("MDM_ACCOUNT")
          let MdmNamespace = "Requests"
          where 
            (MdmAccount != "" AND MdmNamespace != "" AND (EventId == 39 OR EventId == 40 OR EventId == 41))
          let Tenant = GetEnvironmentVariable("MONITORING_TENANT")
          let Role = GetEnvironmentVariable("MONITORING_ROLE")
          let RoleLocation = GetEnvironmentVariable("MONITORING_ROLE_LOCATION")
          let RoleInstance = GetEnvironmentVariable("MONITORING_ROLE_INSTANCE")
          let SourceNamespace = GetEnvironmentVariable("MDSCONFIG_NAMESPACE")
          let JarvisEndpoint = GetEnvironmentVariable("JARVIS_ENDPOINT")

          let CounterId = "StorageRequestsLatency"
          let ServerFailureCountId = "StorageServerFailureLatency"
          let Latency = durationInMilliseconds * 0.001
          let AccountName = accountName
          let ResourceType = resourceType
          let HttpStatusCode = httpStatusCode
          let OperationName = operationName
          let Result = (EventId == 39) ? "Success" : (EventId == 41) ? "ClientFailure" : "ServerFailure"
          
          let IsMetricSet = SetMdmMeasureMetric(MdmAccount, MdmNamespace, CounterId, Latency, "RoleLocation", RoleLocation, "SourceNamespace", SourceNamespace, "JarvisEndpoint", JarvisEndpoint, "__Tenant", Tenant, "__Role", Role, "__RoleInstance", RoleInstance, "AccountName", AccountName, "ResourceType", ResourceType, "HttpStatusCode", ToString(HttpStatusCode), "OperationName", OperationName, "Result", Result)
          let IsServerFailureMetricSet = (Result == "ServerFailure") ? SetMdmMeasureMetric(MdmAccount, MdmNamespace, ServerFailureCountId, Latency, "RoleLocation", RoleLocation, "SourceNamespace", SourceNamespace, "JarvisEndpoint", JarvisEndpoint, "__Tenant", Tenant, "__Role", Role, "__RoleInstance", RoleInstance, "AccountName", AccountName, "ResourceType", ResourceType, "HttpStatusCode", ToString(HttpStatusCode), "OperationName", OperationName): false
          ]]></Query>
      </DerivedEvent>
      <DerivedEvent eventName="JobHistoryDerived" storeType="Local" duration="PT60S" source="JobHistory">
        <Query><![CDATA[
          let MdmAccount = GetEnvironmentVariable("MDM_ACCOUNT")
          let MdmNamespace = "Jobs"
          where 
            (MdmAccount != "" AND MdmNamespace != "")
          let Tenant = GetEnvironmentVariable("MONITORING_TENANT")
          let Role = GetEnvironmentVariable("MONITORING_ROLE")
          let RoleLocation = GetEnvironmentVariable("MONITORING_ROLE_LOCATION")
          let RoleInstance = GetEnvironmentVariable("MONITORING_ROLE_INSTANCE")
          let SourceNamespace = GetEnvironmentVariable("MDSCONFIG_NAMESPACE")
          let JarvisEndpoint = GetEnvironmentVariable("JARVIS_ENDPOINT")

          let DurationCounterId = "JobExecutionDuration"
          let Duration = double(executionTimeInMilliseconds) * 0.001
          let DelayCounterId = "JobExecutionDelay"
          let Delay = double(executionDelayInMilliseconds) * 0.001

          let Callback = RegexMatch(callback, "^([^\.]*)(\..*)?$", 1)
          let ProviderNamespace = RegexMatch(callback, "^([^\.]*)\.([^/]*)(/.*)?$", 2)
          let Result = (executionStatus == "Completed" OR executionStatus == "Succeeded") ? "Completed" : (executionStatus == "Failed" OR executionStatus == "Faulted") ? "Failed" : (executionStatus == "Cancelled" OR executionStatus == "Rescheduled") ? "Cancelled" : executionStatus
          let Exception = RegexMatch(executionMessage, "([^:\s]*Exception).*:.*", 1)
          
          let IsDurationMetricSet = SetMdmMeasureMetric(MdmAccount, MdmNamespace, DurationCounterId, Duration, "RoleLocation", RoleLocation, "SourceNamespace", SourceNamespace, "JarvisEndpoint", JarvisEndpoint, "__Tenant", Tenant, "__Role", Role, "__RoleInstance", RoleInstance, "Callback", Callback, "ProviderNamespace", ProviderNamespace, "Result", Result, "Exception", Exception)
          let IsDelayMetricSet = SetMdmMeasureMetric(MdmAccount, MdmNamespace, DelayCounterId, Delay, "RoleLocation", RoleLocation, "SourceNamespace", SourceNamespace, "JarvisEndpoint", JarvisEndpoint, "__Tenant", Tenant, "__Role", Role, "__RoleInstance", RoleInstance, "Callback", Callback, "ProviderNamespace", ProviderNamespace, "Result", Result, "Exception", Exception)
          ]]></Query>
      </DerivedEvent>
      <DerivedEvent eventName="ErrorsDerived" storeType="Local" duration="PT60S" source="Errors">
        <Query><![CDATA[
          let MdmAccount = GetEnvironmentVariable("MDM_ACCOUNT")
          let MdmNamespace = "Errors"
          where 
            (MdmAccount != "" AND MdmNamespace != "")
          let Tenant = GetEnvironmentVariable("MONITORING_TENANT")
          let Role = GetEnvironmentVariable("MONITORING_ROLE")
          let RoleLocation = GetEnvironmentVariable("MONITORING_ROLE_LOCATION")
          let RoleInstance = GetEnvironmentVariable("MONITORING_ROLE_INSTANCE")
          let SourceNamespace = GetEnvironmentVariable("MDSCONFIG_NAMESPACE")
          let JarvisEndpoint = GetEnvironmentVariable("JARVIS_ENDPOINT")

          let CounterId = "Errors"
          let MetricValue = 1
          let Source = "Errors"
          let OperationName = operationName
          let ProviderNamespace = RegexMatch(OperationName, "^.*\(([^/]+)\)$", 1)
          let Level = (EventId == 23) ? "Critical" : "Error"
          let Exception = RegexMatch(exception, "([^:\s]*Exception).*:", 1)

          let IsMetricSet = SetMdmMeasureMetric(MdmAccount, MdmNamespace, CounterId, MetricValue, "RoleLocation", RoleLocation, "SourceNamespace", SourceNamespace, "JarvisEndpoint", JarvisEndpoint, "__Tenant", Tenant, "__Role", Role, "__RoleInstance", RoleInstance, "Source", Source, "OperationName", OperationName, "ProviderNamespace", ProviderNamespace, "Level", Level, "Exception", Exception)
          ]]></Query>
      </DerivedEvent>
      <DerivedEvent eventName="JobErrorsDerived" storeType="Local" duration="PT60S" source="JobErrors">
        <Query><![CDATA[
          let MdmAccount = GetEnvironmentVariable("MDM_ACCOUNT")
          let MdmNamespace = "Errors"
          where 
            (MdmAccount != "" AND MdmNamespace != "")
          let Tenant = GetEnvironmentVariable("MONITORING_TENANT")
          let Role = GetEnvironmentVariable("MONITORING_ROLE")
          let RoleLocation = GetEnvironmentVariable("MONITORING_ROLE_LOCATION")
          let RoleInstance = GetEnvironmentVariable("MONITORING_ROLE_INSTANCE")
          let SourceNamespace = GetEnvironmentVariable("MDSCONFIG_NAMESPACE")
          let JarvisEndpoint = GetEnvironmentVariable("JARVIS_ENDPOINT")

          let CounterId = "Errors"
          let MetricValue = 1
          let Source = "JobErrors"
          let OperationName = operationName
          let ProviderNamespace = ""
          let Level = (EventId == 53) ? "Critical" : "Error"
          let Exception = RegexMatch(message, "([^:\s']*Exception).*:", 1)

          let IsMetricSet = SetMdmMeasureMetric(MdmAccount, MdmNamespace, CounterId, MetricValue, "RoleLocation", RoleLocation, "SourceNamespace", SourceNamespace, "JarvisEndpoint", JarvisEndpoint, "__Tenant", Tenant, "__Role", Role, "__RoleInstance", RoleInstance, "Source", Source, "OperationName", OperationName, "ProviderNamespace", ProviderNamespace, "Level", Level, "Exception", Exception)
          ]]></Query>
      </DerivedEvent>
      <DerivedEvent eventName="JobDispatchingErrorsDerived" storeType="Local" duration="PT60S" source="JobDispatchingErrors">
        <Query><![CDATA[
          let MdmAccount = GetEnvironmentVariable("MDM_ACCOUNT")
          let MdmNamespace = "Errors"
          where 
            (MdmAccount != "" AND MdmNamespace != "")
          let Tenant = GetEnvironmentVariable("MONITORING_TENANT")
          let Role = GetEnvironmentVariable("MONITORING_ROLE")
          let RoleLocation = GetEnvironmentVariable("MONITORING_ROLE_LOCATION")
          let RoleInstance = GetEnvironmentVariable("MONITORING_ROLE_INSTANCE")
          let SourceNamespace = GetEnvironmentVariable("MDSCONFIG_NAMESPACE")
          let JarvisEndpoint = GetEnvironmentVariable("JARVIS_ENDPOINT")

          let CounterId = "Errors"
          let MetricValue = 1
          let Source = "JobDispatchingErrors"
          let OperationName = operationName
          let ProviderNamespace = ""
          let Level = "Error"
          let Exception = RegexMatch(exception, "([^:\s]*Exception).*:", 1)

          let IsMetricSet = SetMdmMeasureMetric(MdmAccount, MdmNamespace, CounterId, MetricValue, "RoleLocation", RoleLocation, "SourceNamespace", SourceNamespace, "JarvisEndpoint", JarvisEndpoint, "__Tenant", Tenant, "__Role", Role, "__RoleInstance", RoleInstance, "Source", Source, "OperationName", OperationName, "ProviderNamespace", ProviderNamespace, "Level", Level, "Exception", Exception)
          ]]></Query>
      </DerivedEvent>
      <DerivedEvent eventName="DispatcherErrorsDerived" storeType="Local" duration="PT60S" source="DispatcherErrors">
        <Query><![CDATA[
          let MdmAccount = GetEnvironmentVariable("MDM_ACCOUNT")
          let MdmNamespace = "Errors"
          where 
            (MdmAccount != "" AND MdmNamespace != "")
          let Tenant = GetEnvironmentVariable("MONITORING_TENANT")
          let Role = GetEnvironmentVariable("MONITORING_ROLE")
          let RoleLocation = GetEnvironmentVariable("MONITORING_ROLE_LOCATION")
          let RoleInstance = GetEnvironmentVariable("MONITORING_ROLE_INSTANCE")
          let SourceNamespace = GetEnvironmentVariable("MDSCONFIG_NAMESPACE")
          let JarvisEndpoint = GetEnvironmentVariable("JARVIS_ENDPOINT")

          let CounterId = "Errors"
          let MetricValue = 1
          let Source = "DispatcherErrors"
          let OperationName = operationName
          let ProviderNamespace = ""
          let Level = (EventId == 63) ? "Critical" : "Error"
          let Exception = RegexMatch(exception, "([^:\s]*Exception).*:", 1)

          let IsMetricSet = SetMdmMeasureMetric(MdmAccount, MdmNamespace, CounterId, MetricValue, "RoleLocation", RoleLocation, "SourceNamespace", SourceNamespace, "JarvisEndpoint", JarvisEndpoint, "__Tenant", Tenant, "__Role", Role, "__RoleInstance", RoleInstance, "Source", Source, "OperationName", OperationName, "ProviderNamespace", ProviderNamespace, "Level", Level, "Exception", Exception)
          ]]></Query>
      </DerivedEvent>
    </DerivedEvents>
    <Extensions>
      <Extension extensionName="MetricsExtension">
        <CommandLine><![CDATA[start.bat]]></CommandLine>
      </Extension>
    </Extensions>
  </Events>
  <EventStreamingAnnotations>
    <EventStreamingAnnotation name="^AppPerfCounters$">
      <LogAnalytics>
        <Content>LogAnalyticsContent</Content>
      </LogAnalytics>
    </EventStreamingAnnotation>
    <EventStreamingAnnotation name="^.*Traces$">
      <LogAnalytics>
        <Content>LogAnalyticsContent</Content>
      </LogAnalytics>
      <Cosmos>
        <Content><![CDATA[ <Config/> ]]></Content>
      </Cosmos>
    </EventStreamingAnnotation>
    <EventStreamingAnnotation name="^.*Errors$">
      <LogAnalytics>
        <Content>LogAnalyticsContent</Content>
      </LogAnalytics>
      <Cosmos>
        <Content><![CDATA[ <Config/> ]]></Content>
      </Cosmos>
    </EventStreamingAnnotation>
    <EventStreamingAnnotation name="^.*HttpIncomingRequests$">
      <LogAnalytics>
        <Content>LogAnalyticsContent</Content>
      </LogAnalytics>
      <Cosmos>
        <Content><![CDATA[ <Config/> ]]></Content>
      </Cosmos>
      <Scrubbing>
        <Content><![CDATA[
            <ScrubbingConfiguration>
              <Strategy>HMACSHA256</Strategy>
              <Rules>
                <Rule Name="NoPII" Type="EntireColumn" ColumnsToApply="clientIpAddress"/>
              </Rules>
            </ScrubbingConfiguration>
          ]]></Content>
      </Scrubbing>
    </EventStreamingAnnotation>
    <EventStreamingAnnotation name="^.*HttpOutgoingRequests$">
      <LogAnalytics>
        <Content>LogAnalyticsContent</Content>
      </LogAnalytics>
      <Cosmos>
        <Content><![CDATA[ <Config/> ]]></Content>
      </Cosmos>
    </EventStreamingAnnotation>
    <EventStreamingAnnotation name="^Job.*$">
      <LogAnalytics>
        <Content>LogAnalyticsContent</Content>
      </LogAnalytics>
    </EventStreamingAnnotation>
    <EventStreamingAnnotation name="^DispatcherEvents$">
      <LogAnalytics>
        <Content>LogAnalyticsContent</Content>
      </LogAnalytics>
    </EventStreamingAnnotation>
    <EventStreamingAnnotation name="^ClientTelemetry$">
      <LogAnalytics>
        <Content>LogAnalyticsContent</Content>
      </LogAnalytics>
      <Cosmos>
        <Content><![CDATA[ <Config/> ]]></Content>
      </Cosmos>
    </EventStreamingAnnotation>
    <EventStreamingAnnotation name="^ClientRequests$">
      <LogAnalytics>
        <Content>LogAnalyticsContent</Content>
      </LogAnalytics>
      <Cosmos>
        <Content><![CDATA[ <Config/> ]]></Content>
      </Cosmos>
      <Scrubbing>
        <Content><![CDATA[
            <ScrubbingConfiguration>
              <Strategy>HMACSHA256</Strategy>
              <Rules>
                <Rule Name="NoPII" Type="EntireColumn" ColumnsToApply="clientIpAddress"/>
              </Rules>
            </ScrubbingConfiguration>
          ]]></Content>
      </Scrubbing>
    </EventStreamingAnnotation>
    <EventStreamingAnnotation name="^.*Service$">
      <LogAnalytics>
        <Content>LogAnalyticsContent</Content>
      </LogAnalytics>
      <Cosmos>
        <Content><![CDATA[ <Config/> ]]></Content>
      </Cosmos>
    </EventStreamingAnnotation>
    <EventStreamingAnnotation name="^MaCounterSummary$">
      <LogAnalytics>
        <Content>LogAnalyticsContent</Content>
      </LogAnalytics>
    </EventStreamingAnnotation>
    <EventStreamingAnnotation name="^MaHeartBeats$">
      <LogAnalytics>
        <Content>LogAnalyticsContent</Content>
      </LogAnalytics>
    </EventStreamingAnnotation>
    <EventStreamingAnnotation name="^MaQosSummary$">
      <LogAnalytics>
        <Content>LogAnalyticsContent</Content>
      </LogAnalytics>
    </EventStreamingAnnotation>
    <EventStreamingAnnotation name="^MaErrorsSummary$">
      <LogAnalytics>
        <Content>LogAnalyticsContent</Content>
      </LogAnalytics>
    </EventStreamingAnnotation>
    <EventStreamingAnnotation name="^StorageRequests$">
      <LogAnalytics>
        <Content>LogAnalyticsContent</Content>
      </LogAnalytics>
      <Cosmos>
        <Content><![CDATA[<Config />]]></Content>
      </Cosmos>
    </EventStreamingAnnotation>
  </EventStreamingAnnotations>
</MonitoringManagement>