﻿<?xml version="1.0" encoding="utf-8"?>
<MonitoringManagement version="1.0" namespace="AzureDatabricksRPWarm" timestamp="2011-08-22T20:08:38.0823856Z" eventVersion="1">
  <Events>
    <WindowsEventLogSubscriptions>
      <Subscription eventName="WindowsEvents" query="System!*[System[Level=1 or Level=2 or Level=3 or Level=4]]" storeType="Central" deadline="PT10M">
        <Column name="TimeCreated">
          <Value>/Event/System/TimeCreated/@SystemTime</Value>
        </Column>
        <Column name="ProviderName">
          <Value>/Event/System/Provider/@Name</Value>
        </Column>
        <Column name="Level" type="mt:int32">
          <Value>/Event/System/Level</Value>
        </Column>
        <Column name="EventID" type="mt:int32">
          <Value>/Event/System/EventID</Value>
        </Column>
        <Column name="Channel">
          <Value>/Event/System/Channel</Value>
        </Column>
        <Column name="EventData">
          <Value>/Event/EventData</Value>
        </Column>
      </Subscription>

      <Subscription eventName="WindowsEvents" query="Application!*[System[Level=1 or Level=2 or Level=3]]" storeType="Central" deadline="PT10M">
        <Column name="TimeCreated">
          <Value>/Event/System/TimeCreated/@SystemTime</Value>
        </Column>
        <Column name="ProviderName">
          <Value>/Event/System/Provider/@Name</Value>
        </Column>
        <Column name="Level" type="mt:int32">
          <Value>/Event/System/Level</Value>
        </Column>
        <Column name="EventID" type="mt:int32">
          <Value>/Event/System/EventID</Value>
        </Column>
        <Column name="Channel">
          <Value>/Event/System/Channel</Value>
        </Column>
        <Column name="EventData">
          <Value>/Event/EventData</Value>
        </Column>
      </Subscription>
    </WindowsEventLogSubscriptions>
  </Events>
</MonitoringManagement>
