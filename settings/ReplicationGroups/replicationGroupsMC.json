[{"properties": {"replicationStreamName": "regional", "storageAccountProperties": [{"storageAccountId": "/subscriptions/a1fc4d92-3628-4757-943a-73e4716bf01e/resourceGroups/adbrpchinaeast2/providers/Microsoft.Storage/storageAccounts/chinaeast2workspacedata", "keyType": "Primary"}, {"storageAccountId": "/subscriptions/a1fc4d92-3628-4757-943a-73e4716bf01e/resourceGroups/adbrpchinaeast2/providers/Microsoft.Storage/storageAccounts/chinaeast2worksr", "keyType": "Primary"}], "compactionTables": [{"tableName": "appliances", "mutationAlgorithmType": "NotSpecified"}], "snapShotStorageAccountProperties": {"storageAccountId": "/subscriptions/a1fc4d92-3628-4757-943a-73e4716bf01e/resourceGroups/adbrpchinaeast2/providers/Microsoft.Storage/storageAccounts/chinaeast2worksb", "keyType": "Primary"}, "state": "Enabled", "isTombstoneEvictionEnabled": true, "replicationGroupType": "Table"}, "id": "/subscriptions/a1fc4d92-3628-4757-943a-73e4716bf01e/resourceGroups/adbrpreplicationgroups/providers/Microsoft.StorageReplication/replicationGroups/chinaeast2replicationgroup", "name": "chinaeast2replicationgroup", "type": "Microsoft.StorageReplication/replicationGroups", "tags": {}}, {"properties": {"replicationStreamName": "regional", "storageAccountProperties": [{"storageAccountId": "/subscriptions/a1fc4d92-3628-4757-943a-73e4716bf01e/resourceGroups/adbrpchinanorth2/providers/Microsoft.Storage/storageAccounts/chinanorth2workspacedata", "keyType": "Primary"}, {"storageAccountId": "/subscriptions/a1fc4d92-3628-4757-943a-73e4716bf01e/resourceGroups/adbrpchinanorth2/providers/Microsoft.Storage/storageAccounts/chinanorth2worksr", "keyType": "Primary"}], "compactionTables": [{"tableName": "appliances", "mutationAlgorithmType": "NotSpecified"}], "snapShotStorageAccountProperties": {"storageAccountId": "/subscriptions/a1fc4d92-3628-4757-943a-73e4716bf01e/resourceGroups/adbrpchinanorth2/providers/Microsoft.Storage/storageAccounts/chinanorth2worksb", "keyType": "Primary"}, "state": "Enabled", "isTombstoneEvictionEnabled": true, "replicationGroupType": "Table"}, "id": "/subscriptions/a1fc4d92-3628-4757-943a-73e4716bf01e/resourceGroups/adbrpreplicationgroups/providers/Microsoft.StorageReplication/replicationGroups/chinanorth2replicationgroup", "name": "chinanorth2replicationgroup", "type": "Microsoft.StorageReplication/replicationGroups", "tags": {}}]