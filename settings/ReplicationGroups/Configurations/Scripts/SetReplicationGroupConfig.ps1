$regionShortNames = "eastus2euap", "westus", "eastus2", "westeurope", "northeurope", "eastus",
 "seastasia", "eastasia", "scentralus", "ncentralus", "westus2", "centralus", "ukwest", "uksouth",
 "auseast", "ausseast", "auscentral", "auscentral2", "japaneast", "japanwest", "cancentral", "caneast",
 "indcentral", "indsouth", "indwest", "korcentral", "safrnorth", "brazsouth", "francentral", "uaenorth",
 "switznorth", "norwayeast", "gerwcentral", "switzwest", "swedcentral", "wcentralus", "westus3", "qatcentral", "cenuseuap"

foreach($regionShortName in $regionShortNames)
{
    $rgName = $regionShortName + "replicationgroup"
    $armTemplateFile = "$PSScriptRoot\..\" + $regionShortName + ".json"
    $Uri = "/subscriptions/9ae8e263-8df7-4b7e-b27b-d59f0bfb685b/resourcegroups/adbrpreplicationgroups/providers/microsoft.storagereplication/replicationgroups/$rgName" + "?api-version=2017-02-01-preview"
    armclient put $Uri "$armTemplateFile"
}

$replicationGroupConfig = "$PSScriptRoot\..\..\replicationGroups.json"
armclient get /subscriptions/9ae8e263-8df7-4b7e-b27b-d59f0bfb685b/resourcegroups/adbrpreplicationgroups/providers/microsoft.storagereplication/replicationgroups?api-version=2017-02-01-preview > $replicationGroupConfig