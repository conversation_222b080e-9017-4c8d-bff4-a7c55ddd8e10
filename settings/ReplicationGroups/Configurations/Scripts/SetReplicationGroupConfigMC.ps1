$regionShortNames = "chinanorth2", "chinaeast2", "chinaeast3", "chinanorth3"

foreach($regionShortName in $regionShortNames)
{
    $rgName = $regionShortName + "replicationgroup"
    $armTemplateFile = "$PSScriptRoot\..\" + $regionShortName + ".json"
    $Uri = "/subscriptions/a1fc4d92-3628-4757-943a-73e4716bf01e/resourcegroups/adbrpreplicationgroups/providers/microsoft.storagereplication/replicationgroups/$rgName" + "?api-version=2017-02-01-preview"
    armclient put $Uri "$armTemplateFile"
}

$replicationGroupConfig = "$PSScriptRoot\..\..\replicationGroupsMC.json"
armclient get /subscriptions/a1fc4d92-3628-4757-943a-73e4716bf01e/resourcegroups/adbrpreplicationgroups/providers/microsoft.storagereplication/replicationgroups?api-version=2017-02-01-preview > $replicationGroupConfig