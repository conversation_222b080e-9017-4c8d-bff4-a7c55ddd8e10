$prodRegionShortNames = "eastus2euap", "westus", "eastus2", "westeurope", "northeurope", "eastus",
 "seastasia", "eastasia", "scentralus", "ncentralus", "westus2", "centralus", "ukwest", "uksouth",
 "auseast", "ausseast", "auscentral", "auscentral2", "japaneast", "japanwest", "cancentral", "caneast",
 "indcentral", "indsouth", "indwest", "korcentral", "safrnorth", "brazsouth", "francentral", "uaenorth",
 "switznorth", "norwayeast", "gerwcentral", "switzwest", "swedcentral", "wcentralus", "westus3", "qatcentral", "cenuseuap"

foreach($regionShortName in $prodRegionShortNames)
{
    $rgName = "adbrp" + $regionShortName
    $armTemplateFile = $regionShortName + ".json"
    $replicationGroupResource = Get-Content -Path "$PSScriptRoot\..\Templates\Template-Production.json"
    $replicationGroupResource = $replicationGroupResource.Replace('RGNAME', $rgName).Replace('SHORTREGIONNAME', $regionShortName)
    $replicationGroupResource | Set-Content "$PSScriptRoot\..\$armTemplateFile"
}

$fairfaxRegionShortNames = "govarizona", "govtexas", "govvirginia"

foreach($regionShortName in $fairfaxRegionShortNames)
{
    $rgName = "adbrp" + $regionShortName
    $armTemplateFile = $regionShortName + ".json"
    $replicationGroupResource = Get-Content -Path "$PSScriptRoot\..\Templates\Template-Fairfax.json"
    $replicationGroupResource = $replicationGroupResource.Replace('RGNAME', $rgName).Replace('SHORTREGIONNAME', $regionShortName)
    $replicationGroupResource | Set-Content "$PSScriptRoot\..\$armTemplateFile"
}

$mooncakeRegionShortNames = "chinanorth2", "chinaeast2", "chinaeast3", "chinanorth3"

foreach($regionShortName in $mooncakeRegionShortNames)
{
    $rgName = "adbrp" + $regionShortName
    $armTemplateFile = $regionShortName + ".json"
    $replicationGroupResource = Get-Content -Path "$PSScriptRoot\..\Templates\Template-Mooncake.json"
    $replicationGroupResource = $replicationGroupResource.Replace('RGNAME', $rgName).Replace('SHORTREGIONNAME', $regionShortName)
    $replicationGroupResource | Set-Content "$PSScriptRoot\..\$armTemplateFile"
}