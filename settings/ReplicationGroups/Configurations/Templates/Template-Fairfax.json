{"properties": {"replicationStreamName": "regional", "storageAccountProperties": [{"storageAccountId": "/subscriptions/37a352eb-6f92-45cb-91a2-27cafb74d09b/resourceGroups/RGNAME/providers/Microsoft.Storage/storageAccounts/SHORTREGIONNAMEworkspacedata", "keyType": "Primary"}, {"storageAccountId": "/subscriptions/37a352eb-6f92-45cb-91a2-27cafb74d09b/resourceGroups/RGNAME/providers/Microsoft.Storage/storageAccounts/SHORTREGIONNAMEworksr", "keyType": "Primary"}], "compactionTables": [{"tableName": "appliances", "mutationAlgorithmType": "NotSpecified"}], "snapShotStorageAccountProperties": {"storageAccountId": "/subscriptions/37a352eb-6f92-45cb-91a2-27cafb74d09b/resourceGroups/RGNAME/providers/Microsoft.Storage/storageAccounts/SHORTREGIONNAMEworksb", "keyType": "Primary"}, "state": "Enabled", "isTombstoneEvictionEnabled": true, "replicationGroupType": "Table"}, "id": "/subscriptions/37a352eb-6f92-45cb-91a2-27cafb74d09b/resourceGroups/adbrpreplicationgroups/providers/Microsoft.StorageReplication/replicationGroups/SHORTREGIONNAMEreplicationgroup", "name": "SHORTREGIONNAMEreplicationgroup", "type": "Microsoft.StorageReplication/replicationGroups", "tags": {}}