{"properties": {"replicationStreamName": "regional", "storageAccountProperties": [{"storageAccountId": "/subscriptions/a1fc4d92-3628-4757-943a-73e4716bf01e/resourceGroups/RGNAME/providers/Microsoft.Storage/storageAccounts/SHORTREGIONNAMEworkspacedata", "keyType": "Primary"}, {"storageAccountId": "/subscriptions/a1fc4d92-3628-4757-943a-73e4716bf01e/resourceGroups/RGNAME/providers/Microsoft.Storage/storageAccounts/SHORTREGIONNAMEworksr", "keyType": "Primary"}], "compactionTables": [{"tableName": "appliances", "mutationAlgorithmType": "NotSpecified"}], "snapShotStorageAccountProperties": {"storageAccountId": "/subscriptions/a1fc4d92-3628-4757-943a-73e4716bf01e/resourceGroups/RGNAME/providers/Microsoft.Storage/storageAccounts/SHORTREGIONNAMEworksb", "keyType": "Primary"}, "state": "Enabled", "isTombstoneEvictionEnabled": true, "replicationGroupType": "Table"}, "id": "/subscriptions/a1fc4d92-3628-4757-943a-73e4716bf01e/resourceGroups/adbrpreplicationgroups/providers/Microsoft.StorageReplication/replicationGroups/SHORTREGIONNAMEreplicationgroup", "name": "SHORTREGIONNAMEreplicationgroup", "type": "Microsoft.StorageReplication/replicationGroups", "tags": {}}