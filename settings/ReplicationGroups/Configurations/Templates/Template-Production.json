{"properties": {"replicationStreamName": "regional", "storageAccountProperties": [{"storageAccountId": "/subscriptions/9ae8e263-8df7-4b7e-b27b-d59f0bfb685b/resourceGroups/RGNAME/providers/Microsoft.Storage/storageAccounts/SHORTREGIONNAMEworkspacedata", "keyType": "Primary"}, {"storageAccountId": "/subscriptions/9ae8e263-8df7-4b7e-b27b-d59f0bfb685b/resourceGroups/RGNAME/providers/Microsoft.Storage/storageAccounts/SHORTREGIONNAMEworksr", "keyType": "Primary"}], "compactionTables": [{"tableName": "appliances", "mutationAlgorithmType": "NotSpecified"}], "snapShotStorageAccountProperties": {"storageAccountId": "/subscriptions/9ae8e263-8df7-4b7e-b27b-d59f0bfb685b/resourceGroups/RGNAME/providers/Microsoft.Storage/storageAccounts/SHORTREGIONNAMEworksb", "keyType": "Primary"}, "state": "Enabled", "isTombstoneEvictionEnabled": true, "replicationGroupType": "Table"}, "id": "/subscriptions/9ae8e263-8df7-4b7e-b27b-d59f0bfb685b/resourceGroups/adbrpreplicationgroups/providers/Microsoft.StorageReplication/replicationGroups/SHORTREGIONNAMEreplicationgroup", "name": "SHORTREGIONNAMEreplicationgroup", "type": "Microsoft.StorageReplication/replicationGroups", "tags": {}}