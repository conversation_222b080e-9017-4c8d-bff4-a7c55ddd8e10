#################################################################################
#                         OneBranch Pipelines - Official                        #
# This pipeline was created by EasyStart from a sample located at:              #
#   https://aka.ms/obpipelines/easystart/samples                                #
# Documentation:  https://aka.ms/obpipelines                                    #
# Yaml Schema:    https://aka.ms/obpipelines/yaml/schema                        #
# Retail Tasks:   https://aka.ms/obpipelines/tasks                              #
# Support:        https://aka.ms/onebranchsup                                   #
#################################################################################

trigger: none # https://aka.ms/obpipelines/triggers

schedules: # https://learn.microsoft.com/en-us/azure/devops/pipelines/process/scheduled-triggers?view=azure-devops&tabs=yaml
- cron: "0 10 * * *" # Runs every day at 2AM PST (10AM UTC)
  displayName: Daily Official Build
  branches:
    include:
    - masterAspnetcore
  always: true

parameters: # parameters are shown up in ADO UI in a build queue time
- name: 'debug'
  displayName: 'Enable debug output'
  type: boolean
  default: false

variables:
  CDP_DEFINITION_BUILD_COUNT: $[counter('', 0)] # needed for onebranch.pipeline.version task https://aka.ms/obpipelines/versioning
  system.debug: ${{ parameters.debug }}
  ENABLE_PRS_DELAYSIGN: 1
  ROOT: $(Build.SourcesDirectory)
  REPOROOT: $(Build.SourcesDirectory)
  OUTPUTROOT: $(REPOROOT)\out
  NUGET_XMLDOC_MODE: none
  MSBUILD_EXE: C:\Program Files\Microsoft Visual Studio\2022\Enterprise\MSBuild\Current\Bin\amd64\MSBuild.exe
  WindowsContainerImage: 'onebranch.azurecr.io/windows/ltsc2019/vse2022:latest' # Docker image which is used to build the project https://aka.ms/obpipelines/containers

resources:
  repositories: 
    - repository: templates
      type: git
      name: OneBranch.Pipelines/GovernedTemplates
      ref: refs/heads/main

extends:
  template: v2/OneBranch.Official.CrossPlat.yml@templates # https://aka.ms/obpipelines/templates
  parameters:
    cloudvault: # https://aka.ms/obpipelines/cloudvault
      enabled: false
    globalSdl: # https://aka.ms/obpipelines/sdl
      tsa:
        enabled: true # onebranch publish all sdl results to TSA. If TSA is disabled all SDL tools will forced into 'break' build mode.
      # credscan:
      #   suppressionsFile: $(Build.SourcesDirectory)\.config\CredScanSuppressions.json
      binskim:
        break: true # always break the build on binskim issues in addition to TSA upload
      policheck:
        break: true # always break the build on policheck issues. You can disable it by setting to 'false'
      # suppression:
      #   suppressionFile: $(Build.SourcesDirectory)\.gdn\global.gdnsuppress

    stages:
    - stage: build
      jobs:
      - job: main
        pool:
          type: windows  # read more about custom job pool types at https://aka.ms/obpipelines/yaml/jobs
        
        variables:
          ob_outputDirectory: '$(Build.SourcesDirectory)\deployment\ExpressV2\ServiceGroupRoot' # this directory is uploaded to pipeline artifacts, reddog and cloudvault. More info at https://aka.ms/obpipelines/artifacts
          ob_sdl_binskim_break: true # https://aka.ms/obpipelines/sdl
          ${{ if eq(variables['Build.SourceBranch'], 'refs/heads/master') }}: # conditionally enable symbolsPublishing for master branch only
            ob_symbolsPublishing_enabled: true # https://aka.ms/obpipelines/symbols

          # ob_sdl_suppression_suppressionFile: $(Build.SourcesDirectory)\.gdn\build.official.gdnsuppress
          
        steps:
          - task: onebranch.pipeline.version@1 # generates automatic version. For other versioning options check https://aka.ms/obpipelines/versioning
            displayName: 'Setup BuildNumber'
            inputs:
              system: 'RevisionCounter'
              major: '1'
              minor: '0'
              exclude_commit: true

          - task: PowerShell@2
            displayName: 'Execute inline powershell script'
            inputs:
              targetType: 'inline'
              script: |
                Write-Host $Env:ADB_PARTNERS_BRANCH
                git clone https://OAuth:$(System.AccessToken)@msdata.visualstudio.com/DefaultCollection/Azure%20Databricks%20Partners/_git/ADB_Partners
                cd ADB_Partners
                git pull
                git checkout $Env:ADB_PARTNERS_BRANCH
                git status
                git log --oneline -1

          - task: CopyFiles@2
            displayName: 'Copy DatabricksTemplates files to Provider.Common'
            inputs:
              SourceFolder: '$(Build.SourcesDirectory)\ADB_Partners\DatabricksTemplates'
              Contents: '**'
              TargetFolder: '$(Build.SourcesDirectory)\src\providers\Roles\Providers.Common\DatabricksTemplates'

          - task: MSBuild@1
            displayName: 'Clean Prod solution'
            inputs:
              solution: '$(Build.SourcesDirectory)\src\Prod.sln'
              msbuildLocationMethod: 'location'
              msbuildLocation: '$(MSBUILD_EXE)'
              platform: 'x64'
              configuration: 'release'
              msbuildArguments: '/t:Clean'
              clean: true
              maximumCpuCount: true
              logProjectEvents: true

          # Restore Nuget packages
          - task: NuGetCommand@2
            displayName: 'Restore Nuget pacakages'
            inputs:
              command: 'restore'
              restoreSolution: 'src/Prod.sln'
              feedsToUse: 'config'
              nugetConfigPath: '$(Build.SourcesDirectory)\NuGet.Config'

          - task: MSBuild@1
            displayName: 'Build Prod solution'
            inputs:
              solution: '$(Build.SourcesDirectory)\src\Prod.sln'
              msbuildLocationMethod: 'location'
              msbuildLocation: '$(MSBUILD_EXE)'
              platform: 'x64'
              configuration: 'release'
              msbuildArguments: '/v:n /nr:false /flp1:Verbosity=d;LogFile=$(OUTPUTROOT)\logs\msbuild_release.log;Encoding=UTF-8 /flp2:logfile=$(OUTPUTROOT)\logs\msbuild_release.err;errorsonly /bl:$(OUTPUTROOT)\logs\msbuild_release.binlog'
              clean: true
              maximumCpuCount: true
              logProjectEvents: true

          - task: VSTest@2
            displayName: 'Run unit tests'
            inputs:
              testSelector: 'testAssemblies'
              testAssemblyVer2: |
                **\bin\Release\UnitTests.Providers.Feature.dll
                **\bin\Release\UnitTests.Providers.Worker.dll
                **\bin\Release\UnitTests.Providers.Common.dll
              searchFolder: '$(Build.SourcesDirectory)\src'
              resultsFolder: '$(Build.SourcesDirectory)\out\logs\TestResults'
              runInParallel: false
              codeCoverageEnabled: true
              testRunTitle: 'unit tests'
              platform: 'x64'
              configuration: 'release'
              publishRunAttachments: true
              failOnMinTestsNotRun: true
              rerunFailedTests: true
              runSettingsFile: '$(Build.SourcesDirectory)\.runsettings'

          - task: PowerShell@2
            displayName: 'Setup integration tests environment'
            inputs:
              targetType: 'inline'
              script: |
                New-SelfSignedCertificate -DnsName "dummycert.azurewebsites.net" -CertStoreLocation "cert:\LocalMachine\My"

          - task: PowerShell@2
            displayName: Start Azurite
            inputs:
              filePath: '$(Build.SourcesDirectory)/.pipelines/StartAzurite.ps1'

          - task: VSTest@2
            displayName: 'Run integration tests'
            inputs:
              testSelector: 'testAssemblies'
              testAssemblyVer2: |
                **\bin\Release\IntegrationTests.ResourceProvider.dll
              searchFolder: '$(Build.SourcesDirectory)\src'
              resultsFolder: '$(Build.SourcesDirectory)\out\logs\IntegrationTestResults'
              runInParallel: false
              codeCoverageEnabled: true
              testRunTitle: 'integration tests'
              platform: 'x64'
              configuration: 'release'
              publishRunAttachments: true
              failOnMinTestsNotRun: true
              rerunFailedTests: true
              runSettingsFile: '$(Build.SourcesDirectory)\.runsettings'

          - task: PowerShell@2
            displayName: 'Execute CreateDrop script'
            inputs:
              filePath: 'CreateDrop.ps1'

          - task: PowerShell@2
            displayName: 'Execute UpdateApplicationVersion script'
            inputs:
              filePath: 'deployment/ExpressV2/UpdateApplicationVersion.ps1'
              arguments: '-PackageLocation "$(Build.SourcesDirectory)\out\release-AMD64\SFPackage" -BuildVersion "$(Build.BuildNumber)"'

          - task: onebranch.pipeline.signing@1 # https://aka.ms/obpipelines/signing
            displayName: 'Sign output'
            inputs:
              command: 'sign'
              signing_environment: 'azure-ado'
              files_to_sign: '**/*.exe;**/*.dll;**/*.ps1;**/*.psm1'
              search_root: '$(Build.SourcesDirectory)\out'

          - task: PowerShell@2
            displayName: 'Execute CreateDSCPackage script'
            inputs:
              filePath: 'deployment/Package/CreateDSCPackage.ps1'
              arguments: '-ServiceGroupRootDir "$(Build.SourcesDirectory)\deployment\ExpressV2\ServiceGroupRoot" -PackageLocation "$(Build.SourcesDirectory)\out\release-AMD64\DSCPackage" -BuildVersion "$(Build.BuildNumber)"'
          
          - task: PowerShell@2
            displayName: 'Execute CreateFolderPath script'
            inputs:
              filePath: 'deployment/Package/CreateFolderPath.ps1'
              arguments: '-BuildVersion "$(Build.BuildNumber)" -BuildLocalPath "$(Build.SourcesDirectory)\deployment\ExpressV2"'

          - task: PowerShell@2
            displayName: 'Execute CreateSFPackage script'
            inputs:
              filePath: 'deployment/Package/CreateSFPackage.ps1'
              arguments: '-PackageLocation "$(Build.SourcesDirectory)\out\release-AMD64\SFPackage" -BuildLocalPath "$(Build.SourcesDirectory)\deployment\ExpressV2"'

          - task: onebranch.pipeline.signing@1 # https://aka.ms/obpipelines/signing
            displayName: 'Sign ManifestCheckIn.ps1 before zip'
            inputs:
              command: 'sign'
              signing_environment: 'azure-ado'
              files_to_sign: '**/Manifests/Scripts/*.ps1'
              search_root: '$(Build.SourcesDirectory)\deployment'
          - task: PowerShell@2
            displayName: 'Generating Deployment.zip file under Scripts folder'
            inputs:
              targetType: 'inline'
              script: |
                $sourcePath = "$(Build.SourcesDirectory)\deployment\Manifests\Scripts\ManifestCheckIn.ps1"
                $destinationPath = "$(Build.SourcesDirectory)\deployment\Manifests\Scripts\Deployment.zip"
                Compress-Archive -Path $sourcePath -DestinationPath $destinationPath -Force

          - task: CopyFiles@2
            displayName: 'Copy Deployment files'
            inputs:
              SourceFolder: '$(Build.SourcesDirectory)\deployment'
              Contents: |
                NewRegion\**
                ManagedSDP\**
                Manifests\**
              TargetFolder: '$(Build.SourcesDirectory)\deployment\ExpressV2\ServiceGroupRoot'

          - task: CopyFiles@2
            displayName: 'Copy DatabricksTemplates files'
            inputs:
              SourceFolder: '$(Build.SourcesDirectory)\ADB_Partners\DatabricksTemplates'
              Contents: '**'
              TargetFolder: '$(Build.SourcesDirectory)\deployment\ExpressV2\ServiceGroupRoot\DatabricksEV2\DatabricksTemplates'

          - task: CopyFiles@2
            displayName: 'Copy DSCPackage files'
            inputs:
              SourceFolder: 'deployment/UploadDSCPackage'
              Contents: '**'
              TargetFolder: '$(Build.SourcesDirectory)\deployment\ExpressV2\ServiceGroupRoot\DatabricksEV2'

          - task: CopyFiles@2
            displayName: 'Copy NewRegion files'
            inputs:
              SourceFolder: 'deployment/NewRegion'
              Contents: '**'
              TargetFolder: '$(Build.SourcesDirectory)\deployment\ExpressV2\ServiceGroupRoot\DatabricksEV2'
 
          - task: CopyFiles@2
            displayName: 'Copy EnvironmentsDetails file'
            inputs:
              SourceFolder: 'deployment/ExpressV2/DeploymentData'
              Contents: '**'
              TargetFolder: '$(Build.SourcesDirectory)\deployment\ExpressV2\ServiceGroupRoot\DatabricksEV2'
 
          - task: CopyFiles@2
            displayName: 'Copy SF Template files'
            inputs:
              SourceFolder: 'deployment/Misc/arm-templates'
              Contents: '**'
              TargetFolder: '$(Build.SourcesDirectory)\deployment\ExpressV2\ServiceGroupRoot\DatabricksEV2'

          - task: CopyFiles@2
            displayName: 'Copy Validations files'
            inputs:
              SourceFolder: 'deployment/Validations'
              Contents: '**'
              TargetFolder: '$(Build.SourcesDirectory)\deployment\ExpressV2\ServiceGroupRoot\DatabricksEV2'
          
          - task: ArchiveFiles@2
            displayName: 'Archive DatabricksEV2 folder to create Ev2DeploymentPackage'
            inputs:
              rootFolderOrFile: '$(Build.SourcesDirectory)\deployment\ExpressV2\ServiceGroupRoot\DatabricksEV2'
              includeRootFolder: false
              archiveType: 'tar'
              tarCompression: 'none'
              archiveFile: '$(Build.SourcesDirectory)\deployment\ExpressV2\ServiceGroupRoot\Ev2DeploymentPackage.tar'
              replaceExistingArchive: true

          - task: ConfigGuard@0
            displayName: 'Validate with ConfigGuard'
            continueOnError: true
            inputs:
              configguardsettingpath: '$(Build.SourcesDirectory)/.configguard.cue'

          - task: onebranch.pipeline.signing@1 # https://aka.ms/obpipelines/signing
            displayName: 'Sign scripts'
            inputs:
              command: 'sign'
              signing_environment: 'azure-ado'
              files_to_sign: '**/*.exe;**/*.dll;**/*.ps1;**/*.psm1;**/Manifests/Scripts/*.zip'
              search_root: '$(Build.SourcesDirectory)\deployment'