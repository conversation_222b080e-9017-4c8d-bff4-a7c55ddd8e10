

#################################################################################
#                               OneBranch Pipelines                             #
# Documentation:  https://aka.ms/obpipelines                                    #
# Yaml Schema:    https://aka.ms/obpipelines/yaml/schema                        #
# Support:        https://aka.ms/onebranchsup                                   #
#################################################################################

trigger: none

parameters:
- name: 'rolloutType'
  displayName: 'SDP rollout type'
  type: string
  default: 'normal'
  values:
    - normal
    - emergency
    - globaloutage

- name: 'overrideManagedValidationDuration'
  displayName: 'Override standard SDP duration?'
  type: boolean
  default: false 

- name: 'managedValidationDurationInHours'
  displayName: 'Override standard SDP duration (in hours)'
  type: number
  default: 0

- name: 'icmIncidentId'
  displayName: 'IcM Incident Id'
  type: number
  default: 0

resources:
  repositories: 
    - repository: templates
      type: git
      name: OneBranch.Pipelines/GovernedTemplates
      ref: refs/heads/main

  pipelines:
    - pipeline: build-artifacts
      source: DatabricksRP-Official-Slots

extends:
  template: v2/OneBranch.Official.CrossPlat.yml@templates
  parameters:

    ev2ManagedSdpRolloutConfig:
      rolloutType: ${{parameters.rolloutType}}
      overrideManagedValidationDuration: ${{parameters.overrideManagedValidationDuration}} 
      managedValidationOverrideDurationInHours: ${{parameters.managedValidationDurationInHours}} 
      icmIncidentId: ${{parameters.icmIncidentId}}

    stages:

    # PROD (Public)
    - stage: 'PROD_Managed_SDP'
      displayName: 'PROD: Managed SDP'
      variables:
        ob_release_environment: Production
      jobs:
      - job: PROD_Managed_SDP
        displayName: 'PROD_Managed_SDP'
        pool:
          type: release

        steps:
          - download: build-artifacts
          - task: vsrm-ev2.ev2-rollout.ev2-rollout-task.Ev2RARollout@2
            displayName: 'Ev2 Managed SDP Rollout'
            inputs:
              EndpointProviderType: ApprovalService
              ApprovalServiceEnvironment: Production
              TaskAction: RegisterAndRollout
              SkipRegistrationIfExists: true
              ForceRegistration: true
              ServiceRootPath: $(Pipeline.Workspace)/build-artifacts/drop_build_main
              RolloutSpecPath: $(Pipeline.Workspace)/build-artifacts/drop_build_main/ManagedSDP/Merged/Slots/S2/DatabricksRP.RolloutSpec.Production.S2.json
              StageMapPath: 'ManagedSDP/Merged/StageMaps/Databricks.RP.StageMap.Prod.json'
              Select: regions(eastus2euap)
