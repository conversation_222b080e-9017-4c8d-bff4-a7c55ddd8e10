

#################################################################################
#                               OneBranch Pipelines                             #
# Documentation:  https://aka.ms/obpipelines                                    #
# Yaml Schema:    https://aka.ms/obpipelines/yaml/schema                        #
# Support:        https://aka.ms/onebranchsup                                   #
#################################################################################

trigger: none

schedules: # https://learn.microsoft.com/en-us/azure/devops/pipelines/process/scheduled-triggers?view=azure-devops&tabs=yaml
- cron: "0 11 * * *" # Runs every day at 3AM PST (11AM UTC)
  displayName: Daily Official Deploy in Staging
  branches:
    include:
    - masterAspnetcore

parameters:
- name: 'rolloutType'
  displayName: 'SDP rollout type'
  type: string
  default: 'normal'
  values:
    - normal
    - emergency
    - globaloutage

- name: 'overrideManagedValidationDuration'
  displayName: 'Override standard SDP duration?'
  type: boolean
  default: false 

- name: 'managedValidationDurationInHours'
  displayName: 'Override standard SDP duration (in hours)'
  type: number
  default: 0

- name: 'icmIncidentId'
  displayName: 'IcM Incident Id'
  type: number
  default: 0


- name: UpdateSFMC
  displayName: 'UpdateSFMC'
  type: string
  default: 'true'
  values:
    - 'true'
    - 'false'

resources:
  repositories: 
    - repository: templates
      type: git
      name: OneBranch.Pipelines/GovernedTemplates
      ref: refs/heads/main

  pipelines:
    - pipeline: build-artifacts
      source: DatabricksRP-Official

extends:
  template: v2/OneBranch.Official.CrossPlat.yml@templates
  parameters:

    ev2ManagedSdpRolloutConfig:
      rolloutType: ${{parameters.rolloutType}}
      overrideManagedValidationDuration: ${{parameters.overrideManagedValidationDuration}} 
      managedValidationOverrideDurationInHours: ${{parameters.managedValidationDurationInHours}} 
      icmIncidentId: ${{parameters.icmIncidentId}}

    stages:

    # Stg (Public)
    - stage: 'Test_Managed_SDP'
      displayName: 'Test: Managed SDP'
      variables:
        ob_release_environment: Test
      jobs:
      - job: STG_Managed_SDP
        displayName: 'STG_Managed_SDP'
        pool:
          type: release

        steps:
          - download: build-artifacts
          - task: vsrm-ev2.ev2-rollout.ev2-rollout-task.Ev2RARollout@2
            displayName: 'Ev2 Managed SDP Rollout'
            inputs:
              ConnectedServiceName: 'EV2 FIC Service Connection'
              TaskAction: RegisterAndRollout
              SkipRegistrationIfExists: true
              ForceRegistration: true
              ServiceRootPath: $(Pipeline.Workspace)/build-artifacts/drop_build_main
              RolloutSpecPath: $(Pipeline.Workspace)/build-artifacts/drop_build_main/ManagedSDP/Staging/DatabricksRP.RolloutSpec.Staging.json
              StageMapPath: 'ManagedSDP/Merged/StageMaps/Databricks.RP.StageMap.Prod.json'
              Select: regions(westus)
              ConfigurationOverrides: '{
                "ConfigurationSpecification": {
                    "settings": {
                        "UpdateSFMC": ${{parameters.UpdateSFMC}}
                    }
                  }
                }'