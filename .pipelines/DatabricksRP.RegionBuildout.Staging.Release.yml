

#################################################################################
#                               OneBranch Pipelines                             #
# Documentation:  https://aka.ms/obpipelines                                    #
# Yaml Schema:    https://aka.ms/obpipelines/yaml/schema                        #
# Support:        https://aka.ms/onebranchsup                                   #
#################################################################################

trigger: none

parameters:
- name: 'rolloutType'
  displayName: 'SDP rollout type'
  type: string
  default: 'normal'
  values:
    - normal
    - emergency
    - globaloutage

- name: 'overrideManagedValidationDuration'
  displayName: 'Override standard SDP duration?'
  type: boolean
  default: false 

- name: 'managedValidationDurationInHours'
  displayName: 'Override standard SDP duration (in hours)'
  type: number
  default: 0

- name: 'icmIncidentId'
  displayName: 'IcM Incident Id'
  type: number
  default: 0

- name: CreateResources
  displayName: 'CreateResources'
  type: string
  default: 'true'
  values:
    - 'true'
    - 'false'

resources:
  repositories: 
    - repository: templates
      type: git
      name: OneBranch.Pipelines/GovernedTemplates
      ref: refs/heads/main

  pipelines:
    - pipeline: build-artifacts
      source: DatabricksRP-Official

extends:
  template: v2/OneBranch.Official.CrossPlat.yml@templates
  parameters:

    ev2ManagedSdpRolloutConfig:
      rolloutType: ${{parameters.rolloutType}}
      overrideManagedValidationDuration: ${{parameters.overrideManagedValidationDuration}} 
      managedValidationOverrideDurationInHours: ${{parameters.managedValidationDurationInHours}} 
      icmIncidentId: ${{parameters.icmIncidentId}}

    stages:

    # Staging (Public)
    - stage: 'Test_Managed_SDP'
      displayName: 'Test: Managed SDP'
      variables:
        ob_release_environment: Test
      jobs:
      - job: Staging_Managed_SDP
        displayName: 'Staging_Managed_SDP'
        pool:
          type: release

        steps:
          - download: build-artifacts
          - task: vsrm-ev2.ev2-rollout.ev2-rollout-task.Ev2RARollout@2
            displayName: 'Ev2 Managed SDP Rollout'
            inputs:
              ConnectedServiceName: 'EV2 FIC Service Connection'
              TaskAction: RegisterAndRollout
              SkipRegistrationIfExists: true
              ForceRegistration: true
              ServiceRootPath: $(Pipeline.Workspace)/build-artifacts/drop_build_main
              RolloutSpecPath: $(Pipeline.Workspace)/build-artifacts/drop_build_main/ManagedSDP/Staging/DatabricksRP.RegionRollout.RolloutSpec.json
              StageMapName: 'Microsoft.Azure.SDP.Standard'
              Select: regions(westus)
              ConfigurationOverrides: '{
                "ConfigurationSpecification": {
                    "settings": {
                        "CREATE_RESOURCES": ${{parameters.CreateResources}},
                    }
                  }
                }'