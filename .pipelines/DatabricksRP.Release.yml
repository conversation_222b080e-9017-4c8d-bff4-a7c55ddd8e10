

#################################################################################
#                               OneBranch Pipelines                             #
# Documentation:  https://aka.ms/obpipelines                                    #
# Yaml Schema:    https://aka.ms/obpipelines/yaml/schema                        #
# Support:        https://aka.ms/onebranchsup                                   #
#################################################################################

trigger: none

parameters:
- name: 'rolloutType'
  displayName: 'SDP rollout type'
  type: string
  default: 'normal'
  values:
    - normal
    - emergency
    - globaloutage

- name: 'overrideManagedValidationDuration'
  displayName: 'Override standard SDP duration?'
  type: boolean
  default: false 

- name: 'managedValidationDurationInHours'
  displayName: 'Override standard SDP duration (in hours)'
  type: number
  default: 0

- name: 'icmIncidentId'
  displayName: 'IcM Incident Id'
  type: number
  default: 0

- name: region
  displayName: 'Select region to deploy'
  type: string
  default: '*'
  values:
  - '*'
  - 'CentralUSEUAP'
  - 'EastUS2EUAP'
  - 'AustraliaCentral2'
  - 'WestCentralUS'
  - 'EastAsia'
  - 'UKSouth'
  - 'EastUS'
  - 'AustraliaEast'
  - 'AustraliaCentral'
  - 'BrazilSouth'
  - 'CanadaCentral'
  - 'NorthEurope'
  - 'GermanyWestCentral'
  - 'CentralIndia'
  - 'WestIndia'
  - 'JapanEast'
  - 'KoreaCentral'
  - 'WestUS'
  - 'EastUS2'
  - 'NorthCentralUS'
  - 'SouthAfricaNorth'
  - 'SwedenCentral'
  - 'SwitzerlandWest'
  - 'QatarCentral'
  - 'SouthEastAsia'
  - 'AustraliaSouthEast'
  - 'SouthCentralUS'
  - 'CanadaEast'
  - 'WestEurope'
  - 'FranceCentral'
  - 'SouthIndia'
  - 'MexicoCentral'
  - 'JapanWest'
  - 'NorwayEast'
  - 'CentralUS'
  - 'WestUS2'
  - 'WestUS3'
  - 'SwitzerlandNorth'
  - 'UKWest'
  - 'UAENorth'
  - 'USGovArizona'
  - 'USGovVirginia'
  - 'USGovTexas'
  - 'ChinaNorth2'
  - 'ChinaEast2'
  - 'ChinaNorth3'
  - 'ChinaEast3'

- name: UpdateSFMC
  displayName: 'UpdateSFMC'
  type: string
  default: 'true'
  values:
    - 'true'
    - 'false'

resources:
  repositories: 
    - repository: templates
      type: git
      name: OneBranch.Pipelines/GovernedTemplates
      ref: refs/heads/main

  pipelines:
    - pipeline: build-artifacts
      source: DatabricksRP-Official

extends:
  template: v2/OneBranch.Official.CrossPlat.yml@templates
  parameters:

    ev2ManagedSdpRolloutConfig:
      rolloutType: ${{parameters.rolloutType}}
      overrideManagedValidationDuration: ${{parameters.overrideManagedValidationDuration}} 
      managedValidationOverrideDurationInHours: ${{parameters.managedValidationDurationInHours}} 
      icmIncidentId: ${{parameters.icmIncidentId}}

    stages:

    # PROD (Public)
    - stage: 'PROD_Managed_SDP'
      displayName: 'PROD: Managed SDP'
      variables:
        ob_release_environment: Production
      jobs:
      - job: PROD_Managed_SDP
        displayName: 'PROD_Managed_SDP'
        pool:
          type: release

        steps:
          - download: build-artifacts
          - task: vsrm-ev2.ev2-rollout.ev2-rollout-task.Ev2RARollout@2
            displayName: 'Ev2 Managed SDP Rollout'
            inputs:
              EndpointProviderType: ApprovalService
              ApprovalServiceEnvironment: Production
              TaskAction: RegisterAndRollout
              SkipRegistrationIfExists: true
              ForceRegistration: true
              ServiceRootPath: $(Pipeline.Workspace)/build-artifacts/drop_build_main
              RolloutSpecPath: $(Pipeline.Workspace)/build-artifacts/drop_build_main/ManagedSDP/Merged/DatabricksRP.RolloutSpec.json
              StageMapPath: 'ManagedSDP/Merged/StageMaps/Databricks.RP.StageMap.Prod.json'
              Select: regions(${{parameters.region}})
              ConfigurationOverrides: '{
                "ConfigurationSpecification": {
                    "settings": {
                        "UpdateSFMC": ${{parameters.UpdateSFMC}}
                    }
                  }
                }'

    # Mooncake
    - stage: 'MC_Managed_SDP'
      displayName: 'MC: Managed SDP'
      dependsOn: PROD_Managed_SDP
      variables:
        ob_release_environment: Mooncake
      jobs:
      - job: MC_Managed_SDP
        displayName: 'MC_Managed_SDP'
        pool:
          type: release

        steps:
          - download: build-artifacts
          - task: vsrm-ev2.ev2-rollout.ev2-rollout-task.Ev2RARollout@2
            displayName: 'Ev2 Managed SDP Rollout'
            inputs:
              EndpointProviderType: ApprovalService
              ApprovalServiceEnvironment: Mooncake
              TaskAction: RegisterAndRollout
              SkipRegistrationIfExists: true
              ForceRegistration: true
              ServiceRootPath: $(Pipeline.Workspace)/build-artifacts/drop_build_main
              RolloutSpecPath: $(Pipeline.Workspace)/build-artifacts/drop_build_main/ManagedSDP/Merged/DatabricksRP.RolloutSpec.json
              StageMapPath: 'ManagedSDP/Merged/StageMaps/Databricks.RP.StageMap.Mooncake.json'
              Select: regions(${{parameters.region}})
              ConfigurationOverrides: '{
                "ConfigurationSpecification": {
                    "settings": {
                        "UpdateSFMC": ${{parameters.UpdateSFMC}}
                    }
                  }
                }'

    # Fairfax
    - stage: 'FF_Managed_SDP'
      displayName: 'FF: Managed SDP'
      dependsOn: MC_Managed_SDP
      variables:
        ob_release_environment: Fairfax
      jobs:
      - job: FF_Managed_SDP
        displayName: 'FF_Managed_SDP'
        pool:
          type: release

        steps:
          - download: build-artifacts
          - task: vsrm-ev2.ev2-rollout.ev2-rollout-task.Ev2RARollout@2
            displayName: 'Ev2 Managed SDP Rollout'
            inputs:
              EndpointProviderType: ApprovalService
              ApprovalServiceEnvironment: Fairfax
              TaskAction: RegisterAndRollout
              SkipRegistrationIfExists: true
              ForceRegistration: true
              ServiceRootPath: $(Pipeline.Workspace)/build-artifacts/drop_build_main
              RolloutSpecPath: $(Pipeline.Workspace)/build-artifacts/drop_build_main/ManagedSDP/Merged/DatabricksRP.RolloutSpec.json
              StageMapPath: 'ManagedSDP/Merged/StageMaps/Databricks.RP.StageMap.Fairfax.json'
              Select: regions(${{parameters.region}})
              ConfigurationOverrides: '{
                "ConfigurationSpecification": {
                    "settings": {
                        "UpdateSFMC": ${{parameters.UpdateSFMC}}
                    }
                  }
                }'
