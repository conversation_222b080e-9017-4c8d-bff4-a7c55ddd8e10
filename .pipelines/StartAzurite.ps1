
$azuriteExecutable="C:\Program Files\Microsoft Visual Studio\2022\Enterprise\Common7\IDE\Extensions\Microsoft\Azure Storage Emulator\Azurite.exe";

if (Test-Path $azuriteExecutable) {
    Write-Output "Azurite executable found. Starting the process..."
    try {
        Start-Process $azuriteExecutable -PassThru
    } catch {
        Write-Error "Failed to start Azurite: $_"
        exit 1
    }

    $attemptCount = 0
    $maxAttempts = 5
    while ($attemptCount++ -lt $maxAttempts) {
        $azuriteProcess = Get-Process -Name azurite -ErrorAction SilentlyContinue
        
        if ($azuriteProcess -and $azuriteProcess.Responding) {
            Write-Output "Azurite has started successfully."
            break
        } else {
            Write-Output "Attempt '$attemptCount' to verify Azurite is running."
            Start-Sleep -Milliseconds 500
        }
    }
    
    if ($attemptCount -gt $maxAttempts) {
        Write-Warning "Azurite failed to start after $maxAttempts attempts."
    }
} else {
    Write-Error "Azurite executable not found at the specified path: $azuriteExecutable"
}