#################################################################################
#                      OneBranch Pipelines - PR Build                           #
# This pipeline was created by EasyStart from a sample located at:              #
#   https://aka.ms/obpipelines/easystart/samples                                #
# Documentation:  https://aka.ms/obpipelines                                    #
# Yaml Schema:    https://aka.ms/obpipelines/yaml/schema                        #
# Retail Tasks:   https://aka.ms/obpipelines/tasks                              #
# Support:        https://aka.ms/onebranchsup                                   #
#################################################################################

trigger: none # https://aka.ms/obpipelines/triggers

parameters: # parameters are shown up in ADO UI in a build queue time
- name: 'debug'
  displayName: 'Enable debug output'
  type: boolean
  default: false

variables:
  CDP_DEFINITION_BUILD_COUNT: $[counter('', 0)] # needed for onebranch.pipeline.version task https://aka.ms/obpipelines/versioning
  system.debug: ${{ parameters.debug }}
  ENABLE_PRS_DELAYSIGN: 0
  ROOT: $(Build.SourcesDirectory)
  REPOROOT: $(Build.SourcesDirectory)
  OUTPUTROOT: $(REPOROOT)\out
  NUGET_XMLDOC_MODE: none
  MSBUILD_EXE: C:\Program Files\Microsoft Visual Studio\2022\Enterprise\MSBuild\Current\Bin\amd64\MSBuild.exe
  WindowsContainerImage: 'onebranch.azurecr.io/windows/ltsc2019/vse2022:latest' # Docker image which is used to build the project https://aka.ms/obpipelines/containers

resources:
  repositories: 
    - repository: templates
      type: git
      name: OneBranch.Pipelines/GovernedTemplates
      ref: refs/heads/main

extends:
  template: v2/OneBranch.NonOfficial.CrossPlat.yml@templates # https://aka.ms/obpipelines/templates
  parameters:
    globalSdl: # https://aka.ms/obpipelines/sdl
      tsa:
        enabled: false # onebranch publish all sdl results to TSA. If TSA is disabled all SDL tools will forced into 'break' build mode.
      # credscan:
      #   suppressionsFile: $(Build.SourcesDirectory)\.config\CredScanSuppressions.json
      binskim:
        break: true # always break the build on binskim issues. You can disable it by setting to 'false'
      policheck:
        break: true # always break the build on policheck issues. You can disable it by setting to 'false'
      # suppression:
      #   suppressionFile: $(Build.SourcesDirectory)\.gdn\global.gdnsuppress

    stages:
    - stage: build
      jobs:
      - job: main
        pool:
          type: windows  # read more about custom job pool types at https://aka.ms/obpipelines/yaml/jobs
        
        variables: # More settings at https://aka.ms/obpipelines/yaml/jobs
          ob_outputDirectory: '$(REPOROOT)\out' # this directory is uploaded to pipeline artifacts, reddog and cloudvault. More info at https://aka.ms/obpipelines/artifacts
          ob_sdl_binskim_break: true # https://aka.ms/obpipelines/sdl

          # ob_sdl_suppression_suppressionFile: $(Build.SourcesDirectory)\.gdn\build.nonofficial.gdnsuppress

        steps:
          - task: onebranch.pipeline.version@1 # generates automatic version. For other versioning options check https://aka.ms/obpipelines/versioning
            displayName: 'Setup BuildNumber'
            inputs:
              system: 'RevisionCounter'
              major: '1'
              minor: '0'
              exclude_commit: true

          - task: MSBuild@1
            displayName: 'Clean Prod solution'
            inputs:
              solution: '$(Build.SourcesDirectory)\src\Prod.sln'
              msbuildLocationMethod: 'location'
              msbuildLocation: '$(MSBUILD_EXE)'
              platform: 'x64'
              configuration: 'release'
              msbuildArguments: '/t:Clean'
              clean: true
              maximumCpuCount: true
              logProjectEvents: true

          # Restore Nuget packages
          - task: NuGetCommand@2
            displayName: 'Restore Nuget pacakages'
            inputs:
              command: 'restore'
              restoreSolution: 'src/Prod.sln'
              feedsToUse: 'config'
              nugetConfigPath: '$(Build.SourcesDirectory)\NuGet.Config'

          - task: MSBuild@1
            displayName: 'Build Prod solution'
            inputs:
              solution: '$(Build.SourcesDirectory)\src\Prod.sln'
              msbuildLocationMethod: 'location'
              msbuildLocation: '$(MSBUILD_EXE)'
              platform: 'x64'
              configuration: 'release'
              msbuildArguments: '/v:n /nr:false /flp1:Verbosity=d;LogFile=$(OUTPUTROOT)\logs\msbuild_release.log;Encoding=UTF-8 /flp2:logfile=$(OUTPUTROOT)\logs\msbuild_release.err;errorsonly /bl:$(OUTPUTROOT)\logs\msbuild_release.binlog'
              clean: true
              maximumCpuCount: true
              logProjectEvents: true

          - task: VSTest@2
            displayName: 'Run unit tests'
            inputs:
              testSelector: 'testAssemblies'
              testAssemblyVer2: |
                **\bin\Release\UnitTests.Providers.Feature.dll
                **\bin\Release\UnitTests.Providers.Worker.dll
                **\bin\Release\UnitTests.Providers.Common.dll
              searchFolder: '$(Build.SourcesDirectory)\src'
              resultsFolder: '$(Build.SourcesDirectory)\out\logs\TestResults'
              runInParallel: false
              codeCoverageEnabled: true
              testRunTitle: 'unit tests'
              platform: 'x64'
              configuration: 'release'
              publishRunAttachments: true
              failOnMinTestsNotRun: true
              rerunFailedTests: true
              runSettingsFile: '$(Build.SourcesDirectory)\.runsettings'

          - task: PowerShell@2
            displayName: 'Setup integration tests environment'
            inputs:
              targetType: 'inline'
              script: |
                New-SelfSignedCertificate -DnsName "dummycert.azurewebsites.net" -CertStoreLocation "cert:\LocalMachine\My"

          - task: PowerShell@2
            displayName: Start Azurite
            inputs:
              filePath: '$(Build.SourcesDirectory)/.pipelines/StartAzurite.ps1'

          - task: VSTest@2
            displayName: 'Run integration tests'
            inputs:
              testSelector: 'testAssemblies'
              testAssemblyVer2: |
                **\bin\Release\IntegrationTests.ResourceProvider.dll
              searchFolder: '$(Build.SourcesDirectory)\src'
              resultsFolder: '$(Build.SourcesDirectory)\out\logs\IntegrationTestResults'
              runInParallel: false
              codeCoverageEnabled: true
              testRunTitle: 'integration tests'
              platform: 'x64'
              configuration: 'release'
              publishRunAttachments: true
              failOnMinTestsNotRun: true
              rerunFailedTests: true
              runSettingsFile: '$(Build.SourcesDirectory)\.runsettings'